<link type="text/css"
      href="https://luna-corporate-dot-luna-hotels.appspot.com/static_1/css/booking_widget_luna_hotels_injection.css?v=5.1"
      rel="stylesheet" media="screen"/>
<link type="text/css"
      href="https://luna-corporate-dot-luna-hotels.appspot.com/static_1/css/datepicker.redmond/jquery-ui-1.8.16.custom.css"
      rel="stylesheet"
      media="screen"/>

<script src="//ajax.googleapis.com/ajax/libs/jqueryui/1.10.1/jquery-ui.min.js"></script>

<link type="text/css" href="https://luna-corporate-dot-luna-hotels.appspot.com/static_1/css/bookingWidget.css"
      rel="stylesheet" media="screen"/>

<link type="text/css" href="https://luna-corporate-dot-luna-hotels.appspot.com/static_1/css/booking/booking_engine_3.css"
      rel="stylesheet" media="screen"/>

<!--[if IE]>
		<link rel="stylesheet" type="text/css" href="/static_1/css/ie8-and-down.css" />
	<![endif]-->

<div id="horizontal_booking_selector">
<div id="full_wrapper_booking" class="booking_horizontal">
<div class="boking_widget_inline">
<link href='//fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,200italic,300italic' rel='stylesheet'
      type='text/css'>

<span id="full-booking-engine-html" class="top_luna_widget" style="margin:0px;padding:0px;">
<!-- MY BOOKINGS INTEGRATED -->

<!-- TITULO -->
<div class="booking_form_title">
    <h4 class="booking_title_1">Reservas</h4>
    <h4 class="booking_title_2">Reserve agora</h4>
    <h4 class="best_price">RESERVE AO MELHOR PREÇO</h4>


</div>
<!-- END TITULO -->
<!-- booking engine 6 -->
<form class="booking_form  paraty-booking-form" action="https://luna-corporate-dot-luna-hotels.appspot.com/booking1"
      method="post">

<input type="hidden" id="language" name="language" value="PORTUGUESE"/>
<input type="hidden" id="agesKid1" name="agesKid1"/>
<input type="hidden" id="agesKid2" name="agesKid2"/>
<input type="hidden" id="agesKid3" name="agesKid3"/>

<input id="roomType" type="hidden" name="roomType" value="">
<input class="roomTypeValue" type="hidden" name="roomTypeValue" value="">

<input id="roomFilter" type="hidden" name="roomFilter" value=".*">
<input id="roomFilterName" type="hidden" name="roomFilterName" value="">

<input type="hidden" id="namespace" name="namespace" value="luna-corporate"/>

<input type="hidden" id="gclid" name="gclid" value=""/>
<input type="hidden" id="_ga" name="_ga" value=""/>

<input type="hidden" value="" name="priceSeekerHotel" id="priceSeekerHotel"/>


<!-- BOOKING 0 -->
<input type="hidden" id="applicationIds" name="applicationIds" value=""/>
<!-- END BOOKING 0-->

<!-- SELECTOR DE HOTEL-->


<div class="destination_wrapper">
    <label for="destination">Selecionar Hotel</label>

    <select class="location_group_select">

        <option value="portugal"
                namespaces="luna-olympus;luna-oura;luna-fatima;luna-esperansa;luna-oceano;luna-alvor-village;luna-alvor-bay;luna-serra;luna-carqueijais;luna-chales;luna-arcos;luna-abrantes;luna-solaqua">
            PORTUGAL
        </option>

        <option value="algarve"
                namespaces="luna-oceano;luna-oura;luna-alvor-bay;luna-alvor-village;luna-olympus;luna-solaqua">
            Algarve
        </option>

        <option value="albufeira" namespaces="luna-oceano;luna-oura;luna-solaqua">
            Albufeira
        </option>

        <option value="alvor" namespaces="luna-alvor-bay;luna-alvor-village">
            Alvor
        </option>

        <option value="vilamoura" namespaces="luna-olympus">
            Vilamoura
        </option>

        <option value="setubal" namespaces="luna-esperansa">
            Setúbal
        </option>

        <option value="abrantes" namespaces="luna-abrantes">
            Abrantes
        </option>

        <option value="arcos" namespaces="luna-arcos">
            Arcos de Valdevez
        </option>

        <option value="fatima" namespaces="luna-fatima">
            Fátima
        </option>

        <option value="serra_estrela" namespaces="luna-serra;luna-chales;luna-carqueijais">
            Serra da Estrela
        </option>

        <option value="angola" namespaces="luna-zombo">
            ANGOLA
        </option>

        <option value="luanda" namespaces="luna-zombo">
            Luanda
        </option>

        <option value="all_hotels"
                namespaces="luna-olympus;luna-oura;luna-fatima;luna-esperansa;luna-oceano;luna-alvor-village;luna-alvor-bay;luna-zombo;luna-serra;luna-carqueijais;luna-chales;luna-arcos;luna-abrantes;luna-solaqua">
            TODAS
        </option>

    </select>

    <div class="destination_field">
        <input class="destination" readonly="readonly" type="text" name="destination" placeholder="Hotel">
        <input type="hidden" id="default_destination_placeholder" name="default_destination_placeholder"
               class="default_destination_placeholder" value="Hotel">

        <div class="right_arrow"></div>
    </div>
</div>


<div class="hotel_selector">
<div class="hotel_selector_inner">
<div class="close_hotel_selector" style="background: url(/static_1/lib/fancybox/source/fancybox_sprite.png);"></div>


<ul class="portugal num-ul-1">

    <li class="title_group"><h3>PORTUGAL</h3></li>


    <li id="luna-olympus" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Olympus ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-olympus" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-olympus" value="luna-olympus">





    <li id="luna-oura" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel da Oura ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oura" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oura" value="luna-oura">


    <li id="luna-fatima" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Fátima Hotel ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-fatima" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-fatima" value="luna-fatima">


    <li id="luna-esperansa" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Esperança Centro ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-esperansa" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-esperansa" value="luna-esperansa">


    <li id="luna-oceano" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Clube Oceano ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oceano" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oceano" value="luna-oceano">




    <li id="luna-alvor-village" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Alvor Village ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-village" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-village" value="luna-alvor-village">


    <li id="luna-alvor-bay" class=" hotel_selector_option adults_only">
        <h3 class="title_selector">Luna Alvor Bay ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-bay" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-bay" value="luna-alvor-bay">


    <li id="luna-serra" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel Serra da Estrela ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-serra" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-serra" value="luna-serra">


    <li id="luna-carqueijais" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel dos Carqueijais ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-carqueijais" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-carqueijais" value="luna-carqueijais">


    <li id="luna-chales" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Chalets da Montanha</h3>
    </li>
    <input type="hidden" id="url_booking_luna-chales" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-chales" value="luna-chales">


    <li id="luna-arcos" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Arcos Hotel ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-arcos" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-arcos" value="luna-arcos">


    <li id="luna-abrantes" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel Turismo ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-abrantes" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-abrantes" value="luna-abrantes">

    <li id="luna-solaqua" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Solaqua ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-solaqua" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-solaqua" value="luna-solaqua">
</ul>


<ul class="algarve num-ul-2">

    <li class="title_group"><h3>Algarve</h3></li>


    <li id="luna-oceano" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Clube Oceano ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oceano" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oceano" value="luna-oceano">


    <li id="luna-oura" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel da Oura ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oura" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oura" value="luna-oura">





    <li id="luna-alvor-bay" class=" hotel_selector_option adults_only">
        <h3 class="title_selector">Luna Alvor Bay ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-bay" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-bay" value="luna-alvor-bay">


    <li id="luna-alvor-village" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Alvor Village ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-village" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-village" value="luna-alvor-village">




    <li id="luna-olympus" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Olympus ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-olympus" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-olympus" value="luna-olympus">

    <li id="luna-solaqua" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Solaqua ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-solaqua" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-solaqua" value="luna-solaqua">
</ul>


<ul class="albufeira num-ul-3">

    <li class="title_group"><h3>Albufeira</h3></li>


    <li id="luna-oceano" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Clube Oceano ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oceano" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oceano" value="luna-oceano">


    <li id="luna-oura" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel da Oura ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oura" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oura" value="luna-oura">



    <li id="luna-solaqua" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Solaqua ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-solaqua" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-solaqua" value="luna-solaqua">
</ul>


<ul class="alvor num-ul-4">

    <li class="title_group"><h3>Alvor</h3></li>


    <li id="luna-alvor-bay" class=" hotel_selector_option adults_only">
        <h3 class="title_selector">Luna Alvor Bay ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-bay" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-bay" value="luna-alvor-bay">


    <li id="luna-alvor-village" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Alvor Village ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-village" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-village" value="luna-alvor-village">





</ul>


<ul class="vilamoura num-ul-5">

    <li class="title_group"><h3>Vilamoura</h3></li>


    <li id="luna-olympus" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Olympus ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-olympus" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-olympus" value="luna-olympus">


</ul>


<ul class="setubal num-ul-6">

    <li class="title_group"><h3>Setúbal</h3></li>


    <li id="luna-esperansa" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Esperança Centro ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-esperansa" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-esperansa" value="luna-esperansa">


</ul>


<ul class="abrantes num-ul-7">

    <li class="title_group"><h3>Abrantes</h3></li>


    <li id="luna-abrantes" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel Turismo ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-abrantes" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-abrantes" value="luna-abrantes">


</ul>


<ul class="arcos num-ul-8">

    <li class="title_group"><h3>Arcos de Valdevez</h3></li>


    <li id="luna-arcos" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Arcos Hotel ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-arcos" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-arcos" value="luna-arcos">


</ul>


<ul class="fatima num-ul-9">

    <li class="title_group"><h3>Fátima</h3></li>


    <li id="luna-fatima" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Fátima Hotel ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-fatima" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-fatima" value="luna-fatima">


</ul>


<ul class="serra_estrela num-ul-10">

    <li class="title_group"><h3>Serra da Estrela</h3></li>


    <li id="luna-serra" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel Serra da Estrela ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-serra" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-serra" value="luna-serra">


    <li id="luna-chales" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Chalets da Montanha</h3>
    </li>
    <input type="hidden" id="url_booking_luna-chales" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-chales" value="luna-chales">


    <li id="luna-carqueijais" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel dos Carqueijais ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-carqueijais" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-carqueijais" value="luna-carqueijais">


</ul>


<ul class="angola num-ul-11">

    <li class="title_group"><h3>ANGOLA</h3></li>


    <li id="luna-zombo" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Zombo ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-zombo" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-zombo" value="luna-zombo">


</ul>


<ul class="luanda num-ul-12">

    <li class="title_group"><h3>Luanda</h3></li>


    <li id="luna-zombo" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Zombo ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-zombo" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-zombo" value="luna-zombo">


</ul>


<ul class="all_hotels num-ul-13">

    <li class="title_group"><h3></h3></li>


    <li id="luna-olympus" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Olympus ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-olympus" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-olympus" value="luna-olympus">





    <li id="luna-oura" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel da Oura ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oura" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oura" value="luna-oura">


    <li id="luna-fatima" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Fátima Hotel ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-fatima" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-fatima" value="luna-fatima">


    <li id="luna-esperansa" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Esperança Centro ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-esperansa" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-esperansa" value="luna-esperansa">


    <li id="luna-oceano" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Clube Oceano ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oceano" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oceano" value="luna-oceano">





    <li id="luna-alvor-village" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Alvor Village ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-village" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-village" value="luna-alvor-village">


    <li id="luna-alvor-bay" class=" hotel_selector_option adults_only">
        <h3 class="title_selector">Luna Alvor Bay ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-bay" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-bay" value="luna-alvor-bay">


    <li id="luna-zombo" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Zombo ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-zombo" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-zombo" value="luna-zombo">


    <li id="luna-serra" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel Serra da Estrela ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-serra" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-serra" value="luna-serra">


    <li id="luna-carqueijais" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel dos Carqueijais ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-carqueijais" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-carqueijais" value="luna-carqueijais">


    <li id="luna-chales" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Chalets da Montanha</h3>
    </li>
    <input type="hidden" id="url_booking_luna-chales" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-chales" value="luna-chales">


    <li id="luna-arcos" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Arcos Hotel ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-arcos" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-arcos" value="luna-arcos">


    <li id="luna-abrantes" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel Turismo ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-abrantes" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-abrantes" value="luna-abrantes">

    <li id="luna-solaqua" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Solaqua ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-solaqua" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-solaqua" value="luna-solaqua">
</ul>

<div class="close_hotel_selector"></div>
</div>
</div>


<!-- END SELECTOR DE HOTEL-->


<!-- HIDE FILTERS -->


<!-- HIDE FILTERS -->

<!-- SELECCION FECHAS DE ENTRADA Y NUMERO HABITACIONES -->
<div class="entry_date_wrapper">
    <label>Chegada</label>

    <div class="date_box entry_date">
        <span class="date_day"></span>
        <span class="date_year"></span>
        <input name="startDate" type="hidden" class="has_datepicker entry_input"/>

        <div></div>
    </div>
</div>

<div class="departure_date_wrapper">
    <label>Saída</label>

    <div class="date_box departure_date">
        <span class="date_day"></span>
        <span class="date_year"></span>
        <input name="endDate" type="hidden" class="has_datepicker departure_input"/>

        <div></div>
    </div>
</div>


<div class="guest_selector">Ocupação</div>

<!-- END SELECCION FECHAS DE ENTRADA Y HABITACIONES -->


<!-- LISTA DE HABITACIONES -->
<div class="room_list_wrapper">


<div class="rooms_number_wrapper">
    <label>Quartos</label>
    <select name="numRooms" class="rooms_number">
        <option value="1" selected="selected">1</option>
        <option value="2">2</option>
        <option value="3">3</option>
    </select>
</div>

<ul class="room_list">

<!---------------------- ROOM 1---------------------->
<li class="room room1">

    <div class="room_title  "> Quarto 1</div>

    <div class="adults_selector">
        <label for="adultsRoom1">Adultos</label>
        <select name="adultsRoom1" class="adults_room_1 room_selector">

            <option value="1">1</option>

            <option value="2" selected="selected">2</option>

            <option value="3">3</option>

            <option value="4">4</option>

            <option value="5">5</option>

            <option value="6">6</option>

            <option value="7">7</option>

            <option value="8">8</option>

        </select>
    </div>

    <div class="children_selector">
        <label for="childrenRoom1">Crianças

        <span class="range-age">


              (2-12&nbsp;anos)



        </span>

        </label>
        <select name="childrenRoom1" class="children_room_1 room_selector">

            <option value="0" selected="selected">

                0

            </option>

            <option value="1">

                1

            </option>

            <option value="2">

                2

            </option>

            <option value="3">

                3

            </option>

            <option value="4">

                4

            </option>

        </select>
    </div>


</li>

<!---------------------- ROOM 2---------------------->
<li class="room room2" style="display: none">

    <div class="room_title ">Quarto 2</div>

    <div class="adults_selector">
        <label for="adultsRoom2">Adultos</label>
        <select name="adultsRoom2" class="adults_room_2 room_selector">

            <option value="1">1</option>

            <option value="2" selected="selected">2</option>

            <option value="3">3</option>

            <option value="4">4</option>

            <option value="5">5</option>

            <option value="6">6</option>

            <option value="7">7</option>

            <option value="8">8</option>

        </select>
    </div>

    <div class="children_selector">
        <label for="childrenRoom2">Crianças


        <span class="range-age">


              (2-12&nbsp;anos)



        </span>


        </label>
        <select name="childrenRoom2" class="children_room_2 room_selector">

            <option value="0" selected="selected">

                0

            </option>

            <option value="1">

                1

            </option>

            <option value="2">

                2

            </option>

            <option value="3">

                3

            </option>

            <option value="4">

                4

            </option>

        </select>
    </div>


</li>

<!---------------------- ROOM 3 ---------------------->
<li class="room room3" style="display: none">

    <div class="room_title ">Quarto 3</div>

    <div class="adults_selector">
        <label for="adultsRoom3">Adultos</label>
        <select name="adultsRoom3" class="adults_room_3 room_selector">

            <option value="1">1</option>

            <option value="2" selected="selected">2</option>

            <option value="3">3</option>

            <option value="4">4</option>

            <option value="5">5</option>

            <option value="6">6</option>

            <option value="7">7</option>

            <option value="8">8</option>

        </select>
    </div>

    <div class="children_selector">
        <label for="childrenRoom3">Crianças


        <span class="range-age">


              (2-12&nbsp;anos)



        </span>


        </label>
        <select name="childrenRoom3" class="children_room_3 room_selector">

            <option value="0" selected="selected">

                0

            </option>

            <option value="1">

                1

            </option>

            <option value="2">

                2

            </option>

            <option value="3">

                3

            </option>

            <option value="4">

                4

            </option>

        </select>
    </div>


</li>


</ul>
</div>
<!-- END LISTA DE HABITACIONES -->


<div class="wrapper_booking_button ">

    <input type="text" class="promocode_input " placeholder="Promocode" name="promocode" value=""
           tabindex="16">

    <button type="button" onclick="$py(this).addClass('clicked_button');bookingSearchWithAgeSelection();return false;"
            class="submit_button buttonsearch-ratecheck">


        Reservar


    </button>
    <div class="spinner_wrapper"></div>


</div>

<div style="clear:both"></div>


</form>


<a class='special_link' href='https://luna-corporate-dot-luna-hotels.appspot.com/pt/minhas-reservas.html' target='_blank'
   class='misreservasLInk'>minhas reservas</a>


<style>
    .ui-dialog {
        position: absolute;
        top: 0;
        left: 0;
        padding: .2em;
        outline: 0;
    }

    .ui-dialog .ui-dialog-titlebar {
        padding: .4em 1em;
        position: relative;
    }

    .ui-dialog .ui-dialog-title {
        float: left;
        margin: .1em 0;
        white-space: nowrap;
        width: 90%;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .ui-dialog .ui-dialog-titlebar-close {
        position: absolute;
        right: .3em;
        top: 50%;
        width: 21px;
        margin: -10px 0 0 0;
        padding: 1px;
        height: 20px;
    }

    .ui-dialog .ui-dialog-content {
        position: relative;
        border: 0;
        padding: .5em 1em;
        background: none;
        overflow: auto;
        text-align: center;
    }

    .ui-dialog .ui-dialog-buttonpane {
        text-align: left;
        border-width: 1px 0 0 0;
        background-image: none;
        margin-top: .5em;
        padding: .3em 1em .5em .4em;
    }

    .ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
        /*float: right;*/
        float: none;
        text-align: center;
    }

    .ui-dialog .ui-dialog-buttonpane button {
        margin: .5em .4em .5em 0;
        cursor: pointer;
    }

    .ui-dialog .ui-resizable-se {
        width: 12px;
        height: 12px;
        right: -5px;
        bottom: -5px;
        background-position: 16px 16px;
    }

    .ui-dialog .ui-button-text {
        /*padding: 10px;*/
        /*margin: 10px;*/
    }

    .ui-dialog .ui-button {
        /*height: 30px;*/
    }

    .ui-draggable .ui-dialog-titlebar {
        cursor: move;
    }

    .ui-dialog {
        min-width: 300px;
        z-index: 9999;
    }

</style>

 <style>
     .ui-dialog-titlebar-close {
         display: none !important;
     }

     #dialog-form {
         padding: 10px;

     }

     .ui-dialog-titlebar {
         padding: 5px;
     }

     .kidAgesSelect {
         margin-left: 10px;

     }

     .ui-dialog {
         z-index: 99999;
     }


 </style>
<!--  <link rel="stylesheet" href="https://code.jquery.com/ui/1.10.1/themes/base/jquery-ui.css" type="text/css"/> -->
 <div id="dialog-form" title="As idades das crianças" style="display:none">
 <form>


 <label id="label1" for="name">Quarto 1</label>


 <select class="kidAgesSelect" id="agesRoom1_1" name="name" onchange="" tabindex="1">

     <option value="2" selected="selected">2</option>

     <option value="3" selected="selected">3</option>

     <option value="4" selected="selected">4</option>

     <option value="5" selected="selected">5</option>

     <option value="6" selected="selected">6</option>

     <option value="7" selected="selected">7</option>

     <option value="8" selected="selected">8</option>

     <option value="9" selected="selected">9</option>

     <option value="10" selected="selected">10</option>

     <option value="11" selected="selected">11</option>

     <option value="12" selected="selected">12</option>

 </select>


 <select class="kidAgesSelect" id="agesRoom1_2" name="name" onchange="" tabindex="1">

     <option value="2" selected="selected">2</option>

     <option value="3" selected="selected">3</option>

     <option value="4" selected="selected">4</option>

     <option value="5" selected="selected">5</option>

     <option value="6" selected="selected">6</option>

     <option value="7" selected="selected">7</option>

     <option value="8" selected="selected">8</option>

     <option value="9" selected="selected">9</option>

     <option value="10" selected="selected">10</option>

     <option value="11" selected="selected">11</option>

     <option value="12" selected="selected">12</option>

 </select>


 <select class="kidAgesSelect" id="agesRoom1_3" name="name" onchange="" tabindex="1">

     <option value="2" selected="selected">2</option>

     <option value="3" selected="selected">3</option>

     <option value="4" selected="selected">4</option>

     <option value="5" selected="selected">5</option>

     <option value="6" selected="selected">6</option>

     <option value="7" selected="selected">7</option>

     <option value="8" selected="selected">8</option>

     <option value="9" selected="selected">9</option>

     <option value="10" selected="selected">10</option>

     <option value="11" selected="selected">11</option>

     <option value="12" selected="selected">12</option>

 </select>


 <select class="kidAgesSelect" id="agesRoom1_4" name="name" onchange="" tabindex="1">

     <option value="2" selected="selected">2</option>

     <option value="3" selected="selected">3</option>

     <option value="4" selected="selected">4</option>

     <option value="5" selected="selected">5</option>

     <option value="6" selected="selected">6</option>

     <option value="7" selected="selected">7</option>

     <option value="8" selected="selected">8</option>

     <option value="9" selected="selected">9</option>

     <option value="10" selected="selected">10</option>

     <option value="11" selected="selected">11</option>

     <option value="12" selected="selected">12</option>

 </select>


 <label id="label2" for="name">Quarto 2</label>


 <select class="kidAgesSelect" id="agesRoom2_1" name="name" onchange="" tabindex="1">

     <option value="2" selected="selected">2</option>

     <option value="3" selected="selected">3</option>

     <option value="4" selected="selected">4</option>

     <option value="5" selected="selected">5</option>

     <option value="6" selected="selected">6</option>

     <option value="7" selected="selected">7</option>

     <option value="8" selected="selected">8</option>

     <option value="9" selected="selected">9</option>

     <option value="10" selected="selected">10</option>

     <option value="11" selected="selected">11</option>

     <option value="12" selected="selected">12</option>

 </select>


 <select class="kidAgesSelect" id="agesRoom2_2" name="name" onchange="" tabindex="1">

     <option value="2" selected="selected">2</option>

     <option value="3" selected="selected">3</option>

     <option value="4" selected="selected">4</option>

     <option value="5" selected="selected">5</option>

     <option value="6" selected="selected">6</option>

     <option value="7" selected="selected">7</option>

     <option value="8" selected="selected">8</option>

     <option value="9" selected="selected">9</option>

     <option value="10" selected="selected">10</option>

     <option value="11" selected="selected">11</option>

     <option value="12" selected="selected">12</option>

 </select>


 <select class="kidAgesSelect" id="agesRoom2_3" name="name" onchange="" tabindex="1">

     <option value="2" selected="selected">2</option>

     <option value="3" selected="selected">3</option>

     <option value="4" selected="selected">4</option>

     <option value="5" selected="selected">5</option>

     <option value="6" selected="selected">6</option>

     <option value="7" selected="selected">7</option>

     <option value="8" selected="selected">8</option>

     <option value="9" selected="selected">9</option>

     <option value="10" selected="selected">10</option>

     <option value="11" selected="selected">11</option>

     <option value="12" selected="selected">12</option>

 </select>


 <select class="kidAgesSelect" id="agesRoom2_4" name="name" onchange="" tabindex="1">

     <option value="2" selected="selected">2</option>

     <option value="3" selected="selected">3</option>

     <option value="4" selected="selected">4</option>

     <option value="5" selected="selected">5</option>

     <option value="6" selected="selected">6</option>

     <option value="7" selected="selected">7</option>

     <option value="8" selected="selected">8</option>

     <option value="9" selected="selected">9</option>

     <option value="10" selected="selected">10</option>

     <option value="11" selected="selected">11</option>

     <option value="12" selected="selected">12</option>

 </select>


 <label id="label3" for="name">Quarto 3</label>


 <select class="kidAgesSelect" id="agesRoom3_1" name="name" onchange="" tabindex="1">

     <option value="2" selected="selected">2</option>

     <option value="3" selected="selected">3</option>

     <option value="4" selected="selected">4</option>

     <option value="5" selected="selected">5</option>

     <option value="6" selected="selected">6</option>

     <option value="7" selected="selected">7</option>

     <option value="8" selected="selected">8</option>

     <option value="9" selected="selected">9</option>

     <option value="10" selected="selected">10</option>

     <option value="11" selected="selected">11</option>

     <option value="12" selected="selected">12</option>

 </select>


 <select class="kidAgesSelect" id="agesRoom3_2" name="name" onchange="" tabindex="1">

     <option value="2" selected="selected">2</option>

     <option value="3" selected="selected">3</option>

     <option value="4" selected="selected">4</option>

     <option value="5" selected="selected">5</option>

     <option value="6" selected="selected">6</option>

     <option value="7" selected="selected">7</option>

     <option value="8" selected="selected">8</option>

     <option value="9" selected="selected">9</option>

     <option value="10" selected="selected">10</option>

     <option value="11" selected="selected">11</option>

     <option value="12" selected="selected">12</option>

 </select>


 <select class="kidAgesSelect" id="agesRoom3_3" name="name" onchange="" tabindex="1">

     <option value="2" selected="selected">2</option>

     <option value="3" selected="selected">3</option>

     <option value="4" selected="selected">4</option>

     <option value="5" selected="selected">5</option>

     <option value="6" selected="selected">6</option>

     <option value="7" selected="selected">7</option>

     <option value="8" selected="selected">8</option>

     <option value="9" selected="selected">9</option>

     <option value="10" selected="selected">10</option>

     <option value="11" selected="selected">11</option>

     <option value="12" selected="selected">12</option>

 </select>


 <select class="kidAgesSelect" id="agesRoom3_4" name="name" onchange="" tabindex="1">

     <option value="2" selected="selected">2</option>

     <option value="3" selected="selected">3</option>

     <option value="4" selected="selected">4</option>

     <option value="5" selected="selected">5</option>

     <option value="6" selected="selected">6</option>

     <option value="7" selected="selected">7</option>

     <option value="8" selected="selected">8</option>

     <option value="9" selected="selected">9</option>

     <option value="10" selected="selected">10</option>

     <option value="11" selected="selected">11</option>

     <option value="12" selected="selected">12</option>

 </select>


 <input type="hidden" id="id_div_parent">

 </form>
 </div>

<script type="text/javascript">


    function sendActiveForm() {

        return $py(".booking_form").each(function () {

            if ($py(this).find(".submit_button").hasClass('clicked_button')) {

                if ($py('#booking-search-popup').length) {

                    show_booking_search_popup($py(this));
                    return false;
                }


                $py(this).submit();
                return true;
            }

        });

    }


    function bookingSearchWithAgeSelection() {


        $py(".booking_form").each(function () {
            if ($py(this).find(".submit_button").hasClass('clicked_button')) {

                kids1 = $py(this).find(".children_room_1 option:selected").val();
                kids2 = $py(this).find(".children_room_2 option:selected").val();
                kids3 = $py(this).find(".children_room_3 option:selected").val();


                if ((!kids1 || kids1 == "0") && (!kids2 || kids2 == "0") && (!kids3 || kids3 == "0")) {

                    //nothing to do because booking.js has a $py(".submit_button").click with the submit action
                    //sendActiveForm();


                } else {

                    updateAgesOptions();

                    $py.fancybox.close();

                    $py("#dialog-form").dialog("open");
                }
            }

        });


    }


    function updateAgesOptions() {


        $py(".booking_form").each(function () {

            if ($py(this).find(".submit_button").hasClass("clicked_button")) {

                var numHab = $py(this).find(".rooms_number option:selected").val();
                for (var i = 3; i > 0; i--) {

                    $py("#label" + i).hide();
                    $py("#agesRoom" + i + "_1").hide();
                    $py("#agesRoom" + i + "_2").hide();
                    $py("#agesRoom" + i + "_3").hide();
                    $py("#agesRoom" + i + "_4").hide();


                }


                for (var i = numHab; i > 0; i--) {

                    numKids = $py(this).find(".children_room_" + i + " option:selected").val();


                    if (numKids > 0) {
                        $py("#label" + i).show();

                        $py("#agesRoom" + i + "_1").show();
                        if (numKids > 1)
                            $py("#agesRoom" + i + "_2").show();
                        if (numKids > 2)
                            $py("#agesRoom" + i + "_3").show();
                        if (numKids > 3)
                            $py("#agesRoom" + i + "_4").show();
                    }
                }

            }
        });
    }


    $py(function () {
        $py("#dialog-form").dialog({
                                       width: "auto",
                                       autoOpen: false,
                                       dialogClass: "kids_age_selector_dialog",
                                       modal: true,
                                       buttons: {
                                           "Continuar": function () {
                                               $py(this).dialog("close");

                                               $py("#agesKid1").val($py("#agesRoom1_1").val() + ";" + $py("#agesRoom1_2").val() + ";" + $py("#agesRoom1_3").val() + ";" + $py("#agesRoom1_4").val());
                                               $py("#agesKid2").val($py("#agesRoom2_1").val() + ";" + $py("#agesRoom2_2").val() + ";" + $py("#agesRoom2_3").val() + ";" + $py("#agesRoom2_4").val());
                                               $py("#agesKid3").val($py("#agesRoom3_1").val() + ";" + $py("#agesRoom3_2").val() + ";" + $py("#agesRoom3_3").val() + ";" + $py("#agesRoom3_4").val());

                                               sendActiveForm();

                                           }
                                       }
                                   });

        updateAgesOptions(1);
    });
</script>







     <div id="ticksContainer">

         <div class="ticks PORTUGUESE" id="tick1">
             Pagamento direto no hotel
         </div>
         <div class="ticks PORTUGUESE" id="tick2">
             Sem custos adicionais
         </div>
         <div class="ticks PORTUGUESE" id="tick3">
             Reserva online 100% segura
         </div>

     </div>









</span>


<script>
    $py(function () {
        $py(".hotel_selector_option").each(function () {
            $py(this).click(function () {
                if ($py(this).hasClass("adults_only")) {
                    $py(".children_selector").hide();
                } else {
                    $py(".children_selector").show();
                }

                if ($py(this).is("#luna-olympus")) {
                    $py(".children_selector label .range-age").html("(4-11 anos)");
                    $py(".kidAgesSelect option").each(function () {
                        ($py(this).val() < 4 || $py(this).val() > 11) ? $py(this).css("display", "none") : $py(this).css("display", "block");
                        $py(this).parent().val(11);
                    });
                } else if ($py(this).is("#luna-alvor-village")) {
                    $py(".children_selector label .range-age").html("(4-12 anos)");
                    $py(".kidAgesSelect option").each(function () {
                        ($py(this).val() < 4 || $py(this).val() > 12) ? $py(this).css("display", "none") : $py(this).css("display", "block");
                        $py(this).parent().val(12);
                    });
                } else if ($py(this).is("#luna-oceano")) {
                    $py(".children_selector label .range-age").html("(4-12 anos)");
                    $py(".kidAgesSelect option").each(function () {
                        ($py(this).val() < 4 || $py(this).val() > 12) ? $py(this).css("display", "none") : $py(this).css("display", "block");
                        $py(this).parent().val(12);
                    });
                } else if ($py(this).is("#luna-oura")) {
                    $py(".children_selector label .range-age").html("(4-12 anos)");
                    $py(".kidAgesSelect option").each(function () {
                        ($py(this).val() < 4 || $py(this).val() > 12) ? $py(this).css("display", "none") : $py(this).css("display", "block");
                        $py(this).parent().val(12);
                    });
                } else if ($py(this).is("#luna-arcos")) {
                    $py(".children_selector label .range-age").html("(4-12 anos)");
                    $py(".kidAgesSelect option").each(function () {
                        ($py(this).val() < 4 || $py(this).val() > 12) ? $py(this).css("display", "none") : $py(this).css("display", "block");
                        $py(this).parent().val(12);
                    });
                } else if ($py(this).is("#luna-esperansa")) {
                    $py(".children_selector label .range-age").html("(4-12 anos)");
                    $py(".kidAgesSelect option").each(function () {
                        ($py(this).val() < 4 || $py(this).val() > 12) ? $py(this).css("display", "none") : $py(this).css("display", "block");
                        $py(this).parent().val(12);
                    });
                } else if ($py(this).is("#luna-serra")) {
                    $py(".children_selector label .range-age").html("(4-12 anos)");
                    $py(".kidAgesSelect option").each(function () {
                        ($py(this).val() < 4 || $py(this).val() > 12) ? $py(this).css("display", "none") : $py(this).css("display", "block");
                        $py(this).parent().val(12);
                    });
                } else if ($py(this).is("#luna-carqueijais")) {
                    $py(".children_selector label .range-age").html("(4-12 anos)");
                    $py(".kidAgesSelect option").each(function () {
                        ($py(this).val() < 4 || $py(this).val() > 12) ? $py(this).css("display", "none") : $py(this).css("display", "block");
                        $py(this).parent().val(12);
                    });
                } else if ($py(this).is("#luna-chales")) {
                    $py(".children_selector label .range-age").html("(4-12 anos)");
                    $py(".kidAgesSelect option").each(function () {
                        ($py(this).val() < 4 || $py(this).val() > 12) ? $py(this).css("display", "none") : $py(this).css("display", "block");
                        $py(this).parent().val(12);
                    });
                } else {
                    $py(".children_selector label .range-age").html("(2-12 anos)");
                    $py(".kidAgesSelect option").css("display", "block");
                    $py(".kidAgesSelect").val(12);
                }
            })
        })
    });
</script>


<script async>

    if (typeof only_once_rescueseeker == "undefined") {
        var only_once_rescueseeker = 1;

        $py.getScript("https://global-dot-rescue-seeker.appspot.com/static/rescue-seeker/common/js/rescue-seeker-widget.js")
                .done(function (script, textStatus) {
                          console.log("RESCUE SEEKER: Main rescue seeker script loaded!");
                          RescueSeeker.load('luna-corporate', 'pt', 'paraty');
                      })
                .fail(function (jqxhr, settings, exception) {
                          console.error("RESCUE SEEKER: Main rescue seeker script NOT loaded!");
                          console.error(exception);
                      });

    }

</script>


<script async>
    jQuery(window).load(function () {
        if (typeof only_once_ratecheck == "undefined") {
            var only_once_ratecheck = 1;

            var countryCode = "es";

            $py.getScript("https://global-dot-rescue-seeker.appspot.com/static/rate-check/common/js/rate-check-widget.js")
                    .done(function (script, textStatus) {
                              console.log("RATE CHECK: Main rate chek script loaded!");
                              RateCheck.load('luna-corporate', 'es_pt', 'paraty');
                          })
                    .fail(function (jqxhr, settings, exception) {
                              console.error("RATE CHECK: Main rate chek script NOT loaded!");
                              console.error(exception);
                          });

        }
    });

</script>


</div>
</div>
</div>

<script type="text/javascript" src="https://www.tripadvisor.com/js3/conversion/pixel.js"></script>


<script type="text/javascript">
    $py(document).ready(function () {
        if (typeof(TAPixel) !== "undefined") {
            TAPixel.impressionWithReferer("001F000000vA4u0");
        }
    });
</script>


<!-- Widget Popup -->
<div style="display: none;">
<div id="data" style="width:500px;">
<div id="horizontal_booking_selector">
<div id="full_wrapper_booking" class="booking_horizontal">
<div class="boking_widget_inline">
<link href="//fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,200italic,300italic" rel="stylesheet"
      type="text/css">


<span id="full-booking-engine-html" style="margin:0px;padding:0px;">
<!-- MY BOOKINGS INTEGRATED -->




<!-- TITULO -->
<div class="booking_form_title">
    <h4 class="booking_title_1">Reservas</h4>
    <h4 class="booking_title_2">Reserve agora</h4>
    <h4 class="best_price">RESERVE AO MELHOR PREÇO</h4>


</div>
<!-- END TITULO -->
<!-- booking engine 6 -->
<form class="booking_form  paraty-booking-form" action="https://luna-corporate-dot-luna-hotels.appspot.com/booking1"
      method="post" target="_blank">

<input type="hidden" id="language" name="language" value="PORTUGUESE">
<input type="hidden" id="agesKid1" name="agesKid1">
<input type="hidden" id="agesKid2" name="agesKid2">
<input type="hidden" id="agesKid3" name="agesKid3">

<input id="roomType" type="hidden" name="roomType" value="">
<input class="roomTypeValue" type="hidden" name="roomTypeValue" value="">

<input id="roomFilter" type="hidden" name="roomFilter" value=".*">
<input id="roomFilterName" type="hidden" name="roomFilterName" value="">

<input type="hidden" id="namespace" name="namespace" value="luna-corporate">

<input type="hidden" id="gclid" name="gclid" value="">
<input type="hidden" id="_ga" name="_ga" value=""/>

<input type="hidden" value="" name="priceSeekerHotel" id="priceSeekerHotel">


<!-- BOOKING 0 -->
<input type="hidden" id="applicationIds" name="applicationIds" value="">
<!-- END BOOKING 0-->

<!-- SELECTOR DE HOTEL-->


<div class="destination_wrapper">
    <label for="destination">Selecionar Hotel</label>

    <select class="location_group_select">

        <option value="portugal"
                namespaces="luna-olympus;luna-oura;luna-fatima;luna-esperansa;luna-oceano;luna-alvor-village;luna-alvor-bay;luna-serra;luna-carqueijais;luna-chales;luna-arcos;luna-abrantes;luna-solaqua">
            PORTUGAL
        </option>

        <option value="algarve"
                namespaces="luna-oceano;luna-oura;luna-alvor-bay;luna-alvor-village;luna-olympus;luna-solaqua">
            Algarve
        </option>

        <option value="albufeira" namespaces="luna-oceano;luna-oura;luna-solaqua">
            Albufeira
        </option>

        <option value="alvor" namespaces="luna-alvor-bay;luna-alvor-village">
            Alvor
        </option>

        <option value="vilamoura" namespaces="luna-olympus">
            Vilamoura
        </option>

        <option value="setubal" namespaces="luna-esperansa">
            Setúbal
        </option>

        <option value="abrantes" namespaces="luna-abrantes">
            Abrantes
        </option>

        <option value="arcos" namespaces="luna-arcos">
            Arcos de Valdevez
        </option>

        <option value="fatima" namespaces="luna-fatima">
            Fátima
        </option>

        <option value="serra_estrela" namespaces="luna-serra;luna-chales;luna-carqueijais">
            Serra da Estrela
        </option>

        <option value="angola" namespaces="luna-zombo">
            ANGOLA
        </option>

        <option value="luanda" namespaces="luna-zombo">
            Luanda
        </option>

        <option value="all_hotels"
                namespaces="luna-olympus;luna-oura;luna-fatima;luna-esperansa;luna-oceano;luna-alvor-village;luna-alvor-bay;luna-zombo;luna-serra;luna-carqueijais;luna-chales;luna-arcos;luna-abrantes;luna-solaqua">
            TODAS
        </option>

    </select>

    <div class="destination_field">
        <input class="destination" readonly="readonly" type="text" name="destination" placeholder="Hotel" value="">
        <input type="hidden" id="default_destination_placeholder" name="default_destination_placeholder"
               class="default_destination_placeholder" value="Hotel">

        <div class="right_arrow"></div>
    </div>
</div>


<div class="hotel_selector">
<div class="hotel_selector_inner">
<div class="close_hotel_selector" style="background: url(/static_1/lib/fancybox/source/fancybox_sprite.png);"></div>


<ul class="portugal num-ul-1">

    <li class="title_group"><h3>PORTUGAL</h3></li>


    <li id="luna-olympus" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Olympus ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-olympus" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-olympus" value="luna-olympus">




    <li id="luna-oura" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel da Oura ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oura" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oura" value="luna-oura">


    <li id="luna-fatima" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Fátima Hotel ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-fatima" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-fatima" value="luna-fatima">


    <li id="luna-esperansa" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Esperança Centro ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-esperansa" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-esperansa" value="luna-esperansa">


    <li id="luna-oceano" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Clube Oceano ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oceano" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oceano" value="luna-oceano">





    <li id="luna-alvor-village" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Alvor Village ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-village" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-village" value="luna-alvor-village">


    <li id="luna-alvor-bay" class=" hotel_selector_option adults_only">
        <h3 class="title_selector">Luna Alvor Bay ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-bay" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-bay" value="luna-alvor-bay">


    <li id="luna-serra" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel Serra da Estrela ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-serra" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-serra" value="luna-serra">


    <li id="luna-carqueijais" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel dos Carqueijais ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-carqueijais" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-carqueijais" value="luna-carqueijais">


    <li id="luna-chales" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Chalets da Montanha</h3>
    </li>
    <input type="hidden" id="url_booking_luna-chales" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-chales" value="luna-chales">


    <li id="luna-arcos" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Arcos Hotel ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-arcos" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-arcos" value="luna-arcos">


    <li id="luna-abrantes" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel Turismo ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-abrantes" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-abrantes" value="luna-abrantes">

    <li id="luna-solaqua" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Solaqua ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-solaqua" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-solaqua" value="luna-solaqua">
</ul>


<ul class="algarve num-ul-2">

    <li class="title_group"><h3>Algarve</h3></li>


    <li id="luna-oceano" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Clube Oceano ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oceano" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oceano" value="luna-oceano">


    <li id="luna-oura" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel da Oura ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oura" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oura" value="luna-oura">





    <li id="luna-alvor-bay" class=" hotel_selector_option adults_only">
        <h3 class="title_selector">Luna Alvor Bay ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-bay" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-bay" value="luna-alvor-bay">


    <li id="luna-alvor-village" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Alvor Village ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-village" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-village" value="luna-alvor-village">





    <li id="luna-olympus" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Olympus ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-olympus" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-olympus" value="luna-olympus">

    <li id="luna-solaqua" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Solaqua ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-solaqua" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-solaqua" value="luna-solaqua">
</ul>


<ul class="albufeira num-ul-3">

    <li class="title_group"><h3>Albufeira</h3></li>


    <li id="luna-oceano" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Clube Oceano ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oceano" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oceano" value="luna-oceano">


    <li id="luna-oura" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel da Oura ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oura" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oura" value="luna-oura">





</ul>


<ul class="alvor num-ul-4">

    <li class="title_group"><h3>Alvor</h3></li>


    <li id="luna-alvor-bay" class=" hotel_selector_option adults_only">
        <h3 class="title_selector">Luna Alvor Bay ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-bay" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-bay" value="luna-alvor-bay">


    <li id="luna-alvor-village" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Alvor Village ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-village" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-village" value="luna-alvor-village">





</ul>


<ul class="vilamoura num-ul-5">

    <li class="title_group"><h3>Vilamoura</h3></li>


    <li id="luna-olympus" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Olympus ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-olympus" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-olympus" value="luna-olympus">


</ul>


<ul class="setubal num-ul-6">

    <li class="title_group"><h3>Setúbal</h3></li>


    <li id="luna-esperansa" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Esperança Centro ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-esperansa" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-esperansa" value="luna-esperansa">


</ul>


<ul class="abrantes num-ul-7">

    <li class="title_group"><h3>Abrantes</h3></li>


    <li id="luna-abrantes" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel Turismo ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-abrantes" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-abrantes" value="luna-abrantes">


</ul>


<ul class="arcos num-ul-8">

    <li class="title_group"><h3>Arcos de Valdevez</h3></li>


    <li id="luna-arcos" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Arcos Hotel ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-arcos" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-arcos" value="luna-arcos">


</ul>


<ul class="fatima num-ul-9">

    <li class="title_group"><h3>Fátima</h3></li>


    <li id="luna-fatima" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Fátima Hotel ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-fatima" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-fatima" value="luna-fatima">


</ul>


<ul class="serra_estrela num-ul-10">

    <li class="title_group"><h3>Serra da Estrela</h3></li>


    <li id="luna-serra" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel Serra da Estrela ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-serra" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-serra" value="luna-serra">


    <li id="luna-chales" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Chalets da Montanha</h3>
    </li>
    <input type="hidden" id="url_booking_luna-chales" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-chales" value="luna-chales">


    <li id="luna-carqueijais" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel dos Carqueijais ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-carqueijais" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-carqueijais" value="luna-carqueijais">


</ul>


<ul class="angola num-ul-11">

    <li class="title_group"><h3>ANGOLA</h3></li>


    <li id="luna-zombo" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Zombo ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-zombo" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-zombo" value="luna-zombo">


</ul>


<ul class="luanda num-ul-12">

    <li class="title_group"><h3>Luanda</h3></li>


    <li id="luna-zombo" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Zombo ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-zombo" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-zombo" value="luna-zombo">


</ul>


<ul class="all_hotels num-ul-13">

    <li class="title_group"><h3></h3></li>


    <li id="luna-olympus" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Olympus ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-olympus" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-olympus" value="luna-olympus">




    <li id="luna-oura" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel da Oura ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oura" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oura" value="luna-oura">


    <li id="luna-fatima" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Fátima Hotel ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-fatima" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-fatima" value="luna-fatima">


    <li id="luna-esperansa" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Esperança Centro ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-esperansa" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-esperansa" value="luna-esperansa">


    <li id="luna-oceano" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Clube Oceano ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-oceano" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-oceano" value="luna-oceano">





    <li id="luna-alvor-village" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Alvor Village ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-village" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-village" value="luna-alvor-village">


    <li id="luna-alvor-bay" class=" hotel_selector_option adults_only">
        <h3 class="title_selector">Luna Alvor Bay ***</h3>
    </li>
    <input type="hidden" id="url_booking_luna-alvor-bay" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-alvor-bay" value="luna-alvor-bay">


    <li id="luna-zombo" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Zombo ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-zombo" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-zombo" value="luna-zombo">


    <li id="luna-serra" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel Serra da Estrela ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-serra" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-serra" value="luna-serra">


    <li id="luna-carqueijais" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel dos Carqueijais ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-carqueijais" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-carqueijais" value="luna-carqueijais">


    <li id="luna-chales" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Chalets da Montanha</h3>
    </li>
    <input type="hidden" id="url_booking_luna-chales" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-chales" value="luna-chales">


    <li id="luna-arcos" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Arcos Hotel ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-arcos" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-arcos" value="luna-arcos">


    <li id="luna-abrantes" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Hotel Turismo ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-abrantes" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-abrantes" value="luna-abrantes">

    <li id="luna-solaqua" class=" hotel_selector_option ">
        <h3 class="title_selector">Luna Solaqua ****</h3>
    </li>
    <input type="hidden" id="url_booking_luna-solaqua" value="-dot-luna-hotels.appspot.com/booking1">
    <input type="hidden" id="namespace_luna-solaqua" value="luna-solaqua">
</ul>

<div class="close_hotel_selector"></div>
</div>
</div>


<!-- END SELECTOR DE HOTEL-->


<!-- HIDE FILTERS -->


<!-- HIDE FILTERS -->

<!-- SELECCION FECHAS DE ENTRADA Y NUMERO HABITACIONES -->
<div class="entry_date_wrapper">
    <label>Chegada</label>

    <div class="date_box entry_date">
        <span class="date_day"></span>
        <span class="date_year"></span>
        <input name="startDate" type="hidden" class="has_datepicker entry_input">

        <div></div>
    </div>
</div>

<div class="departure_date_wrapper">
    <label>Saída</label>

    <div class="date_box departure_date">
        <span class="date_day"></span>
        <span class="date_year"></span>
        <input name="endDate" type="hidden" class="has_datepicker departure_input">

        <div></div>
    </div>
</div>


<div class="guest_selector">Ocupação</div>

<!-- END SELECCION FECHAS DE ENTRADA Y HABITACIONES -->


<!-- LISTA DE HABITACIONES -->
<div class="room_list_wrapper">


<div class="rooms_number_wrapper">
    <label>Quartos</label>
    <select name="numRooms" class="rooms_number">
        <option value="1" selected="selected">1</option>
        <option value="2">2</option>
        <option value="3">3</option>
    </select>
</div>

<ul class="room_list">

<!---------------------- ROOM 1---------------------->
<li class="room room1">

    <div class="room_title  "> Quarto 1</div>

    <div class="adults_selector">
        <label for="adultsRoom1">Adultos</label>
        <select name="adultsRoom1" class="adults_room_1 room_selector">

            <option value="1">1</option>

            <option value="2" selected="selected">2</option>

            <option value="3">3</option>

            <option value="4">4</option>

            <option value="5">5</option>

            <option value="6">6</option>

            <option value="7">7</option>

            <option value="8">8</option>

        </select>
    </div>

    <div class="children_selector">
        <label for="childrenRoom1">Crianças

        <span class="range-age">


              (2-12&nbsp;anos)



        </span>

        </label>
        <select name="childrenRoom1" class="children_room_1 room_selector">

            <option value="0" selected="selected">

                0

            </option>

            <option value="1">

                1

            </option>

            <option value="2">

                2

            </option>

            <option value="3">

                3

            </option>

            <option value="4">

                4

            </option>

        </select>
    </div>


</li>

<!---------------------- ROOM 2---------------------->
<li class="room room2" style="display: none">

    <div class="room_title ">Quarto 2</div>

    <div class="adults_selector">
        <label for="adultsRoom2">Adultos</label>
        <select name="adultsRoom2" class="adults_room_2 room_selector">

            <option value="1">1</option>

            <option value="2" selected="selected">2</option>

            <option value="3">3</option>

            <option value="4">4</option>

            <option value="5">5</option>

            <option value="6">6</option>

            <option value="7">7</option>

            <option value="8">8</option>

        </select>
    </div>

    <div class="children_selector">
        <label for="childrenRoom2">Crianças


        <span class="range-age">


              (2-12&nbsp;anos)



        </span>


        </label>
        <select name="childrenRoom2" class="children_room_2 room_selector">

            <option value="0" selected="selected">

                0

            </option>

            <option value="1">

                1

            </option>

            <option value="2">

                2

            </option>

            <option value="3">

                3

            </option>

            <option value="4">

                4

            </option>

        </select>
    </div>


</li>

<!---------------------- ROOM 3 ---------------------->
<li class="room room3" style="display: none">

    <div class="room_title ">Quarto 3</div>

    <div class="adults_selector">
        <label for="adultsRoom3">Adultos</label>
        <select name="adultsRoom3" class="adults_room_3 room_selector">

            <option value="1">1</option>

            <option value="2" selected="selected">2</option>

            <option value="3">3</option>

            <option value="4">4</option>

            <option value="5">5</option>

            <option value="6">6</option>

            <option value="7">7</option>

            <option value="8">8</option>

        </select>
    </div>

    <div class="children_selector">
        <label for="childrenRoom3">Crianças


        <span class="range-age">


              (2-12&nbsp;anos)



        </span>


        </label>
        <select name="childrenRoom3" class="children_room_3 room_selector">

            <option value="0" selected="selected">

                0

            </option>

            <option value="1">

                1

            </option>

            <option value="2">

                2

            </option>

            <option value="3">

                3

            </option>

            <option value="4">

                4

            </option>

        </select>
    </div>


</li>


</ul>
</div>
<!-- END LISTA DE HABITACIONES -->


<div class="wrapper_booking_button ">

    <input type="text" class="promocode_input " placeholder="Promocode" name="promocode" value=""
           tabindex="16">

    <button type="button" onclick="$py(this).addClass('clicked_button');bookingSearchWithAgeSelection();return false;"
            class="submit_button buttonsearch-ratecheck">


        Reservar


    </button>
    <div class="spinner_wrapper"></div>


</div>

<div style="clear:both"></div>


</form>





<style>
    .ui-dialog {
        position: absolute;
        top: 0;
        left: 0;
        padding: .2em;
        outline: 0;
    }

    .ui-dialog .ui-dialog-titlebar {
        padding: .4em 1em;
        position: relative;
    }

    .ui-dialog .ui-dialog-title {
        float: left;
        margin: .1em 0;
        white-space: nowrap;
        width: 90%;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .ui-dialog .ui-dialog-titlebar-close {
        position: absolute;
        right: .3em;
        top: 50%;
        width: 21px;
        margin: -10px 0 0 0;
        padding: 1px;
        height: 20px;
    }

    .ui-dialog .ui-dialog-content {
        position: relative;
        border: 0;
        padding: .5em 1em;
        background: none;
        overflow: auto;
        text-align: center;
    }

    .ui-dialog .ui-dialog-buttonpane {
        text-align: left;
        border-width: 1px 0 0 0;
        background-image: none;
        margin-top: .5em;
        padding: .3em 1em .5em .4em;
    }

    .ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
        /*float: right;*/
        float: none;
        text-align: center;
    }

    .ui-dialog .ui-dialog-buttonpane button {
        margin: .5em .4em .5em 0;
        cursor: pointer;
    }

    .ui-dialog .ui-resizable-se {
        width: 12px;
        height: 12px;
        right: -5px;
        bottom: -5px;
        background-position: 16px 16px;
    }

    .ui-dialog .ui-button-text {
        /*padding: 10px;*/
        /*margin: 10px;*/
    }

    .ui-dialog .ui-button {
        /*height: 30px;*/
    }

    .ui-draggable .ui-dialog-titlebar {
        cursor: move;
    }

    .ui-dialog {
        min-width: 300px;
        z-index: 9999;
    }

</style>

 <style>
     .ui-dialog-titlebar-close {
         display: none !important;
     }

     #dialog-form {
         padding: 10px;

     }

     .ui-dialog-titlebar {
         padding: 5px;
     }

     .kidAgesSelect {
         margin-left: 10px;

     }

     .ui-dialog {
         z-index: 99999;
     }


 </style>
<!--  <link rel="stylesheet" href="https://code.jquery.com/ui/1.10.1/themes/base/jquery-ui.css" type="text/css"/> -->











<script>
    $(function(){

        $py("body").on("classChange", function(){
            $py(".entry_input").datepicker("hide");
            $py(".departure_input").datepicker("hide");
        });

        body_selector = $py("body");
        body_fixed = false;
        $py(window).scroll(function(){
            if(body_selector.hasClass('Fixed') && !body_fixed){
                body_fixed = true;
                body_selector.trigger('classChange');
            }

            if(!body_selector.hasClass('Fixed') && body_fixed){
                body_fixed = false;
                body_selector.trigger('classChange');
            }
        })
    })
</script>






</span>


</div>
</div>
</div>
</div>
</div>

<script src="https://luna-corporate-dot-luna-hotels.appspot.com/static_1/scripts/url_modifier.js?v=1"></script>