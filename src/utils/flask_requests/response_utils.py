import logging
import time

from flask import g
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.language.language_utils import get_language_selected_from_session
from booking_process.utils.request.cookies_utils import get_cookie_domain


def add_response_headers(headers: dict):

    if not hasattr(g, 'response_headers'):
        g.response_headers = {}

    # Controlled invalid headers (not allowed to be set)
    cleaned_headers = {}
    for header_name, header_value in headers.items():
        cleaned_headers[header_name] = header_value.replace('\n', '').replace('\r', '')

    g.response_headers.update(cleaned_headers)


def add_response_cookie(name, value, max_age=None, secure=None, path=None):

    if not hasattr(g, 'response_cookies'):
        g.response_cookies = []

    cookie_data = {
        'name': name,
        'value': value,
        'max_age': max_age,
        'secure': secure
    }

    if path:
        cookie_data['path'] = path

    if target_domain := get_cookie_domain(name, get_language_selected_from_session()):
        cookie_data['domain'] = target_domain

    g.response_cookies.append(cookie_data)
    g.response_cookies.append((name, value, max_age, secure, path))


def delete_cookie(name):
    if not hasattr(g, 'response_delete_cookies'):
        g.response_delete_cookies = []

    g.response_delete_cookies.append(name)


def set_response_redirection(url: str, status_code: int = 302, permanent: bool = False, delay=False):

    if permanent:
        status_code = 301

    if delay:
        logging.info("redirecting to %s with delay: %s", url, delay)
        time.sleep(int(delay))

    if type(status_code) != int:
        logging.info("Status code is not int, by default will be temporally redirection")
        status_code = 302

    logging.info(f"Setting redirection to {url} with status code {status_code}")

    g.response_redirection = (url, status_code)
    set_response_status_code(status_code)
    add_response_headers({'Location': url})


def get_response_redirection():
    if hasattr(g, 'response_redirection'):
        return g.response_redirection

    return None


def get_response_headers():

    if hasattr(g, 'response_headers'):
        return g.response_headers

    return {}


def set_response_status_code(status_code: int):
    g.response_status_code = status_code


def get_response_status_code():

    if hasattr(g, 'response_status_code'):
        return g.response_status_code

    return 200


def clear_response_content():
    if hasattr(g, 'response_content'):
        g.response_content = ''


def add_to_response_content(content: str):

    if not hasattr(g, 'response_content'):
        g.response_content = ''

    if content:
        g.response_content += content


def get_response_content():

    if hasattr(g, 'response_content'):
        return g.response_content

    return ''


def get_response_cookies():

    if hasattr(g, 'response_cookies'):
        return g.response_cookies

    return []


def add_file_to_response_content(file_content: bytes):
    if file_content:
        g.response_content = file_content


def get_response_delete_cookies():
    if hasattr(g, 'response_delete_cookies'):
        return g.response_delete_cookies

    return []


def set_native_response(use_native: bool):
    g.use_native_response = use_native


def use_native_response():
    if hasattr(g, 'use_native_response'):
        return g.use_native_response

    return False


class CustomResponse:
    cookies = {}
    content = {}
    status_code = 200
    headers = {}
    redirection_url = None

    def set_cookie(self, name, value, max_age=None):
        self.cookies[name] = {'value': value, 'max_age': max_age}


def process_custom_response(custom_response):
    if custom_response:
        if custom_response.cookies:
            for cookie_name, cookie_info in custom_response.cookies.items():
                if cookie_info['max_age']:
                    add_response_cookie(cookie_name, cookie_info['value'], cookie_info['max_age'])
                else:
                    add_response_cookie(cookie_name, cookie_info['value'])

        if custom_response.content:
            add_to_response_content(custom_response.content)

        if custom_response.status_code:
            set_response_status_code(custom_response.status_code)

        if custom_response.headers:
            add_response_headers(custom_response.headers)

        if custom_response.redirection_url:
            set_response_redirection(custom_response.redirection_url)
