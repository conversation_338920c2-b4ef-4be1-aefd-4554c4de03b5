div#full_wrapper_booking.booking_engine_wrapper_process {
  background-color: $corporate_2;
  * {
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
  }
  div#booking.boking_widget_inline {
    .stay_selection {
      width: 371px !important;
      .entry_date_wrapper {
        &:after {
          left: 0;
        }
      }
      .departure_date_wrapper {
        &:after {
          right: 0;
        }
      }
      .entry_date_wrapper, .departure_date_wrapper {
        background: transparent;
        border-bottom: 0;
        height: 77px;
        position: relative;
        &:after {
          content: '';
          width: 1px;
          height: 60px;
          background: rgba(white, .6);
          @include center_y;
        }
        label {
          font-family: "Playfair Display", serif;
          font-weight: 700;
          font-size: 12px;
          letter-spacing: 1px;
          color: white;
        }
        .date_box {
          background: transparent;
          margin-top: -5px;
          span.date_day {
            color: white;
            font-family: "Playfair Display", serif;
            font-weight: 700;
            span {
              display: inline-block;
              padding: 0 3px 0 0;
            }
            .day {
              font-size: 50px;
              line-height: 1;
              font-weight: 300;
            }
            &:after {
              content: '\f107';
              display: inline-block;
              font-size: 14px;
              @extend .fa;
              color: lightgrey;
              position: absolute;
              bottom: 5px;
              right: 15px;
            }
          }
        }
      }
    }
    .guest_selector {
      background: transparent;
      width: 290px !important;
      margin-top: 10px;
      &:after {
        content: '';
        width: 1px;
        height: 60px;
        background: rgba(white, .6);
        @include center_y;
        right: 0  ;
      }
      label {
        font-family: "Playfair Display", serif;
        font-weight: 700;
        font-size: 12px;
        letter-spacing: 1px;
        margin-top: -10px;
        margin-bottom: 10px;
        color: white;
      }
      .placeholder_text {
        color: white;
        font-size: 35px;
        font-family: "Playfair Display", serif;
        font-weight: 300;
        letter-spacing: -3px;
        margin-top: 15px;
        span {
          font-size: 12px;
          letter-spacing: 0;
          font-weight: 700;
        }
      }
    }
    .wrapper_booking_button {
      width: 470px !important;
      vertical-align: top;
      margin-top: 10px;
      .promocode_wrapper {
        padding: 20px 10px;
        width: 48%;
        label.promocode_label {
          font-weight: 300;
          font-size: 12px;
          line-height: 12px;
          letter-spacing: 1px;
        }
      }
      .spinner_wrapper {
        background-color: $corporate_3;
        height: 90px;
        width: calc(52% - 2px);
        margin-top: -15px;
      }
      .submit_button {
        position: absolute;
        top: -15px;
        right: 0;
        height: 90px;
        width: 52%;
        background-color: $corporate_3;
          @include transition(background, 0s);
        &:hover {
          opacity: 1;
          background: transparent;
          &:before {
            width: 100%;
          }
        }

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          background: darken($corporate_2, 10%);
          width: 0;
          z-index: -1;
          -webkit-transition: width 0.4s;
          -moz-transition: width 0.4s;
          -ms-transition: width 0.4s;
          -o-transition: width 0.4s;
          transition: width 0.4s;
        }
        &:after {
          content: '';
          position: absolute;
          top: 10px;
          left: 10px;
          right: 10px;
          bottom: 10px;
          z-index: 2;
          border: 2px solid white;
          border-image: 33.334% url('data:image/svg+xml,<svg xmlns="http:%2F%2Fwww.w3.org%2F2000%2Fsvg" width="30" height="30"><rect width="45%" height="100%" fill="#fff"/><rect y="0" width="100%" height="50%" fill="#fff"/><rect x="55%" width="45%" height="100%" fill="#fff"/></svg>');
          border-image-repeat: no-repeat;
        }
      }
    }
  }
}

.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
  .datepicker_ext_inf_sd,
  .datepicker_ext_inf_ed {
    .ui-state-hover, .ui-state-active {
      background: $corporate_2 !important;
      color: white !important;
      span::before {
        color: white !important;
      }
    }
  }
}