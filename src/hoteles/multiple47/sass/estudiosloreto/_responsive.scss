@mixin promocode_placeholder_styles($size, $color) {
  color: $color;
  font-size: $size;
  font-weight: 400;
  text-transform: uppercase;
  font-family: $title_family;
  letter-spacing: 1px;
  text-align: center;
  margin: 0;
  border: 0;
  padding: 0;
  box-shadow: none;
}

@mixin promocode_styles($size, $color) {
  @include promocode_placeholder_styles($size, $color);

  &::-webkit-input-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
  &::-moz-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
  &:-ms-input-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
  &:-moz-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
}

@media (max-width: 1165px) and (min-width: 1001px) {
  #widget_paraty #full_wrapper_booking {
    width: auto;

    #full-booking-engine-html-7 .booking_form {
      justify-content: center;

      .stay_selection {
        width: 340px;
      }

      .guest_selector {
        width: 200px;
      }

      .wrapper_booking_button {
        position: relative;
        right: 0px;
        width: 410px;
        display: flex;
        flex-wrap: nowrap;
      }
    }
  }
}

@media (min-width: 1001px) {
  #widget_paraty {
    #full_wrapper_booking {
      display: block !important;
    }
  }

  #floating_button_paraty {
    display: none;
  }
  .datepicker_wrapper_element,
  .datepicker_wrapper_element_2,
  .datepicker_wrapper_element_3 {
    top: auto!important;
    bottom: 120px
  }
}

@media (max-width: 1000px) {
  .datepicker_wrapper_element .specific_month_selector,
  .datepicker_wrapper_element .go_back_button,
  .datepicker_wrapper_element_2 .specific_month_selector,
  .datepicker_wrapper_element_2 .go_back_button,
  .datepicker_wrapper_element_3 .specific_month_selector,
  .datepicker_wrapper_element_3 .go_back_button {
    display: none !important;
  }
  .datepicker_wrapper_element,
  .datepicker_wrapper_element_2,
  .datepicker_wrapper_element_3 {
    z-index: 100000002;
  }

  body {
    #widget_paraty {
      background: none;
      padding: 0;
      margin: 0;
      @include transform(none);
      width: 0;
      height: 0;
      padding: 0 !important;
      -webkit-box-shadow: none;
      -moz-box-shadow: none;
      box-shadow: none;

      &.floating_widget {
        @include transform(none);
        padding: 0;
        top: auto;
        bottom: auto;
        left: auto;
        right: auto;
        box-shadow: none;
      }

      #full_wrapper_booking {
        display: none;
        position: fixed;
        overflow: scroll;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100% !important;
        background-color: $black;
        z-index: 1100;

        #full-booking-engine-html-7 {
          @include center_xy;
          top: calc(50% - 50px);
          width: 95%;
          text-align: center;

          .booking_form {
            max-width: 400px;
            width: 100%;
            flex-direction: column;
            flex-wrap: wrap;

            .stay_selection {
              display: flex;
              width: 100%;
              flex-wrap: wrap;

              .entry_date_wrapper, .departure_date_wrapper {
                background-color: transparent;
                width: 100%;
                height: auto;

                &:before {
                  color: white;
                }

                .date_box .date_day {
                  color: $corporate_1;
                }

                > label {
                  font-size: 20px;
                  font-weight: 600;
                  color: white !important;
                }
              }

              &:before {
                left: 50% !important;
                transform: translateX(-100%);
              }
            }

            .rooms_number_wrapper {
              width: 100%;
              height: auto;
              background-color: transparent;

              &:before {
                bottom: 15px;
              }

              &:after {
                display: none;
              }

              .rooms_label {
                font-size: 12px;
                color: $corporate_1 !important;
              }

              .selectricWrapper {
                &:before {
                  font-size: 12px;
                  color: $corporate_1;
                }

                .selectric {
                  p.label {
                    font-size: 28px;
                  }
                }
              }
            }


            .guest_selector {
              margin-top: 0;
              width: 100%;
              height: auto;
              background-color: transparent;

              &:before {
                color: $corporate_1;
              }

              &:after {
                //display: none;
              }

              > label {
                font-size: 20px;
                font-weight: 600;
                color: white !important;
              }

              .placeholder_text {
                color: $corporate_1;
                font-size: 15px;
                line-height: 17px;

                .adults,
                .kids {
                  font-size: 15px;
                  line-height: 17px;
                  line-height: 1;
                }
              }
            }

            .room_list_wrapper {
              z-index: 10;
              width: 100%;
              right: 0;
              top: calc(50% - 40px) !important;

              .room_list {
                padding-top: 20px;
                background-color: white;

                .full_ages_wrapper {
                  label {
                    text-align: center;
                    font-size: 10px;
                  }

                  .kids_age_selection {
                    margin-left: 0;
                  }
                }

                .room {
                  width: auto !important;
                  margin: auto;
                  justify-content: center;

                  .adults_selector, .children_selector, .babies_selector {
                    > label {
                      font-size: 16px;

                      .range-age {
                        font-size: 9px;
                      }
                    }

                    .selectricWrapper {
                      &:before {
                        font-size: 10px;
                      }

                      .selectric {
                        .label {
                          font-size: 16px;
                        }
                      }
                    }
                  }

                  &.room_with_babies {
                    width: 100%;
                  }
                }
              }
            }

            .wrapper_booking_button {
              margin-top: 300px;
              display: inline-flex;
              flex-wrap: wrap;
              width: 100%;

              .promocode_wrapper {
                width: 100%;
                height: 60px;
                border: 1px dashed $corporate_1;
                margin-bottom: 10px;
                top: -35px;

                .promocode_label {
                  display: none !important;
                }

                .promocode_input {
                  width: 100%;
                  transform: none !important;
                }

                input.promocode_input {
                  @include promocode_styles(14px, $label_color);
                }

                &:after {
                  display: none;
                }
              }

              .submit_button {
                width: 100%;
                height: 60px;
                font-size: 16px;
                padding: 5px 0;
                top: 0;
                position: relative;

                &:before, &:after {
                  display: none;
                }
              }
            }
          }
        }
      }

      .close_widget {
        position: fixed;
        top: 10px;
        right: 30px;
        text-align: right;
        color: white;
        display: block;
        font-size: 24px;
      }
    }

    #floating_button_paraty {
      display: inline-block;
      width: 100%;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      text-align: center;
      font-family: $text_family;
      background-color: $corporate_1;
      color: white;
      border: 1px solid $corporate_1;
      cursor: pointer;
      z-index: 10;
      text-transform: none;
      padding: 20px 10px;

      &.hidden {
        display: none !important;
      }
    }
  }

  .fancy-booking-search_v2 .container_popup_booking {
    max-width: 100%;
    width: 100%;
  }
  .datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
    @include center_xy;
    top: 50% !important;
    left: 50% !important;
    position: fixed;
  }
  .absolute-footer {
    padding: 10px 0 60px;
  }
}