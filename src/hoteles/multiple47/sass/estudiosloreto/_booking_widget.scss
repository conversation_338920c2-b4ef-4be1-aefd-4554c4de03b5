#widget_paraty {
  position: fixed;
  bottom: 0;
  top: auto;
  z-index: 1100;
  width: 100%;
  margin-top: 0;
  color: $corporate_1;
  border-right: 15px;
  border-radius: 0;
  text-align: center;
  margin-top: 20px;
  background: $widget_bg;


  [class^="icon-"], [class*=" icon-"] {
    font-family: 'icomoon' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  &.inner_widget {
    position: relative;
    @include transform(none);
    bottom: auto;
    left: auto;
    margin-top: 110px;
    padding: 20px;
  }

  &.floating_widget {
    @include center_x;
    position: fixed;
    top: 0;
    bottom: auto;
    margin-top: 0;
    max-width: none;
    padding: 0;
    background-color: $widget_bg;
    width: 100vw;
    @include box_shadow;

    #full_wrapper_booking {
      width: 1120px;
      margin: auto;

      .rooms_number_wrapper {
        .selectricWrapper {
          .selectricItems {
            bottom: 80px !important;
            top: auto;
          }
        }
      }

      .room_list_wrapper {
        top: calc(100% + 17px) !important;
        bottom: auto !important;
      }

      .wrapper_booking_button {
        .submit_button {
          border-radius: 0 !important;
        }
      }
    }
  }

  * {
    box-sizing: border-box;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    line-height: 1;
  }

  #full_wrapper_booking {
    position: relative;
    width: 1120px;
    margin: auto;

    #full-booking-engine-html-7 {
      z-index: 2;
      display: block;
      width: 100%;

      .booking_form_title {
        display: none;
      }

      .booking_form {
        position: relative;
        display: inline-flex;
        justify-content: flex-start;
        text-align: left;
        margin-bottom: 0;
        width: 100%;

        .stay_selection {
          position: relative;
          display: flex;
          flex-wrap: nowrap;
          width: 390px;
          border-radius: 0;
          overflow: hidden;


          .entry_date_wrapper, .departure_date_wrapper {
            @include input_base_styles;
            @include option_subborder;
            @include date_icon;
            width: 50%;
            padding:20px 11px;

            label {
              @include label_styles;
            }

            .date_box {
              text-align: left;

              .date_day {
                font-size: 18px;
                @include option_styles;
                border-bottom: none !important;

                > div {
                  display: inline-block;
                }

                .day {
                  font-size: 25px;
                }
              }

              .date_year {
                display: none;
              }
            }
            &::before{
              content: "";
              width: 10px;
              height: 10px;
              background: none;
              position: absolute;
              display: block;
              border-right: 1px solid white;
              border-bottom: 1px solid white;
              transform: rotate(45deg) translate(-100%, -0%);
            }
          }
        }

        .dates_selector_personalized {
          display: none;
        }

        .rooms_number_wrapper {
          display: none;
        }

        .guest_selector {
          @include input_base_styles;
          @include option_subborder;
          width: 300px;
          padding:20px 11px;

          label {
            @include label_styles;
          }

          .placeholder_text {
            @include option_styles;
            display: block;
            text-align: left;

            .kids, .babies {
              @include option_styles;
            }
          }

          b.button {
            display: none;
          }
        }

        .room_list_wrapper {
          display: none;
          position: absolute;
          bottom: calc(100% + 12px);
          right: 390px;
          text-align: center;
          width: 400px;
          box-sizing: unset;
          background-color: white;
          color: $corporate_1;
          border: none;
          box-shadow: 0px 10px 22px -5px rgba(0, 0, 0, 0.33);


          .room_list {
            margin: 0;
            padding: 10px;
            list-style: none;
            width: 100%;
            display: inline-block;

            .room {
              margin: 0;
              display: flex;
              flex-wrap: nowrap;


              &.room2, &.room3 {
                .adults_selector, .children_selector, .babies_selector {
                  > label {
                    display: none;
                  }
                }
              }


              .room_title {
                display: none;
              }

              .adults_selector, .children_selector, .babies_selector {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                flex: 1;
                position: relative;

                > label {
                  display: flex !important;
                  flex-direction: column;
                  text-align: center !important;
                  @include label_styles;
                  color: $black !important;
                  font-size: 11px;

                  .range-age {
                    font-size: 10px;
                  }
                }

                .selectricWrapper {
                  &:before {
                    color: white;
                    font-size: 12px;
                    right: 25px;
                  }

                  .selectric {
                    cursor: pointer;

                    .label {
                      @include option_styles;
                      color: $corporate_1 !important;
                      padding: 5px 0 !important;
                    }

                    .button {
                      display: none;
                    }
                  }
                }
              }

              .children_selector, .babies_selector {
                &:after {
                  left: 0;
                  top: 5px;
                  height: calc(100% - 10px);
                }
              }

              .children_selector {
                visibility: visible !important;
              }
            }
          }

          .add_remove_room_wrapper {
            display: flex;
            align-items: center;

            .add_room,
            .remove_room {
              width: 50%;
              color: white;
              padding: 10px;
              cursor: pointer;
            }

            .add_room {
              background-color: $corporate_1;
            }

            .remove_room {
              background-color: $black;
            }
          }
        }

        .wrapper_booking_button {
          position: absolute;
          top: 0;
          bottom: 0;
          right: 0;
          width: 430px;
          display: flex;
          flex-wrap: nowrap;

          label.promocode_label {
            @include label_styles;
          }

          .promocode_wrapper {
            @include input_base_styles;
            @include option_subborder;
            width: 50%;
            height: 100%;

            input.promocode_input {
              background: transparent;
              width: 100%;
              height: 100%;
              border: 0;
              @include option_styles;
              transform: translateY(13px);

              &:focus {
                outline: none;
              }

              &::placeholder {
                @include option_styles;
              }
            }
          }

          .submit_button {
            z-index: 1;
            color: white;
            width: 50%;
            height: 45px;
            border: none;
            font-size: 45px;
            line-height: 40px;
            letter-spacing: 1px;
            background-color: $widget_bg;
            font-weight: 300;
            text-transform: lowercase;
            font-family: $text_family;
            cursor: pointer;
            border-radius: 0;
            overflow: hidden;
            transform: translateY(52px);
            @include transition(all, .6s);

            &:hover {
              color: $black;
            }
          }
        }
      }
    }
  }

  .close_widget {
    display: none;
  }

  .selectricWrapper {
    .selectricHideSelect, .selectricInput {
      display: none;
    }

    &.selectricOpen {
      .selectricItems {
        display: block;
      }
    }

    .selectricItems {
      position: absolute;
      top: calc(100% + 10px);
      background-color: white;
      display: none;
      border: none;
      z-index: 5;
      @include box_shadow;

      .selectricScroll {
        ul {
          margin: 0;
          list-style: none;
          padding: 0;

          li {
            margin: 0;
            background-color: white;
            text-align: center;
            color: $corporate_1;
            padding: 10px 5px;
            font-weight: 600;
            cursor: pointer;
            @include transition(all, .4s);

            &:hover {
              background-color: rgba($corporate_1, .6);
              color: white;
            }

            &.selected {
              background-color: $corporate_1;
              color: white;
            }
          }
        }
      }
    }
  }
}

.elementor-element-037273a {
  .elementor-widget-container {
    height: 110px;

    #widget_paraty {
      width: 100%;

      .stay_selection {
        width: 35%!important;
      }

      .guest_selector {
        width: 20% !important;
      }

      .room_list_wrapper {
        top: auto !important;
        bottom: calc(100% + 12px) !important;
        right: 550px!important;
      }

      .wrapper_booking_button {
        width: 45%!important;
      }
    }
  }
}

.calendar_popup_wrapper {
  .header_popup {
    .calendar_button_head,
    .graph_button_head {
      background-color: $black;

      &.active {
        background-color: #7b7b7d;
      }
    }

    .popup_helper_wrapper {
      background-color: $corporate_1;
      border: none;
    }
  }

  .total_price_wrapper {
      .nights_number_wrapper {
        background-color: $black!important;
      }

      .booking_button_element {
        background-color: $corporate_1;
      }
  }

  table.calendar {
      td,
      th {
        padding: 0;

        .day-content {
          .price {
            height: auto!important;
            .currencyValue {
              display: inline-block!important;
            }
          }
        }
      }
    }
}

.datepicker_wrapper_element,
.datepicker_wrapper_element_2,
.datepicker_wrapper_element_3 {
  z-index: 1200;
  box-shadow: 1px 1px 15px 3px rgba(0, 0, 0, 0.3);

  .header_datepicker {
    background-color: $corporate_1;
  }

  .ui-datepicker-header {
    background: transparent !important;
  }

  .ui-datepicker .ui-widget-header .ui-datepicker-title {
    color: $corporate_1 !important;
    font-family: $title_family;
    font-weight: 400;
  }

  .ui-datepicker-inline{
    .ui-datepicker-next, .ui-datepicker-prev{
      &::before{
        content: "";
        width: 10px;
        height: 10px;
        background: none;
        position: absolute;
        display: block;
        border-right: 1px solid white;
        border-bottom: 1px solid white;
        top: 50%;
        right: 30%;
        transform: rotate(45deg) translate(-50%, -50%);
      }
    }
  }

}

.gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}

#fancybox-overlay {
  top: 0;
  position: absolute;
  z-index: 1300;
  background-color: rgba(0, 0, 0, .6);
}

.container_popup_booking {
  border: 0 !important;
}

.paraty_form_wrapper {
  z-index: 5;
}

.calendar_popup_wrapper {
  .popup_month_selector .month_select_element {
    display: inline-block;
    vertical-align: top;
  }

  table.calendar td .day-content .price {
    height: 35px;

    .currencyValue {
      display: block;
    }
  }
}