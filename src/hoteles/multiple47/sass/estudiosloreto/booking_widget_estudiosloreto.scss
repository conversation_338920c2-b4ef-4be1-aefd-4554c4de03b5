@import "plugins/iconmoon";
@import "plugins/fontawesome5injectionfix";
@import "plugins/fontawesome5pro_inj";
@import "plugins/mixins";
@import "plugins/fancybox_2_1_5";
@import "plugins/spiners/all_spiners";

@import url('https://fonts.googleapis.com/css?family=Poppins:300,400,600,700&display=swap');



$corporate_1: #414141;
$corporate_2: white;
$widget_bg: $corporate_1;
$grey: #878787;
$separator_color: $corporate_1;
$label_color: white;
$black: #333333;

$title_family: "Poppins", Sans-serif;
$text_family: "Poppins", Sans-serif;

$height: 105px;
$vertical_padding: 20px;
$horizontal_padding: 20px !default;
$width: 200px;
$box_shadow: 1px 1px 15px 3px rgba(0, 0, 0, 0.3);

@mixin box_shadow() {
  -webkit-box-shadow: $box_shadow;
  -moz-box-shadow: $box_shadow;
  box-shadow: $box_shadow;
}

@mixin input_base_styles() {
  position: relative;
  text-align: center;
  //background-color: $widget_bg;
  height: $height;
  //width: $width;
  padding: $vertical_padding $horizontal_padding;
  margin: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

@mixin label_styles() {
  display: block;
  text-align: left;
  font-size: 16px !important;
  color: $label_color !important;
  font-weight: 500;
  font-family: $title_family;
  text-transform: capitalize;
  margin: 0 0 7px;
}


@mixin option_styles() {
  display: inline-block;
  font-size: 15px;
  line-height: 17px;
  color: white !important;
  font-weight: 300;
  font-family: $text_family;
  cursor: pointer;
  margin: 0;
}

@mixin option_styles_big() {
  display: inline-block;
  font-size: 20px;
  line-height: 22px;
  color: white !important;
  font-weight: 700 !important;
  font-family: $text_family;
  cursor: pointer;
  margin: 0;
}

@mixin option_subborder() {
  position: relative;

  &::after {
    position: absolute;
    content: '';
    height: 2px;
    bottom: 10px;
    left: 10px;
    right: 10px;
    background-color: white;
  }
}

@mixin date_icon() {
  position: relative;
  &:before {
    content: '\f073';
    font-family: "Font Awesome 5 Pro";
    font-size: 20px;
    font-weight: 300;
    color: $black;
    position: absolute;
    right: 0px;
    top: auto;
    bottom: 20px;
    transform: translateX(-50%);
    z-index: 1;
  }
}

@mixin promocode_placeholder_styles($size, $color) {
  color: white;
  font-size: $size;
  font-weight: 700;
  text-transform: uppercase;
  font-family: $title_family;
  letter-spacing: 1px;
  text-align: center;
  margin: 0;
  border: 0;
  padding: 0;
  box-shadow: none;
}

@mixin promocode_styles($size, $color) {
  @include promocode_placeholder_styles($size, $color);

  &::-webkit-input-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
  &::-moz-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
  &:-ms-input-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
  &:-moz-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
}

@import "booking_widget";
@import "web";
@import "responsive";


