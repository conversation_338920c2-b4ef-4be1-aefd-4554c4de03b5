# -*- coding: utf-8 -*-
import os

from utils.web.BaseInjectionHandler import InjectionScript


class EstudiosLoretoScript(InjectionScript):

	def params_base_script_controller(self):
		context = {
			"widget_url": "estudiosloretowidget",
			"widget_css": "estudiosloreto",
			"static_version": "1.28",
			"booking_version": "7",
			"calendar_version": "5"
		}

		return context

	def template_controller_name(self):
		return "estudiosloreto_controller.js"

	def template_controller_path(self):
		return os.path.join(os.path.dirname(__file__), self.template_controller_name())