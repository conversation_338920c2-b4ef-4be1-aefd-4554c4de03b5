$ = jQuery;
{{ widget_controller_injection|safe }}

setTimeout(function() {
    if($("#price_calendar_v2").length > 0) {
         window.calendar_data.update_namespace_calendar('b3-virrey');
    }
}, 10000);

bookingWidgetController.add_widget_html = function () {
    const paraty_widget = bookingWidgetController.config.widget_html;

    if (!$("#widget_paraty").length) {
        $("body").append($("<div id='widget_paraty'></div>"));
    }

    $("#widget_paraty").html(paraty_widget);
};

bookingWidgetController.add_button_mobile_version_html = function() {
    if ($("#floating_button_paraty").length) {
        $("#floating_button_paraty").remove();
    }
    let header = $("body").find("[data-elementor-type='header']");
    if (header.length > 0) {
        header.append("<div id='floating_button_paraty'></div>");
        $('#floating_button_paraty').html($.i18n._("reserva_ahora"));
    } else {
        $("body").append($("<div id='floating_button_paraty'></div>").html($.i18n._("reserva_ahora")));
    }

        $("#floating_button_paraty").addClass("auto-position");
        $(window).scroll(function () {
            if ($(this).scrollTop() > 40) {
                $("#floating_button_paraty").addClass("scroll-position");
            } else {
                $("#floating_button_paraty").removeClass("scroll-position");
            }
        });

    $("#widget_paraty #full_wrapper_booking").append($("<i class='fa fa-times close_widget'></i>"));
};


bookingWidgetController.open_widget = function () {
    $("#floating_button_paraty").click(function (e) {
        e.preventDefault();
        $("#floating_button_paraty").addClass("hidden");
        $("#widget_paraty").fadeIn(function() {
            $("#full_wrapper_booking").show();
        });
    });
};

bookingWidgetController.close_widget = function () {
    $("i.fa-times.close_widget").click(function () {
        $("#floating_button_paraty").removeClass("hidden");
        $("#widget_paraty").fadeOut();
    });
};

bookingWidgetController.floating_widget = function(){
    var actual_position = $(window).scrollTop(),
        widget = $("#widget_paraty"),
        window_width = $(window).innerWidth();

    if ($("body").hasClass("home")) {
        if (window_width >= 1280 && actual_position > 40) {
           $('body').addClass('widget_top');
           widget.addClass("top");
        } else {
           $('body').removeClass('widget_top');
           widget.removeClass("top");
        }
    } else {
        if (window_width >= 1280 && actual_position > 40) {
           $('body').addClass('widget_top');
           widget.addClass("top");
        } else {
           $('body').removeClass('widget_top');
           widget.removeClass("top");
        }
    }
};

bookingWidgetController.custom_functions = function () {
    bookingWidgetController.config.languages = {
        "es": "SPANISH",
        "en": "ENGLISH",
    };

    bookingWidgetController.floating_widget();
    set_occupancy_number();
    prepare_guests_selector();

    $(".close_widget").click(function () {
        $('body').removeClass('noscroll');
    });

    $(window).scroll(bookingWidgetController.floating_widget);

    $("#full_wrapper_booking .children_selector select.room_selector").change(function () {
        check_kids_ages($(this));
    });
    if ($("#booking").length) {
        $("#full_wrapper_booking .kidAgesSelect").selectric();
    }

    $(".custompromocode").click(function (e) {
        e.preventDefault();
        $("#widget_paraty input[name='promocode']").val("RESIDENTE");
        if($("#widget_paraty.zocos")){
          $(".custompromocode").addClass("active");
        };
    });

    var add_room_html = "<div class='add_remove_room_wrapper clearfix'><div class='add_room'>" + $.i18n._("T_anadir") + " " + $.i18n._("T_habitacion") + "</div><div class='remove_room'>" + $.i18n._("T_eliminar") + "</div></div>";
    $(".room_list_wrapper").append(add_room_html);
    $(".room_list_wrapper").on("click", ".add_room", function () {
        add_room();
    });
    $(".room_list_wrapper").on("click", ".remove_room", function () {
        remove_room();
    });
};

bookingWidgetController.custom_format_date = function (dateComponents) {
    if (!dateComponents) return '';
    dateComponents = dateComponents.split("/");
    var month_names = $.datepicker._defaults.monthNamesShort;
    var target_month = parseInt(dateComponents[1], 10) - 1;
    var html_date = "<div class='day'>%d</div><div class='month'>%m</div><div class='year'>%y</div>";

    return html_date.replace("%d", dateComponents[0]).replace("%m", month_names[target_month]).replace("%y", dateComponents[2]);
};

bookingWidgetController.datepicker_configuration = function() {
    DP_extend_info.config.booking_version = '5';

    DP_extend_info.config.hotel_path_endpoint = bookingWidgetController.config.base_url;
    bookingWidgetController.separate_entry_departure_datepicker();
    DP_extend_info.config.custom_format_day_month = bookingWidgetController.custom_format_date;
    DP_extend_info.config.custom_sd_selection = function() {$(".datepicker_wrapper_element").scrollTop(0);}
};

function check_kids_ages(select_element){
   var parent_list = select_element.closest("li"),
       selected_value = select_element.val(),
       target_age_selector = parent_list.next(".full_ages_wrapper"),
       childs_elements = target_age_selector.find(".kid_age_element_wrapper"),
       target_childs_elements = childs_elements.slice(0, parseInt(selected_value));

   if (parseInt(selected_value)) {
      childs_elements.css('display', 'none');
      target_childs_elements.css('display', 'block');
      target_age_selector.slideDown(function () {
           $(this).css("overflow","inherit");
       });
   } else {
      childs_elements.css('display', 'none');
      target_age_selector.slideUp(function () {
           $(this).css("overflow","inherit");
       });
   }
}

function paraty_perform_search() {
    $(".paraty-booking-form .submit_button").trigger('click');
}

function prepare_guests_selector() {
   $("select.room_selector").unbind("change");
    $(".room_selector").selectric('destroy');
    $(".room_selector").selectric({disableOnMobile: false});
   $("select.room_selector, select.rooms_number").change(function(){
      set_occupancy_number();
   });

}

function toggle_guest_selector(){
   var target_room_wrapper = $(".room_list_wrapper");
   if(!target_room_wrapper.hasClass('active')) {
      target_room_wrapper.addClass('active');
      target_room_wrapper.show();
   } else {
      target_room_wrapper.removeClass('active');
      target_room_wrapper.hide();
   }
   set_occupancy_number();
}

function set_occupancy_number(){
   var number_of_rooms = parseInt($("select[name='numRooms']").val()),
       adults_number = 0,
       kids_number = 0,
       babies_number = 0;

   if (number_of_rooms){
      for (var room_loop = 1;room_loop <= number_of_rooms;room_loop++){
         var actual_select_adults = $("select[name='adultsRoom" + room_loop + "']").val(),
             actual_select_kids = $("select[name='childrenRoom" + room_loop + "']").val(),
             actual_select_baby = $("select[name='babiesRoom" + room_loop + "']").val();

         if(actual_select_adults || actual_select_kids){
            adults_number += parseInt(actual_select_adults);
            kids_number += parseInt(actual_select_kids);
            babies_number += parseInt(actual_select_baby);
         }
      }
   }

   var target_placeholder = $(".guest_selector .placeholder_text");

   adults_number = parseInt(adults_number);
   kids_number = parseInt(kids_number);
   babies_number = parseInt(babies_number);

   placeholder_string = "<span class='guest_adults'> " + adults_number  + " " + $.i18n._('T_adultos') + "</span>";

   if(!$(".adults_only_selector").length){
      placeholder_string += " <span class='separator_plus'>/</span> " + kids_number + " " + $.i18n._('T_ninos');
   }

   if($(".babies_selector").length) {
      placeholder_string += " <span class='separator_plus'>/</span> " + babies_number + " " + $.i18n._('T_bebes');
   }

   target_placeholder.html(placeholder_string);
}

function add_room() {
    var number_rooms = parseInt($("select.rooms_number").val());
    console.log(number_rooms);
    if (number_rooms < 3) {
        $($(".rooms_number_wrapper .selectricItems li").get(number_rooms)).trigger("click");
        bookingWidgetController.set_occupancy_number();
    }
}

function remove_room() {
    var number_rooms = parseInt($("select.rooms_number").val());
    if (number_rooms > 1) {
        $($(".rooms_number_wrapper .selectricItems li").get(number_rooms - 2)).trigger("click");
        bookingWidgetController.set_occupancy_number();
    }
}

$(function() {
    bookingWidgetController.init();
});