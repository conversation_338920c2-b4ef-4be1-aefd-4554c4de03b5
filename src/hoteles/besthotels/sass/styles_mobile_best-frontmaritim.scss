$color1: #665d58;
$color2: #fbbc1e;

@import "_styles_mobile_base_2";
@import "_styles_mobile_base_2_personalized";

@mixin center-image {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  max-width: initial;
}

nav#menu {
  background: $color1;
}

[data-role="button"] {
  background-color: $color2;
}

header {
  background-color: $color1;
  #simple-menu {
    &:after {
      color: white;
    }
  }
  .contact .telefono:after {
    color: white;
  }
  &.with_extra_header {
    top: 40px !important;
    .extra_top_header {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      font-size: 10px;
      width: 100%;
      background: #F3D132;
      color: #4b4b4b;
      text-align: center;
      i {
        display: inline-block;
        margin-right: 5px;
      }
      p {
        display: inline-block;
      }
      a {
        position: relative;
        color: #4b4b4b;
        text-decoration: none;
      }
    }
  }
}
nav#menu.with_extra_header {
  top: 11em;
}

.default_text_content, .normal_section_mobile, .my_reservation_section, .contact_section_wrapper {
  .section_title, .section-title {
    font-size: 47px !important;
    font-weight: 300 !important;
    color: $color1 !important;
    margin-bottom: 25px !important;
    line-height: 1 !important;
    margin-top: 0px !important;
    font-family: 'Source Sans Pro', sans-serif !important;
  }

  .section_description, .section-content, .default_reservation_text, .contact_content_element {
    font-weight: 300;
    font-size: 13px;
    position: relative;
    line-height: 20px;
    color: #646464;
    text-align: justify;
    margin-bottom: 20px;

    strong {
      font-weight: bold;
    }
  }
}

.normal_section_mobile .section-content {
  a {
    &:first-child{
      margin-top: 20px;
    }
    box-sizing: border-box;
  }
}

.default_text_content {
  margin-top: 20px;
  padding: 15px;
}

.logo img {
  width: 26em;
  margin-top: 1em;
  height: auto;
}

.section-content {
  iframe {
    width: 100%;
  }
}

form.paraty-booking-form #room_1 div {
  border: 2px solid #bfbfbf;
}

form.paraty-booking-form .selectricWrapper.selectricOpen .selectric-items, form.paraty-booking-form .selectric-wrapper.selectric-hover .selectric-items {
  overflow: auto;
}

form.paraty-booking-form .selectricItems ul li.selected, form.paraty-booking-form .selectric-items ul li.selected {
  background: #315390;
}

form.paraty-booking-form input[type="submit"] {
  background: #F3D132;
}

/*====== Destiny section =====*/
.destiny_section_wrapper {
  display: table;
  padding-bottom: 20px;
  margin-bottom: 40px;
  border-bottom: 1px solid lightgray;
}

.destiny_element_block {
  width: 100%;
  float: left;
  margin-right: 0;
  margin-bottom: 20px;
  overflow: hidden;

  .exceded {
    position: relative;
    height: 270px;
    overflow: hidden;

    .destiny_image {
      @include center-image();
    }

    h2.destiny_title {
      font-size: 35px;
      font-weight: 300;
      color: white;
      width: 100%;
      text-align: center;
      z-index: 3;
      position: absolute;
      top: 50%;
      -webkit-transform: translateY(-50%);
      -moz-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      -o-transform: translateY(-50%);
      transform: translateY(-50%);
      padding: 0 22px;
      box-sizing: border-box;
    }
  }

  .since_price {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    text-align: center;
    color: white;
    border: 1px solid white;
    font-size: 23px;
    padding: 6px 0 12px;
    z-index: 3;

    small {
      text-transform: uppercase;
      font-size: 13px;
      vertical-align: bottom;
      line-height: 19px;
      margin-right: 0;
    }
  }

  .destiny_description {
    padding: 20px;

    .text_wrapper {
      color: white;
      font-weight: 300;
      font-size: 13px;
      position: relative;
      height: auto;
      overflow: hidden;
      line-height: 20px;
      background: inherit;
    }
  }

  .buttons_wrapper {
    display: table;
    width: 100%;
    margin-top: 12px;

    .see_more_button {
      display: table;
      float: left;
      width: 45px;
      text-align: center;
      font-size: 26px;
      line-height: 45px;
      border: 1px solid white;
      color: white;
      text-decoration: none;
    }
  }

  .see_hotels_button {
    display: inline-block;
    border: 0;
    font-size: 18px;
    float: right;
    line-height: 47px;
    background: white;
    text-transform: uppercase;
    padding: 0 31px;
    text-decoration: none;
    cursor: pointer;
    width: 50%;
    text-align: center;

    &.full_width_button {
      width: 100%;
      box-sizing: border-box;
      text-align: center;
    }
  }

  .black_overlay {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    opacity: 0.4;
    z-index: 2;
  }
}

.individual_destiny_wrapper {
  width: 100%;
  display: table;
  margin-top: 40px;
  margin-bottom: 35px;
  padding-bottom: 35px;
  border-bottom: 1px solid lightgray;

  .individual_destiny_content {
    width: 100%;
    float: left;

    .destiny_title {
      font-size: 47px;
      font-weight: 300;
      color: $color1;
      margin-bottom: 40px;
    }

    .destiny_description {
      font-weight: 300;
      font-size: 13px;
      position: relative;
      line-height: 20px;
      color: #646464;
      text-align: justify;
      margin-bottom: 20px;

    }
  }

  .destiny_section {
    color: white;
    font-size: 20px;
    text-transform: uppercase;
    width: 100%;
    text-align: center;
    height: 55px;
    display: block;
    text-decoration: none;
    line-height: 56px;
    margin-bottom: 10px;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -ms-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s;

    &:hover {
      opacity: 0.7;
    }
  }

  .right_wrapper_buttons {
    float: right;
    width: 100%;

    .see_pictures_button {
      margin-bottom: 10px;
    }

    .see_pictures_button, .see_video_button {
      width: 100%;
      height: 55px;
      text-align: center;
      border: 1px solid $color1;
      color: $color1;
      text-transform: uppercase;
      font-size: 23px;
      line-height: 54px;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      cursor: pointer;
      -webkit-transition: all 0.5s;
      -moz-transition: all 0.5s;
      -ms-transition: all 0.5s;
      -o-transition: all 0.5s;
      transition: all 0.5s;

      &:hover {
        opacity: 0.7;
      }

      small {
        font-size: 14px;
      }

      .image_wrapper {
        width: 55px;
        height: 55px;
        position: relative;
        float: left;
        background: $color1;

        .pictures_image, .video_image {
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          margin: auto;
          -webkit-transform: translateY(-50%);
          -moz-transform: translateY(-50%);
          -ms-transform: translateY(-50%);
          -o-transform: translateY(-50%);
          transform: translateY(-50%);
        }
      }
    }

    a {
      text-decoration: none;
      color: $color1;
    }
  }
}

#hidden_video_wrapper iframe {
  display: block;
}

.info_blocks_wrapper {
  display: table;
  width: 100%;
  margin-bottom: 35px;
  padding-bottom: 35px;
  border-bottom: 1px solid lightgray;

  .info_block_element {
    width: 100%;
    //height: 450px;
    display: inline-block;
    margin-right: 0;
    margin-bottom: 20px;

    &:nth-child(4n+4) {
      margin-right: 0;
    }

    .exceded {
      height: 270px;
      position: relative;
      width: 100%;
      display: inline-block;
      overflow: hidden;
      float: left;

      .black_overlay {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        opacity: 0.3;
      }

      .info_image {
        @include center-image();
      }

      h3.info_title {
        position: absolute;
        top: 50%;
        width: 100%;
        text-align: center;
        color: white;
        line-height: 1;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        padding: 0 32px;
        box-sizing: border-box;
        z-index: 2;
        font-size: 34px;
      }
    }

    .info_content {
      width: 100%;
      float: right;
      box-sizing: border-box;
      height: auto;
      position: relative;
      padding: 15px 30px 90px;
      background: $color1!important;
    }

    .info_description {
      color: white;
      font-weight: 300;
      line-height: 20px;
      font-size: 13px;
      height: 60px;
      position: relative;
      overflow: hidden;
      background: inherit;
    }

    .see_more_info_block, .see_more_pictures_info_block, .video_info_blocks {
      display: inline-block;
      width: 45px;
      height: 45px;
      text-align: center;
      border: 1px solid white;
      vertical-align: top;
      box-sizing: border-box;
      position: relative;

      img {
        position: absolute;
        top: 50%;
        left: 50%;
        -webkit-transform: translateY(-50%) translateX(-50%);
        -moz-transform: translateY(-50%) translateX(-50%);
        -ms-transform: translateY(-50%) translateX(-50%);
        -o-transform: translateY(-50%) translateX(-50%);
        transform: translateY(-50%) translateX(-50%);
      }
    }

    .see_more_info_block {
      font-size: 25px;
      color: white;
      padding: 16px 0;
    }
  }

  .info_buttons_wrapper {
    text-align: center;
    margin-top: 13px;
    position: absolute;
    right: 30px;
    bottom: 30px;

    a {
      text-decoration: none;
    }
  }
}

.hidden_video_block {
  iframe {
    display: block;
  }
}

.see_more_info_block_hidden {
  padding: 20px;

  h3.info_title {
    font-size: 47px;
    font-weight: 300;
    color: $color1;
    margin-bottom: 15px;
    line-height: 1;
  }

  .info_description {
    font-weight: 300;
    font-size: 13px;
    position: relative;
    line-height: 20px;
    color: #646464;
  }
}

.info_blocks_wrapper.info_blocks_inline {
  .info_block_element {
    width: 100%;
    height: 450px;
    display: inline-block;
    margin-bottom: 20px;

    &:nth-of-type(4n) {
      margin-right: 0;
    }

    .exceded {
      height: 270px;
      position: relative;
      overflow: hidden;
    }

    .info_content {
      width: auto;
      height: auto;

      .info_description {
        height: 100px;
      }
    }

    .info_buttons_wrapper {
      display: block;
      position: relative;
      right: auto;
      bottom: auto;
    }
  }

  .info_block_element {
    overflow: hidden;
  }
}

@import "best_corporate/club_styles";
@import "best_corporate/hotel_group";
@import "best_corporate/contact_section";

/*=== My booking ====*/
.my_reservation_section {
  margin-bottom: 45px;
}

/*==== Ico Blocks ====*/
.ico_block_element {
  text-align: center;
  margin-bottom: 30px;
  width: 49%;
  display: inline-block;
  vertical-align: top;

  .ico_description {
    font-size: 15px;
    line-height: 1;
    margin-top: 10px;
    color: #646464;
    font-weight: 300;
  }
}


/*==== Contact ====*/
.contact_section_content .contact_title_section{
  color: $color1!important;
}

#contact-button-wrapper #contact-button{
  background: $color2!important;
}


/*==== User Club ====*/
.form_suscribe_wrapper button{
  background-color: $color2;
}

.work_suscribe_form button{
  background: $color2;
}

.iframe_map_wrapper iframe {
  width: 100%;
}

.gallery_1 {
  width: 100%;
  height: 250px;
  position: relative;
  .crop {
    width: 100%;
    height: 250px;
    position: relative;
  }
  &.owl-carousel {
    .owl-nav {
      font-size: 25px;
      color: $color1;
      .owl-prev {
        position: absolute;
        left: 10px;
        @include center_y;
      }
      .owl-next {
        position: absolute;
        right: 10px;
        @include center_y;
      }
    }
    .owl-item img {
      @include center_image;
    }
  }
}

.banner_bottom_title {
  font-size: 3.6em !important;
  bottom: auto !important;
  top: 50% !important;
  transform: translateY(-50%);
}

.banner_bottom_description {
display: none;
}

.owl-item .description_text {
  top: 50% !important;
  @include center_y;
  bottom: auto !important;
}
body .booking_general_button.with_extra_header {
  margin-top: 12em;
}
body #popup-not-cancellation.active {
  text-align: center;
  display: block !important;
  min-width: 100%;
  position: fixed;
  min-height: 100%;
  top: 0;
  background: white;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;
  .close_popup_btn {
    position: absolute;
    top: 120px;
    right: 40px;
    cursor: pointer;
    i {
      color: $color2;
      font-size: 30px;
    }
  }
  .cancelation_wrapper_popup {
    @include center_xy;
    line-height: 1;
    .cancelation_title {
      color: $color1;
      font-size: 26px;
      font-weight: 700;
      font-style: italic;
    }
    i {
      font-size: 30px;
      margin: 5px 5px 20px;
      color: $color2;
    }
    .cancelation_text {
      font-size: 14px;
      width: 300px;
      display: block;
      margin: auto;
      font-weight: 300;
    }
    .cancelation_phone {
      display: block;
      font-size: 16px;
      color: $color2;
      text-decoration: none;
    }
  }
  .close_popup_btn {
    &:before {
      font-family: "FontAwesome", sans-serif;
    }
  }
}

.covid_overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0,0,0,0.4);
  z-index: 1002;
}
.covid_container {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
  margin: auto;
  z-index: 1003;

  .close_covid_container {
    position: absolute;
    right: 0;
    bottom: 100%;
    width: 30px;
    height: 30px;
    background: rgba($color1, 0.8);
    cursor: pointer;
    i {
      color: white;
      font-size: 16px;
      @include center_xy;
    }
  }
  a {
    img {
      min-width: 150px;
      max-width: 250px;
    }
  }
}

.covid_button_container {
  position: fixed;
  right: 20px;
  bottom: 100px;
  width: 60px;
  height: 60px;
  background: rgb(19, 69, 157);
  border-radius: 50%;
  z-index: 1000;
  opacity: 0;
  @include transition(opacity,.6s);
  &.showed {
    opacity: 1;
  }

  a {
    .icon_container {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      border: 2px solid white;
      width: 40px;
      height: 40px;
      border-radius: 50%;

      i {
        font-family: "fontawesome", sans-serif;
        color: white;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
        font-size: 25px;
      }
    }
  }
}