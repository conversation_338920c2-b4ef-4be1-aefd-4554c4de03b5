div#step-1 {
  .contTipoHabitacion {
    box-shadow: 0 0 20px rgba(0, 0, 0, .2);
    margin-bottom: 50px;

    &.radio_selected {
      .contFotoHabitacion {
        margin-left: -10px !important;
        margin-top: 0px !important;
      }
    }

    .overlay_picture_text {
      position: absolute;
      z-index: 2;
      text-align: center;
      top: 30px;
      left: 52px;
      font-size: 9px;
      font-weight: bold;
      width: 99px;

      strong {
        display: block;
        font-size: 27px;
        margin-bottom: 15px;
      }

      .s_tag {
        color: #E75354;
      }

      .m_tag {
        color: green;
      }

      .l_tag {
        color: #7CCFF4;
      }

      .xl_tag {
        color: black;
      }
    }

    .contDescHabitacion {
      .cabeceraNombreHabitacion {
        padding: 0;
        margin: 0 0 10px;
      }

      .descripcionHabitacion {
        padding: 0;
        margin-left: 2px;
        color: $black_2;
        letter-spacing: 1px;
        font-weight: 300;
      }

      .room_services i {
        font-size: 20px;
        float: left;
        margin-top: 2px;
      }

      .see_more_rooms_v2 {
        margin-left: 0;
      }
    }

    .contFotoHabitacion {
      min-height: 220px !important;
      width: 320px;
      margin-left: -26px !important;
      margin-top: -20px !important;

      .occupancy {
        display: table;
        width: 100%;
        background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.4));
        position: absolute;
        bottom: 0;
        right: 0;
        z-index: 100;
        padding: 5px;
        text-align: right;
        font-family: $text_family;
        font-weight: 700;
        font-size: 12px;
        line-height: 12px;
        color: white;

        i {
          display: inline-block;
          vertical-align: bottom;
          padding: 0;
          color: white;
          font-size: 12px;
          line-height: 12px;

          &.adult {
            font-size: 22px;
            line-height: 22px;
            padding-right: 5px;
          }

          &.kid {
            font-size: 18px;
            line-height: 18px;
            padding-right: 5px;
          }

          &.baby {
            font-size: 14px;
            line-height: 14px;
            margin-bottom: 0;
            padding-left: 2px;
          }
        }

        b {
          position: absolute;
          bottom: 15px;
          right: 0;
          opacity: 0;
          display: inline-block;
          padding: 10px;
          font-weight: 400;
          text-transform: lowercase;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          border-radius: 5px;
          @include transition(all, .6s);

          &:after {
            position: absolute;
            top: 100%;
            right: 30px;
            content: '';
            border: 5px solid transparent;
            border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
          }
        }

        &:hover {
          b {
            bottom: 30px;
            opacity: 1;
          }
        }
      }
    }

    .tipoHabitacion {
      color: $black_2 !important;
      font-size: 21px !important;
      letter-spacing: 2px;
      font-weight: 500 !important;
      font-family: $title_family;
    }

    .see_more_rooms_v2 {
      .plus_sign {
        color: #333 !important;
        font-weight: 500;
      }

      .see_more {
        text-decoration: none !important;
        text-transform: uppercase !important;
        color: #333 !important;
        font-weight: 500;
      }

      .lupa {
        top: 10px;
        left: 10px;
        font-size: 30px;
        font-weight: 300;
        font-family: "Font Awesome 5 Pro", sans-serif;
      }
    }

    .very_asked_message {
      background-color: $corporate_4 !important;
      color: $corporate_1;
      letter-spacing: 1px;
      text-transform: uppercase;

      &:after {
        border-left-color: $corporate_4;
      }
    }

    .room_services {
      border-top-width: 2px;
      border-top-style: dashed;
      border-bottom-width: 2px;
      border-color: rgba($grey_2, .5);

      .service_element {
        border-right-width: 2px;
        margin: 3px 0 3px 0;
        border-color: rgba($grey_2, .5);
        text-align: left;
        padding-left: 10px;

        &:lang(en) {

        }

        .service_description {
          color: $black_2;
          letter-spacing: 1px;
          padding-left: 10px;
          font-size: 12px;
          max-width: 125px;
          line-height: 13px;
          font-weight: 300;
        }
      }
    }

    .preciosHabitacion {
      width: 1140px;
      margin-left: -20px;

      .listadoHabsTarifas {
        border-spacing: 0;

        .regimen_tr_element {
          &.with_highlight_column {
            .regimenColumn {
              width: 30%;
            }

            .precioNocheColumn  {
              padding-right: 33px;
            }

            .precioTotalColumn {
              padding-right: 20px;
            }

            .marketing_logo_info_wrapper {
              position: absolute;
              right: -30px;
              width: 130px;
              top: 115%;
              color: beige;
              font-size: 13px;
              max-height: 0;
              padding: 0 20px;
              transition: all .5s;
              overflow: hidden;
              z-index: 5;

              &.active, &.hover {
                padding: 30px 20px 20px;
                max-height: 600px;
              }

              &:before {
                right: 75px;
              }
            }
          }
        }

        .radio_rate_element {
          .rate_conditions_link {
            color: $black_2;

            &:before {
              color: $black_2;
              border-color: $black_2;
            }
          }
        }

        tr:not(:first-of-type) {
          padding: 8px 0;
          width: 100%;
          margin: auto;
          display: table;

          &:last-of-type {
            padding: 8px 0 0;
          }

          &:not(.rate_tr_element) {
            width: calc(100% - 20px);
            margin: 0 auto 0 20px;
          }
        }

        td.regimenColumn .regimenColumnContent {
          padding: 0 15px 0 0;
          margin-left: -5px;

          .regimen_name_wrapper {
            color: $black_2;
            letter-spacing: 1px;
            font-size: 13px;
            font-weight: 300;
          }

          .tTextoOferta {
            color: $corporate_1;
            font-weight: 500;
          }
        }
      }
    }

    .precioTotalColumn, .precioNocheColumn {
      text-align: right;

      .precioTachadoDiv {
        color: $corporate_1;

        .tPrecioTachado {
          color: $corporate_1;
        }
      }

      .precioGeneralDiv {
        .precioGeneral {
          font-weight: bold;
        }

        .tPrecioOferta {
          color: $black;
        }
      }

      .promotion_percentage_square {
        border-radius: 50%;
        background-color: $corporate_1 !important;
        font-weight: bold;
        text-align: center;
      }

      .priceTitle {
        font-weight: lighter;
      }
    }

    .booking-button {
      font-family: $text_family;
      background-color: $corporate_1;
      color: white;
      padding: 13px 50px 13px 30px;
      font-size: 16px;
      letter-spacing: 2px;
      font-weight: 700;
      position: relative;
      -webkit-transition: all 0.6s;
      -moz-transition: all 0.6s;
      -ms-transition: all 0.6s;
      -o-transition: all 0.6s;
      transition: all 0.6s;
      display: inline-block;
      vertical-align: middle;
      text-transform: uppercase;
      z-index: 1;

      &:hover {
        background-color: $corporate_2;
      }
      &:lang(fr) {
        width: 180px;
        font-size: 12px;
      }
      &:after {
        content: '';
        background: url(/static_1/images/flecha.png);
        right: 15px;
        position: absolute;
        top: 12px;
        height: 25px;
        width: 15px;
        background-repeat: no-repeat;
      }

      &:lang(en) {
        padding: 13px 30px;
      }

    }

    .last_day_cancellation_text {
      background: transparent;
      position: relative;
      color: $black;
      letter-spacing: 0.4px;
      font-family: $font_1;
      @extend .fa-check;

      &:before {
        @extend .fa;
        position: absolute;
        top: 50%;
        left: 3px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        color: $corporate_1;
        border: 1px solid $corporate_1;
        padding: 0 3px;
        font-size: 8px;
        line-height: 14px;
        border-radius: 50%;
      }
    }

    .cheapest_rate_message {
      .conditions_info_wrapper {
        display: inline-block;
        vertical-align: middle;
        width: auto;
        position: unset;
      }

      .rate_conditions_link {
        color: $white;
        font-weight: lighter;
        padding: 0 10px;
        text-decoration: underline;
        position: relative;
        font-size: 11px;
        letter-spacing: 1px;
        text-transform: none;

        &:after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 0;
          right: 0;
          margin: auto;
          height: 1px;
          width: 80%;
          background-color: $black;
        }

        &:before {
          display: none;
        }
      }

      &.has_conditions {
        padding: 10px 30px 10px 40px !important;
      }
    }

    .rate_conditions_link {
      background: transparent;
      position: relative;
      @extend .fa-info;

      &:before {
        @extend .fa;
        width: 3px;
        height: 14px;
        position: absolute;
        top: 50%;
        right: 3px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        border: 1px solid white;
        background: transparent;
        color: white;
        padding: 0 5px;
        font-size: 10px;
        line-height: 14px;
        border-radius: 50%;
      }
    }

    .contTitTipoTarifa {
      background-color: #f5f5f5;
      color: $black;
      font-family: $text_family;

      .titTipoTarifa {
        font-weight: normal;
        letter-spacing: 1px;
        font-size: 13px;
        color: $black_2;
        font-weight: 300;
      }

      .cheapest_rate_message {
        background-color: $black;
        color: white;
        letter-spacing: 1px;
        overflow: initial;
        font-weight: 100;

        &:before {
          border-left: 19px solid #f5f5f5
        }

        .before_block {
          border-left-color: $corporate_1;
        }
      }

      .advice_rate_message {
        background-color: $corporate_1;
        color: white;

        &:before {
          border-left-color: $grey;
        }
      }

      > .conditions_info_wrapper {
        a {
          color: $black_2;
          font-weight: lighter;
          padding: 0 10px;
          text-decoration: none;
          position: relative;
          font-size: 10px;
          letter-spacing: 0;
          text-transform: none;

          &:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 10px;
            right: 10px;
            margin: auto;
            height: 1px;
            background-color: $black_2;
          }

          &:before {
            display: none;
          }
        }
      }
    }

    .sectoption {
      border: none;
      padding: 5px 0;

      .titSelOpt {
        color: $black;
        font-size: 18px;
        font-family: $title_family;
      }

      .listaradio {
        li {
          input {
            display: inline-block;
            vertical-align: middle;
            margin: 0 5px 0;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: white;
            border: 1px solid $black;
            cursor: pointer;

            &:checked {
              background-color: $corporate_1;
            }

            &:focus {
              outline: 0;
            }
          }
        }
      }
    }
  }

  .room_step_selection .room_step_selection_title {
    background: $corporate_1;
  }

  table.listadoHabsTarifas.country_location_param_actived td {
    padding: 6px 18px !important;
  }

  .conditions_info_wrapper {
    width: 98%;
    margin: auto;

  }
}

div#step-1 .conditions_info_wrapper .rate_conditions_link {
  color: $corporate_2;
  font-size: 9px;
  text-transform: uppercase;
  box-sizing: border-box;
  letter-spacing: 1px;
  font-weight: bolder;
  padding: 0 30px 0 0;

  &:before {
    border-color: $corporate_1;
    color: $corporate_1;
  }
}

.popup_see_more_rooms_second {
  .fancybox-inner {
    background-color: white;
  }

  .room_popup_individual_element {
    .popup_title {
      background: white;
      font-family: $title_family;
      font-size: 25px;
      font-weight: 500;
      color: #333;
      top: auto;
      bottom: calc(100% - 460px);
      padding-right: 35%;
      padding-bottom: 10px;
      border-top: 10px solid white;
    }

    .close_button_element {
      font-family: $text_family;
      font-size: 18px;
      font-weight: 700;
      color: white;
      background-color: $corporate_3;
      width: 50px;
      line-height: 50px;
      top: 0;
      right: 0;
    }

    .popup_carousel {
      .element_carousel_pictures {
        .exceded {
          height: 400px;
          margin-bottom: 70px;

          img {
            width: 100%;
          }

          a {
            display: block !important;
            position: absolute;
            top: 270px;
            right: 0;
            background: radial-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
            padding: 10px;
            color: white;

            i.fa {
              font-size: 25px;
            }

            &:hover {
              color: $corporate_1;
            }
          }
        }
      }

      .flex-direction-nav {
        .flex-nav-prev, .flex-nav-next {
          width: 50px;
          height: 50px;
          @include transition(width, .6s);

          &:hover {
            width: 60px;

            a {
              &:before {
                margin-left: 12px;
              }
            }
          }

          a {
            background-color: $corporate_3;
            @extend .icon-longarrow;

            &:before {
              font-family: "icomoon", sans-serif;
              font-size: 30px;
              color: white;
              position: absolute;
              top: 50%;
              left: 50%;
              -webkit-transform: translate(-50%, -50%);
              -moz-transform: translate(-50%, -50%);
              -ms-transform: translate(-50%, -50%);
              -o-transform: translate(-50%, -50%);
              transform: translate(-50%, -50%);
              @include transition(margin, .6s);
            }

            img {
              display: none;
            }
          }
        }

        .flex-nav-prev {
          a:before {
            -webkit-transform: translate(-50%, -50%) rotate(-180deg);
            -moz-transform: translate(-50%, -50%) rotate(-180deg);
            -ms-transform: translate(-50%, -50%) rotate(-180deg);
            -o-transform: translate(-50%, -50%) rotate(-180deg);
            transform: translate(-50%, -50%) rotate(-180deg);
          }

          &:hover {
            a {
              &:before {
                margin-left: -12px;
              }
            }
          }
        }
      }
    }

    .popup_room_pictures {
      background-color: white;
      top: 440px;
    }

    .room_services {
      padding: 0 !important;
      border-color: $grey;
      border-top: none;
      border-bottom: 2px solid $corporate_3;
      background: transparent;
      display: flex !important;

      .service_element {
        border-right: none;
        height: auto !important;
        width: 24% !important;
        margin-right: 5px !important;

        i {
          font-size: 25px;
          display: block;
          padding-bottom: 0;
          margin-right: 0;
        }

        .service_description {
          font-size: 12px;
          font-family: $text_family;
          font-weight: 300;
          max-width: calc(100% - 35px);
          color: $black_2;
        }

        &:last-of-type {
          border-right: none;
        }
      }
    }

    .popup_room_description {
      margin-top: 10px;
      margin-bottom: 30px;
      padding-top: 20px;
      padding-bottom: 20px;
      margin-left: 110px;
      font-family: $text_family;
      padding: 0 60px;
      background: white;
      color: $black_2;
      font-weight: 300;

      .desc, .list {
        display: inline-block;
        vertical-align: top;
        width: calc(100% / 3);
        box-sizing: border-box;
      }

      .desc {
        width: calc(100% / 3 * 2);
        padding-right: 20px;

        strong {
          font-weight: 700;
        }
      }

      .list {
        padding: 0 0 0 25px;

        li {
          font-weight: 700;
          padding: 5px 0;
          @extend .icon-longarrow;

          &:before {
            font-family: "icomoon", sans-serif;
            margin-right: 10px;
            color: $corporate_2;
          }
        }
      }
    }
  }
}