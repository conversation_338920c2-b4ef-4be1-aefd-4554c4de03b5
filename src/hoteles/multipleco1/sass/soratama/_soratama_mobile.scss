  header .logo_wrapper img {
   max-width:190px;
  }

  .continue_booking,
  .back_booking {
    background-color: $corporate_1;
  }

  .continue_booking,
  .back_booking,
  .btn.add_service {
    border-radius: 0 !important;
  }

  .main_content_wrapper.wizard {
    .booking_steps_wrapper {
      .booking_step_sentence {
        font-weight: 300;
        letter-spacing: 1px;
        color: $black;
      }
    }

    .search-resume-wrapper {
      .dates,
      .search_text {
        font-size: 14px;
        letter-spacing: 1px;
        line-height: 18px;
        color: $black;
      }
    }
  }

  .double_button_wrapper {

    .modify_search,
    .show_calendar,
    .back_button {
      border-color: $corporate_3;
      color: $corporate_3;
      background: transparent;

      &::before {
        color: $corporate_3;
      }
    }

    button.submit_button,
    button.close_button {
      //margin: 5px;
      border-color: $black;
      color: white;
      font-weight: 700;
      font-size: 11px;
      letter-spacing: 0.75px;
    }
  }

  .main_content_wrapper {
    &.step_0 {
      .tabs_wrapper {
        .tabs {
          li {
            border-radius: 0;

            .tab_btn {
              background-color: $corporate_1;
              border-radius: 0;
              font-size: 12px;

              &.active {
                background-color: white;
                color: $black;
              }
            }

            .tab_btn.packages {
              .tab_text {
                font-weight: 700;
                text-transform: uppercase;
              }
            }
          }

          li.active {

          }

          li + li {
            &::before {

            }
          }

          &:not(.triple) {
            li {
              width: 45%;
            }
          }
        }
      }

      .room_list {
        .room_pack_option {
          .room_name.content_title {
            .title {
              font-family: $title_family;
              font-weight: 700;
            }

            .info_btn {
              color: $black;

              &::before {
                border-color: $black;
              }
            }
          }

          .rates_details_wrapper {
            .rate_selected_title {
              span {
                font-size: 11px;
                color: $black;
              }
            }

            .regime_item_content {
              .discount_percentage {
                background-color: $red!important;
              }

              .regime_description {
                .regime_offer_detail {
                  color: $black;
                }
              }

              .prices_options {
                .final_price {
                  color: $black;

                  .tax_inc_info {
                    color: $corporate_3!important;
                  }
                }
              }

              .prices_options {
                .price_through {
                  color: $red;
                }
              }

              .regime_price_wrapper {
                div.submit {
                  span {
                    border-radius: 0;
                  }
                }
              }
            }
          }
        }
      }
    }

    &.step_1 {
      .additional_services {
        .perform_additional_services_booking {
          background: $corporate_1 !important;
        }

        .additional_services_wrapper {
          .additional_service_element {
            .price_tag {
              background: $corporate_1;
            }

            .supplement_complete_content {
              .service_selection_wrapper {
                .service_select {
                  .amount_selection {
                    .button-selection {
                      background: $corporate_1;
                    }
                  }
                }

                .add_service_button {
                  background: $corporate_1;
                }
              }
            }
          }
        }
      }
    }
  }

  .shopping_cart_summary.v2.mobile {
    font-family: $text_family;

    .full_body_wrapper .scrollable_content_wrapper .items_wrapper .item:not(:last-child) {
      border-color: $corporate_2;
    }

    .start_wrapper .book_button {
      background-color: $corporate_1;
    }
  }

  &.shopping_cart_process_v2 .booking_details_wrapper {
    .book_type_wrapper {
      background: $corporate_1;
    }

    .rooms_breakdown_wrapper .room_selected_element .room_details_wrapper .rate_name {
      color: $corporate_1;
    }
  }

  .hotels_availability_wrapper .choice_element .hotels_carousel_wrapper .hotels_list .hotel_element .product_details .flexible_days {
    color: $corporate_2;
  }

  .share_links_wrapper {
    .share_links_prev {
      background-color: $corporate_2;
    }

    .share_links_cont .share_link {
      background-color: $corporate_2;
    }
  }

  #main_modal {
    &.active {
      .body_modal {
        &.rooms_features_modal_wrapper {
          .body_modal_content {
            .modal_container {
              &.room_content {
                height: 43%;

                .content_title {
                  .title {
                    font-size: 20px;
                  }
                }

                .icons_room {
                  .room_services {
                    .service_element {
                      i {
                        font-weight: 400;
                      }
                    }
                  }
                }

                .room_description {
                  font-size: 15px !important;
                  font-weight: 400 !important;
                }
              }
            }
          }
        }
      }
    }
  }

  .booking_widget_wrapper {
    .occupancy_popup {
      top: 0;
    }
  }

  .flexible_dates_wrapper.active {
    bottom: 0;
  }

  #login_wrapper_element {
    background: white;
    border: 1px solid #31314c;

    .content_login_wrapper {
      .club_icons_wrapper {
        .club_icon_element.with_desc {
          width: 130px;
          left: 60%;
          top: -15px;
          position: absolute;
          .icon_description_wrapper {
              color: #40371E;
              display: block !important;
          }
        }

        .club_icon_element {
          &:not(.with_desc) {
            position: absolute;
            right: -100%;
            top: -15px;
          }
          .club_icon_description {
            color: #393939;
            font-weight: 400;
          }
        }
      }
    }

    .users_buttons_wrapper {
      .join_button_wrapper {
        background: #dcb596;
      }

      .already_member_wrapper {
        border: 1px solid #dcb596;
        color: #dcb596 !important;
      }
    }
  }

  #logged_user_info_wrapper {
    background: white;
    border: 1px solid #31314c;

    .content_logged_wrapper {
      background: transparent !important;

      .center_content_wrapper {
        .logged_user_text {
          color: #393939;
        }

        .user_points {
          .content_wrapper {
            color: #393939 !important;
          }
        }

        .logout_button_wrapper {
          span {
            color: $corporate-2;
          }
        }
      }
    }
  }

  .lock_board_wrapper {
    border: 1px solid $corporate-2;
    padding: 10px !important;
    border-radius: 6px !important;
    position: relative;
    background: transparent !important;

    img {
      float: right;
      margin-left: 10px;
      margin-right: 0 !important;
    }

    span {
      color: $corporate-1;
    }

    span.currencyValue {
      font-size: 18px;
      letter-spacing: 0.99px;
      line-height: 24px;
    }

    .lock_ico {
      position: absolute;
      margin-left: 0 !important;
      top: -10px;
      left: 10px;
      background: white;
      padding: 0 5px;
      font-size: 10px !important;
      color: $corporate-1;

      &::after {
        content: "Desbloquear";
        font-family: $text-family;
        font-size: 9px;
        line-height: 22px;
        margin-left: 5px;
      }

      &:lang(en) {
        &:after {
          content: 'Unlock';
        }
      }
    }
  }

  #popup_login_information {
    .tabs_wrapper {
      div.active {
        color: $corporate-2;
        background: #FFF !important;
        border-color: $corporate-2;
        border: 1px solid;
        border-bottom: 1px solid #FFF;
        top: 1px;
        position: relative;
      }
    }

    #login_form_wrapper_v1 {
      .title_wrapper_block {
          background: #FFF;
          color: $corporate-1;
          border-top: 1px solid $corporate-1;

        .main_form_title,
        .subtitle_register{
          color: $corporate-2;
        }
      }

      .login_block {
        .login_form {
          .login_button_element {
            background: #FFF;
            color: $corporate-2;
            border-color: $corporate-2;
            border: 1px solid;
            &:hover {
                opacity: 0.8;
            }
          }
        }
      }
    }

    #register_form_wrapper_v1 {
      #signup_form {
        .title_wrapper_block {
          background: #FFF;
          color: $corporate-1;
          border-top: 1px solid $corporate-1;
          .main_form_title,
          .subtitle_register {
            color: $corporate-1;
          }
        }

        .inputs_wrapper {
          .buttons_wrapper_signup {
            .sign_up_button {
                background: #FFF;
                color: $corporate-2;
                border-color: $corporate-2;
                border: 1px solid;
                &:hover {
                    opacity: 0.8;
                }
            }
          }
        }
      }
    }
  }

  #reservation {
    h3 {
        font-weight: 700;
    }
  }

  .price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .btn_booking.active svg {
    left: 0%;
  }
