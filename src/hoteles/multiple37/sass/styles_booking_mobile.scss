@import "booking_mobile/v1/mixin_colores";
@import "booking_mobile/booking";

@import url('https://fonts.googleapis.com/css?family=Domine:400,700|Gothic+A1:300,400,500,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,600;0,700;0,800;1,300;1,400;1,600;1,700;1,800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Bellota+Text:wght@300;400;700&display=swap');

html[lang='ru'], html[lang='de'] {
  body.sao-felix .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit:after, body .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
    font-size: 0.7rem;
  }
}
//
//body.sao-felix {
//  $corporate_1: #938d1c;
//  $corporate_2: #2b808c;
//  $corporate_3: #292828;
//  $black: #261f07;
//  $red: #e75354;
//  $font_1: 'Gothic A1', sans-serif;
//
//  font-family: $font_1;
//  color: $black;
//
//  input, button, select {
//    font-family: $font_1;
//  }
//
//  @include set_color_process($corporate_1, $corporate_2, $corporate_3, white, $corporate_1);
//  header .menu.side_menu_wrapper .logo-wrapper img {
//    height: 50px;
//    max-width: 90%;
//  }
//  .modify_search {
//    background-color: $corporate_1;
//    color: white;
//    border: none;
//    font-size: 10px;
//    margin: 5px;
//  }
//  .continue_booking {
//    background: $corporate_1;
//  }
//  .show_calendar, .back_button {
//    background-color: white;
//    color: $corporate_1;
//    border: 2px solid $corporate_1;
//    padding: 0 10px 0 40px;
//    font-size: 10px;
//    margin: 5px
//  }
//  .btn.btn_secondary {
//    background-color: white;
//    color: $corporate_1;
//    border: 2px solid $corporate_1;
//  }
// .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit:after, .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
//   background: $corporate_3;
//   font-weight: bold;
// }
//  .regime_item_content {
//    .prices_options {
//      .final_price {
//        color: $black !important;
//      }
//      .price_through {
//        color: $red !important;
//      }
//    }
//    .regime_description .regime_offer_detail {
//      color: $red !important;
//    }
//    .discount_percentage {
//      background-color: $red !important;
//    }
//  }
//  .modal_container.room_content {
//    padding: 0 15px 35px 15px !important;
//    .room_description {
//      ul {
//        padding-left: 20px;
//        margin-top: 20px;
//      }
//    }
//  }
//  .owl-carousel .owl-item .product_picture img {
//    @include cover_image;
//  }
//  #calendar_price_availability .header_wrapper_calendar_availability .popup_helper_wrapper.search_button {
//    .message_element {
//      background: $corporate_1;
//      color: white;
//      padding: 10px;
//      font-weight: bold;
//    }
//  }
//
//  #calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper {
//    margin-bottom: 15px;
//  }
//  #calendar_price #prices-calendar .calendars-section .buttons-section .button, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .button{
//    position: relative;
//  }
//  .loading_animation_popup {
//    background: $corporate_1;
//    span {
//      color: white;
//    }
//    .spincircle {
//      border-color: rgba(#f5f5f5, .2);
//      .spinner_animation {
//        border-color: transparent transparent $corporate_2 transparent;
//        width: 150px;
//        height: 150px;
//      }
//      &:after {
//        border-color: transparent transparent $corporate_2 transparent !important;
//      }
//    }
//  }
//  .booking_widget_wrapper #entry_date_popup .header_wrapper .banner_title i, .booking_widget_wrapper #departure_date_popup .header_wrapper .banner_title i {
//    color: $corporate_1;
//  }
//  .booking_widget_wrapper {
//    #entry_date_popup .start_datepicker *, #entry_date_popup .end_datepicker *, #departure_date_popup .start_datepicker *, #departure_date_popup .end_datepicker * {
//      font-family: $font_1 !important;
//    }
//    .occupancy_popup .occupancy_head.content_title::before {
//      color: $corporate_1;
//    }
//    .input_wrapper {
//      .input_title, .ocupation_detail p {
//        font-size: 1rem !important;
//      }
//    }
//  }
//  #reservation {
//    .email_header_logo {
//      text-align: center;
//      #logo-container {
//        float: none !important;
//        #logo {
//          float: none !important;
//          img {
//            margin: auto;
//          }
//        }
//      }
//    }
//  }
//  footer .logo_wrapper img {
//    max-height: 90px;
//    max-width: 90%;
//  }
//}
//
//body.ecorkhotel {
//  $corporate_4: #7a7c60;
//  $corporate_5: #493a30;
//
//  #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper ~ .button {
//    background-color: $corporate_5;
//  }
//
//  .modify_search,
//  .btn.btn_primary {
//    background-color: $corporate_5;
//    color: white;
//    border: 1px solid white;
//  }
//
//  .show_calendar,
//  .back_button,
//  .btn.btn_secondary,
//  .label.label_primary {
//    background-color: $corporate_4;
//    color: white;
//    border: 1px solid white;
//  }
//
//  .booking_widget_wrapper {
//    .input_wrapper {
//      &::before {
//        background-color: $corporate_5;
//      }
//    }
//
//    #entry_date_popup,
//    #departure_date_popup {
//      .header_wrapper {
//        .banner_title {
//          i {
//            color: $corporate_4;
//          }
//        }
//      }
//
//      .step_label {
//        color: $corporate_4;
//      }
//
//      .start_datepicker,
//      .end_datepicker {
//        .ui-datepicker-inline {
//          .ui-datepicker-current-day {
//            &::before {
//              background: $corporate_5;
//            }
//
//            .ui-state-active {
//
//              &::before {
//                background-color: $corporate_4;
//              }
//            }
//          }
//
//          .highlight_day {
//            background: $corporate_5;
//          }
//
//          .ui-datepicker-header {
//            border-bottom-color: $corporate_5;
//          }
//
//          .start_date_selection {
//            &::before {
//              background: $corporate_5;
//            }
//
//            span {
//              &::before {
//                background-color: $corporate_4;
//              }
//            }
//          }
//        }
//
//        .ui-datepicker-calendar {
//          .ui-datepicker-week-end:nth-child(6) {
//            border-left-color: $corporate_5 !important;
//          }
//        }
//      }
//    }
//
//    #calendar_price_availability {
//      .header_wrapper_calendar_availability {
//        .popup_helper_wrapper {
//          color: $corporate_4;
//        }
//      }
//
//      #prices-calendar {
//        .calendars-section {
//          .buttons-section {
//            .button {
//              background-color: $corporate_5;
//            }
//          }
//        }
//      }
//    }
//
//    .occupancy_popup {
//      .occupancy_head.content_title {
//        &::before {
//          color: $corporate_5;
//        }
//      }
//
//      .occupancy_popup_body {
//        .room_resume_item {
//          .room_label {
//            color: $corporate_5;
//          }
//        }
//
//        .counter_box {
//          .control {
//            border-color: $corporate_5;
//
//            &::before {
//              background-color: $corporate_5;
//            }
//
//            &.add {
//              background-color: $corporate_5;
//
//              &::before {
//                background-color: white;
//              }
//            }
//          }
//        }
//
//        .room_resume_item .kids_age_select .age_options_wrapper .age_option {
//          color: $corporate_5;
//
//          &::before {
//            border: 1px solid $corporate_5;
//          }
//        }
//      }
//
//      .confirm_ocupation {
//        button {
//          background-color: $corporate_5;
//        }
//      }
//    }
//  }
//
//  #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr {
//    td.selected-cell-parent.first-selection, td.selected-cell-parent.end-selection {
//      &::before{
//        background-color: $corporate_4;
//      }
//
//      .day-content {
//        background-color: $corporate_5;
//      }
//
//      .day {
//        background-color: $corporate_5;
//      }
//    }
//
//    td.selected-cell-parent {
//      .day-content {
//        background-color: $corporate_4;
//      }
//
//      .day {
//        background-color: $corporate_4;
//      }
//    }
//  }
//
//  .main_content_wrapper {
//    &.step_0 {
//      .room_list {
//        .room_pack_option {
//          .rates_details_wrapper {
//            .regime_item_content {
//              .regime_price_wrapper {
//                div.submit {
//                  span {
//                    background-color: $corporate_5;
//                  }
//                }
//              }
//            }
//
//            .rate_selected_title {
//              span {
//                color: $corporate_4;
//              }
//            }
//          }
//
//          .room_name {
//            .info_icon {
//              color: $corporate_5;
//            }
//          }
//        }
//      }
//    }
//
//    &.step_1 {
//      .additional_services {
//        .custom_carousel {
//          .counter_box > div {
//            border-color: $corporate_4;
//            color: $corporate_4;
//          }
//        }
//      }
//    }
//
//    &.step_2 {
//      .reservation_summary {
//        .option_selected {
//          .price {
//            color: $corporate_4;
//          }
//
//          .rate {
//            .conditions {
//              color: $corporate_4;
//            }
//          }
//        }
//      }
//    }
//  }
//
//
//  .owl-carousel.custom_carousel {
//    .owl-item {
//      .price_label {
//        background-color: $corporate_4;
//      }
//    }
//  }
//}
//
//body.pio-apartments {
//  $corporate_1: #FFAC4D;
//  $corporate_2: #1D252D;
//  $corporate_3: #9d968d;
//  $black: #3D3D3C;
//  $red: #EC6363;
//  $font_1: 'Open Sans', sans-serif;
//
//  font-family: $font_1;
//  color: $black;
//  font-weight: 400;
//
//  input, button, select {
//    font-family: $font_1;
//  }
//
//  @include set_color_process($corporate_1, $corporate_2, $corporate_3);
//
//  .modify_search {
//    background-color: $corporate_1;
//    color: white;
//    border: none;
//    margin: 5px;
//  }
//
//  .show_calendar, .back_button {
//    background-color: white;
//    color: $corporate_1;
//    border: 2px solid $corporate_1;
//    padding: 0 20px 0 50px;
//    margin: 5px
//  }
//  .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
//    background: $corporate_1;
//  }
//  .regime_item_content {
//    .prices_options {
//      .final_price {
//        color: $black !important;
//      }
//      .price_through {
//        color: $red !important;
//      }
//    }
//    .regime_description .regime_offer_detail {
//      color: $red !important;
//    }
//    .discount_percentage {
//      background-color: $red !important;
//    }
//  }
//  .modal_container.room_content {
//    padding: 0 15px 35px 15px !important;
//    .room_description {
//      ul {
//        padding-left: 20px;
//        margin-top: 20px;
//      }
//    }
//  }
//  #calendar_price_availability .header_wrapper_calendar_availability .popup_helper_wrapper.search_button {
//    .message_element {
//      background: $corporate_1;
//      color: white;
//      padding: 10px;
//      font-weight: bold;
//    }
//  }
//
//  #calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper {
//    margin-bottom: 15px;
//  }
//  #calendar_price #prices-calendar .calendars-section .buttons-section .button, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .button{
//    position: relative;
//  }
//  .loading_animation_popup {
//    .spincircle {
//      border-color: rgba(#f5f5f5, .2);
//      .spinner_animation {
//        border-color: transparent transparent $corporate_2 transparent;
//        width: 150px;
//        height: 150px;
//      }
//      &:after {
//        border-color: transparent transparent $corporate_2 transparent !important;
//      }
//    }
//  }
//  .booking_widget_wrapper #entry_date_popup .header_wrapper .banner_title i, .booking_widget_wrapper #departure_date_popup .header_wrapper .banner_title i {
//    color: $corporate_1;
//  }
//  .booking_widget_wrapper {
//    #entry_date_popup .start_datepicker *, #entry_date_popup .end_datepicker *, #departure_date_popup .start_datepicker *, #departure_date_popup .end_datepicker * {
//      font-family: $font_1 !important;
//    }
//    .occupancy_popup .occupancy_head.content_title::before {
//      color: $corporate_1;
//    }
//    .input_wrapper {
//      .input_title, .ocupation_detail p {
//        font-size: 1rem !important;
//        font-weight: 400;
//      }
//    }
//  }
//  #reservation {
//    .email_header_logo {
//      text-align: center;
//      #logo-container {
//        float: none !important;
//        #logo {
//          float: none !important;
//          img {
//            margin: auto;
//          }
//        }
//      }
//    }
//  }
//  .main_content_wrapper.step_0 .tabs_wrapper {
//    .tabs li {
//      .tab_btn.active .tab_text {
//        font-weight: 400;
//        font-size: 14px;
//      }
//      &:nth-of-type(2) {
//        .tab_btn.active .tab_text {
//          font-size: 10px;
//        }
//      }
//    }
//    .tabs.with_info {
//      padding-bottom: 55px;
//      .extra_info {
//        font-size: 11px !important;
//      }
//    }
//    #content .packages_content .package_element.slide form {
//      .price_label, .product_details .product_description, .product_details .product_title_wrapper {
//        font-weight: 400;
//      }
//    }
//  }
//  footer {
//    .logo_wrapper {
//      display: none;
//    }
//    .info {
//      width: 100%;
//      text-align: center;
//    }
//  }
//  .booking_widget_wrapper .occupancy_popup .occupancy_popup_body .counter_box .control.subtract {
//    border: none;
//    padding: 0;
//    &:before {
//        content: "\f056";
//        font-family: "Font Awesome 5 Pro";
//        mask-image: none;
//        -webkit-mask-image: none;
//        background-color: transparent;
//        font-size: 30px;
//        width: auto;
//        margin-top: 3px;
//        font-weight: 300;
//        height: auto;
//    }
//  }
//}
//
//@import "palau-apartments/mobile_styles_booking";
//
//
//@import url('https://fonts.googleapis.com/css2?family=Bellota+Text:wght@300;400;700&display=swap');
//body.puertobahia-spa {
//  $corporate_1: #369CE0;
//  $corporate_2: #092A5B;
//  $corporate_3: $corporate_2;
//  $black: #3D3D3C;
//  $red: #EC6363;
//  $font_1: 'Bellota Text', cursive;
//
//  font-family: $font_1;
//  color: $black;
//  font-weight: 400;
//
//  input, button, select {
//    font-family: $font_1;
//  }
//
//  @include set_color_process($corporate_1, $corporate_2, $corporate_3);
//
//  .modify_search {
//    background-color: $corporate_1;
//    color: white;
//    border: none;
//    margin: 5px;
//  }
//  header {
//    #sideMenu {
//      background: $corporate_1;
//    }
//    .logo_wrapper img {
//      width: 180px;
//    }
//    .icon_toggle_wrapper.icon_toggle_active:before {
//      color: white !important;
//    }
//  }
//  .show_calendar, .back_button {
//    background-color: white;
//    color: $corporate_1;
//    border: 2px solid $corporate_1;
//    padding: 0 20px 0 50px;
//    margin: 5px
//  }
//  .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
//    background: $corporate_1;
//  }
//  .regime_item_content {
//    .prices_options {
//      .final_price {
//        color: $black !important;
//      }
//      .price_through {
//        color: $red !important;
//      }
//    }
//    .regime_description .regime_offer_detail {
//      color: $red !important;
//    }
//    .discount_percentage {
//      background-color: $red !important;
//    }
//  }
//  .modal_container.room_content {
//    padding: 0 15px 35px 15px !important;
//    .room_description {
//      ul {
//        padding-left: 20px;
//        margin-top: 20px;
//      }
//    }
//  }
//  #calendar_price_availability .header_wrapper_calendar_availability .popup_helper_wrapper.search_button {
//    .message_element {
//      background: $corporate_1;
//      color: white;
//      padding: 10px;
//      font-weight: bold;
//    }
//  }
//
//  #calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper {
//    margin-bottom: 15px;
//  }
//  #calendar_price #prices-calendar .calendars-section .buttons-section .button, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .button{
//    position: relative;
//  }
//  .loading_animation_popup {
//    .spincircle {
//      border-color: rgba(#f5f5f5, .2);
//      .spinner_animation {
//        border-color: transparent transparent $corporate_2 transparent;
//        width: 150px;
//        height: 150px;
//      }
//      &:after {
//        border-color: transparent transparent $corporate_2 transparent !important;
//      }
//    }
//  }
//  .booking_widget_wrapper #entry_date_popup .header_wrapper .banner_title i, .booking_widget_wrapper #departure_date_popup .header_wrapper .banner_title i {
//    color: $corporate_1;
//  }
//  .booking_widget_wrapper {
//    #entry_date_popup .start_datepicker *, #entry_date_popup .end_datepicker *, #departure_date_popup .start_datepicker *, #departure_date_popup .end_datepicker * {
//      font-family: $font_1 !important;
//    }
//    .occupancy_popup .occupancy_head.content_title::before {
//      color: $corporate_1;
//    }
//    .input_wrapper {
//      .input_title, .ocupation_detail p {
//        font-size: 1rem !important;
//        font-weight: 400;
//      }
//    }
//  }
//  #reservation {
//    .email_header_logo {
//      text-align: center;
//      #logo-container {
//        float: none !important;
//        #logo {
//          float: none !important;
//          img {
//            margin: auto;
//          }
//        }
//      }
//    }
//  }
//  .main_content_wrapper.step_0 .tabs_wrapper {
//    .tabs li {
//      .tab_btn.active .tab_text {
//        font-weight: 400;
//        font-size: 14px;
//      }
//      &:nth-of-type(2) {
//        .tab_btn.active .tab_text {
//          font-size: 10px;
//        }
//      }
//    }
//    .tabs.with_info {
//      padding-bottom: 55px;
//      .extra_info {
//        font-size: 11px !important;
//      }
//    }
//    #content .packages_content .package_element.slide form {
//      .price_label, .product_details .product_description, .product_details .product_title_wrapper {
//        font-weight: 400;
//      }
//    }
//  }
//  footer {
//    .logo_wrapper {
//      display: none;
//    }
//    .info {
//      width: 100%;
//      text-align: center;
//    }
//  }
//  .booking_widget_wrapper .occupancy_popup .occupancy_popup_body .counter_box .control.subtract {
//    border: none;
//    padding: 0;
//    &:before {
//        content: "\f056";
//        font-family: "Font Awesome 5 Pro";
//        mask-image: none;
//        -webkit-mask-image: none;
//        background-color: transparent;
//        font-size: 30px;
//        width: auto;
//        margin-top: 3px;
//        font-weight: 300;
//        height: auto;
//    }
//  }
//}
//
//@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;700&display=swap');
//body.ramblas-barcelona, body.ramblas-vendrell {
//  $corporate_1: #4BB2F2;
//  $corporate_2: #0B2A4F;
//  $corporate_3: $corporate_2;
//  $black: #505050;
//  $red: #e75354;
//  $font_1: 'Open Sans', sans-serif;
//
//
//  font-family: $font_1;
//  color: $black;
//
//  input, button, select {
//    font-family: $font_1;
//  }
//
//  @include set_color_process($corporate_1, $corporate_2, $corporate_3, $black, white);
//  header {
//    background-color: $corporate_1;
//
//    #sideMenu {
//      background-color: $corporate_1;
//    }
//  }
//
//
//  header .menu.side_menu_wrapper {
//    .logo-wrapper img {
//      height: 50px;
//      max-width: 90%;
//    }
//
//    .side_menu_content {
//      .ticks_wrapper {
//        .ticks_content {
//          .ticks_list {
//            .ticks_list_item {
//              .font-icon-wrapper::before {
//                color: $corporate_2;
//              }
//            }
//          }
//        }
//      }
//    }
//  }
//  .modify_search {
//    background-color: $corporate_1;
//    color: white;
//    border: none;
//    margin: 5px;
//  }
//  .continue_booking {
//    background: $corporate_1;
//  }
//
//  .back_booking {
//    background-color: $corporate_2;
//  }
//
//  .show_calendar, .back_button {
//    background-color: white;
//    color: $corporate_1;
//    border: 2px solid $corporate_1;
//    padding: 0 20px 0 40px;
//    margin: 5px
//  }
//  .btn.btn_secondary {
//    background-color: white;
//    color: $corporate_1;
//    border: 2px solid $corporate_1;
//  }
// .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit:after, .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
//   background: $corporate_3;
//   font-weight: bold;
//   border-radius: 0;
// }
//  .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_description {
//    padding: 20px 40px 20px 5px;
//  }
//
//  footer .legal_info_wrapper .current_year {
//    color: white;
//  }
//
//  .main_content_wrapper.step_2 .personal_details_form_wrapper .personal_details_form .bottom_button_wrapper #btn-finish-booking {
//    border-radius: 0;
//  }
//
//  //header .icon_toggle_wrapper {
//  //  background: white;
//  //  &.icon_toggle_active {
//  //      right: calc(100vw - 300px);
//  //      background: transparent;
//  //      &:before {
//  //        background: transparent !important;
//  //      }
//  //  }
//  //
//  //  &:before, &:after {
//  //    background: white;
//  //  }
//  //}
//  //
//  //header .logo_wrapper {
//  //  padding-left: 0;
//  //}
//
//  .regime_item_content {
//    .prices_options {
//      .final_price {
//        color: $black !important;
//      }
//      .price_through {
//        color: $red !important;
//      }
//    }
//    .regime_description .regime_offer_detail {
//      color: $red !important;
//    }
//    .discount_percentage {
//      background-color: $red !important;
//    }
//  }
//  .modal_container.room_content {
//    padding: 0 15px 35px 15px !important;
//    .room_description {
//      ul {
//        padding-left: 20px;
//        margin-top: 20px;
//      }
//    }
//  }
//  #calendar_price_availability .header_wrapper_calendar_availability .popup_helper_wrapper.search_button {
//    .message_element {
//      background: $corporate_1;
//      color: white;
//      padding: 10px;
//      font-weight: bold;
//    }
//  }
//
//  #calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper {
//    margin-bottom: 15px;
//  }
//  #calendar_price #prices-calendar .calendars-section .buttons-section .button, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .button{
//    position: relative;
//  }
//  .loading_animation_popup {
//    background: $corporate_1;
//    span {
//      color: white;
//    }
//    .spincircle {
//      border-color: rgba(#f5f5f5, .2);
//      .spinner_animation {
//        border-color: transparent transparent $corporate_2 transparent;
//        width: 150px;
//        height: 150px;
//      }
//      &:after {
//        border-color: transparent transparent $corporate_2 transparent !important;
//      }
//    }
//  }
//  .booking_widget_wrapper #entry_date_popup .header_wrapper .banner_title i, .booking_widget_wrapper #departure_date_popup .header_wrapper .banner_title i {
//    color: $corporate_1;
//  }
//  .booking_widget_wrapper {
//    #entry_date_popup .start_datepicker *, #entry_date_popup .end_datepicker *, #departure_date_popup .start_datepicker *, #departure_date_popup .end_datepicker * {
//      font-family: $font_1 !important;
//    }
//    .occupancy_popup .occupancy_head.content_title::before {
//      color: $corporate_1;
//    }
//    .input_wrapper {
//      .input_title, .ocupation_detail p {
//        font-size: 1rem !important;
//      }
//    }
//  }
//  #reservation {
//    .email_header_logo {
//      text-align: center;
//      #logo-container {
//        float: none !important;
//        #logo {
//          float: none !important;
//          img {
//            margin: auto;
//          }
//        }
//      }
//    }
//  }
//  footer .logo_wrapper img {
//    max-height: 90px;
//    max-width: 90%;
//  }
//  footer {
//    background-color: $corporate_1;
//  }
//
//  .owl-carousel .owl-item .product_picture img {
//    @include cover_image;
//  }
//}

body.hotel-mariner.booking_process_mobile_v1{
  $corporate_1: #102A64;
  $corporate_2: #8B5637;
  $corporate_3: #015EC5;
  $corporate_4: #63ac3c;


  font-family: 'Bodoni Moda', sans-serif;

  #main_modal.active{
    .body_modal{
      &.regime_conditions_modal_wrapper, &.iframe_modal_wrapper{
        .body_modal_content iframe{
          height: 69vh;
        }
      }
      &.rooms_features_modal_wrapper{
        overflow-y: scroll;
      }
    }
  }

  .double_button_wrapper .btn {
    color: $corporate_1;
    border: 1px solid $corporate_1;
  }

  .main_content_wrapper.step_0 {
    .tabs_wrapper .tabs li .tab_btn.active {
      color: $corporate_1;
    }

    .room_list .room_pack_option .rates_details_wrapper {
      .rate_selected_title span {
        color: $corporate_2;
      }

      .regime_item_content .regime_price_wrapper div.submit span {
        background: $corporate_3;
      }
    }
  }

  .booking_widget_wrapper {
    .input_wrapper:before {
      background-color: $corporate_1;
    }

    #entry_date_popup {
      .header_wrapper .banner_title i {
        color: $corporate_1;
      }

      .start_datepicker .ui-datepicker-inline {
        .ui-datepicker-header {
          color: $corporate_1;
        }

        .ui-datepicker-current-day .ui-state-active:before {
          background-color: $corporate_1;
        }
      }
    }
  }

  .continue_booking {
    background-color: $corporate_3;
  }

  .btn.btn_secondary {
    background-color: $corporate_4;
  }

  .main_content_wrapper.step_2 {
    .reservation_summary .option_selected .price {
      color: $corporate_1;
    }

    #btn-finish-booking {
      background-color: $corporate_4;
    }

    .modify_search, .back_button {
      color: $corporate_1;
      border: 1px solid $corporate_1;
    }
  }
}

body.hotel-leyre.booking_process_mobile_v1:not(.habitus-source)   {
  $corporate_1: #262626;
  $corporate_2: #262626;
  $corporate_3: $corporate_2;
  $black: #3D3D3C;
  $red: #EC6363;
  $font_1: "Montserrat",sans-serif;

  $gold: #b08401;

  font-family: $font_1;
  color: $black;
  font-weight: 400;

  input, button, select {
    font-family: $font_1;
  }

  @include set_color_process($corporate_1, $corporate_2, $corporate_3);

  #main_modal.active .body_modal.iframe_modal_wrapper .body_modal_content {
    overflow: auto;
  }

  .modify_search {
    background-color: $corporate_1;
    color: white;
    border: none;
    margin: 5px;
  }
  header {
    #sideMenu {
      background: $corporate_1;
    }
    .logo_wrapper img {
      width: auto;
    }
    .icon_toggle_wrapper.icon_toggle_active:before {
      color: white !important;
    }
  }
  .show_calendar, .back_button {
    background-color: white;
    color: $corporate_1;
    border: 2px solid $corporate_1;
    padding: 0 20px 0 50px;
    margin: 5px
  }
  .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
    background: $corporate_1;
    border-radius: 0;
  }
  .regime_item_content {
    .prices_options {
      .final_price {
        color: $black !important;
      }
      .price_through {
        color: $red !important;
      }
    }
    .regime_description .regime_offer_detail {
      color: $red !important;
    }
    .discount_percentage {
      background-color: $red !important;
    }
  }
  .modal_container.room_content {
    padding: 0 15px 35px 15px !important;
    .room_description {
      ul {
        padding-left: 20px;
        margin-top: 20px;
      }
    }
  }
  #calendar_price_availability .header_wrapper_calendar_availability .popup_helper_wrapper.search_button {
    .message_element {
      background: $corporate_1;
      color: white;
      padding: 10px;
      font-weight: bold;
    }
  }

  #calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper {
    margin-bottom: 15px;
  }
  #calendar_price #prices-calendar .calendars-section .buttons-section .button, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .button{
    position: relative;
  }
  .loading_animation_popup {
    .spincircle {
      border-color: rgba(#f5f5f5, .2);
      .spinner_animation {
        border-color: transparent transparent $corporate_2 transparent;
        width: 150px;
        height: 150px;
      }
      &:after {
        border-color: transparent transparent $corporate_2 transparent !important;
      }
    }
  }
  .booking_widget_wrapper #entry_date_popup .header_wrapper .banner_title i, .booking_widget_wrapper #departure_date_popup .header_wrapper .banner_title i {
    color: $corporate_1;
  }
  .booking_widget_wrapper {
    #entry_date_popup .start_datepicker *, #entry_date_popup .end_datepicker *, #departure_date_popup .start_datepicker *, #departure_date_popup .end_datepicker * {
      font-family: $font_1 !important;
    }
    .occupancy_popup .occupancy_head.content_title::before {
      color: $corporate_1;
    }
    .input_wrapper {
      .input_title, .ocupation_detail p {
        font-size: 1rem !important;
        font-weight: 400;
      }
    }
  }
  #reservation {
    .email_header_logo {
      text-align: center;
      #logo-container {
        float: none !important;
        #logo {
          float: none !important;
          img {
            margin: auto;
          }
        }
      }
    }
  }
  .main_content_wrapper.step_0 .tabs_wrapper {
    .tabs li {
      .tab_btn.active .tab_text {
        font-weight: 400;
        font-size: 14px;
      }
      &:nth-of-type(2) {
        .tab_btn.active .tab_text {
          font-size: 10px;
        }
      }
    }
    .tabs.with_info {
      padding-bottom: 55px;
      .extra_info {
        font-size: 11px !important;
      }
    }
    #content .packages_content .package_element.slide form {
      .price_label, .product_details .product_description, .product_details .product_title_wrapper {
        font-weight: 400;
      }
    }
  }
  .additional_service_form .continue_booking, .main_content_wrapper.step_2 .personal_details_form_wrapper .personal_details_form .bottom_button_wrapper #btn-finish-booking{
    background: $corporate_1;
  }
  footer {
    .logo_wrapper {
      display: none;
    }
    .info {
      width: 100%;
      text-align: center;
    }
  }
  .booking_widget_wrapper .occupancy_popup .occupancy_popup_body .counter_box .control.subtract {
    border: none;
    padding: 0;
    &:before {
        content: "\f056";
        font-family: "Font Awesome 5 Pro";
        mask-image: none;
        -webkit-mask-image: none;
        background-color: transparent;
        font-size: 30px;
        width: auto;
        margin-top: 3px;
        font-weight: 300;
        height: auto;
    }
  }

  #reservation {
    button.booking-button--confirmed-booking {
      display: none !important;
    }
  }
  .main_content_wrapper.step_0 .tabs_wrapper .tabs li{
    border-radius: 0px;
  }
}

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
body.hotel-leyre.booking_process_mobile_v1.habitus-source {
  $corporate_1: #F9AE00;
  $corporate_2: #EDA602;
  $corporate_3: #00A870;

  $black: #3C3C3B;
  $lightgrey: #F5F5F5;
  $red: #EC6363;

  $font_1: 'Poppins', sans-serif;
  $font_2: 'Poppins', sans-serif;
  $fa6: "Font Awesome 6 Pro";

  $title_family: $font_1;
  $text_family: $font_2;
  @import "leyre_booking_from_habitus/mobile/habitus-booking";
}

body.quinta-dos-machados.booking_process_mobile_v1{
  #main_modal.active{
    .body_modal{
      &.regime_conditions_modal_wrapper, &.iframe_modal_wrapper{
        .body_modal_content iframe{
          height: 69vh;
        }
      }
    }
  }
  .reservation_summary.booking4{
    padding: 0 15px;
    .email_header{
      table{
        font-size: 14px;
      }
    }
  }
}

body.casa-romana-boutique.booking_process_mobile_v1 {
  $corporate_1: #D8D0BE;
  $corporate_2: #1D1D1B;
  $corporate_3: $corporate_2;
  $black: #3D3D3C;
  $red: #EC6363;
  $font_1: 'Mulli', sans-serif;
  $font_booking_button: 'Georgia', serif;

  font-family: $font_1;
  color: $black;
  font-weight: 400;

  input, button, select {
    font-family: $font_1;
  }

  @include set_color_process($corporate_1, $corporate_2, $corporate_3);

  .modify_search {
    background-color: $corporate_1;
    color: white;
    border: none;
    margin: 5px;
  }
  header {
    #sideMenu {
      background: $corporate_1;
    }
    .logo_wrapper img {
      width: auto;
    }
    .icon_toggle_wrapper.icon_toggle_active:before {
      color: white !important;
    }
  }
  .show_calendar, .back_button {
    background-color: white;
    color: $corporate_1;
    border: 2px solid $corporate_1;
    padding: 0 20px 0 50px;
    margin: 5px
  }
  .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
    background: $corporate_2;
    border-radius: 0;
    font-family: $font_booking_button;
  }
  .regime_item_content {
    .prices_options {
      .final_price {
        color: $black !important;
      }
      .price_through {
        color: $red !important;
      }
    }
    .regime_description .regime_offer_detail {
      color: $red !important;
    }
    .discount_percentage {
      background-color: $red !important;
    }
  }
  .modal_container.room_content {
    padding: 0 15px 35px 15px !important;
    .room_description {
      ul {
        padding-left: 20px;
        margin-top: 20px;
      }
    }
  }
  #calendar_price_availability .header_wrapper_calendar_availability .popup_helper_wrapper.search_button {
    .message_element {
      background: $corporate_1;
      color: white;
      padding: 10px;
      font-weight: bold;
    }
  }

  #calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper {
    margin-bottom: 15px;
  }
  #calendar_price #prices-calendar .calendars-section .buttons-section .button, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .button{
    position: relative;
  }
  .loading_animation_popup {
    .spincircle {
      border-color: rgba(#f5f5f5, .2);
      .spinner_animation {
        border-color: transparent transparent $corporate_2 transparent;
        width: 150px;
        height: 150px;
      }
      &:after {
        border-color: transparent transparent $corporate_2 transparent !important;
      }
    }
  }
  .booking_widget_wrapper #entry_date_popup .header_wrapper .banner_title i, .booking_widget_wrapper #departure_date_popup .header_wrapper .banner_title i {
    color: $corporate_1;
  }
  .booking_widget_wrapper {
    #entry_date_popup .start_datepicker *, #entry_date_popup .end_datepicker *, #departure_date_popup .start_datepicker *, #departure_date_popup .end_datepicker * {
      font-family: $font_1 !important;
    }
    .occupancy_popup .occupancy_head.content_title::before {
      color: $corporate_1;
    }
    .input_wrapper {
      .input_title, .ocupation_detail p {
        font-size: 1rem !important;
        font-weight: 400;
      }
    }
  }
  #reservation {
    .email_header_logo {
      text-align: center;
      #logo-container {
        float: none !important;
        #logo {
          float: none !important;
          img {
            margin: auto;
          }
        }
      }
    }
  }
  .main_content_wrapper.step_0 .tabs_wrapper {
    .tabs li {
      .tab_btn.active .tab_text {
        font-weight: 400;
        font-size: 14px;
      }
      &:nth-of-type(2) {
        .tab_btn.active .tab_text {
          font-size: 10px;
        }
      }
    }
    .tabs.with_info {
      padding-bottom: 55px;
      .extra_info {
        font-size: 11px !important;
      }
    }
    #content .packages_content .package_element.slide form {
      .price_label, .product_details .product_description, .product_details .product_title_wrapper {
        font-weight: 400;
      }
    }
  }
  footer {
    .logo_wrapper {
      display: none;
    }
    .info {
      width: 100%;
      text-align: center;
    }
  }
  .booking_widget_wrapper .occupancy_popup .occupancy_popup_body .counter_box .control.subtract {
    border: none;
    padding: 0;
    &:before {
        content: "\f056";
        font-family: "Font Awesome 5 Pro";
        mask-image: none;
        -webkit-mask-image: none;
        background-color: transparent;
        font-size: 30px;
        width: auto;
        margin-top: 3px;
        font-weight: 300;
        height: auto;
    }
  }

  #reservation {
    button.booking-button--confirmed-booking {
      display: none !important;
    }
  }
  .main_content_wrapper.step_2 .personal_details_form_wrapper .personal_details_form .bottom_button_wrapper #btn-finish-booking{
    background: $corporate_2;
  }
  .loading_animation_popup{
    span{
      color: white;
    }
    .spincircle .spinner_animation{
      .circle1, .circle2, .circle3, .circle4{
        background-color: white;
      }
    }
  }
  .sectoption{
    border-color: $corporate_1;
    .titSelOpt{
      color: $corporate_1;
    }
    input[type=checkbox]:checked::before,
    input[type=radio]:not(.payment_radiobtn):checked::before{
      background: $corporate_1;
    }
  }
  .all_additional_services_wrapper .category_wrapper{
    &:not(.upgrade){
      .additional_services_wrapper{
        .additional_service_element{
          .service_select{
            .add_service_button, .amount_selection .button-selection{
              background: $corporate_1;
            }
          }
          &:before, &:after{
            background: $corporate_1;
          }
        }
      }
    }

  }
  .title_category .main_title{
    color: $corporate_1;
  }
  .additional_services_total_wrapper .perform_additional_services_booking,
  .top_continue_booking .perform_additional_services_booking,
  .all_additional_services_wrapper .category_wrapper:not(.upgrade) .additional_services_wrapper .additional_service_element .price_tag{
    background: $corporate_2;
  }
}


@font-face {
  font-family: 'circe rounded alt bold';
  src: url("/static_1/fonts/CirceRounded/CirceRounded-AltBold.otf");
}

@font-face {
  font-family: 'circe rounded extra light';
  src: url("/static_1/fonts/CirceRounded/CirceRounded-ExtraLight.otf");
}

@font-face {
  font-family: 'circe rounded regular';
  src: url("/static_1/fonts/CirceRounded/CirceRounded-Regular.otf");
}

@font-face {
  font-family: 'circe rounded regular 2';
  src: url("/static_1/fonts/CirceRounded/CirceRounded-Regular2.otf");
}

@font-face {
  font-family: 'circe rounded regular 3';
  src: url("/static_1/fonts/CirceRounded/CirceRounded-Regular3.otf");
}

body.magaluf-playa {
  $corporate_1: #d6b99c;
  $corporate_2: #0F2453;
  $corporate_3: #5cb5e2;
  $black: #0F2453;
  $red: #ff8756;
  $font_1: "Karla", sans-serif;

  font-family: 'circe rounded regular';
  color: $black;

  input, button, select {
    font-family: $font_1;
  }

  @include set_color_process($corporate_1, $corporate_2, $corporate_3);

  header {
    #sideMenu {
      background: $corporate_1;
    }

    a.icon_toggle_wrapper.icon_toggle_active:before {
      color: white;
    }
  }

  .modify_search {
    background-color: $corporate_1;
    color: white;
    border: none;
    margin: 5px;
  }

  .continue_booking {
    background: $corporate_1;
  }

  .show_calendar, .back_button {
    background-color: $corporate_2;
    color: white;
    border: none;
    padding: 0 20px 0 50px;
    margin: 5px
  }

  .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit:after, .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
    background: $corporate_3;
  }

  .regime_item_content {
    .prices_options {
      .final_price {
        color: $black !important;
      }

      .price_through {
        color: $red !important;
      }
    }

    .regime_description .regime_offer_detail {
      color: $red !important;
    }

    .discount_percentage {
      background-color: $red !important;
    }
  }

  .modal_container.room_content {
    padding: 0 15px 35px 15px !important;

    .room_description {
      ul {
        padding-left: 20px;
        margin-top: 20px;
      }
    }
  }

  #calendar_price_availability .header_wrapper_calendar_availability .popup_helper_wrapper.search_button {
    .message_element {
      background: $corporate_1;
      color: white;
      padding: 10px;
      font-weight: bold;
    }
  }

  #calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper {
    margin-bottom: 15px;
  }

  #calendar_price #prices-calendar .calendars-section .buttons-section .button, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .button {
    position: relative;
  }

  .loading_animation_popup {
    background: $corporate_1;

    span {
      color: white;
    }

    .spincircle {
      border-color: rgba(#f5f5f5, .2);

      .spinner_animation {
        border-color: transparent transparent $corporate_2 transparent;
        width: 150px;
        height: 150px;
      }

      &:after {
        border-color: transparent transparent $corporate_2 transparent !important;
      }
    }
  }

  .booking_widget_wrapper #entry_date_popup .header_wrapper .banner_title i, .booking_widget_wrapper #departure_date_popup .header_wrapper .banner_title i {
    color: $corporate_1;
  }

  .booking_widget_wrapper {
    #entry_date_popup .start_datepicker *, #entry_date_popup .end_datepicker *, #departure_date_popup .start_datepicker *, #departure_date_popup .end_datepicker * {
      font-family: $font_1 !important;
    }
  }

  #main_modal.active{
    .body_modal{
      &.regime_conditions_modal_wrapper, &.iframe_modal_wrapper{
        .body_modal_content iframe{
          height: 69vh;
        }
      }
      &.rooms_features_modal_wrapper{
        overflow-y: scroll;
      }
    }
  }
}

body.nura-can-beia{
  $corporate_1: #967a50;
  $corporate_2: #967a50;
  $corporate_3: #0f2453;
  $corporate_4: #ff8756;
  $light_corportate_1: lighten($corporate_1, 35%);
  $black: #0f2453;
  $grey: #eae7e7;
  $lightgrey: #f7f7f7;
  font-family: 'circe rounded regular';
  color: $black;

  @include set_color_process($corporate_1, $corporate_2, $corporate_3);

  .double_button_wrapper{
   .modify_search{
      color: $corporate_1;
      border-color: $corporate_1;
    }
    .show_calendar, .back_button{
      color: $corporate_3;
      border-color: $corporate_3;
    }
  }
  .main_content_wrapper.step_2 .personal_details_form_wrapper .personal_details_form .bottom_button_wrapper #btn-finish-booking{
    background: $corporate_1;
  }
  #main_modal.active{
    .body_modal{
      &.regime_conditions_modal_wrapper, &.iframe_modal_wrapper{
        .body_modal_content iframe{
          height: 69vh;
        }
      }
    }
  }

  #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.available .price::before{
    top: 30px;
  }

  @media(max-width: 575px){
    #main_modal.active .body_modal.rooms_features_modal_wrapper .body_modal_content .modal_container{
      height: calc(100% - 380px);
    }
  }
}
@import url("https://fonts.googleapis.com/css?family=Nunito:400,700,800|Playfair+Display:400,400i,700,700i,900,900i&display=swap");

body.nura-boreal , body.nura-condor, body.nura-ponsa, body.nura-rosa{
  $corporate_1: #85a078;
  $corporate_2: #85a078;
  $corporate_3: #44693D;
  $corporate_4: #ff8756;
  $light_corportate_1: lighten($corporate_1, 35%);
  $black: #0f2453;
  $grey: #eae7e7;
  $lightgrey: #f7f7f7;
  $font: 'Nunito', sans-serif;
  font-family: 'Nunito', sans-serif;
  color: $black;

  @include set_color_process($corporate_1, $corporate_2, $corporate_3);

  .double_button_wrapper{
   .modify_search{
      color: $corporate_1;
      border-color: $corporate_1;
      font-family: $font;
    }
    .show_calendar, .back_button{
      color: $corporate_3;
      border-color: $corporate_3;
      font-family: $font;
    }
  }
  .main_content_wrapper.step_2 .personal_details_form_wrapper .personal_details_form .bottom_button_wrapper #btn-finish-booking{
    background: $corporate_1;
  }
  #main_modal.active{
    .body_modal{
      &.regime_conditions_modal_wrapper, &.iframe_modal_wrapper{
        .body_modal_content iframe{
          height: 69vh;
        }
      }
    }
  }

  #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.available .price::before{
    top: 30px;
  }

  @media(max-width: 575px){
    #main_modal.active .body_modal.rooms_features_modal_wrapper .body_modal_content .modal_container{
      height: calc(100% - 380px);
    }
  }
 .local_resident_wrapper {
          background-color: #44693D;
          color: white;
          input{
            border-color: white !important;
            &:before{
              background-color: #44693D;
            }
            &:checked{
              &:before{
                color: white !important;
              }
            }
          }
        }

  .personal_details_form_wrapper{
    .personal_details_form{
      .bottom_button_wrapper{
        #btn-finish-booking{
          font-family: $font;
        }
      }
    }
  }

}

body.fleming-poniente {
  .main_content_wrapper.wizard.step_3 {
    #reservation {
      margin-bottom: -160px;

      .email_header_logo {
        h2 {
          color: black;
        }
      }
    }

    .double_button_wrapper {
      display: none;
    }
  }

  #main_modal.active .body_modal.iframe_modal_wrapper .body_modal_content iframe {
    height: 68vh;
  }
}