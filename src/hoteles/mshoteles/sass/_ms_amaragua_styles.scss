@import url('https://fonts.googleapis.com/css2?family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;600;700;800&display=swap');
@import 'general_desktop_styles';

body.ms-amaragua.booking_process_version_1 {

  $corporate_1: #005487;
  $corporate_2: #867874;
  $corporate_3: #545454;
  $corporate_4: #63ac3c;

  $black: $corporate_3;
  $grey: #C4BEB6;
  $lightgrey: #F6F6F6;

  $body_text_color: $black;
  $links_color: $black;

  $font_1: 'Libre Baskerville', serif;
  $font_2: 'Raleway', sans-serif;

  $title_family: $font_1;
  $text_family: $font_2;

  $shadow: 0px 3px 12px 3px rgba(0, 0, 0, 0.22);

  color: $body_text_color !important;


  div#step-1 .hide_modify_search.booking-box--search .booking-box__content {
    #currencyDiv {
      margin: 30px auto 10px !important;
    }

    .title_booking_breakdown::after {
      height: 90px;
      top: 5px;
      right: -5px;
    }
  }

  div.site-header {
    height: 130px;
    padding: 0 30px 0 0;
    background: white !important;
    font-family: $font_2;
    border-bottom: 1px solid $corporate_2;

    i:before {
      font-family: "Font Awesome 5 Pro";
    }

    .site-header__logo {
      position: relative;
      margin-top: 40px;

      a {
        img {
          margin: 0;
          max-height: 60px;
        }
      }
    }

    .site-header__ticks {
      margin-top: 30px;
      margin-right: 60px;

      .site-header__tick-item {
        margin-top: 5px;
        margin-right: 0;
        float: none;
        display: inline-block;
        vertical-align: middle;
        width: 120px;

        p {
          width: 80px;
          font-size: 8px;
          line-height: 12px;
          text-transform: uppercase;
          font-weight: 300;
          text-align: center;
          margin-left: -10px;
          letter-spacing: 1px;
          color: $black !important;
        }

        .icon-checkmark {
          background-image: none;
          font-size: 50px;
          margin-top: -10px;
          display: inline-block;

          &:before {
            font-size: 22px !important;
            line-height: 52px !important;
            color: $corporate_2 !important;
          }
        }

        &:first-of-type {
          margin-top: 5px !important;

          .icon-checkmark {
            padding: 5px !important;

            &:before {
              font-family: "Font Awesome 5 Pro" !important;
              content: "\f274" !important;
              font-weight: 300;
            }
          }

          p {
            margin-left: 0;
            top: 0 !important;
          }
        }

        &:nth-of-type(2) .icon-checkmark:before {
          font-family: "Font Awesome 5 Pro" !important;
          content: "\f132" !important;
          font-weight: 300;

          p {
            width: 60px;
          }
        }

        &:nth-of-type(3) .icon-checkmark:before {
          font-family: "Font Awesome 5 Pro" !important;
          content: "\f4d3" !important;
          font-weight: 300;
        }

        &:last-of-type {
          display: none;
        }
      }
    }

    .language_header_selector_booking_process {
      bottom: -35px;
      border: none;
      width: 110px;
      right: 0;
      text-transform: uppercase;

      .selected_language {
        font-size: 14px;
        margin-top: 0;
        color: $black !important;
        font-weight: 300;
        width: 110px;

        i {
          color: $corporate_2;
          font-size: 20px;
        }
      }

      .selected_language_code {
        &:before {
          content: "\f107";
          font-family: "Font Awesome 5 Pro";
          font-size: 18px;
          font-weight: 300;
          position: absolute;
          right: -15px;
          top: 25px;
        }
      }

      .language_booking_selector_wrapper {
        top: 65% !important;
      }
    }
  }

  #redirection-message {
    z-index: 10;
  }

  .actual_wizard_step {
    padding-top: 30px;

    li.wizard-tab--small {
      width: 33%;

      &::after {
        width: 358px !important;
        left: 196px !important;
        border-color: #efefef !important;
      }

      &:nth-child(3) {
        &::after {
          display: none !important;
        }
      }

      &:last-child {
        display: none !important;
      }
    }

    li.wizard-tab--small a,
    li.wizard-tab--big a {
      border-left: 2px solid white;

      .step-inner--small {
        font-family: $text_family;
      }

      &:not(.disable) {
        &:before {
          display: block;
          border-left-color: white;
          left: calc(100% + 2px);
        }
      }

      &.disable {
        &:before {
          display: block;
          border-left-color: white;
          left: calc(100% + 2px);
        }
      }
    }
  }

  #full_wrapper_booking {
    box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    padding: 0;
  }

  .booking_engine_wrapper_process {
    background-color: transparent;

    #booking.boking_widget_inline {
      width: 100%;
      background: white;
      border-bottom: 1px solid $corporate_2;
      padding-top: 3px;
      padding-bottom: 0;

      .booking_form.paraty-booking-form {
        width: 1140px;
        margin: auto;
        position: relative;
      }

      .stay_selection {
         width: 25%!important;

        .date_box {
          &.entry_date,
          &.departure_date {
            background-image: none;

            .date_day {
              font-family: $text_family !important;
              color: $black;
              font-weight: 400;
            }
          }

          .departure_date {
            background-image: none !important;
          }
        }

        .departure_date_wrapper, .entry_date_wrapper {
          border-bottom: none !important;
          height: 85px;
          padding: 15px 15px;
          text-transform: uppercase;

          .date_box {
            margin-top: 15px;
          }
        }

        .departure_date_wrapper {
          background: white;
          padding-left: 56%;

          label {
            font-family: $text_family;
          }

          &:after {
            content: '';
            position: absolute;
            top: 10px;
            height: 65px;
            bottom: 5px;
            right: 0;
            width: 1px;
            display: block;
            background: $corporate_1;
            opacity: .3;
          }
        }

        .entry_date_wrapper {
          background: transparent;

          label {
            font-family: $text_family;
          }

          &:after {
            content: '\f105';
            font-family: "Font Awesome 5 Pro";
            font-size: 35px;
            position: absolute;
            top: 50%;
            height: 45px;
            bottom: 5px;
            font-weight: 100;
            right: -5px;
            display: block;
            color: $corporate_2;
          }
        }

        label {
          color: $black;
          font-size: 12px;
          font-weight: normal;
          letter-spacing: 4px;
          text-transform: capitalize;
        }

        .date_day {
          font-weight: normal;
          font-style: normal;
          font-size: 18px !important;
          letter-spacing: 1px;
          color: black;

          span {
            display: inline-block;
            vertical-align: baseline;

            &.day {
              font-weight: 700;
              font-size: 25px;
            }

            &.month {
              padding: 0 5px;
            }
          }
        }
      }

      .guest_selector {
        margin: 0;
        height: 85px;
        padding: 15px 15px;
        width: 275px;
        background: white;

        &:after {
          content: '';
          position: absolute;
          top: 10px;
          height: 65px;
          bottom: 5px;
          right: 0;
          width: 1px;
          display: block;
          background: $corporate_1;
          opacity: .3;
        }

        label {
          color: $black;
          text-transform: capitalize;
          font-size: 12px;
          font-weight: normal;
          letter-spacing: 4px;
          font-family: $text_family;
        }

        .placeholder_text {
          font-style: normal;
          letter-spacing: 1px;
          font-family: $text_family;
          color: $black;
          font-weight: 400;
          margin-top: 15px;

          span {
            font-size: 18px;
            letter-spacing: 1px;
            font-family: $text_family;
            color: $black;
            font-weight: 400;
            text-transform: lowercase;
          }
        }
      }

      .room_list_wrapper .buttons_container_guests .save_guest_button {
        background: $corporate_1 !important;
      }

      .wrapper_booking_button {
        background: white;
        height: 85px;
        width: 425px;

        .promocode_wrapper {
          height: 100%;
          width: calc(50% - 65px);

          .promocode_label {
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
            -moz-transform: translate(-50%, -50%);
            -ms-transform: translate(-50%, -50%);
            -o-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
            color: $black;
            font-weight: normal;
            line-height: 9px;
            letter-spacing: 1px;
            opacity: 0;

            strong {
              font-weight: normal;
              position: relative;
              color: transparent;

              &:after {
                content: 'PROMOCODE?';
                position: absolute;
                left: 50%;
                -webkit-transform: translate(-50%, 0%);
                -moz-transform: translate(-50%, 0%);
                -ms-transform: translate(-50%, 0%);
                -o-transform: translate(-50%, 0%);
                transform: translate(-50%, 0%);
                color: $black;
              }
            }
          }

          .promocode_input {
            color: $black !important;
            font-weight: 300;
            text-transform: uppercase;
            width:100%;

            &::-webkit-input-placeholder {
              color: $black;
              font-size: 10px;
              letter-spacing: 1px;
            }

            &::-moz-placeholder {
              color: $black;
              font-size: 10px;
              letter-spacing: 1px;
            }

            &:-ms-input-placeholder {
              color: $black;
              font-size: 10px;
              letter-spacing: 1px;
            }

            &:-moz-placeholder {
              color: $black;
              font-size: 10px;
              letter-spacing: 1px;
            }
          }
        }

        .submit_button {
          font-family: $title_family;
          text-transform: capitalize;
          position: relative;
          font-weight: normal !important;
          font-size: 14px;
          letter-spacing: 1px;
          height: 80%;
          text-align: center;
          padding: 0 40px 0 70px;
          width: calc(45% + 70px);
          margin: 7px;
          float: right;
          background-color: $corporate_1;
          @include transition(all, .6s);
          @extend .fa-undo;

          &:before {
            @extend .fa;
            position: absolute;
            top: 50%;
            font-weight: 300;
            -webkit-transform: translateY(-50%);
            -moz-transform: translateY(-50%);
            -ms-transform: translateY(-50%);
            -o-transform: translateY(-50%);
            transform: translateY(-50%);
            left: 20px;
            font-size: 30px;
            color: white;
          }
        }

        .spinner_wrapper {
          width: calc(55% - 2px);
          height: 100%;
          background: $corporate_1;
        }

      }
    }

    .booking_footer_message {
      position: absolute;
      left: 0;
      bottom: -25px;
    }

    &.has_babies {
      .submit_button {
        width: 52% !important;
      }
       .guest_selector {
        width: 307px !important;
      }
    }
  }

  .datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
    .specific_month_selector, .go_back_button {
      background: $corporate_2 !important;
    }
  }

  #wizard {
    ul.actual_wizard_step {
      padding-top: 10px;

      .wizard-tab--small {
        display: grid;
        position: relative;
        margin-top: 7px;

        &:last-of-type {
          &:after {
            display: none;
          }
        }

        a, a.disable {
          background-color: transparent;
          border: none;
          font-weight: normal;
          font-size: 10px;
          padding-top: 3px;
          letter-spacing: 1px;

          &:after, &:before {
            display: none;
          }
        }

        a {
          color: $black;
        }

        a.disable {
          color: lightgray;
          margin-top: 5px;
        }

        &:before {
          content: "";
          color: white;
          z-index: 1;
          text-align: center;
          border: 1px solid lightgray;
          justify-self: center;
          width: 5px;
          height: 5px;
          padding: 5px;
          border-radius: 50%;
          font-weight: 300;
          margin: 5px auto;
        }

        &:after {
          content: "";
          width: 267px;
          border: 1px solid lightgray;
          position: absolute;
          left: 150px;
          top: 14px;
        }
      }
    }

    &.step_0 {
      ul.actual_wizard_step {
        .wizard-tab--small:nth-of-type(1) {
          margin-top: auto;

          &:before {
            content: "\f236";
            border: none;
            height: auto;
            font-family: "Font Awesome 5 Pro";
            font-size: 16px;
            font-weight: 300;
            background: $corporate_2;
            width: 20px;
          }

          &:after {
            top: 20px;
          }
        }
      }
    }

    &.step_1 {
      ul.actual_wizard_step {
        .wizard-tab--small:nth-of-type(2) {
          margin-top: auto;

          &:before {
            content: "\f4fc";
            border: none;
            height: auto;
            font-family: "Font Awesome 5 Pro";
            font-size: 16px;
            background: $black;
            width: 20px;
          }

          &:after {
            top: 20px;
          }
        }
      }
    }

    &.step_2 {
      ul.actual_wizard_step {
        .wizard-tab--small:nth-of-type(3) {
          margin-top: auto;

          &:before {
            content: "\f4fc";
            border: none;
            height: auto;
            font-family: "Font Awesome 5 Pro";
            font-size: 16px;
            background: $black;
            width: 20px;
          }

          &:after {
            top: 20px;
          }
        }
      }
    }

    &.step_3 {
      ul.actual_wizard_step {
        .wizard-tab--small:nth-of-type(4) {
          margin-top: auto;

          &:before {
            content: "\f518";
            border: none;
            height: auto;
            font-family: "Font Awesome 5 Pro";
            font-size: 16px;
            background: $black;
            width: 20px;
            padding: 10px 5px;
            margin-bottom: 0;
          }

          &:after {
            top: 20px;
          }
        }
      }
    }
  }

  .rooms_packages_selectors_wrapper {
    border-bottom: none;

    .button_package_room.rooms_selector, .button_package_room.packages_selector {
      color: white;
      font-family: $font_2;
      opacity: 1;
      padding: 15px 60px;
      margin-right: 20px;
      font-size: 16px;
      line-height: 16px;
      height: auto;
      letter-spacing: 2px;
      box-shadow: 3px -5px 6px 0px rgba(0, 0, 0, 0.2);
      background: $corporate_1;

      &.active {
        background: white;
        color: $corporate_1;
      }

    }
  }

  div#step-3 .booking-button--confirm-booking {
    font-family: $font_1;
    background-color: $corporate_1;
  }

  div#step-1, div#step-2 {
    background-color: white;

    .booking-box--search {
      margin-top: 0 !important;
      border: solid 1px $corporate_2;

      .booking-box__content {
        width: 85% !important;
        border: none;
        padding: 0 0 0 20px !important;
        position: relative;

        .booking-search-results__search-data {
          color: black;
          padding-left: 30px;
          font-size: 14px;
          padding-right: 30px;
          white-space: nowrap;
          max-width: 90px;

          .booking-title-info.booking-hotel-name {
            text-transform: none;
            font-size: 12px;
            font-family: $title_family;
            font-weight: 400;
            letter-spacing: 1px;
            color: $corporate_1;
          }

          .booking-3-info {
            color: $black;
            margin-bottom: 10px;
            //             margin-top: 20px;
            font-size: 12px !important;
            text-transform: none;
            display: inline-block;
            padding: 0 3px;
            font-family: $title_family;
            font-weight: 400 !important;
            letter-spacing: 1px;
            color: $corporate_1;
          }

          .booking-title-info, i {
            color: $corporate_2;
            font-size: 12px;
            letter-spacing: 1px;

            &.fa-long-arrow-left,
            &.fa-long-arrow-right {
              margin-left: -5px;
              margin-right: 5px;
              font-size: 14px;
              @extend .icon-longarrow;

              &:before {
                display: inline-block;
                font-family: "icomoon", sans-serif;
                color: rgba($corporate_2, 1);
              }
            }

            &.fa-long-arrow-left {
              &:before {
                -webkit-transform: rotate(180deg);
                -moz-transform: rotate(180deg);
                -ms-transform: rotate(180deg);
                -o-transform: rotate(180deg);
                transform: rotate(180deg);
              }
            }
          }

          .booking-title-info,
          .notranslate {
            font-family: $text_family;
            font-size: 12px;
            font-weight: 300;
            letter-spacing: 1px;
            color: $black
          }

          .notranslate {
            > span {
              display: inline-block;
              vertical-align: middle;
              padding: 0 2px;
            }
          }
        }

        .booking-search-results__rooms-list {
          color: black;
          max-width: 280px;
          font-size: 12px;
          white-space: nowrap;
          padding-top: 50px;
          padding-left: 60px !important;
          @include center_y;
          top: 55%;
          transform: translate(-55%, -75%);


          .booking-title-info {
            font-family: $text_family;
            font-size: 12px;
            font-weight: 300;
            letter-spacing: 1px;
            color: $black;

            &:before {
              content: "\e9e4";
              display: inline-block;
              vertical-align: middle;
              margin-right: 5px;
              font-family: "icomoon", sans-serif;
              font-size: 14px;
              color: rgba($corporate_2, 1);
            }
          }

          .search-item {
            .booking-title-info {
              &:before {
                content: "\e9f8";
              }
            }
          }

          i {
            display: none;
          }

          b {
            font-weight: 400;
          }
        }

        .booking-search-results__new-search {
          .booking-button {
            font-family: $title_family;
            text-transform: none !important;
            position: relative;
            background-color: $corporate_2;
            text-transform: uppercase;
            max-width: 210px;
            height: 85px;
            margin: 10px 0 0 0;
            border-radius: 0 !important;
            padding: 20px 20px 20px 70px !important;
            letter-spacing: 1px;
            font-size: 12px;
            line-height: 20px;
            font-weight: 700;
            -webkit-transition: all 0.6s;
            -moz-transition: all 0.6s;
            -ms-transition: all 0.6s;
            -o-transition: all 0.6s;
            transition: all 0.6s;
            z-index: 1;
            @extend .icon-specialcalendar;

            &:hover {
              background: $corporate_1;
            }

            &:before {
              @extend .fa;
              font-weight: 300;
              font-family: "icomoon", sans-serif;
              position: absolute;
              top: 50%;
              left: 10px;
              -webkit-transform: translate(0%, -50%);
              -moz-transform: translate(0%, -50%);
              -ms-transform: translate(0%, -50%);
              -o-transform: translate(0%, -50%);
              transform: translate(0%, -50%);
              color: white;
              font-size: 35px;
              padding: 10px;
              border-radius: 50%;
              z-index: 1;
            }
          }
        }

        #currencyDiv {
          margin: 0 auto 10px !important;
          width: 188px;
        }
      }

      .call_center_wrapper {
        width: 15%;
        border: none;

        .web_support_label_1 {

          .web_support_wrapper {
            text-align: center;
            font-size: 11px;
            font-weight: 600;
            color: $corporate_2;
            font-family: $text_family;

            strong, span {
              font-weight: 600;
            }
          }

          &::before {
            display: none;
          }
        }
      }
    }


    .contTipoHabitacion {
      box-shadow: $shadow;
      padding: 0 0 10px;
      margin-bottom: 40px;

      &:first-of-type {
        padding: 10px 0;
      }

      .contFotoDescripcion {
        padding: 0;

        .contFotoHabitacion {
          .see_more_rooms_v2 {
            i {
              position: absolute;
              left: 10px;
              top: 10px;
              font-size: 30px;
            }
          }
        }

        .contDescHabitacion {
          width: 810px;

          .room_description_name_wrapper {
            .cabeceraNombreHabitacion {
              padding-top: 20px;

              .tipoHabitacion {
                font-family: $title_family;
                color: $corporate_1;
                text-decoration: none;
                font-size: 18px;
                line-height: 20px;
              }

              .just_booking_message {
                background-color: #ea6d64;
              }

              .very_asked_message {
                background-color: $corporate_4;

                &::before {
                  border-left-color: white !important;
                }

                &::after {
                  border-left-color: $corporate_4 !important;
                }
              }
            }

            .descripcionHabitacion {
              font-family: $text_family;
              font-weight: 300;
              font-size: 12px;
              color: $black;
            }
          }

          .see_more_rooms_v2 {
            .see_more {
              text-transform: uppercase;
              font-weight: 700;
              font-family: $text_family;
              text-decoration: none;
            }
          }

          .room_services {
            border-top: 1px solid $lightgrey;
            border-bottom: 1px solid $lightgrey;

            .service_element {
              border-right: 1px solid $lightgrey;
            }
          }
        }
      }
    }

    .preciosHabitacion {
      margin-top: 0;

      .listadoHabsTarifas {
        font-family: $text_family;

        .rate_tr_element {
          .filaTipoTarifa {
            .contTitTipoTarifa {
              background-color: $lightgrey;
              font-family: $text_family;
              font-weight: 600;
              text-transform: none;

              .titTipoTarifa {
                color: $corporate_2;
                font-weight: 500;
              }

              .cheapest_rate_message {
                background-color: $corporate_3;

                .rate_conditions_link {
                  color: $corporate_2;
                  font-size: 9px;
                  color: white !important;
                  text-decoration: none !important;
                  font-weight: 300;
                }

                &::before {
                  border-left-color: $lightgrey !important;
                }

                .before_block {
                  border-left: 19px solid $corporate_2;
                }
              }

              .advice_rate_message {
                background-color: $corporate_2;

                &::before {
                  border-left-color: $lightgrey !important;
                }
              }
            }

            .conditions_info_wrapper {
              .rate_conditions_link {
                font-weight: 300;
                letter-spacing: 1px;
                background-image: none;
              }

              .last_day_cancellation_text {
                background-image: none;
                font-style: normal !important;
                position: relative;

                &::before {
                  position: absolute;
                  font-family: 'Font Awesome 5 Pro';
                  content: '\f00c';
                  top: 50%;
                  left: 0;
                  transform: translateY(-50%);
                  color: $corporate_4;
                  font-size: 15px;
                  font-weight: 700;
                }
              }
            }
          }
        }

        .regimen_tr_element {
          border-bottom: 1px solid $lightgrey;

          .regimenColumn {
            text-transform: none;

            .regimen_name_wrapper {
              text-transform: capitalize !important;
            }

            .tTextoOferta {
              color: $corporate_4 !important;
              font-weight: 600;
            }
          }

          .precioNocheColumn,
          .precioTotalColumn {
            .priceValues {
              text-align: right;

              .precioTachadoDiv {
                color: $corporate_4;

                .tPrecioTachado {
                  color: $corporate_4;
                }
              }

              .precioGeneralDiv {
                margin-bottom: 8px;

                .precioGeneral {
                  font-weight: 600 !important;
                  color: $black !important;
                }
              }

              .priceTitle {
                font-weight: 300;
              }

              .promotion_percentage_square {
                background-color: $corporate_4;
                border-radius: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: -50px;

                .promotion_discount {
                  padding: 5px 10px;
                  text-align: center;
                  z-index: 1;
                  background: $corporate_4;
                }
              }
            }
          }

          .botonReservarColumn {
            button {
              background-color: $corporate_1;
              padding: 15px 65px 15px 40px;
              text-transform: capitalize;
              font-family: "Libre Baskerville", serif;
              font-size: 20px;
              letter-spacing: 2px;
              position: relative;

              &:lang(en) {
                padding: 15px 55px 15px 30px;
                min-width: 225px;
              }

              &::before {
                position: absolute;
                font-family: 'Font Awesome 5 Pro';
                content: '\f061';
                color: white;
                font-weight: 300;
                font-size: 12px;
                top: 50%;
                right: 30px;
                transform: translateY(-50%);
              }
            }
          }
        }
      }
    }
  }

  .package_element_wrapper {
    font-family: $font_2;

    .package_room_pictures_selector {
      select {
        text-overflow: ellipsis;
      }

      .picture_selector {
        background: $corporate_1;
      }
    }

    .package_prices_wrapper .perform_package_booking_search {
      background: $corporate_1;
      text-transform: capitalize;
    }
  }

  div#step-2 {
    .booking_button_wrapper .booking-button {
      background: $corporate_1;
      font-family: $font_1;
    }

    .booking-2-service-container {
      .booking-2-service-title {
        font-family: $font_1;
        color: $corporate_1;
        font-size: 18px;
        line-height: 20px;
      }

      .booking-2-service-price, select, .booking-2-service-description {
        font-family: $font_2;
      }

    }

    .booking-2-services-list .add_service_element {
      background-color: $corporate_1;
      font-family: $font_1;
      font-size: 16px;
      letter-spacing: 2px;
      text-transform: capitalize;
    }

    .booking-box.booking-box--search.has_currency_selector {
      .booking-search-results.booking-box__content.has_web_support {
        padding: 10px 0 0 20px !important;

        .booking-search-results__new-search {
          .total_price_label {
            margin-bottom: 0 !important;
            max-width: 192px;
            margin-top: -10px !important;
          }

          .booking-button.booking-button--action {
            margin-top: 0;
          }
        }
      }
    }

    .hidden_booking_summary.showed {
      padding: 20px 0;

      .center_container .booking-search-results.booking-box__content.has_web_support {
        .booking-search-results__rooms-list {
          @include center_xy;
          top: 55%;
          transform: translate(-45%, -75%);
        }

        .booking-search-results__new-search {
          top: 50% !important;

          .total_price_label {
            display: block;
            margin-bottom: 0 !important;
          }

          .booking-button.booking-button--action {
            margin: 0px 10px 0 0 !important;
          }
        }
      }
    }
  }

  .hidden_booking_summary {
    border-bottom: none;
    box-shadow: 0px 10px 25px -15px rgba(0, 0, 0, 0.30);

    .booking-box__content {
      width: 85% !important;
      border: none;
      padding: 0 0 0 20px !important;
      position: relative;

      .booking-search-results__search-data {
        color: black;
        padding-left: 30px;
        font-size: 14px;
        padding-right: 30px;
        white-space: nowrap;
        max-width: 90px;

        .booking-title-info.booking-hotel-name {
          text-transform: none;
          font-size: 12px;
          font-family: $title_family;
          font-weight: 400;
          letter-spacing: 1px;
          color: $corporate_1;
        }

        .booking-3-info {
          color: $black;
          margin-bottom: 10px;
          //           margin-top: 20px;
          font-size: 12px !important;
          text-transform: none;
          display: inline-block;
          padding: 0 3px;
          font-family: $title_family;
          font-weight: 400 !important;
          letter-spacing: 1px;
          color: $corporate_1;
        }

        .booking-title-info, i {
          color: $corporate_2;
          font-size: 12px;
          letter-spacing: 1px;

          &.fa-long-arrow-left,
          &.fa-long-arrow-right {
            margin-left: -5px;
            margin-right: 5px;
            font-size: 14px;
            @extend .icon-longarrow;

            &:before {
              display: inline-block;
              font-family: "icomoon", sans-serif;
              color: rgba($corporate_2, 1);
            }
          }

          &.fa-long-arrow-left {
            &:before {
              -webkit-transform: rotate(180deg);
              -moz-transform: rotate(180deg);
              -ms-transform: rotate(180deg);
              -o-transform: rotate(180deg);
              transform: rotate(180deg);
            }
          }
        }

        //         !*.booking-title-info,
        //         .notranslate {
        //           font-family: $text_family;
        //           font-size: 12px;
        //           font-weight: 300;
        //           color: $black;
        //         }*!
        .booking-title-info,
        .notranslate {
          font-family: $text_family;
          font-size: 12px;
          font-weight: 300;
          color: $black;
        }

        .notranslate {
          letter-spacing: 1px;
          font-size: 12px;

          > span {
            display: inline-block;
            vertical-align: middle;
            padding: 0 2px;
          }
        }
      }

      .booking-search-results__rooms-list {
        color: black;
        max-width: 280px;
        font-size: 12px;
        white-space: nowrap;
        padding-top: 50px;
        padding-left: 60px !important;
        @include center_xy;
        top: 55%;
        transform: translate(-45%, -75%);

        .booking-title-info {
          color: $black;
          letter-spacing: 1px;
          font-weight: 400;

          &:before {
            content: "\e9e4";
            display: inline-block;
            vertical-align: middle;
            margin-right: 5px;
            font-family: "icomoon", sans-serif;
            font-size: 14px;
            color: rgba($corporate_2, 1);
          }
        }

        .search-item {
          .booking-title-info {
            &:before {
              content: "\e9f8";
            }
          }
        }

        i {
          display: none;
        }

        b {
          font-weight: 400;
        }
      }

      .booking-search-results__new-search {
        .booking-button {
          font-family: $title_family;
          text-transform: none !important;
          position: relative;
          background-color: $corporate_2 !important;
          text-transform: uppercase;
          max-width: 210px;
          height: 85px;
          margin: 10px 10px 0 0;
          border-radius: 0 !important;
          padding: 20px 20px 20px 70px !important;
          letter-spacing: 1px;
          font-size: 12px;
          line-height: 20px;
          font-weight: 700;
          -webkit-transition: all 0.6s;
          -moz-transition: all 0.6s;
          -ms-transition: all 0.6s;
          -o-transition: all 0.6s;
          transition: all 0.6s;
          z-index: 1;
          @extend .icon-specialcalendar;

          &:hover {
            background: $corporate_1;
          }

          &:before {
            @extend .fa;
            font-weight: 300;
            font-family: "icomoon", sans-serif;
            position: absolute;
            top: 50%;
            left: 10px;
            -webkit-transform: translate(0%, -50%);
            -moz-transform: translate(0%, -50%);
            -ms-transform: translate(0%, -50%);
            -o-transform: translate(0%, -50%);
            transform: translate(0%, -50%);
            color: white;
            font-size: 35px;
            padding: 10px;
            border-radius: 50%;
            z-index: 1;
          }
        }
      }

      #currencyDiv {
        display: none;
      }
    }

    .call_center_wrapper {
      width: 15%;
      border: none;

      .web_support_label_1 {

        .web_support_wrapper {
          text-align: center;
          font-size: 11px;
          font-weight: 600;
          color: $corporate_2;
          font-family: $text_family;

          strong, span {
            font-weight: 600;
          }
        }

        &::before {
          display: none;
        }
      }
    }
  }


}

