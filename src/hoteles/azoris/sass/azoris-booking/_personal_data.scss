div#step-3 {
  #personal-details-form {
    .booking_details_prices_wrapper {
      .booking-button {
        border-radius: 0;
        transition: all .6s;

        &:hover {
          background: #00B2C5;
        }

        &::before {
          display: none;
        }
      }

      .booking_details_wrapper .total_booking_wrapper {
        height: 70px;
        padding-top: 10px;

        i {
          margin-left: 3px;
          position: relative;
          top: 3px;
          border-radius: 50%;
          font-family: $font_awesome;

          &:before {
            content: '\f646';
            position: relative;
            font-size: 25px;
            color: #008AAB;
            z-index: 3;
            //background: radial-gradient(white 50%, white 50%, transparent 50%);
          }
        }
      }
    }
  }

  .booking-form-field {
    display: table;
    width: 100%;

    #phone_prefix {
      display: inline;
      width: 34%;
      padding: 7px 12px;
      height: auto;
      color: transparent;
    }

    input[name="telephone"] {
      float: right;
      width: 65%;
    }

    p {
      margin-bottom: 0;
    }

  }
}
