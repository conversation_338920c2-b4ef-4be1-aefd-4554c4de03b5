#wizard {
  background: transparent;

    .clearfix .booking-box.booking-box--search {
      .booking-search-results {
        border: 0;
        border-radius: 0;
        background-color: white;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
        background-image: none;
        width: 100% !important;

        .title_booking_breakdown {
          color: $corporate_1;
          font-size: 16px;
          position: relative;
          padding: 35px 30px 35px 10px;
          line-height: 24px;
          margin-right: 30px;

          &:before {
            @include center_y;
            right: 15px;
            content: '\f084';
            font-family: "Fontawesome";
            font-size: 20px;
            color: $corporate_1;
          }

          &:after {
            content: '';
            @include center_y;
            height: 60px;
            width: 1px;
            background-color: rgba(black, .4);
            right: 0;
          }
        }

        .fa {
          color: $corporate_1;
        }

        .booking-search-results__search-data {
          > i {
            margin-right: 0;
            margin-left: 20px;
          }

          .booking-3-info {
            color: $corporate_1;
            display: inline-block;
            font-family: $text_family;
            margin-left: 0;
            text-transform: none;
            font-size: 15px;
            letter-spacing: 1px;
            margin-bottom: 10px;
            padding-left: 10px;
          }

          .booking-title-info, span.notranslate {
            color: $black;
            font-weight: 700;
          }
          .booking-title-info {
            margin-left: 10px;
          }
        }

        .booking-search-results__rooms-list {
          font-size: 14px;
          position: relative;
          display: inline-block;
          color: #F49D37;
          top: 10px;
          right: 310px;
          width: 370px;
          max-width: 370px;
          left: -120px;
          color: $black;
          margin-top: 14px;
          font-weight: 700;
          margin-left: 90px;
          .fa-users {
            display: none;
          }
          .booking-title-info {
            color: $black;
            font-weight: 400;
            @extend .icon-specialbed;
            &:before {
              font-size: 18px;
              font-family: "icomoon", sans-serif;
              margin-right: 10px;
              color: $corporate_1;
              display: inline-block;
              vertical-align: middle;
            }
          }
          .search-item {
            .booking-title-info {
              @extend .icon-specialfamily;
              &:before {
                color: $corporate_1;
                display: inline-block;
                vertical-align: middle;
              }
            }
          }
        }

        .booking-search-results__new-search {
          position: absolute;
          top: 0;
          bottom: 0;
          right: 0;
          border-radius: 0;
          overflow: hidden;
          width: 280px;
          height: 120px;

          &:before {
            color: white !important;
            content: "\e9fc";
            font-family: "icomoon", sans-serif;
            font-size: 26px;
          }

          .booking-button.booking-button--action {
            padding-left: 70px;
            font-size: 14px;
            background-color: $corporate_1;
            font-family: $text_family;
          }
        }
        #currencyDiv {
          border-radius: 0;
          transform: translate(18px, -1px)!important;
          width: 256px;
          text-align: center;
        }
      }
      .call_center_wrapper {
        position: fixed;
        width: auto;
        bottom: auto;
        top: 45%;
        right: 0;
        border: none;
        z-index: 1100;
        &:before {
          content: '\e90b';
          font-family: "icomoon" !important;
          display: inline-block;
          box-sizing: border-box;
          vertical-align: middle;
          font-size: 46px;
          color: white;
          background-color: $corporate_1;
          padding: 16px 3px;
          width: 50px;
          height: 50px;
          border-top-left-radius: 5px;
          border-bottom-left-radius: 5px;
        }
        .wrapper-new-web-support {
          box-sizing: border-box;
          display: inline-block;
          vertical-align: middle;
          height: 50px;
          max-width: 0;
          width: 200px;
          overflow: hidden;
          background-color: black;
          margin: 0 !important;
          padding: 10px 0;
          @include transition(all, .6s);
          .web_support_label_1 {
            position: relative;
            top: auto;
            left: auto;
            right: auto;
            transform: none;
            width: auto;
            color: white;
            &:before {
              display: none;
            }
            .web_support_wrapper {
              width: 200px;
              font-size: 10px;
              strong {
                font-weight: 400;
              }
              .web_support_number {
                font-size: 13px !important;
              }
            }
          }
        }
        &:hover {
          .wrapper-new-web-support {
            max-width: 300px;
            padding: 10px;
          }
        }
      }
    }

    .hidden_booking_summary {
      z-index: 110;
      .booking-search-results {
        border: 0;
        border-radius: 0;
        background-color: #f4f4f4;
        background-image: none;
        width: 100% !important;

        .title_booking_breakdown {
          color: $corporate_1;
          font-size: 16px;
          position: relative;
          padding: 35px 50px 35px 10px;
          line-height: 24px;
          margin-right: 30px;

          &:before {
            @include center_y;
            right: 15px;
            content: '\f084';
            font-family: "Fontawesome";
            font-size: 20px;
            color: $corporate_1;
          }

          &:after {
            content: '';
            @include center_y;
            height: 60px;
            width: 1px;
            background-color: rgba(black, .4);
            right: 0;
          }
        }

        .fa {
          color: $corporate_1;
        }

        .booking-search-results__search-data {
          > i {
            margin-right: 0;
            margin-left: 30px;
          }

          .booking-3-info {
            color: $corporate_1;
            display: inline-block;
            font-family: $text_family;
            margin-left: 0;
            text-transform: none;
            font-size: 15px;
            letter-spacing: 1px;
            margin-bottom: 10px;
            padding-left: 10px;
          }

          .booking-title-info, span.notranslate {
            color: $black;
            font-weight: 700;
          }
          .booking-title-info {
            margin-left: 10px;
          }
        }

        .booking-search-results__rooms-list {
          color: $black;
          top: 8px;
          right: 44px;
          font-weight: 700;
          max-width: none;
          margin-left: 90px;

          .fa-users {
            display: none;
          }
          .booking-title-info {
            color: $black;
            font-weight: 400;
            @extend .icon-specialbed;
            &:before {
              font-size: 18px;
              font-family: "icomoon", sans-serif;
              margin-right: 10px;
              color: $corporate_1;
              display: inline-block;
              vertical-align: middle;
            }
          }
          .search-item {
            .booking-title-info {
              @extend .icon-specialfamily;
              &:before {
                color: $corporate_1;
                display: inline-block;
                vertical-align: middle;
              }
            }
          }
        }

        .booking-search-results__new-search {
          height: 70px;
          top: 50%;
          right: 7px;
          -webkit-transform: translateY(-50%);
          -moz-transform: translateY(-50%);
          -ms-transform: translateY(-50%);
          -o-transform: translateY(-50%);
          transform: translateY(-50%);
          border-radius: 0;
          overflow: hidden;
          width: 280px;

          &:before {
            content: "\e9fc";
            font-family: "icomoon", sans-serif;
            position: absolute;
            top: 50%;
            -webkit-transform: translate(0%, -50%);
            -moz-transform: translate(0%, -50%);
            -ms-transform: translate(0%, -50%);
            -o-transform: translate(0%, -50%);
            transform: translate(0%, -50%);
            left: 25px;
            color: white;
            font-size: 26px;
            z-index: 10;
          }

          .booking-button.booking-button--action {
            padding-left: 70px;
            font-size: 14px;
            background-color: $corporate_1;
            font-family: $text_family;
            display: inline-block;
            height: 100%;
            position: relative;
            color: white;
            letter-spacing: 3px;
            text-transform: uppercase;
            border-radius: 0;
            overflow: hidden;
            z-index: 2;
            line-height: 20px;
            font-weight: 400;
          }
        }
      }
      .call_center_wrapper {
        display: none;
      }
    }
}