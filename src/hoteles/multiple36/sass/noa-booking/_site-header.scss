.site-header {
  padding: 0;
  .site-header__logo {
    width: 300px;
    height: 100px;
    position: relative;
    padding: 0;

    a {
      @include center_y;
      display: block;
      width: 100%;
      height: auto;

      img {
        @include contain_image;
        object-position: left;
        max-height: 75px;
      }
    }
  }

  .header_right_block {
    .site-header__ticks {
      margin-top: 0;
      padding: 15px 50px 15px 0;

      .site-header__tick-item {
        margin: 0 !important;

        .icon-checkmark {
          &::before {
            font-family: "Font Awesome 5 Pro" !important;
            font-weight: 300;
            font-size: 22px !important;
            line-height: 52px;
            color: $grey-2 !important;
          }
        }

        p {
          font-family: $text_family;
          font-weight: 400;
          letter-spacing: 2.15px;
          top: 0 !important;
        }
      }

      .site-header__tick-item:nth-of-type(1) {
        .icon-checkmark {
          &::before {
            content: "\f274" !important;
          }
        }
      }
      .site-header__tick-item:nth-of-type(2) {
        .icon-checkmark {
          &::before {
            content: "\f132" !important;
          }
        }
      }
      .site-header__tick-item:nth-of-type(3) {
        .icon-checkmark {
          &::before {
            content: "\f4d3" !important;
          }
        }
      }
      .site-header__tick-item:nth-of-type(4) {
        .icon-checkmark {
          &::before {
            content: "\f0ac" !important;
          }
        }
      }
    }

    .language_header_selector_booking_process {
      padding: 5px 20px 5px 0;
      @include center_y;
      bottom: auto;
      z-index: 56;

      &::before {
        content: "\f107";
        font-weight: 300;
        font-size: 25px;
        top: 40%;
        z-index: -1;
      }

      .selected_language {
        position: static;
        @include transform(none);
        padding: 0;
        padding-right: 25px;

        i {
          font-family: "Font Awesome 5 Pro";
          font-weight: 300;
          font-size: 18px;
          margin-right: 5px;
          vertical-align: middle;
        }

        .selected_language_code {
          font-family: $text_family;
          font-weight: 400;
          font-size: 16px;
          letter-spacing: 0.65px;
          text-transform: uppercase;
        }
      }
    }
  }
}