$fontawesome5: true;
@import url('https://fonts.googleapis.com/css?family=Nunito:300,400,700&display=swap');
@import url('https://fonts.googleapis.com/css?family=Nunito:300,400,700&display=swap');
@import "plugins/mixins";
@import "plugins/fonts";
@import "plugins/fontawesome5pro";

$text_family: "Nunito", sans-serif;
$title_family: "Golden Plains", sans-serif;
$corporate_1: #776A57;
$corporate_2: #7FC9E1;
$corporate_3: #0999C3;
$orange: #F49D37;
$black: #333;
$grey: #F4F4F4;




@mixin btn_styles() {
  position: relative;
  z-index: 10;
  display: inline-block;
  overflow: hidden;
  background: linear-gradient(to right, $corporate_2, $corporate_3);
  padding: 15px 30px;
  text-transform: uppercase;
  color: white;
  font-size: 14px;
  text-align: center;
  font-family: $text_family;
  font-weight: 300;
  border: none;
  cursor: pointer;
  @include transition(all, .6s);
  &:before {
    content: '';
    @include full_size;
    z-index: -1;
    background: linear-gradient(to right, $corporate_2, $corporate_3);
    max-width: 0;
    visibility: hidden;
    @include transition(all, .6s);
  }
  &:hover {
    &:before {
      max-width: 300px;
      visibility: visible;
    }
  }
}

body.sand-beach {
  font-family: $text_family;

  @include booking_header_v2(white, $corporate_1, $corporate_3, $grey, $corporate_3);

  &.shopping_cart_process_v2 {
    @import "shopping_cart_styles";
  }

  .fancybox-wrap.fancy-booking-search_v2 {
    .container_popup_booking {
      .dots_loader .dot {
        background: $corporate_1;
      }
    }
  }

  .site-header {
    .site-header__logo {
      margin: 12px 0;
      img {
        max-height: 80px;
      }
    }
    .site-header__ticks {
      margin-top: 20px;
      margin-right: 50px;

      .site-header__tick-item {
        margin-top: 5px;
        float: none;
        display: inline-block;
        vertical-align: middle;
        width: 150px;
        margin-right: 0;
        &:nth-of-type(3) {
          margin-right: 30px;
        }
        p {
          width: 80px;
          font-size: 10px;
          line-height: 14px;
          font-family: $text_family;
          text-transform: uppercase;
          font-weight: 500;
          letter-spacing: 2px;
          color: rgba($corporate_1, .4) !important;
        }
        .icon-checkmark {
          font-size: 50px;
          margin-top: -10px;
          display: inline-block;

          &:before {
            font-size: 42px !important;
            color: rgba($corporate_1, .4) !important;
          }
        }
      }
    }
    .language_header_selector_booking_process {
      border: none;
      width: 150px;
      text-transform: uppercase;
      .selected_language {
        font-size: 16px;
        font-weight: 500;
        letter-spacing: 2px;
        text-align: left;
        .selected_language_code {
          color: $corporate_1;
        }
        i {
          font-size: 20px;
          color: $corporate_1;
        }
      }
      &:before {
        color: $corporate_1;
        position: absolute;
        @include center_y;
        right: 20px;
        font-size: 20px;
      }
      .language_booking_selector_wrapper.active {
        max-height: 300px;
      }
    }
  }

  #full_wrapper_booking.booking_engine_wrapper_process {
    background-color: $grey;
    padding: 25px 0;
    box-shadow: 0 4px 10px -2px grey;
    #booking.boking_widget_inline {
      .booking_form.has_nights {
        .nights_number_wrapper_personalized {
          margin: 0;
          height: 70px;
          position: relative;
          width: 100px;
          padding: 7px 15px;

          &:before, &:after {
            content: '';
            width: 2px;
            height: auto;
            background-color: $grey;
            position: absolute;
            top: 10px;
            bottom: 10px;
          }

          &:before {
            left: 0;
          }

          &:after {
            right: 0;
          }

          .nights_label {
            display: none;
          }

          .days_number_datepicker {
            @include center_y;
            left: 15px;
            font-family: $text_family;
            font-weight: 400;
          }

          .night_label {
            @include center_y;
            right: 15px;
            color: $corporate_1;
            font-size: 24px;
          }
        }

        .guest_selector {
          width: 270px;

          &:before {
            display: none;
          }
        }

        .room_list_wrapper {
          left: 425px;
        }

        .wrapper_booking_button {
          width: 410px !important;

          .promocode_wrapper {
            width: 175px;
          }

          .submit_button, .spinner_wrapper {
            width: 235px;
          }
        }
      }

      .entry_date_wrapper, .departure_date_wrapper {
        border-bottom: none;
        height: 50px;
        padding-top: 5px;
        .entry_date_label, .departure_date_label {
          color: $corporate_1;
          font-size: 13px;
          font-weight: 300;
        }
        .date_day {
          font-family: $text_family;
          font-weight: 400;
          color: $black;
          font-size: 18px !important;
        }
        .departure_date {
          background-image: none;
          &:before {
            content: "\e9a1";
            font-family: 'icomoon' !important;
            color: rgba($corporate_1, .1);
            position: absolute;
            @include center_y;
            right: 20px;
            font-size: 24px;
          }
        }
      }
      .entry_date_wrapper {
        &:before {
          content: '\f105';
          font-family: 'Fontawesome' !important;
          color: $black;
          position: absolute;
          @include center_y;
          right: 10px;
          font-size: 24px;
        }
      }
      .guest_selector {
        position: relative;
        margin-left: 25px;
        height: 50px;
        width: 280px !important;
        padding: 5px 0px;
        label {
          color: $corporate_1;
          font-size: 13px;
          font-weight: 300;
        }
        .placeholder_text {
          position: relative;
          font-family: $text_family;
          font-weight: 400;
          color: $black;
          font-size: 18px !important;
          &:before {
            content: "\e938";
            font-family: 'icomoon' !important;
            color: rgba($corporate_1, .1);
            position: absolute;
            @include center_y;
            top: 10%;
            right: 0;
            font-size: 24px;
          }
        }
      }

      .wrapper_booking_button {
        width: 460px;
        .promocode_wrapper {
          position: relative;
          height: 50px;
          padding-top: 2px;
          background-color: transparent;
          .promocode_label {
            color: $black;
            padding-top: 5px;
            font-size: 12px;
          }
          .promocode_input {
            color: $black;
          }
        }
        .submit_button {
          @include btn_styles;
          padding: 13px 20px;
          height: 50px;
        }
        .spinner_wrapper {
          display: none;
        }
      }
    }
  }

  #wizard {
    background-image: url("/static_1/images/patron-sand-beach.png");
    background-size: contain;
    background-repeat: repeat-y;
    background-repeat-x: repeat;
  }

  .actual_wizard_step {
    .wizard-tab--small {
      .booking-step {
        background-color: $corporate_1;
        &:after {
          border-left-color: $corporate_1;
        }
        &.disable {
          background-color: $grey;
          color: $black;

          &:before, &:after {
            border-left-color: $grey;
          }
        }
      }
    }
  }

  .fancyboxContent_second_v {
    .room_popup_individual_element {
      .popup_room_pictures {
        background-color: $corporate_1 !important;
        border-color: $corporate_1;
      }
      .room_services {
        background-color: $corporate_1 !important;
        border-color: $corporate_1;
        .owl-prev, .owl-next {
          background-color: $corporate_1 !important;
          width: 15px !important;
          i {
            color: white !important;
          }
        }
      }
      .popup_title, .popup_room_description {
        letter-spacing: .5px;
      }
    }
  }

  div#step-1 {
    background: transparent;
    .clearfix .booking-box.booking-box--search {
      .booking-search-results {
        border: 0;
        border-radius: 0;
        background-color: white;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
        background-image: none;
        width: 100% !important;

        .title_booking_breakdown {
          color: $corporate_1;
          font-size: 16px;
          position: relative;
          padding: 35px 30px 35px 10px;
          line-height: 24px;
          margin-right: 30px;

          &:before {
            @include center_y;
            right: 15px;
            content: '\f084';
            font-family: "Fontawesome";
            font-size: 20px;
            color: $corporate_1;
          }

          &:after {
            content: '';
            @include center_y;
            height: 60px;
            width: 1px;
            background-color: rgba(black, .4);
            right: 0;
          }
        }

        .fa {
          color: $corporate_1;
        }

        .booking-search-results__search-data {
          > i {
            margin-right: 0;
            margin-left: 20px;
          }

          .booking-3-info {
            color: $corporate_1;
            display: inline-block;
            font-family: $text_family;
            margin-left: 0;
            text-transform: none;
            font-size: 15px;
            letter-spacing: 1px;
            margin-bottom: 10px;
            padding-left: 10px;
          }

          .booking-title-info, span.notranslate {
            color: $black;
            font-weight: 700;
          }
          .booking-title-info {
            margin-left: 10px;
          }
        }

        .booking-search-results__rooms-list {
          font-size: 14px;
          position: relative;
          display: inline-block;
          color: #F49D37;
          top: 10px;
          right: 310px;
          width: 370px;
          max-width: 370px;
          left: -120px;
          color: $black;
          margin-top: 14px;
          font-weight: 700;
          .fa-users {
            display: none;
          }
          .booking-title-info {
            color: $black;
            font-weight: 400;
            @extend .icon-specialbed;
            &:before {
              font-size: 18px;
              font-family: "icomoon", sans-serif;
              margin-right: 10px;
              color: $corporate_1;
              display: inline-block;
              vertical-align: middle;
            }
          }
          .search-item {
            .booking-title-info {
              @extend .icon-specialfamily;
              &:before {
                color: $corporate_1;
                display: inline-block;
                vertical-align: middle;
              }
            }
          }
        }

        .booking-search-results__new-search {
          position: absolute;
          top: 0;
          bottom: 0;
          right: 0;
          border-radius: 0;
          overflow: hidden;
          width: 280px;
          height: 120px;

          &:before {
            color: white !important;
            content: "\e9fc";
            font-family: "icomoon", sans-serif;
            font-size: 26px;
          }

          .booking-button.booking-button--action {
            padding-left: 70px;
            font-size: 14px;
            background-color: $corporate_1;
            font-family: $text_family;
          }
        }
        #currencyDiv {
          border-radius: 0;
          transform: translate(18px, -1px)!important;
          width: 256px;
          text-align: center;
        }
      }
      .call_center_wrapper {
        position: fixed;
        width: auto;
        bottom: auto;
        top: 45%;
        right: 0;
        border: none;
        z-index: 1100;
        &:before {
          content: '\e90b';
          font-family: "icomoon" !important;
          display: inline-block;
          box-sizing: border-box;
          vertical-align: middle;
          font-size: 46px;
          color: white;
          background-color: $corporate_1;
          padding: 16px 3px;
          width: 50px;
          height: 50px;
          border-top-left-radius: 5px;
          border-bottom-left-radius: 5px;
        }
        .wrapper-new-web-support {
          box-sizing: border-box;
          display: inline-block;
          vertical-align: middle;
          height: 50px;
          max-width: 0;
          width: 200px;
          overflow: hidden;
          background-color: black;
          margin: 0 !important;
          padding: 10px 0;
          @include transition(all, .6s);
          .web_support_label_1 {
            position: relative;
            top: auto;
            left: auto;
            right: auto;
            transform: none;
            width: auto;
            color: white;
            &:before {
              display: none;
            }
            .web_support_wrapper {
              width: 200px;
              font-size: 10px;
              strong {
                font-weight: 400;
              }
              .web_support_number {
                font-size: 13px !important;
              }
            }
          }
        }
        &:hover {
          .wrapper-new-web-support {
            max-width: 300px;
            padding: 10px;
          }
        }
      }
    }

    .hidden_booking_summary {
      z-index: 110;
      .booking-search-results {
        border: 0;
        border-radius: 0;
        background-color: $grey;
        background-image: none;
        width: 100% !important;

        .title_booking_breakdown {
          color: $corporate_1;
          font-size: 16px;
          position: relative;
          padding: 35px 50px 35px 10px;
          line-height: 24px;
          margin-right: 30px;

          &:before {
            @include center_y;
            right: 15px;
            content: '\f084';
            font-family: "Fontawesome";
            font-size: 20px;
            color: $corporate_1;
          }

          &:after {
            content: '';
            @include center_y;
            height: 60px;
            width: 1px;
            background-color: rgba(black, .4);
            right: 0;
          }
        }

        .fa {
          color: $corporate_1;
        }

        .booking-search-results__search-data {
          > i {
            margin-right: 0;
            margin-left: 30px;
          }

          .booking-3-info {
            color: $corporate_1;
            display: inline-block;
            font-family: $text_family;
            margin-left: 0;
            text-transform: none;
            font-size: 15px;
            letter-spacing: 1px;
            margin-bottom: 10px;
            padding-left: 10px;
          }

          .booking-title-info, span.notranslate {
            color: $black;
            font-weight: 700;
          }
          .booking-title-info {
            margin-left: 10px;
          }
        }

        .booking-search-results__rooms-list {
          color: $black;
          top: 8px;
          right: 80px;
          font-weight: 700;
          max-width: none;
          .fa-users {
            display: none;
          }
          .booking-title-info {
            color: $black;
            font-weight: 400;
            @extend .icon-specialbed;
            &:before {
              font-size: 18px;
              font-family: "icomoon", sans-serif;
              margin-right: 10px;
              color: $corporate_1;
              display: inline-block;
              vertical-align: middle;
            }
          }
          .search-item {
            .booking-title-info {
              @extend .icon-specialfamily;
              &:before {
                color: $corporate_1;
                display: inline-block;
                vertical-align: middle;
              }
            }
          }
        }

        .booking-search-results__new-search {
          height: 70px;
          top: 50%;
          right: 7px;
          -webkit-transform: translateY(-50%);
          -moz-transform: translateY(-50%);
          -ms-transform: translateY(-50%);
          -o-transform: translateY(-50%);
          transform: translateY(-50%);
          border-radius: 0;
          overflow: hidden;
          width: 280px;

          &:before {
            content: "\e9fc";
            font-family: "icomoon", sans-serif;
            position: absolute;
            top: 50%;
            -webkit-transform: translate(0%, -50%);
            -moz-transform: translate(0%, -50%);
            -ms-transform: translate(0%, -50%);
            -o-transform: translate(0%, -50%);
            transform: translate(0%, -50%);
            left: 25px;
            color: white;
            font-size: 26px;
            z-index: 10;
          }

          .booking-button.booking-button--action {
            padding-left: 70px;
            font-size: 14px;
            background-color: $corporate_1;
            font-family: $text_family;
            display: inline-block;
            height: 100%;
            position: relative;
            color: white;
            letter-spacing: 3px;
            text-transform: uppercase;
            border-radius: 0;
            overflow: hidden;
            z-index: 2;
            line-height: 20px;
            font-weight: 400;
          }
        }
      }
      .call_center_wrapper {
        display: none;
      }
    }

    .rooms_packages_selectors_wrapper {
      position: relative;
      border: none;
      &:before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 5;
        height: 5px;
        background: linear-gradient(to right, $corporate_1, $corporate_2);
      }
      .button_package_room {
        position: relative;
        padding: 15px 0 15px 230px;
        margin: 0;
        height: auto;
        font-size: 18px;
        width: calc(50% - 10px);
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        &:first-of-type {
          &:before {
            content: '\e9f8';
            font-family: "icomoon" !important;
            @include center_y;
            left: 160px;
            font-size: 22px;
            color: white;
          }
        }
        &:last-of-type {
          float: right;
          margin-left: 10px;
          &:before {
            content: '\ea1f';
            font-family: "icomoon" !important;
            @include center_y;
            left: 120px;
            font-size: 22px;
            color: white;

          }
        }
        &.active {
          height: auto;
          background: linear-gradient(to right, $corporate_1, $corporate_2);
        }
      }
    }
  }

  #rooms_b1_wrapper {
    .hotel_rooms_container {
      .hotel_title_container {
        background: linear-gradient(to right, $corporate_1, $corporate_2);
        position: relative;
        box-sizing: border-box;
        padding: 15px 0 15px 180px;
        margin: 0;
        height: auto;
        width: 560px;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        .hotel_number {
          display: none;
        }
        .hotel_name {
          position: relative;
          font-size: 18px;
          color: white;
          text-transform: uppercase;
          font-weight: 700;
          &:before {
            content: '\f084';
            font-family: "Fontawesome";
            @include center_y;
            left: -40px;
            font-size: 22px;
            color: white;
          }
        }
        &:after {
          background: linear-gradient(to right, $corporate_1, $corporate_2);
        }
      }
      &:first-of-type {
        .hotel_title_container {
          display: none;
        }
      }
    }

    .contTipoHabitacion {
      .contFotoDescripcion {
        .contDescHabitacion {
          .room_description_name_wrapper {
            .cabeceraNombreHabitacion {
              .tipoHabitacion {
                color: $corporate_1;
                font-weight: 300;
                letter-spacing: .5px;
                font-family: $text_family;
                text-transform: none;
              }

              .very_asked_message {
                background-color: $corporate_3;

                &:after {
                  border-left-color: $corporate_3;
                }
              }
            }

            .descripcionHabitacion {
              color: gray;
              font-size: 13px;
              line-height: 18px;
              letter-spacing: .5px;
              font-weight: 300;
            }
          }

          .room_services {
            border-top-style: dashed;
            border-bottom-style: dashed;
            .service_element {
              border-right-style: dashed;
              .service_description {
                font-family: $text_family;
                text-transform: none;
                font-size: 12px;
              }
            }
          }

          .see_more_rooms_v2 {
            .plus_sign {
              color: $black;
              font-size: 16px;
            }
            .see_more {
              color: $black;
              font-size: 16px;
              text-transform: uppercase;
              text-decoration: none;
            }
          }
        }
      }

      .preciosHabitacion {
        .listadoHabsTarifas {
          .rate_tr_element {
            .filaTipoTarifa {
              .contTitTipoTarifa {
                position: relative;
                background-color: $grey;

                .swipe_hand {
                  background-color: $corporate_1;
                  border-radius: 50%;
                  padding: 4px;
                  width: 23px;
                }

                .titTipoTarifa {
                  color: $black;
                }
                .cheapest_rate_message {
                  background: $corporate_1;
                  position: relative;
                  padding: 10px 30px 10px 40px !important;

                  &:before {
                    border-left-color: $grey;
                  }

                  &.has_conditions {
                    padding: 3px 30px 17px 40px !important;
                  }

                  * {
                    color: white !important;
                  }

                  .conditions_info_wrapper {
                    z-index: 2;
                    opacity: 1;
                    right: 0;
                    width: 100%;
                    position: absolute;
                    bottom: 2px;
                    a {
                      font-size: 8px;
                      padding-right: 50px;
                      text-decoration: underline;
                    }
                  }
                  .before_block {
                    border-left-color: #999798;
                  }
                }

                .advice_rate_message {
                  background-color: #999798;
                }

                .advice_rate_message.has_conditions {
                  position: relative;
                  padding: 3px 30px 17px 40px !important;

                  .conditions_info_wrapper {
                    z-index: 2;
                    position: absolute;
                    bottom: 14px;
                    opacity: 1;
                    right: 0;
                    width: 100%;
                    @include center_y;

                    a {
                      font-size: 8px;
                      padding-right: 40px;
                    }
                  }
                }

                .advice_rate_message {
                  &:before {
                    border-left-color: $grey;
                  }
                }
              }
            }
          }

          .regimen_tr_element {
            min-height: 60px;
            .regimenColumn {
              .regimenColumnContent {
                .tTextoOferta {
                  font-size: 12px;
                  font-weight: 600;
                  margin-left: 3px;
                  margin-bottom: 3px;
                  line-height: 19px;
                  span.offer_name_element {
                    color: $orange !important;
                  }
                }
              }
            }
            .custom_board_message {
              width: 5%;

              & + .precioNocheColumn {
                width: 12%;
                padding-left: 10px;
              }
              & + .precioNocheColumn + .precioTotalColumn {
                width: 22%;
                padding-right: 4%;
              }
              & + .noPrecioNocheColumn + .precioTotalColumn {
                width: 40%;
                padding-right: 10px;
              }
            }
            .precioNocheColumn  {
              width: 12%;
              padding-left: 4.5%;
              .precioGeneralDiv {
                span {
                  color: $orange !important;
                }
              }
            }
            .precioTotalColumn {
              color: $black;

              .tPrecioOferta {
                color: $orange !important;
              }
              .promotion_discount {
                background-color: $corporate_1;
                z-index: 12;
              }
            }
            .promotion_percentage_square {
              background-color: $orange !important;
            }
            .botonReservarColumn {
              .booking-button {
                @include btn_styles;
                padding: 5px 30px;
              }
            }
          }
        }
      }
    }
  }

  .booking-title-info.booking-hotel-name {
    display: inline-block !important;
    font-size: 15px !important;
    letter-spacing: 1px;
  }

  div#step-2 {
    background: transparent;
    .clearfix .booking-box.booking-box--search {
      .booking-search-results {
        border: 0;
        border-radius: 0;
        padding: 10px 20px;
        background-color: white;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
        background-image: none;
        width: 100% !important;

        .title_booking_breakdown {
          color: $corporate_1;
          font-size: 16px;
          position: relative;
          padding: 35px 0;
          line-height: 24px;
          margin-right: 30px;

          &:before {
            @include center_y;
            right: 15px;
            content: '\f084';
            font-family: "Fontawesome";
            font-size: 20px;
            color: $corporate_1;
          }

          &:after {
            content: '';
            @include center_y;
            height: 60px;
            width: 1px;
            background-color: rgba(black, .4);
            right: 0;
          }
        }

        .fa {
          color: $corporate_1;
        }

        .booking-search-results__search-data {
          > i {
            margin-right: 0;
            margin-left: 20px;
          }

          .booking-3-info {
            color: $corporate_1;
            display: inline-block;
            font-family: $text_family;
            margin-left: 0;
            text-transform: none;
            font-size: 15px;
            letter-spacing: 1px;
            margin-bottom: 10px;
            padding-left: 10px;
          }

          .booking-title-info, span.notranslate {
            color: $black;
            font-weight: 700;
          }
          .booking-title-info {
            margin-left: 10px;
          }
        }

        .booking-search-results__rooms-list {
          color: $black;
          top: 13px;
          right: 230px;
          font-weight: 700;
          .fa-users {
            display: none;
          }
          .booking-title-info {
            color: $black;
            font-weight: 400;
            @extend .icon-specialbed;
            &:before {
              font-size: 18px;
              font-family: "icomoon", sans-serif;
              margin-right: 10px;
              color: $corporate_1;
              display: inline-block;
              vertical-align: middle;
            }
          }
          .search-item {
            .booking-title-info {
              @extend .icon-specialfamily;
              &:before {
                color: $corporate_1;
                display: inline-block;
                vertical-align: middle;
              }
            }
          }
        }

        .booking-search-results__new-search {
          position: absolute;
          top: 0;
          bottom: 0;
          right: 0;
          border-radius: 0;
          padding: 25px 20px 0;
          overflow: hidden;
          width: 280px;

          .booking-button.booking-button--action {
            font-size: 14px;
            background: linear-gradient(to right, #ffc15e, #f49d37);
            font-family: $text_family;
          }
        }
      }
      .call_center_wrapper {
        position: fixed;
        width: auto;
        bottom: auto;
        top: 45%;
        right: 0;
        border: none;
        z-index: 1100;
        &:before {
          content: '\e90b';
          font-family: "icomoon" !important;
          display: inline-block;
          box-sizing: border-box;
          vertical-align: middle;
          font-size: 46px;
          color: white;
          background-color: $corporate_1;
          padding: 16px 3px;
          width: 50px;
          height: 50px;
          border-top-left-radius: 5px;
          border-bottom-left-radius: 5px;
        }
        .wrapper-new-web-support {
          box-sizing: border-box;
          display: inline-block;
          vertical-align: middle;
          height: 50px;
          max-width: 0;
          width: 200px;
          overflow: hidden;
          background-color: black;
          margin: 0 !important;
          padding: 10px 0;
          @include transition(all, .6s);
          .web_support_label_1 {
            position: relative;
            top: auto;
            left: auto;
            right: auto;
            transform: none;
            width: auto;
            color: white;
            &:before {
              display: none;
            }
            .web_support_wrapper {
              width: 200px;
              font-size: 10px;
              strong {
                font-weight: 400;
              }
              .web_support_number {
                font-size: 13px !important;
              }
            }
          }
        }
        &:hover {
          .wrapper-new-web-support {
            max-width: 300px;
            padding: 10px;
          }
        }
      }
    }

    .hidden_booking_summary {
      z-index: 110;
      .booking-search-results {
        border: 0;
        border-radius: 0;
        background-color: $grey;
        background-image: none;
        width: 100% !important;

        .title_booking_breakdown {
          color: $corporate_1;
          font-size: 16px;
          position: relative;
          padding: 35px 50px 35px 10px;
          line-height: 24px;
          margin-right: 30px;

          &:before {
            @include center_y;
            right: 15px;
            content: '\f084';
            font-family: "Fontawesome";
            font-size: 20px;
            color: $corporate_1;
          }

          &:after {
            content: '';
            @include center_y;
            height: 60px;
            width: 1px;
            background-color: rgba(black, .4);
            right: 0;
          }
        }

        .fa {
          color: $corporate_1;
        }

        .booking-search-results__search-data {
          > i {
            margin-right: 0;
            margin-left: 60px;
          }

          .booking-3-info {
            color: $corporate_1;
            display: inline-block;
            font-family: $text_family;
            margin-left: 0;
            text-transform: none;
            font-size: 15px;
            letter-spacing: 1px;
            margin-bottom: 10px;
            padding-left: 10px;
          }

          .booking-title-info, span.notranslate {
            color: $black;
            font-weight: 700;
          }
          .booking-title-info {
            margin-left: 10px;
          }
        }

        .booking-search-results__rooms-list {
          color: $black;
          top: 15px;
          right: 30px;
          font-weight: 700;
          max-width: none;
          .fa-users {
            display: none;
          }
          .booking-title-info {
            color: $black;
            font-weight: 400;
            @extend .icon-specialbed;
            &:before {
              font-size: 18px;
              font-family: "icomoon", sans-serif;
              margin-right: 10px;
              color: $corporate_1;
              display: inline-block;
              vertical-align: middle;
            }
          }
          .search-item {
            .booking-title-info {
              @extend .icon-specialfamily;
              &:before {
                color: $corporate_1;
                display: inline-block;
                vertical-align: middle;
              }
            }
          }
        }

        .booking-search-results__new-search {
          height: 80px;
          top: 50%;
          right: 7px;
          -webkit-transform: translateY(-50%);
          -moz-transform: translateY(-50%);
          -ms-transform: translateY(-50%);
          -o-transform: translateY(-50%);
          transform: translateY(-50%);
          border-radius: 0;
          overflow: hidden;
          width: 280px;

          .booking-button.booking-button--action {
            font-size: 14px;
            background-color: $corporate_1;
            font-family: $text_family;
            display: inline-block;
            color: white;
          }
        }
      }
      .call_center_wrapper {
        display: none;
      }
    }

    .supplements_list_wrapper {
      .booking-2-service-container {
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
        .booking-2-container-description {
          .booking-2-service-title {
            color: $black;
          }
          .add_service_element {
            padding: 10px;
            font-size: 16px;
            background-color: $corporate_1;
            font-family: $text_family;
            @include transition(all, .6s);
            &:hover {
              background-color: $corporate_3;
            }
          }
          .see_more_supplements_v2 {
            @include transition(opacity, .6s);
            &:hover {
              opacity: .6;
            }
            .plus_sign {
              color: $corporate_3;
              font-size: 16px;
              background-color: transparent !important;
            }
            .see_more {
              color: $corporate_3;
              font-size: 16px;
              text-transform: uppercase;
              text-decoration: none;
            }
          }
        }
        &.free_label {
          .booking-2-container-description {
            .booking-2-service-price {
              right: 135px;
              top: 30px;
              .currencyValue {
                color: #B40405;
                text-transform: uppercase;
                font-size: 25px;
                &:after {
                  content: '';
                  width: 18px;
                  height: 20px;
                  background: url(https://lh3.googleusercontent.com/Lq_f59ugVxGaZV8bwfcdyqtoYPHjSTs3BjumRKHLbyDCnKl9RdJyg-bgi58YWLLNSKX_kCUjgloGeO3Hetd4ERSu4F-Rj3Q=s1900) no-repeat;
                  display: block;
                  background-size: contain;
                  position: absolute;
                  bottom: 15px;
                  left: 105px;
                }
              }
            }

            .see_more_supplements_v2:not(.price_value_text){
              left: 70px;
              &:after {
                content: '';
                width: 30px;
                height: 30px;
                background: url(https://lh3.googleusercontent.com/M4cOe0-JThivZQects4zr2-loetu1o1aQazfNP10j8vVvsWolNvW92VNkMM0yFPZRSX58BrmNlJ5_fDWzgrJhAzb5pP6w0o=s1900) no-repeat;
                display: block;
                background-size: contain;
                position: absolute;
                bottom: 0;
                right: 100%;
              }

              .see_more {
                margin-left: 0;
                color: #B40405;
                font-size: 15px;
              }
            }
          }
        }
      }
    }
  }

  #step-3 {
    background: transparent;
    .booking-button--confirm-booking {
      @include btn_styles;
      padding: 20px 30px;
    }
  }

  .site-footer {
    background-color: $corporate_1;
    padding: 50px 0;

    * {
      color: white;
      font-size: 14px !important;
    }
  }
}