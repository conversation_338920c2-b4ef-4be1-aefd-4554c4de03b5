#login_wrapper_element.v5, #logged_user_info_wrapper.v5 {
  background-color: transparent;
  border: 1px solid;
  font-family: "Open Sans", sans-serif !important;
  @media only screen and (max-width: 1140px) {
      width: 1070px !important;
      .content_login_wrapper {
        width: 78%;
      }
      .content_logged_wrapper {
        width: 850px;
      }
  }

  .content_login_wrapper .club_icons_wrapper .club_icon_element .icon_image_wrapper img {
    margin-right: 10px;
  }

  .content_login_wrapper .club_icons_wrapper .club_icon_element .club_icon_description {
    color: #434343 !important;
    font-size: 16px !important;
    line-height: 1.3;
    font-family: "Open Sans", sans-serif !important;
  }

  .content_login_wrapper .users_buttons_wrapper {
    .join_button_wrapper .want_join, .already_member_wrapper {
      border-radius: 9px !important;
      font-family: "Open Sans", sans-serif !important;
      width: 100%;
    }

    .join_button_wrapper .want_join {
      background: transparent linear-gradient(76deg, $corporate_2 0%, $corporate_3 100%) 0 0 no-repeat padding-box;
    }

    .already_member_wrapper {
      border: 1px solid $corporate_2 !important;
    }
  }

  .logo_wrapper:after {
    border-right: 1px solid lightgray !important;
  }
}

#logged_user_info_wrapper.v5 {
  .content_logged_wrapper {
    .center_content_wrapper {
      .logged_user_text, .extra_logged_user_info, .logout_button_wrapper {
        color: $black;
        font-family: $text_family;
        line-height: 1.2;

        i {
          color: $black;
        }

        span {
          font-family: $text_family;
          color: $black;
        }
      }
    }
  }
}

div#step-1 table.listadoHabsTarifas tr .lock_board {
  padding: 3px !important;
  border-radius: 6px !important;

  .lock_board_wrapper {
    display: inline-flex;
    justify-content: flex-end;
    border: 1px solid;
    padding: 10px !important;
    border-radius: 6px !important;
    position: relative;
    min-width: 150px;
    font-weight: 700;
    font-size: 18px;
    letter-spacing: 1px;

    .lock_ico {
      position: absolute;
      margin-left: 0 !important;
      top: -10px;
      left: 10px;
      background: white;
      padding: 0 5px;
      font-size: 10px !important;

      &::after {
        content: "Desbloquear";
        font-family: $font-1;
        font-size: 9px;
        line-height: 22px;
        margin-left: 5px;
      }

      &:lang(en) {
        &:after {
          content: 'Unlock';
        }
      }
    }

    .club_lock_logo {
      margin-right: auto;
    }
  }
}


#personal-details-form .lock_rates_wrapper {
  margin-bottom: 30px !important;
}

#logged_user_info_wrapper.v5.showed {
  border: 1px solid !important;
}

#register_form_wrapper.v5, body #login_form_wrapper_v1.v5 {
.inputs_wrapper#user_info_form .data_form_field {

  label{
      text-align: left;
      font-size: 12px;
      position: absolute;
      top: 1px;
      left: -9px;
      padding: 8px;
      transition: .3s;
      display: inherit;
  }
    label[for="pais"],label[for="birthday"]{
      display: none !important;
  }
  input{
    &::placeholder{
      color: transparent;
    }
     &:focus ~ label,
     &:not(:placeholder-shown) ~ label {
      text-align: left;
      font-size: 12px;
      position: absolute;
      top: -23px;
      left: -9px;
      padding: 8px;
      transition: .3s;
   }
  }
    }


  font-family: "Open Sans", sans-serif !important;

  .buttons_wrapper_signup .sign_up_button, .login_button_element {
    font-family: "Open Sans", sans-serif !important;
  }
}

.club_send_password_wrapper #club-send-password-form #submit_recovery_club {
  background: $black !important;
}

#login_form_wrapper_v1.v5 .login_block .login_form .login_data_block{
  input{

    &::placeholder{
      color: transparent;
    }

     &:focus ~ label,
     &:not(:placeholder-shown) ~ label {
       text-align: left;
       color: #4D4F5C;
       font-size: 14px !important;
       position: absolute;
       top: -14px !important;
       left: 10px !important;
       padding: 4px;
       background: white;
       transition: .3s;
   }

  }
  label{
        text-align: left;
        color: #4D4F5C;
        font-size: 17px;
        position: absolute;
        top: 9px;
        left: 10px;
        padding: 8px;
        background: white;
        transition: .3s;
  }
  label[for="password_input"]{
    display: inherit;
  }
  }