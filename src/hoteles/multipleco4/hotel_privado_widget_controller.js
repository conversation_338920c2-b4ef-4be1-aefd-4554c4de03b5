{{ widget_controller_injection|safe }}

bookingWidgetController.config.avoid_guest_autoclose_click = true;

bookingWidgetController.add_widget_html = function () {
  var paraty_widget = bookingWidgetController.config.widget_html;
  if (!$("#widget_paraty").length) {
    $("body").append($("<div id='widget_paraty'></div>"));
  }

  $("#widget_paraty").html(paraty_widget);

  if ($("#widget_paraty input#extra_widget_class").length) {
    $("body").addClass($("#widget_paraty input#extra_widget_class").val());
  }

  $("#widget_paraty #full_wrapper_booking .booking_form_title").append($(".cancel_booking_link"));

  $("#widget_paraty .booking_steps").detach().appendTo($('body'));
};


bookingWidgetController.add_button_mobile_version_html = function () {
  if (!$("#floating_button_paraty").length) {
    $("body").append($("<div id='floating_button_paraty'></div>").html($.i18n._("reserva_ahora")));
  }
  $("#widget_paraty #full_wrapper_booking").append($("<i class='fa fa-times close_widget'></i>"));
}

bookingWidgetController.custom_format_date = function (dateComponents) {
  dateComponents = dateComponents.split("/");
  var html_date = "%d %m";

  let date = new Date();
  date.setMonth(dateComponents[1] - 1);

  let month = date.toLocaleString([], {
    month: 'short',
  });

  month = month.charAt(0).toUpperCase() + month.slice(1);

  return html_date.replace("%d", dateComponents[0]).replace("%m", month);
};

bookingWidgetController.occupancy_format_html = function () {
  return "<span class='adults'>@@N_A@@</span><span class='kids'>/@@N_C@@</span><span class='babies'>/@@N_B@@</span>";
};

bookingWidgetController.adding_room_tag_selector = function () {
  $("select.rooms_number option").each(function (index, element) {
    $(element).text($(element).text());
  });
  $("select.rooms_number").selectric("refresh");
};

bookingWidgetController.open_widget = function () {
  $("#floating_button_paraty").click(function (e) {
    e.preventDefault();
    $("#widget_paraty").fadeToggle();
  });
};

bookingWidgetController.close_widget = function () {
  $("i.fa-times.close_widget").click(function () {
    $("#widget_paraty").fadeOut();
    $(".booking_steps").hide();
  });
};


bookingWidgetController.custom_functions = function () {
  bookingWidgetController.config.languages = {
    "es": "SPANISH",
    "en": "ENGLISH"
  };

  //Get fontawesome 6
  var script_fa_tag = document.createElement('script');
  script_fa_tag.src = 'https://kit.fontawesome.com/d8b9925505.js';
  script_fa_tag.setAttribute("defer", "");
  document.head.appendChild(script_fa_tag);

  booking_engine_controller();
  prepare_guests_selector();
  set_occupancy_number();
  room_selector_dates();
  init_calendar();

  $("#full_wrapper_booking .children_selector select.room_selector").change(function () {
    check_kids_ages($(this));
  });

  if ($("#booking").length) {
    $("#full_wrapper_booking .kidAgesSelect").selectric();
  }

  $('#widget_paraty .widget_contact_selected').on('click', function () {
    $(this).toggleClass('active');
    $(this).siblings('.widget_contact_desc').slideToggle('fast');
  })
};

function init_calendar() {
  window.calendar_data.change_date_callback = function (date, isStartDateSelection) {
    function format(inputDate) {
      let date, month, year;

      date = inputDate.getDate();
      month = inputDate.getMonth() + 1;
      year = inputDate.getFullYear();

      date = date.toString().padStart(2, '0');
      month = month.toString().padStart(2, '0');

      return `${date}/${month}/${year}`;
    }

    let widget_form_wrapper = $('#full_wrapper_booking .paraty-booking-form');
    let date_formated = format(date);
    let day_month_format = $.datepicker.formatDate('dd M', date);

    if (isStartDateSelection) {
      widget_form_wrapper.find('input[name=startDate]').val(date_formated);
      widget_form_wrapper.find('.entry_date .date_day').html(day_month_format);
      widget_form_wrapper.find('.entry_date .date_year').html('');
    } else {
      widget_form_wrapper.find('input[name=endDate]').val(date_formated);
      widget_form_wrapper.find('.departure_date .date_day').html(day_month_format);
      widget_form_wrapper.find('.departure_date .date_year').html('');
      $(".booking_steps .step_3").trigger('click');
      $('.booking_steps .step_2').addClass('done');
    }

    load_new_room_dates();
  };
}

function prepare_guests_selector() {

  $("select.room_selector").unbind("change");
  $(".room_selector").selectric('destroy');
  $(".room_selector").selectric({disableOnMobile: false});
  $("select.room_selector, select.rooms_number").change(function () {
    set_occupancy_number();
  });

  $(".remove_room_element").click(function () {
    var actual_room_numbers = $("select.rooms_number").val();
    if (actual_room_numbers > 1) {
      var target_room_number = parseInt(actual_room_numbers) - 1;
      $("select.rooms_number option").removeAttr('selected');
      $("select.rooms_number option[value='" + target_room_number + "']").attr('selected', 'selected');
      $(".room" + actual_room_numbers).hide();
      $("select.rooms_number").val(target_room_number);
      $("select.rooms_number").selectric("refresh");
    }
    set_occupancy_number()
  });

  var add_room_html = "<div class='add_room'>" + $.i18n._('T_add_another_room') + "</div>",
      remove_room_html = "<div class='remove_room'>" + $.i18n._('T_eliminar') + ' ' + $.i18n._('T_habitacion') + "</div>",
      close_btn = "<div class='icon-xcross close_guest_selector'></div>",
      close_calendar_btn = "<div class='icon-xcross close_calendar_app'></div>",
      booking_btn = "<div class='wrapper_booking_button_guest'><div class='promocode_wrapper'>" +
          "<input autocomplete='off' type='text' class='promocode_input' id='popup_promocode' placeholder='" + $(".wrapper_booking_button .promocode_input").attr("placeholder") + "' name='promocode' value='' tabindex='16'>" +
          "</div><button type='button' class='submit_button popup_button'>" + $(".wrapper_booking_button .submit_button").html() + "</button></div>";
  $(".room_list_wrapper").append(close_btn);
  $(".calendar_root_wrapper").append(close_calendar_btn);
  $(".room_list_wrapper .room_list .room").each(function () {
    $(this).append(add_room_html);
  });
  $(".room_list_wrapper .room_list .room2").append(remove_room_html);
  $(".room_list_wrapper .room_list .room3").append(remove_room_html);
  $(".add_room").click(add_room);
  $(".remove_room").click(remove_room);

  $(".room_list_wrapper .room_list").append(booking_btn);

  const motorPromocodeInput = $(".wrapper_booking_button .promocode_wrapper input.promocode_input");
  const popupPromocodeInput = $(".wrapper_booking_button_guest #popup_promocode");

  popupPromocodeInput.change(function () {
    motorPromocodeInput.val($(this).val());
  });

  motorPromocodeInput.change(function () {
    popupPromocodeInput.val($(this).val());
  });

  $(".wrapper_booking_button_guest .submit_button").click(function () {
    $(".wrapper_booking_button .submit_button").click();
  });

  $(document).on("click", ".close_guest_selector, .close_calendar_app", function () {
    $(".booking_steps .step_1, .booking_steps .step_2, .booking_steps .step_3").removeClass("current_step");
    $(".hotel_selector").slideUp();
    $(".calendar_root_wrapper").slideUp();
    $(".room_list_wrapper").slideUp();
    $("#full_wrapper_booking").removeClass("fixed");
    $("#widget_paraty").removeClass("open");
    $('.booking_steps').hide();
    $("body").removeClass("widget_paraty_open");
  });

  $(".adults_selector .selectric-room_selector .label").click(function () {
    change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 8, 0, 1);
  });
  $(".adults_selector .selectric-room_selector .button").click(function () {
    change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 9, 1, -1);
  });
  $(".children_selector .selectric-room_selector .label").click(function () {
    change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 4, -1, 1);
  });
  $(".children_selector .selectric-room_selector .button").click(function () {
    change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 5, 0, -1);
  });
  $(".babies_selector .selectric-room_selector .label").click(function () {
    change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 4, -1, 1);
  });
  $(".babies_selector .selectric-room_selector .button").click(function () {
    change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 5, 0, -1);
  });
}

function change_selectric_rooms(element, parent_class, select, max, min, operator) {
  var selectric_element = element.closest(parent_class).find(select),
      label_for = element.closest(".range_label_enabled");
  if (parseInt(selectric_element.val()) > min &&
      parseInt(selectric_element.val()) < max) {
    var new_select_val = parseInt(selectric_element.val()) + operator;
    selectric_element.val(new_select_val);
    selectric_element.selectric('refresh');
    set_occupancy_number();
  }
}

function add_room() {
  var number_rooms = parseInt($("select.rooms_number").val());
  if (number_rooms < 3) {
    $($(".selectric-rooms_number .selectricItems li").get(number_rooms)).trigger("click");
    set_occupancy_number();
  }

  if (number_rooms == 1) {
    $(".room1 .add_room").hide();
  }

  if (number_rooms == 2) {
    $(".add_room").hide();
    $(".room2 .remove_room").hide();
  }
}

function remove_room() {
  var number_rooms = parseInt($("select.rooms_number").val());
  if (number_rooms > 1) {
    $($(".selectric-rooms_number .selectricItems li").get(number_rooms - 2)).trigger("click");
    set_occupancy_number();
  }

  if (number_rooms == 2) {
    $(".room1 .remove_room").show();
    $(".room1 .add_room").show();
  }

  if (number_rooms == 3) {
    $(".room2 .remove_room").show();
    $(".room2 .add_room").show();
  }

  $("select.rooms_number").change(function (event) {
    var number = $(this).val(),
        _room1 = $(".room1"),
        _room2 = $(".room2"),
        _room3 = $(".room3"),
        _room_age1 = $(".room_ages_1"),
        _room_age2 = $(".room_ages_2"),
        _room_age3 = $(".room_ages_3");

    if (number == 1) {
      _room2.hide().promise().done(function () {
        _room2.css("overflow", "initial");
      });
      _room_age2.removeClass("show")
      _room_age3.removeClass("show")
      _room3.hide().promise().done(function () {
        _room3.css("overflow", "initial");
      });
      $(".horizontal_engine").css("height", "379px");
    } else if (number == 2) {
      _room2.show("fast").promise().done(function () {
        _room2.css("overflow", "initial");
      });
      _room_age3.removeClass("show")
      _room3.hide().promise().done(function () {
        _room3.css("overflow", "initial");
      });
      $(".horizontal_engine").css("height", "449px");
    } else {
      _room2.show("fast").promise().done(function () {
        _room2.css("overflow", "initial");
      });
      _room3.show().promise().done(function () {
        _room3.css("overflow", "initial");
      });
      $(".horizontal_engine").css("height", "518px");
    }
  });
}

function toggle_guest_selector() {
  var target_room_wrapper = $(".room_list_wrapper");
  if (!target_room_wrapper.hasClass('active')) {
    target_room_wrapper.addClass('active');
    target_room_wrapper.show();
    console.log("showing");
  } else {
    target_room_wrapper.removeClass('active');
    target_room_wrapper.hide();
  }
  set_occupancy_number();
}

function set_occupancy_number() {
    var number_of_rooms = $("select[name='numRooms']").val(),
        adults_number = 0,
        kids_number = 0,
        babies_number = 0;

    if (number_of_rooms) {
        for (var room_loop = 1; room_loop <= number_of_rooms; room_loop++) {
            var actual_select_adults = $("select[name='adultsRoom" + room_loop + "']").val(),
                actual_select_kids = $("select[name='childrenRoom" + room_loop + "']").val(),
                actual_select_baby = $("select[name='babiesRoom" + room_loop + "']").val();

            if (actual_select_adults || actual_select_kids) {
                adults_number += parseInt(actual_select_adults);
                kids_number += parseInt(actual_select_kids);
                babies_number += parseInt(actual_select_baby);
            }
        }
    }

    var target_placeholder = $(".guest_selector .placeholder_text"),
        placeholder_string = "";

    adults_number = parseInt(adults_number);
    kids_number = parseInt(kids_number);
    babies_number = parseInt(babies_number);

    placeholder_string += "<span class='guests'>" + adults_number + " " + $.i18n._('T_adultos') + " + " + kids_number + " " + $.i18n._('T_ninos');

    if (babies_number) {
        placeholder_string += + " + " + babies_number + $.i18n._('T_bebes');
    }

    placeholder_string += "</span>";

    target_placeholder.html(placeholder_string);
}


bookingWidgetController.after_load_booking_script = function () {
  _set_datepicker_regional($);
  $(".room_selector").selectric({disableOnMobile: false});
  $(".rooms_number").selectric({disableOnMobile: false});
  bookingWidgetController.prepare_guest_selector();
  bookingWidgetController.adding_room_tag_selector();
  bookingWidgetController.set_occupancy_number();
  bookingWidgetController.update_date_by_timezone();
};

bookingWidgetController.datepicker_configuration = function () {
  let is_mobile = ($(window).width() <= 1140);
  DP_extend_info.config.booking_version = '5';
  DP_extend_info.config.hotel_path_endpoint = bookingWidgetController.config.base_url;
  DP_extend_info.config.months_show = (is_mobile) ? 1 : 2;
  DP_extend_info.config.months_show_highlight = true;
  DP_extend_info.config.force_hightlight = true;

  DP_extend_info.config.custom_format_day_month = function (dateComponents) {
    dateComponents = dateComponents.split("/");
    var month_short = $.datepicker._defaults['monthNamesShort'][parseInt(dateComponents[1], 10) - 1];
    return "<span class='day'>" + dateComponents[0] + "</span><span class='month'>" + month_short + "</span>";
  };

  DP_extend_info.init();
};

function room_selector_dates() {
  var room_list_wrapper = $('.room_list_wrapper .room_list'),
      dates_wrapper = $('<div class="room_info_wrapper"><div class="hotel_name_rooms"></div><div class="dates_wrapper"></div></div>');

  if (room_list_wrapper.length) {
    room_list_wrapper.prepend(dates_wrapper);
  }
  $('.dates_wrapper, .stay_selection').on('click', function () {
    $(".booking_steps .step_2").trigger('click');
  });
}

function check_kids_ages(select_element) {
  var parent_list = select_element.closest("li"),
      selected_value = select_element.val(),
      target_age_selector = parent_list.next(".full_ages_wrapper"),
      childs_elements = target_age_selector.find(".kid_age_element_wrapper"),
      target_childs_elements = childs_elements.slice(0, parseInt(selected_value));

  if (parseInt(selected_value)) {
    childs_elements.css('display', 'none');
    target_childs_elements.css('display', 'block');
    target_age_selector.slideDown(function () {
      $(this).css("overflow", "inherit");
    });
  } else {
    childs_elements.css('display', 'none');
    target_age_selector.slideUp(function () {
      $(this).css("overflow", "inherit");
    });
  }
}

function load_new_room_dates() {
  if ($('.dates_wrapper').length) {
    var start_date = $.datepicker.parseDate("dd/mm/yy", $("input[name=startDate]").val()),
        start_date_format = $.datepicker.formatDate("dd M", start_date),
        end_date = $.datepicker.parseDate("dd/mm/yy", $("input[name=endDate]").val()),
        end_date_format = $.datepicker.formatDate("dd M", end_date);

    $(".room_info_wrapper .dates_wrapper").html(decodeEntities($.i18n._("T_info_reserva")));
  }
}

function booking_engine_controller() {
  let widget = $("#widget_paraty"),
      booking_form = widget.find('.booking_form');

  widget.find(".hotel_selector_option").click(function () {
    widget.find(".hotel_selector").hide("fast");
    widget.find(".hotel_selector_option").removeClass("selected");
    $(this).addClass("selected");

    selectHotel($(this).attr("id"))
    $('.price_calendar_wrapper .toggle_chart').css('opacity', '1').css('pointer-events', 'auto');
  });

  $(document).on("click", ".booking_steps .step_1", function () {
    $(".booking_steps .step_2, .booking_steps .step_3").removeClass("current_step");
    $(".close_button_datepicker").click();
    $(".close_room_selector").click();
    $(".close_calendar_app").click();
    $(".hotel_selector").slideDown();
    $(".booking_steps .step_1").addClass("current_step");
    $("#widget_paraty").addClass("open");
    $('.booking_steps').show();
    $("body").addClass("widget_paraty_open");
    $("#full_wrapper_booking").addClass("fixed");
    load_new_room_dates();
  });

  $(document).on("click", ".booking_steps .step_2:not(.current_step)", function () {
    $(".booking_steps .step_1, .booking_steps .step_3").removeClass("current_step");
    $(".hotel_selector .close").click();
    $(".close_room_selector").click();
    $(".calendar_root_wrapper").slideDown();
    $(".booking_steps .step_2").addClass("current_step");
    $("#widget_paraty").addClass("open");
    $('.booking_steps').show();
    $("body").addClass("widget_paraty_open");
    $("#full_wrapper_booking").addClass("fixed");
    load_new_room_dates();
  });

  $(document).on("click", ".booking_steps .step_3:not(.current_step)", function () {
    $(".guest_selector").click();
    load_new_room_dates();
  });

  $(".guest_selector").click(function () {
    $(".booking_steps .step_1, .booking_steps .step_2, .booking_steps .step_3").removeClass("current_step");
    $(".hotel_selector").slideUp();
    $(".calendar_root_wrapper").slideUp();
    $(".room_list_wrapper").slideDown();
    $(".booking_steps .step_3").addClass("current_step");
    $("#widget_paraty").addClass("open");
    $('.booking_steps').show();
    $("body").addClass("widget_paraty_open");
    $("#full_wrapper_booking").addClass("fixed");
    load_new_room_dates();
  });

  $(".hotel_selector_inner li").click(function (e) {
    $(".booking_steps .step_1").removeClass("current_step");
    $('.booking_steps .step_1').addClass('done');
    if (!$("#widget_paraty").hasClass("dates_selected")) {
      $("#widget_paraty").addClass("open");
      $('.booking_steps').show();
      $("body").addClass("widget_paraty_open");
      $(".booking_steps .step_2").addClass("current_step");
      $(".calendar_root_wrapper").slideDown();
      load_new_room_dates();
    } else {
      if ($("#full_wrapper_booking").hasClass("fixed")) {
        $(".hotel_selector").hide();
        $(".booking_steps .step_2").removeClass("current_step");
        $("body").addClass("widget_paraty_open");
        if (!$('.calendar_app').is(':visible')) {
          $("#widget_paraty").addClass("open");
          $('.booking_steps').show();
          $("body").addClass("widget_paraty_open");
          $(".booking_steps .step_3").addClass("current_step");
          $(".room_list_wrapper").slideDown();
          load_new_room_dates();
        }
      }
    }
  });

  $(".close_button_datepicker").unbind("click");
  $(".close_button_datepicker").click(function () {
    $("#widget_paraty").removeClass("open");
    $('.booking_steps').hide();
    $("body").removeClass("widget_paraty_open");
    $("#full_wrapper_booking").removeClass("fixed");
    $(".booking_steps .step_2").removeClass("current_step");
  });
}

bookingWidgetController.init();


const decodeEntities = (() => {
  const element = document.createElement('div');

  function decodeHTMLEntities(str) {
    if (str && typeof str === 'string') {
      str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim, '');
      str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gim, '');
      element.innerHTML = str;
      str = element.textContent;
      element.textContent = '';
    }
    return str;
  }

  return decodeHTMLEntities;
})();

