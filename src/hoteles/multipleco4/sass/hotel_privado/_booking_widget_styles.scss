#floating_button_paraty,
.close_widget {
  display: none;
}

#widget_paraty {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  z-index: 1001;
  font-family: $primary_font;
  background-color: white;
  @import "booking_engine_v7_defaults";
  @import "booking/selectric";

  &.open {
    &::before {
      position: absolute;
      content: '';
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: white;
    }

    #full_wrapper_booking #full-booking-engine-html-7 .booking_form .stay_selection label,
    #full_wrapper_booking #full-booking-engine-html-7 .booking_form .guest_selector label {
      display: none;
    }
  }

  #full_wrapper_booking {
    width: 1140px;
    margin: 0 auto;
    overflow: hidden;
    background-color: white;

    &, * {
      box-sizing: border-box;
    }

    .widget_paraty_header {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      transform: translateY(-100%);
      height: 100px;
      display: none;
      align-items: center;
      justify-content: center;
      background-color: white;
      border-bottom: 1px solid $corporate_1;

      .widget_contact_wrapper {
        position: absolute;
        top: 50%;
        left: 50px;
        transform: translateY(-50%);

        .widget_contact_selected {
          font-family: $primary_font;
          color: white;
          font-size: 16px;
          cursor: pointer;
          position: relative;

          &::after {
            display: inline-block;
            vertical-align: middle;
            font-family: "Font Awesome 6 Pro";
            content: '\f078';
            margin: -5px 0 0 7px;
          }

          i {
            margin-right: 7px;
            font-weight: 300;
          }

          &.active {
            &::after {
              content: '\f077';
            }
          }
        }

        .widget_contact_desc {
          margin-bottom: -100px;
          position: absolute;
          left: 0px;
          top: 30px;
          background: $black;
          width: 250px;
          font-family: $primary_font;
          font-size: 12px;
          color: white;
          font-weight: 100;
          padding: 10px;
          border-radius: 10px;
        }
      }

      .logotype_widget {
        img {
          max-height: 70px;
        }
      }
    }

    #full-booking-engine-html-7 {
      background: transparent;
      display: block;
      padding: 0;
      z-index: 3;
      position: relative;

      .booking_form_title {
        display: none;
      }

      .calendar_root_wrapper {
        position: fixed;
        top: 170px;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 500;
        background: white;
        display: none;

        .close_calendar_app {
          position: absolute;
          width: 40px;
          height: 40px;
          border-width: 0;
          border-radius: 0;
          top: -140px;
          right: 50px;
          cursor: pointer;

          &:before, &:after {
            background: $corporate_1;
          }
        }

        .calendar_app {
          display: block !important;

          .price_calendar_wrapper {
            box-shadow: none;
            position: absolute;
            left: 50%;
            top: 30px;
            transform: translateX(-50%);

            @media (max-width: 1540px) {
              &:not(.is_mobile) .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr.week-row {
                height: 48px;

                td.day_wrapper {
                  width: 50px;
                  height: 35px !important;
                }
              }
            }

            .full_container {
              .selector_full_wrapper {
                .selector_wrapper {
                  .selector_label {
                    font-family: $primary_font;
                    letter-spacing: 1.2px;
                    color: $text-color;
                    font-weight: 500;
                  }

                  .selector {
                    .option {
                      color: $text-color;
                      font-family: $primary_font;

                      &:hover {
                        opacity: 0.7;
                      }
                    }
                  }
                }
              }

              .bottom_wrapper {
                .top {
                  .info_currency_wrapper {
                    .left_wrapper {
                      .notice_info_chart {
                        font-family: $primary_font;
                      }
                    }
                  }
                }
              }
            }

            &:not(.is_mobile) {
              .full_container {
                padding: 0 100px;

                .calendar_wrapper {
                  margin-top: 0;

                  .month_selector_wrapper {
                    .selector_label {
                      font-family: $primary_font;
                      font-size: 24px;
                      letter-spacing: 0;
                      color: $text-color;
                      font-weight: 500;
                    }

                    .selector {
                      .option {
                        color: $text-color;
                        font-family: $primary_font;

                        &:hover {
                          opacity: 0.7;
                        }
                      }
                    }
                  }


                  .month_full_wrapper table.month_wrapper {
                    padding-top: 20px;

                    thead tr td {
                      font-weight: 400;
                    }

                    tbody tr td.day_wrapper {
                      width: 57px;
                      height: 52px;

                      .popup_min_stay {
                        top: -60px;

                        &:after {
                          bottom: -6.4px;
                        }
                      }

                      .day {
                        font-family: $primary_font;
                        font-size: 20px;
                        letter-spacing: 0;
                        color: $text-color;
                        font-weight: 400;
                      }

                      .price {
                        font-family: $primary_font;
                        font-size: 12px;
                        font-weight: 300;
                      }

                      &.disabled,
                      &.closed {
                        .day,
                        .no_dispo_text {
                          color: #ADADAD;
                        }
                      }

                      &.selected {
                        .day {
                          color: white;
                        }
                      }

                      @media (max-width: 1440px) {
                        height: 45px;
                      }
                    }
                  }
                }
              }
            }

            .bottom_wrapper {
              .top {
                .selectors_wrapper .currency_selector_wrapper {
                  display: none;
                }

                .info_currency_wrapper .right_wrapper .toggle_chart {
                  opacity: 0.6;
                  pointer-events: none;
                }

                .right_wrapper {
                  .toggle_chart {
                    color: $black;
                    opacity: 1 !important;
                  }
                }
              }

              .bottom {
                display: none;
              }
            }
          }
        }
      }

      .booking_form {
        background-color: transparent;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 70px;
        margin: 0;

        .stay_selection {
          position: relative;
          width: 40%;
          @include input_base_styles;
          @include display_flex(nowrap);
          justify-content: space-between;
          align-items: center;
          border-radius: 0;
          padding: 0 20px;
          cursor: pointer;
          @include separator;

          label {
            @include label_styles;
          }

          .dates_wrapper {
            @include display_flex(nowrap);
            justify-content: space-between;
            align-items: center;
            width: 100%;

            &:before {
              right: 50%;
            }

            .dates_separator {
              display: none;
            }
          }

          .departure_date_wrapper {
            padding-left: 15px;
          }

          .entry_date_wrapper,
          .departure_date_wrapper {
            width: 50%;

            .date_box {
              text-align: left;

              .date_day {
                @include option_styles($ff: $primary_font);
                border-bottom: none !important;
              }

              .date_year {
                display: none;
              }

              &.entry_date {
                position: relative;

                &::after {
                  position: absolute;
                  font-family: "Font Awesome 6 Pro";
                  content: '\f178';
                  font-weight: 300;
                  top: 50%;
                  right: 30px;
                  transform: translate(50%, -50%);
                }
              }
            }
          }
        }

        .dates_selector_personalized {
          display: none;
        }

        .rooms_number_wrapper {
          display: none;
        }


        .guest_selector {
          float: left;
          width: 30%;
          margin-left: 10px;
          cursor: pointer;
          text-align: center;
          @include separator;

          label {
            @include label_styles;
            text-align: center;
          }

          .placeholder_text {
            @include option_styles($ff: $primary_font);
            width: 100%;
            text-align: center;
          }

          .guest_adults {
            margin-right: 6px;
          }

          b.button {
            display: none;
          }
        }

        .room_list_wrapper {
          position: fixed;
          top: 170px;
          bottom: 0;
          left: 0;
          right: 0;
          z-index: 1;
          width: 100%;
          background: white;
          float: none;
          display: none;
          vertical-align: middle;

          .close_guest_selector {
            position: absolute;
            width: 40px;
            height: 40px;
            border-width: 0;
            border-radius: 0;
            top: -140px;
            right: 50px;
            cursor: pointer;

            &:before, &:after {
              background: $corporate_1;
            }
          }

          .room_list {
            position: absolute;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            width: auto;
            text-align: center;
            white-space: nowrap;
            display: flex;
            flex-flow: column;
            align-items: center;
            max-height: 75%;
            overflow-y: scroll;
            min-width: 650px;

            &::-webkit-scrollbar {
              width: 4px;
              height: 4px;
            }

            &::-webkit-scrollbar-thumb {
              background: $corporate_2;
              border-radius: 15px;
            }

            &::-webkit-scrollbar-thumb:hover {
              background: $corporate_1;
            }

            &::-webkit-scrollbar-track {
              background: transparent;
              border-radius: 10px;
            }

            .add_room, .remove_room {
              display: inline-block;
              vertical-align: middle;
              position: absolute;
              cursor: pointer;
              height: 30px;
              border-radius: 50%;
              position: absolute;
              left: 50%;
              bottom: -40px;
              margin: 0;
              text-align: center;
              font-family: $primary_font;
              font-size: 17px;
              font-weight: 400;
              text-decoration: underline;
              transform: translate(-50%, 0%);
            }

            .add_room {
              &::before {
                content: '+ ';
              }
            }

            .remove_room {
              bottom: -65px;

              &::before {
                content: '- ';
              }
            }

            .room3 .remove_room {
              bottom: -40px;
            }

            .room_info_wrapper {
              display: table;
              height: auto;
              position: relative;
              color: $text_color;

              .dates_wrapper {
                width: 100%;
                display: inline-block;
                font-size: 18px;
                line-height: 32px;
                margin-bottom: 50px;

                .dates_wrapper_title {
                  font-size: 24px;
                  line-height: 32px;
                }
              }
            }

            .wrapper_booking_button_guest {
              display: block;
              width: 455px;
              margin: 20px auto 0;

              .promocode_wrapper, .submit_button {
                display: block;
                width: 100%;
                padding: 0;
                margin: 5px 0;
              }

              .promocode_wrapper {
                padding-bottom: 15px;
                width: 100%;

                .promocode_input {
                  text-align: center;
                  -webkit-appearance: none;
                  -moz-appearance: none;
                  appearance: none;
                  background-color: transparent;
                  height: 40px;
                  font-size: 14px;
                  font-weight: 300;
                  font-family: $secondary_font;
                  color: $black;
                  border: none;
                  border-bottom: 0.5px solid $black;
                  width: 100%;

                  &::-webkit-input-placeholder {
                    color: $black;
                  }

                  &::-moz-placeholder {
                    color: $black;
                  }

                  &:-ms-input-placeholder {
                    color: $black;
                  }

                  &:-moz-placeholder {
                    color: $black;
                  }
                }
              }

              .submit_button {
                width: 238px;
                height: 62px;
                line-height: 35px;
                font-size: 26px;
                font-weight: 300;
                letter-spacing: 0;
                margin: auto;
                background: $corporate_1;
                color: white;
                border: none;
                cursor: pointer;
                text-transform: uppercase;
                transition: all .4s;
                font-family: $secondary_font;
                display: inline-flex;
                align-items: center;
                justify-content: center;

                &:hover {
                  background-color: $corporate_3;
                }
              }
            }

            .room {
              position: relative;
              display: flex;
              vertical-align: middle;
              height: auto;
              padding: 15px 35px;
              text-align: center;
              border: 1px solid $black;
              border-radius: 10px;
              margin: 0 0 40px;
              overflow: initial !important;
              color: $text_color;

              label {
                margin: 0;
              }

              &:lang(de) {
                width: 310px;
              }

              .room_title {
                display: none;
              }

              label {
                display: inline-block;
                vertical-align: middle;
                text-transform: capitalize;
                font-size: 16px;
                text-align: left;
                font-weight: 500;
                position: relative;

                .range-age {
                  display: block;
                  font-size: 10px;
                }
              }


              .selectric-room_selector {
                display: inline-block;
                vertical-align: middle;

                .selectricItems {
                  display: none !important;
                }

                .selectric {
                  height: 30px;
                  margin: 0 0 0 5px;

                  .label {
                    text-align: center;
                    margin: 0;
                    color: $text-color;
                    font-family: $primary_font;
                    font-size: 18px;
                    line-height: 18px;
                    font-weight: 600;
                    display: inline-flex;
                    align-items: center;
                    height: 100%;

                    &:before {
                      content: '\f067';
                      position: absolute;
                      top: 50%;
                      right: 5px;
                      -webkit-transform: translateY(-50%);
                      -moz-transform: translateY(-50%);
                      -ms-transform: translateY(-50%);
                      -o-transform: translateY(-50%);
                      transform: translateY(-50%);
                      font-family: "Font Awesome 6 Pro";
                      font-weight: bold;
                      font-size: 14px;
                      color: $text-color;
                    }
                  }

                  .button {
                    position: absolute;
                    top: 0;
                    text-indent: 0;
                    height: auto;
                    color: white;
                    margin: 0;
                    font-size: 0;
                    left: -2px;
                    line-height: 24px;
                    background: transparent !important;
                    text-shadow: 0 0 0 rgba(0, 0, 0, 0) !important;

                    &:before {
                      content: '\f068';
                      position: absolute;
                      top: 50%;
                      right: 15px;
                      -webkit-transform: translateY(-50%);
                      -moz-transform: translateY(-50%);
                      -ms-transform: translateY(-50%);
                      -o-transform: translateY(-50%);
                      transform: translateY(-50%);
                      display: inline-block;
                      vertical-align: middle;
                      font-family: "Font Awesome 6 Pro";
                      font-weight: bold;
                      font-size: 14px;
                      line-height: 24px;
                      color: $text-color;
                    }
                  }
                }
              }


              .adults_selector, .children_selector, .babies_selector {
                height: auto;
                padding: 0 25px;
                border-left-width: 0;
                border-right: 1px solid $corporate_1;
              }

              .children_selector {
                border-right: none;
              }

              &.room_with_babies {
                .children_selector {
                  border-right: 1px solid $corporate_1;
                }

                .babies_selector {
                  border-right: none;
                }
              }
            }

            .full_ages_wrapper {
              display: none;
              margin-bottom: 10px;

              .kids_age_selection {
                .kid_age_element_wrapper, .kid_age_element_wrapper.hide {
                  display: none;
                }
              }

              .slides_wrapper {
                .input_slide {
                  display: inline-flex;
                  flex-flow: column;
                  width: 168px;

                  .slide_content {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 10px;
                    font-size: 14px;

                    .max-value {
                      font-weight: bold;
                    }
                  }

                  input[type="range"].slider_age {
                    appearance: none;
                    -moz-appearance: none;
                    -webkit-appearance: none;
                    display: block;
                    background: lightgray;
                    width: 168px;
                    height: 4px;
                    background-image: -webkit-gradient(linear, 0% 0%, 100% 0%, from(rgb(0, 45, 66)), from(rgb(211, 211, 211)));
                    background-image: -moz-linear-gradient(left center,
                            $corporate_2 0%, $corporate_2 0%,
                            lightgray 100%, lightgray 100%);

                    &::-webkit-slider-thumb {
                      appearance: none;
                      -webkit-appearance: none;
                      border: 1px solid $corporate_2;
                      height: 19px;
                      width: 19px;
                      border-radius: 50%;
                      background: $corporate_2;
                      cursor: pointer;
                      margin-top: 0;
                    }

                    &::-moz-range-thumb, &::-ms-thumb {
                      appearance: none;
                      -webkit-appearance: none;
                      border: 1px solid $corporate_2;
                      height: 19px;
                      width: 19px;
                      border-radius: 50%;
                      background: $corporate_2;
                      cursor: pointer;
                    }

                    &::-webkit-slider-runnable-track, &::-moz-range-track, &::-ms-track {
                      appearance: none;
                      -webkit-appearance: none;
                      -moz-appearance: none;
                      width: 210px;
                      height: 4px;
                      cursor: pointer;
                      border: none;
                      background: lightgray;
                    }

                    &:focus {
                      outline: none;

                      &::-webkit-slider-thumb {
                        margin-top: -7px;
                      }

                      &::-webkit-slider-runnable-track {
                        height: 4px;
                      }
                    }

                    &::-ms-track {
                      width: 100%;
                      cursor: pointer;
                      background: transparent;
                      border-color: transparent;
                      color: transparent;
                    }
                  }

                  &.hide {
                    display: none;
                  }

                  &.show {
                    &:not(:first-of-type) {
                      margin-left: 40px;
                    }
                  }
                }
              }

              &.show {
                display: block;
              }
            }
          }
        }

        .wrapper_booking_button {
          position: relative;
          display: inline-block;
          float: right;
          width: 40%;
          height: $height;
          @include separator;

          &::before {
            position: absolute;
            content: '';
            top: 0;
            bottom: 0;
            right: 0;
            width: 5px;
            background-color: $corporate_1;
          }

          .promocode_wrapper {
            display: inline-block;
            float: left;
            height: 100%;
            width: 45%;
            position: relative;
            padding: 0;
            border-top: none;

            label {
              display: none;
            }

            .promocode_input {
              @include center_xy;
              text-align: center;
              -webkit-appearance: none;
              -moz-appearance: none;
              appearance: none;
              background-color: transparent;
              width: 85%;
              height: 40px;
              font-size: 13px;
              line-height: 20px;
              font-weight: 300;
              font-family: $secondary_font;
              text-transform: uppercase;
              color: $black;
              border: none;

              &:focus {
                outline: 0;
              }

              &::-webkit-input-placeholder {
                @include promocode_placeholder;
              }

              &:-moz-placeholder {
                @include promocode_placeholder;
              }

              &::-moz-placeholder {
                @include promocode_placeholder;
              }

              &:-ms-input-placeholder {
                @include promocode_placeholder;
              }

              &::placeholder {
                @include promocode_placeholder;
              }
            }
          }

          .submit_button {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            float: left;
            width: 55%;
            height: 100%;
            padding-top: 5px;
            font-size: 26px;
            font-weight: 300;
            letter-spacing: 0;
            font-family: $secondary_font;
            background: $corporate_1;
            text-transform: uppercase;
            color: white;
            margin: 0;
            border-style: none;
            cursor: pointer;
            transition: all .4s;

            &:hover {
              background-color: $corporate_3;
            }
          }
        }
      }
    }
  }

  &.open {
    transform: none;
    top: 100px;
    bottom: initial;
    border-bottom: 1px solid $corporate_1;

    &::before {
      background-color: white;
      backdrop-filter: initial;
      -webkit-backdrop-filter: initial;
    }

    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background-color: $corporate_1;
    }

    #full_wrapper_booking {
      .widget_paraty_header {
        display: flex;
      }

      .widget_buttons {
        display: none;
      }

      .booking_steps {
        display: flex;
      }
    }
  }
}

.booking_steps {
  position: fixed;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 1000001;
  bottom: 30px;
  text-align: center;
  display: none;
  justify-content: center;
  align-items: stretch;
  width: 1140px;


  .step {
    position: relative;
    width: 1140px;
    margin: 0 auto;
    font-family: $primary_font;
    font-size: 16px;
    color: $black;
    font-weight: 300;
    display: none;

    .from_text {
      text-transform: lowercase;
    }

    &.current_step {
      display: block;
    }

    &:before {
      content: '';
      width: 33.3%;
      height: 8px;
      background: $corporate_1;
      position: absolute;
      left: 0;
      bottom: -15px;
      border-radius: 12px;
      z-index: 1;
    }

    &:after {
      content: '';
      width: 100%;
      height: 8px;
      background: #d6d6d6;
      @include center_x;
      bottom: -15px;
      border-radius: 12px;
    }

    &.step_2 {
      &:before {
        width: 66.3%;
      }
    }

    &.step_3 {
      &:before {
        width: 100%;
      }
    }
  }
}

.ui-dialog.ui-widget {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%);
  background-color: white;
  font-family: $primary_font;
  color: $text_color;

  .ui-dialog-title {
    font-weight: 400;
    font-size: 20px;
    letter-spacing: 0.9px;
    padding: 10px 0;
  }

  .ui-dialog-buttonpane {
    border: none !important;

    .ui-dialog-buttonset {
      .ui-button {
        position: relative;
        padding: 15px;
        border: none;
        border-radius: 0;
        background-color: $corporate_1;
        font-family: $primary_font;
        font-weight: 700;
        font-size: 18px;
        letter-spacing: 1px;
        line-height: 1;
        text-transform: uppercase;
        color: white;
        cursor: pointer;

        &:lang(ru) {
          font-size: 16px;
        }

        &:hover {
          background-color: $corporate_2;
        }
      }
    }
  }
}


@media (max-width: 1440px) {
  #widget_paraty #full_wrapper_booking #full-booking-engine-html-7 {
    .calendar_root_wrapper {
      top: 170px;

      .close_calendar_app {
        top: -140px;
      }
    }
  }
}


