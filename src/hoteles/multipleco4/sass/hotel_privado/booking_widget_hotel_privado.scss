@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

@import "plugins/mixins";
@import "plugins/fancybox_2_1_5";
@import "plugins/spiners/all_spiners";

@import "booking_widget_defaults";

$primary_font: "Poppins", serif;
$secondary_font: "Poppins", serif;




$text_color: #000;
$separator_color: $text_color;
$box_shadow: 0px 3px 15px rgba(#000000, 0.2);
$horizontal_padding: 15px;
$label_color: #a3a3a3;
$height: 70px;
$vertical_padding: 25px;
$black: $text_color;
$gray-1: #606163;

body {
  $corporate_1: #8c847c;
  $corporate_2: $corporate_1;
  $corporate_3: #a39c94;
  $option_color: red;

  $white: rgb(255, 255, 255);

  $booking_widget_color_1: $white; //body back ground & year input text color
  $booking_widget_color_2: $corporate_1; //header background & input texts
  $booking_widget_color_3: gray; //label texts
  $booking_widget_color_4: gray; //not used, but must be defined

  @import "datepicker";
  @import "booking_widget_styles";
  @import "responsive";

  &.widget_paraty_open {
    overflow-y: hidden;
  }

  .widget_paraty_header {
    z-index: 2 !important;
  }
}


#fancybox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 8000;
}

.fancybox-wrap.fancy-booking-search_v2 {
  width: 100% !important;
  height: 100% !important;
  top: 0 !important;
  left: 0 !important;
  background: transparent;
  z-index: 1000001;

  .container_popup_booking {
    @include center_xy;
    position: fixed;
    top: 45%;
    width: 555px;
    display: block;
    padding: 30px 0;
    box-sizing: border-box;
    margin: 7px;
    border: none!important;

    .description_top_popup_booking {
      width: 100%;
      margin-bottom: 20px;
    }

    .description_bottom_popup_booking {
      font-family: $primary_font;
      font-size: 15px;
      letter-spacing: 0.9px;
      line-height: 19px;
      text-transform: none;
      color: #1C1C1C;
    }

    .gif_wrapper {
      display: none;
    }
  }


  @media screen and (max-width: 400px) {
    .fancybox-wrap.fancy-booking-search_v2 {
      .container_popup_booking {
        width: 100vw;
        height: 100vh;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        transform: none;
        margin: 0;
        padding-top: 40%;
      }
    }
  }
}




