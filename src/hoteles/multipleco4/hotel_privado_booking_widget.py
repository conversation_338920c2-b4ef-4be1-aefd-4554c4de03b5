# -*- coding: utf-8 -*-
import os

from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name_with_properties
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.language.language_utils import get_web_dictionary
from booking_process.constants.advance_configs_names import SHOW_BABIES, CUSTOM_DOMAIN
from utils.flask_requests import response_utils
from utils.web.BaseInjectionHandler import InjectionScript, InjectionWidgetHandler


class HotelPrivadoScript(InjectionScript):

    def params_base_script_controller(self):
        context = {
            "widget_url": "hotelprivadowidget",
            "widget_css": "hotel_privado",
            "static_version": "1.01",
            "booking_version": "7",
            "calendar_version": "5"
        }

        return context

    def template_controller_name(self):
        return "hotel_privado_widget_controller.js"

    def template_controller_path(self):
        return os.path.join(os.path.dirname(__file__), self.template_controller_name())


class HotelPrivadoInjectionHandler(InjectionWidgetHandler):

    def get(self, *args):
        headers = response_utils.get_response_headers()
        headers['Access-Control-Allow-Origin'] = '*'
        headers['Access-Control-Allow-Headers'] = 'Origin, Content-Type, X-Auth-Token, X-CSRF-Token'
        response_utils.add_response_headers(headers)
        return super(InjectionWidgetHandler, self).get(*args)

    def getBookingWidgetOptions(self, language, selectOptions=None):
        options = super(HotelPrivadoInjectionHandler, self).getBookingWidgetOptions(language)
        options['custom_promocode_label'] = get_web_dictionary(language).get('T_promocode')

        widget_config = get_section_from_section_spanish_name_with_properties("_widget_config", language)
        if widget_config:
            if widget_config.get('extra_widget_class'):
                options['ExtraElementBeforeRoomList'] = '<input type="hidden" id="extra_widget_class" value="%s"/>' % widget_config['extra_widget_class']

            mini_dict = dict(get_web_dictionary(language))
            widget_pics = get_pictures_from_section_name("_widget_config", language)
            mini_dict['widget_buttons'] = []
            for button in widget_pics:
                if button.get('title') == 'contact':
                    button.update(get_properties_for_entity(button.get('key'), language))
                    mini_dict['widget_contact'] = button

            logotype = list(filter(lambda x: x.get('title') == 'logo', widget_pics))
            if logotype:
                mini_dict['logo'] = logotype[0]

            mini_dict['booking_steps'] = True

            options['custom_html_before_wrapper'] = self.buildTemplate_2("hotel_privado/_widget_content.html", mini_dict, False)

        options['inline_ages'] = False
        options['namespace'] = ''

        options['caption_submit_book'] = True
        options['departure_date_select'] = True
        options['booking_no_hide'] = True
        options['showBabies'] = get_config_property_value(SHOW_BABIES)
        options['avoid_translations'] = True

        #mini_dict = dict(get_web_dictionary(language))

        domain = get_config_property_value(CUSTOM_DOMAIN)
        if 'mini_dict' in locals():
            mini_dict['booking_0_domain'] = domain or ''

        return options






