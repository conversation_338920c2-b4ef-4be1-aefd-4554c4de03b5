{{ widget_controller_injection|safe }}

bookingWidgetController.config.avoid_guest_autoclose_click = true;

bookingWidgetController.add_widget_html = function () {
  const widgetHTML = bookingWidgetController.config.widget_html;
  const widgetId = '#widget_paraty';

  if (!$(widgetId).length) {
    $("body").append($("<div>", {id: 'widget_paraty', class: 'auto-position'}));
  }

  const widgetContainer = $(widgetId);

  widgetContainer.html(widgetHTML);

  const extraClass = $("#widget_paraty input#extra_widget_class").val();
  if (extraClass) {
    $("body").addClass(extraClass);
  }
  const cancelLink = $(".cancel_booking_link");
  $("#widget_paraty #full_wrapper_booking .booking_form_title").append(cancelLink);

  if ($("#widget_paraty .destination_wrapper").length) {
    widgetContainer.addClass('has-hotel-selector');
  }

  const bookingSteps = $("#widget_paraty .booking_steps").detach();
  $('body').append(bookingSteps);

  widgetContainer.before($("<div>", {class: 'widget_paratybg'}));

  const isMobile = $(window).width() <= 1140;

  if (isMobile) {
    widgetContainer.addClass('is_mobile');
  }
};

bookingWidgetController.add_button_mobile_version_html = function () {
  if (!$("#floating_button_paraty").length) {
    $("body").append($("<div id='floating_button_paraty'></div>").html($.i18n._("reserva_ahora")));
    $("#floating_button_paraty").addClass('auto-position');
  }
  $("#widget_paraty #full_wrapper_booking").append($("<i class='fa fa-times close_widget'></i>"));
}

bookingWidgetController.custom_format_date = function (dateComponents) {
  dateComponents = dateComponents.split("/");
  var html_date = "%d %m";

  let date = new Date();
  date.setMonth(dateComponents[1] - 1);

  let month = date.toLocaleString([], {
    month: 'short',
  });

  month = month.charAt(0).toUpperCase() + month.slice(1);

  return html_date.replace("%d", dateComponents[0]).replace("%m", month);
};

bookingWidgetController.occupancy_format_html = function () {
  return "<span class='adults'>@@N_A@@</span><span class='kids'>/@@N_C@@</span><span class='babies'>/@@N_B@@</span>";
};

bookingWidgetController.adding_room_tag_selector = function () {
  $("select.rooms_number option").each(function (index, element) {
    $(element).text($(element).text());
  });
  $("select.rooms_number").selectric("refresh");
};

bookingWidgetController.open_widget = function () {
  $("#floating_button_paraty").click(function (e) {
    e.preventDefault();
    $("#widget_paraty").fadeToggle();
    if($("#widget_paraty").hasClass("is_mobile")) $(this).addClass("hidden")
  });
};

bookingWidgetController.close_widget = function () {
  $("i.fa-times.close_widget").click(function () {
    $("#widget_paraty").fadeOut();
    $(".booking_steps").hide();
    if($("#widget_paraty").hasClass("is_mobile")) $("#floating_button_paraty").removeClass("hidden")
  });
};


bookingWidgetController.custom_functions = function () {
  bookingWidgetController.config.languages = {
    "es": "SPANISH",
    "en": "ENGLISH"
  };

  //Get fontawesome 6
  var script_fa_tag = document.createElement('script');
  script_fa_tag.src = 'https://kit.fontawesome.com/d8b9925505.js';
  script_fa_tag.setAttribute("defer", "");
  document.head.appendChild(script_fa_tag);

  booking_engine_controller();
  prepare_guests_selector();
  set_occupancy_number();
  room_selector_dates();
  init_calendar();
  hotel_filter_by_input();
  changeAgeKids();
  $(document).ready(function () {
    showWidgetButtons()
  });

  $("#full_wrapper_booking .children_selector select.room_selector").change(function () {
    check_kids_ages($(this));
  });
  if ($("#booking").length) {
    $("#full_wrapper_booking .kidAgesSelect").selectric();
  }

  let hotel_preselection = $("#widget_paraty").attr("data-hotel-namespace");
  let destiny_preselection = $("#widget_paraty").attr("data-destino");
  let spanish = $("html").attr("lang") == "es" || $("html").attr("lang") == "es-ES";

  if (hotel_preselection) {
    selectHotel(hotel_preselection);
  } else if (destiny_preselection) {
    if (spanish) {
      switch (destiny_preselection) {
        case "cordoba":
          destiny_preselection = "Córdoba";
          break;
        case "caceres":
          destiny_preselection = "Cáceres";
          break;
        case "cadiz":
          destiny_preselection = "Cádiz";
          break;
        case "malaga":
          destiny_preselection = "Málaga";
          break;
      }
    }
    if (destiny_preselection == "puerto-de-santa-maria") {
      destiny_preselection = "Puerto de Santa María";
    }

    let destiny = $(".booking_0_destiny").filter(function () {
      return (
          $(this).attr("hotel_name").toLowerCase() == destiny_preselection.toLowerCase()
      );
    });
    destiny.click();
    $("#full-booking-engine-html-7 .calendar_root_wrapper").css(
        "display",
        "none"
    );
  }

  let destination_placeholder = $.i18n._('T_establecimiento') + ' - ' + $.i18n._('T_destino');
  $('#widget_paraty .destination_wrapper .destination_fieldo .destination').attr('placeholder', destination_placeholder);

  $('#widget_paraty .widget_contact_selected').on('click', function () {
    $(this).toggleClass('active');
    $(this).siblings('.widget_contact_desc').slideToggle('fast');
  })

  let ol_map_library_url = 'https://cdn.jsdelivr.net/gh/openlayers/openlayers.github.io@master/en/v6.2.1/build/ol.js'

  $.getScript(ol_map_library_url, function () {
    ol_map();
  });

  //control for booking-steps class
  $("#widget_paraty .hotel_selector .hotel_selector_filter .destiny_filter.load_map").click(function (){
    $('.hotel_selector').addClass('no_gradient');
  })

  $("#widget_paraty .hotel_selector .hotel_selector_filter .destiny_filter.hotels").click(function (){
    $('.hotel_selector').removeClass('no_gradient');
  })
  $(document).ready(function () {
    let widget = $("#widget_paraty");
    let selectedOption = widget.find(".hotels_group .hotel_selector_option.selected");

    if (selectedOption.length) {
        let originalKidsAgesRange = getKidsAgesRange();
        let kidsAgesRange = selectedOption.attr('data-individual-kids-age-range');

        let kidsAgesRangeArray = kidsAgesRange
            ? kidsAgesRange.split('-').map(Number)
            : originalKidsAgesRange;

        setKidsAgesRange(kidsAgesRangeArray[0], kidsAgesRangeArray[1]);
    }
});
};

function init_calendar() {
  window.calendar_data.change_date_callback = function (date, isStartDateSelection) {
    function format(inputDate) {
      let date, month, year;

      date = inputDate.getDate();
      month = inputDate.getMonth() + 1;
      year = inputDate.getFullYear();

      date = date.toString().padStart(2, '0');
      month = month.toString().padStart(2, '0');

      return `${date}/${month}/${year}`;
    }

    let widget_form_wrapper = $('#full_wrapper_booking .paraty-booking-form');
    let date_formated = format(date);
    let day_month_format = $.datepicker.formatDate('dd M', date);
    let year_format = $.datepicker.formatDate('yy', date);

    if (isStartDateSelection) {
      widget_form_wrapper.find('input[name=startDate]').val(date_formated);
      widget_form_wrapper.find('.entry_date .date_day').html(day_month_format);
      widget_form_wrapper.find('.entry_date .date_year').html(year_format);
    } else {
      widget_form_wrapper.find('input[name=endDate]').val(date_formated);
      widget_form_wrapper.find('.departure_date .date_day').html(day_month_format);
      widget_form_wrapper.find('.departure_date .date_year').html(year_format);
      $(".booking_steps .step_3").trigger('click');
      $('.booking_steps .step_2').addClass('done');
    }

    load_new_room_dates();
  };
}

function prepare_guests_selector() {

  $("select.room_selector").unbind("change");
  $(".room_selector").selectric('destroy');
  $(".room_selector").selectric({disableOnMobile: false});
  $("select.room_selector, select.rooms_number").change(function () {
    set_occupancy_number();
  });

  $(".remove_room_element").click(function () {
    var actual_room_numbers = $("select.rooms_number").val();
    if (actual_room_numbers > 1) {
      var target_room_number = parseInt(actual_room_numbers) - 1;
      $("select.rooms_number option").removeAttr('selected');
      $("select.rooms_number option[value='" + target_room_number + "']").attr('selected', 'selected');
      $(".room" + actual_room_numbers).hide();
      $("select.rooms_number").val(target_room_number);
      $("select.rooms_number").selectric("refresh");
    }
    set_occupancy_number()
  });

  var add_room_html = "<div class='add_room'>" + $.i18n._('T_add_another_room') + "</div>",
      remove_room_html = "<div class='remove_room'>" + $.i18n._('T_eliminar') + ' ' + $.i18n._('T_habitacion') + "</div>",
      close_btn = "<div class='icon-xcross close_guest_selector'></div>",
      close_calendar_btn = "<div class='icon-xcross close_calendar_app'></div>",
      booking_btn = "<div class='wrapper_booking_button_guest'><div class='promocode_wrapper'>" +
          "<input autocomplete='off' type='text' class='promocode_input' id='popup_promocode' placeholder='" + $(".wrapper_booking_button .promocode_input").attr("placeholder") + "' name='promocode' value='' tabindex='16'>" +
          "</div><button type='button' class='submit_button popup_button'>" + $(".wrapper_booking_button .submit_button").html() + "</button></div>";
  $(".room_list_wrapper").append(close_btn);
  $(".calendar_root_wrapper").append(close_calendar_btn);
  $(".room_list_wrapper .room_list .room").each(function () {
    $(this).append(add_room_html);
  });
  $(".room_list_wrapper .room_list .room2").append(remove_room_html);
  $(".room_list_wrapper .room_list .room3").append(remove_room_html);
  $(".add_room").click(add_room);
  $(".remove_room").click(remove_room);

  $(".room_list_wrapper .room_list").append(booking_btn);

  const motorPromocodeInput = $(".wrapper_booking_button .promocode_wrapper input.promocode_input");
  const popupPromocodeInput = $(".wrapper_booking_button_guest #popup_promocode");

  popupPromocodeInput.change(function () {
    motorPromocodeInput.val($(this).val());
  });

  motorPromocodeInput.change(function () {
    popupPromocodeInput.val($(this).val());
  });

  $(".wrapper_booking_button_guest .submit_button").click(function () {
    $(".wrapper_booking_button .submit_button").click();
  });

  $(document).on("click", ".close_guest_selector, .close_calendar_app", function () {
    $(".booking_steps .step_1, .booking_steps .step_2, .booking_steps .step_3").removeClass("current_step");
    $(".hotel_selector").slideUp();
    $(".calendar_root_wrapper").slideUp();
    $(".room_list_wrapper").slideUp();
    $("#full_wrapper_booking").removeClass("fixed");
    $("#widget_paraty").removeClass("open");
    $('.booking_steps').hide();
    $("body").removeClass("widget_paraty_open");
  });

  $(".adults_selector .selectric-room_selector .label").click(function () {
    change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 8, 0, 1);
  });
  $(".adults_selector .selectric-room_selector .button").click(function () {
    change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 9, 1, -1);
  });
  $(".children_selector .selectric-room_selector .label").click(function () {
    change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 4, -1, 1);
  });
  $(".children_selector .selectric-room_selector .button").click(function () {
    change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 5, 0, -1);
  });
  $(".babies_selector .selectric-room_selector .label").click(function () {
    change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 4, -1, 1);
  });
  $(".babies_selector .selectric-room_selector .button").click(function () {
    change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 5, 0, -1);
  });
}

function change_selectric_rooms(element, parent_class, select, max, min, operator) {
  var selectric_element = element.closest(parent_class).find(select),
      label_for = element.closest(".range_label_enabled");
  if (parseInt(selectric_element.val()) > min &&
      parseInt(selectric_element.val()) < max) {
    var new_select_val = parseInt(selectric_element.val()) + operator;
    selectric_element.val(new_select_val);
    selectric_element.selectric('refresh');
    set_occupancy_number();
  }
}

function add_room() {
  var number_rooms = parseInt($("select.rooms_number").val());
  if (number_rooms < 3) {
    $($(".selectric-rooms_number .selectricItems li").get(number_rooms)).trigger("click");
    set_occupancy_number();
  }

  if (number_rooms == 1) {
    $(".room1 .add_room").hide();
  }

  if (number_rooms == 2) {
    $(".add_room").hide();
    $(".room2 .remove_room").hide();
  }
}

function remove_room() {
  var number_rooms = parseInt($("select.rooms_number").val());
  if (number_rooms > 1) {
    $($(".selectric-rooms_number .selectricItems li").get(number_rooms - 2)).trigger("click");
    set_occupancy_number();
  }

  if (number_rooms == 2) {
    $(".room1 .remove_room").show();
    $(".room1 .add_room").show();
  }

  if (number_rooms == 3) {
    $(".room2 .remove_room").show();
    $(".room2 .add_room").show();
  }

  $("select.rooms_number").change(function (event) {
    var number = $(this).val(),
        _room1 = $(".room1"),
        _room2 = $(".room2"),
        _room3 = $(".room3"),
        _room_age1 = $(".room_ages_1"),
        _room_age2 = $(".room_ages_2"),
        _room_age3 = $(".room_ages_3");

    if (number == 1) {
      _room2.hide().promise().done(function () {
        _room2.css("overflow", "initial");
      });
      _room_age2.removeClass("show")
      _room_age3.removeClass("show")
      _room3.hide().promise().done(function () {
        _room3.css("overflow", "initial");
      });
      $(".horizontal_engine").css("height", "379px");
    } else if (number == 2) {
      _room2.show("fast").promise().done(function () {
        _room2.css("overflow", "initial");
      });
      _room_age3.removeClass("show")
      _room3.hide().promise().done(function () {
        _room3.css("overflow", "initial");
      });
      $(".horizontal_engine").css("height", "449px");
    } else {
      _room2.show("fast").promise().done(function () {
        _room2.css("overflow", "initial");
      });
      _room3.show().promise().done(function () {
        _room3.css("overflow", "initial");
      });
      $(".horizontal_engine").css("height", "518px");
    }
  });
}

function toggle_guest_selector() {
  var target_room_wrapper = $(".room_list_wrapper");
  if (!target_room_wrapper.hasClass('active')) {
    target_room_wrapper.addClass('active');
    target_room_wrapper.show();
    console.log("showing");
  } else {
    target_room_wrapper.removeClass('active');
    target_room_wrapper.hide();
  }
  set_occupancy_number();
}

function set_occupancy_number() {
  var number_of_rooms = $("select[name='numRooms']").val(),
      adults_number = 0,
      kids_number = 0,
      babies_number = 0;

  if (number_of_rooms) {
    for (var room_loop = 1; room_loop <= number_of_rooms; room_loop++) {
      var actual_select_adults = $("select[name='adultsRoom" + room_loop + "']").val(),
          actual_select_kids = $("select[name='childrenRoom" + room_loop + "']").val(),
          actual_select_baby = $("select[name='babiesRoom" + room_loop + "']").val();

      if (actual_select_adults || actual_select_kids) {
        adults_number += parseInt(actual_select_adults);
        kids_number += parseInt(actual_select_kids);
        babies_number += parseInt(actual_select_baby);
      }
    }
  }

  var target_placeholder = $(".guest_selector .placeholder_text"),
      placeholder_string = "";

  adults_number = parseInt(adults_number);
  kids_number = parseInt(kids_number);
  babies_number = parseInt(babies_number);

  placeholder_string += "<span class='guests'>" + adults_number + " " + $.i18n._('T_adultos') + " + " + kids_number + " " + $.i18n._('T_ninos');

  if (babies_number) {
    placeholder_string += +" + " + babies_number + " " + $.i18n._('T_bebes');
  }

  placeholder_string += "</span>";

  target_placeholder.html(placeholder_string);
}

bookingWidgetController.after_load_booking_script = function () {
  _set_datepicker_regional($);
  $(".room_selector").selectric({disableOnMobile: false});
  $(".rooms_number").selectric({disableOnMobile: false});
  bookingWidgetController.prepare_guest_selector();
  bookingWidgetController.adding_room_tag_selector();
  bookingWidgetController.set_occupancy_number();
  bookingWidgetController.update_date_by_timezone();
};

bookingWidgetController.datepicker_configuration = function () {
  let is_mobile = ($(window).width() <= 1140);
  DP_extend_info.config.booking_version = '5';
  DP_extend_info.config.hotel_path_endpoint = bookingWidgetController.config.base_url;
  DP_extend_info.config.months_show = (is_mobile) ? 1 : 2;
  DP_extend_info.config.months_show_highlight = true;
  DP_extend_info.config.force_hightlight = true;

  DP_extend_info.config.custom_format_day_month = function (dateComponents) {
    dateComponents = dateComponents.split("/");
    var month_short = $.datepicker._defaults['monthNamesShort'][parseInt(dateComponents[1], 10) - 1];
    return "<span class='day'>" + dateComponents[0] + "</span><span class='month'>" + month_short + "</span>";
  };


  DP_extend_info.init();
};

function room_selector_dates() {
  var room_list_wrapper = $('.room_list_wrapper .room_list'),
      dates_wrapper = $('<div class="room_info_wrapper"><div class="hotel_name_rooms"></div><div class="dates_wrapper"></div></div>');

  if (room_list_wrapper.length) {
    room_list_wrapper.prepend(dates_wrapper);
  }
  $('.dates_wrapper, .stay_selection').on('click', function () {
    $(".booking_steps .step_2").trigger('click');
  });
}

function check_kids_ages(select_element) {
  var parent_list = select_element.closest("li"),
      selected_value = select_element.val(),
      target_age_selector = parent_list.next(".full_ages_wrapper"),
      childs_elements = target_age_selector.find(".kid_age_element_wrapper"),
      target_childs_elements = childs_elements.slice(0, parseInt(selected_value));

  if (parseInt(selected_value)) {
    childs_elements.css('display', 'none');
    target_childs_elements.css('display', 'block');
    target_age_selector.slideDown(function () {
      $(this).css("overflow", "inherit");
    });
  } else {
    childs_elements.css('display', 'none');
    target_age_selector.slideUp(function () {
      $(this).css("overflow", "inherit");
    });
  }
}

function load_new_room_dates() {
  if ($('.dates_wrapper').length) {
    var start_date = $.datepicker.parseDate("dd/mm/yy", $("input[name=startDate]").val()),
        start_date_format = $.datepicker.formatDate("dd MM", start_date),
        end_date = $.datepicker.parseDate("dd/mm/yy", $("input[name=endDate]").val()),
        end_date_format = $.datepicker.formatDate("dd MM", end_date);

    $(".room_info_wrapper .dates_wrapper").html(decodeEntities($.i18n._("T_info_reserva")));
  }
}

function selectHotel(namespace) {
  let widget = $("#widget_paraty"),
      booking_form = widget.find('.booking_form');
  let new_placeholder_html = $(".hotel_selector .hotel_selector_option#" + namespace).find(".title_selector").html().replace("<br>", " ").replace("&amp;", "&").replace(/<.*?>/g, ""),
      url_booking = widget.find("#url_booking_" + namespace).val(),
      hotel_value = url_booking.indexOf('http') > -1 ? url_booking : "https://" + namespace + url_booking;

  widget.find(".destination").val(new_placeholder_html);
  booking_form.attr("action", hotel_value).find("#namespace").val(namespace);
  changeAgeKids();
  widget.find(".hotel_selector_option").removeClass("selected");
  $(".hotel_selector .hotel_selector_option#" + namespace).addClass("selected");
  setTimeout(() => {
    window.calendar_data.update_namespace_calendar(namespace);
  }, 100);
}
function booking_engine_controller() {
  let widget = $("#widget_paraty"),
      booking_form = widget.find('.booking_form');

  $(".destination_wrapper").click(function () {
    $(".booking_steps .step_1, .booking_steps .step_2, .booking_steps .step_3").removeClass("current_step");
    //$(".close_button_datepicker").click();
    $(".close_calendar_app").click();
    $(".close_room_selector").click();
    $(".hotel_selector").slideDown();
    $(".booking_steps .step_1").addClass("current_step");
    $("#widget_paraty").addClass("open");
    $('.booking_steps').show();
    $("body").addClass("widget_paraty_open");
    $("#full_wrapper_booking").addClass("fixed");
    load_new_room_dates();
    if ($("#filter_selector").length) {
      _filter_selector($(this));
    }
  });

  $(document).on("click", ".booking_steps .step_1", function () {
    $(".booking_steps .step_2, .booking_steps .step_3").removeClass("current_step");
    $(".close_button_datepicker").click();
    $(".close_room_selector").click();
    $(".close_calendar_app").click();
    $(".hotel_selector").slideDown();
    $(".booking_steps .step_1").addClass("current_step");
    $("#widget_paraty").addClass("open");
    $('.booking_steps').show();
    $("body").addClass("widget_paraty_open");
    $("#full_wrapper_booking").addClass("fixed");
    load_new_room_dates();
  });

  $(document).on("click", ".booking_steps .step_2:not(.current_step)", function () {
    $(".booking_steps .step_1, .booking_steps .step_3").removeClass("current_step");
    $(".hotel_selector .close").click();
    $(".close_room_selector").click();
    $(".calendar_root_wrapper").slideDown();
    $(".booking_steps .step_2").addClass("current_step");
    $("#widget_paraty").addClass("open");
    $('.booking_steps').show();
    $("body").addClass("widget_paraty_open");
    $("#full_wrapper_booking").addClass("fixed");
    load_new_room_dates();
  });

  $(document).on("click", ".booking_steps .step_3:not(.current_step)", function () {
    $(".guest_selector").click();
    load_new_room_dates();
  });

  $(".guest_selector").click(function () {
    $(".booking_steps .step_1, .booking_steps .step_2, .booking_steps .step_3").removeClass("current_step");
    $(".hotel_selector").slideUp();
    $(".calendar_root_wrapper").slideUp();
    $(".room_list_wrapper").slideDown();
    $(".booking_steps .step_3").addClass("current_step");
    $("#widget_paraty").addClass("open");
    $('.booking_steps').show();
    $("body").addClass("widget_paraty_open");
    $("#full_wrapper_booking").addClass("fixed");
    load_new_room_dates();
  });

  $(".hotel_selector_inner li").click(function (e) {
    $(".booking_steps .step_1").removeClass("current_step");
    $('.booking_steps .step_1').addClass('done');
    if (!$("#widget_paraty").hasClass("dates_selected")) {
      $("#widget_paraty").addClass("open");
      $('.booking_steps').show();
      $("body").addClass("widget_paraty_open");
      $(".booking_steps .step_2").addClass("current_step");
      $(".calendar_root_wrapper").slideDown();
      load_new_room_dates();
    } else {
      if ($("#full_wrapper_booking").hasClass("fixed")) {
        $(".hotel_selector").hide();
        $(".booking_steps .step_2").removeClass("current_step");
        $("body").addClass("widget_paraty_open");
        if (!$('.calendar_app').is(':visible')) {
          $("#widget_paraty").addClass("open");
          $('.booking_steps').show();
          $("body").addClass("widget_paraty_open");
          $(".booking_steps .step_3").addClass("current_step");
          $(".room_list_wrapper").slideDown();
          load_new_room_dates();
        }
      }
    }
  });

  $(".close_button_datepicker").unbind("click");
  $(".close_button_datepicker").click(function () {
    $("#widget_paraty").removeClass("open");
    $('.booking_steps').hide();
    $("body").removeClass("widget_paraty_open");
    $("#full_wrapper_booking").removeClass("fixed");
    $(".booking_steps .step_2").removeClass("current_step");
  });

  $(".hotels_group .hotels_grouped_list .booking_0_destiny").click(function(){
    setTimeout(() => {
      window.calendar_data.update_namespace_calendar("")
    }, 100)

      let booking_0_domain = '';

      if ($('#booking_0_domain').length) {
        booking_0_domain = $('#booking_0_domain').val();
      }

      let all_namespaces = $(this).attr('namespaces'),
          hotel_name = $(this).attr('hotel_name');

      $(".hotel_selector").slideUp();
      $("#full-booking-engine-html-7 .calendar_root_wrapper").css("display", "block");
      booking_form.attr("action", booking_0_domain + "/booking0");
      booking_form.find(".destination").val(hotel_name);

      if (!booking_form.find("input[name='applicationIds']").length) {
          $("<input type='hidden' id='applicationIds' name='applicationIds' value=''>").appendTo(booking_form);
      }

      booking_form.find("input[name='applicationIds']").val(all_namespaces);
  });

  $(".hotel_selector .close").click(function (e) {
    e.preventDefault();
    $(this).toggleClass("active");
    $(".hotel_selector").slideUp();
    $("booking_steps .step_1").removeClass("current_step");
    $("#widget_paraty").removeClass("open");
    $('.booking_steps').hide();
    $("body").removeClass("widget_paraty_open");
    $("#full_wrapper_booking").removeClass("fixed");
  });

  $(".wrapper_booking_button .submit_button").click(function () {
    if ($(".destination_fieldo input[name=destination]").val() !== "") {
      $(".booking_steps .step_1, .booking_steps .step_2, .booking_steps .step_3").removeClass("current_step");
      $(".close_button_datepicker").click();
      $(".close_room_selector").click();
    } else {
      $(".booking_steps .step_1").click();
    }
  });

  //select hotels from map cards
  $('.hotel_card .bottom .button-promotion').click(function (){
    let hotel_card_namespace = $(this).data('namespace');
    let hotel_option = $('.hotel_selector_option');

    hotel_option.each(function (){
      let hotel_option_namespace = $(this).attr('id');

      if (hotel_card_namespace === hotel_option_namespace) {
        $(this).trigger('click');
      }
    })
  })
}

bookingWidgetController.init();

const decodeEntities = (() => {
  const element = document.createElement('div');

  function decodeHTMLEntities(str) {
    if (str && typeof str === 'string') {
      str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim, '');
      str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gim, '');
      element.innerHTML = str;
      str = element.textContent;
      element.textContent = '';
    }
    return str;
  }

  return decodeHTMLEntities;
})();


//load map selector hotels
function retreive_hotel_markers() {
  var markers = [];
  $(".hotel_selector_inner .hotel_selector_option").each(function () {
    if (!$(this).closest(".hotels_group").hasClass("hide")) {
        var lat = $(this).attr('lat'),
        lng = $(this).attr('lng'),
        namespace = $(this).attr('id');
    }
    markers.push({lat: lat, lng: lng, namespace: namespace})
  });
  return markers
}


function ol_map() {
    MapController = function () {
        return {
            initialize_map: function () {
                map = new ol.Map({
                    target: 'map',
                    layers: [
                        new ol.layer.Tile({
                            source: new ol.source.OSM()
                        })
                    ],
                    view: new ol.View({
                        center: ol.proj.fromLonLat([-4, 40]),
                        zoom: 6
                    })
                });
            },

            initialize_markers: function (markers, namespace) {
                var Markers = markers;

                var features = [],
                    target_hotel = null;

                for (var i = 0; i < Markers.length; i++) {
                    var item = Markers[i],
                        longitude = item.lng,
                        latitude = item.lat,
                        hotel_namespace = item.namespace;

                    if (!$('#poi_icon').length) {
                        var iconStyle = new ol.style.Style({
                            text: new ol.style.Text({
                                text: '\uf3c5',
                                font: 'normal bold 30px "Font Awesome 6 Pro"',
                                textBaseline: 'bottom',
                                fill: new ol.style.Fill({
                                    color: namespace == hotel_namespace ? '#11CCC7' : 'rgba(17, 204, 199, 0.45)'
                                })
                            }),
                        });
                    } else {
                        var iconStyle = new ol.style.Style({
                            image: new ol.style.Icon({
                                scale: 1,
                                src: $('#poi_icon').val()
                            })
                        });
                    }

                    var iconFeature = new ol.Feature({
                        geometry: new ol.geom.Point(ol.proj.transform([longitude, latitude], 'EPSG:4326', 'EPSG:3857')),
                        namespace: hotel_namespace
                    });

                    iconFeature.setStyle(iconStyle);
                    features.push(iconFeature);

                    if (item.namespace == namespace) {
                        target_hotel = item;
                    }
                }

                var vectorSource = new ol.source.Vector({
                    features: features
                });

                map_hotels_layer = new ol.layer.Vector({
                    source: vectorSource
                });
                map.addLayer(map_hotels_layer);

                if (target_hotel) {
                    map.getView().animate({
                        center: ol.proj.transform([target_hotel.lng, target_hotel.lat], 'EPSG:4326', 'EPSG:3857'),
                        zoom: 15
                    });
                }
            },

            focus_hotel_namespace: function (available_hotels, target_namespace) {
                for (var i = 0; i < available_hotels.length; i++) {
                    var actual_hotel = available_hotels[i];
                    if (actual_hotel.namespace == target_namespace) {
                        map.removeLayer(map_hotels_layer);
                        this.initialize_markers(available_hotels, target_namespace);
                    }
                }
            }
        }
    }();

    $("#widget_paraty .hotel_selector .hotel_selector_filter .tab.load_map").click(function () {
        $("#map").html("");
        setTimeout(function () {
            MapController.initialize_map();
            const markers = retreive_hotel_markers();
            MapController.initialize_markers(markers);

            map.on('singleclick', function (evt) {
                var feature = map.forEachFeatureAtPixel(evt.pixel, function (feature, layer) {
                    return feature;
                });

                if (feature) {
                    MapController.initialize_markers(markers, feature.values_.namespace);
                    $(".hotel_card").slideUp().promise().done(function () {
                        $(".hotel_card." + feature.values_.namespace).slideDown();
                    });
                } else {
                    $(".hotel_card").slideUp();
                }
            });
        }, 500);
    });
}


function hotel_filter_by_input() {
  var destiny_group = $('#widget_paraty .hotel_selector_inner .hotels_group');

  $('#widget_paraty .hotel_selector .searching_hotel').click(function (){
    $('.hotel_selector_filter .hotels').click();
  })

  $('#widget_paraty .hotel_selector .searching_hotel').keyup(function () {
    var text_tofind = $(this).val().toLowerCase().replace('á', 'a').replace('é', 'e').replace('í', 'i').replace('ó', 'o').replace('ú', 'u').replace(',', '').replace('.', '');

    if (text_tofind == '') {
      destiny_group.show();
      destiny_group.find('.hotels_grouped_list .hotel_selector_option').show();
    } else {
      destiny_group.each(function () {
        var hotels = $(this).find('.hotels_grouped_list .hotel_selector_option');
        var show_destiny = false;
        hotels.each(function () {
          var hotel_name = $(this).find('.title_selector').text().toLowerCase().replace('á', 'a').replace('é', 'e').replace('í', 'i').replace('ó', 'o').replace('ú', 'u').replace(',', '').replace('.', '');
          if (hotel_name.indexOf(text_tofind) < 0) {
            $(this).hide();
          } else {
            $(this).show();
            show_destiny = true;
          }
        });

        var destiny = $(this).find('.hotels_destiny_title').text().toLowerCase().replace('á', 'a').replace('é', 'e').replace('í', 'i').replace('ó', 'o').replace('ú', 'u').replace(',', '').replace('.', '');

        if (show_destiny) {
          if (destiny.indexOf(text_tofind) < 0) {
            $(this).show();
          } else {
            $(this).show();
            hotels.show();
          }
        } else {
          if (destiny.indexOf(text_tofind) < 0) {
            $(this).hide();
          } else {
            $(this).show();
            hotels.show();
          }
        }
      });
    }
  });
}

function changeAgeKids() {
  let originalKidsAgesRange = getKidsAgesRange(),
      originalKidsAgesRangeLabel = originalKidsAgesRange.join('-');

  $(document).on('click', '.hotel_selector_option', function () {
    let kidsAgesRange = $(this).attr('data-individual-kids-age-range'),
        kidsAgesRangeArray = originalKidsAgesRange;
    if (kidsAgesRange) {
      kidsAgesRangeArray = kidsAgesRange.split('-').map(x => parseInt(x));
    }

    setKidsAgesRange(kidsAgesRangeArray[0], kidsAgesRangeArray[1]);
  });

  $(document).on('click', '.booking_0_destiny', function () {
    setKidsAgesRange(originalKidsAgesRange[0], originalKidsAgesRange[1]);
  });
}

function getKidsAgesRange() {
  let kidsAgesRange = [];

  $('#dialog-form .wrapper_age_kids .kidAgesSelect').first().each(function () {
    kidsAgesRange.push(parseInt($(this).find('option').first().val()));
    kidsAgesRange.push(parseInt($(this).find('option').last().val()));
  });

  return kidsAgesRange;
}

function setKidsAgesRange(min, max) {
  $('#dialog-form .wrapper_age_kids .kidAgesSelect').empty().each(function () {
    for (let i = min; i <= max; i++) {
      $(this).append($('<option>', {
        value: i,
        text: i
      }));
    }
  });

  $('.kids_label').text([min, max].join('-'));
}

function showWidgetButtons() {
  let widget = $("#widget_paraty");
  let buttons = widget.find(".widget_buttons .button");
  let hotelPreselection = widget.data("hotel-namespace");

  buttons.each(function () {
    let availableNamespaces = $(this).data("available-for-namespace");
    if (availableNamespaces) {
      let namespaces = availableNamespaces.split(";").map(x => x.trim());
      $(this).toggle(namespaces.includes(hotelPreselection)).css("opacity", "1");
    }
  });
}

function update_date_for_selected_hotel() {
  const hotel_selected = $("#widget_paraty .hotel_selector_option.selected");
  if (!hotel_selected.length) return;

  const namespace = hotel_selected.attr("id");
  const input = hotel_selected.find(`#forcedStartDate_${namespace}`);

  if (namespace && input.length) {
    setTimeout(function () {
        bookingWidgetController.force_start_date(input);
    }, 1000);
  }
}

window.addEventListener("namespaceUpdated.price_calendar_v2", update_date_for_selected_hotel);