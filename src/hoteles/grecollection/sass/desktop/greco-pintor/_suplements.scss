div#step-2 {
  background-color: white;

  .additional_services_total_wrapper {
    .total_label, .total_prices {
      color: #383838;
    }

    .perform_additional_services_booking {
      transition: all .6s;
      border-radius: 8px;
      background: $corporate_1;

      &:hover {
        opacity: 0.7;
      }
    }
    @media only screen and (max-width: 1140px) {
      width: 1071px;
    }


  }
  .additional_services_total_wrapper {
    &.top:not(.fixed) {
      display: none;
    }

    &.fixed {
      .perform_additional_services_booking{
        right: calc((100% - 1120px) / 2);
        top: 5px;
        width: 190px;
      }
    }
  }

  .booking-2-service-container {
    .booking-2-container-description {
      height: auto !important;
      padding-bottom: 60px;

      .booking-2-service-title {
        font-family: $text_family;
        font-weight: 500;
        font-size: 21px;
        letter-spacing: 1.15px;
        line-height: 1;
        text-transform: capitalize;
      }

      .services_buttons_wrapper {
        .add_service_element,
        .remove_service_element {
          font-family: $title_family;
          //padding: 10px 5px;
          text-align: center;
          font-size: 18px;
          font-weight: bold;
        }

        .add_service_element {
          background-color: white;
          border: 1px solid $corporate_1;
          color: $corporate_1;
        }

        .remove_service_element {
          background-color: $corporate_2 !important;
        }
      }
    }

    .booking-2-service-price {
      .currencyValue.free {
        font-size: 40px;
        color: $corporate_2;
      }
    }
  }

  .booking_button_wrapper {
    .booking-button {
      background-color: $corporate_1;
      font-family: $title_family;
    }
  }

  .all_additional_services_wrapper, .with_tabs_scroll {
      padding: 15px 40px;
      margin: 110px auto 25px;
      padding: 0 40px 29px;
      border-radius: 15px;
      border: 0.75px solid #CDCFD0;

      .additional_services_tabs_wrapper {
        position: relative;
        margin-bottom: 0;
        top: -80px;
        left: -40px;
        @media only screen and (max-width: 1140px) {
          left: -5px;
      }

        .additional_services_tabs_list {
          position: relative;

          &::before {
            display: none;
          }

          .additional_services_tab {
            transition: all .6s;
            border: 1px solid #000000;
            padding: 10px 15px;
            .main_title{
              font-weight: 400;
            }

            &.active, &:hover {
              border: 2px solid #000000;

              .main_title {
                font-weight: 500;
              }
            }

            &:hover {
              opacity: .7;
            }
          }
        }

        &.fixed {
          position: fixed;
          top: 0;
          left: 0;

          .additional_services_tabs_list {
            &::before {
              display: none;
            }

            .additional_services_tab {
              &.active {
                .main_title {
                  border-bottom: 1px solid $corporate-2;
                }
              }
            }
          }
        }
      }

      .category_wrapper {
        .title_category {
          .main_title {
            color: $corporate-2;
            margin-top: 20px;

            small {
              font-weight: 500;
            }
          }
        }

        .additional_services_wrapper {
          .additional_service_element {
            background: transparent;
            border: 1px solid #707070;
            border-radius: 10px;

            &:hover {
              box-shadow: none;
            }

            .service_content {
              width: 39%;

              .service_title {
                margin-bottom: 10px;
                font-size: 20px;
              }
              .service_description {
                font-size:16px;
                div span{
                  font-size:16px !important;
                }
              }
            }

            .service_selection_wrapper {
              width: 35%;
              display: flex;
              justify-content: space-around;

              .price_service {
                color: #383838;
                width: 60%;
                margin-right: 15px;
              }

              .add_service_button {
                i, .label {
                  color: $black !important;
                }
              }
              .quantity_selection_wrapper {
                .quantity_selection {
                    border: 1px solid $black;
                    select, i {
                        color: $black !important;
                    }
                }
                .quantity_selection_title {
                    color: $black !important;
                }
              }
              .days_selection_wrapper {
                .days_selection {
                    border: 1px solid $black !important;
                    select, i {
                        color: $black !important;
                    }
                }
                .days_selection_title {
                    color: $black !important;
                }
              }
            }
          }
        }
        &.upgrade{
          .additional_service_element .service_selection_wrapper .quantity_selection_wrapper{
            display: none !important;
          }
        }
      }
    }
  }


.all_additional_services_wrapper .additional_service_element{
  &:before, &:after{
    background: $corporate_2;
  }
  &.selected{
    .service_selection_wrapper .add_service_button{
      i{
        font-weight: 100;
        color: $corporate_2 !important;
      }
      .remove_label{
        color: $corporate_2 !important;
      }
    }
  }
}

.full_service_popup_info .service_popup_info_description{
  font-size: 15px;
}