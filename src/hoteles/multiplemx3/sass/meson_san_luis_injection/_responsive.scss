@media (max-width: 1140px) {

  body #widget_paraty {
    width: 100vw;
    height: 400px;
    position: absolute;
    top: 120px;
    margin: 0 auto;
    background: transparent;
    box-shadow: none;
    border: none;

    #full_wrapper_booking {
      #full-booking-engine-html-7 {
        .booking_form {
          flex-direction: column;
          height: unset;
          width: 345px;
          background: white;
          margin: 0 auto;

          .destination_wrapper {
            height: 55px;
            width: 90%;
            padding: 10px 0 10px 10px;
            background-color: $light_grey;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 15px auto;

            &::after {
              display: none;
            }
          }

          .stay_selection {
            width: 90%;
            padding: 10px 0 10px 10px;
            background-color: $light_grey;

            &::after {
              display: none;
            }

            &::before {
              content: "\f073";
              font-family: $font_awesome;
              font-size: 23px;
              color: $black;
            }

            .entry_date_wrapper, .departure_date_wrapper {
              label {
                display: block;
                font-size: 12px;
                text-align: center;
                margin-bottom: 5px;
                text-transform:capitalize;
              }
            }

            .entry_date_wrapper {
              &::after {
                content: '';
                position: absolute;
                top: 45%;
                -webkit-transform: translateY(-50%);
                -moz-transform: translateY(-50%);
                -ms-transform: translateY(-50%);
                -o-transform: translateY(-50%);
                transform: translateY(-50%);
                right: -1px;
                height: 40px;
                width: 1px;
                background: $separator_color;
              }
            }
          }

          .guest_selector {
            width: 90%;
            padding: 10px 0 10px 10px;
            background-color: $light_grey;
            margin-top: 15px;

            &::before {
              content: "\f0c0";
              font-family: $font_awesome;
              font-size: 23px;
              color: $black;
              position: absolute;
              top: 30%;
            }

            &::after {
              display: none;
            }

            label {
              display: block;
              font-size: 12px;
              text-align: center;
              margin-bottom: 5px;
            }
          }

          .wrapper_booking_button {
            .submit_button {
              width: 100%;
            }

            .promocode_wrapper {
              width: 90%;
              margin: 15px auto;
              border: 1px solid $corporate_2;
            }
          }

          .hotel_selector {
            bottom: unset;
            width: 100%;
            top: 15px;
            left: 0;
            right:0;
            max-height: unset;
          }

          .room_list_wrapper {
            z-index: 1;
            right: 0px;
            width: 345px;
          }

        }
      }
    }
  }
  @import "datepicker_mobile";

}
