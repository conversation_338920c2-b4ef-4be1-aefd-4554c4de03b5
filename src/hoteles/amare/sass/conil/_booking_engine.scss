#full_wrapper_booking {
  position: fixed;
  padding: 20px 0;
  width: 100%;
  min-width: 1140px;
  background: rgba(0, 0, 0, 0.3);
  z-index: 50;
  top: auto;
  bottom: 180px;

  #full-booking-engine-html-7 {
    .booking_form {
      margin: 0;
    }
  }

  .selectricItems {
    width: 100% !important;
  }

  &.fixed_booking {
    position: fixed;
    top: 0 !important;
  }

  /*======== Booking Widget =======*/

  div#wrapper_booking.home {
    bottom: 70px;
  }

  .booking_widget {
    position: absolute;
    left: 0;
  }

  .room_list_wrapper .children_selector, .room_list_wrapper .babies_selector {
    position: relative;

    &:hover {
      .tooltip {
        display: block;
      }
    }

    .tooltip {
      position: absolute;
      bottom: calc(100% + 10px);
      background-color: #dfe4dd;
      left: 0;
      right: 0;
      width: 90%;
      margin: auto;
      box-sizing: border-box;
      padding: 5px;
      font-size: 10px;
      cursor: pointer;
      line-height: 16px;
      font-weight: 600;
      text-align: center;
      display: none;

      &:before {
        content: '';
        width: 10px;
        height: 10px;
        margin: auto;
        position: absolute;
        left: 0;
        right: 0;
        top: 100%;
        display: block;
        border: 10px solid transparent;
        border-color: #dfe4dd transparent transparent transparent;
      }
    }
  }

  .booking_form_title .best_price {
    display: none;
    color: white;
    font-size: 16px;
    padding: 20px;
    font-weight: 600;
    text-align: center;
  }
  .promocode_header p.first_offer_name {
    color: white;
  }
  .booking_widget .date_box, .booking_widget .selectricWrapper, #full_wrapper_booking_widget_popup .date_box, #full_wrapper_booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #full_wrapper_booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 35px;
    background: transparent;
  }

  .room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
    height: auto;
    float: left;
    box-sizing: border-box;
    background: white;
    width: 120px !important;
  }

  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1 {
    line-height: 15px !important;
    font-size: 14px !important;
  }

  .wrapper-new-web-support.booking_form_title {
    //background: gray!important;
    text-align: center;
    background: none;
    opacity: 1;
    margin-top: 7px;
    font-size: 13px !important;

    .web_support_label_2 {
      display: inline-block;
      margin: 0 10px;
    }

    .phone_support_image {
      display: none;
    }
  }

  .date_box.entry_date, .date_box.departure_date {
    margin-top: 6px;

    .date_year {
      display: none;
    }

    .date_day {
      border-bottom: 0 !important;
      font-family: sans-serif;
      font-weight: lighter;
      font-size: 16px !important;
      color: black;
    }
  }

  .selectricWrapper {
    width: 100% !important;

    .selectric {
      margin-top: 0;
      font-family: Roboto, sans-serif;
    }
  }

  .promocode_text {
    display: none;
  }

  .stay_selection {
    display: inline-block;
    vertical-align: top;
    float: left;

    .entry_date_wrapper, .departure_date_wrapper {
      display: inline-block;
      vertical-align: top;
      float: left;
      margin-right: 5px;
      border: 0 !important;
      background: white;
      width: 212px;
      height: 47px;
    }

    .departure_date_wrapper {
      border-left: 0;
      border-right: 0;
    }

    .nights_number_wrapper {
      display: inline-block;
      width: 95px;
      float: left;
      vertical-align: top;
      border-top: 1px solid lightgrey;
    }
  }

  .rooms_number_wrapper {
    float: left;
    vertical-align: top;
    width: 190px;
    height: 47px;
    margin-right: 5px;
    background: white;
    position: relative;

    .rooms_number {
      padding-left: 45px;
      box-sizing: border-box;
      background: url(/static_1/images/booking_5/rooms_number.png) no-repeat center left;
      background-position-y: 40%;
    }
  }

  .room_list_wrapper {
    vertical-align: top;
    float: left;
    //background: white;
    //border-bottom: 1px solid lightgrey;
    //border-top: 1px solid lightgrey;
    width: auto;
    margin-right: 5px;
    position: relative;
    //left: 466px;
    //top: 66px;

    .room {
      background: white;
      height: auto !important;

      &.room1, &.room2, &.room3 {
        .children_selector {
          border-right: 1px solid lightgray;
          position: relative;
        }

        .babies_selector {
          position: relative;
        }

        .adults_selector {
          position: relative;
          margin-right: 0;
        }
      }

      &.room3, &.room2 {
        border-bottom: 1px solid lightgray;
      }

      &.room3 {
        border-top: 0;
      }
    }
  }

  .wrapper_booking_button {
    display: inline-block;
    width: auto;
    float: left;
    height: 47px;

    label.promocode_label {
      display: none;
    }

    .promocode_wrapper {
      display: inline-block;
      vertical-align: top;
      float: left;
      width: 125px;
      margin-right: 5px;
      height: 47px;
      background: transparent;
      border: 1px solid white !important;
      position: relative;
      padding-top: 5px;
    }

    .submit_button {
      width: 150px;
      height: 67px;
      margin-top: -10px;
      display: inline-block;
      vertical-align: top;
      float: left;
      color: white;
      background: rgba(0, 42, 68, .8);
      font-weight: 500;
      font-family: Gudea, sans-serif;
      position: absolute;
      left: 100%;
      //font-size: 27px;
      font-size: 18px;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.cheapest_month_selector {
  background: rgba(0, 42, 68, 0.8) !important;
}

body.home_section #full_wrapper_booking.fixed_booking {
  width: 100%;
}

.babies_selector {
  width: 120px;
  display: inline-block;
  padding: 7px 10px 5px;
  box-sizing: border-box;

  label {
    font-family: 'Montserrat', sans-serif;
    text-transform: uppercase;
    font-size: 10px;
  }
}

#full_wrapper_booking label {
  cursor: pointer;
}

/*===== Slider container ====*/
#slider_container {
  position: relative;
}

input.promocode_input {
  margin-top: 0;
  font-family: sans-serif;
  color: white;
  background: transparent;
  text-align: center;

  &::-webkit-input-placeholder {
    color: white;
    font-size: 11px;
    font-weight: lighter;
    font-family: sans-serif;
    text-transform: capitalize;
  }
  &::-moz-placeholder {
    color: white;
    font-size: 11px;
    font-weight: lighter;
    font-family: sans-serif;
    text-transform: capitalize;
  }
  &:-ms-input-placeholder {
    color: white;
    font-size: 11px;
    font-weight: lighter;
    font-family: sans-serif;
    text-transform: capitalize;
  }
  &:-moz-placeholder {
    color: white;
    font-size: 11px;
    font-weight: lighter;
    font-family: sans-serif;
    text-transform: capitalize;
  }
}

.selectricWrapper .selectric .label {
  font-weight: lighter;
  font-size: 16px;
  line-height: 37px;
  color: #002a44;
  font-family: Gudea, sans-serif;
  font-weight: 400;
}

#full_wrapper_booking .room_list label {
  display: none;
}

#full_wrapper_booking {
  .rooms_number {
    .selectricItems {
      width: 153px !important;
      margin-left: -10px !important;
    }
  }
}

div#full_wrapper_booking {
  position: absolute;
  top: auto;
  left: 0;
  right: 0;
  margin: auto;
  background: rgba(35, 82, 124, .7);
  z-index: 99 !important;
  padding: 0;
  -webkit-transition: width 0.5s;
  -moz-transition: width 0.5s;
  -ms-transition: width 0.5s;
  -o-transition: width 0.5s;
  transition: width 0.5s;
  min-width: auto;
  width: 1030px;
}

body.inner_section div#full_wrapper_booking {
  top: 125px;
  bottom: auto;
  width: 100%;
  box-sizing: border-box;
}

#full_wrapper_booking label {
  display: none;
  position: absolute;
  bottom: 106%;
  left: 0;
  color: white;
  margin-left: 0;
  font-family: Roboto, sans-serif;
}

.hotel_selector {
  display: none;
}

.destination_wrapper {
  display: inline-block;
  float: left;
  margin-right: 5px;
  cursor: pointer;

  input {
    height: 46px;
    box-sizing: border-box;
    font-weight: lighter;
    font-size: 17px;
    padding-left: 15px;
    cursor: pointer;
    color: #646464;
    font-family: Roboto, sans-serif;
  }
}

//Datepicker selector
.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
  .datepicker_ext_inf_sd, .datepicker_ext_inf_ed {
    td.highlight {
      background: #6485a2 !important;

      a {
        background: transparent !important;
        color: white !important;
      }
    }

    .ui-datepicker td.last-highlight-selection {
      a {
        background: none !important;
      }
    }

    .ui-state-active {
      background: rgba(0, 42, 68, 0.8) !important;
    }
  }
}

body .ui-datepicker-start_date .ui-state-default {
  background: rgba(0, 42, 68, 0.8) !important;
}

.calendar_popup_wrapper .popup_month_selector .month_select_element {
  margin-bottom: 1px;
}

#full_wrapper_booking {
  position: fixed;
  padding: 20px 0;
  width: 100%;
  min-width: 1140px;
  background: rgba(0, 0, 0, 0.3);
  z-index: 50;
  top: 120px;

  &.fixed_booking {
    position: fixed;
    top: 0 !important;
  }

  /*======== Booking Widget =======*/

  div#wrapper_booking.home {
    bottom: 70px;
  }

  .booking_widget {
    position: absolute;
    left: 0;
  }

  .booking_form_title .best_price {
    display: none;
    color: white;
    font-size: 16px;
    padding: 20px;
    font-weight: 600;
    text-align: center;
  }
  .promocode_header p.first_offer_name {
    color: white;
  }
  .booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 35px;
    background: transparent;
  }

  .room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
    height: auto;
    float: left;
    box-sizing: border-box;
    background: white;
    width: 120px !important;
  }

  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1 {
    line-height: 15px !important;
    font-size: 14px !important;
  }

  .wrapper-new-web-support.booking_form_title {
    //background: gray!important;
    text-align: center;
    background: none;
    opacity: 1;
    margin-top: 7px;
    font-size: 13px !important;

    .web_support_label_2 {
      display: inline-block;
      margin: 0 10px;
    }

    .phone_support_image {
      display: none;
    }
  }

  .date_box.entry_date, .date_box.departure_date {
    margin-top: 6px;

    .date_year {
      display: none;
    }

    .date_day {
      border-bottom: 0 !important;
      font-family: sans-serif;
      font-weight: lighter;
      font-size: 16px !important;
      color: black;
    }
  }

  .selectricWrapper {
    width: 100% !important;

    .selectric {
      margin-top: 0;
      font-family: Roboto, sans-serif;
    }
  }

  .promocode_text {
    display: none;
  }

  .stay_selection {
    display: inline-block;
    vertical-align: top;
    float: left;

    .entry_date_wrapper, .departure_date_wrapper {
      display: inline-block;
      vertical-align: top;
      float: left;
      margin-right: 5px;
      border: 0 !important;
      background: white;
      width: 212px;
      height: 47px;
    }

    .departure_date_wrapper {
      border-left: 0;
      border-right: 0;
    }

    .nights_number_wrapper {
      display: inline-block;
      width: 95px;
      float: left;
      vertical-align: top;
      border-top: 1px solid lightgrey;
    }
  }

  .rooms_number_wrapper {
    float: left;
    vertical-align: top;
    width: 190px;
    height: 47px;
    margin-right: 5px;
    background: white;
    position: relative;

    .rooms_number {
      padding-left: 45px;
      box-sizing: border-box;
      background: url(/static_1/images/booking_5/rooms_number.png) no-repeat center left;
      background-position-y: 40%;
    }
  }

  .room_list_wrapper {
    vertical-align: top;
    float: left;
    width: auto;
    margin-right: 5px;
    position: relative;

    .room_list {
      list-style: none;
      margin: 0;
    }

    .room {
      background: white;
      height: auto !important;
      margin: 0;

      &.room1, &.room2, &.room3 {
        .children_selector {
          border-right: 1px solid lightgray;
          position: relative;
        }

        .babies_selector {
          position: relative;
        }

        .adults_selector {
          position: relative;
          margin-right: 0;
        }
      }

      &.room3, &.room2 {
        border-bottom: 1px solid lightgray;
      }

      &.room3 {
        border-top: 0;
      }
    }
  }

  .wrapper_booking_button {
    display: inline-block;
    width: auto;
    float: left;
    height: 47px;

    label.promocode_label {
      display: none;
    }

    .promocode_wrapper {
      display: inline-block;
      vertical-align: top;
      float: left;
      width: 125px;
      margin-right: 5px;
      height: 47px;
      background: transparent;
      border: 1px solid white !important;
      position: relative;
      padding-top: 5px;
    }

    .submit_button {
      width: 150px;
      height: 67px;
      margin-top: -10px;
      display: inline-block;
      vertical-align: top;
      float: left;
      color: white;
      background: #48A1AB;
      font-weight: 500;
      font-family: Gudea, sans-serif;
      position: absolute;
      left: 100%;
      margin-bottom: 0;
      font-size: 18px;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.cheapest_month_selector {
  background: rgba(0, 42, 68, 0.8) !important;
}

body.home_section #full_wrapper_booking.fixed_booking {
  width: 100%;
}

.babies_selector {
  width: 120px;
  display: inline-block;
  padding: 7px 10px 5px;
  box-sizing: border-box;
  background-color: white;

  label {
    font-family: 'Montserrat', sans-serif;
    text-transform: uppercase;
    font-size: 10px;
  }
}

#booking label {
  cursor: pointer;
}

/*===== Slider container ====*/
#slider_container {
  position: relative;
}

input.promocode_input {
  margin-top: 0;
  font-family: sans-serif;
  color: white;
  background: transparent;
  text-align: center;
  margin: 0;
  box-sizing: border-box;

  &::-webkit-input-placeholder {
    color: white;
    font-size: 11px;
    font-weight: lighter;
    font-family: sans-serif;
    text-transform: capitalize;
  }
  &::-moz-placeholder {
    color: white;
    font-size: 11px;
    font-weight: lighter;
    font-family: sans-serif;
    text-transform: capitalize;
  }
  &:-ms-input-placeholder {
    color: white;
    font-size: 11px;
    font-weight: lighter;
    font-family: sans-serif;
    text-transform: capitalize;
  }
  &:-moz-placeholder {
    color: white;
    font-size: 11px;
    font-weight: lighter;
    font-family: sans-serif;
    text-transform: capitalize;
  }
}

.selectricWrapper .selectric .label {
  font-weight: lighter;
  font-size: 16px;
  color: #585d63;
  font-family: Gudea, sans-serif;
  font-weight: 400;
}

#booking .room_list label {
  display: none;
}

#full_wrapper_booking {
  .selectric-rooms_number {
    .selectricItems {
      width: 100% !important;
    }
  }

  .rooms_number {
    .selectricItems {
      width: 153px !important;
      margin-left: -10px !important;
    }
  }
}

div#full_wrapper_booking {
  position: absolute;
  top: auto;
  left: 0;
  right: 167px;
  margin: auto;
  background: #31747A;
  z-index: 1001;
  padding: 0;
  -webkit-transition: width 0.5s;
  -moz-transition: width 0.5s;
  -ms-transition: width 0.5s;
  -o-transition: width 0.5s;
  transition: width 0.5s;
  min-width: auto;
  width: 905px;

  &.widget_with_babies {
    width: 1030px;
  }
}

body.inner_section div#full_wrapper_booking {
  top: 125px;
  bottom: auto;
  width: 100%;
  box-sizing: border-box;
}

#booking label {
  display: none;
  position: absolute;
  bottom: 106%;
  left: 0;
  color: white;
  margin-left: 0;
  font-family: Roboto, sans-serif;
}

.hotel_selector {
  display: none;
}

.destination_wrapper {
  display: inline-block;
  float: left;
  margin-right: 5px;
  cursor: pointer;

  input {
    height: 46px;
    box-sizing: border-box;
    font-weight: lighter;
    font-size: 17px;
    padding-left: 15px;
    cursor: pointer;
    color: #646464;
    font-family: Roboto, sans-serif;
  }
}

//Datepicker selector
.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
  .datepicker_ext_inf_sd, .datepicker_ext_inf_ed {
    td.highlight {
      background: #6485a2 !important;

      a {
        background: transparent !important;
        color: white !important;
      }
    }

    .ui-datepicker td.last-highlight-selection {
      a {
        background: none !important;
      }
    }

    .ui-state-active {
      background: rgba(0, 42, 68, 0.8) !important;
    }
  }
}

body .ui-datepicker-start_date .ui-state-default {
  background: rgba(0, 42, 68, 0.8) !important;
}

#full-booking-engine-html-7 .selectric .button {
  margin-top: 7px;
}

//*======= Inner Sections ======*//
#booking-mobile-desktop {
  #b-booking {
    height: 65px;

    #full_wrapper_booking {
      position: fixed;
      top: auto !important;
      background: none;
      width: auto;
      left: auto;
      right: auto;
      bottom: auto;

      .wrapper_booking_button .submit_button {
        height: 48px;
        top: auto;
        position: relative;
        display: inline-block;
        left: auto;
        vertical-align: middle;
        margin-top: 0;
      }
    }
  }
}

.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
  z-index: 99999;
}

#full_wrapper_booking .rooms_number_wrapper {
  width: 170px;
}

html[lang=es-ES] {
  #full_wrapper_booking .room_list_wrapper .adults_selector, #full_wrapper_booking .room_list_wrapper .children_selector {
    width: 150px !important;
  }
}

html[lang=fr-FR] {
  #full_wrapper_booking .room_list_wrapper .adults_selector, #full_wrapper_booking .room_list_wrapper .children_selector {
    width: 130px !important;
  }
}

html[lang=de-DE] {
  div#full_wrapper_booking {
    width: 1050px;
  }

  #full_wrapper_booking .room_list_wrapper .adults_selector, #full_wrapper_booking .room_list_wrapper .children_selector {
    width: 170px !important;
  }

  #full_wrapper_booking .rooms_number_wrapper {
    width: 150px !important;
  }
}

@media only screen and (max-width: 1000px) {
  .specific_month_selector {
    display: none !important;
  }
}