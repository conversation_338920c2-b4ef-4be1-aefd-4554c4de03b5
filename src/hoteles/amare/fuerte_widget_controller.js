$ = jQuery;

var bookingWidgetController = function () {
    return {
        config: {
            _custom_hotel_namespace: false,
            _custom_hotel_domain: false,
            messages: {
                es: {
                    kids_age: "De 3 a 12 años",
                    babies_age: "<PERSON><PERSON> de 3 años (cuna bajo petición)"
                },
                en: {
                    kids_age: "From 3 to 12 years",
                    babies_age: "Infants aged under 3 (cot on request)"
                },
                de: {
                    kids_age: "Von 3 bis 12 Jahre",
                    babies_age: "Kinder unter 3 Jahren (kinderbett auf anfrage)"
                },
                fr: {
                    kids_age: "De 3 à 12 ans",
                    babies_age: "Moins de 3 ans (berceau sous demande)"
                },
                nl: {
                    kids_age: "Van 3 tot 12 jaar oud",
                    babies_age: "Onder de 3 jaar (kinderbed op aanvraag)"
                }
            }
        },

        init: function () {
            this.prepare_dependencies();
        },

        is_mobile: function() {
            if(navigator.userAgent.match(/Android/i)
              || navigator.userAgent.match(/webOS/i)
              || navigator.userAgent.match(/iPhone/i)
              || navigator.userAgent.match(/iPod/i)
              || navigator.userAgent.match(/iPad/i)
              || navigator.userAgent.match(/BlackBerry/i)
              || navigator.userAgent.match(/Windows Phone/i)) {
                return true;
            }

            return false;
        },

        prepare_dependencies: function () {
            jQuery('head').append('<link rel="stylesheet" type="text/css" href="{{base_url}}/static_1/plugins/dates-selector/css/datepicker_ext_inf.css"/>');
            var target_css = "{{base_url}}/static_1/css/fuerte/booking_widget_fuerte.css?v=10.85";
            jQuery('head').append(jQuery('<link rel="stylesheet" type="text/css" />').attr('href', target_css));
            var target_fawesome = "//maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css";
            jQuery('head').append(jQuery('<link rel="stylesheet" type="text/css" />').attr('href', target_fawesome));
            jQuery('head').append('<link rel="stylesheet" type="text/css" href="{{base_url}}/static_1/plugins/pricescalendar/styles.css?v=1.1"/>');
            jQuery('head').append('<link rel="stylesheet" type="text/css" href="{{base_url}}/static_1/css/datepicker.redmond/jquery-ui-1.8.16.custom.min.css"/>');
            jQuery.getScript("{{base_url}}/static_inj/lib/jquery-ui-1.10.1.custom.min.modified.js").done(function(){
                $ = jQuery;
                jQuery.getScript("{{base_url}}/static_1/js/mainWebSkeletonAux_injection.js");
                jQuery.getScript("{{base_url}}/static_1/lib/selectric/jquery.selectric.1.8.js").done(function () {
                    var language_widget = jQuery("html").attr("lang").replace(/-.*/, '');
                    jQuery.getScript("{{base_url}}/static_1/js/datepicker/jquery.ui.datepicker-" + language_widget + ".js?v=1").complete(function () {
                        bookingWidgetController.get_booking_widget_html();
                    });
                });
            });
        },

        get_booking_widget_html: function () {
        if(jQuery(".buscador-reservas").length){
            var language_widget = jQuery("html").attr("lang").replace(/-.*/, ''),
                target_widget_url = "{{base_url}}/fuertewidget/" + language_widget,
                paraty_widget_namespace = bookingWidgetController.get_namespace_widget();

            if (paraty_widget_namespace && ! paraty_widget_namespace == 'custom-paraty-format') target_widget_url = target_widget_url + "?namespace=" + paraty_widget_namespace;

            jQuery.get(target_widget_url, function (e) {
                parent_wrapper_widget = jQuery("<div id='full_wrapper_booking'></div>").html(e);
            }).done(function () {
                jQuery(".buscador-reservas").html(parent_wrapper_widget);

                jQuery(".room.room1 .children_selector").append(jQuery("<div class='tooltip'></div>").html(bookingWidgetController.config.messages[language_widget].kids_age));
                jQuery(".room.room1 .babies_selector").append(jQuery("<div class='tooltip'></div>").html(bookingWidgetController.config.messages[language_widget].babies_age));

                jQuery.getScript("{{base_url}}/static_1/scripts/hotel_selector_2.js?v=1");

                if(jQuery(".destination_wrapper").length) {
                    jQuery("#full_wrapper_booking").addClass("with_hotel_selector");
                }

                jQuery.getScript("{{base_url}}/static_1/lib/jquery-i18n-1.1.1/jquery.i18n.js").done(function () {
                    jQuery.getScript("{{base_url}}/static_1/i18n/messages_" + language_widget + ".js?v=1").done(function () {
                        jQuery.i18n.load(messages);
                    });
                });

                _set_datepicker_regional(jQuery);
                jQuery.getScript("{{base_url}}/static_1/plugins/pricescalendar/calendar.plugin.js?v=1.1").done(function(){
                    _set_datepicker_regional(jQuery);
                    jQuery.getScript("{{base_url}}/static_1/plugins/dates-selector/datepicker_v1.js?v=1.5").done(function () {
                        DP_extend_info.config.booking_version = '7';
                        DP_extend_info.config.hotel_path_endpoint = '{{base_url}}';

                        if (bookingWidgetController.config._custom_hotel_domain){
                            DP_extend_info.config.hotel_path_endpoint = bookingWidgetController.config._custom_hotel_domain;
                        }

                        DP_extend_info.init();

                        jQuery.getScript("{{base_url}}/static_1/scripts/booking_7.js?v=1.1").done(function () {
                            bookingWidgetController.prepare_booking_button();
                            bookingWidgetController.prepare_rooms_modifications();
                            jQuery("select.room_selector").unbind("change");
                            jQuery(".days_number_datepicker").html("1");
                            jQuery(".room_selector").selectric({disableOnMobile: false});
                            jQuery(".rooms_number").selectric({disableOnMobile: false});

                            jQuery("#full_wrapper_booking select.room_selector").change(function () {
                                bookingWidgetController.set_occupancy_number();
                            });
                            _set_datepicker_regional(jQuery);

                            if(jQuery(".destination_wrapper").length) {
                                if (bookingWidgetController.config._custom_hotel_namespace) {
                                    jQuery("#" + bookingWidgetController.config._custom_hotel_namespace).trigger('click');
                                }
                            }

                            if (DP_extend_info.config.min_date) {
                                DP_extend_info.format_dates(jQuery.datepicker.formatDate("dd/mm/yy", DP_extend_info.config.min_date));
                            }

                            var promocode_hidden_input = jQuery("#paraty_promocode");
                            if (promocode_hidden_input.length) {
                                var promocode_applied_mesage = jQuery("<div></div>").addClass('promocode_applied_message').html(jQuery.i18n._('descuento_aplicado')),
                                    full_wrapper_booking = jQuery(".booking_form.paraty-booking-form"),
                                    promocode_input = full_wrapper_booking.find(".promocode_input");

                                promocode_input.addClass("auto_applied").val(promocode_hidden_input.val());
                                full_wrapper_booking.find(".wrapper_booking_button ").addClass('promocode_autoapplied');
                                promocode_applied_mesage.insertAfter(promocode_input);
                            }

                            var hide_hotel_selector_input = jQuery("#paraty_hotel_selector_hidden");
                            if (hide_hotel_selector_input.length){
                                var engine_wrapper_selector = jQuery("#full_wrapper_booking");
                                engine_wrapper_selector.find(".destination_wrapper").addClass('hide');
                                engine_wrapper_selector.removeClass('with_hotel_selector');
                            }
                        });
                    });
                });

                if (jQuery(".babies_selector").length) {
                    jQuery("#full_wrapper_booking").addClass("widget_with_babies");
                }

                bookingWidgetController.adding_room_tag_selector();
                bookingWidgetController.adding_adults_tag_selector();
                bookingWidgetController.adding_kids_tag_selector();
                bookingWidgetController.adding_babies_tag_selector();
                bookingWidgetController.prepare_rooms_modifications();
                bookingWidgetController.bind_click_buttons();
                bookingWidgetController.hide_hotels_in_selector();
                var has_promocode_at_url = getUrlParameter('promocode');
                if (has_promocode_at_url) {
                    jQuery(".promocode_wrapper .promocode_input").val(has_promocode_at_url);
                }
            });
        }
        },

        get_namespace_widget: function(){
            var paraty_widget_namespace = jQuery("#paraty_widget_namespace"),
                paraty_widget_internal_url = jQuery("#paraty_widget_url");

            if (paraty_widget_namespace.length){
                this.config._custom_hotel_namespace = paraty_widget_namespace.val();
                if (paraty_widget_namespace.val() == 'custom-paraty-format') return 'custom-paraty-format';
                this.config._custom_hotel_domain = paraty_widget_internal_url.val();
                return this.config._custom_hotel_namespace;
            }
        },

        bind_click_buttons: function(){
            jQuery("a.btn-submit[href*='booking']").click(function(e){
                e.preventDefault();
                jQuery(".start_end_date_wrapper").trigger('click');
            });

            jQuery(".paraty_listener").click(function(){
                bookingWidgetController.apply_clicked_options($(this))
            });

            jQuery(".hotel_selector_option").click(function(){
                DP_extend_info.config.datepicker_sd_wrapper.datepicker('destroy');
                DP_extend_info.create_datepicker_containers();

                var open_hotel_property = jQuery(this).attr('data-open-hotel'),
                    external_open = jQuery(this).attr("data-open-external"),
                    target_max_date = new Date("2050-12-31");

                if(open_hotel_property) {
                    var json_hotel_property = JSON.parse(open_hotel_property);
                    DP_extend_info.config.period_closed = json_hotel_property;
                    var target_min_date;
                     for (var n=0;n<json_hotel_property.length;n++){
                        var closed_hotel = json_hotel_property[n].close,
                            open_hotel = json_hotel_property[n].open;
                        if (closed_hotel && new Date(closed_hotel)<= new Date()){
                            if (open_hotel && new Date(open_hotel)>= new Date())
                                target_min_date = new Date(open_hotel);
                            if (open_hotel && new Date() > new Date(open_hotel))
                                target_min_date = new Date();
                        }
                    }
                    if (!target_min_date) {target_min_date = new Date()}
                    DP_extend_info.config.datepicker_sd_wrapper.datepicker('option', 'minDate', target_min_date);
                    bookingWidgetController.set_dates(target_min_date, target_max_date);

                } else {
                    var today_date = new Date(),
                        future_date = new Date("2050-12-31");

                    bookingWidgetController.set_dates(today_date, future_date);
                }

                if (external_open) {
                    jQuery(".booking_form").attr("target", "_blank");
                } else {
                    jQuery(".booking_form").removeAttr("target");
                }
            });

            this.prepare_user_info();

            bookingWidgetController.slide_up_click_outside(".room_list_wrapper", ".guest_selector");
            bookingWidgetController.slide_up_click_outside(".hotel_selector", ".destination_wrapper");
        },

        apply_clicked_options: function(clicked_element){
            var hotel_selection = clicked_element.attr('hotel_selection'),
                start_date = clicked_element.attr('start_date');

            if (hotel_selection) {
                try {
                    jQuery("#" + hotel_selection).click();
                } catch (e) {
                    console.log("[Paraty] Can't set the default hotel");
                }
            }

            if (start_date) {
                try {
                    DP_extend_info.set_datepicker_start_date(start_date);
                } catch (e) {
                    console.log("[Paraty] Can't set the start date");
                }
            }
        },

        set_dates: function(date1, date2) {
            var tomorrow = new Date(),
                today = new Date();
                // date_in_input = $("#full_wrapper_booking .entry_input").val();

            if (date1 < today) {
                date1 = today;
            }

            tomorrow.setDate(date1.getDate() + 1);
            DP_extend_info.config.datepicker_sd_wrapper.datepicker('option', 'minDate', date1);
            DP_extend_info.config.datepicker_sd_wrapper.datepicker('option', 'maxDate', date2);
            DP_extend_info.config.datepicker_ed_wrapper.datepicker('option', 'minDate', tomorrow);
            DP_extend_info.config.datepicker_ed_wrapper.datepicker('option', 'maxDate', date2);
            DP_extend_info.format_dates(jQuery.datepicker.formatDate("dd/mm/yy", date1));
            DP_extend_info.set_datepicker_start_date(jQuery.datepicker.formatDate("dd/mm/yy", date1));

            // if(date_in_input) {
            //     var date_selected = jQuery.datepicker.parseDate("dd/mm/yy", date_in_input);
            //
            //     if (date_selected < date1 || date_selected > date2) {
            //         DP_extend_info.format_dates(jQuery.datepicker.formatDate("dd/mm/yy", date1));
            //         DP_extend_info.set_datepicker_start_date(jQuery.datepicker.formatDate("dd/mm/yy", date1));
            //     }
            // }
        },

        prepare_user_info: function () {
            var exists_user_data = jQuery("#paraty_widget_user_data");
            if (!exists_user_data.length || !exists_user_data.val()) return;

            jQuery(".paraty-booking-form").each(function() {
                var input_to_create = jQuery("<input name='paraty-widget-user' type='hidden' id='paraty-widget-user'>").val(exists_user_data.val());
               jQuery(this).append(input_to_create);
            });
        },

        prepare_rooms_modifications: function () {
            var _room1 = jQuery("#full_wrapper_booking .room1");
            var _room2 = jQuery("#full_wrapper_booking .room2");
            var _room3 = jQuery("#full_wrapper_booking .room3");
            jQuery("#full_wrapper_booking select.rooms_number").unbind("change");
            jQuery("#full_wrapper_booking select.rooms_number").change(function (event) {
                var number = jQuery(this).val();

                if (number == 1) {
                    _room2.hide();
                    _room3.hide();
                } else if (number == 2) {
                    _room2.show();
                    _room3.hide();
                } else {
                    _room2.show();
                    _room3.show();
                }

                bookingWidgetController.set_occupancy_number();
            });
        },

        prepare_booking_button: function () {
            jQuery("#full-booking-engine-html .paraty-booking-form .wrapper_booking_button .submit_button").unbind("click").click(function () {
                try {
                    ga(function (tracker) {
                        client_id = tracker.get('clientId');
                    });
                    jQuery('<input>').attr({
                        type: 'hidden',
                        id: '_ga',
                        name: '_ga',
                        value: client_id
                    }).appendTo(jQuery(this));
                }
                catch (err) {
                    console.log("Google Analytics defined incorrectly (May use a old version)");
                }
            });
        },

        set_occupancy_number: function(){
           var number_of_rooms = jQuery("select[name='numRooms']").val(),
               adults_number = 0,
               kids_number = 0,
               babies_number = 0;

           if (number_of_rooms){
              for (var room_loop = 1;room_loop <= number_of_rooms;room_loop++){
                 var actual_select_adults = jQuery("select[name='adultsRoom" + room_loop + "']").val(),
                     actual_select_kids = jQuery("select[name='childrenRoom" + room_loop + "']").val(),
                     actual_select_babies = jQuery("select[name='babiesRoom" + room_loop + "']").val();

                 if(actual_select_adults || actual_select_kids || actual_select_babies){
                    adults_number += parseInt(actual_select_adults);
                    kids_number += parseInt(actual_select_kids);
                    babies_number += parseInt(actual_select_babies);
                 }
              }
           }

           var target_placeholder = jQuery(".guest_selector .placeholder_text"),
               placeholder_string = adults_number + " - " + kids_number + " - " + babies_number;

           target_placeholder.html(placeholder_string);
        },

        adding_room_tag_selector: function () {
            jQuery("select.rooms_number option").each(function (index, element) {
                if (parseInt(jQuery(element).val()) > 1) {
                    jQuery(element).text(jQuery(element).text() + " " + jQuery("#rooms_tag").val());
                } else {
                    jQuery(element).text(jQuery(element).text() + " " + jQuery("#room_tag").val());
                }
            });
            jQuery("select.rooms_number").selectric("refresh");
        },

        adding_adults_tag_selector: function () {
            jQuery(".adults_selector select option").each(function (index, element) {
                if (parseInt(jQuery(element).val()) > 1) {
                    jQuery(element).text(jQuery(element).text() + " " + jQuery("#adults_tag").val());
                } else {
                    jQuery(element).text(jQuery(element).text() + " " + jQuery("#adult_tag").val());
                }
            });
            jQuery(".adults_selector select").selectric("refresh");
        },

        adding_kids_tag_selector: function () {
            jQuery(".children_selector select option").each(function (index, element) {
                if (parseInt(jQuery(element).val()) > 1) {
                    jQuery(element).text(jQuery(element).text() + " " + jQuery("#kids_tag").val());
                } else {
                    jQuery(element).text(jQuery(element).text() + " " + jQuery("#kid_tag").val());
                }
            });
            jQuery(".children_selector select").selectric("refresh");
        },

        adding_babies_tag_selector: function () {
            jQuery(".babies_selector select option").each(function (index, element) {
                if (parseInt(jQuery(element).val()) > 1) {
                    jQuery(element).text(jQuery(element).text() + " " + jQuery("#babies_tag").val());
                } else {
                    jQuery(element).text(jQuery(element).text() + " " + jQuery("#baby_tag").val());
                }
            });
            jQuery(".babies_selector select").selectric("refresh");
        },

        hide_hotels_in_selector: function() {
            var hotels_to_hide = $("#paraty_hide_hotels").val();

            if (hotels_to_hide){
                hotels_to_hide = hotels_to_hide.split(";");
                hotels_to_hide.forEach(function(element){
                    var hotel = $("#" + element);
                    if(hotel.length) {
                        hotel.hide();
                    }
                });
            }
        },

        slide_up_click_outside: function(element1, element2) {
            jQuery("html").click(function(event){
                var event_target = jQuery(event.target);
                if(!event_target.closest(element1).length && !event_target.closest(element2).length) {
                    jQuery(element1).slideUp();
                }
            });
        }
    }
}();

var getUrlParameter = function getUrlParameter(sParam) {
    var sPageURL = decodeURIComponent(window.location.search.substring(1)),
        sURLVariables = sPageURL.split('&'),
        sParameterName,
        i;

    for (i = 0; i < sURLVariables.length; i++) {
        sParameterName = sURLVariables[i].split('=');

        if (sParameterName[0] === sParam) {
            return sParameterName[1] === undefined ? true : sParameterName[1];
        }
    }
};
$(window).load(function () {
    $(".hotel_selector_option").click(function() {
        setTimeout(function(){

            $(".paraty-booking-form").each(function(){
                var submit_button = $(this).find(".submit_button");
                if (!submit_button.is(':visible') || submit_button.attr('disabled')){
                    submit_button.trigger('click');
                }
            });
        }, 1000);
    });
});

bookingWidgetController.init();