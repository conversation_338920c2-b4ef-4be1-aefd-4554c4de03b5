#floating_button_paraty,
.close_widget {
  display: none;
}

#widget_paraty {
  position: absolute;
  top: calc(100vh - 200px);
  width: 100%;
  z-index: 1021;
  font-family: $primary_font;
  @import "booking_engine_v7_defaults";
  @import "booking/selectric";

  &, * {
    box-sizing: border-box;
  }

  &:not(.initialized) {
    display: none;
  }


  @media (min-width: 1141px) {
    &.scrolled {
      position: fixed;
      top: 50px;
      background-color: white;

      .widget_buttons {
        display: none;
      }

      #full-booking-engine-html-7 {
        &::before {
          box-shadow: none!important;
        }
      }
    }
  }

  #full_wrapper_booking {
    width: 1100px;
    margin: 0 auto;
    border-radius: 70px;

    #full-booking-engine-html-7 {
      position: relative;
      display: block;
      padding: 0;

      @media (min-width: 1141px) {
        &::before {
          @include full_size;
          content: '';
          background: white;
          border-radius: 70px;
          @include box_shadow;
        }
      }

      .booking_form_title {
        display: none;
      }

      .calendar_root_wrapper {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 500;
        background: white;
        display: none;


        @media (max-height: 800px) {
          .calendar_app .price_calendar_wrapper:not(.is_mobile) {
            .full_container .calendar_wrapper {
              td.day_wrapper {
                height: 35px !important;

                .day {
                  font-size: 18px !important;
                }
              }
            }
          }
        }

        .close_calendar_app {
          position: absolute;
          width: 40px;
          height: 40px;
          border-width: 0;
          border-radius: 0;
          top: 50px;
          right: 50px;
          cursor: pointer;

          &:before, &:after {
            background: $text-color;
          }
        }

        .calendar_app {
          display: block !important;

          .price_calendar_wrapper {
            box-shadow: none;
            @include center_xy;
            top: 55%;

            .full_container {
              .selector_full_wrapper {
                .selector_wrapper {
                  .selector_label {
                    font-family: $primary_font;

                    letter-spacing: 1.2px;
                    color: $text-color;
                    font-weight: 500;
                  }

                  .selector {
                    .option {
                      color: $text-color;
                      font-family: $primary_font;

                      &:hover {
                        opacity: 0.7;
                      }
                    }
                  }
                }
              }

              .bottom_wrapper {
                .top {
                  .info_currency_wrapper {
                    .left_wrapper {
                      .notice_info_chart {
                        font-family: $primary_font;
                      }
                    }
                  }
                }
              }
            }

            &:not(.is_mobile) {
              .full_container {
                padding: 0 100px;

                .calendar_wrapper {
                  margin-top: 0;

                  .month_selector_wrapper {
                    .selector_label {
                      font-family: $primary_font;
                      font-size: 24px;
                      letter-spacing: 1.2px;
                      color: $text-color;
                      font-weight: 500;
                    }

                    .selector {
                      .option {
                        color: $text-color;
                        font-family: $primary_font;

                        &:hover {
                          opacity: 0.7;
                        }
                      }
                    }
                  }

                  .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper {
                    width: 57px;
                    height: 52px;

                    .popup_min_stay {
                      top: -60px;

                      &:after {
                        bottom: -6.4px;
                      }
                    }

                    .day {
                      font-family: $primary_font;
                      font-size: 20px;
                      letter-spacing: 0;
                      color: $text-color;
                      font-weight: 500;
                    }

                    .price {
                      font-family: $primary_font;
                      font-size: 12px;
                      font-weight: 300;
                    }

                    &.disabled,
                    &.closed {
                      .day,
                      .no_dispo_text {
                        color: #ADADAD;
                      }
                    }

                    &.selected {
                      .day {
                        color: white;
                      }
                    }
                  }
                }
              }
            }

            .bottom_wrapper {
              .top {
                .info_currency_wrapper .right_wrapper .toggle_chart {
                  opacity: 0.6;
                  pointer-events: none;
                }
              }

              .bottom {
                display: none;
              }
            }
          }
        }
      }

      .booking_form {
        position: relative;
        display: flex;
        justify-content: center;
        padding: 7px;

        .destination_wrapper {
          position: relative;
          @include input_base_styles;
          @include display_flex(nowrap);
          flex-direction: column;
          justify-content: center;
          align-items: stretch;
          padding: 25px 5px 25px 50px;
          background: transparent;
          width: 20%;
          cursor: pointer;

          .icon {
            position: absolute;
            top: 25px;
            left: 20px;
          }

          label {
            @include label_styles;
            cursor: pointer;
          }

          .destination_fieldo {
            .destination {
              @include option_styles($ff: $primary_font);
              width: 100%;
              background: none;
              border: none;
              text-overflow: ellipsis;
              padding-left: 0;

              &::-webkit-input-placeholder {
                @include option_styles($ff: $primary_font);
              }

              &::-moz-placeholder {
                @include option_styles($ff: $primary_font);
              }

              &::-moz-placeholder {
                @include option_styles($ff: $primary_font);
              }

              &::-ms-input-placeholder {
                @include option_styles($ff: $primary_font);
              }
            }
          }
        }

        .hotel_selector {
          display: none;
          position: fixed;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          z-index: 500;
          background: white;

          .center_xy {
            position: unset;
            transform: none !important;
          }

          .close {
            position: absolute;
            top: 55px;
            right: 50px;
            width: 40px;
            height: 40px;
            cursor: pointer;
            @include transition(all, .6s);

            @media (max-height: 800px) and (min-width: 1140px) {
              top: 25px;
            }

            &.active {
              -webkit-transform: rotate(180deg);
              -moz-transform: rotate(180deg);
              -ms-transform: rotate(180deg);
              -o-transform: rotate(180deg);
              transform: rotate(180deg);
            }

            &:before, &:after {
              background: $text-color;
            }

            &:hover {
              &:before, &:after {
                background: $text-color;
              }
            }
          }

          .hotel_selector_inner {
            width: 90vw;
            max-width: 1300px;
            margin: 130px auto 40px auto;
            display: none;
            opacity: 0;
            max-height: 68vh;
            overflow-y: auto;
            @include transition(opacity, .6s);

            @media (max-height: 800px) and (min-width: 1140px) {
              margin: 90px auto 40px auto;
            }

            &.active {
              display: block;
              opacity: 1;
            }

            ul {
              list-style: none;
              display: flex;
              justify-content: center;
              align-items: center;
              flex-wrap: wrap;

              li {
                margin: 20px;
                cursor: pointer;
                max-width: 200px;
                min-height: 260px;

                .picture_wrapper {
                  width: 200px;
                  height: 200px;
                  border-radius: 10px;
                  overflow: hidden;

                  img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    transition: all .4s;
                  }
                }

                .title_selector {
                  font-size: 16px;
                  letter-spacing: 0.8px;
                  cursor: pointer;
                  color: $text_color;
                  display: block;
                  margin-top: 15px;
                }

                &:hover {
                  .picture_wrapper {
                    img {
                      transform: scale(1.05);
                    }
                  }
                }
              }
            }
          }

          .all_hotels {
            text-align: center;
            margin-top: 40px;

            .booking_0_hotel_selection {
              display: inline-block;
              font-size: 15px;
              font-family: $primary_font;
              color: $text_color;
              letter-spacing: 0.75px;
              text-align: center;
              padding: 10px 20px 10px 30px;
              background: rgba($corporate_1, 0.08);
              cursor: pointer;
              border-radius: 50px;
              transition: all .4s;

              i {
                margin-right: 5px;
                position: relative;
                &::before {
                    content: "";
                    position: absolute;
                    top: -15px;
                    left: -16px;
                    width: 20px;
                    height: 20px;
                    background-image: url(https://cdn2.paraty.es/santorini-express/images/d1c432e4757f699);
                    background-size: cover;
                    background-position: center;
                    border-radius: 50%;
                    z-index: -1;
                    filter: brightness(35%);
                }
              }
            }

            &:hover {
              .booking_0_hotel_selection {
                opacity: 0.7;
              }
            }
          }
        }

        .stay_selection {
          position: relative;
          width: 32%;
          @include input_base_styles;
          @include display_flex(nowrap);
          justify-content: space-between;
          align-items: center;
          border-radius: 0;
          padding: 0 20px;
          cursor: pointer;
          @include separator;

          label {
            @include label_styles;
          }

          .dates_wrapper {
            @include display_flex(nowrap);
            justify-content: space-between;
            align-items: center;
            width: 100%;

            &:before {
              right: 50%;
            }
          }

          .entry_date_wrapper {
            position: relative;
          }

          .departure_date_wrapper {
            padding-left: 15px;
            @include separator;

            &::after {
              top: -2px;
              bottom: -2px;
            }
          }

          .entry_date_wrapper,
          .departure_date_wrapper {
            width: 50%;

            .date_box {
              text-align: left;

              .date_day, .date_year {
                @include option_styles($ff: $primary_font);
                border-bottom: none !important;
                font-family: $primary_font;
                text-transform: lowercase;
              }
            }
          }
        }


        .dates_selector_personalized {
          display: none;
        }

        .rooms_number_wrapper {
          display: none;
        }

        .guest_selector {
          @include input_base_styles;
          float: left;
          padding: 12px 15px 0;
          width: 16%;
          text-align: left;
          cursor: pointer;
          @include separator;

          &:after {
            bottom: 10px;
            font-weight: 100;
          }

          label {
            @include label_styles;
          }

          .placeholder_text {
            @include option_styles($ff: $primary_font);
            font-family: $primary_font;
            color: $corporate_2;
            font-weight: 400;
            width: 100%;
            text-transform: lowercase;
          }

          .guest_adults {
            margin-right: 6px;
          }

          b.button {
            display: none;
          }
        }

        .room_list_wrapper {
          position: fixed;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          z-index: 1;
          width: 100%;
          background: white;
          float: none;
          display: none;
          vertical-align: middle;

          .close_guest_selector {
            position: absolute;
            width: 40px;
            height: 40px;
            border-width: 0;
            border-radius: 0;
            top: 50px;
            right: 50px;
            cursor: pointer;

            &:before, &:after {
              background: $text-color;
            }
          }

          .room_list {
            @include center_xy;
            width: auto;
            text-align: center;
            white-space: nowrap;
            display: flex;
            flex-flow: column;
            align-items: center;
            max-height: 75%;
            overflow-y: scroll;
            min-width: 650px;

            &::-webkit-scrollbar {
              width: 4px;
              height: 4px;
            }

            &::-webkit-scrollbar-thumb {
              background: $corporate_2;
              border-radius: 15px;
            }

            &::-webkit-scrollbar-thumb:hover {
              background: $corporate_1;
            }

            &::-webkit-scrollbar-track {
              background: transparent;
              border-radius: 10px;
            }

            .add_room, .remove_room {
              display: inline-block;
              vertical-align: middle;
              position: absolute;
              cursor: pointer;
              height: 30px;
              border-radius: 50%;
              bottom: -40px;
              margin: 0;
              text-align: center;
              font-size: 14px;
              font-weight: bold;
              letter-spacing: 0.7px;
              text-decoration: underline;
              position: absolute;
              left: 50%;
              transform: translate(-50%, 0%);
            }

            .remove_room {
              bottom: -65px;
            }

            .room:last-of-type .remove_room {
              bottom: -40px !important;
            }

            .room_info_wrapper {
              display: table;
              height: auto;
              position: relative;
              color: $text_color;

              .hotel_name_rooms.with_name {
                background: rgba($corporate_2, .1);
                font-size: 16px;
                letter-spacing: 0.8px;
                padding: 10px 25px;
                line-height: 25px;
                margin-bottom: 12px;
                border-radius: 50px;
              }

              .dates_wrapper {
                width: 100%;
                display: inline-block;
                background: rgba($corporate_2, .1);
                font-size: 16px;
                padding: 10px 25px;
                line-height: 25px;
                margin-bottom: 50px;
                border-radius: 50px;
              }
            }

            .wrapper_booking_button_guest {
              display: block;
              width: 280px;
              margin: 20px auto 0;

              .promocode_wrapper, .submit_button {
                display: block;
                width: 100%;
                padding: 0;
                margin: 5px 0;
              }

              .promocode_wrapper {
                padding-bottom: 15px;

                .promocode_input {
                  text-align: center;
                  -webkit-appearance: none;
                  -moz-appearance: none;
                  appearance: none;
                  background-color: transparent;
                  height: 40px;
                  font-size: 14px;
                  letter-spacing: 0.7px;
                  line-height: 20px;
                  font-weight: 400;
                  font-family: $primary_font;
                  color: red !important;
                  border: 0.5px dashed $corporate_3;
                  border-radius: 50px;
                  width: 203px;

                  &::-webkit-input-placeholder {
                    color: $corporate_3;
                  }

                  &::-moz-placeholder {
                    color: $corporate_3;
                  }

                  &:-ms-input-placeholder {
                    color: $corporate_3;
                  }

                  &:-moz-placeholder {
                    color: $corporate_3;
                  }
                }
              }


              .submit_button {
                width: 203px;
                height: 62px;
                line-height: 35px;
                font-size: 20px;
                letter-spacing: 0.44px;
                border-radius: 62px;
                margin: auto;
                background: $corporate-1;
                color: white;
                border: none;
                cursor: pointer;
                text-transform: uppercase;
                transition: all .4s;
                font-family: $secondary-font;
                font-weight: bold;

                &:hover {
                  background: #4a4a4a;
                }

                &:lang(fr) {
                  width: 80%;
                }
              }
            }

            &.size_2 {
              .room1 {
                border-color: $corporate_2;

                .add_room {
                  display: none !important;
                }

                .room_title,
                .remove_room,
                label,
                .room_selector .selectric .label,
                .room_selector .selectric .label:before,
                .room_selector .selectric .button:before {
                  color: $corporate_2;
                }

                .children_selector {
                  border-left-color: $corporate_2;
                }
              }
            }

            &.size_3 {
              .room1, .room2 {
                border-color: $corporate_2;

                .room_title,
                .remove_room,
                label,
                .room_selector .selectric .label,
                .room_selector .selectric .label:before,
                .room_selector .selectric .button:before {
                  color: $corporate_2;
                }

                .children_selector {
                  border-left-color: $corporate_2;
                }
              }
            }

            .room {
              position: relative;
              display: flex;
              vertical-align: middle;
              height: auto;;
              padding: 10px 35px;
              text-align: center;
              border: 1px solid $corporate_1;
              border-radius: 65px;
              margin: 0 0 40px;
              overflow: initial !important;
              color: $text_color;

              label {
                margin: 0;
              }

              &:lang(de) {
                width: 310px;
              }

              .room_title {
                display: none;
              }

              label {
                display: inline-block;
                vertical-align: middle;
                text-transform: capitalize;
                font-size: 13px;
                letter-spacing: 1.3px;
                text-align: left;

                .range-age {
                  display: block;
                  font-size: 10px;
                }
              }

              .selectric-room_selector {
                display: inline-block;
                vertical-align: middle;

                .selectricItems {
                  display: none !important;
                }

                .selectric {
                  height: 30px;
                  margin: -15px 0 0 5px;


                  .label {
                    text-align: center;
                    margin-left: -2px;
                    color: $text-color;
                    font-family: $primary_font;
                    font-size: 18px;
                    line-height: 30px;
                    font-weight: bold;

                    &:before {
                      content: '\f067';
                      position: absolute;
                      top: 50%;
                      right: 5px;
                      -webkit-transform: translateY(-50%);
                      -moz-transform: translateY(-50%);
                      -ms-transform: translateY(-50%);
                      -o-transform: translateY(-50%);
                      transform: translateY(-50%);
                      font-family: "Font Awesome 5 Pro";
                      font-weight: bold;
                      font-size: 14px;
                      color: $text-color;
                    }
                  }

                  .button {
                    position: absolute;
                    top: 0;
                    text-indent: 0;
                    height: auto;
                    color: white;
                    margin: 0;
                    font-size: 0;
                    left: -2px;
                    line-height: 24px;
                    background: transparent !important;
                    text-shadow: 0 0 0 rgba(0, 0, 0, 0) !important;

                    &:before {
                      content: '\f068';
                      position: absolute;
                      top: 50%;
                      right: 15px;
                      -webkit-transform: translateY(-50%);
                      -moz-transform: translateY(-50%);
                      -ms-transform: translateY(-50%);
                      -o-transform: translateY(-50%);
                      transform: translateY(-50%);
                      display: inline-block;
                      vertical-align: middle;
                      font-family: "Font Awesome 5 Pro";
                      font-weight: bold;
                      font-size: 14px;
                      line-height: 24px;
                      color: $text-color;
                    }
                  }
                }
              }

              .adults_selector, .children_selector, .babies_selector {
                height: auto;
                padding: 5px 25px;
                border-left-width: 0;
                border-right: 1px solid $corporate_1;
              }

              .children_selector {
                border-right: none;
              }

              &.room_with_babies {
                .children_selector {
                  border-right: 1px solid $corporate_1;
                }

                .babies_selector {
                  border-right: none;
                }
              }
            }

            .full_ages_wrapper {
              display: none;
              margin-bottom: 10px;

              .kids_age_selection {
                .kid_age_element_wrapper, .kid_age_element_wrapper.hide {
                  display: none;
                }
              }

              .slides_wrapper {
                .input_slide {
                  display: inline-flex;
                  flex-flow: column;
                  width: 168px;

                  .slide_content {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 10px;
                    font-size: 14px;

                    .max-value {
                      font-weight: bold;
                    }
                  }

                  input[type="range"].slider_age {
                    appearance: none;
                    -moz-appearance: none;
                    -webkit-appearance: none;
                    display: block;
                    background: lightgray;
                    width: 168px;
                    height: 4px;
                    background-image: -webkit-gradient(linear, 0% 0%, 100% 0%, from(rgb(0, 45, 66)), from(rgb(211, 211, 211)));
                    background-image: -moz-linear-gradient(left center,
                            $corporate_2 0%, $corporate_2 0%,
                            lightgray 100%, lightgray 100%);

                    &::-webkit-slider-thumb {
                      appearance: none;
                      -webkit-appearance: none;
                      border: 1px solid $corporate_2;
                      height: 19px;
                      width: 19px;
                      border-radius: 50%;
                      background: $corporate_2;
                      cursor: pointer;
                      margin-top: 0;
                    }

                    &::-moz-range-thumb, &::-ms-thumb {
                      appearance: none;
                      -webkit-appearance: none;
                      border: 1px solid $corporate_2;
                      height: 19px;
                      width: 19px;
                      border-radius: 50%;
                      background: $corporate_2;
                      cursor: pointer;
                    }

                    &::-webkit-slider-runnable-track, &::-moz-range-track, &::-ms-track {
                      appearance: none;
                      -webkit-appearance: none;
                      -moz-appearance: none;
                      width: 210px;
                      height: 4px;
                      cursor: pointer;
                      border: none;
                      background: lightgray;
                    }

                    &:focus {
                      outline: none;

                      &::-webkit-slider-thumb {
                        margin-top: -7px;
                      }

                      &::-webkit-slider-runnable-track {
                        height: 4px;
                      }
                    }

                    &::-ms-track {
                      width: 100%;
                      cursor: pointer;
                      background: transparent;
                      border-color: transparent;
                      color: transparent;
                    }
                  }

                  &.hide {
                    display: none;
                  }

                  &.show {
                    &:not(:first-of-type) {
                      margin-left: 40px;
                    }
                  }
                }
              }

              &.show {
                display: block;
              }
            }
          }
        }

        .wrapper_booking_button {
          position: relative;
          display: inline-block;
          float: right;
          width: 32%;
          height: 67px;
          @include separator;

          .promocode_wrapper {
            display: inline-block;
            float: left;
            height: 100%;
            width: 40%;
            position: relative;
            padding: 0;
            border-top: none;

            label {
              display: none;
            }

            .promocode_input {
              @include center_xy;
              text-align: center;
              -webkit-appearance: none;
              -moz-appearance: none;
              appearance: none;
              background-color: transparent;
              width: 100%;
              height: 40px;
              font-size: 16px;
              line-height: 20px;
              font-weight: 400;
              font-family: $primary_font;
              color: $text-color;
              border: none;

              &:focus {
                outline: 0;
              }

              &::-webkit-input-placeholder {
                @include promocode_placeholder;
              }

              &:-moz-placeholder {
                @include promocode_placeholder;
              }

              &::-moz-placeholder {
                @include promocode_placeholder;
              }

              &:-ms-input-placeholder {
                @include promocode_placeholder;
              }

              &::placeholder {
                @include promocode_placeholder;
              }
            }
          }

          .submit_button {
            position: absolute;
            top: 0;
            bottom: 0;
            right: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            float: left;
            width: 60%;
            font-size: 20px;
            font-weight: bold;
            letter-spacing: 0.4px;
            border-radius: 70px;
            font-family: $secondary-font;
            background-color: $corporate_1;
            text-transform: uppercase;
            color: white;
            margin: 0;
            border-style: none;
            cursor: pointer;
            @include transition(background-color, .4s);

            &:hover {
              background-color: #4a4a4a !important;
            }
          }
        }
      }
    }
  }

  .widget_buttons {
    position: absolute;
    top: -50px;
    display: flex;
    justify-content: center;
    align-items: center;

    .content_wrapper {
      .button {
        background: #15151530;
        backdrop-filter: blur(41px);
        padding: 10px 20px;
        border-radius: 50px;
        margin-right: 10px;
        color: white;
        font-size: 13px;
        font-weight: 400;
        letter-spacing: 0.65px;
        font-family: $primary_font;
        position: relative;
        border: 0 !important;
        text-decoration: none;

        &:before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: -1;
          border-radius: 15px;
          backdrop-filter: blur(15px);
          -webkit-backdrop-filter: blur(15px);
        }

        i {
          font-size: 16px;
          margin-right: 5px;
        }

        svg {
          position: relative;
          top: 5px;
          display: inline-block !important;
          vertical-align: baseline !important;
        }
      }
    }
  }

  .logotype_widget {
    display: none;
  }

  &.open {
    transform: none;

    .widget_buttons {
      display: none;
    }

    #full_wrapper_booking {
      .booking_steps {
        display: flex;
      }
    }
  }
}

.booking_steps {
  position: fixed;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 1000001;
  top: 30px;
  text-align: center;
  display: none;
  justify-content: center;
  align-items: stretch;
  width: 1000px;
  padding: 0 0 20px 0;

  @media (max-height: 800px) and (min-width: 1140px) {
    top: 0 !important;
  }

  .content_wrapper {
    display: flex;;
    padding: 30px 0 15px 0;
    margin: 0 auto;
    list-style: none;
    justify-content: space-evenly;

    .step {
      display: flex;
      align-items: center;
      position: relative;
      cursor: pointer;
      color: $corporate_1;
      letter-spacing: 0.7px;
      font-size: 14px;
      font-weight: 300;

      .number_step {
        background: transparent;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        border: 1px solid $corporate-2;
        display: inline-block;
        margin-right: 20px;
        position: relative;

        span {
          font-family: $primary_font;
          font-size: 12px;
          letter-spacing: 0.66px;
          line-height: 12px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }

        i {
          display: none;
        }
      }

      span.booking-step {
        font-family: $primary_font;
        font-size: 14px;
        letter-spacing: 0.7px;
        background: transparent;
      }

      &.current_step {
        .number_step {
          background: $corporate-2;

          span {
            color: white;
          }
        }
      }

      &.done {
        .number_step {
          background: $corporate_1;
          display: flex;
          justify-content: center;
          align-items: center;
          color: white;
          border-color: $corporate_1;
          font-size: 18px;

          span {
            display: none;
          }

          i {
            display: inline;
          }
        }
      }

      &:first-of-type {
        &::before {
          content: "";
          position: absolute;
          left: 124%;
          top: 50%;
          background: $corporate_1;
          height: 1px;
          width: 55px;

          html[lang="es"] & {
            left: 114%;
          }
        }
      }

      &:not(:first-of-type):not(:last-of-type) {
        &::before {
          content: "";
          position: absolute;
          left: 130%;
          top: 50%;
          background: $corporate_1;
          height: 1px;
          width: 55px;

          html[lang="es"] & {
            left: 113%;
          }
        }
      }
    }
  }
}

.ui-dialog.ui-widget {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%);
  background-color: white;
  font-family: $primary_font;
  color: $text_color;

  .ui-dialog-title {
    font-weight: 400;
    font-size: 20px;
    letter-spacing: 0.9px;
  }

  .ui-dialog-buttonpane {
    border: none !important;

    .ui-dialog-buttonset {
      .ui-button {
        position: relative;
        padding: 15px;
        border: none;
        border-radius: 0;
        background-color: $corporate_1;
        font-family: $primary_font;
        font-weight: 700;
        font-size: 18px;
        letter-spacing: 1px;
        line-height: 1;
        text-transform: uppercase;
        color: white;
        cursor: pointer;

        &:lang(ru) {
          font-size: 16px;
        }

        &:hover {
          background-color: $corporate_2;
        }
      }
    }
  }
}
