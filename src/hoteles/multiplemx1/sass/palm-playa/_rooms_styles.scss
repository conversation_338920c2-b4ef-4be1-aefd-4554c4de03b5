div#step-1 {
  background: unset;

  .contTipoHabitacion {
    box-shadow: 0 0 10px #0000001A;
    margin-bottom: 20px;

    .overlay_picture_text {
      position: absolute;
      z-index: 10;
      text-align: center;
      top: 30px;
      left: 52px;
      font-size: 9px;
      font-weight: bold;
      width: 99px;

      strong {
        display: block;
        font-size: 27px;
        margin-bottom: 15px;
      }

      .s_tag {
        color: #E75354;
      }

      .m_tag {
        color: green;
      }

      .l_tag {
        color: #7CCFF4;
      }

      .xl_tag {
        color: black;
      }
    }

    .contDescHabitacion {
      .cabeceraNombreHabitacion {
        padding: 0;
        margin: 0 0 10px;
      }

      .descripcionHabitacion {
        padding: 0;
        margin-right: 85px;
      }

      .room_services i {
        font-size: 14px;
        color: #747474;
        text-transform: none;
      }

      .see_more_rooms_v2 {
        font-family: $text_family;
        font-weight: bold;
        font-size: 13px;
        letter-spacing: 1.56px;
        color: $black;
        margin-left: 0;
        bottom: 25px;
      }
    }

    .contFotoDescripcion {
      .room_button_controls {
        right: calc(100% + 20px);

        .open_see_more_room_v2 {
          background: $corporate_3;
        }
      }
    }

    .contFotoHabitacion {
      min-height: 245px !important;
      width: 320px;
      margin-left: -26px !important;
      margin-top: -20px !important;

      .occupancy {
        display: table;
        width: 100%;
        background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.4));
        position: absolute;
        bottom: 0;
        right: 0;
        z-index: 10;
        padding: 5px;
        text-align: right;
        font-family: $text_family;
        font-weight: 700;
        font-size: 12px;
        line-height: 12px;
        color: white;

        i {
          display: inline-block;
          vertical-align: bottom;
          padding: 0;
          color: white;
          font-size: 12px;
          line-height: 12px;

          &.adult {
            font-size: 22px;
            line-height: 22px;
            padding-right: 5px;
          }

          &.kid {
            font-size: 18px;
            line-height: 18px;
            padding-right: 5px;
          }

          &.baby {
            font-size: 14px;
            line-height: 14px;
            margin-bottom: 0;
            padding-left: 2px;
          }
        }

        b {
          position: absolute;
          bottom: 15px;
          right: 0;
          opacity: 0;
          display: inline-block;
          padding: 10px;
          font-weight: 400;
          text-transform: lowercase;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          border-radius: 5px;

          &:after {
            position: absolute;
            top: 100%;
            right: 30px;
            content: '';
            border: 5px solid $white;
            border-color: rgba(0, 0, 0, 0.8) $white $white $white;
          }
        }

        &:hover {
          b {
            bottom: 30px;
            opacity: 1;
          }
        }
      }
    }

    .tipoHabitacion {
      color: $corporate_2 !important;
      font-size: 23px !important;
      letter-spacing: 0;
      line-height: 19px;
      font-weight: 500 !important;
      font-family: $title_family;
    }

    .descripcionHabitacion {
      font-family: $text_family;
      font-weight: lighter;
      font-size: 15px;
      letter-spacing: .9px;
      line-height: 22px;
      color: $black !important;
      height: 65px;
    }

    .see_more_rooms_v2 {
      .plus_sign {
        color: #333 !important;
      }

      .see_more {
        text-decoration: none !important;
        text-transform: uppercase !important;
        color: #333 !important;
      }

      .lupa {
        display: block;
        top: 10px;
        left: 10px;
        font-size: 30px;
        font-weight: 300;
        font-family: "Font Awesome 5 Pro", sans-serif;
      }
    }

    .very_asked_message {
      background-color: $corporate_2 !important;
      color: white;
      font-family: $text_family;
      font-weight: 300;
      font-size: 13px;
      letter-spacing: .6px;
      border-radius: 20px 0px 0px 20px;
      margin-right: -30px;
      text-transform: uppercase;
      height: 16px;
      top: -7px;

      &:before {
        color: white;
        content: '';
        top: 0px;
        width: 0px;
        left: -6px;
        height: 0px;
        border-left: 13px solid white;
        border-right: 13px solid transparent;
        border-bottom: 13px solid transparent;
        border-top: 13px solid transparent;
        background: $corporate_2;
        position: absolute;
      }

      &:after {
        display: none;
      }
    }

    .room_services {
      border-top-width: 2px;
      border-top-style: dashed;
      border-bottom-width: 2px;
      border-color: #d8d8d8;

      .service_element {
        border-right-width: 2px;
        margin: 3px 0 3px 0;
        border-color: #d8d8d8;
        text-align: left;
        padding-left: 10px;

        &:lang(en) {
          padding-left: 0;
        }

        .service_description {
          color: $black;
          font-family: $text_family;
          font-weight: normal;
          font-size: 12px;
          letter-spacing: 0.72px;
          text-transform: none;
          padding-left: 10px;
          display: initial !important;

          &:lang(en) {
            padding-left: 0;
          }
        }
      }
    }

    .preciosHabitacion {
      width: 1140px;
      margin-left: -20px;
      margin-top: 40px;

      .listadoHabsTarifas {
        border-spacing: 0;

        .regimenColumn {
          width: 300px;

          .regimenColumnContent {
            padding: 0 15px 0 0;
            margin-left: -5px;

            font-family: $text_family;
            font-weight: normal;
            font-size: 14px;
            letter-spacing: .42px;
            color: $black;

            .regimen_name_wrapper {
              color: $black;
              letter-spacing: 1px;
              font-size: 18px;
            }

            .tTextoOferta {
              color: $corporate_4 !important;

              span.offer_name_element {
                color: $corporate_1;
              }
            }
          }
        }
      }
    }

    .precioTotalColumn, .precioNocheColumn {
      text-align: right;

      .precioTachadoDiv {
        color: $corporate_4;

        .tPrecioTachado {
          color: $corporate_4;
          font-family: $text_family;
          font-weight: bold;
          font-size: 11px;
          letter-spacing: .66px;
        }
      }

      .precioGeneralDiv {
        .precioGeneral {
          font-size: 18px;
          font-weight: 600;
        }

        .tPrecioOferta {
          color: $black;
          font-family: $text_family;
          font-weight: bold;
          font-size: 18px;
          letter-spacing: .99px;
          margin-right: 4px;
        }
      }

      .promotion_percentage_square {
        border-radius: 50%;
        background-color: $corporate_4 !important;
        font-family: $text_family;
        font-weight: bold;
        font-size: 12px;
        letter-spacing: .18px;
        text-align: center;
      }

      .tax_not_included_label {
        font-family: $text_family;
        letter-spacing: 0.6px;
      }

      .priceTitle {
        font-family: $text_family;
        font-weight: normal;
        font-size: 14px;
        letter-spacing: 0.6px;
      }
    }

    .booking-button {
      border-radius: 0px;
      font-family: $title_family;
      background-color: $corporate_3;
      color: white;
      padding: 10px 40px;
      font-size: 20px;
      letter-spacing: 2px;
      font-weight: normal;
      position: relative;
      -webkit-transition: all 0.6s;
      -moz-transition: all 0.6s;
      -ms-transition: all 0.6s;
      -o-transition: all 0.6s;
      transition: all 0.6s;
      display: inline-block;
      vertical-align: middle;
      text-transform: capitalize;
      z-index: 9;

      &:hover {
        background-color: $corporate_3;
        opacity: 0.75;
      }

      &:lang(en) {
        padding: 10px 30px;
      }

    }

    .last_day_cancellation_text {
      background: $white;
      position: relative;
      color: #2ca96e;
      letter-spacing: 0.8px;
      font-family: $text_family;
      font-style: normal;
      font-size: 15px;

      &:before {
        @extend .fa;
        position: absolute;
        top: 50%;
        left: 3px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        color: #2ca96e;
        padding-top: 5px;
        font-size: 15px;
        line-height: 14px;
        border-radius: 50%;
        content: '\f00c';
      }
    }

    .cheapest_rate_message {
      .conditions_info_wrapper {
        position: absolute;
        bottom: 0;
        display: block;
        right: 0;
        border-bottom: 0;
        padding: 0;
        z-index: 10;
      }

      .rate_conditions_link {
        margin: 0 !important;
        display: block;
        width: 60%;
        text-align: right;
        padding-right: 30px;
        background: none;
        color: #444444;
        font-size: 10px;
        font-weight: normal;
        text-transform: none;
        font-family: $title_family;
      }

      &.has_conditions {
        padding: 5px 30px 15px 40px !important;
      }
    }

    .rate_conditions_link {
      position: relative;
      @extend .fa-info;

      &:before {
        @extend .fa;
        width: 3px;
        height: 14px;
        position: absolute;
        top: 50%;
        left: -24px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        border: 1px solid white;
        background: $white;
        color: white;
        padding: 0 5px;
        font-size: 10px;
        line-height: 14px;
        border-radius: 50%;
      }

      &:hover {
        color: $corporate_2;
      }

      &:first-of-type:before {
        width: 3px;
        height: 12px;
        position: absolute;
        top: 50%;
        left: -10px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        border: 1px solid black;
        background: transparent;
        color: black;
        padding: 0 5px;
        font-size: 9px;
        line-height: 12px;
        border-radius: 50%;
      }

    }

    .contTitTipoTarifa {
      background-color: $grey_2;
      color: $black;
      font-family: $text_family;

      .titTipoTarifa {
        font-weight: 100;
        font-size: 16px;
        letter-spacing: 0.6px;
        text-transform: none;
      }

      .cheapest_rate_message {
        background-color: transparent;
        color: #444444;
        letter-spacing: 1px;
        overflow: initial;

        .best_price_label_info {
          font-weight: 400;
          font-size: 14px;
          letter-spacing: .8px;
        }

        &:before {
          border-left: 19px solid $grey_2
        }

        .before_block {
          border-left-color: $corporate_3 !important;
        }
      }

      .advice_rate_message {
        background-color: $corporate_3 !important;
        color: $white !important;

        &.has_conditions {
          background-color: $corporate_1 !important;
          color: white !important;
        }

        &:before {
          border-left-color: $grey_2;
        }
      }


      > .conditions_info_wrapper {
        a {
          color: $corporate_2;
          font-weight: lighter;
          padding: 0 10px;
          text-decoration: none;
          position: relative;
          font-size: 12px;
          letter-spacing: 0;
          text-transform: none;
          line-height: 1;
          top: 7px;
          right: 18px;

          &:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            margin: auto;
            height: 1px;
            width: 80%;
            background-color: $grey;
          }
        }
      }
    }

    .sectoption {
      border: none;
      padding: 5px 0;

      .titSelOpt {
        color: $black;
        font-size: 18px;
        font-family: $title_family;
      }

      .listaradio {
        li {
          input {
            display: inline-block;
            vertical-align: middle;
            margin: 0 5px 0;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: white;
            border: 1px solid $black;
            cursor: pointer;

            &:checked {
              background-color: $corporate_1;
            }

            &:focus {
              outline: 0;
            }
          }
        }
      }
    }
  }

  .listadoHabsTarifas {
    &.country_location_param_actived {
      td {
        padding: 6px 18px !important;

        &.lock_board {
          .lock_board_wrapper {
            font-family: $text_family;

            &:hover {
              opacity: 0.75;
            }
          }
        }
      }
    }
  }

  .conditions_info_wrapper {
    width: 98%;
    margin: auto;

  }

  //table.listadoHabsTarifas.country_location_param_actived .tax_inc_info:before {
  //  content: none;
  //}
}

.tax_inc_wrapper_info.active {
  padding: 3px 10px;
}

div#step-1 {
  .contTitTipoTarifa + .conditions_info_wrapper {
    .rate_conditions_link {
      color: $corporate_2;
      font-size: 12px;
      text-transform: none;
      box-sizing: border-box;
      letter-spacing: 0.4px;
      font-weight: 400;
      padding: 0 0 0 0;
      bottom: 35px;
      background: none;
      right: 8px;

      &:before {
        border-color: #212630;
        color: #212630;
        background: transparent;
        left: -20px;
        top: 9px;
      }
    }
  }

  table.listadoHabsTarifas tr .lock_board .lock_board_wrapper span {
    font-size: 18px;
    letter-spacing: 0.67px;
  }
}

.popup_see_more_rooms_second {
  .fancybox-inner {
    background-color: white;
    max-height: 90vh;
  }

  .room_popup_individual_element {
    .popup_title {
      background: white;
      font-family: $title_family;
      font-size: 25px;
      font-weight: 600;
      letter-spacing: 1.25px;
      line-height: 29px;
      color: $corporate_2;
      top: auto;
      bottom: calc(100% - 460px);
      padding-right: 20%;
      padding-bottom: 10px;
      border-top: 10px solid white;
    }

    .close_button_element {
      font-family: $text_family;
      font-size: 18px;
      font-weight: 300;
      color: white;
      width: 50px;
      line-height: 50px;
      top: 0;
      right: 0;
    }

    .popup_carousel {
      .element_carousel_pictures {
        .exceded {
          height: 400px;
          margin-bottom: 20px;
          position: relative;

          img {
            width: 100%;
          }

          a {
            display: block !important;
            position: absolute;
            top: calc(100% - 40px);
            right: 0;
            background: radial-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
            padding: 10px;
            color: white;

            i.fa {
              font-size: 25px;
            }

            &:hover {
              color: $corporate_1;
            }
          }
        }
      }

      .flex-direction-nav {
        .flex-nav-prev, .flex-nav-next {
          width: 50px;
          height: 50px;

          &:hover {
            width: 60px;

            a {
              &:before {
                margin-left: 12px;
              }
            }
          }

          a {
            background-color: $corporate_2;

            &:before {
              position: absolute;
              font-family: "Font Awesome 5 Pro";
              font-size: 30px;
              color: white;
              content: '\f054';
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              font-size: 18px;
              font-weight: 300;
            }

            img {
              display: none;
            }
          }
        }

        .flex-nav-prev {
          a:before {
            content: '\f053';
          }

          &:hover {
            a {
              &:before {
                margin-left: -12px;
              }
            }
          }
        }
      }
    }


    .room_services {
      padding: 0 !important;
      border-color: #b0b0b0;
      border-top-width: 1px;
      border-bottom-width: 1px;
      background: #d3d3d3;
      display: flex !important;

      .service_element {
        border-right-color: #b0b0b0;
        height: auto !important;

        i {
          font-size: 25px;
          display: block;
          padding-bottom: 0;
          margin-right: 0;
        }


        .service_description {
          font-size: 15px;
          letter-spacing: .4px;
          font-family: $text_family;
          font-weight: 300;
          max-width: calc(100% - 35px);
        }

        &:last-of-type {
          border-right: none;
        }
      }
    }

    .popup_room_description {
      margin-top: 30px;
      margin-bottom: 30px;
      padding-top: 20px;
      padding-bottom: 20px;
      margin-left: 110px;
      font-family: $title_family;
      font-size: 16px;
      letter-spacing: .6px;
      line-height: 20px;


      padding: 0 60px;
      background: white;

      .desc, .list {
        display: inline-block;
        vertical-align: top;
        width: calc(100% / 3);
        box-sizing: border-box;
      }

      .desc {
        width: calc(100% / 3 * 2);
        padding-right: 20px;

        strong {
          font-weight: 700;
        }
      }

      .list {
        padding: 0 0 0 25px;

        li {
          font-weight: 700;
          padding: 5px 0;
          @extend .icon-longarrow;

          &:before {
            font-family: "icomoon", sans-serif;
            margin-right: 10px;
            color: $corporate_2;
          }
        }
      }
    }
  }
}

.fancybox-overlay {
  .room_popup_individual_element {
    .popup_title {
      font-family: $text_family;
      font-weight: 500;
      font-size: 21px;
      letter-spacing: 1.15px;
      line-height: 1;
      text-transform: capitalize;
      color: white;
      background: #00345485;
      top: 0;
      height: fit-content;
      border-top: 0;
    }

    .popup_room_pictures {
      top: 390px;
      background: #d3d3d3;
    }

    .popup_carousel {
      .exceded {
        padding: 0;

        .popup_image {
          @include cover_image;
          position: static;
        }
      }
    }

    .popup_room_description {
      font-family: $text_family;
      font-weight: 400;
      font-size: 16px;
      letter-spacing: 0.6px;
      line-height: 21px;
      color: $black;
    }
  }
}