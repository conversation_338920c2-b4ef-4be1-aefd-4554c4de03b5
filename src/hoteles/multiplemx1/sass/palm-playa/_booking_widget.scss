#full_wrapper_booking {
  box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  padding: 0;
}

.booking_engine_wrapper_process {
  background-color: $white;

  #booking.boking_widget_inline {
    width: 100%;
    background: white;
    border-bottom: 1px solid $grey;
    padding-top: 3px;
    padding-bottom: 0;

    .booking_form.paraty-booking-form {
      width: 1140px;
      margin: auto;
      position: relative;
    }

    .stay_selection {
      width: calc(32% + 10px);

      .date_box.departure_date {
        background-image: none !important;
      }

      .departure_date_wrapper, .entry_date_wrapper {
        border-bottom: none !important;
        height: 85px;
        padding: 15px 15px;
        text-transform: uppercase;

        .date_box {
          margin-top: 5px;
        }
      }

      .departure_date_wrapper {
        background: white;
        padding-left: 50%;

        &:after {
          content: '';
          position: absolute;
          top: 10px;
          height: 65px;
          bottom: 5px;
          right: 35px;
          width: 1px;
          display: block;
          background: $grey_3;
          opacity: 1;
        }
      }

      .entry_date_wrapper {
        background: $white;

        &:after {
          content: '';
          position: absolute;
          top: 40px;
          bottom: 5px;
          right: 20px;
          border-bottom: 1px solid $grey_3;
          border-right: 1px solid $grey_3;
          height: 10px;
          width: 10px;
          transform: rotate(-45deg);
        }
      }

      label {
        font-family: $text_family;
        color: $black;
        font-size: 15px;
        font-weight: 100;
        letter-spacing: 1.5px;
        line-height: 14.4px;
        text-transform: capitalize;
        margin-top: 10px;
      }

      .date_day {
        font-family: $text_family;
        font-weight: 700;
        font-style: normal;
        font-size: 22px !important;
        letter-spacing: 2.2px;
        color: $black;

        span {
          display: inline-block;
          vertical-align: baseline;

          &.day {
            font-weight: 700;
            font-size: 25px;
          }

          &.month {
            padding: 0 5px;
          }
        }
      }
    }

    .guest_selector {
      margin: 0;
      height: 85px;
      padding: 15px 15px;
      width: 225px;
      background: white;

      &:after {
        content: '';
        position: absolute;
        top: 10px;
        height: 65px;
        bottom: 5px;
        right: 0;
        width: 1px;
        display: block;
        background: $grey_3;
        opacity: 1;
      }

      label {
        font-family: $text_family;
        color: $black;
        font-size: 15px;
        font-weight: 400;
        letter-spacing: 1.5px;
        line-height: 14.4px;
        text-transform: capitalize;
        margin-top: 10px;
      }

      .placeholder_text {
        font-family: $text_family;
        font-style: normal;
        font-weight: lighter;
        font-size: 25px;
        letter-spacing: 2px;
        color: $black;
        margin-top: 5px;

        span {
          font-size: 22px;
          font-weight: lighter;
          text-transform: lowercase;
        }
      }
    }

    .room_list_wrapper .buttons_container_guests .save_guest_button {
      background: $corporate_2 !important;
    }

    .room_list_wrapper {
      top: 80px;
      left: 330px;
      font-family: $text-family;

      &:before {
        border-bottom: 11px solid $corporate_2;
      }

      .room_list {
        border-top: 2px solid $corporate_2;
      }

      label,
      .selectric .label,
      .add_room_element {
        font-family: $text-family;
        font-size: 14px;
        letter-spacing: 1px;
        color: black;
      }

      .buttons_container_guests {
        font-family: $text-family;
      }
    }

    .wrapper_booking_button {
      background: white;
      height: 85px;
      width: 540px;
      display: inline-flex;


      .promocode_wrapper {
        height: 100%;
        width: 140px;

        .promocode_label {
          color: $black;
          font-weight: 400;
          font-family: $text_family;
          text-transform: uppercase;
          font-size: 13px;
          position: absolute;
          top: 31px;
          left: 0;
          line-height: initial;
          letter-spacing: 1.5px;
          width: 110px;
          margin-left: 10px;
        }

        .promocode_input {
          transform: none;
          color: $black !important;
          font-weight: 400;
          font-family: $text_family;
          text-transform: uppercase;
          width: 80%;
          bottom: 15px;
          height: 55px;
          top: 10px;

          &::-webkit-input-placeholder {
            color: transparent;
            font-size: 13px;
            letter-spacing: 1.5px;
            white-space: pre-line;
            position: relative;
          }

          &::-moz-placeholder {
            color: transparent;
            font-size: 13px;
            letter-spacing: 1.5px;
            white-space: pre-line;
            position: relative;
          }

          &:-ms-input-placeholder {
            color: transparent;
            font-size: 13px;
            letter-spacing: 1.5px;
            white-space: pre-line;
            position: relative;
          }

          &:-moz-placeholder {
            color: transparent;
            font-size: 13px;
            letter-spacing: 1.5px;
            white-space: pre-line;
            position: relative;
          }
        }
      }


      .submit_button {
        border-radius: 0;
        color: white;
        position: relative;
        font-family: $text_family !important;
        font-weight: bold !important;
        font-size: 15px;
        letter-spacing: 0.85px;
        height: 57px;
        text-align: center;
        padding: 0 40px 0 70px;
        float: right;
        margin-left: 10px;
        margin-top: 14px;
        width: 40%;
        background-color: $corporate_2;
        line-height: 18px;
        @extend .fa-undo;

        &:before {
          @extend .fa;
          position: absolute;
          top: 27%;
          font-weight: 300;
          transform: rotate(-25deg);
          left: 20px;
          font-size: 30px;
          color: white;
        }

        &:hover {
          background: $corporate_3;
          opacity: 1;
        }
      }

      .spinner_wrapper {
        width: calc(55% - 2px);
        height: 100%;
        background: $corporate_1;
      }

    }

    .booking-button.booking-button--action.modify-calendar-button {
      height: 35px;
      text-align: center;
      background: transparent;
      border: 1px solid #003454;
      border-radius: 0;
      margin-top: 15px;
      color: #003454;
      padding: 10px 10px 10px 25px;
      text-transform: uppercase;
      font-size: 15px;
      line-height: 18px;
      font-family: $text_family;
      font-weight: bold;
      max-width: 165px;
      display: flex;
      align-items: center;

      &:before {
        content: '\f073';
        color: black;
        position: absolute;
        left: 147px;
        font-family: "Font Awesome 5 Pro", sans-serif;
        top: 34px;
        font-size: 19px;
        font-weight: 100;
      }
    }
  }

  .booking_footer_message {
    position: absolute;
    left: 0;
    bottom: -25px;
  }

  &.has_babies {
    .guest_selector {
      &,
      .placeholder_text,
      .placeholder_text span {
        font-size: 20px !important;
        letter-spacing: 0 !important;
      }
    }

    .submit_button {
      width: 52% !important;
    }
  }
}


.datepicker_wrapper_element .ui-datepicker .ui-widget-header {
  .ui-datepicker-next, .ui-datepicker-prev {
    &.ui-state-hover, &.ui-datepicker-next-hover, &.ui-datepicker-prev-hover {
      span:before {
        color: white;
      }
    }
  }
}