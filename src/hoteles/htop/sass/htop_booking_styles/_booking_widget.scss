#full_wrapper_booking {
  box-shadow: 0 0 0 rgba(0,0,0,0);
  padding: 0;
}

.booking_engine_wrapper_process {
  background-color: transparent;

  #booking.boking_widget_inline {
    width: 100%;
    background: $corporate_3;
    padding-top:3px;
    padding-bottom:0;
    .booking_form.paraty-booking-form {
      width: 1140px;
      margin: auto;
      position: relative;
    }
    .stay_selection {
      width: calc(35% + 30px);
      .date_box.departure_date {
        background-image: none !important;
      }
      .departure_date_wrapper, .entry_date_wrapper {
        border-bottom: none !important;
        height: 85px;
        padding: 15px 15px;
        text-transform: uppercase;
        .date_box {
          margin-top: 15px;
        }
      }
      .departure_date_wrapper {
        padding-left: 50%;
        &:after {
          content: '';
          position: absolute;
          top: 10px;
          height: 65px;
          bottom: 5px;
          right: 35px;
          width: 1px;
          display: block;
          background: $corporate_1;
          opacity: 1;
        }
      }
      .entry_date_wrapper {
        background: transparent;
        &:after {
          content: '\f105';
          font-family: "Font Awesome 5 Pro";
          font-size: 35px;
          position: absolute;
          top: 40px;
          height: 45px;
          bottom: 5px;
          font-weight: 300;
          right: 20px;
          display: block;
          color: $corporate_1;
          opacity: 1;
        }
      }
      label {
        font-family: $text_family;
        color: $black;
        font-size: 13px;
        font-weight: 300;
        letter-spacing: 1.8px;
        line-height: 14.4px;
        text-transform: uppercase;
      }
      .date_day {
        font-family: $text_family;
        font-weight: 300;
        font-style: normal;
        font-size: 18px !important;
        letter-spacing: 2px;
        color: $black;
        span {
          display: inline-block;
          vertical-align: baseline;
          &.day {
            font-weight: 700;
            font-size: 25px;
          }
          &.month {
            padding: 0 5px;
          }
        }
      }
    }
    .guest_selector {
      margin: 0;
      height: 85px;
      padding: 15px 0;
      width: 380px !important;
      right: 15px;
      background: transparent;
      &:lang(de) {
        width: 435px !important;
      }
      &:after {
        content: '';
        position: absolute;
        top: 10px;
        height: 65px;
        bottom: 5px;
        right: -10px;
        width: 1px;
        display: block;
        background: $corporate_1;
        opacity: 1;
      }
      label {
        font-family: $text_family;
        color: $black;
        font-size: 13px;
        font-weight: 300;
        letter-spacing: 1.8px;
        line-height: 14.4px;
        text-transform: uppercase;
      }
      .placeholder_text {
        font-family: $text_family;
        font-style: normal;
        font-weight: 300;
        font-size: 25px;
        letter-spacing: 2px;
        color: $black;
        margin-top: 15px;

        span {
          font-size: 18px;
          font-weight: 300;
          text-transform: lowercase;
        }
      }
    }

    .room_list_wrapper .buttons_container_guests .save_guest_button {
      background: $corporate_1 !important;
    }
    .room_list_wrapper .room_list .room .remove_room_element{
      top: 20px;
    }
    .wrapper_booking_button {
      height: 85px;
      width: 365px !important;

      &:lang(de) {
        width: 340px !important;
      }

      &:lang(ru) {
        width: 395px !important;
      }

      .promocode_wrapper {
        height: 100%;
        width: 45%;

        .promocode_label {
          display: none;
        }

        .promocode_input {
          transform: none;
          color: $black !important;
          font-weight: 300;
          text-transform: uppercase;
          width: 100%;
          bottom: 0;
          height: 75px;
          top: 0;

          &:lang(de) {
            top: -20px;
          }

          &::-webkit-input-placeholder {
            color: $grey;
            font-size: 13px;
            letter-spacing: 1.3px;
            white-space:pre-line;
            position:relative;
          }
          &::-moz-placeholder {
            color: $grey;
            font-size: 13px;
            letter-spacing: 1.3px;
            white-space:pre-line;
            position:relative;
          }
          &:-ms-input-placeholder {
            color: $grey;
            font-size: 13px;
            letter-spacing: 1.3px;
            white-space:pre-line;
            position:relative;
          }
          &:-moz-placeholder {
            color: $grey;
            font-size: 13px;
            letter-spacing: 1.3px;
            white-space:pre-line;
            position:relative;
          }
        }
      }
      .submit_button {
        border-radius: 5px;
        color: white;
        position: relative;
        font-family: $text_family !important;
        font-weight: 700 !important;
        font-size: 13px;
        letter-spacing: 2px;
        height: 80%;
        text-align: center;
        padding: 0 40px 0 70px;
        margin: 7px;
        float: right;
        background-color: $corporate_1;
        @include transition(all, .6s);
        @extend .fa-undo;
        &:lang(de) {
          letter-spacing: 1px;
          padding: 0 40px 0 60px;
        }
        &:before {
          @extend .fa;
          position: absolute;
          top: 50%;
          font-weight: 300;
          -webkit-transform: translateY(-50%);
          -moz-transform: translateY(-50%);
          -ms-transform: translateY(-50%);
          -o-transform: translateY(-50%);
          transform: translateY(-50%);
          left: 20px;
          font-size: 30px;
          color: white;
        }

        &:hover{
          background: $corporate_2;
          opacity: 1;
        }
      }

      .spinner_wrapper {
        width: calc(55% - 2px);
        height: 100%;
        background: $corporate_1;
      }

    }
  }

  .booking_footer_message {
    position: absolute;
    left: 0;
    bottom: -25px;
  }

  &.has_babies {
    .submit_button {
      width: 52% !important;
    }
  }
}

.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
  .specific_month_selector, .go_back_button {
    background: $corporate_2 !important;
  }
}

.datepicker_wrapper_element .ui-datepicker .ui-widget-header{
  .ui-datepicker-next, .ui-datepicker-prev{
    &.ui-state-hover, &.ui-datepicker-next-hover, &.ui-datepicker-prev-hover{
      span:before{
        color: white;
    }
    }
  }
}