#floating_button_paraty,
.close_widget {
  display: none;
}


#widget_paraty {
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 14;
  font-family: $primary_font;
  @import "booking_engine_v7_defaults";
  @import "booking/selectric";


  #full_wrapper_booking {
    width: 100%;
    padding: 0 calc((100% - 1140px) / 2);
    background: white;
    box-sizing: border-box;
    border-radius: 0 !important;
    @include box_shadow;

    #full-booking-engine-html-7 {
      background: white;
      display: block;
      padding: 15px 20px !important;

      .booking_form_title {
        display: none;
      }

      .calendar_root_wrapper {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 500;
        background: white;
        display: none;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 76px;
          background: #F9FCFD;
        }

        .close_calendar_app {
          position: absolute;
          width: 40px;
          height: 40px;
          border-width: 0;
          border-radius: 0;
          top: 20px;
          right: 50px;
          cursor: pointer;

          &:before, &:after {
            background: $text-color;
          }
        }

        .calendar_app {
          display: block !important;

          .price_calendar_wrapper {
            box-shadow: none;
            @include center_xy;
            top: 57%;
            min-height: 600px;

            .full_container {
              .selector_full_wrapper {
                .selector_wrapper {
                  .selector_label {
                    font-family: $primary_font;

                    letter-spacing: 1.2px;
                    color: $text-color;
                    font-weight: 500;
                  }

                  .selector {
                    .option {
                      color: $text-color;
                      font-family: $primary_font;

                      &:hover {
                        opacity: 0.7;
                      }
                    }
                  }
                }
              }

              .bottom_wrapper {
                .top {
                  .info_currency_wrapper {
                    .left_wrapper {
                      .notice_info_chart {
                        font-family: $primary_font;
                      }
                    }
                  }
                }
              }
            }

            &:not(.is_mobile) {
              .full_container {
                padding: 0 100px;

                .calendar_wrapper {
                  margin-top: 0;

                  .month_selector_wrapper {
                    .selector_label {
                      font-family: $primary_font;
                      font-size: 24px;
                      letter-spacing: 1.2px;
                      color: $text-color;
                      font-weight: 500;
                    }

                    .selector {
                      .option {
                        color: $text-color;
                        font-family: $primary_font;

                        &:hover {
                          opacity: 0.7;
                        }
                      }
                    }
                  }

                  .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper {
                    width: 57px;
                    height: 52px;

                    .popup_min_stay {
                      top: -60px;

                      &:after {
                        bottom: -6.4px;
                      }
                    }

                    .day {
                      font-family: $primary_font;
                      font-size: 20px;
                      letter-spacing: 0;
                      color: $text-color;
                      font-weight: 500;
                    }

                    .price {
                      font-family: $primary_font;
                      font-size: 12px;
                      font-weight: 300;
                    }

                    &.disabled,
                    &.closed {
                      .day,
                      .no_dispo_text {
                        color: #ADADAD;
                      }
                    }

                    &.selected {
                      .day {
                        color: white;
                      }
                    }
                  }
                }
              }
            }

            .bottom_wrapper {
              .top {
                .info_currency_wrapper .right_wrapper .toggle_chart {
                  opacity: 0.6;
                  pointer-events: none;
                }
              }

              .bottom {
                display: none;
              }
            }
          }
        }
      }

      .booking_form {
        background-color: transparent;
        position: relative;
        display: flex;
        justify-content: center;

        .destination_wrapper {
          position: relative;
          @include input_base_styles;
          @include display_flex(nowrap);
          flex-direction: column;
          justify-content: center;
          align-items: stretch;
          padding: 0 5px;
          background: transparent;
          width: 28%;
          padding-left: 10px;
          box-sizing: border-box;

          .icon {
            position: absolute;
            font-size: 20px;
            left: -5px;
          }

          label {
            @include label_styles;
            padding-left: 15px;
          }

          .destination_fieldo {
            .destination {
              @include option_styles($ff: $primary_font);
              line-height: 18px;
              width: 100%;
              background: none;
              border: none;
              text-overflow: ellipsis;
              padding-left: 15px;

              &::-webkit-input-placeholder {
                @include option_styles($ff: $primary_font);
              }

              &::-moz-placeholder {
                @include option_styles($ff: $primary_font);
              }

              &::-moz-placeholder {
                @include option_styles($ff: $primary_font);
              }

              &::-ms-input-placeholder {
                @include option_styles($ff: $primary_font);
              }
            }
          }
        }

        .hotel_selector {
          position: fixed;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          z-index: 500;
          background: white;
          padding: 240px calc((100% - 1140px) / 2) 100px;
          display: none;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 76px;
            background: #F9FCFD;
          }

          .center_xy {
            position: relative;
            top: 0;
            left: initial;
            bottom: 0;
            transform: none;
            height: 100%;
            display: flex;
          }

          .close {
            position: absolute;
            top: 20px;
            right: 50px;
            width: 40px;
            height: 40px;
            cursor: pointer;
            @include transition(all, .6s);

            &.active {
              -webkit-transform: rotate(180deg);
              -moz-transform: rotate(180deg);
              -ms-transform: rotate(180deg);
              -o-transform: rotate(180deg);
              transform: rotate(180deg);
            }

            &:before, &:after {
              background: $text-color;
            }

            &:hover {
              &:before, &:after {
                background: $text-color;
              }
            }
          }

          .hotel_selector_inner {
            display: block;
            z-index: 1;
            width: 50%;

            &:first-of-type {
              .hotels_grouped {
                &::before {
                  content: "";
                  position: absolute;
                  top: -30px;
                  bottom: 40px;
                  right: 30px;
                  background: $text_color;
                  opacity: .2;
                  width: 1px;
                }
              }
            }

            &:not(:first-of-type) {
              .group_title, .hotels_grouped {
                padding-left: 20px;
              }
            }

            .group_title {
              font-family: $primary_font;
              font-size: 16px;
              letter-spacing: 0.8px;
              line-height: 24px;
              color: $text_color;
              font-weight: bold;
              text-transform: uppercase;
              padding-left: 50px;
              margin-bottom: 10px;
            }

            .booking_0_destiny {
              cursor: pointer;
            }

            .hotels_grouped {
              padding-left: 50px;
              display: flex;
              flex-direction: column;
              flex-wrap: wrap;
              position: relative;
              max-height: 520px;
              column-gap: 15px;

              .hotels_group {
                margin-bottom: 40px;

                .hotels_group_inner {
                  display: inline-block;

                  .hotels_country_title {
                    font-family: $primary_font;
                    font-size: 16px;
                    letter-spacing: 0.35px;
                    line-height: 24px;
                    font-weight: 500;
                    margin: 0 0 15px;
                    color: $corporate_2;
                  }

                  .hotels_grouped_list {
                    list-style: none;
                    padding: 0;
                    margin: 0;

                    li {
                      margin: 0 0 12px 0;
                      cursor: pointer;
                      padding-right: 5px;
                      box-sizing: border-box;

                      .title_selector {
                        font-family: $primary_font;
                        font-size: 16px;
                        line-height: 24px;
                        letter-spacing: 0.35px;
                        font-weight: 400;
                        cursor: pointer;
                        color: $text_color;
                        display: block;
                      }
                    }

                    span {
                      font-family: $primary_font;
                      font-size: 16px;
                      line-height: 24px;
                      letter-spacing: 0.35px;
                      font-weight: 400;
                      cursor: pointer;
                      color: $text_color;
                      display: block;
                    }
                  }
                }
              }
            }
            .all_hotels {
              margin-bottom: 10px;
              .booking_0_hotel_selection {
                color: $corporate_1;
                padding-left: 50px;
                font-size: 16px;
                letter-spacing: 0.35px;
                line-height: 24px;
                font-weight: bold;
                cursor: pointer;
              }
            }
          }
        }

        .stay_selection {
          position: relative;
          width: 30%;
          @include input_base_styles;
          @include display_flex(nowrap);
          justify-content: space-between;
          align-items: center;
          border-radius: 0;
          padding: 0 0 0 20px;
          cursor: pointer;
          @include separator;

          label {
            @include label_styles;
          }

          .dates_wrapper {
            @include display_flex(nowrap);
            justify-content: space-between;
            align-items: center;
            width: 100%;

            &:before {
              right: 50%;
            }

            .dates_separator {
              display: none;
            }
          }

          .departure_date_wrapper {
            padding-left: 15px;
            @include separator;
          }

          .entry_date_wrapper, .departure_date_wrapper {
            width: 50%;

            .date_box {
              text-align: left;

              .date_day, .date_year {
                @include option_styles($ff: $primary_font);
                border-bottom: none !important;
                font-family: $primary_font;
                text-transform: lowercase;

                .day {
                  margin-right: 7px;
                }
              }
            }
          }
        }

        .dates_selector_personalized {
          display: none;
        }

        .rooms_number_wrapper {
          display: none;
        }

        .guest_selector {
          float: left;
          padding: 0 15px;
          width: 13%;
          height: $height;
          text-align: left;
          margin-left: 10px;
          cursor: pointer;
          @include separator;
          box-sizing: border-box;

          label {
            @include label_styles;
          }

          .placeholder_text {
            @include option_styles($ff: $primary_font);
            font-family: $primary_font;
            color: $corporate_2;
            font-weight: 400;
            width: 100%;
            text-transform: lowercase;
          }

          .guest_adults {
            margin-right: 6px;
          }

          b.button {
            display: none;
          }
        }

        .room_list_wrapper {
          position: fixed;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          z-index: 1;
          width: 100%;
          background: white;
          float: none;
          display: none;
          vertical-align: middle;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 76px;
            background: #F9FCFD;
          }

          .close_guest_selector {
            position: absolute;
            width: 40px;
            height: 40px;
            border-width: 0;
            border-radius: 0;
            top: 20px;
            right: 50px;
            cursor: pointer;

            &:before, &:after {
              background: $text-color;
            }
          }

          .room_list {
            @include center_xy;
            width: auto;
            text-align: center;
            white-space: nowrap;
            display: flex;
            flex-flow: column;
            align-items: center;
            max-height: 75%;
            overflow-y: scroll;
            min-width: 650px;

            &::-webkit-scrollbar {
              width: 4px;
              height: 4px;
            }

            &::-webkit-scrollbar-thumb {
              background: $corporate_2;
              border-radius: 15px;
            }

            &::-webkit-scrollbar-thumb:hover {
              background: $corporate_1;
            }

            &::-webkit-scrollbar-track {
              background: transparent;
              border-radius: 10px;
            }

            .add_room, .remove_room {
              display: inline-block;
              vertical-align: middle;
              position: absolute;
              cursor: pointer;
              height: 30px;
              border-radius: 50%;
              bottom: -40px;
              left: 40%;
              margin: 0;
              text-align: center;
              font-size: 14px;
              letter-spacing: 0.7px;
              text-decoration: underline;
              position: absolute;
              left: 50%;
              transform: translate(-50%, 0%);
            }

            .remove_room {
              bottom: -65px;
            }

            .room_info_wrapper {
              display: table;
              height: auto;
              position: relative;
              color: $text_color;

              .hotel_name_rooms.with_name {
                background: #F5F5F5;
                font-size: 16px;
                letter-spacing: 0.8px;
                padding: 10px 50px;
                line-height: 25px;
                margin-bottom: 12px;
                border-radius: 50px;
              }

              .dates_wrapper {
                width: 100%;
                display: inline-block;
                background: #F5F5F5;
                font-size: 16px;
                letter-spacing: 0.8px;
                padding: 10px 50px;
                line-height: 24px;
                margin-bottom: 50px;
                border-radius: 50px;
                box-sizing: border-box;
              }
            }

            .wrapper_booking_button_guest {
              display: block;
              width: 280px;
              margin: 20px auto 0;

              .promocode_wrapper, .submit_button {
                display: block;
                width: 100%;
                padding: 0;
                margin: 5px 0;
              }

              .promocode_wrapper {
                padding-bottom: 15px;

                .promocode_input {
                  text-align: center;
                  -webkit-appearance: none;
                  -moz-appearance: none;
                  appearance: none;
                  background-color: transparent;
                  height: 40px;
                  font-size: 14px;
                  letter-spacing: 0.7px;
                  line-height: 44px;
                  font-weight: 400;
                  font-family: $primary_font;
                  color: $text_color;
                  border: 0.5px dashed $text_color;
                  border-radius: 50px;
                  width: 203px;


                  &::-webkit-input-placeholder {
                    color: $text_color;
                    text-transform: capitalize;
                  }

                  &::-moz-placeholder {
                    color: $text_color;
                    text-transform: capitalize;
                  }

                  &:-ms-input-placeholder {
                    color: $text_color;
                    text-transform: capitalize;
                  }

                  &:-moz-placeholder {
                    color: $text_color;
                    text-transform: capitalize;
                  }
                }
              }

              .submit_button {
                width: 203px;
                height: 62px;
                line-height: 39px;
                font-size: 16px;
                letter-spacing: 0.35px;
                border-radius: 5px;
                margin: auto;
                padding-top: 6px;
                background: $corporate-1;
                color: white;
                border: none;
                cursor: pointer;
                text-transform: uppercase;
                transition: all .4s;
                font-family: $secondary-font;

                &:hover {
                  background: #33BADA;
                }

                &:lang(fr) {
                  width: 80%;
                }
              }
            }

            &.size_2 {
              .room1 {
                border-color: $corporate_2;

                .add_room {
                  display: none !important;
                }

                .room_title,
                .remove_room,
                label,
                .room_selector .selectric .label,
                .room_selector .selectric .label:before,
                .room_selector .selectric .button:before {
                  color: $corporate_2;
                }

                .children_selector {
                  border-left-color: $corporate_2;
                }
              }
            }

            &.size_3 {
              .room1, .room2 {
                border-color: $corporate_2;

                .room_title,
                .remove_room,
                label,
                .room_selector .selectric .label,
                .room_selector .selectric .label:before,
                .room_selector .selectric .button:before {
                  color: $corporate_2;
                }

                .children_selector {
                  border-left-color: $corporate_2;
                }
              }
            }

            .room {
              position: relative;
              display: flex;
              vertical-align: middle;
              height: auto;;
              padding: 19px 5px;
              text-align: center;
              border: 1px solid $text_color;
              border-radius: 10px;
              margin: 0 0 40px;
              overflow: initial !important;
              color: $text_color;

              label {
                margin: 0;
              }

              .room_title {
                display: none;
              }

              label {
                display: inline-block;
                vertical-align: middle;
                text-transform: capitalize;
                font-size: 13px;
                letter-spacing: 1.3px;
                text-align: left;

                .range-age {
                  display: block;
                  font-size: 10px;
                }
              }

              .selectric-room_selector {
                display: inline-block;
                vertical-align: middle;

                .selectricItems {
                  display: none !important;
                }

                .selectric {
                  height: 30px;
                  margin: 0 0 0 5px;

                  .label {
                    text-align: center;
                    margin-left: -2px;
                    color: $text-color;
                    font-family: $primary_font;
                    font-size: 18px;
                    line-height: 30px;
                    font-weight: 500;

                    &:before {
                      content: '\f067';
                      position: absolute;
                      top: 50%;
                      right: 5px;
                      -webkit-transform: translateY(-50%);
                      -moz-transform: translateY(-50%);
                      -ms-transform: translateY(-50%);
                      -o-transform: translateY(-50%);
                      transform: translateY(-50%);
                      font-family: "Font Awesome 5 Pro";
                      font-weight: bold;
                      font-size: 14px;
                      color: $text-color;
                    }
                  }

                  .button {
                    position: absolute;
                    top: 0;
                    text-indent: 0;
                    height: auto;
                    color: white;
                    margin: 0;
                    font-size: 0;
                    left: -2px;
                    line-height: 24px;
                    background: transparent !important;
                    text-shadow: 0 0 0 rgba(0, 0, 0, 0) !important;

                    &:before {
                      content: '\f068';
                      position: absolute;
                      top: 50%;
                      right: 15px;
                      -webkit-transform: translateY(-50%);
                      -moz-transform: translateY(-50%);
                      -ms-transform: translateY(-50%);
                      -o-transform: translateY(-50%);
                      transform: translateY(-50%);
                      display: inline-block;
                      vertical-align: middle;
                      font-family: "Font Awesome 5 Pro";
                      font-weight: bold;
                      font-size: 14px;
                      line-height: 24px;
                      color: $text-color;
                    }
                  }
                }
              }

              .adults_selector, .children_selector, .babies_selector {
                height: auto;
                padding: 5px 25px;
                border-left-width: 0;
                border-right: 1px solid $text_color;


                .label {
                  margin: 0;
                }
              }

              .children_selector {
                border-right: none;
              }

              &.room_with_babies {
                .children_selector {
                  border-right: 1px solid $text_color;
                }

                .babies_selector {
                  border-right: none;
                }
              }
            }

            .full_ages_wrapper {
              display: none;
              margin-bottom: 10px;

              .kids_age_selection {
                .kid_age_element_wrapper, .kid_age_element_wrapper.hide {
                  display: none;
                }
              }

              .slides_wrapper {
                .input_slide {
                  display: inline-flex;
                  flex-flow: column;
                  width: 168px;

                  .slide_content {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 10px;
                    font-size: 14px;

                    .max-value {
                      font-weight: bold;
                    }
                  }

                  input[type="range"].slider_age {
                    appearance: none;
                    -moz-appearance: none;
                    -webkit-appearance: none;
                    display: block;
                    background: lightgray;
                    width: 168px;
                    height: 4px;
                    background-image: -webkit-gradient(linear, 0% 0%, 100% 0%, from(rgb(0, 45, 66)), from(rgb(211, 211, 211)));
                    background-image: -moz-linear-gradient(left center,
                            $corporate_2 0%, $corporate_2 0%,
                            lightgray 100%, lightgray 100%);

                    &::-webkit-slider-thumb {
                      appearance: none;
                      -webkit-appearance: none;
                      border: 1px solid $corporate_2;
                      height: 19px;
                      width: 19px;
                      border-radius: 50%;
                      background: $corporate_2;
                      cursor: pointer;
                      margin-top: 0;
                    }

                    &::-moz-range-thumb, &::-ms-thumb {
                      appearance: none;
                      -webkit-appearance: none;
                      border: 1px solid $corporate_2;
                      height: 19px;
                      width: 19px;
                      border-radius: 50%;
                      background: $corporate_2;
                      cursor: pointer;
                    }

                    &::-webkit-slider-runnable-track, &::-moz-range-track, &::-ms-track {
                      appearance: none;
                      -webkit-appearance: none;
                      -moz-appearance: none;
                      width: 210px;
                      height: 4px;
                      cursor: pointer;
                      border: none;
                      background: lightgray;
                    }

                    &:focus {
                      outline: none;

                      &::-webkit-slider-thumb {
                        margin-top: -7px;
                      }

                      &::-webkit-slider-runnable-track {
                        height: 4px;
                      }
                    }

                    &::-ms-track {
                      width: 100%;
                      cursor: pointer;
                      background: transparent;
                      border-color: transparent;
                      color: transparent;
                    }
                  }

                  &.hide {
                    display: none;
                  }

                  &.show {
                    &:not(:first-of-type) {
                      margin-left: 40px;
                    }
                  }
                }
              }

              &.show {
                display: block;
              }
            }
          }
        }

        .wrapper_booking_button {
          position: relative;
          display: inline-block;
          float: right;
          width: 30%;
          height: $height;
          @include separator;

          &:before {
            content: "";
            left: 0;
            top: 10px;
            bottom: 10px;
            width: 1px;
            position: absolute;
            background-color: white;
          }

          .promocode_wrapper {
            display: inline-block;
            float: left;
            height: 100%;
            width: 43%;
            position: relative;
            padding: 0;
            border-top: none;

            label {
              display: none;
            }

            .promocode_input {
              @include center_xy;
              text-align: center;
              -webkit-appearance: none;
              -moz-appearance: none;
              appearance: none;
              background-color: transparent;
              width: 85%;
              height: 40px;
              font-size: 16px;
              line-height: 20px;
              font-weight: 400;
              font-family: $primary_font;
              color: $text-color;
              border: none;

              &:focus {
                outline: 0;
              }

              &::-webkit-input-placeholder {
                @include promocode_placeholder;
                transform: translateY(-7px);
              }

              &:-moz-placeholder {
                @include promocode_placeholder;
                transform: translateY(-7px);
              }

              &::-moz-placeholder {
                @include promocode_placeholder;
                transform: translateY(-7px);
              }

              &:-ms-input-placeholder {
                @include promocode_placeholder;
                transform: translateY(-7px);
              }

              &::placeholder {
                @include promocode_placeholder;
                transform: translateY(-7px);
              }
            }
          }

          .submit_button {
            display: flex;
            justify-content: center;
            align-items: center;
            float: left;
            min-width: 165px;
            height: 100%;
            padding-top: 5px;
            font-size: 16px;
            font-weight: 400;
            letter-spacing: 0.35px;
            line-height: 39px;
            border-radius: 5px;
            font-family: $secondary-font;
            background-color: $corporate_1;
            text-transform: uppercase;
            color: white;
            margin: 0;
            border-style: none;
            cursor: pointer;
            @include transition(background-color, .4s);

            &:hover {
              background-color: #33BADA !important;
            }
          }
        }
      }
    }
  }

  .widget_buttons {
    position: absolute;
    bottom: 100px;
    display: flex;
    justify-content: center;
    align-items: center;

    .content_wrapper {
      .button {
        background: #01788E;
        padding: 10px 20px;
        border-radius: 50px;
        margin-right: 10px;
        color: white;
        font-size: 14px;
        letter-spacing: 0.7px;
        line-height: 20px;
        font-weight: 500;
        text-decoration: none;
        font-family: $primary_font;
        position: relative;
        @include box_shadow;

        &:before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: -1;
          border-radius: 15px;
          backdrop-filter: blur(15px);
          -webkit-backdrop-filter: blur(15px);
        }

        i {
          font-size: 15px;
          margin-right: 5px;
        }

        svg {
          position: relative;
          top: 5px;
        }
      }
    }
  }

  &.open {
    transform: none;

    .widget_buttons {
      display: none;
    }

    #full_wrapper_booking {
      .top_popup_link {
        display: block;
      }

      .logotype_widget {
        display: block;
      }

      .booking_steps {
        display: flex;
      }

      .hotel_selector {
        display: block;
      }
    }
  }
}

.top_popup_link {
  position: fixed;
  left: 50px;
  top: 25px;
  text-decoration: none;
  font-family: $primary_font;
  font-size: 16px;
  letter-spacing: 0.35px;
  line-height: 20px;
  color: $text_color;
  font-weight: 300;
  z-index: 501;
  display: none;

  i {
    margin-right: 10px;
    font-size: 17px;
  }
}

.logotype_widget {
  position: fixed;
  left: 50%;
  top: 20px;
  transform: translateX(-50%);
  display: none;
  width: 145px;
  z-index: 501;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.top_section_popup {
  display: none;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 501;

  &::before {
    @include full_size;
    content: '';
    background-color: #525252;
    opacity: .65;
  }

  .popup_content_wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 615px;
    padding: 40px;
    text-align: left;
    background: white;
    border-radius: 20px;
    box-shadow: #FFFFFF 0% 0% no-repeat padding-box;
    box-sizing: border-box;

    .close_popup {
      position: absolute;
      top: 40px;
      right: 40px;
      cursor: pointer;
      z-index: 1;

      i {
        color: $text_color;
        font-size: 22px;
        transition: all .4s;
      }
    }

    .popup_title {
      position: relative;
      font-family: $primary_font;
      color: $text_color;
      font-size: 24px;
      line-height: 32px;
      font-weight: 700;
      margin-bottom: 30px;
    }

    .popup_list {
      list-style: none;
      padding-left: 0;

      li {
        margin-bottom: 20px;
        display: flex;

        i {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background: $corporate-1;
          color: white;
          margin-right: 30px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          vertical-align: middle;
          font-size: 22px;
        }

        .info_contact {
          .title_link_popup {
            font-family: $primary_font;
            color: $corporate-1;
            font-size: 18px;
            line-height: 20px;
            text-transform: uppercase;
            font-weight: 700;
            margin-bottom: 5px;
            display: block;
          }

          .description_link_popup {
            font-family: $primary_font;
            color: $text_color;
            font-size: 16px;
            line-height: 27px;
            font-weight: 400;

            strong {
              font-size: 18px;
            }
          }
        }
      }
    }
  }
}

.booking_steps {
  position: fixed;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 1021;
  top: 90px;
  text-align: center;
  display: none;
  justify-content: center;
  align-items: stretch;
  width: 1000px;
  padding: 0 0 20px 0;

  .content_wrapper {
    display: flex;
    padding: 15px 0;
    margin: 0 auto;
    list-style: none;
    justify-content: space-evenly;

    .step {
      display: flex;
      align-items: center;
      position: relative;
      cursor: pointer;
      color: $corporate-1;
      letter-spacing: 0.7px;
      font-family: $primary_font;
      font-size: 14px;
      line-height: 13px;
      font-weight: 300;

      .number_step {
        font-family: $primary_font;
        background: transparent;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        border: 1px solid $corporate-1;
        display: inline-block;
        margin-right: 20px;
        position: relative;

        span {
          font-family: $primary_font;
          font-size: 12px;
          letter-spacing: 0.66px;
          line-height: 12px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }

        i {
          display: none;
        }
      }

      span.booking-step {
        background: transparent;
      }

      &.current_step {
        .number_step {
          background: $corporate-1;

          span {
            color: white;
          }
        }
      }

      &.done {
        .number_step {
          background: $corporate-1;
          display: flex;
          justify-content: center;
          align-items: center;
          color: white;
          border-color: $corporate-1;
          font-size: 18px;

          span {
            display: none;
          }

          i {
            display: inline;
          }
        }
      }

      &:first-of-type {
        &::before {
          content: "";
          position: absolute;
          left: 124%;
          top: 50%;
          background: #D8D8D8;
          height: 1px;
          width: 70px;

          html[lang="es"] & {
            left: 114%;
          }
        }
      }

      &:not(:first-of-type):not(:last-of-type) {
        &::before {
          content: "";
          position: absolute;
          left: 130%;
          top: 50%;
          background: #D8D8D8;
          height: 1px;
          width: 70px;

          html[lang="es"] & {
            left: 113%;
          }
        }
      }
    }
  }
}

.ui-dialog.ui-widget {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%);
  background-color: white;
  font-family: $primary_font;
  color: $text_color;

  .ui-dialog-title {
    font-weight: 400;
    font-size: 20px;
    letter-spacing: 0.9px;
  }

  .ui-dialog-buttonpane {
    border: none !important;

    .ui-dialog-buttonset {
      .ui-button {
        position: relative;
        padding: 15px;
        border: none;
        border-radius: 0;
        background-color: $corporate_1;
        font-family: $primary_font;
        font-weight: 700;
        font-size: 18px;
        letter-spacing: 1px;
        line-height: 1;
        text-transform: uppercase;
        color: white;
        cursor: pointer;

        &:lang(ru) {
          font-size: 16px;
        }

        &:hover {
          background-color: $corporate_2;
        }
      }
    }
  }
}

#fancybox-overlay{
  opacity: 0.95!important;
}