div#full_wrapper_booking.booking_widget_step_-1 {

}


#wizard.step_-1 {
  #step-0 {
    .booking_0_buttons_controller {
      //* temp *//
      display: none;

      .see_list_button, .see_map_button {
        border: none;
        border-radius: 10px 10px 0 0;
        height: 22px;
        padding-top: 16px;
        color: $corporate_1;
        font-size: 16px;
        letter-spacing: 0.35px;
        font-weight: 400;
        font-family: $font_2;

        &:not(.active) {
          border: 1px solid lightgrey;
          height: 21px;
          border-bottom: 0;
        }
        
        .fa.fa-list {
            font-size: 23px;
            vertical-align: middle;
            margin-right: 10px;
            margin-top: -5px;
        }

        .icon-mapmarker {
          font-family: "Font Awesome 6 Pro"!important;
          font-weight: 300;
          margin-right: 10px;

          &::before {
            content: '\f3c5';
          }
        }
      }
    }

    .hotels_list_wrapper {
      .booking-0-hotel-item {
        box-shadow: 0px 0px 5px -1px rgba(0, 0, 0, 0.1);

        .hotels_list_wrapper {
          padding: 15px 0;
          background-color: #eff4f8;
        }

        .info_hotel_wrapper {
          .booking-box {
            .booking-box__title {
              font-family: $font_1;
              background-color: white;
              padding: 24px 0 10px 18px;
              color: $corporate_3;
              border-bottom: 1px solid lightgray;

              .hotel-title__name {
                text-transform: none;
                font-size: 24px;
                line-height: 35px;
                letter-spacing: 0;
                font-weight: 400;
              }
              .hotel-title__phone {
                font-family: $font_2;
              }
            }

            .booking-box__content.hotel_description {
              .hotel_description_container {
                font-family: $font_2;
                color: $black;
                font-size: 14px;
                margin-bottom: 10px;
              }

              .read_more {
                color: $corporate_3;
              }

              .bottom_buttons_links {
                .flexible_days {
                  font-family: $font_2;
                  color: $corporate_1;
                  font-size: 14px;
                  letter-spacing: .75px;

                  .fa-bar-chart {
                    &::before {
                      content: '\f080';
                    }
                  }
                }

                .buttons_booking0 a > div {
                  background-color: $corporate_1;


                  i {
                    font-family: "Font Awesome 6 Pro" !important;
                    font-size: 20px;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: 300;
                  }

                  .icon-mapmarker::before {
                    font-family: 'icomoon';
                    content: "\e947";
                  }

                  .fa.fa-camera::before {
                    content: '\f083';
                  }
                }
              }
            }

            .booking-box--action {
              padding: 20px 30px;
              margin-top: 0;

              .booking-box__title {
                padding: 0;
                border: 0;
                font-size: 14px;
                color: $corporate_3;
                font-family: $font_2;
                font-weight: 700;
              }

              .booking-box__content {
                .not_available_prices {
                  font-family: $font_2;
                  color: $corporate_3;
                }

                .hotel-promotion {
                  .hotel-price__promotion {
                    font-weight: 300;
                    letter-spacing: .75px;
                    color: red;
                    font-size: 16px;
                  }
                }

                .hotel-price {
                  .hotel-price__current {
                    font-weight: 300;
                    letter-spacing: .75px;
                    color: $black;
                  }
                }

                .price_information, .included_tax {
                  font-weight: 300;
                  letter-spacing: .75px;
                  color: $black;
                }

                .booking-button.booking-button--action {
                  font-family: $font_1;
                  background-color: $corporate_1;
                  font-size: 14px;
                  line-height: 24px;
                  letter-spacing: 1px;
                  transition: all .6s;
                  &:hover {
                    background: $corporate_3;
                  }
                }

                .no_availability_hotel_button {
                  font-family: $font_2;
                  background-color: $corporate_1;
                  text-decoration: none;

                  .fa-calendar {
                    font-family: "Font Awesome 6 Pro";

                    &::before {
                      content: '\f073';
                      font-weight: 300;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .map_hotels_list_wrapper {
      .gm-style-iw-c {
        border-radius: 4px;
      }

      .hotel_poi_info {
        padding: 0;
        min-height: 70px;

        .picture_poi {
          width: 80px;
          max-width: initial;
          height: 80px;
        }

        .poi_tit_desc_wrapper {
          font-family: $font_2;
          color: $corporate_3;

          .booking_poi_button {
            background-color: $corporate_1;
            padding: 10px 20px;
          }
        }
      }
    }
  }
}


.hotel_content_popup {
  .hotel_name {
    font-family: $font_2;
    color: $corporate_3;
    margin-bottom: 10px;
  }
}