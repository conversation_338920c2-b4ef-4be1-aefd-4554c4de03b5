.site-header {
  background: $corporate_1;

  .language_header_selector_booking_process {
    border-width: 0;
    width: 200px;

    .selected_language {
      position: relative;
      color: #5d5a5b;
      font-size: 16px;
      font-weight: normal;
      text-transform: uppercase;
      font-family: $title_family;
      @extend .fa-angle-down;
      &:before {
        @extend .fa;
        position: absolute;
        top: 50%;
        -webkit-transform: translate(0, -50%);
        -moz-transform: translate(0, -50%);
        -ms-transform: translate(0, -50%);
        -o-transform: translate(0, -50%);
        transform: translate(0, -50%);
        right: 10px;
      }
      i.fa {
        font-size: 25px;
        vertical-align: middle;
        text-indent: -20px;
        color: #5d5a5b;
      }
      span {
        display: inline-block;
        vertical-align: middle;
      }
    }
  }
  .site-header__ticks {
    margin-right: 100px;
    .site-header__tick-item {
      display: inline-block;
      vertical-align: middle;
      &:first-of-type {
        margin-top: 5px;

        .icon-checkmark {
          background: none;
          padding: 0;

          &:before {
            font-family: "icomoon";
            content: "\e962";
            color: $corporate_3;
            font-size: 45px;
          }
        }

        p {
          top: -2px;
        }
      }

      &:nth-of-type(2) {
        margin-top: 0;
        .icon-checkmark {
          background: none;

          &:before {
            font-family: "FontAwesome";
            content: "\f132";
            color: $corporate_3;
          }
        }
      }

      &:nth-of-type(3) {
        margin-top: 0;
        .icon-checkmark {
          background: none;

          &:before {
            font-family: "icomoon";
            content: "\e913";
            color: $corporate_3;
          }
        }
      }
      &:nth-of-type(4) {
        display: none;
      }

      span {
        font-size: 40px;
        display: inline-block;
        vertical-align: middle;
      }

      p {
        width: 70px;
        color: #5d5a5b;
        display: inline-block;
        vertical-align: middle;
        text-transform: uppercase;
        font-size: 10px;
        line-height: 12px;
        letter-spacing: 1px;
      }
    }
  }
}

.site-footer {
  background: $corporate_1;
}

.booking_engine_wrapper_process {
  background-color: #F0F0F0;
  #booking.boking_widget_inline {
    .entry_date_wrapper, .departure_date_wrapper {
      .entry_date, .departure_date {
        margin-top: 0;
        .date_day {
          font-size: 18px !important;
          color: black;
          font-family: $title_family;
          font-weight: 500;

        }
      }
    }
    .entry_date_wrapper {
      @extend .fa-angle-right;
      &:before {
        @extend .fa;
        position: absolute;
        bottom: 0;
        right: 10px;
        font-size: 50px;
        -webkit-transform: scale(0.5, 1);
        -moz-transform: scale(0.5, 1);
        -ms-transform: scale(0.5, 1);
        -o-transform: scale(0.5, 1);
        transform: scale(0.5, 1);
      }
    }
    .departure_date_wrapper {
      @extend .fa-calendar;
      &:before {
        @extend .fa;
        position: absolute;
        bottom: 10px;
        right: 10px;
        font-size: 20px;
        color: #DEDEDE;
      }
      .departure_date {
        background: transparent;
      }
    }
    .guest_selector {
      @extend .fa-user;
      &:before {
        @extend .fa;
        position: absolute;
        bottom: 10px;
        right: 10px;
        font-size: 20px;
        color: #DEDEDE;
      }
      .placeholder_text {
        font-size: 18px !important;
        color: black;
        font-family: $title_family;
        font-weight: 500;
        margin-top: 0;
      }
    }
    .wrapper_booking_button {
      .promocode_wrapper {
        label.promocode_label {
          margin-top: 5px;
          font-weight: normal;
          font-size: 12px;
          line-height: 12px;
          color: #999;
        }
      }
      button.submit_button {
        position: relative;
        background-color: transparent;
        height: 45px;
        padding: 0;
        overflow: hidden;
        margin-bottom: 5px;
        letter-spacing: 1px;
        box-shadow: 4px 4px #c5c5c7;

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          background: darken($corporate_4, 10%);
          background: linear-gradient(172.5deg, $corporate_4 0, $corporate_4 50%, darken($corporate_4, 2%) 0, darken($corporate_4, 2%) 75%);
          width: 100%;
          z-index: -1;
          @include transition(all, .6s);
        }
        &:hover {
          background-color: $corporate_4_hover;
        }
      }
      .spinner_wrapper {
        display: none;
      }
    }
  }
}

.wizard-tab--small a,
.wizard-tab--big a {
  background-color: $corporate_2;
  &:after, &:before {
    border-left-color: $corporate_2;
  }
  &.disable {
    background-color: #C5C5C7;
    &:after {
      border-left-color: #C5C5C7;
    }
    &:before {
      border-left-color: white;
      left: calc(100% + 2px);
    }
  }
}

.rate_conditions_popup_wrapper .rate_description_content {
  color: #000 !important;
  font-weight: 300;
}

.room_step_selection td.filaTipoTarifa {

}

div#step-1 {
  .booking-box--search, .hidden_booking_summary {
    .booking-box__content {
      font-family: $title_family;
      background: white;
      border: 1px solid $corporate_2;
      box-shadow: 4px 4px #c5c5c7;

      .title_booking_breakdown {
        font-style: italic;
        font-weight: normal;
        color: #2b2b2b;
      }
      .booking-title-info, .notranslate, .booking-search-results__rooms-list {
        font-size: 11px;
      }
      .booking-search-results__search-data {
        text-transform: uppercase;
        .booking-3-info, .booking-3-info--title, .booking-3-info--price, .booking-hotel-name {
          color: lighten($corporate_2, 40%);
        }
      }
      .booking-button {
        background-color: $corporate_4;
        letter-spacing: 1px;
        font-size: 12px;
        border-radius: 0;

        &:hover {
          background-color: $corporate_4_hover;
        }
      }
      #currencyDiv {
        padding-right: 5px;
        border-radius: 0;
        background-color: #AAA;
        @extend .fa-angle-down;
        &:before {
          @extend .fa;
          position: absolute;
          top: 50%;
          -webkit-transform: translate(0, -50%);
          -moz-transform: translate(0, -50%);
          -ms-transform: translate(0, -50%);
          -o-transform: translate(0, -50%);
          transform: translate(0, -50%);
          right: 25px;
          color: white;
          font-size: 18px;
        }
        label.see_prices_label {
          display: none;
        }
        #currencySelect {
          width: 60px;
          color: white;
        }
      }
    }

    .call_center_wrapper {
      border-left: 1px solid white;
    }
  }

  .hidden_booking_summary {
    border-bottom: 1px solid $corporate_2;

    .booking-search-results.booking-box__content {
      border: 0;
      box-shadow: initial;
    }

    .title_booking_breakdown {
      padding-right: 20px;
    }
  }

  .cheapest_rate_message {
    letter-spacing: 1px;
    background-color: $corporate_2;
    font-family: $title_family;
    .conditions_info_wrapper a {
      font-size: 8px;
      text-transform: capitalize;
      text-align: left;
      padding-left: 40px;
      padding-right: 0;
      box-sizing: border-box;
    }
    .before_block {
      border-left-color: $corporate_1;
    }
  }
  .advice_rate_message {
    letter-spacing: 1px;
    background-color: $corporate_2;
    font-family: $title_family;
    &.has_conditions {
      text-align: left;
      .conditions_info_wrapper a {
        font-size: 8px;
        text-transform: capitalize;
        text-align: left;
        padding-left: 40px;
        padding-right: 0;
        box-sizing: border-box;
      }
    }
  }

  .see_more_rooms_v2 {
    .plus_sign {
      display: inline-block;
      vertical-align: middle;
      color: black;
      font-size: 10px;
      padding-right: 0;
    }
    .see_more {
      color: black;
      text-transform: uppercase;
      text-decoration: none;
      font-size: 12px;
    }
  }
  table.listadoHabsTarifas {
    .booking-button {
      position: relative;
      z-index: 9;
      border-radius: 0;
      overflow: hidden;
      background-color: transparent;
      letter-spacing: 2px;

      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        background: $corporate_4;
        width: 100%;
        z-index: -1;
        @include transition(all, .6s);
      }

      &:hover:before {
        background-color: $corporate_4_hover;
        background: $corporate_4_hover;
      }
    }
    font-family: $title_family;
    td.filaTipoTarifa {
      width: 400px;
      .contTitTipoTarifa .titTipoTarifa {
        width: auto;
      }
    }
    td.regimenColumn {
      display: block;
      width: 500px;
      margin-bottom: 10px;
      text-align: left !important;
      .regimen_element_title {
        color: $corporate_1;
      }
      .input_selector_wrapper {
        float: left;
        input[type='radio'] {
          margin-right: 10px;
        }
      }
    }
  }
}

.wizard-tab--small {
  width: 33%;
}

.wizard-tab--small:nth-of-type(2) a, .wizard-tab--big:nth-of-type(2) a {
  display: none;
}

div#step-2 {
  .booking-2-service-container .booking-2-service-title {
    line-height: 25px;
    margin-top: 9px;
  }
}

.description_bottom_popup_booking {
  color: white !important;
}

.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube:before {
  background-color: $corporate_2;
}

.description_bottom_popup_booking {
  color: #195795 !important;
}
