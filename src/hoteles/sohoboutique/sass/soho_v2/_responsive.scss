$mobile_height: 75px;
$mobile_padding: 15px 20px;

@media (min-width: 1141px) {
  #widget_paraty {
    display: block !important;
  }

  .datepicker_wrapper_element,
  .datepicker_wrapper_element_2,
  .datepicker_wrapper_element_3 {
    width: auto !important;
  }
}

@media (max-width: 1140px) {
  #widget_paraty {
    border: none;

    &.open {
      .close_widget {
        display: none;
      }
    }
  }
  .widget_paratybg{
    display:none;
  }
  .datepicker_wrapper_element .specific_month_selector,
  .datepicker_wrapper_element .go_back_button,
  .datepicker_wrapper_element_2 .specific_month_selector,
  .datepicker_wrapper_element_2 .go_back_button,
  .datepicker_wrapper_element_3 .specific_month_selector,
  .datepicker_wrapper_element_3 .go_back_button {
    display: none !important;
  }
  .datepicker_wrapper_element,
  .datepicker_wrapper_element_2,
  .datepicker_wrapper_element_3 {
    z-index: 1000000 !important;
    position: fixed !important;
    min-width: unset;

    .header_datepicker {
      .close_button_datepicker {
        width: 30px;
        height: 30px;
        top: 15px;
        right: 15px;
      }
    }

    .datepicker_ext_inf_sd,
    .datepicker_ext_inf_ed {
      padding: 0 20px;

      .ui-widget-content {
        width: 100% !important;

        .ui-widget-header {
          margin-right: 0;
        }

        .ui-datepicker-calendar {
          td {
            height: 45px;
          }

          td.ui-datepicker-start_date {
            span {
              border-radius: 50px;
            }
          }

          td.last-highlight-selection {
            a {
              background: white !important;
              color: $text-color !important;
            }
          }
        }
      }
    }
  }
  #widget_paraty {
    z-index: 100000;
    display: none;
    top: 0;
    bottom: 0;

    .widget_top_icons {
      display: none;
    }

    &.auto-position {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      @include transform(none);
    }

    #full_wrapper_booking {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: white;
      border-radius: 0;
      transform: none;
      width: auto;

      #full-booking-engine-html-7 {
        @include full_size;
        @include display_flex(nowrap);
        justify-content: center;
        align-items: center;
        text-align: left;

        .calendar_root_wrapper {
          top: 0;

          .close_calendar_app {
            width: 20px;
            height: 20px;
            top: 25px;
            right: 25px;
          }

          .calendar_app {
            .price_calendar_wrapper {
              top: 60px;
              overflow-y: auto;
              max-height: 87%;
              padding-bottom: 50px;
              transform: translate(-50%, 0);

              .full_container {
                padding: 0;

                .calendar_wrapper {
                  margin: 0;

                  .month_selector_wrapper {
                    .selector_label {
                      font-family: $primary_font;
                      color: $text-color;
                      font-weight: 500;
                    }

                    .selector {
                      .option {
                        color: $text-color;
                        font-family: $primary_font;

                        &:hover {
                          opacity: 0.7;
                        }
                      }
                    }
                  }

                  .month_full_wrapper {
                    table.month_wrapper {
                      tbody {
                        tr {
                          &.week-row {
                            height: 55px;
                          }

                          td.day_wrapper {
                            .day {
                              font-family: $primary_font;
                              color: $text-color;
                              font-weight: 500;
                            }

                            .price {
                              font-family: $primary_font;
                            }

                            &.disabled,
                            &.closed {
                              .day,
                              .no_dispo_text {
                                color: #ADADAD;
                              }
                            }

                            &.selected {
                              .day {
                                color: white;
                              }
                            }
                          }
                        }
                      }
                    }
                  }

                }
              }
            }
          }
        }

        form.booking_form.paraty-booking-form {
          position: static;
          max-width: 400px;
          width: 100%;
          padding: 15px;
          flex-direction: column;

          .destination_wrapper {
            width: calc(100% - 40px) !important;
            padding: 15px 20px;
            height: 75px;
            justify-content: space-between;
            background: white;
            border-bottom: 1px solid $black;
            border-radius: 0;

            .icon {
              top: 35%;
            }

            label {
              display: none;
            }

            .destination {
              height: auto;
              text-indent: 0;
              font-size: 18px;
              text-align: center;
            }

            .right_arrow {
              display: none;
            }
          }

          .hotel_selector {
            top: 0;
            padding: 20px 20px 80px;

            .hotel_selector_filter {
              .destiny_filter {
                margin-right: 20px;
                font-size: 13px;
              }
            }

            .close {
              width: 20px;
              height: 20px;
              top: 25px;
              right: 25px;
              z-index: 1;
            }

            .center_xy {
              top: initial;
              bottom: initial;
              width: 100%;
              padding: 0;
              overflow-y: auto;
              max-height: initial;

              .hotel_selector_search {
                margin-bottom: 20px;

                i {
                  font-size: 16px;
                }

                .searching_hotel {
                  width: calc(100% - 110px)!important;
                  font-size: 16px;
                  padding: 10px 10px 10px 40px;
                  height: 20px!important;

                  &::-webkit-input-placeholder { /* Chrome/Opera/Safari */
                    font-size: 16px;
                  }

                  &::-moz-placeholder { /* Firefox 19+ */
                    font-size: 16px;
                  }

                  &:-ms-input-placeholder { /* IE 10+ */
                    font-size: 16px;
                  }

                  &:-moz-placeholder { /* Firefox 18- */
                    font-size: 16px;
                  }
                }
              }

              .hotel_selector_inner {
                width: 100%;
                top: 120px;

                .hotels_grouped {
                  height: auto;
                  display: block;
                  column-count: initial;

                  .hotels_group {
                    width: 100%;
                    margin-bottom: 20px;

                    .hotels_group_inner {
                      .hotels_destiny_title {
                        margin: 0 0 5px;
                      }

                      ul {
                        li.hotel_selector_option {
                          cursor: pointer;

                          .title_selector {
                            font-size: 14px;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }

          .stay_selection {
            width: calc(100% - 40px) !important;
            padding: $mobile_padding;
            height: $mobile_height;
            justify-content: center;
            background: white;
            border-bottom: 1px solid $black;
            margin-top: 15px;

            &:after {
              display: none;
            }

            .stay_label {
              display: block;
            }

            .entry_date_wrapper,
            .departure_date_wrapper {
              text-align: center;
              padding: 0;

              .date_box {
                text-align: center;
              }

              &:before {
                display: none;
              }
            }

            /*.dates_wrapper {
              justify-content: flex-start;

              &::before {
                order: 2;
                position: static;
                margin: 0 10px;
                @include transform(none);
              }
            }*/
          }

          .rooms_number_wrapper {
            width: 100% !important;
            padding: $mobile_padding;
            height: $mobile_height;

            &:after {
              display: none;
            }

            .selectricWrapper {
              .selectricItems {
                top: auto;
                bottom: 50px;
                left: 50%;
                transform: translateX(-50%);
                width: calc(100% - 40px);
              }
            }
          }

          .guest_selector {
            width: calc(100% - 40px) !important;
            justify-content: space-between;
            align-items: stretch;
            background: white;
            border-bottom: 1px solid $black;
            margin-top: 15px;
            padding: 15px 20px;

            &:after {
              display: none;
            }

            label {
              display: none;
            }

            .placeholder_text {
              justify-content: flex-start;
            }
          }

          .room_list_wrapper {
            z-index: 501;
            top: 0;

            .close_guest_selector {
              width: 20px;
              height: 20px;
              top: 25px;
              right: 25px;
            }

            .room_list {
              top: 50px;
              max-height: calc(100vh - 150px);
              min-width: unset;
              width: calc(100% - 40px);
              padding: 0 20px;
              white-space: initial;
              overflow: initial;

              .wrapper_booking_button_guest {
                margin: 60px auto 0;
                width: auto;

                .promocode_input:focus,
                .promocode_input:focus-visible {
                  outline-color: transparent;
                }
              }

              .room_info_wrapper {
                .dates_wrapper {
                  margin-bottom: 30px;
                  font-size: 16px;
                  line-height: 24px;

                  .dates_wrapper_title {
                    font-size: 18px;
                    line-height: 24px;
                  }
                }
              }

              .room {
                width: 100%;
                padding: 10px 0;
                margin: 0 0 20px;

                .selectric-room_selector .selectric {
                  height: 15px;

                  .label {
                    line-height: 15px;
                  }
                }

                .adults_selector, .children_selector, .babies_selector {
                  width: calc(100% / 2) !important;
                  padding: 0 15px;

                  label {
                    display: block;
                    text-align: center;
                  }
                }
              }
            }
          }

          .wrapper_booking_button {
            display: inline-flex;
            flex-wrap: wrap;
            width: 203px !important;
            height: auto;
            margin: 30px auto;

            &::after {
              display: none;
            }

            .promocode_wrapper {
              width: 100%;
              padding-bottom: 15px;

              .promocode_label {
                display: none;
              }

              input.promocode_input {
                text-align: center;
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                background-color: transparent;
                height: 40px;
                font-size: 16px;
                font-weight: 300;
                font-family: $primary_font;
                color: $black;
                border: none;
                border-bottom: 0.5px solid $black;
                width: 100%;

                &::placeholder {
                  font-size: 16px;
                  color: $black;
                }

                &::-webkit-input-placeholder {
                  font-size: 16px;
                  color: $black;
                }

                &::-moz-placeholder {
                  font-size: 16px;
                  color: $black;
                }

                &:-ms-input-placeholder {
                  font-size: 16px;
                  color: $black;
                }
              }

              &:after {
                display: none;
              }
            }

            .submit_button {
              width: 100%;
              height: 62px;
              font-size: 20px;
              letter-spacing: 0.44px;
              padding-top: 5px;
              top: 0;
              position: relative;
              background-color: $corporate_1;
              color: white;

              &:before, &:after {
                display: none;
              }
            }
          }
        }
      }
    }

    .close_widget {
      position: fixed;
      top: 25px;
      right: 25px;
      text-align: right;
      color: $text-color;
      display: block;
      font-family: "Font Awesome 6 Pro" !important;
      font-weight: 300 !important;
      font-size: 24px;
      cursor: pointer;
      z-index: 3;
    }

    .widget_buttons {
      display: block !important;
      align-items: center;
      position: fixed;
      bottom: 35px;
      left: 50%;
      transform: translateX(-50%);
      right: initial;
      top: initial!important;
      z-index: 4;

      /*.content_wrapper {
        .button {
          background: #15151560;
          color: #614B79;

          &:before {
            display: none;
          }

          svg, svg * {
            fill: white;
          }
        }
      }*/
    }

    .logotype_widget {
      display: block;
      z-index: 1;
      position: absolute;
      top: 50px;
      left: 50%;
      transform: translate(-50%, 0%);
      width: 109px;
      height: 33px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .booking_steps {
    top: initial;
    left: 20px;
    right: 20px;
    bottom: 20px;
    width: auto;
    margin: 0 auto;

    .step {
      position: relative;
      width: 100%;
      margin: 0 auto;
      font-family: $primary_font;
      font-size: 16px;
      color: $black;
      display: none;

      &.current_step {
        display: block;
      }

      &:before {
        content: '';
        width: 33.3%;
        height: 8px;
        background: $black;
        position: absolute;
        left: 0;
        bottom: -15px;
        border-radius: 12px;
        z-index: 1;
      }

      &:after {
        content: '';
        width: 100%;
        height: 8px;
        background: #d6d6d6;
        @include center_x;
        bottom: -15px;
        border-radius: 12px;
      }

      &.step_2 {
        &:before {
          width: 66.3%;
        }
      }

      &.step_3 {
        &:before {
          width: 100%;
        }
      }
    }

    &::before {
      position: absolute;
      content: '';
      bottom: -20px;
      left: 0;
      right: 0;
      height: 70px;
      background: linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
    }
  }

  #floating_button_paraty.auto-position {
    display: inline-block;
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(270deg, rgba(169, 169, 169, 1) 0%, rgba(96, 97, 99, 1) 100%);
    color: white;
    cursor: pointer;
    z-index: 1000;
    text-transform: uppercase;
    font-family: $primary_font;
    letter-spacing: 1px;
    font-size: 20px;
    font-weight: 600;
    height: 60px;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    &.hidden {
      display: none !important;
    }
  }

  .fancy-booking-search_v2 .container_popup_booking {
    max-width: 100%;
    width: 100%;

    img {
      width: 30%;
    }
  }

  #fancybox-overlay {
    z-index: 100000;
  }
  .absolute-footer {
    padding: 10px 0 60px;
  }

  .ui-dialog {
    z-index: 999999;
  }
}

@media screen and (max-height: 660px) {
  .booking_steps .content_wrapper {
    display: none;
  }
}

@media screen and (max-height: 950px) {
  #widget_paraty:not(.is_mobile) {
    #full_wrapper_booking {
      #full-booking-engine-html-7 {
        .calendar_root_wrapper {
          max-height: 70vh;
          overflow: scroll;
          .calendar_app {
            .price_calendar_wrapper {
              &:not(.is_mobile) {
                .full_container {
                  .calendar_wrapper {
                    .month_selector_wrapper {
                      .selector_label {
                        font-size: 20px;
                      }
                    }

                    .month_full_wrapper {
                      .month-container {
                        padding-bottom: 10px;
                      }

                      table.month_wrapper {
                        thead {
                          tr {
                            td {
                              font-size: 18px;
                            }
                          }
                        }

                        tbody {
                          tr.week-row {
                            height: 50px;
                          }
                        }
                      }
                    }
                  }

                  .bottom_wrapper {
                    margin-top: 0;
                    .top {
                      .info_currency_wrapper {
                        .left_wrapper {
                          .legends_wrapper, .notice_info {
                            font-size: 13px;
                          }
                        }
                        .right_wrapper{
                          .toggle_chart {
                            font-size: 15px;
                          }
                        }
                      }
                      .selectors_wrapper {
                        margin-bottom: 15px;

                        .filters_selector_wrapper {
                          .selector_full_wrapper {
                            .selector_wrapper {
                              font-size: 14px;
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}