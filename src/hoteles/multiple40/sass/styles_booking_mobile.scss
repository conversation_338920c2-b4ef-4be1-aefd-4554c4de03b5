@import "booking_mobile/v1/mixin_colores";
@import "booking_mobile/booking";

$loader-size: 60px;
$loader-width: 250px;
$loader-color: linear-gradient(45deg, #5C1669 0%, #9A1F6E 100%);

@keyframes loading {
  0% {
    transform: scale(.1);
  }

  45% {
    left: ($loader-width - $loader-size) / 2;
    transform: scale(1);
  }

  90% {
    left: $loader-width - $loader-size * 0.1;
    transform: scale(.1);
  }
}


@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap');
body.booking_process_mobile_v1.alanda-marbella {
  $corporate_1: #1bacb5;
  $corporate_2: #76c9e1;
  $corporate_3: darken($corporate_1, 10%);
  $corporate_4: #1e2d33;

  $red: #EC6363;
  $green: #2CA96E;
  $grey-1: #333333;
  $grey-2: #444444;
  $grey-3: #F5F5F5;
  $grey-4: #F4F4F4;
  $black: $corporate_1;

  $title_family: 'Cormorant Garamond', serif;
  $text_family: 'Montserrat', sans-serif;

  font-family: $text_family;
  color: $black;

  input, button, select {
    font-family: $text_family;
  }

  .color1 {
    color: $corporate_1;
  }

  .bgc1 {
    background-color: $corporate_1;
  }

  @include set_color_process($corporate_2, $corporate_1, $corporate_3);

  .continue_booking,
  .back_booking {
    background-color: $corporate_1;
  }

  .continue_booking,
  .back_booking,
  .btn.add_service {
    border-radius: 0 !important;
  }

  .main_content_wrapper.wizard {
    .booking_steps_wrapper {
      .booking_step_sentence {
        font-weight: 300;
        letter-spacing: 1px;
        color: $black;
      }
    }

    .search-resume-wrapper {
      .dates,
      .search_text {
        font-size: 14px;
        letter-spacing: 1px;
        line-height: 18px;
        color: $black;
      }
    }
  }

  .double_button_wrapper {
    button {
      margin: 5px;
      border-color: $black;
      color: $black;
      font-weight: 700;
      font-size: 11px;
      letter-spacing: 0.75px;

      &.btn_secondary {
        color: white;
      }
    }
  }

  .main_content_wrapper {
    &.step_0 {
      .tabs_wrapper {
        .tabs {
          li {
            border-radius: 0;

            .tab_btn {
              background-color: $corporate_1;
              border-radius: 0;
              font-size: 12px;

              &.active {
                background-color: white;
                color: $black;
              }
            }

            .tab_btn.packages {
              .tab_text {
                font-weight: 700;
                text-transform: uppercase;
              }
            }
          }

          li.active {

          }

          li + li {
            &::before {

            }
          }

          &:not(.triple) {
            li {
              width: 45%;
            }
          }
        }
      }

      .room_list {
        .room_pack_option {
          .room_name.content_title {
            .title {
              font-family: $title_family;
              font-weight: 700;
            }

            .info_btn {
              color: $black;

              &::before {
                border-color: $black;
              }
            }
          }

          .rates_details_wrapper {
            .rate_selected_title {
              span {
                font-size: 11px;
                color: $black;
              }
            }

            .regime_item_content {
              .discount_percentage {
                background-color: $red;
              }

              .regime_description {
                .regime_offer_detail {
                  color: $black;
                }
              }

              .prices_options {
                .final_price {
                  color: $black;
                }
              }

              .prices_options {
                .price_through {
                  color: $red;
                }
              }

              .regime_price_wrapper {
                div.submit {
                  span {
                    border-radius: 0;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .shopping_cart_summary.v2.mobile {
    font-family: $text_family;

    .full_body_wrapper .scrollable_content_wrapper .items_wrapper .item:not(:last-child) {
      border-color: $corporate_2;
    }

    .start_wrapper .book_button {
      background-color: $corporate_1;
    }
  }

  &.shopping_cart_process_v2 .booking_details_wrapper {
    .book_type_wrapper {
      background: $corporate_1;
    }

    .rooms_breakdown_wrapper .room_selected_element .room_details_wrapper .rate_name {
      color: $corporate_1;
    }
  }

  .hotels_availability_wrapper .choice_element .hotels_carousel_wrapper .hotels_list .hotel_element .product_details .flexible_days {
    color: $corporate_2;
  }

  .share_links_wrapper {
    .share_links_prev {
      background-color: $corporate_2;
    }

    .share_links_cont .share_link {
      background-color: $corporate_2;
    }
  }

  #main_modal {
    &.active {
      .body_modal {
        &.rooms_features_modal_wrapper {
          .body_modal_content {
            .modal_container {
              &.room_content {
                height: 43%;

                .content_title {
                  .title {
                    font-size: 20px;
                  }
                }

                .icons_room {
                  .room_services {
                    .service_element {
                      i {
                        font-weight: 400;
                      }
                    }
                  }
                }

                .room_description {
                  font-size: 15px !important;
                  font-weight: 400 !important;
                }
              }
            }
          }
        }
      }
    }
  }

  .booking_widget_wrapper {
    .occupancy_popup {
      top: 0;
    }
  }

  .flexible_dates_wrapper.active {
    bottom: 35px;
  }
}


// Copied from Park Royal
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');
body.booking_process_mobile_v1.demo-fitur,body.booking_process_mobile_v1.demo-fitur2 {
  $corporate_1: #E4AC51;
  $corporate_2: #456BA7;
  $corporate_3: #0088CC;
  $corporate_4: #D66E5B;
  $corporate_5: #092A5B;
  $corporate_6: #1B97CA;

  $black: #383838;
  $lightgrey: #dedede;
  $red: #DB5A42;
  $green: #35CE8D;
  $grey-1: #D8D8D8;
  $grey-2: #141414;
  $grey-3: #707070;


  $font_1: 'Open Sans', sans-serif;
  $font_2: 'Open Sans', sans-serif;

  $title_family: $font_1;
  $text_family: $font_2;

  font-family: $text-family;

  font-weight: 400;

  @import "mobile/demo-fitur-booking/demo-fitur-booking";
  @import "common/booking1_react";
  @import "mobile/demo-fitur-booking/booking_flight";
}

body.puerta-de-javalambre.booking_process_mobile_v1 {
  $corporate_1: #7C0B0C;
  $corporate_2: #1C1C1C;
  $corporate_3: rgba(#1C1C1C, 0.3);
  $corporate_4: #0BCB89;
  $corporate_5: #E3F5F5;
  $corporate_6: rgba(#DEC2A6, 0.5);

  $red: #EC6363;
  $green: #2CA96E;
  $grey-1: #333333;
  $grey-2: #444444;
  $grey-3: #F5F5F5;
  $black: $grey-2;

  $title_family: 'Poppins', sans-serif;
  $text_family: 'Nunito', sans-serif;

  font-family: $text_family;
  color: $grey-1;

  input, button, select {
    font-family: $title_family;
  }

  .color1 {
    color: $corporate_1;
  }

  .bgc1 {
    background-color: $corporate_1;
  }

  @include set_color_process($corporate_2, $corporate_1, $corporate_3);

  .main_content_wrapper.wizard {
    .booking_steps_wrapper {
      .booking_step_sentence {
        font-weight: 300;
        letter-spacing: 1px;
        color: $grey-2;
      }
    }

    .search-resume-wrapper {
      .dates,
      .search_text {
        font-size: 14px;
        letter-spacing: 1px;
        line-height: 18px;
        color: $grey-2;
      }
    }
  }

  .double_button_wrapper {
    button {
      margin: 5px;
      border-color: $grey-2;
      color: $grey-2;
      font-weight: 700;
      font-size: 11px;
      letter-spacing: 0.75px;

      &.btn_primary,
      &.btn_secondary {
        color: white;
      }
    }
  }

  .back_booking {
    background-color: $corporate_1;
    color: white;
  }

  .main_content_wrapper {
    &.step_0 {
      .tabs_wrapper {
        .tabs {
          li {
            background-color: transparent;
            border: 1px solid $grey-3;
            border-bottom-color: $corporate_2;
            border-radius: 15px 15px 0 0;
            margin: 0 6px 5px;
            border: none;

            .tab_btn {
              height: 35px;
              background-color: transparent;
              font-weight: 300;
              font-size: 15px;
              letter-spacing: 0.75px;
              color: $grey-2;
              border: none;
              font-weight: 500;
              border-radius: 15px 15px 0 0;

              &::before {
                display: none;
              }

              &.rooms, &.packages {
                &::before {
                  background-image: url(https://storage.googleapis.com/cdn.paraty.es/abrigall-masella/files/pest-left_blue.svg);
                  background-repeat: no-repeat;
                  background-size: contain;
                  content: "";
                  display: block;
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  width: 95vw;
                  height: 40px;
                  opacity: 0;
                  pointer-events: none;
                }

                &.active {
                  &:before {
                    opacity: 1;
                  }
                }
              }

              &.packages {
                &::before {
                  background-image: url(https://storage.googleapis.com/cdn.paraty.es/abrigall-masella/files/pest-right_blue.svg);
                  transform: translateX(-51%);
                }

                .tab_text {
                  &:before {
                    content: "";
                    width: 11px;
                    height: 11px;
                    background: $corporate_1;
                    border-radius: 50%;
                    opacity: 0.75;
                    display: inline-block;
                  }
                }
              }

              .tab_text {
                display: block;
              }
            }
          }

          li.active {
            background-color: white;
            border-color: $corporate_2;

            .tab_btn {
              background-color: transparent;
              color: $corporate_1;

              .tab_text {
                &:before {
                  display: none;
                }
              }
            }
          }

          li + li {
            &::before {
              bottom: auto;
              top: 100%;
              display: none;
              background-color: $corporate_2;
            }
          }
        }

        .tabs:not(.triple) {
          li, li.active {
            width: 48%;
          }
        }

        #content {
          .packages_content {
            .package_element.slide:not(.active) {
              form .product_details .product_title_wrapper {
                font-size: 14px;
              }
            }

            .package_element.slide.active {
              form {
                .product_picture {
                  border-top-left-radius: 15px;
                  border-top-right-radius: 15px;
                }

                .product_details {
                  .product_title_wrapper {
                    font-weight: 700;
                    font-size: 14px;
                  }

                  .product_description {
                    font-weight: 400;
                    font-size: 14px;
                    display: block;
                  }

                  .select_options_wrapper {
                    .package_supplements_wrapper {
                      background: white;
                    }

                    select {
                      height: 37px;
                      vertical-align: middle;
                      border-radius: 5px;
                      font-weight: 400;
                      font-size: 14px;
                      color: $grey-2;
                    }

                    .picture_selector {
                      width: calc(20% - 5px);
                      height: 37px;
                      vertical-align: middle;
                      border-radius: 5px;
                      margin: 0;

                      i {
                        font-size: 24px;
                      }
                    }
                  }

                  button {
                    padding: 10px 40px;
                  }
                }
              }
            }
          }
        }
      }

      .room_list {
        .room_pack_option {
          .room_name.content_title {
            .info_btn {
              color: $grey-2;

              &::before {
                border-color: $grey-2;
              }
            }
          }

          .rates_details_wrapper {
            .rate_selected_title {
              span {
                font-size: 11px;
                color: $grey-2;
              }
            }

            .regime_item_content {
              .discount_percentage {
                background-color: $corporate_4;
              }

              .regime_description {
                .regime_offer_detail {
                  color: $grey-2;
                }
              }

              .prices_options {
                .final_price {
                  color: $grey-2;
                }
              }

              .prices_options {
                .price_through {
                  color: $corporate_4;
                }
              }
            }
          }
        }
      }
    }
  }

  .shopping_cart_summary.v2.mobile {
    font-family: $text_family;

    .full_body_wrapper .scrollable_content_wrapper .items_wrapper .item:not(:last-child) {
      border-color: $corporate_2;
    }

    .start_wrapper .book_button {
      background-color: $corporate_1;
    }
  }

  &.shopping_cart_process_v2 .booking_details_wrapper {
    .book_type_wrapper {
      background: $corporate_1;
    }

    .rooms_breakdown_wrapper .room_selected_element .room_details_wrapper .rate_name {
      color: $corporate_1;
    }
  }

  .hotels_availability_wrapper .choice_element .hotels_carousel_wrapper .hotels_list .hotel_element .product_details .flexible_days {
    color: $corporate_2;
  }

  .share_links_wrapper {
    .share_links_prev {
      background-color: $corporate_2;
    }

    .share_links_cont .share_link {
      background-color: $corporate_2;
    }
  }

  #main_modal {
    &.active {
      .body_modal {
        &.rooms_features_modal_wrapper {
          .body_modal_content {
            .modal_container {
              &.room_content {
                .content_title {
                  .title {
                    font-size: 20px;
                  }
                }

                .icons_room {
                  .room_services {
                    .service_element {
                      i {
                        font-weight: 400;
                      }
                    }
                  }
                }

                .room_description {
                  font-size: 15px !important;
                  font-weight: 400 !important;
                }
              }
            }
          }
        }
      }
    }
  }

  .flexible_dates_wrapper.active #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper {
    margin-bottom: 80px;
  }

  #main_modal.active .body_modal {
    max-height: 100vh;
  }
}

@font-face {
  font-family: 'Brandon Grotesque';
  src: url("https://www3.paratytech.com/wysiwyg/fonts/Brandon_Grotesque/BrandonGrotesque-Light.ttf");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Brandon Grotesque';
  src: url("https://www3.paratytech.com/wysiwyg/fonts/Brandon_Grotesque/BrandonGrotesque-Medium.ttf");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Brandon Grotesque';
  src: url("https://www3.paratytech.com/wysiwyg/fonts/Brandon_Grotesque/BrandonGrotesque-Regular.ttf");
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Brandon Grotesque';
  src: url("https://www3.paratytech.com/wysiwyg/fonts/Brandon_Grotesque/BrandonGrotesque-Bold.ttf");
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Brandon Grotesque';
  src: url("https://www3.paratytech.com/wysiwyg/fonts/Brandon_Grotesque/BrandonGrotesque-Black.ttf");
  font-weight: 900;
  font-style: normal;
}


body.booking_process_mobile_v1.maria-del-mar {
  $corporate_1: #333333;
  $corporate_2: #921725;
  $corporate_3: $corporate_2;
  $corporate_4: $corporate_2;
  $corporate_5: $corporate_2;
  $corporate_6: $corporate_2;

  $black: #383838;
  $lightgrey: #dedede;
  $red: #DB5A42;
  $green: #35CE8D;
  $grey-1: #D8D8D8;
  $grey-2: #141414;
  $grey-3: #707070;


  $font_1: 'Cabin', sans-serif;
  $font_2: 'Cabin', sans-serif;

  $title_family: $font_1;
  $text_family: $font_2;

  font-family: $text-family;

  @import "mobile/maria-del-mar-booking/maria-del-mar-booking";
}

@import url('https://fonts.googleapis.com/css2?family=Spartan:wght@100;300;400;500;700&display=swap');
body.playamaro {
  @font-face {
    font-family: 'georgia';
    src: url("/static_inj/fonts/georgia/v2/GEORGIA.ttf");
    font-weight: normal;
    font-style: normal;
  }

  @font-face {
    font-family: 'georgia';
    src: url("/static_inj/fonts/georgia/v2/GEORGIAB.ttf");
    font-weight: bold;
    font-style: normal;
  }
  @font-face {
    font-family: 'georgia';
    src: url("/static_inj/fonts/georgia/v2/GEORGIAI.ttf");
    font-weight: normal;
    font-style: italic;
  }
  @font-face {
    font-family: 'georgia';
    src: url("/static_inj/fonts/georgia/v2/GEORGIAZ.ttf");
    font-weight: bold;
    font-style: italic;
  }

  $corporate_1: #206372;
  $corporate_2: #F2A400;
  $corporate_3: #FAFAF7;

  $red: #E75354;
  $black: #222222;
  $grey: #696969;
  $grey-1: #F1EDE4;
  $green: #F2A400; //its orange now :-O
  $offer: $corporate_1;

  $title_family: 'Spartan', sans-serif;
  $text_family: $title_family;
  $extra_family: "georgia", sans-serif;

  font-family: $text_family;
  color: $black;

  @import "mobile/playamaro-booking/playamaro-booking";
}

body.hotel-ninays {
  $corporate_1: #003580;
  $corporate_2: #003580;
  $corporate_3: #66b8d9;
  $black: #454e59;
  $red: #EC6363;
  $font_1: 'Open Sans', sans-serif;

  font-family: $font_1;
  color: $black;

  input, button, select, textarea {
    font-family: $font_1;
  }

  @include set_color_process($corporate_1, $corporate_2, $corporate_3);
  @import "mobile/ninays/ninays_booking_process_mobile";

  .loading_animation_popup{
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  .loading_animation_popup .spincircle .spinner_animation *{
    background: $black!important;
  }
}

// Copied from Demo Fitur
@font-face {
  font-family: 'Nigelina';
  src: url('/static_1/fonts/Nigelina/Nigelina-Regular.eot');
  src: url('/static_1/fonts/Nigelina/Nigelina-Regular.eot?#iefix') format('embedded-opentype'),
  url('/static_1/fonts/Nigelina/Nigelina-Regular.woff2') format('woff2'),
  url('/static_1/fonts/Nigelina/Nigelina-Regular.woff') format('woff'),
  url('/static_1/fonts/Nigelina/Nigelina-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@import url('https://fonts.googleapis.com/css2?family=Hanken+Grotesk:wght@300;400;700&display=swap');
body.booking_process_mobile_v1.coronado {
  $corporate_1: #B19261;
  $corporate_2: #93774A;
  $corporate_3: #F6F1EA;

  $black: #212121;
  $lightgrey: #F5F5F5;
  $red: #EC6363;

  $font_1: 'Nigelina', sans-serif;
  $font_2: 'Hanken Grotesk', sans-serif;
  $fa6: "Font Awesome 6 Pro";

  $title_family: $font_1;
  $text_family: $font_2;

  @import "mobile/coronado-booking/coronado-booking";

  #login_wrapper_element.version_banner_v1.v4 {
    .content_login_wrapper {
      background: transparent !important;

      .logo_wrapper, .club_icons_wrapper {
        width: 45%;

        img {
          max-height: 40px;
        }
      }
    }

    .users_buttons_wrapper {
      padding: 10px 10px 10px 10px;

      .already_member_wrapper {
        background: transparent;
      }

      .join_button_wrapper {
        color: white;
      }
    }
  }

  .modal_wrapper.login_information_wrapper {
    z-index: 49;
  }

  .main_content_wrapper.step_2 {
    .personal_details_form_wrapper {
      .personal_details_form {
        .inputs_wrapper {
          input {
            border: 1px solid rgb(131, 131, 133);
          }
        }
      }
    }
  }
}

@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap');
body.booking_process_mobile_v1.regency-salgados {
  $corporate_1: #00445E;
  $corporate_2: #C9BFB1;
  $corporate_3: rgba(#C9BFB1, 0.3);
  $corporate_4: #00445E;

  $red: #EC6363;
  $green: #2CA96E;
  $grey-1: #333333;
  $grey-2: #444444;
  $grey-3: #F5F5F5;
  $grey-4: #F4F4F4;
  $black: $corporate_1;

  $title_family: 'Cormorant Garamond', serif;
  $text_family: 'Montserrat', sans-serif;

  font-family: $text_family;
  color: $black;

  input, button, select {
    font-family: $text_family;
  }

  .color1 {
    color: $corporate_1;
  }

  .bgc1 {
    background-color: $corporate_1;
  }

  @include set_color_process($corporate_2, $corporate_1, $corporate_3);

  @import "mobile/regency-salgados/salgados_process";

  #main_modal.active .body_modal.iframe_modal_wrapper .body_modal_content iframe {
    height: 70vh;
  }
}

body.lago-montargil.booking_process_mobile_v1 {
  $corporate_1: #506D85;
  $red: #ec6060;
  $black: #1F2A44;

  $title_family: "Heebo", Sans-serif;
  $text_family: "Open Sans", Sans-serif;

  @import "booking_mobile/custom_booking";
  @import "mobile/montargil-booking/_mobiles_styles.scss"
}

@import url('https://fonts.googleapis.com/css2?family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&family=Open+Sans:wght@300;400;600;700&display=swap');
body.booking_process_mobile_v1.peso-village {
  $corporate_1: #542b0b;
  $corporate_2: #444444;
  $corporate_3: #c68439;
  $corporate_4: $corporate_1;
  $lightblue: #6CB9FF;
  $black: #3D3D3C;
  $grey: #F4F4F4;
  $grey_2: #989898;
  $green: #01B951;
  $red: #EC6363;
  $title_family: 'Open Sans', sans-serif;
  $text_family: 'Open Sans', sans-serif;
  $font_1: 'Open Sans', sans-serif;
  $font_2: 'Open Sans', sans-serif;

  $title_rooms: 'Libre Baskerville', serif;

  font-family: $text_family;

  b, strong {
    font-weight: bold;
  }

  @import "mobile/peso-village-booking/peso_village_booking_process_mobile";

  header .menu.side_menu_wrapper .logo-wrapper {
    background-image: url(https://storage.googleapis.com/cdn.paraty.es/peso-village/files/ff790cb9c4f13de%3Ds300.png);
    background-repeat: no-repeat;
    background-size: contain;
    width: 132px;
    height: 60px;

    img {
      display: none;
    }
  }

  footer {
    .row {
      .logo_wrapper {
        background-image: url(https://storage.googleapis.com/cdn.paraty.es/peso-village/files/ff790cb9c4f13de%3Ds300.png);
        background-repeat: no-repeat;
        background-size: contain;
        width: 160px;
        height: 60px;

        img {
          display: none;
        }
      }
    }
  }
}

html[lang="en"] {
  body.coronado.booking_process_mobile_v1 {
    .room_option_block .room_rates_list .rates_details_wrapper .regime_item_content .regime_price_wrapper .lock_board_wrapper .lock_ico::after {
      content: 'Unlock';
    }
  }
}

html[lang="de"] {
  body.coronado.booking_process_mobile_v1 {
    .room_option_block .room_rates_list .rates_details_wrapper .regime_item_content .regime_price_wrapper .lock_board_wrapper .lock_ico::after {
      content: 'Aufschließen';
    }
  }
}