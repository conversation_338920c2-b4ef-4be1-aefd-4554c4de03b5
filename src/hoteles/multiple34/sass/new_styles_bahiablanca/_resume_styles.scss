&.pt {
  #step-2 .booking-box--search .booking-box__content .booking-search-result__pets-number {
    right: 270px !important;
    background-color: transparent !important;
    width: 132px !important;
  }

  #step-2 .hidden_booking_summary.showed .booking-search-result__pets-number {
    right: 270px !important;
    background-color: transparent !important;
    width: 132px !important;
  }
}

div#step-1 .booking-box--search .booking-box__content,
div#step-2 .booking-box--search .booking-box__content {
  border: none;
  padding: 12px 0 13px 20px !important;
  position: relative;
  background: #F7F5F5;
  border-radius: 16px;

  &:lang(de) {
    padding: 0 !important;
    @media only screen and (max-width: 1140px) {
      padding: unset !important;
    }
  }

  .title_booking_breakdown {
    position: relative;
    color: #222222;
    height: auto;
    max-width: 145px;
    vertical-align: middle;
    font-family: $title_family;
    text-align: left;
    line-height: 32px;
    font-size: 24px;
    font-weight: 400;
    letter-spacing: 0.6px;
    padding: 17px 60px 0 10px;
    vertical-align: top;
    word-break: break-word;
    @extend .fa-key;

    &:before {
      @extend .fal;
      content: "";
      width: 30px;
      height: 30px;
      position: absolute;
      background-image: url(https://storage.googleapis.com/cdn.paraty.es/test5-copia8/files/fi-rr-calendar.svg);
      background-size: cover;
      background-repeat: no-repeat;
      font-size: 30px;
      top: 35px;
      right: 25px;
      -webkit-transform: translateY(-50%);
      -moz-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      -o-transform: translateY(-50%);
      transform: translateY(-50%);
      color: rgba($corporate_1, 1);
    }

    &:after {
      content: '';
      position: absolute;
      top: 10px;
      bottom: 10px;
      right: 0;
      width: 1px;
      background: $grey;
    }
  }

  .title_booking_breakdown:lang(de) {
    &:before {
      right: 6px;
    }
  }

  .title_booking_breakdown:lang(fr), .title_booking_breakdown:lang(sv) {
    padding: 5px 60px 0 10px;
  }

  .booking-search-results__search-data {
    color: $black;
    padding-left: 25px;
    font-size: 14px;
    padding-right: 70px;
    white-space: nowrap;
    max-width: 90px;
    padding-bottom: 15px;
    vertical-align: top;
    padding-top: 17px;

    .booking-title-info.booking-hotel-name {
      font-size: 15px;
      font-family: $title_family;
      font-weight: 400;
      letter-spacing: 1px;
      display: none;

      + .booking-3-info,
      + .booking-3-info + br {
        display: none;
      }
    }

    .booking-3-info {
      color: $corporate_2 !important;
      font-family: $title_family;
      font-size: 18px;
      display: inline-block;
      font-weight: 500 !important;
      padding: 0 3px;
      text-transform: none;
    }

    .booking-title-info {
      font-size: 12px;
      font-family: $text_family;
      letter-spacing: 0.6px;
      line-height: 24px;
      color: $black !important;
    }

    .notranslate {
      font-size: 16px;
      font-weight: bold;
      font-family: $text_family;
      letter-spacing: 0.8px;
      line-height: 24px;
      color: $black !important;
    }

    .booking-title-info, i {
      &.fa-long-arrow-left,
      &.fa-long-arrow-right {
        margin-left: 0;
        margin-right: 10px;
        font-size: 22px;
        font-weight: 300;
        @extend .icon-longarrow;

        &:before {
          display: inline-block;
          font-family: "Font Awesome 6 Pro", sans-serif;
          color: rgba($corporate_7, 1);
        }
      }

      &.fa-long-arrow-right {
        &::before {
          content: "\f178";
          color:$corporate_7;
        }
      }

      &.fa-long-arrow-left {
        &:before {
          content: "\f177";
          color:$corporate_7;
        }
      }
    }

    b {
      font-weight: 400;
    }

    .notranslate {
      > span {
        display: inline-block;
        vertical-align: middle;
        padding: 0 2px;
      }
    }

    &:lang(sv), &:lang(fr) {
      padding-top: 25px !important;
    }
  }

  .booking-search-results__rooms-list {
    display: inline-block;
    vertical-align: top;
    margin-left: 0;
    color: black;
    max-width: 280px;
    font-size: 14px;
    white-space: nowrap;
    padding-top: 15px !important;

    .booking-title-info {
      font-size: 12px;
      font-family: $text_family;
      letter-spacing: 0.6px;
      line-height: 24px;
      color: $black !important;

      &:before {
        content: "\e9e4";
        display: inline-block;
        vertical-align: middle;
        margin-right: 5px;
        font-family: "icomoon", sans-serif;
        font-size: 14px;
        color: rgba($corporate_7, 1);
      }
    }

    .rooms_amount_number {
      font-size: 16px;
      font-weight: bold;
      font-family: $text_family;
      letter-spacing: 0.8px;
      line-height: 24px;
      color: $black !important;
    }

    .search-item {
      font-size: 16px;
      font-weight: bold;
      font-family: $text_family;
      letter-spacing: 0;
      line-height: 22px;
      color: $black !important;

      .booking-title-info {
        font-size: 12px;
        font-family: $text_family;
        letter-spacing: 0.6px;
        line-height: 24px;
        color: $black !important;
        font-weight: 400;

        &:before {
          content: "\e9f8";
          color:$corporate_7;
        }
      }
    }

    i {
      display: none;
    }

    b {
      font-weight: 400;
    }

    &:lang(sv), &:lang(fr) {
      padding-top: 24px !important;
    }

    &:lang(fi), &:lang(en) {
      padding-left: 20px;
    }
  }

  .booking-search-results__new-search {
    .booking-button {
      font-family: $title_family;
      position: relative;
      background-color: transparent !important;
      border: 2px solid $corporate-2;
      color: $corporate-2;
      text-transform: uppercase;
      max-width: 250px;
      width: 250px;
      border-radius: 10px;
      text-align: center;
      height: 81px;
      margin: 10px 10px 0 0;
      padding: 20px 10px 20px 60px !important;
      letter-spacing: 0.8px;
      font-size: 16px;
      line-height: 21px;
      font-weight: bold;
      -webkit-transition: all 0.6s;
      -moz-transition: all 0.6s;
      -ms-transition: all 0.6s;
      -o-transition: all 0.6s;
      transition: all 0.6s;
      z-index: 1;
      @extend .icon-specialcalendar;

      &:before {
        content: "";
        width: 30px;
        height: 30px;
        background-image: url(https://storage.googleapis.com/cdn.paraty.es/test5-copia8/files/fi-rr-calendar.svg);
        background-size: cover;
        background-repeat: no-repeat;
        position: absolute;
        top: 50%;
        left: 15px;
        -webkit-transform: translate(0%, -50%);
        -moz-transform: translate(0%, -50%);
        -ms-transform: translate(0%, -50%);
        -o-transform: translate(0%, -50%);
        transform: translate(0%, -50%);
        transition: all 0.6s;
        z-index: 1;
      }

      &:hover {
        background: $corporate-2 !important;
        color: white;

        &::before {
          color: white;
          background-image: url(https://storage.googleapis.com/cdn.paraty.es/test5-copia8/files/fi-rr-calendar%20white.svg);
        }
      }

      &:lang(en) {
        padding: 20px 20px 20px 90px !important;
      }

      &:lang(fr) {
        padding: 20px 0px 20px 55px !important;
        font-size: 15px;

        &:before {
          left: 18px;
        }
      }
    }
  }
}

.booking-box--search.has_currency_selector #currencyDiv {
  max-width: 199px;
  width: 199px;
}

.booking-box--search .booking-box__content, .hidden_booking_summary {
  .booking-search-result__pets-number {
    background-color: transparent;
    text-align: center;
    position: absolute;
    right: 240px;
    top: 0;
    bottom: 0;
    display: inline-block;
    vertical-align: middle;
    width: 120px;

    .center_block {
      position: absolute;
      top: 50%;
      left: 50%;
      -webkit-transform: translate(-50%, -50%);
      -moz-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
      -o-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
      display: table;
    }

    .fa {
      color: rgba($corporate_2, .3);
      font-size: 32px;
      font-family: "Font Awesome 6 Pro";
      font-weight: 300;

      &:before {
        content: "\f6d3";
      }
    }

    & + .booking-search-results__new-search {
      #modify-button {
        max-width: 240px;
      }
    }
  }
}

.hidden_booking_summary {
  border: none;
  box-shadow: 0 0 20px rgba(0, 0, 0, .3);
  padding: 0;

  .title_booking_breakdown {
    position: relative;
    color: #222222;
    height: 95px;
    max-width: 185px;
    vertical-align: middle;
    font-family: $title_family;
    text-align: left;
    line-height: 32px;
    font-size: 24px;
    font-weight: 400;
    letter-spacing: 0.6px;
    padding: 20px 60px 0 10px;
    vertical-align: top;
    word-break: break-word;

    @extend .fa-key;

    &:before {
      @extend .fal;
      content: "";
      width: 30px;
      height: 30px;
      position: absolute;
      background-image: url(https://storage.googleapis.com/cdn.paraty.es/test5-copia8/files/fi-rr-calendar.svg);
      background-size: cover;
      background-repeat: no-repeat;
      font-size: 30px;
      top: 38px;
      right: 25px;
      -webkit-transform: translateY(-50%);
      -moz-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      -o-transform: translateY(-50%);
      transform: translateY(-50%);
      color: rgba($corporate_7, 1);
    }

    &:after {
      content: '';
      position: absolute;
      top: 10px;
      bottom: 10px;
      right: 0;
      width: 1px;
      background: $grey;
    }
  }

  .booking-search-results__search-data {
    color: $black;
    padding-left: 25px;
    font-size: 14px;
    padding-right: 70px;
    white-space: nowrap;
    max-width: 90px;
    vertical-align: top;
    padding-top: 17px;

    .booking-title-info.booking-hotel-name {
      font-size: 15px;
      font-family: $title_family;
      font-weight: 400;
      letter-spacing: 1px;
      display: none;
    }

    .booking-3-info {
      color: $corporate_2 !important;
      font-family: $title_family;
      font-size: 18px;
      display: inline-block;
      font-weight: 500 !important;
      padding: 0 3px;
      text-transform: none;
      display: none;

      + br {
        display: none;
      }
    }

    .booking-title-info {
      font-size: 12px;
      font-family: $text_family;
      letter-spacing: 0.6px;
      line-height: 24px;
      color: $black !important;
    }

    .notranslate {
      font-size: 16px;
      font-weight: bold;
      font-family: $text_family;
      letter-spacing: 0.8px;
      line-height: 24px;
      color: $black !important;
    }

    .booking-title-info, i {
      &.fa-long-arrow-left,
      &.fa-long-arrow-right {
        margin-left: 0;
        margin-right: 10px;
        font-size: 22px;
        font-weight: 300;
        @extend .icon-longarrow;

        &:before {
          display: inline-block;
          font-family: "Font Awesome 6 Pro", sans-serif;
          //color: rgba($corporate_7, 1);
          color:red;
        }
      }

      &.fa-long-arrow-right {
        &::before {
          content: "\f178";
        }
      }

      &.fa-long-arrow-left {
        &:before {
          content: "\f177";
        }
      }
    }

    b {
      font-weight: 400;
    }

    .notranslate {
      > span {
        display: inline-block;
        vertical-align: middle;
        padding: 0 2px;
      }
    }
  }

  .booking-search-results__rooms-list {
    color: $black;
    max-width: 280px;
    font-size: 14px;
    white-space: nowrap;
    padding-top: 17px !important;
    padding-left: 40px !important;
    padding-bottom: 10px;
    vertical-align: top;

    .booking-title-info {
      font-size: 12px;
      font-family: $text_family;
      letter-spacing: 0.6px;
      line-height: 24px;
      color: $black !important;

      &:before {
        content: "\e9e4";
        display: inline-block;
        vertical-align: middle;
        margin-right: 5px;
        font-family: "icomoon", sans-serif;
        font-size: 14px;
        color: rgba($corporate_7, 1);
      }
    }

    .rooms_amount_number {
      font-size: 16px;
      font-weight: bold;
      font-family: $text_family;
      letter-spacing: 0.8px;
      line-height: 24px;
      color: $black !important;
    }

    .search-item {
      font-size: 16px;
      font-weight: bold;
      font-family: $text_family;
      letter-spacing: 0;
      line-height: 24px;
      color: $black !important;

      .booking-title-info {
        font-size: 12px;
        font-family: $text_family;
        letter-spacing: 0.6px;
        line-height: 24px;
        color: $black !important;
        font-weight: 400;

        &:before {
          content: "\e9f8";
        }
      }
    }

    i {
      display: none;
    }

    b {
      font-weight: 400;
    }
  }

  #redirection-message {
    z-index: 2;
  }

  .booking-search-results__new-search {
    .booking-button {
      font-family: $title_family;
      position: relative;
      background-color: transparent !important;
      border: 2px solid $corporate-2;
      color: $corporate-2 !important;
      text-transform: uppercase;
      max-width: 240px;
      width: 240px;
      border-radius: 10px !important;
      text-align: center;
      height: 81px;
      padding: 20px 20px 20px 60px !important;
      letter-spacing: 0.8px;
      font-size: 16px;
      line-height: 21px;
      font-weight: bold;
      -webkit-transition: all 0.6s;
      -moz-transition: all 0.6s;
      -ms-transition: all 0.6s;
      -o-transition: all 0.6s;
      transition: all 0.6s;
      z-index: 1;
      @extend .icon-specialcalendar;

      &:before {
        content: "";
        width: 30px;
        height: 30px;
        background-image: url(https://storage.googleapis.com/cdn.paraty.es/test5-copia8/files/fi-rr-calendar.svg);
        background-size: cover;
        background-repeat: no-repeat;
        position: absolute;
        top: 50%;
        left: 25px;
        -webkit-transform: translate(0%, -50%);
        -moz-transform: translate(0%, -50%);
        -ms-transform: translate(0%, -50%);
        -o-transform: translate(0%, -50%);
        transform: translate(0%, -50%);
        transition: all 0.6s;
        z-index: 1;
      }

      &:hover {
        background: $corporate-2 !important;
        color: white !important;

        &::before {
          color: white;
          background-image: url(https://storage.googleapis.com/cdn.paraty.es/test5-copia8/files/fi-rr-calendar%20white.svg);
        }
      }

      &:lang(en) {
        padding: 20px 20px 20px 90px !important;
      }

      &:lang(fr) {
        padding: 20px 0px 20px 55px !important;

        &:before {
          left: 10px;
        }
      }
    }
  }
}

.booking-search-results .booking-search-results__new-search .total_price_label + .booking-button {
  font-family: $text_family;
  position: relative;
  background: $corporate_2;
  text-transform: uppercase;
  max-width: 280px;
  margin: 0;
  border-radius: 0;
  letter-spacing: 1px;
  font-size: 16px;
  font-weight: bold;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
  z-index: 1;
}

.booking-search-results .booking-search-results__new-search .total_price_label {
  font-family: $text_family;
  letter-spacing: 1px;
  font-weight: 300;
  margin-bottom: 0;
  margin-top: 0;
}

.booking-box--search .booking-box__content {
  border: 0 !important;
}

#step-2 {
  .hidden_booking_summary {
    z-index: 60;

    .total_price_label {
      display: none;
    }

    .booking-search-results__new-search {
      top: 0;
      -webkit-transform: none;
      -moz-transform: none;
      -ms-transform: none;
      -o-transform: none;
      transform: none;
    }
  }

  .booking-search-results.has_web_support {
    width: 85% !important;
    border: 1px solid black !important;
  }

  .booking-button.booking-button--action {
    padding: 20px !important;

    &:before {
      display: none;
    }
  }
}