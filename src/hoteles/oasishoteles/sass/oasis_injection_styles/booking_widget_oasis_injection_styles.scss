@import url('https://fonts.googleapis.com/css2?family=Josefin+Sans:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap');
@import "plugins/mixins";
@import "plugins/fancybox_2_1_5";
@import "plugins/spiners/all_spiners";

@import "booking_widget_defaults";

$primary_font: 'Roboto', sans-serif;
$secondary_font: 'Josefin Sans', sans-serif;
$text_color: #444444;
$separator_color: #222222;
$box_shadow: 0px 3px 15px rgba(#000000, 0.2);
$horizontal_padding: 15px;
$label_color: #a3a3a3;
$height: 70px;
$vertical_padding: 25px;

body {
  $corporate_1: #4284d0;
  $corporate_2: #444444;
  $corporate_3: #0065b3;

  $grey_dark: #364546;
  $option_color: $corporate_1;

  $white: rgb(255, 255, 255);

  $booking_widget_color_1: $white; //body back ground & year input text color
  $booking_widget_color_2: $corporate_1; //header background & input texts
  $booking_widget_color_3: gray; //label texts
  $booking_widget_color_4: gray; //not used, but must be defined

  @import "booking_widget_styles";
  @import "responsive";

  &:not(.cheapest_prices_test) .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.cheapest_price {
    color: #444444;
  }

   &:has(.children_selector.desactivate){
     .ui-dialog{
       display: none;
     }
   }
  &.full_widget_paraty_open,
  &.widget_paraty_open {
    overflow-y: hidden !important;

    #widget_paraty,
    .booking_steps {
      z-index: 1000000;
    }

    #qt-app-container.notranslate {
      display: none !important;
      opacity: 0 !important;
    }
  }

  // If in the widget_config section you have the extra_widget_class property with the widget_fixed_footer class, the widget is positioned fixedly in footer
  &.widget_fixed_footer {
    #widget_paraty {
      background: white;
      bottom: 0;

      #full_wrapper_booking {
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
      }
    }
  }

  // If in the widget_config section you have the extra_widget_class property with the widget_fixed_footer class, the widget is positioned fixedly in header

  @media (min-width: 1141px) {
    &.widget_fixed_header {
      @import "scrolled_fixed";
    }
    &.version_white {
        @import "scrolled_fixed_version_white";
    }
  }
}

#fancybox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 8000;
}

.fancybox-wrap.fancy-booking-search_v2 {
  width: 100% !important;
  height: 100% !important;
  top: 0 !important;
  left: 0 !important;
  background: white;
  z-index: 1000001;

  .container_popup_booking {
    @include center_xy;
    position: fixed;
    top: 45%;
    width: 555px;
    display: block;
    padding: 30px 0;
    box-sizing: border-box;
    margin: 7px;
    border: 1px solid #3483b2;

    .description_top_popup_booking {
      width: 100%;
      height: 250px;
      margin-bottom: 20px;

      video, img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .description_bottom_popup_booking {
      font-family: $primary_font;
      font-size: 15px;
      letter-spacing: 0.9px;
      line-height: 19px;
      text-transform: none;
      color: #1C1C1C;
    }

    .gif_wrapper {
      display: none;
    }
  }

  @media screen and (max-width: 400px) {
    .fancybox-wrap.fancy-booking-search_v2 {
      .container_popup_booking {
        width: 100vw;
        height: 100vh;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        transform: none;
        margin: 0;
        padding-top: 40%;
      }
    }
  }
}

