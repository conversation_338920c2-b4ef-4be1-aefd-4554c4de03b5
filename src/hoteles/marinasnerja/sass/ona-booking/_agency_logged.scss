#step-3 .booking-step-title.logged_agency {
  padding: 20px 40px !important;
  margin-bottom: 15px;
}
.booking-step-title.logged_agency {
    background: #002D42;
    color:white;
    border-radius: 20px;
    margin-top: 15px;
    padding: 20px 40px !important;
    box-sizing: border-box;
    .agency_logo_header {
        display: inline-block;
        vertical-align: middle;
        width: 200px;
        img {
            max-height: 40px;
        }
    }
    h2 {
        display: inline-block;
        vertical-align: middle;
        text-transform: none;
        width: calc(100% - 405px);
        box-sizing: border-box;
        padding-left: 40px;
        .modification_header {
            color:white;
        }
        small {
            font-size: 80%;
            font-weight: lighter;
            padding-left: 5px;
        }
    }
    .agency_logout_btn {
        text-transform: uppercase;
        display: inline-block;
        vertical-align: middle;
        letter-spacing: 2px;
        color: white;
        text-align: right;
        width: 200px;
        font-size: 12px;
        font-weight: 300;
        text-transform: uppercase;
        &:hover {
        text-decoration: undeline;
        }
        &:before {
            content: '';
            background-image: url("/img/onacp/logout.png");
            background-repeat: no-repeat;
            background-position: center 3px;
            background-size: 18px;
            display: inline-block;
            vertical-align: middle;
            width: 25px;
            height: 25px;
            margin-right: 5px;
        }
    }
}
div#step-1 div.contTipoHabitacion .precioGeneralDiv i.logged_agency_img_info {
    float:left;
    position: absolute;
    left:0;
    top: 50%;
    transform: translateY(-50%);
    margin-left: -50px !important;
    width: 100px;
    box-sizing: border-box;
    padding: 10px;
    height: 40px;
    background: #002D42;
    border-radius: 10px;
    &:before {
        content: '\f05a';
        position: absolute;
        top: 5px;
        right: 5px;
        left: auto;
        margin: auto 0 auto auto;
    }
    &:after {
        display: block;
        content: '';
        background-image: url('https://cdn2.paraty.es/ona-corporativa/images/5e6e888aac64eae=s100');
        background-repeat: no-repeat;
        background-position: center center;
        background-size: contain;
        width: 100%;
        height: 100%;
    }
}
table.listadoHabsTarifas.agencies_view_actived .logged_agency_wrapper_info {
    background-color: rgba(#002D42, .95);
    color:white;
    border-radius: 10px;
    line-height: 20px;
    span {
        line-height: 20px;
        color: white;
    }
    .total_field {
        font-size: 14px;
        font-weight: lighter;
        .value_elem, .monedaConv {
            color: #11CCC7;
            font-weight: normal;
        }
    }
}

&.logged_agency {


  div#step-1 div.contTipoHabitacion {
    .precioGeneralDiv i.logged_agency_img_info {
      left: -195px;
    }

    .preciosHabitacion table.listadoHabsTarifas.agencies_view_actived {
      tr .lock_board .lock_board_wrapper {
        display: none;
      }

      .logged_agency_wrapper_info {
        width: 280px;
        left: -130px;
        z-index: 10;
      }

      td.precioTotalColumn .priceValues .precioGeneralDiv {
        width: 120px;
      }
    }
  }

  .matcher-main-wrapper.rate-check-v5 {
    display: none !important;
  }

  .booking-step-title.logged_agency {
    display: flex;
    align-items: center;

    h2 {
      display: inline-block;
      width: inherit;
    }

    .agency_buttons_wrapper {
      width: inherit;
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;

      a {
        display: flex;
        align-items: center;
      }

      .agency_home_btn {
        &::before {
          content: '';
          background-image: url("/img/onacp/logout.png");
          background-repeat: no-repeat;
          background-position: center 3px;
          background-size: 18px;
          display: inline-block;
          vertical-align: middle;
          width: 25px;
          height: 25px;
          margin-right: 5px;
        }

      }

      .agency_logout_btn {
        width: auto;
        margin-left: 20px;

        &::before {
          content: '\f011';
          display: inline-block;
          font-family: 'Font Awesome 6 Pro';
          vertical-align: middle;
          font-size: 17px;
          margin-right: 5px;
          background-image: unset !important;
          width: auto;
          height: auto;
        }
      }
    }
  }
}
