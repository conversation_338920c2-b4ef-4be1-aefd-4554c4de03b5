$color1: #2c50ae;
$color2: #ededed;

@import "_styles_mobile_base_2.scss";
@import "_styles_mobile_base_2_personalized";

/*==== Sorteo ====*/
.video_wrapper {
  position: relative;
}

h1.sorteo_title {
  padding: 10px 30px;
  background-color: #e2ebf4;
  color: #2c73b5;
  font-size: 3em;
  text-transform: uppercase;
  border-bottom: 2px solid white;
  text-align: center;
  font-weight: bold;
  line-height: 1em;
  margin-bottom: 1em;
}

.sorteo_text {
  font-size: 2em;
  line-height: 1em;
  color: gray;
  margin-bottom: 1em;
}

.overlay_video {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
}

.sort-form form #contact-button-sorteo {
  background: $color1;
  color: white;
  font-size: 2em;
  text-transform: uppercase;
}

//////////////////
////hoteles//////
////////////////

h3.title-module {
  margin-top: 50px;
}

.img-shadow, .hotels-img, .link-corporate2 {
  display: none;
}

#top_content {
  img {
    width: 100%;
  }
}

span.btn-corporate {
  font-family: "Helvetica Neue", Verdana, sans-serif;
  background-color: #f2f2f4;
  margin: 0 auto;
  width: 100%;
  border: none;
  border-radius: 3px;
  font-size: 1.5em;
  font-weight: lighter;
  text-transform: uppercase;
  padding: 13px 0;
  display: block;
  text-align: center;

  a {
    color: $color1;
  }
}

span.destino {
  font-size: 23px;
}

.precio-desde-hotel {
  font-weight: 700;
  color: $color1;
  text-align: center !important;
}

#title-main-section {
  margin-bottom: 10px;
}

////////////////////
/////habitaciones//
///////////////////

.room_home_wrapper {
  margin-bottom: 30px !important;
}

.room_home_title {
  font-size: 1.2em;
  border: none;
  padding: 3% 0;
  background-color: $color1;
  margin: 0 auto;
  color: white !important;
  display: block;
  width: 100%;
  text-align: center;
  text-transform: uppercase;
  font-weight: normal !important;
  font-family: "Helvetica Neue", Verdana, sans-serif !important;
  margin-bottom: 20px;
}

.room-img-lupa, .button-room-more {
  display: none;
}

.room_description {
  table {
    width: 100%!important;

    td:nth-child(odd) {
      display: none;
    }

    td:nth-child(even) {
      display: table;
      width: 100%!important;
      text-align: center;
    }
  }
}

.room_home_image img {
  width: 100%;
}

.room-description-hidden {
  display: none;
}

.prices-room {
  display: none;
}

.button-promotion .room_home_button {
  font-size: 1.2em;
  border: none;
  padding: 3% 0;
  background-color: $color2;
  margin: 0 auto;
  color: $color1 !important;
  display: block;
  width: 100%;
  text-align: center;
  text-transform: uppercase;
  font-weight: normal !important;
  font-family: "Helvetica Neue", Verdana, sans-serif !important;
}

////////////////////
/////ofertas////////
///////////////////

.promotions-item {
  border-bottom: 1px solid $color1;
  padding-bottom: 30px;

  img {
    width: 100%;
  }
}

//////////////////////
//////mis reservas////
//////////////////////

form [data-role="fieldContain"] input {
  margin-top: 11px;
}

form input[type="submit"] {
  color: white;
}

//////////////////
////Galeria//////
////////////////
.visita_or_video_mobile {
  width: 100%;

  iframe {
    width: 100%;
    height: 190px;
  }
}

.crop img {
  width: 100%;
}

///////////////
////Ofertas///
/////////////
.promo_home_image {
  img {
    width: 100%;
  }
}

.promo_home_title {
  font-weight: bold;
  color: $color1;
  margin-bottom: 15px;
}

.promo_home_description {
  .promo_home_button {
    padding: 4px 12px;
    background: #0E4A77;
    color: white;
    border-radius: 4px;
    border: 0px;
    font-size: 20px;
    margin-left: 90px;
  }
}

.promo_home_wrapper {
  border-bottom: 1px solid black;
}

.ui-bar-a {
  background-image: linear-gradient(#FFFFFF, #FFFFFF) !important;
}

.iconos li p, #form label {
  word-wrap: break-word;
}

.info #contact-button {
  padding: 0px !important;
  font-size: 1.1em;
}

.info div:nth-child(6) {
  display: none;
}

.location_destiny {
  display: none;
}

.ui-widget-content {
  display: none;
}

//************ Eventos *************//

.image_title_event {
  margin-bottom: 20px;
}

.event_image_wrapper img {
  width: 100%;
}

.section-content {
  ul {
    padding-left: 20px;
    li {
      list-style: circle;
    }
  }
}

.hotel-image {
  width: 100%;
}

.hidden-pictures {
  display: none;
  margin-top: 2em;
}

span#button2_rooms {
  font-family: "Helvetica Neue", Verdana, sans-serif;
  background-color: $color1;
  margin: 0 auto;
  width: 100%;
  border: none;
  border-radius: 0;
  color: white;
  font-size: 1.5em;
  font-weight: lighter;
  text-transform: uppercase;
  padding: 13px 0;
  display: block;
  text-align: center;
  margin-top: 15px;
}

.hotel {
  border-bottom: 2px solid $color1;
  padding-bottom: 20px;
  margin-top: 20px;

  .hotel-title {
    margin: 1em 0;
    font-size: 20px !important;
    color: $color1;
  }

  .hotel-description {
    font-size: 1.5em;
    line-height: 1.4em;
    font-weight: lighter;
    padding-bottom: 1em;
    text-align: left;
  }
}

/*=== Private Styles ===*/
.private_styles {
  h2 {
    font-family: 'Montserrat', sans-serif;
    font-weight: bolder;
    color: $color1;
    font-size: 2.437em;
    margin: 0.3em 0;
    line-height: 1em;
    padding-top: 0.5em;
    text-align: center;
  }

  .section_content_description {
    font-size: 1.9em;
    line-height: 1.4em;
    font-weight: lighter;
    padding-bottom: 1em;
    text-align: left;
    margin-top: 2em;
  }
}

.event_image_wrapper {
  .image_title_event {
    font-family: 'Montserrat', sans-serif;
    font-weight: bolder;
    color: $color1;
    font-size: 2.437em;
    margin: 0.3em 0 1em;
    line-height: 1em;
    padding-top: 0.5em;
    text-align: center;
  }
}

.form_booking.wedding {
  .titular h2 {
    font-family: 'Montserrat', sans-serif;
    font-weight: bolder;
    color: $color1;
    font-size: 2.437em;
    margin: 0.3em 0 1em;
    line-height: 1em;
    padding-top: 0.5em;
    text-align: center;
  }

  input, textarea {
    height: 2.5em;
    background: #eaeaea;
    width: 100%;
    box-sizing: border-box;
    border: 0;
    margin-bottom: 1em;
    padding: 1.3em 2em;
    font-size: 2em;
  }

  textarea {
    height: auto;
  }

  label {
    font-size: 1.8em;
    color: gray;
    display: block;
    padding-bottom: 0.5em;
  }

  a#contact-button {
    background-color: $color1;
    margin: 0 auto;
    width: 100%;
    border: none;
    border-radius: 0;
    color: white;
    font-size: 2.625em;
    font-weight: lighter;
    text-transform: uppercase;
    padding: 13px 0;
    font-family: 'Montserrat', sans-serif;
    -webkit-appearance: none;
    display: block;
    text-align: center;
    line-height: 1em;
  }
}

.promo-description {
  display: block!important;
}

.destinations_elements_wrapper {
  .flex-direction-nav .flex-prev, .flex-direction-nav .flex-next {
    opacity: 1;
    display: block;
    -webkit-transform: translate(0%,-50%);
    -moz-transform: translate(0%,-50%);
    -ms-transform: translate(0%,-50%);
    -o-transform: translate(0%,-50%);
    transform: translate(0%,-50%);

    & .prev_arrow {
      background: url(/static_1/images/mobile_img/renovation/flecha_izq.png) no-repeat center;
      height: 30px;
    }

    & .next_arrow {
      background: url(/static_1/images/mobile_img/renovation/flecha_der.png) no-repeat center;
      height: 30px;
    }

    &:before {
      content: "";
    }
  }
}
