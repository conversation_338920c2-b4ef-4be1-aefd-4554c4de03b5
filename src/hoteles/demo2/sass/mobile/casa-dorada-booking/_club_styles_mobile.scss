$corporate_1_club: #00428B;
$corporate_2_club: #009EB5;

#login_wrapper_element.version_banner_v1.v5,
#logged_user_info_wrapper.version_banner_v1 {
  background: transparent;
  margin: 10px auto;
  width: calc(100% - 20px);
  border-radius: 20px;
  padding: 0;

  &::before {
    @include full_size;
    content: '';
    background-color: $corporate_2_club;
    opacity: 0.15;
    border-radius: 20px;
  }

  .content_login_wrapper {
    padding: 0 20px;
    background-color: transparent;
    position: relative;
    z-index: 2;
    margin: 0;
    display: flex;
    align-items: center;
    height: 110px;
    flex-direction: column;
    text-align: center;

    .logo_wrapper {
      background-color: transparent;
      width: 50%;
      float: none;
      align-items: center;
      justify-content: center;
      height: initial;
      padding: 0;
      margin: auto;
      display: none;

      img {
        position: relative;
        top: initial;
        left: initial;
        transform: none;
        min-width: initial;
        min-height: initial;
      }
    }

    .button_wrapper {
      .see_more_button {
        display: none;
      }
    }

    .club_icons_wrapper {
      display: flex;
      justify-content: center;
      margin-top: 30px;

      .icon_image_wrapper {
        .icon_image_element {
           max-height: 45px;
        }
      }
    }

    .description_wrapper {
      margin-bottom: 20px;
      color: #005e81;
      display: none;
    }

    .overlay_element,
    .hidden_user_club_info,
    .square_plus {
      display: none;
    }
  }

  .users_buttons_wrapper {
    position: relative;
    z-index: 1;
    display: inline-flex;
    flex-direction: row-reverse;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 20px 20px;


    .join_button_wrapper,
    .already_member_wrapper {
      border-radius: 10px;
      width: calc(50% - 5px);
      height: 45px;
      font-family: $title_family;
      font-weight: 600;
      font-size: 11px;
      letter-spacing: .31px;
      text-transform: none;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 7px;
    }

    .join_button_wrapper {
      background: $corporate-1 !important;
      color: white;
    }

    .already_member_wrapper {
      background-color: white;
      color: $corporate_1_club;
    }

  }

  .content_login_wrapper {
    height: 90px;
  }

  .content_logged_wrapper {
    background: transparent;

    .logged_user_text, .user_points .content_wrapper {
      color: #383838;
    }
  }
}

#logged_user_info_wrapper {
  .content_logged_wrapper {
    width: 70%;
    margin: 0;
    position: relative;

    .logged_user_text {
      padding: 10px 0 5px 35px;
      position: relative;
      text-align: left;
      border-color: #b9dae6;
      width: 100%;

      i {
        position: absolute;
        top: 10px;
        left: 0;
        color: $corporate_1_club;
      }

      .default_text {
        color: $corporate_1_club;
      }

      .username_text {
        display: block;
        font-weight: 500;
        color: $corporate_1_club;
      }
    }

    .user_category_image {
      padding: 10px 10px 10px 0;
      position: relative;
      border-color: #b9dae6;
    }

    .user_points {
      .content_wrapper {
        margin-top: 0;
        border-top: 0;

        span {
          color: $corporate_1_club;

          &.points_amount {
            font-weight: bold;
          }
        }
      }
    }
  }

  .logout_button_wrapper {
    background-color: $corporate_1_club;
    border-color: $corporate_1_club;

    i {
      font-size: 12px;
    }
  }
}

.modal_wrapper {
  #popup_login_information {
    .tabs_wrapper {
      padding: 0 15px;

      .register_tab,
      .login_tab {
        font-family: $text_family;
        font-size: 16px;
        letter-spacing: 0.8px;
        line-height: 19px;
        font-weight: 700;
        text-transform: uppercase;
        color: #444444;
        opacity: .44;

        &.active {
          background: white !important;
          border: 1px solid #444444;
          border-bottom-color: white;
          opacity: 1;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            background: white;
            bottom: -3px;
            left: 0;
            right: 0;
            height: 3px;
          }
        }
      }
    }
  }

  #signup_form, #login_form_wrapper_v1 {
    padding: 0 15px;
  }

  #login_form_wrapper_v1 {
    .main_form_title {
      display: none;
    }

    .login_block {
      padding: 0;

      .login_form_title {
        display: none;
      }

      .login_form {
        .input_block {
          padding: 7px 12px;

          label {
            font-family: $text_family;
            font-size: 15px;
            padding: 6px;
            letter-spacing: 0.82px;
            color: #444444;
          }

          input {
            &::placeholder {
              font-size: 13px;
            }

            &:-webkit-autofill,
            &:-webkit-autofill:hover,
            &:-webkit-autofill:focus {
              -webkit-box-shadow: 0 0 0px 1000px white inset;
              -webkit-text-fill-color: $corporate_1_club;
            }
          }
        }
      }
    }

    .club_send_password_wrapper {
      margin: 15px auto 20px;

      .content, .toggled_content {
        font-family: $text-family;
        font-weight: 500;
      }

      .toggled_content {
        font-weight: 400;
      }
    }

    .social_login_xee {
      position: relative;

      #facebook-login-xee, #google-login-xee {
        font-family: $text_family;
        font-size: 17px;
        letter-spacing: 0.37px;
        line-height: 20px;
      }

      &::before {
        content: "O";
        position: absolute;
        left: 50%;
        top: -2px;
        transform: translateX(-50%);
        width: 60px;
        height: 20px;
        background: white;
        font-family: $text_family;
        font-size: 13px;
        letter-spacing: 0.39px;
        line-height: 20px;
        color: #444444;
        text-align: center;
      }
    }
  }

  #register_form_wrapper_v1,
  #login_form_wrapper_v1 {
    .title_wrapper_block {
      background: white !important;
      border-top: 1px solid #444444;
      padding: 20px 0;

      .main_form_title {
        font-family: $text_family;
        font-weight: 500;
        text-transform: none;
        color: #444444;
      }

      .subtitle_register {
        color: #444444;
        font-weight: 300;
      }
    }

    .buttons_wrapper_signup {
      width: 100%;
    }

    .buttons_wrapper_signup .sign_up_button,
    .buttons_wrapper_signup .user_modification_button,
    .login_button_element {
      background: $corporate-1 !important;
      color: white;
      font-family: $text_family;
      font-size: 18px;
      letter-spacing: 0.4px;
      line-height: 21px;
      font-weight: 500;
      text-transform: none;
      border-radius: 3px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 35px;
      height: 50px;
      width: 100%;
      border: 0;
    }

    .lopd_wrapper {
      display: none;
    }

    #user_info_form {
      padding: 0;

      .input_block {
        padding: 8px 0 !important;
      }
    }

    .promotions_checkbox {
      margin: 20px 10px;

      input {
        border: 1px solid #3D3D3C;
        width: 26px;
        height: 12px;

        &::before {
          width: 9px;
          height: 9px;
        }
      }
    }
  }

  #register_form_wrapper_v1 {
    .input_block.address, .input_block.city {
      display: none !important;
    }

    input {
      &:-webkit-autofill,
      &:-webkit-autofill:hover,
      &:-webkit-autofill:focus {
        -webkit-box-shadow: 0 0 0 1000px white inset;
        -webkit-text-fill-color: $corporate_1_club;
      }
    }
  }
}


#register_form_wrapper_v1.v4 .buttons_wrapper_signup .sign_up_button {
  background: $corporate_1;
}

.main_content_wrapper.step_0 {
  .tabs_wrapper .tabs.user_logged.hide_normal_rates li {
    width: 48%;
  }

  .room_list {
    .room_pack_option {
      .rates_details_wrapper {
        .regime_item_content {
          .regime_price_wrapper.lock_on_right_config {
            .lock_board_wrapper {
              min-width: 115px;
              padding: 5px;

              .club_lock_logo {
                margin-right: 5px;
              }

              .currencyValue, .monedaConv, .lock_ico {
                font-size: 14px;
              }

              .monedaConv {
                margin-left: 3px;
              }

              .lock_ico {
                position: absolute;
                right: 15px;
              }
            }
          }

          .regime_price_wrapper {
            div.submit {
              span {
                top: 40%;
              }
            }
          }

          .regime_description {
            .lock_board{
              position: absolute;
              right: -60%;
              top: 35px;
              max-width: fit-content;
              z-index: 1;
              .prices_detail_tooltip{
                color: $corporate_1_club;
                font-size: 20px;
                right: -15px;
              }
              .lock_board_wrapper {
                background-color: white !important;
                border: 1px solid $corporate_1_club;
                padding: 8px;
                margin: 20px 0 0;
                position: relative;
                min-width: 170px;
                display: grid;
                grid-template-columns: min-content 1fr;
                max-width: 200px;
                margin-right: 10px;

                .club_lock_logo{
                  max-height: 25px;
                  margin-top: 8px;
                  grid-row: 1 / 3;
                }

                .lock_tooltip {
                  background-color: $corporate_1_club;
                  color: white;
                  font-size: 12px;
                  font-weight: 300;

                  &::before {
                    border-color: $corporate_1_club transparent transparent transparent;
                  }
                }

                .currencyValue {
                  font-size: 20px;
                }

                .lock_ico {
                  font-size: 15px;
                  position: absolute;
                  top: -11px;
                  left: 30px;
                  z-index: 2;
                  margin: 0;
                  color: $corporate_1_club;
                }

                .lock_extra_text {
                  color: $corporate_1_club;
                }

                .precioTachadoDiv {
                  font-size: 14px;
                  color: $black;
                  font-weight: bold;
                  top: 8px;
                  left: 35px;
                  display: flex;
                  flex-direction: row-reverse;
                  justify-content: center;

                  .tPrecioTachado {
                    font-size: 14px;
                    font-family: $font_1;
                  }
                }

                .precioGeneralDiv{
                  margin-top: 20px;
                  color: $black;
                  font-family: $font_1;
                  grid-column: 2 / 3;
                  margin: 0;

                  .monedaConv{
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 14px;
                  }
                  .currencyValue{
                    font-size: 20px;
                    font-weight: bold;
                    line-height: 14px;
                  }
                  .night_label{
                    //display: block;
                    font-size: 11px;
                    color: $lightgrey2;
                    line-height: 14px;
                  }
                }

                .lock_mini_label {
                  position: absolute;
                  padding: 0 5px 0 30px;
                  top: -7px;
                  left: 20px;
                  background-color: white;
                  font-family: $font_1;
                  font-size: 11px;
                  line-height: 11px;
                  z-index: 1;
                  font-weight: 500;
                }

              }

              .tag_logged_rates_wrapper {
                color: var(--club-main-color);
                position: relative;
                font-size: 12px;
                font-weight: 500;
                margin: 5px 0 5px 23px;

                &:before {
                  content: '';
                  background-image: url(https://storage.googleapis.com/cdn.paraty.es/hotansa-massana/files/Logo_club.png);
                  width: 20px;
                  height: 20px;
                  position: absolute;
                  background-size: contain;
                  background-repeat: no-repeat;
                  top: 50%;
                  transform: translateY(-50%);
                  left: -23px;
                }
              }
            }
            .precioNocheColumn.daily_prices_normal_rate{
              position: relative;
              .normal_rates_mini_label{
                display: block;
                color: $corporate_1_club;
                font-size: 11px;
                font-family: $font_1;
                font-weight: 500;
              }
              .price_through{
                font-size: 14px;
                font-weight: bold;
                color: $black;
                font-family: $font_1;
              }
              .final_price{
                font-family: $font_1;
                display: inline-block;
                &.has_promotion{
                  color: $red !important;
                }
                .monedaConv{
                  font-size: 16px;
                  font-weight: 400;
                }
                .currencyValue{
                  font-size: 20px;
                  font-weight: bold;
                }
              }
              .prices_detail_tooltip{
                color: $corporate_1_club;
                font-size: 20px;
                margin-left: 10px;
              }
              .night_label{
                color: $lightgrey2;
                font-family: $font_1;
                font-size: 14px;
                font-weight: 400;
              }
            }

            .tax_inc_wrapper_info.prices_detail_popup{
              border-color: $corporate_1_club;
              font-family: $font_1;
              top: -10px;
              border-radius: 10px;
              padding: 16px;

              .club_rates_mini_label, .normal_rates_mini_label{
                font-size: 11px;
                color: $corporate_1_club;
                font-weight: 500;
                line-height: 21px;
              }
              .price_detail, .field{
                font-size: 16px;
                font-weight: 400;
                padding: 3px 0;
                color: $black;
                .label_nam{
                  font-weight: 400;
                }
                .block_price, &.sub_total_field{
                  font-weight: bold;
                  .monedaConv, .currencyValue{
                    color: inherit;
                  }
                }
              }
            }

            .lock_board{
              .tax_inc_wrapper_info.prices_detail_popup{
                left: auto;
                right: 0;
                transform: translate(0%, -100%);
              }
            }
            .precioNocheColumn.daily_prices_normal_rate{
              .tax_inc_wrapper_info.prices_detail_popup{
                left: 0;
                transform: translateY(-100%);
              }
            }

          }
        }

        &.not_logged, &.only_logged{
          .regime_description{
            display: flex;
            flex-direction: column;
            .prices_options{
              order: 1;
            }
          }
          .regime_price_wrapper {
            height: 180px;
            z-index: 0;
            width: 40%;
            .submit.room_submit{
              height: max-content;
              top: auto;
              bottom: 40px;
              span{
                min-width: 170px;
                margin-right: 30px;
              }
            }
          }
        }

        &.only_logged {
          .regime_item_content {
            .regime_description {
              .lock_board_wrapper, .tag_logged_rates_wrapper {
                display: none !important;
              }
            }
          }
        }
      }
    }
  }
}

.room_pictures.modal_launch.owl-carousel.owl-loaded.owl-drag{
  z-index: 0;
}

.modal_wrapper.recover_password_popup{
  .modal_content{
    width: 100%;
    background-color: rgba(0, 0, 0, 0.6);
  }
}

&.currency_COP, &.currency_MXN{
  .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_description .lock_board .lock_board_wrapper{
    .precioGeneralDiv{
      .currencyValue {
        font-size:14px;
      }

      .night_label{
        font-size:10px;
      }
    }

    .precioTachadoDiv .tPrecioTachado{
      font-size:13px;
    }

    .club_lock_logo{
      margin-right: 3px;
    }
  }
}

