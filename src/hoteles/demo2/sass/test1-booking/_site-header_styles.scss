div.site-header {
    height: 130px;
    padding: 0 30px 0 0;
    //background: $corporate_1 !important;
    font-family: $font_2;
    border-bottom: 1px solid $corporate_1;
    i:before {
      font-family: "Font Awesome 5 Pro";
    }
    .site-header__logo {
      position: relative;
      margin-top: 20px;
      a {
        img {
          margin:0;
          max-height: 80px;
        }
      }
    }
    .site-header__ticks {
      margin-top: 30px;
      margin-right: 60px;

      .site-header__tick-item {
        margin-top: 5px;
        margin-right: 0;
        float: none;
        display: inline-block;
        vertical-align: middle;
        width: 120px;

        p {
          width: 80px;
          font-size: 8px;
          font-family: $text_family;
          line-height: 12px;
          text-transform: uppercase;
          font-weight: 300;
          text-align: center;
          margin-left: -10px;
          letter-spacing: 2px;
          color: #adc067 !important;

        }
        .icon-checkmark {
          font-size: 50px;
          margin-top: -10px;
          display: inline-block;

          &:before {
            font-size: 22px !important;
            line-height: 52px !important;
            color: #adc067 !important;
          }
        }
        &:first-of-type {
          margin-top: 5px !important;
          .icon-checkmark{
            padding: 5px !important;
            &:before {
              font-family: "Font Awesome 5 Pro" !important;
              content: "\f274" !important;
              font-weight: 300;
            }
          }
          p {
            margin-left: 0;
            top: 0 !important;
          }
        }
        &:nth-of-type(2) .icon-checkmark:before {
          font-family: "Font Awesome 5 Pro" !important;
          content: "\f132" !important;
          font-weight: 300;
          p {
            width: 60px;
          }
        }
        &:nth-of-type(3) .icon-checkmark:before {
          font-family: "Font Awesome 5 Pro" !important;
          content: "\f4d3" !important;
          font-weight: 300;
        }
        &:last-of-type {
          display: none;
        }
      }
    }

    .language_header_selector_booking_process {
      bottom: -35px;
      border: none;
      width: 110px;
      right: 0;
      text-transform: uppercase;
      .selected_language {
        font-family: $text_family !important;
        font-size: 14px;
        margin-top: 0;
        color: #adc067 !important;
        font-weight: 300;
        width: 110px;
        i {
          color: #adc067;
          font-size: 20px;
        }
      }
      .selected_language_code {
        padding-right: 15px;

        &:before {
          content:"\f107";
          font-family: "Font Awesome 5 Pro";
          font-size: 18px;
          font-weight: 300;
          position: absolute;
          right: 5px;
          top: 25px;
        }
      }
      .language_booking_selector_wrapper {
        top: 65% !important;
      }
    }
  }

.actual_wizard_step {
  padding-top: 30px;
  li.wizard-tab--small a,
  li.wizard-tab--big a {
    border-left: 2px solid white;
    background: $corporate_1;

    &:not(.disable) {
      &:before {
        display: block;
        border-left-color: white;
        left: calc(100% + 2px);
      }
      &:after {
        border-left: 19px solid $corporate_1;
      }
    }

    &.disable {
      background: #f5f5f5;
      color: $black;

      &:before {
        display: block;
        border-left-color: white;
        left: calc(100% + 2px);
      }
      &:after {
        border-left: 19px solid #f5f5f5;
      }
    }
  }
}

