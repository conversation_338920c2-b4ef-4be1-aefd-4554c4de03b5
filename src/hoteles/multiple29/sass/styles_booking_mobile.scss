@import "booking_mobile/v1/mixin_colores";
@import "booking_mobile/booking";
@import url('https://fonts.googleapis.com/css2?family=Merriweather:ital,wght@0,300;0,400;0,700;1,400;1,700&family=Nunito:wght@300;400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Arya:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cabin:wght@400;500;600;700&display=swap');


html[lang='ru'], html[lang='de'] {
  body.mogay .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit:after, body .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
    font-size: 0.7rem;
  }
}

body.mogay {
  $corporate_1: #8F2C36;
  $corporate_2: #707070;
  $corporate_3: #C4A789;
  $black: #3D3D3C;
  $red: #EC6363;
  $font_1: "Nunito", sans-serif;

  font-family: $font_1;
  color: $black;

  input, button, select {
    font-family: $font_1;
  }

  @include set_color_process($corporate_1, $corporate_2, $corporate_3);

  header {
    background-color: $corporate_1;

    a.icon_toggle_wrapper {
      color: white;

      &:before {
        color: white;
      }
    }

    #sideMenu {
      background: $corporate_1;
    }

    a.icon_toggle_wrapper.icon_toggle_active:before {
      color: white;
    }
  }

  .modify_search {
    background-color: $corporate_1;
    color: white;
    border: none;
    margin: 5px;
  }

  .continue_booking {
    background: $corporate_1;
  }

  .show_calendar, .back_button {
    background-color: $corporate_2 !important;
    color: white;
    border: none;
    padding: 0 20px 0 50px;
    margin: 5px
  }

  .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit:after, .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
    background: $corporate_1;
  }

  .regime_item_content {
    .prices_options {
      .final_price {
        color: $black !important;
      }

      .price_through {
        color: $red !important;
      }
    }

    .regime_description .regime_offer_detail {
      color: $red !important;
    }

    .discount_percentage {
      background-color: $red !important;
    }
  }

  .modal_container.room_content {
    padding: 0 15px 35px 15px !important;

    .room_description {
      ul {
        padding-left: 20px;
        margin-top: 20px;
      }
    }
  }

  #calendar_price_availability .header_wrapper_calendar_availability .popup_helper_wrapper.search_button {
    .message_element {
      background: $corporate_1;
      color: white;
      padding: 10px;
      font-weight: bold;
    }
  }

  #calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper {
    margin-bottom: 15px;
  }

  #calendar_price #prices-calendar .calendars-section .buttons-section .button, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .button {
    position: relative;
  }

  .loading_animation_popup {
    background: $corporate_1;

    span {
      color: white;
    }

    .spincircle {
      border-color: rgba(#f5f5f5, .2);

      .spinner_animation {
        border-color: transparent transparent white transparent;
        width: 150px;
        height: 150px;
      }

      &:after {
        border-color: transparent transparent $corporate_2 transparent !important;
      }
    }
  }

  .booking_widget_wrapper #entry_date_popup .header_wrapper .banner_title i, .booking_widget_wrapper #departure_date_popup .header_wrapper .banner_title i {
    color: $corporate_1;
  }

  .booking_widget_wrapper {
    #entry_date_popup .start_datepicker *, #entry_date_popup .end_datepicker *, #departure_date_popup .start_datepicker *, #departure_date_popup .end_datepicker * {
      font-family: $font_1 !important;
    }

    .occupancy_popup .occupancy_head.content_title::before {
      color: $corporate_1;
    }

    .input_wrapper {
      .input_title, .ocupation_detail p {
        font-size: 1rem !important;
      }
    }
  }

  #reservation {
    .email_header_logo {
      text-align: center;

      #logo-container {
        float: none !important;

        #logo {
          float: none !important;

          img {
            margin: auto;
          }
        }
      }
    }
  }

  footer {
    background-color: $corporate_1;
    color: white;

    .legal_info_wrapper a {
      color: white;
    }

    .logo_wrapper img {
      max-height: 60px;
    }
  }
}

body.casas-novas {
  $corporate_1: #3E5902;
  $corporate_2: #595857;
  $corporate_3: #cdcecf;
  $black: #3D3D3C;
  $red: #EC6363;
  $font_1: "Quicksand", sans-serif;

  font-family: $font_1;
  color: $black;

  input, button, select {
    font-family: $font_1;
  }

  @include set_color_process($corporate_1, $corporate_2, $corporate_3);

  header {
    background-color: white;

    a.icon_toggle_wrapper {
      color: $corporate_1;

      &:before {
        color: $corporate_1;
      }
    }

    #sideMenu {
      background: $corporate_1;
    }

    a.icon_toggle_wrapper.icon_toggle_active:before {
      color: white;
    }
  }

  .modify_search {
    background-color: $corporate_1;
    color: white;
    border: none;
    margin: 5px;
  }

  .continue_booking {
    background: $corporate_1;
  }

  .show_calendar, .back_button {
    background-color: $corporate_2;
    color: white;
    border: none;
    padding: 0 20px 0 50px;
    margin: 5px
  }

  .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit:after, .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
    background: $corporate_1;
  }

  .regime_item_content {
    .prices_options {
      .final_price {
        color: $black !important;
      }

      .price_through {
        color: $red !important;
      }
    }

    .regime_description .regime_offer_detail {
      color: $red !important;
    }

    .discount_percentage {
      background-color: $red !important;
    }
  }

  .modal_container.room_content {
    padding: 0 15px 35px 15px !important;

    .room_description {
      ul {
        padding-left: 20px;
        margin-top: 20px;
      }
    }
  }

  #calendar_price_availability .header_wrapper_calendar_availability .popup_helper_wrapper.search_button {
    .message_element {
      background: $corporate_1;
      color: white;
      padding: 10px;
      font-weight: bold;
    }
  }

  #calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper {
    margin-bottom: 15px;
  }

  #calendar_price #prices-calendar .calendars-section .buttons-section .button, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .button {
    position: relative;
  }

  .loading_animation_popup {
    background: $corporate_1;

    span {
      color: white;
    }

    .spincircle {
      border-color: rgba(#f5f5f5, .2);

      .spinner_animation {
        border-color: transparent transparent white transparent;
        width: 150px;
        height: 150px;
      }

      &:after {
        border-color: transparent transparent $corporate_2 transparent !important;
      }
    }
  }

  .booking_widget_wrapper #entry_date_popup .header_wrapper .banner_title i, .booking_widget_wrapper #departure_date_popup .header_wrapper .banner_title i {
    color: $corporate_1;
  }

  .booking_widget_wrapper {
    #entry_date_popup .start_datepicker *, #entry_date_popup .end_datepicker *, #departure_date_popup .start_datepicker *, #departure_date_popup .end_datepicker * {
      font-family: $font_1 !important;
    }

    .occupancy_popup .occupancy_head.content_title::before {
      color: $corporate_1;
    }

    .input_wrapper {
      .input_title, .ocupation_detail p {
        font-size: 1rem !important;
      }
    }
  }

  #reservation {
    .email_header_logo {
      text-align: center;

      #logo-container {
        float: none !important;

        #logo {
          float: none !important;

          img {
            margin: auto;
          }
        }
      }
    }
  }

  footer {
    background-color: $corporate_1;
    color: white;

    .row.has_pad {
      flex-direction: column;

      .info.w-50 {
        width: calc(100% - 30px);
        text-align: center;

        img {
          max-width: 100%;
        }
      }
    }

    .legal_info_wrapper a, .current_year {
      color: white;
    }

    .logo_wrapper img {
      max-height: 60px;
    }
  }
}


body.booking_process_mobile_v1.neptuno-roquetas {
  $corporate_1: #46C2B1;
  $corporate_2: rgba($corporate_1, .7);
  $corporate_3: #9CC5C9;
  $corporate_4: rgb(231, 239, 240);
  $gradient: transparent linear-gradient(270deg, #46C2B1 0%, #236159 100%) 0% 0% no-repeat padding-box;

  $red: #EC6363;
  $green: #9FD015;
  $black: #3D4E53;
  $grey-1: #F5F5F5;
  $grey-2: #444444;
  $grey-3: #F5F5F5;
  $offer: #9FD015;

  $dispo: #34A853;
  $special: #FBBC05;
  $nodispo: #EA4335;
  $prevday: #AAAAAA;

  $title_family: 'Arya', sans-serif;
  $text_family: 'Cabin', sans-serif;

  font-family: $text_family;
  color: $black;

  @import "new-booking-on/onhotels_booking_mobile";

  .main_content_wrapper.step_2 .personal_details_form_wrapper .personal_details_form .bottom_button_wrapper #btn-finish-booking {
    background: $corporate_1;
  }
}

body.lisbon-best-choice {
  $color1: #918063;
  $color2: #707173;

  .modify_search {
    background-color: $color1;
    color: white;
    border: 0;
  }

  .show_calendar, .back_button {
    color: $color1;
    border: 1px solid $color1;
  }

  .submit {
    span {
      background: $color1 !important;
    }
  }
}

$day_not_available: #fd013c !default;
$day_available: #6cb9ff !default;

@import "booking_mobile/v1/mixin_colores";
@import url('https://fonts.googleapis.com/css?family=Quicksand:300,400,500,600,700&display=swap');

.senhora-rosa.booking_process_mobile_v1 {
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
  $corporate_1: #b0ca9a;
  $corporate_2: #b0ca9a;
  $corporate_3: #b0ca9a;
  $corporate_4: #b0ca9a;
  $black: black;
  $grey: #F4F4F4;
  $grey-2: #444444;
  $green: #004621;
  $darkGreen: #004621;
  $red: #BC1B36;
  $title_family: 'Poppins', sans-serif;
  $text_family: 'Poppins', sans-serif;
  $font_1: $text_family;
  $font_2: $text_family;

  .label.label_primary {
    background: #d3d3d3;
  }

  .double_button_wrapper {
    button {
      margin: 5px;
      border-color: $grey-2;
      color: $grey-2;
      font-weight: 700;
      font-size: 11px;
      letter-spacing: 0.75px;
    }
  }

  .back_booking,
  .continue_booking {
    background-color: $corporate_1;
  }

  .booking_widget_wrapper {
    .double_button_wrapper {
      .close_button {
        background-color: $corporate_1;
        color: white;
      }
    }

    .input_wrapper:before {
      background-color: $corporate_1;
    }

    #departure_date_popup,
    #entry_date_popup {
      .header_wrapper {
        .banner_title {
          i {
            color: $corporate_1;
          }
        }
      }

      .end_datepicker,
      .start_datepicker {
        .ui-datepicker-inline {
          .highlight_day {
            background: $corporate_1;
          }

          .start_date_selection {
            span {
              &::before {
                background: $corporate_1;
              }
            }

            &::before {
              background: $corporate_1;
            }
          }

          .ui-datepicker-current-day {
            .ui-state-active:before {
              background: $corporate_1;
            }

            &::before {
              background: $corporate_1;
            }
          }

          .ui-datepicker-header {
            color: $corporate_1;
          }
        }
      }
    }
  }

  .main_content_wrapper {
    &.step_0 {
      .double_button_wrapper {
        .modify_search {
          background-color: $darkGreen;
          color: #fff;
          border-color: $darkGreen;
        }

        .show_calendar {
          background-color: $corporate_1;
          color: #fff;
          border-color: $corporate_1;
        }
      }

      .paraty-booking-form {
        .double_button_wrapper {
          .re-search_button.btn.btn_primary.btn_icon.btn_block.submit_button {
            background-color: $darkGreen;
            color: #fff;
          }
        }
      }

      .room_list {
        .room_pack_option {
          .labels_wrapper {
            .just_booking.label.label_primary {
              background-color: $corporate_1;
            }
          }

          .rates_details_wrapper {
            .rate_selected_title {
              span {
                color: $corporate_1;
              }
            }

            .regime_item_content {
              .regime_description {
                .previously_selected {
                  background-color: $corporate_1;
                }

                .regime_offer_detail {
                  .room_list_name_offer {
                    .promotion_element_name {
                      color: $black;
                    }
                  }
                }

                .prices_options {
                  .price_through {
                    color: $black;
                  }

                  .final_price {
                    color: $black;
                  }
                }
              }

              .regime_price_wrapper {
                .discount_percentage {
                  background-color: $darkGreen;
                }

                .submit.room_submit {
                  span {
                    background-color: $corporate_1;
                  }

                }
              }
            }
          }
        }
      }
    }

    &.step_1 {
      .additional_services {
        .additional_services_wrapper {
          .counter_box {
            > div {
              color: $corporate_1;
            }

            .control {
              border-color: $corporate_1;

              &.subtract {
                &::before {
                  background-color: $corporate_1;
                }
              }

              &.add {
                background-color: $corporate_1;
              }
            }
          }

          .supplement_element {
            .product_details {
              .btn.btn_secondary.add_service {
                background-color: $corporate_1;
              }
            }
          }
        }
      }
    }

    &.step_2 {
      .reservation_summary {
        .option_selected {
          .rate {
            .conditions {
              color: $corporate_1;
            }
          }

          .price {
            color: $corporate_1;
          }
        }
      }
    }
  }

  footer {
    .logo_wrapper {
      display: none;
    }

    .info {
      text-align: center;
      width: 80%;

    }
  }

  #main_modal.active {
    .body_modal.regime_conditions_modal_wrapper, .body_modal.iframe_modal_wrapper {
      .body_modal_content iframe {
        height: 70vh;
      }
    }

    .body_modal.rooms_features_modal_wrapper {
      .body_modal_content {
        .modal_container.room_content {
          .icons_room {
            .room_services .service_element {
              margin: 5px 0px;

              .service_image {
                max-width: 35px;
              }
            }
          }
        }
      }
    }
  }
}

.boho-marbella.booking_process_mobile_v1 {
  header {
    .logo_wrapper {
      a {
        height: 20px;

        &::before {
          content: "";
          position: absolute;
          left: 50%;
          top: 50%;
          width: 105px;
          height: 15px;
          transform: translate(-50%, -50%);
          background-image: url(https://storage.googleapis.com/cdn.paraty.es/boho-marbella/files/logo_color_reserva_movil.png);
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
          z-index: 1;
        }

        img {
          display: none;
        }
      }
    }

    .menu {
      .logo-wrapper {
        position: relative;
        height: 40px;

        img {
          display: none;
        }

        &::before {
          content: "";
          position: absolute;
          left: 50%;
          top: 50%;
          width: 105px;
          height: 15px;
          transform: translate(-50%, -50%);
          background-image: url(https://storage.googleapis.com/cdn.paraty.es/boho-marbella/files/logo_color_reserva_movil.png);
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
          z-index: 1;
        }
      }
    }
  }

  .double_button_wrapper {
    .show_calendar {
      background: #ffbfc5;
      color: white;
    }
  }

  .room_option_block {
    .room_pack_option {
      .room_rates_list {
        .rates_details_wrapper {
          .rate_selected_title {
            .modal_launch {
              color: #000000;
            }
          }

          .regime_item_content {
            .regime_description {
              .regime_offer_detail {
                .promotion_name {
                  color: #a35157;
                }
              }

              .price_through {
                color: #a35157;
              }
            }

            .regime_price_wrapper {
              .discount_percentage {
                background: #a35157;
              }
            }
          }
        }
      }
    }
  }
}

body.bahia-serena.booking_process_mobile_v1 {
  @import "bahia-serena/bahiaserena_booking_mobile";
}
