body.booking_process_version_1 {
  .datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
    .datepicker_ext_inf_sd,
    .datepicker_ext_inf_ed {
      .ui-datepicker-start_date {
        .ui-state-default {
          background: $corporate_1 !important;
          &:before {
            border-left-color: $corporate_1 !important;
          }
        }
      }
      .highlight {
        background: rgba($corporate_2, .5) !important;
        a.ui-state-default {
          color: white !important;
          background: transparent !important;
        }
        &.last-highlight-selection {
          background: rgba($corporate_2, .8) !important;
        }
      }
      .ui-state-active {
        background: $corporate_2 !important;
      }
      .ui-state-hover {
        background: $corporate_1 !important;
      }
    }
    .specific_month_selector, .go_back_button {
      background: $corporate_1 !important;
    }
  }
  .booking_engine_modifications_wrapper {
    &.calendar_showed {
      overflow: visible;
      max-height: 780px;
    }
  }

  .header_wrapper_calendar_availability {
    width: 1040px;
    .graph_calendar_selector {
      .calendar_button,
      .graph_button {
        background-color: #D7D7D7;
        text-transform: uppercase;
        letter-spacing: 1px;
        color: black;
        .ico {
          position: relative;
          width: 30px;
          height: 30px;
          background-size: 30px;
          background: transparent;
          &:before {
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
            -moz-transform: translate(-50%, -50%);
            -ms-transform: translate(-50%, -50%);
            -o-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
            font-family: "icomoon", sans-serif;
            font-size: 30px;
            speak: none;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }
        }
        &.active {
          background: white;
          color: black;
          position: relative;
          z-index: 1;
        }
      }
      .calendar_button {
        .ico {
          @extend .icon-calendar2;
        }
      }
      .graph_button {
        .ico {
          @extend .icon-chart;
        }
      }
    }
    .popup_helper_wrapper {
      background-color: $corporate_2;
    }
  }

  #prices-calendar {
    width: 1040px;
    padding: 50px 0 0;
    background-color: white;
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.5);
    border-radius: 0 10px 10px 10px;
    .overlay_loading {
      background-color: white;
    }
    .popup_month_selector {
      position: absolute;
      top: 65px;
      left: 0;
      right: 0;
      width: 100%;
      padding: 0;
      .month_select_element {
        background-color: #D7D7D7;
        background-image: url("/static_1/images/booking/angle-down.png");
        background-size: 12px;
        background-position: calc(100% - 10px) center;
        border-width: 0;
        text-transform: uppercase;
        font-size: 14px;
        margin-top: -83px;
        margin-left: 7px;
      }
      .previous_month_selector,
      .next_month_selector {
        position: relative;
        background: transparent;
        @extend .fa-caret-left;
        &:before {
          position: absolute;
          top: 50%;
          left: 50%;
          -webkit-transform: translate(-50%, -50%);
          -moz-transform: translate(-50%, -50%);
          -ms-transform: translate(-50%, -50%);
          -o-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
          @extend .fa;
          font-size: 40px;
          color: $corporate_1;
        }
      }
      .previous_month_selector {
        float: left;
        margin-left: 10px;
      }
      .next_month_selector {
        float: right;
        margin-right: 10px;
      }
    }
    #selectorRooms {
      margin: 0 0 8px 620px;
      .text {
        color: black;
      }
      #roomFilterInCalendar {
        background-color: #D7D7D7;
        background-image: url("/static_1/images/booking/angle-down.png");
        background-size: 12px;
        background-position: calc(100% - 10px) center;
        border-width: 0;
        text-transform: uppercase;
        font-size: 14px;
        width: 220px;
      }
    }
    .calendars-section {
      padding: 0;
      .calendars {
        max-height: 500px;
        padding: 0 15px;
        &.hide {
          display: none !important;
        }
        .calendars_wrapper {
          .calendar {
            text-align: center;
            table.calendar {
              width: 470px;
              th {
                border-color: transparent;
                background-color: $corporate_1;
                font-size: 30px;
                letter-spacing: 3px;
                padding: 10px 0;
                &.day_label_element {
                  border-color: white;
                  font-size: 14px;
                  padding: 5px 0;
                  background-color: #4B4B4B;
                  border-top: 4px solid white;
                  border-bottom: 4px solid white;
                }
              }
              td {
                border-color: transparent;
                &.selected-cell-parent {
                  .day {
                    &.available-day {
                      background-color: #73DCF8 !important;
                    }
                  }
                  .day-content.selected-cell {
                    background-color: #73DCF8 !important;
                    .price {
                      &:before {
                        color: white;
                      }
                    }
                  }
                  &.first-selection {
                    .day {
                      &.available-day {
                        background-color: #00B8F2 !important;
                      }
                    }
                    .selected-cell {
                      background-color: #00B8F2 !important;
                      &:before {
                        border-color: transparent transparent transparent #00B8F2 !important;
                        color: white;
                      }
                    }
                  }
                  &.end-selection {
                    .day {
                      &.available-day {
                        background-color: #007694 !important;
                      }
                    }
                    .selected-cell {
                      background-color: #007694 !important;
                      &:before {
                        border-color: transparent #007694 transparent transparent !important;
                        color: white;
                      }
                    }
                  }
                }
                .day {
                  &.available-day {
                    background-color: $corporate_2;
                  }
                  &.not-available-day {
                    background-color: $corporate_1;
                  }
                  &.restricted-day {
                    & + .day-content {
                      .price {
                        &:before {
                          margin-top: 5px;
                          margin-bottom: -5px;
                        }
                      }
                    }
                  }
                }
                .day-content {
                  background-color: #fafafa;
                  &.no-available {
                    .not-available-message {
                      padding: 6px 10px;
                    }
                  }
                }
                .day-content {
                  height: 40px;
                  &.available {
                    .price {
                      margin-top: -7px;
                      @extend .fa-check;
                      &:before {
                        display: block;
                        @extend .fa;
                        color: $corporate_2;
                        font-size: 16px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      .graphs_fields_wrapper {
        background: transparent;
        margin-top: 10px;
      }
      .legend {
        padding: 10px 0;
        ul {
          li {
            display: inline-block;
            div {
              display: inline-block;
              vertical-align: middle;
              width: 15px;
              height: 15px;
              &.available-day-box {
                background-color: $corporate_2;
              }
              &.restricted-day-box {
                background-color: #E28921;
              }
              &.not-available-day-box {
                background-color: $corporate_1;
              }
            }
            p {
              display: inline-block;
              vertical-align: middle;
              color: #666;
              font-size: 12px;
              font-weight: bold;
              margin-bottom: 0;
            }
          }
        }
      }
      .buttons-section {
        .actual_selection_info_wrapper {
          .label_actual_selection,
          .nights_number_wrapper,
          .selection_price_wrapper {
            height: 75px;
            .vertical_center {
                top: 47%;
            }
          }
          .label_actual_selection {
            padding-top: 15px;
            padding-bottom: 15px;
            font-size: 18px;
            font-weight: lighter;
            font-family: "Montserrat", sans-serif;
          }
          .nights_number_wrapper {
            position: relative;
            background-color: #999;
            .nights_number, label {
              display: block;
              text-align: center;
              width: 100%;
            }
            .nights_number {
              margin-top: 25px;
              font-size: 30px;
              font-weight: normal;
              font-family: "Montserrat", sans-serif;
              font-weight: lighter;
            }
            label {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              margin: 0;
              text-transform: uppercase;
              font-size: 14px;
              font-family: "Source Sans Pro", sans-serif;
              font-weight: normal;
            }
          }
        }
        button.button {
          height: 75px;
          font-family: $font_1;
          background-color: $corporate_1;
          color: white;
          font-size: 25px;
          position: relative;
          -webkit-transition: all 0.6s;
          -moz-transition: all 0.6s;
          -ms-transition: all 0.6s;
          -o-transition: all 0.6s;
          transition: all 0.6s;
          display: inline-block;
          vertical-align: middle;
          font-weight: bold;
          text-transform: uppercase;
          border-radius: 0;
          z-index: 1;
          @extend .icon-longarrow;
          &:before {
            @extend .fa;
            font-family: 'icomoon', sans-serif;
            position: absolute;
            top: 50%;
            right: 15px;
            -webkit-transform: translate(0%, -50%);
            -moz-transform: translate(0%, -50%);
            -ms-transform: translate(0%, -50%);
            -o-transform: translate(0%, -50%);
            transform: translate(0%, -50%);
            color: white;
            font-size: 35px;
            z-index: 1;
          }
          &:after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: auto;
            background: $corporate_1;
            width: 0;
            -webkit-transition: width 0.6s;
            -moz-transition: width 0.6s;
            -ms-transition: width 0.6s;
            -o-transition: width 0.6s;
            transition: width 0.6s;
            z-index: -1;
          }
          &:hover {
            color: white;
            &:after {
              left: 0;
              width: 100%;
            }
          }
        }
      }
    }
  }
  .room_popup_individual_element {
    .popup_carousel {
      overflow: hidden;
    }
  }
}

div.calendar_popup_wrapper {
  .header_popup {
    .calendar_button_head, .graph_button_head {
      background-color: #D7D7D7;
      text-transform: uppercase;
      letter-spacing: 1px;
      color: black;
      span {
        position: relative;
        width: 30px;
        height: 30px;
        background-size: 30px;
        background: transparent;
        &:before {
          position: absolute;
          top: 50%;
          left: 50%;
          -webkit-transform: translate(-50%, -50%);
          -moz-transform: translate(-50%, -50%);
          -ms-transform: translate(-50%, -50%);
          -o-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
          font-family: "icomoon", sans-serif;
          font-size: 30px;
          speak: none;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
      }
      &.active {
        background: white;
        color: black;
        position: relative;
        z-index: 1;
      }
    }
    .calendar_button_head {
      span {
        @extend .icon-calendar2;
      }
    }
    .graph_button_head {
      span {
        @extend .icon-chart;
      }
    }
  }
  .popup_month_selector {
    position: absolute;
    top: 90px;
    left: 0;
    right: 0;
    width: 100%;
    padding: 5px 10px !important;
    background: transparent !important;
    .month_select_element {
      display: none;
    }
    .previous_month_selector,
    .next_month_selector {
      position: relative;
      background: transparent;
      @extend .fa-caret-left;
      &:before {
        position: absolute;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        -moz-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        -o-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        @extend .fa;
        font-size: 40px;
        color: white;
      }
    }
    .previous_month_selector {
      float: left;
      margin-left: 10px;
    }
    .next_month_selector {
      float: right;
      margin-right: 10px;
    }
  }
  .calendar_field {
    padding-top: 20px !important;
    background: white !important;
    table.calendar {
      th {
        border-color: transparent;
        background-color: $corporate_1;
        font-size: 20px;
        padding: 10px 0;
        &.day_label_element {
          border-color: white;
          font-size: 12px;
          padding: 5px 0;
          background-color: #4B4B4B;
          border-top: 4px solid white;
          border-bottom: 4px solid white;
        }
      }
      td {
        border-color: transparent;
        &.selected-cell-parent {
          .day {
            &.available-day {
              background-color: #73DCF8 !important;
            }
          }
          .day-content.selected-cell {
            background-color: #73DCF8 !important;
            .price {
              &:before {
                color: white;
              }
            }
          }
          &.first-selection {
            .day {
              &.available-day {
                background-color: #00B8F2 !important;
              }
            }
            .selected-cell {
              background-color: #00B8F2 !important;
              &:before {
                border-color: transparent transparent transparent #00B8F2 !important;
                color: white;
              }
            }
          }
          &.end-selection {
            .day {
              &.available-day {
                background-color: #007694 !important;
              }
            }
            .selected-cell {
              background-color: #007694 !important;
              &:before {
                border-color: transparent #007694 transparent transparent !important;
                color: white;
              }
            }
          }
        }
        .day {
          &.available-day {
            background-color: $green;
          }
          &.not-available-day {
            background-color: $red;
          }
          &.restricted-day {
            & + .day-content {
              .price {
                &:before {
                  margin-bottom: -5px;
                  color: $corporate_2;
                }
              }
            }
          }
        }

        .day-content {
          background-color: #fafafa;
          &.no-available {
            .not-available-message {
              padding: 6px 10px;
            }
          }
        }
        .day-content {
          height: 40px;
          &.available {
            .price {
              margin-top: -7px;
              @extend .fa-check;
              &:before {
                display: block;
                @extend .fa;
                color: $green;
                font-size: 16px;
              }
            }
          }
        }
      }
    }
  }
  div.loading_popup_spinner {
    background: white;
    span {
      background-color: #DCDCDC;
      font-family: $font_1;
      font-weight: 400;
      color: white;
      border-radius: 20px;
      strong {
        font-weight: 700;
      }
    }
  }
  .graphs_field_wrapper {
    background: white !important;
    padding-top: 10px !important;
  }
  .legend_wrapper {
    background: white !important;
    div {
      color: #333 !important;
      .legend_square {
        display: inline-block;
        vertical-align: middle;
        width: 15px;
        height: 15px;
      }
      &.available_stay .legend_square {
        background-color: $green;
      }
      &.min_stay .legend_square {
        background-color: #E28921;
      }
      &.no_dispo .legend_square {
        background-color: $red;
      }
    }
  }

  .total_price_wrapper {
    background: white !important;
    .total_price_label {
      .nights_number_wrapper {
        background: $corporate_1 !important;
      }
    }
    .booking_button_element {
      font-family: $font_1;
      background-color: $corporate_1 !important;
      color: white;
      font-size: 25px;
      position: relative;
      -webkit-transition: all 0.6s;
      -moz-transition: all 0.6s;
      -ms-transition: all 0.6s;
      -o-transition: all 0.6s;
      transition: all 0.6s;
      display: inline-block;
      vertical-align: middle;
      font-weight: bold;
      text-transform: uppercase;
      border-radius: 0 !important;
      z-index: 1;
      @extend .icon-longarrow;
      &:before {
        @extend .fa;
        font-family: 'icomoon', sans-serif;
        position: absolute;
        top: 50%;
        right: 15px;
        -webkit-transform: translate(0%, -50%);
        -moz-transform: translate(0%, -50%);
        -ms-transform: translate(0%, -50%);
        -o-transform: translate(0%, -50%);
        transform: translate(0%, -50%);
        color: white;
        font-size: 35px;
        z-index: 1;
      }
      &:after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: auto;
        background: $corporate_1;
        width: 0;
        -webkit-transition: width 0.6s;
        -moz-transition: width 0.6s;
        -ms-transition: width 0.6s;
        -o-transition: width 0.6s;
        transition: width 0.6s;
        z-index: -1;
      }
      &:hover {
        color: white;
        &:after {
          left: 0;
          width: 100%;
        }
      }
    }
  }
}

.calendar_selection_fancybox .fancybox-close,
.calendar_selection_fancybox .fancybox-close-small {
  top: -20px !important;
}

/***** booking 0 ****/

.popup_helper_wrapper {
  background-color: #00A5D2 !important;
}

.calendar_popup_wrapper {
  table {
    &.calendar {
      td {
        td .day.restricted-day + .day-content .price {
          top: 28px;
        }
        .day.restricted-day + .day-content .restriction-message {
          padding-top: 1px !important;

        }
        .day.restricted-day + .day-content .price {
          top: 31px !important;
        }
      }
    }
  }
  .calendar_field {
    table.calendar {
      tr:nth-child(1) th {
        background-color: #7CCFF4;
        padding: 10px 0;
        font-family: $font_1;
        font-size: 25px;
      }
      td {
        font-family: $font_1;
        .day-content {
          &.available {
            .price {
              top: 26px;
            }
          }
          &.no-available {
            .not-available-message {
              padding: 10px 0px;

            }
          }
        }
      }
      th {
        border-color: white;
        font-size: 12px;
        padding: 5px 0;
        background-color: #4B4B4B;
        border-top: 4px solid white;
        border-bottom: 4px solid white;
        font-family: $font_1;
        letter-spacing: 3px;
      }
    }
  }
}

#prices-calendar {
  .calendars-section {
    .graphs_fields_wrapper {
      .graphs_field {
        .day_element_option:not(.disabled_day) {
          .graph_block {
            .graph_element {
              background-color: #00A5D2 !important;
            }
          }
        }
      }
    }
  }
}