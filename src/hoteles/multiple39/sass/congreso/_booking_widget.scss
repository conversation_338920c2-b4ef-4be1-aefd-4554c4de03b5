div#full_wrapper_booking {
  width: 100%;
  min-width: 1140px;
  border-top: 1px solid $corporate_5;
  border-bottom: 1px solid $corporate_5;
  padding: 0;
  color: $corporate_2;

  .boking_widget_inline {
    .booking_form {
      width: 1140px;
      height: 90px;
      margin: 0 auto;
      position: relative;

        .special .promocode_input{
            left:inherit !important;
       }
      .entry_date_label,
      .departure_date_label,
      .guest_selector label,
      .children_label,
      .rooms_label,
      .adults_label,
      .promocode_label,
      .promocode_input {
        font-family: $title_family;
        font-weight: 400;
        font-size: 13px;
        letter-spacing: 1.3px;
        line-height: 20px;
        color: $grey !important;
      }

      .entry_date_label,
      .departure_date_label,
      .guest_selector label {
        margin-bottom: -8px;
      }

      .stay_selection .date_day,
      .guest_selector .placeholder_text {
        font-family: $text_family;
        font-weight: 600;
        font-size: 20px !important;
        letter-spacing: 1.2px;
        line-height: 25px;
        color: $grey;
      }

      .stay_selection .departure_date_wrapper,
      .guest_selector {
        &::after {
          content: '';
          display: block;
          @include center_y;
          height: 50px;
          width: 2px;
          background: rgba($corporate_1, 0.5);
        }
      }

      .stay_selection {
        width: 430px !important;
        height: 100%;
        margin-left: 15px;

        .entry_date_wrapper,
        .departure_date_wrapper{
          border-bottom: none;
          height: 100%;
        }

        .entry_date_wrapper {
          padding: 23px 6px;

          &::after {
            content: '\f105';
            font-family: "Font Awesome 5 Pro";
            font-weight: 300;
            font-size: 35px;
            @include center_y;
            right: 30px;
            color: $corporate_1;
            -webkit-font-smoothing: antialiased;
          }
        }

        .departure_date_wrapper {
          padding: 23px 15px 15px 50%;

          &::after {
            right: 15px;
          }

          .date_box {
            background: none;
          }
        }
      }

      .guest_selector {
        width: 300px !important;
        height: 100%;
        padding: 23px 15px;
        margin: 0;

        &::after {
          right: 0px;
        }
      }

      .room_list_wrapper {
        top: 100%;
        width: 375px;
      }

      .wrapper_booking_button {
        width: 410px !important;
        height: 100%;

        .promocode_wrapper {
          width: calc(100% - 235px) !important;
          padding: 25px 10px;

          .promocode_input {
            outline-color: $corporate_1;
          }
        }

        button.submit_button {
          position: relative;
          width: 200px !important;
          height: 64px;
          border-radius: 0;
          margin: calc((90px - 64px) / 2) 0; // 90px - full widget height, 64px - button height
          padding: 0 60px 0 80px;
          font-family: $text_family;
          font-weight: 400;
          font-size: 17px;
          letter-spacing: 0.85px;
          line-height: 22px;
          background: $corporate_5;
          left: -32px;

          &:hover{
            opacity: 1;
          }

          &::before {
            content: '\f0e2';
            font-family: "Font Awesome 5 Pro";
            font-weight: 300;
            font-size: 25px;
            @include center_y;
            left: 30px;
            -webkit-font-smoothing: antialiased;
          }
        }

        .spinner_wrapper {
          width: 200px !important;
          height: 64px;
          border-radius: 10px;
          background-color: $corporate_3;
          top: calc((90px - 64px) / 2);
          right: 32px;
        }
      }
    }
  }
}

div#full_wrapper_booking.has_babies {
  .boking_widget_inline {
    .booking_form {
      .stay_selection {
        width: 340px !important;

        .entry_date_wrapper {
          &::after {
            right: 15px;
          }
        }
      }

      .guest_selector {
        width: 350px !important;
        left:20px;
      }

      .wrapper_booking_button {
        width: 430px !important;
      }
    }
  }
}

#calendar_price_availability {
  z-index: 2;
}

.datepicker_wrapper_element .ui-datepicker .ui-widget-header{
  .ui-datepicker-next, .ui-datepicker-prev{
    &.ui-state-hover, &.ui-datepicker-next-hover, &.ui-datepicker-prev-hover{
      span:before{
        color: white;
    }
    }
  }
}