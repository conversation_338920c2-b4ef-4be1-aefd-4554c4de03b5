$fontawesome5: true;
@import "booking/booking_colors";
@import "booking/booking_all_styles";
@import "plugins/mixins";
@import "plugins/fontawesome5pro";
// @import "toboso/toboso_mixins";

@mixin new_header {

    .site-header {
        border-bottom-color: $color2;
        background-color: $color1;
    }

    .site-header .site-header__ticks {
        margin-top: 25px;
        width: auto;
    }

    .swMain ul.anchor li a.selected {
        background: $color2;

        &:after {
            border-color: $color2 transparent transparent transparent;
        }
    }
    .site-header .site-header__tick-item span {
        vertical-align: middle;
    }
    .site-header__tick-item:nth-of-type(1) .icon-checkmark {
        background-size: 42px !important;
        background: url(/static_1/images/tick1_booking.png) no-repeat center;
    }

    .site-header__tick-item:nth-of-type(2) .icon-checkmark {
        background: url(/static_1/images/tick2.png) no-repeat center;
        background-size: 39px !important;
    }

    .site-header__tick-item:nth-of-type(3) .icon-checkmark {
        background: url(/static_1/images/tick3.png) no-repeat center;
        background-size: 39px !important;
    }

    .site-header__tick-item:last-of-type {
        display: none;
    }

    .site-header .site-header__tick-item {
        width: auto;
        text-align: center;
        margin-right: 20px;
    }

    .site-header .site-header__tick-item span {
        font-size: 30px;
        color: transparent;
    }

    .site-header .site-header__tick-item p {
        color: white;
        top: 0;
        margin: 0;
        font-size: 12px;
        vertical-align: middle;
    }
}


@mixin label_widget_styles() {
    display: inline-block;
    vertical-align: middle;
    box-sizing: border-box;
    font-size: 16px;
    font-weight: 400;
    color: white;
    font-family: 'Montserrat', sans-serif;;
    text-transform: uppercase;
}

@mixin number_widget_styles() {
    font-size: 24px;
    display: block;
    font-weight: normal;
    box-sizing: border-box;
    line-height: 36px;
    color: white;
    font-family: 'Montserrat', sans-serif;;
}

@mixin btn_styles($reservation_button) {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    padding: 12px 40px;
    color: white;
    font-weight: 700;
    cursor: pointer;
    background: $reservation_button;
    font-size: 18px;
    z-index: 1;
    box-sizing: border-box;
    font-family: 'Montserrat', sans-serif;;
    text-transform: uppercase;
    &:hover {
        background: darken($reservation_button, 20%);

        &:before {
            left: 100%;
            top: 100%;
        }
    }
    &:before {
        content: '';
        @include full_size;
        z-index: -1;
        @include transition(all, .6s);
    }
}


.pasarela-sevilla {
  $color1: #A5534B;
  $color2: #7b3e38;

  .site-header {
    border-bottom-color: $color2;
    background-color: $color1;
  }

  .site-header .site-header__ticks {
    margin-top: 10px;
  }

  .swMain ul.anchor li a.selected {
    background: $color2;

    &:after {
      border-color: $color2 transparent transparent transparent;
    }
  }

  .site-header__tick-item:nth-of-type(1) .icon-checkmark {
    background-size: 32px !important;
    background: url(/static_1/images/tick1_booking.png) no-repeat center;
  }

  .site-header__tick-item:nth-of-type(2) .icon-checkmark {
    background: url(/static_1/images/tick2.png) no-repeat center;
    background-size: 29px !important;
  }

  .site-header__tick-item:nth-of-type(3) .icon-checkmark {
    background: url(/static_1/images/tick3.png) no-repeat center;
    background-size: 29px !important;
  }

  .site-header__tick-item:last-of-type {
    display: none;
  }

  .site-header .site-header__tick-item {
    width: auto;
    text-align: center;
    margin-right: 20px;
  }

  .site-header .site-header__tick-item span {
    color: transparent;
  }

  .site-header .site-header__tick-item p {
    color: white
  }
}

.novo-sancti-petri {
  $color1: #67CBCB;

  .swMain ul.anchor li a.selected {
    background: $color1;

    &:after {
      border-top-color: $color1;
    }
  }

  .site-header {
    border-bottom-color: $color1;
  }

  .contDescHabitacion .cabeceraNombreHabitacion, p.popup_title, .booking-2-container-description .booking-2-service-title {
    background-color: $color1;
  }
}

@import url('https://fonts.googleapis.com/css?family=Roboto+Slab:400,700|Oswald:300,400,700&display=swap');
body {
  &.essence-donpaquito {
    $color1: #d4b154;
    $color2: #282828;
    $color3: #A18594;

    &.fr {
      font-size: 13px;
    }

    .site-header {
      height: 160px;
      background: $color2;

      .container_12 {
        position: relative;
        height: 100%;
      }

      .site-header__logo {
        position: absolute;
        top: 50%;
        left: 0;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);

        img {
          vertical-align: middle;
          max-height: none;
          height: 125px;
        }
      }

      .site-header__ticks {
        text-align: justify;
        position: absolute;
        top: 50%;
        right: 80px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        height: 50px;
        margin-top: 0;

        &:after {
          content: "";
          margin-left: 100%;
          display: inline-block;
        }
      }

      .language_header_selector_booking_process {
        border-width: 0;
        width: 70px;

        .selected_language {
          @extend .fa-caret-down;

          &:before {
            @extend .fa;
            float: right;
            font-size: 16px !important;
            line-height: 30px !important;
            color: white;
          }

          i.fa {
            display: inline-block;
            vertical-align: middle;
            font-size: 20px;
            color: $color1;
          }

          .selected_language_code {
            display: inline-block;
            vertical-align: middle;
            width: 20px;
            overflow: hidden;
            color: white;
            letter-spacing: 3px;
            font-family: "Oswald", sans-serif;
            font-size: 20px;
            font-weight: 300;
            line-height: 30px;
            text-transform: uppercase;
          }
        }
      }

      .site-header__tick-item {
        display: inline-block;
        width: 120px;
        float: none;
        margin-right: 0;
        margin-top: 0;

        p {
          display: inline-block;
          vertical-align: middle;
          top: inherit;
          left: inherit;
          font-family: "Oswald", sans-serif;
          font-weight: 300;
          font-size: 10px;
          line-height: 12px;
          letter-spacing: 2px;
          margin-bottom: 0;
          color: white;
          width: 65px;
          text-transform: uppercase;
        }

        .icon-checkmark {
          display: inline-block;
          vertical-align: middle;
          background: none !important;
          color: $color1;
          font-size: 40px;
          width: 35px;
          padding: 0;
        }

        &:nth-of-type(1) {
          .icon-checkmark {
            @extend .icon-booking;

            &:before {
              font-family: "icomoon", sans-serif;
            }
          }
        }

        &:nth-of-type(2) {
          .icon-checkmark {
            @extend .icon-shield;

            &:before {
              font-family: "icomoon", sans-serif;
            }
          }
        }

        &:nth-of-type(3) {
          .icon-checkmark {
            @extend .icon-pig;

            &:before {
              font-family: "icomoon", sans-serif;
            }
          }
        }

        &:last-of-type {
          display: none;
        }
      }
    }

    #full_wrapper_booking {
      padding: 30px 0;
      background-color: #EAEAEA;

      &.booking_engine_wrapper_process {
        #booking.boking_widget_inline {
          .entry_date_wrapper,
          .departure_date_wrapper {
            background: transparent;
            border-bottom-width: 0;
            border-left: 1px solid #CCC;
            @extend .fa-angle-right;

            &:before {
              position: absolute;
              top: 50%;
              -webkit-transform: translateY(-50%);
              -moz-transform: translateY(-50%);
              -ms-transform: translateY(-50%);
              -o-transform: translateY(-50%);
              transform: translateY(-50%);
              right: 20px;
              @extend .fa;
              color: #AAA;
              font-size: 25px !important;
            }

            label {
              display: none;
            }

            .date_box {
              margin-top: 15px;
              background: transparent;

              .date_day {
                text-transform: lowercase;
                letter-spacing: 1px;
                font-family: "Oswald", sans-serif;
                font-weight: 400;
                font-size: 15px !important;
                color: #282828;
              }
            }
          }

          .departure_date_wrapper {
            border-left-width: 0;
            border-right: 1px solid #CCC;
            @extend .icon-specialcalendar;

            &:before {
              color: $color1;
              font-family: "icomoon", sans-serif !important;
            }
          }

          .rooms_number_wrapper {
            display: inline-block;
            vertical-align: top;
            width: 15%;
            float: none;
            background: transparent;
            position: relative;
            @extend .icon-specialbed;
            border-right: 1px solid #CCC;

            &:before {
              position: absolute;
              top: 50%;
              -webkit-transform: translateY(-50%);
              -moz-transform: translateY(-50%);
              -ms-transform: translateY(-50%);
              -o-transform: translateY(-50%);
              transform: translateY(-50%);
              right: 20px;
              font-size: 25px;
              color: $color1;
              font-family: "icomoon", sans-serif;
            }

            label.rooms_label {
              display: none;
            }

            .rooms_number {
              width: 110px !important;
              margin-top: 8px;

              .label {
                font-family: "Oswald", sans-serif;
                font-weight: 400;
                font-size: 15px;
                color: $color2;
                text-transform: lowercase;
              }

              .selectricItems {
                width: 150px !important;
                overflow: auto !important;

                li {
                  font-family: "Oswald", sans-serif;
                  font-weight: 400;
                  font-size: 15px !important;
                  color: $color2;
                  text-transform: lowercase;
                }
              }
            }
          }

          .guest_selector {
            background: transparent;
            border-right: 1px solid #CCC;
            @extend .icon-specialfamily;

            &:before {
              position: absolute;
              top: 50%;
              -webkit-transform: translateY(-50%);
              -moz-transform: translateY(-50%);
              -ms-transform: translateY(-50%);
              -o-transform: translateY(-50%);
              transform: translateY(-50%);
              right: 20px;
              font-size: 25px;
              color: $color1;
              font-family: "icomoon", sans-serif;
            }

            label {
              display: none;
            }

            .placeholder_text {
              margin-top: 15px;
              text-transform: lowercase;
              font-family: "Oswald", sans-serif;
              font-weight: 400;
              font-size: 15px;
              color: #282828;
            }
          }

          .wrapper_booking_button {
            width: 350px;
            float: right;

            .promocode_wrapper {
              .promocode_label {
                color: #999;
                font-size: 0;

                &:before {
                  content: 'PROMOCODE';
                  letter-spacing: 1px;
                  font-size: 12px;
                  line-height: 30px;
                  font-family: "Oswald", sans-serif;
                  font-weight: 300;
                }

                strong {
                  display: none;
                }
              }
            }

            .submit_button {
              padding: 0;
              letter-spacing: 1px;
              font-family: "Roboto Slab", serif;
              font-weight: 300;
              text-align: center;
              background: linear-gradient(to bottom right, darken($color1, 5%), lighten($color1, 10%), darken($color1, 5%));

              &:hover {
                background: linear-gradient(to bottom right, darken($color1, 5%) 25%, lighten($color1, 10%), darken($color1, 5%) 75%);
              }
            }

            .spinner_wrapper {
              width: calc(50% - 1px);
            }
          }

        }
      }
    }

    #wizard {
      .actual_wizard_step {
        .wizard-tab--small {
          width: calc(100% / 3);

          &:nth-child(2) {
            display: none;
          }

          a.booking-step {
            background: $color1;
            letter-spacing: 1px;
            font-size: 16px;
            font-family: "Oswald", sans-serif;
            font-weight: 300;

            &:before,
            &:after {
              border-left-color: $color1;
            }

            &:before {
              border-left-color: white;
              border-width: 22px;
              top: -2px;
            }

            &.disable {
              background: #F5F5F5;
              color: black;

              &:after {
                border-left-color: #F5F5F5;
              }
            }
          }
        }
      }
    }

    div#step-1 {
      background: white;

      table.listadoHabsTarifas td.regimenColumn .tTextoOferta {
        line-height: 12px;
        display: inline-block;
      }

      .booking-box {
        .call_center_wrapper {
          position: fixed;
          right: -210px;
          top: 350px;
          z-index: 100;
          width: 260px;
          bottom: auto;
          border-width: 0;
          @include transition(all, 1s);

          &:hover {
            right: 0;
          }

          .wrapper-new-web-support {
            padding: 0 !important;
            margin: 0 !important;
          }

          .web_support_label_1 {
            width: 100%;
            background: linear-gradient(to right, $color3, $color3 50px, #333 50px, #333);
            border-radius: 10px 0 0 10px;
            color: white;
            margin: 0;
            @extend .icon-headset;

            &:before {
              font-family: "icomoon", sans-serif;
              position: absolute;
              top: 50%;
              left: 3px;
              font-size: 40px;
              -webkit-transform: translateY(-50%);
              -moz-transform: translateY(-50%);
              -ms-transform: translateY(-50%);
              -o-transform: translateY(-50%);
              transform: translateY(-50%);
            }

            .web_support_wrapper {
              padding: 10px 10px 10px 60px;
            }
          }
        }

        .booking-search-results.booking-box__content.has_web_support {
          width: 100% !important;
          background: #F6F6F6;
          box-shadow: 0 5px 10px rgba(0, 0, 0, .15);
          border-width: 0;
          padding: 20px;

          .title_booking_breakdown {
            font-family: "Roboto Slab", serif;
            font-weight: 300;
            letter-spacing: 1px;
            color: black;
            position: relative;
            display: inline-block;
            margin-right: 20px;
            padding-left: 40px;
            @extend .fa-key;

            &:before {
              display: inline-block;
              vertical-align: middle;
              @extend .fa;
              color: $color1;
              font-size: 30px !important;
              position: absolute;
              top: 50%;
              -webkit-transform: translateY(-50%);
              -moz-transform: translateY(-50%);
              -ms-transform: translateY(-50%);
              -o-transform: translateY(-50%);
              transform: translateY(-50%);
              left: 0;
            }

            &:after {
              content: '';
              display: block;
              position: absolute;
              right: 0;
              top: -10px;
              bottom: -10px;
              left: auto;
              width: 1px;
              background: #aaa;
            }
          }

          .booking-search-results__search-data {
            display: inline-block;
            font-family: "Roboto Slab", serif;
            letter-spacing: 1px;
            white-space: nowrap;

            .booking-title-info {
              font-family: "Oswald", sans-serif;
              font-weight: 400;
              color: black;

              &.booking-hotel-name {
                color: $color1;
                font-weight: 300;
                font-family: "Roboto Slab", serif;
                @extend .icon-specialbuilding;

                &:before {
                  font-family: "icomoon", sans-serif;
                  font-size: 18px;
                  margin-right: 5px;
                  margin-left: -10px;
                }
              }
            }

            .booking-3-info {
              color: black;
              text-transform: none;
            }

            .notranslate {
              font-family: "Oswald", sans-serif;
              font-weight: normal;
              color: black;
            }

            .fa-long-arrow-right,
            .fa-long-arrow-left {
              @extend .icon-longarrow;
              color: $color1;
              font-family: "icomoon", sans-serif;
              margin-left: 30px;
              margin-right: 10px;
            }

            .fa-long-arrow-left {
              -webkit-transform: rotate(180deg);
              -moz-transform: rotate(180deg);
              -ms-transform: rotate(180deg);
              -o-transform: rotate(180deg);
              transform: rotate(180deg);
            }
          }

          .booking-search-results__rooms-list {
            display: inline-block;
            margin-left: -150px;
            margin-top: 25px;
            padding-left: 10px;
            font-family: "Oswald", serif;
            letter-spacing: 1px;
            color: black;

            .fa-users {
              display: none;
            }

            .booking-title-info {
              color: black;
              font-weight: 400;
              @extend .icon-specialbed;

              &:before {
                color: $color1;
                font-size: 18px;
                font-family: "icomoon", sans-serif;
                margin-right: 10px;
              }
            }

            .search-item {
              .booking-title-info {
                @extend .icon-specialfamily;
              }
            }
          }

          .booking-search-results__new-search {
            width: 190px;
            height: 0;
            position: relative;

            .booking-button {
              position: absolute;
              top: -5px;
              right: -10px;
              left: auto;
              width: 100%;
              height: 80px;
              border-radius: 0;
              padding-left: 50px;
              background: linear-gradient(to bottom right, darken($color1, 5%), lighten($color1, 10%), darken($color1, 5%));
              text-transform: uppercase;
              font-family: "Roboto Slab", sans-serif;
              @extend .icon-specialcalendar;

              &:before {
                font-family: "icomoon", sans-serif;
                font-size: 20px;
                position: absolute;
                top: 50%;
                left: 10px;
                -webkit-transform: translateY(-50%);
                -moz-transform: translateY(-50%);
                -ms-transform: translateY(-50%);
                -o-transform: translateY(-50%);
                transform: translateY(-50%);
              }

              &:hover {
                background: linear-gradient(to bottom right, darken($color1, 5%) 25%, lighten($color1, 10%), darken($color1, 5%) 75%);
              }
            }
          }
        }
      }

      .hidden_booking_summary {
        background: #F6F6F6;
        box-shadow: 0 5px 10px rgba(0, 0, 0, .15);
        border-width: 0;
        padding: 20px;

        .call_center_wrapper {
          display: none;
        }

        .center_container .booking-search-results.booking-box__content.has_web_support {
          width: 1140px;
        }

        .title_booking_breakdown {
          float: left;
          font-family: "Roboto Slab", serif;
          font-weight: 300;
          letter-spacing: 1px;
          color: black;
          position: relative;
          display: inline-block;
          margin-right: 20px;
          padding-left: 40px;
          width: 160px;
          margin-top: 20px;
          @extend .fa-key;

          &:before {
            display: inline-block;
            vertical-align: middle;
            @extend .fa;
            color: $color1;
            font-size: 30px !important;
            position: absolute;
            top: 50%;
            -webkit-transform: translateY(-50%);
            -moz-transform: translateY(-50%);
            -ms-transform: translateY(-50%);
            -o-transform: translateY(-50%);
            transform: translateY(-50%);
            left: 0;
          }

          &:after {
            content: '';
            display: block;
            position: absolute;
            right: 0;
            top: -10px;
            bottom: -10px;
            left: auto;
            width: 1px;
            background: #aaa;
          }
        }

        .booking-search-results__search-data {
          display: inline-block;
          font-family: "Roboto Slab", serif;
          letter-spacing: 1px;
          white-space: nowrap;

          .booking-title-info {
            font-family: "Oswald", sans-serif;
            font-weight: 400;
            color: black;

            &.booking-hotel-name {
              color: $color1;
              font-weight: 300;
              font-family: "Roboto Slab", serif;
              @extend .icon-specialbuilding;

              &:before {
                font-family: "icomoon", sans-serif;
                font-size: 18px;
                margin-right: 5px;
                margin-left: -10px;
              }
            }
          }

          .booking-3-info {
            color: black;
            text-transform: none;
          }

          .notranslate {
            font-family: "Oswald", sans-serif;
            font-weight: normal;
            color: black;
          }

          .fa-long-arrow-right,
          .fa-long-arrow-left {
            @extend .icon-longarrow;
            color: $color1;
            font-family: "icomoon", sans-serif;
            margin-left: 30px;
            margin-right: 10px;
          }

          .fa-long-arrow-left {
            -webkit-transform: rotate(180deg);
            -moz-transform: rotate(180deg);
            -ms-transform: rotate(180deg);
            -o-transform: rotate(180deg);
            transform: rotate(180deg);
          }
        }

        .booking-search-results__rooms-list {
          display: inline-block;
          margin-left: -200px;
          margin-top: 25px;
          padding-left: 10px;
          font-family: "Oswald", serif;
          letter-spacing: 1px;
          color: black;

          .fa-users {
            display: none;
          }

          .booking-title-info {
            color: black;
            font-weight: 400;
            @extend .icon-specialbed;

            &:before {
              color: $color1;
              font-size: 18px;
              font-family: "icomoon", sans-serif;
              margin-right: 10px;
            }
          }

          .search-item {
            .booking-title-info {
              @extend .icon-specialfamily;
            }
          }
        }

        .booking-search-results__new-search {
          width: 190px;
          height: 0;
          position: relative;

          .booking-button {
            position: absolute;
            top: -5px;
            right: -10px;
            left: auto;
            width: 100%;
            height: 80px;
            border-radius: 0;
            padding-left: 50px;
            background: linear-gradient(to bottom right, darken($color1, 5%), lighten($color1, 10%), darken($color1, 5%));
            text-transform: uppercase;
            font-family: "Roboto Slab", sans-serif;
            @extend .icon-specialcalendar;

            &:before {
              font-family: "icomoon", sans-serif;
              font-size: 20px;
              position: absolute;
              top: 50%;
              left: 10px;
              -webkit-transform: translateY(-50%);
              -moz-transform: translateY(-50%);
              -ms-transform: translateY(-50%);
              -o-transform: translateY(-50%);
              transform: translateY(-50%);
            }
          }
        }
      }

      .contTipoHabitacion {
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);

        .contFotoHabitacion {
          a:before {
            background: linear-gradient(to bottom right, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0), rgba(0, 0, 0, 0));
          }

          .lupa {
            right: auto;
            left: 0;
          }
        }

        .contDescHabitacion {
          .cabeceraNombreHabitacion {
            .tipoHabitacion {
              width: 400px !important;
              font-size: 20px;
              font-family: "Oswald", sans-serif;
              font-weight: 400;
              color: black;
            }
          }

          .very_asked_message, .just_booking_message {
            background: $color3;
            font-weight: 400;
            font-size: 10px;
            letter-spacing: 1px;
            text-transform: uppercase;

            &:after {
              border-left-color: $color3;
            }
          }

          .just_booking_message {
            background: $color1;

            &:after {
              border-left-color: $color1;
            }
          }

          .descripcionHabitacion {
            font-family: "Roboto Slab", serif;
            font-size: 12px;
          }

          .see_more_rooms_v2 {
            font-family: "Roboto Slab", serif;

            .plus_sign {
              color: #999;
              font-weight: 300;
              padding-right: 0;
            }

            .see_more {
              color: #999;
              font-weight: 300;
              text-decoration: none;
              padding-left: 10px;
              text-transform: uppercase;
            }
          }

          .room_services {
            border-width: 2px 0;
            border-style: dashed;
            border-color: #EAEAEA;

            .service_element {
              border-right-width: 2px;
              border-style: dashed;
              border-color: #EAEAEA;

              &:nth-of-type(4) {
                border-right-width: 0;
              }
            }
          }
        }

        table.listadoHabsTarifas {
          td.regimenColumn {
            .regimenColumnContent {
              font-family: "Roboto Slab", serif !important;

              .regimen_name_wrapper {
                font-size: 12px;
              }
            }
          }

          td.precioNocheColumn, td.precioTotalColumn {
            .tPrecioOferta, .precioTachadoDiv {
              font-family: "Roboto Slab", serif !important;
              font-weight: 400;
            }
          }
        }

        .preciosHabitacion {
          .contTitTipoTarifa {
            font-family: "Oswald", sans-serif;
            background: #282828;
            color: white;
            font-weight: 300;
            text-transform: uppercase;
            letter-spacing: 1px;

            .cheapest_rate_message {
              background: $color1;

              &:before {
                border-left-color: #282828;
              }
            }
          }

          tr.listadoHabsTarifas {
            td.regimenColumn .regimenColumnContent * {
              font-family: "Roboto Slab", serif !important;
            }
          }

          .booking-button {
            font-family: "Roboto Slab", serif;
            border: 1px solid black;
            color: black;
            background: transparent;

            &:hover {
              background: $color1;
              border-color: $color1;
              color: white;
            }
          }
        }
      }
    }

    #step-3 {
      .booking-box__content, .booking-box__title {
        font-family: "Roboto Slab", serif;
        font-weight: 400;
      }

      .booking_details_prices_wrapper .booking_details_wrapper .total_booking_wrapper {
        background: $color3;
      }

      .booking-button--confirm-booking {
        background: linear-gradient(to bottom right, darken($color1, 5%), lighten($color1, 10%), darken($color1, 5%));

        &:before {
          background: transparent;
        }

        &:hover {
          background: linear-gradient(to bottom right, darken($color1, 5%) 25%, lighten($color1, 10%), darken($color1, 5%) 95%);
        }
      }
    }

    .site-footer {
      background: $color2;
      color: white;

      #footer_bottom_text p {
        font-family: "Roboto Slab", serif;
        font-size: 14px;
        font-weight: 300;
        letter-spacing: 1px;
        padding: 5px 0;
      }

      a {
        color: white;
      }
    }
  }
}

body.agaro.booking_process_version_1 {
  $font_color: #707173;
  $color1: #AE8B2D;

  #calendar_price_availability{
    z-index: 1;
    position: relative;
  }

  .site-header {
    background: $color1;

    .language_header_selector_booking_process {
      border-color: white;

      .selected_language {
        color: white;
      }
    }

    .site-header__ticks {
      .site-header__tick-item {
        &:first-of-type {
          margin-top: 3px;

          .icon-checkmark {
            background: none;
            padding: 0;

            &:before {
              font-family: "icomoon";
              content: "\e962";
              color: white;
              font-size: 34px;
            }
          }

          p {
            top: -2px;
          }
        }

        &:nth-of-type(2) {
          .icon-checkmark {
            background: none;

            &:before {
              font-family: "FontAwesome";
              content: "\f132";
              color: white;
            }
          }
        }

        &:nth-of-type(3) {
          .icon-checkmark {
            background: none;

            &:before {
              font-family: "icomoon";
              content: "\e913";
              color: white;
            }
          }
        }

        p {
          color: white;
        }
      }
    }
  }

  .datepicker_wrapper_element .header_datepicker, .datepicker_wrapper_element_2 .header_datepicker, .datepicker_wrapper_element_3 .header_datepicker {
    background: $color1;
  }

  .datepicker_wrapper_element .specific_month_selector, .datepicker_wrapper_element .go_back_button, .datepicker_wrapper_element_2 .specific_month_selector, .datepicker_wrapper_element_2 .go_back_button, .datepicker_wrapper_element_3 .specific_month_selector, .datepicker_wrapper_element_3 .go_back_button {
    background: $color1;
    color: white;

    strong {
      color: white;
    }
  }
}

/******* New booking process *******/

@import "booking/booking_process_v1/booking_header";

.toboso-almunecar, .jimesol, .toboso-aparturis {
  $loyalty_corporate: #293C7F;
  $loyalty_corporate2: #615c7d;
//   @include booking_header(white, #293C7F, #585d63, #293C7F, #585d63);
  @import "loyalty_club";
}
.jimesol{
  .contDescHabitacion .cabeceraNombreHabitacion .occupancy_wrapper{
    display: none;
  }
  .banner_room_club{
    bottom: 38px;
  }
}
.toboso-plaza {
  $loyalty_corporate: #ca9339;
  $loyalty_corporate2: #fcd733;
//   @include booking_header(white, $loyalty_corporate, #585d63, $loyalty_corporate, #585d63);
  @import "loyalty_club";
}

body.baltum.booking_process_version_1 {
  $corporate_1: #0e2259;
  $corporate_2: #0e2259;
  $corporate_3: #f5f5f5;
  $corporate_4: #b08401;
  $corporate_5: #0e2259;
  $corporate_6: #c23329;
  $corporate_7: #242f6f;
  $gold: #b08401;

  $red: $gold;
  $black: #00142F;
  $grey: #383838;

  $title_family: "Montserrat", sans-serif;
  $text_family: "Montserrat", sans-serif;
  $extra_family: "Montserrat", sans-serif;

  color: $grey;
  font-family: $text_family;

  @import "baltum_booking_styles/baltum_booking_process";
  div#step-1 .contTipoHabitacion .preciosHabitacion .listadoHabsTarifas .regimenColumn .tTextoOferta {
    line-height: 1.4!important;
  }
  #reservation {
    button.booking-button--confirmed-booking {
      display: none !important;
    }
    .footer_buttons_extra_functionality{
      .booking-button{
        background: $corporate_7;
        &:first-letter{
          text-transform: uppercase;
        }
      }
    }
  }

  .cards_banners_wrapper .card_element_wrapper, .cards_extended_wrapper .card_element_wrapper {
    border-radius: 0px;
  }

  #step-3 .payment_type_grid #expiryMonth {
    margin-left: 0px;
  }
  .ui-tooltip {
    opacity: 1;
  }

}

//.don-paco-malaga {
//  $brown1: #BEA77D;
//  $brown2: #8e7547;
//  $brown3: #8E8371;
//  $grey: #ababab;
//  @include booking_header(white, $brown1, $grey, $brown2, $brown1);
//
//  .booking_engine_wrapper_process {
//    #booking.boking_widget_inline {
//      .submit_button {
//        background: $brown1;
//      }
//    }
//  }
//
//  .booking-search-results {
//    .booking-search-results__search-data {
//      .booking-3-info {
//        position: absolute;
//        margin-left: 10px;
//      }
//    }
//
//    .booking-search-results__new-search {
//      float: none;
//    }
//
//    .booking-search-results__rooms-list {
//      padding-top: 20px;
//
//      i.fa {
//        top: 60%;
//      }
//    }
//  }
//}

body.booking_process_version_1.don-paco-malaga {
  $corporate_1: #FAF6F0;
  $corporate_2: #414141;
  $black: $corporate_2;
  $lightgrey: #F4F4F4;
  $grey: $corporate_1;
  $grey2: #707070;
  $color_text: #4d4d4d;
  $highlight_rate: $corporate_1;
  $background_blue: #BEA77D;
  $green: #255351;
  $light_green: #306B68;
  $light_brown: #D5C4BB;
  $light_blue: #4E817E;
  $hover_button: #8E7547;
  $black: #3D3D3C;


  $title_family: 'Open Sans', sans-serif;
  $text_family: 'Open Sans', sans-serif;


  @include booking_header_v2($lightgrey, $corporate_2, $corporate_1, $corporate_1, $highlight_rate);
  @import "booking_don-paco-malaga/don-paco-malaga-booking";

  .wrapper_booking_button .submit_button, div#step-1 #rooms_b1_wrapper .booking-button, #step-3 .booking-button--confirm-booking {
    color: $corporate_1 !important;
  }

  .site-main div#full_wrapper_booking #booking.boking_widget_inline .special .promocode_input {
    color: white;
  }

  .site-footer {
    background-color: $corporate_1;
    color: white;
  }

  .site-main {
    #full_wrapper_booking {
      .boking_widget_inline {
        .booking_form {
          .submit_button {
            &:hover {
              background: $hover_button;
            }
          }
        }
      }
    }
  }


  div#step-1 {
    .clearfix {
      .booking-search-results {
        .booking-search-results__new-search {
          .booking-button {
            border-color: $background_blue !important;
          }
        }
      }

      .booking-box--search {
        .booking-search-results {
          .title_booking_breakdown {
            &:before {
              color: $background_blue;
            }
          }
        }
      }
    }

    #rooms_b1_wrapper {
      .contTipoHabitacion {
        .contFotoDescripcion {
          .contDescHabitacion {
            .room_description_name_wrapper {
              .descripcionHabitacion {
                font-weight: 400 !important;
                height: 55px !important;
              }
            }
          }
        }

        .preciosHabitacion {
          .listadoHabsTarifas {
            .regimen_tr_element {
              .botonReservarColumn {
                .booking-button:hover {
                  background: $hover_button !important;
                }
              }
            }
          }
        }
      }
    }
  }

  div#step-3 {
    .booking-button--confirm-booking {
      &:hover {
        background: $hover_button !important;
      }
    }
  }

  div#step-1, div#step-2 {
    .hidden_booking_summary {
      .booking-button {
        &.booking-button--action {
          border: 1px solid $background_blue;
        }
      }
    }
  }

  .additional_services_total_wrapper .perform_additional_services_booking {
    &:hover {
      background: $hover_button !important;
    }
  }

  .all_additional_services_wrapper {
    .quantity_supplement {
      padding: 6px 31px 6px 10px!important;
    }
  }
}

//.zeus-malaga {
//  $color1: #4b698c;
//  $color2: #27374a;
//  $color3: #8E8371;
//  $grey: #ababab;
//  @include booking_header(white, $color1, $grey, $color1, $color2);
//
//  .booking_engine_wrapper_process {
//    background: $grey;
//
//    #booking.boking_widget_inline {
//      .submit_button {
//        background: $color1;
//      }
//    }
//  }
//
//  .booking-search-results {
//    .booking-search-results__new-search {
//      float: none;
//    }
//
//    .booking-search-results__rooms-list {
//      padding-top: 20px;
//
//      i.fa {
//        top: 60%;
//      }
//    }
//  }
//}

body.zeus-malaga.booking_process_version_1 {

  $corporate-1: #F5F9FF;
  $corporate_2: #2A3748;
  $black: #444444;
  $grey: #F0F0F0;
  $background_blue: #516889;
  $green: #255351;
  $light_green: #306B68;
  $light_brown: #D5C4BB;
  $light_blue: #5ECCBB;
  $title_family: 'Open Sans', serif;
  $text_family: 'Open Sans', sans-serif;

  font-family: $text_family;
  @import "zeus-booking-process/zeus-booking";
  @include booking_header_v2(white, $corporate_2, $corporate_1, $corporate_1, $background_blue);

  @media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    overflow-x: hidden;
    div#step-1{
      #rooms_b1_wrapper{
        position: relative;
        left: 20px;
      }
      .clearfix{
        .booking-search-results{
          .booking-search-results__new-search{
            width: 245px!important;
            &::before{
              top: 45%!important;
              left: 20px!important;
            }
          }
        }
      }
    }
    .additional_services_total_wrapper{
      left: -35px;
    }
    .site-header .language_header_selector_booking_process{
      position: absolute;
      right: 20px;
    }
    .fancyboxContent_second_v .room_popup_individual_element{
      height: auto!important;
    }
  }


}

body.secure-pepemesa {
  @include booking_header(white, #3c6ba7, #5a5a5a, #3c6ba7, white);

  .submit_button {
    background: #930c35 !important;
  }
}

body.playa-catedrales {
  @include booking_header(white, #4F8ABD, #5a5a5a, #4F8ABD, white);

  .submit_button {
    background: #19C8CC !important;
  }
}

body.toboso-corporativa {

  $text_family: "Merriweather", Serif;
  $title_family: "Merriweather", Serif;
  $font-family: $text_family;
  // corporative colors definitions
  $corporate_1: #293C7F;
  $corporate_2: #293C7F;
  $corporate_3: #585d63;
  $corporate_4: #625c7d;
  $black: black;
  $grey: #F5F5F5;
  $grey_2: #C2C2C2;
  $red: #C61C34;
  $font_1: $text_family;


  //@import "booking/booking_process_v1/booking_header";

  $black: #343434;
  $corporate_rgba: rgba(245, 238, 228, .7);
  $white_rgba: rgba(255, 255, 255, .7);
  $grey: #F5F5F5;
  $grey_2: #C2C2C2;
  $red: #C61C34;

  @import "calendar_disco_styles";

  @include booking_header(white, #989898, #989898, $corporate_1, white);

  @import "site-header_styles";
  @import "booking_widget_styles";
  @import "booking0";

  #step-3 .booking_details_prices_wrapper .booking_info_element.pet_element {
    color: $corporate_1;
  }

  .clearfix {
    padding-bottom: 20px;
  }

  .actual_wizard_step .wizard-tab--small, actual_wizard_step .wizard-tab--big {
    font-family: $text_family;

    a {
      background-color: $corporate_2;
      letter-spacing: 1px;

      &:before, &:after {
        border-left-color: $corporate_3;
      }

      &.disable {
        background-color: $grey;
        color: $black;

        &:before, &:after {
          border-left-color: $grey;
        }
      }
    }
  }


  .actual_wizard_step {
    li.wizard-tab--small a,
    li.wizard-tab--big a {
      border-left: 2px solid white;

      &:not(.disable) {
        &:before {
          display: block;
          border-left-color: white;
          left: calc(100% + 2px);
        }
      }

      &.disable {
        &:before {
          display: block;
          border-left-color: white;
          left: calc(100% + 2px);
        }
      }


    }

    .wizard-tab--small a, .wizard-tab--big a {
      text-decoration: none;
      text-transform: uppercase;
      position: relative;
      display: block;
      text-align: left;
      padding-top: 11px;
      padding-bottom: 10px;
      font-size: 13px;
      height: auto;
      cursor: default;
      color: #fff;
      background: $corporate_3;
      font-weight: 500;
      padding-left: 11px;
    }
  }

  .wizard-tab--small a {
    &:after, &:before {
      position: absolute;
      left: 100%;
      margin: 0 auto;
      right: 0;
      bottom: 0;
      content: "";
      z-index: 9;
      width: 0;
      height: 0;
      border-left: 19px solid #707173;
      border-right: 19px solid transparent;
      border-bottom: 20px solid transparent;
      border-top: 19px solid transparent;
    }

    //&:before {
    //  left: 0;
    //  right: auto;
    //  border-left: 23px solid white;
    //  z-index: 5;
    //  top: 0;
    //  height: auto;
    //  bottom: 0;
    //}
  }

  .selection_price_wrapper {
    .total_price_label {
      margin-bottom: 6px !important;
      font-family: $text_family;
    }

    .total_price_value {
      &.currencyValue {
        font-family: $text_family;
        font-size: 30px;
        font-weight: normal;
      }
    }

    .monedaConv {
      font-family: $text_family;;
      font-size: 30px;
      font-weight: normal;
    }
  }

  @import "resume_styles";

  .no_availability_message_booking0 {
    font-family: $text_family;
    background: $corporate_2;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  }

  @import "rooms_styles";
  @import "additional_services_styles";

  div#step-1 {
    background-color: white;
    font-family: $text_family;
  }

  div#step-1 .precioNocheColumn .precioTachadoDiv {
    color: black;
  }

  div#step-1 {
    table.listadoHabsTarifas {
      td.precioTotalColumn,
      td.precioNocheColumn,
      td.regimenColumn {
        .tTextoOferta,
        .tPrecioOferta,
        .tPrecioOferta {
          color: $red;
        }
      }

      td.precioTotalColumn,
      td.precioNocheColumn {
        color: black;
      }
    }
  }

  div#step-1 {
    .contTipoHabitacion {
      .descripcionHabitacion {
        .desc {
          strong {
            font-weight: 700;
          }
        }
      }

      .contFotoHabitacion .occupancy {
        b {
          text-transform: uppercase;
        }
      }
    }

    .precioTotalColumn .priceValues .promotion_percentage_square {
      background-color: $red;

      .promotion_discount {
        right: 120%;
        left: auto;
        background: $corporate_2;
      }
    }
  }

  .rates_conditions_popup {
    .rate_description_content {
      overflow: visible;
      font-size: 14px;
      font-weight: 400 !important;
      max-height: 350px;
      padding: 15px 15px 0 15px;

      strong {
        font-weight: 700;
      }

      div {
        margin: 0;
      }

      ul {
        margin: 7px 0;

        li {
          list-style-type: disc;
          margin-left: 30px;
        }
      }
    }
  }


  #step-2 {
    .booking-3-info, .booking-3-info--title, .booking-3-info--price {
      color: $corporate_1;
    }

    .booking_button_wrapper .booking-button {
      background: $corporate_2;
    }
  }

  #step-3 {
    background: white;

    .personal_details_payment_wrapper,
    .booking_details_prices_wrapper {
      display: table-cell;
      min-height: 500px;
      position: relative;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
    }

    .booking-button {
      font-family: $text_family;
      background-color: $corporate_2;

      &:before {
        background-color: $corporate_1;
      }
    }
  }

  .site-footer {
    background: $black;
    color: white;

    #footer_bottom_text p {
      font-size: 15px;
      font-weight: 300;
      letter-spacing: 1px;
      margin-bottom: 20px;
    }
  }

  .fancybox-wrap.fancy-booking-search_v2 {
    .container_popup_booking {
      border: 1px solid $corporate_1;
    }

    .gif_wrapper .default_line_loading {
      background-color: $corporate_1;
    }
  }

  @import "club_styles";

  .cards_banners_wrapper.to_right, .cards_extended_wrapper.to_right {
    right: 60px !important;
  }


  #step-0 .booking-0-hotel-item .booking_picture img {
    width: auto;
    max-height: 350px !important;
  }

  .booking_0_buttons_controller {
    margin-top: 25px !important;
  }
}

body.toboso-almunecar, body.jimesol, body.toboso-plaza, body.toboso-aparturis {
//    $corporate_1: #9cc5c9;
//    $corporate_2: #c9ab44;
//    @import "toboso/toboso_booking_styles";
//    @include onhotel_mixin($corporate_1, $corporate_2);

  $corporate_1: #293C7F;
  $corporate_2: #3C54A8;
  $corporate_3: #fcd8c7;
  $corporate_1_hover: #3C54A8;
  $offer: #d9ac59;
  $promotion: #3C54A8;
  $grey: #696969;
  $grey-1: #F5F5F5;

  $loyalty_corporate: #293C7F;
  $loyalty_corporate2: #615c7d;

  $black: #585D63;
  $lightgrey: #F5F5F5;
  $red: #EC6363;

  $font_1: 'Poppins', sans-serif;
  $font_2: $font_1;
  $fa6: "Font Awesome 6 Pro";


  $title_family: $font_1;
  $text_family: $font_2;

  font-family: $text_family;


  @import "toboso_new/_toboso-booking";
//   @import "club_styles";

  @media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    div#step-1 {
      #rooms_b1_wrapper {
        .contTipoHabitacion {
          .contFotoDescripcion {
            .contDescHabitacion {
              width: 730px;
            }
          }
        }
      }
    }
  }
  .modal_wrapper .modal_content:before {
     border: 0;
  }
}

@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,300;0,400;0,700;1,300;1,400;1,700&display=swap');

body.agaro.booking_process_version_1 {
  $corporate_1: #A4791E;
  $corporate_2: #75654C;
  $corporate_3: #F3DD92;
  $corporate_4: #FFF1C2;

  $white: #FFFFFF;
  $red: #DB5A42;
  $green: #35CE8D;
  $black: #172121;
  $grey-1: #EAEAEA;
  $grey-2: #141414;
  $grey-3: #333333;
  $grey-4: #1D1D1B;

  $title_family: 'Lato', sans-serif;
  $text_family: 'Lato', sans-serif;
  $font_1: 'Lato', sans-serif;

  font-family: $text_family;
  color: $black;

  @import "agaro-booking/agaro_booking_process";
  @import "agaro-booking/site-header";
  @import "agaro-booking/booking_widget";
  @import "agaro-booking/actual_wizard_step";
  @import "agaro-booking/booking_summary";
  @import "agaro-booking/rooms_packages_selectors";
  @import "agaro-booking/rooms";
  @import "agaro-booking/packages";
  @import "agaro-booking/site-footer";
  @import "agaro-booking/personal_data";
  /*@import "agaro-booking/suplements";*/
  @import "agaro-booking/booking0";

  @include booking_header_v2(white, $corporate_1, $grey-2, white, $red);

  #rooms_b1_wrapper, #packages_b1_wrapper {
    position: relative;
    background: transparent;
    z-index: 0;

    &::before {
      content: "";
      position: absolute;
      background-image: url(https://storage.googleapis.com/cdn.paraty.es/test4-copia11/files/fondo_booking.png);
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
      top: -190px;
      left: 0;
      right: 0;
      bottom: -60px;
      z-index: -1;
      opacity: .5;
    }
  }

  #packages_b1_wrapper {
    &::before {
      left: -220px !important;
    }
  }
}



