header {
  padding: 5px 15px;

  .phone_mobile_dropdown {
    display: none;
  }

  .logo_wrapper {
    max-width: calc(100% - 80px);

    img {
      max-height: initial;
      max-width: calc(100% - 80px);
    }
  }
}

.loading_animation_popup {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.8) !important;

  .spincircle {
    height: 260px;

    img {
      transform: translate(-50%, -85%) !important;
      max-width: 200px !important;
    }

    .spinner_animation {
      position: relative;
      width: $loader-width;
      height: $loader-size;
      margin: 40px auto;
      transform: translateX(-25%);
      top: calc(100% - 95px) !important;

      .circle1, .circle2, .circle3, .circle4 {
        display: inline-block;
        position: absolute;
        top: 0;
        left: 0;
        width: $loader-size;
        height: $loader-size;
        background: $loader-color !important;
        border-radius: 50%;
        animation: loading 4s infinite;
        transform: scale(.1);
        transform-origin: center left;
      }

      .circle1 {
        animation-delay: 0s !important;
      }

      .circle2 {
        animation-delay: 0.4s !important;
      }

      .circle3 {
        animation-delay: 0.8s !important;
      }

      .circle4 {
        animation-delay: 1.2s !important;
      }
    }
  }

  div {
    color: $black !important;
  }
}

#login_wrapper_element.version_banner_v1.v5, #logged_user_info_wrapper.version_banner_v1 {
  background: transparent;
  border: 1px solid $black;

  .content_login_wrapper {
    height: 70px;
  }

  .content_logged_wrapper {
    background: transparent;

    .logged_user_text, .user_points .content_wrapper {
      color: #383838;
    }
  }
}

.register_information_wrapper .tabs_wrapper .register_tab.active,
.register_information_wrapper .tabs_wrapper .login_tab.active,
#register_form_wrapper_v1.v5 .title_wrapper_block .main_form_title,
#login_form_wrapper_v1.v5 .title_wrapper_block .main_form_title {
  color: white;
}

.club_send_password_wrapper {
  margin: 40px auto;
}

#register_form_wrapper_v1.v5 {
  .lopd_wrapper, .promotions_checkbox {
    label.error {
      margin-right: 10px;
    }
  }
}

.step_item {
  &::before {
    left: calc(-50% + 9px) !important;
    width: calc(100% - 17px) !important;
  }

  &::after {
    background-color: white !important;
  }

  &.check {
    &::after {
      background-color: $corporate_2 !important;
      border: 2px solid $corporate_2 !important;
    }

    &::before {
      background-color: $corporate-2 !important;
    }
  }
}

.step_item.active {
  &::after {
    border: 2px solid $corporate_2 !important;
    background-color: transparent !important;
  }

  &::before {
    background-color: $corporate-2 !important;
  }
}

.booking_step_sentence {
  font-weight: 500 !important;
  font-size: 16px;
  line-height: 33.2px;
}

.content_title {
  .title {
    font-size: 15px !important;
    text-transform: none;
  }
}

.dates {
  p {
    font-size: 14px;
    line-height: 26px;
    font-weight: 400 !important;
  }
}

.search_text {
  p {
    font-size: 14px;
    line-height: 26px;
  }
}

.double_button_wrapper {
  padding: 10px;
  justify-content: space-between;

  .modify_search, .show_calendar, .back_button {
    font-family: $text_family;
    color: white;
    border: 1px solid $corporate_2;
    width: 48.5%;
    padding: 0 12px 0 50px;
    text-align: left;
    border-radius: 10px;
  }

  .modify_search {
    text-align: center;
    color: $corporate-2;
  }

  .show_calendar, .back_button {
    text-align: left;
    background: transparent linear-gradient(90deg, #456ba7 0%, #0088cc 100%) 0 0 no-repeat padding-box;
  }
}

.continue_booking,
.add_service,
.back_booking {
  border-radius: 10px !important;
}

.room_option_block {
  .choose_room_label {
    background: $black !important;
  }

  .has_modal {
    .previously_selected {
      background-color: $corporate-3 !important;
    }

    .room_name {
      .title {
        font-size: 16px !important;
        text-transform: uppercase;
      }

      .info_btn {
        font-weight: 400 !important;
        font-size: 13px !important;
        line-height: 16px !important;
      }
    }
  }

  .very_asked {
    background-color: white !important;
  }

  .rate_selected_title {
    font-size: 14px !important;
    font-weight: 400;

    span {
      font-family: $text_family;
      font-size: 11px !important;
      padding-bottom: 1px;
    }

    .modal_launch {
      color: #333333 !important;
      opacity: .8;
      font-weight: 400;
      font-size: 13px !important;
      line-height: 16px;
    }
  }

  .last_day_cancellation_text {
    color: $green !important;
  }

  .room_rates_list {
    .rates_details_wrapper {
      .regime_item_content {
        .regime_description {
          .regime_title {
            font-size: 15px !important;
            margin-bottom: 4px;
          }

          .regime_offer_detail {
            color: $corporate_3;
          }

          .lock_board_wrapper {
            border: 1px solid;
            padding: 10px !important;
            border-radius: 6px !important;
            position: relative;

            img {
              float: right;
              margin-left: 10px;
              margin-right: 0 !important;
            }

            span.currencyValue {
              font-size: 18px;
              letter-spacing: 0.99px;
              line-height: 24px;
            }

            .lock_ico {
              position: absolute;
              margin-left: 0 !important;
              top: -10px;
              left: 10px;
              background: white;
              padding: 0 5px;
              font-size: 10px !important;

              &::after {
                content: "Desbloquear";
                font-family: $font-1;
                font-size: 9px;
                line-height: 22px;
                margin-left: 5px;
              }
            }
          }
        }
      }
    }
  }

  .discount_percentage {
    background-color: $corporate-3 !important;
  }

  .final_price {
    color: $black !important;
    font-size: 25px !important;
    margin-top: 5px;
  }

  .submit {
    left: -20px !important;

    span {
      background: linear-gradient(76deg, $corporate_2, $corporate_3) !important;
      border-radius: 8px !important;
      width: 135px !important;
      font-size: 16px !important;
      letter-spacing: 0.5px;
    }
  }
}

.back_button, .bottom_modify_search {
  text-align: center !important;
}

footer {
  margin-top: 20px;
}

footer > div:first-child {
  display: none !important;
}

.main_content_wrapper {
  &.step_0 {
    .tabs_wrapper {
      .tabs {
        li {
          background-color: transparent;
          border: 1px solid $lightgrey;
          border-bottom-color: $corporate_2;
          border-radius: 15px 15px 0 0;
          margin: 0 6px 5px;
          border: none;

          .tab_btn {
            height: 35px;
            background-color: transparent;
            font-weight: 300;
            font-size: 15px;
            letter-spacing: 0.75px;
            color: $grey-2;
            border: none;
            font-weight: 500;
            border-radius: 15px 15px 0 0;

            &::before {
              display: none;
            }

            &.rooms, &.packages, &.club {
              &::before {
                background-image: url(https://storage.googleapis.com/cdn.paraty.es/demo-fitur/files/pest-left_blue_mobile.svg);
                background-repeat: no-repeat;
                background-size: contain;
                content: "";
                display: block;
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                width: 95vw;
                height: 40px;
                opacity: 0;
                pointer-events: none;
              }

              &.active {
                &:before {
                  opacity: 1;
                }
              }
            }

            &.club {
              .tab_text {
                font-size: inherit;
              }
            }

            &.packages {
              &::before {
                background-image: url(https://storage.googleapis.com/cdn.paraty.es/demo-fitur/files/pest-right_blue_mobile.svg);
                transform: translateX(-51%);
              }
            }

            .tab_text {
              display: block;
            }
          }
        }

        li.active {
          background-color: white;
          border-color: $corporate_2;

          .tab_btn {
            background-color: transparent;
            color: $corporate_2;

            .tab_text {
              &:before {
                display: none;
              }
            }
          }
        }

        li + li {
          &::before {
            bottom: auto;
            top: 100%;
            display: none;
            background-color: $corporate_2;
          }
        }
      }

      .tabs:not(.triple) {
        li, li.active {
          width: 48%;
        }
      }
    }
  }

  &.step_1 {
    .additional_services .additional_services_wrapper .counter_box {
      .control {
        border-color: $black;

        &.quantity {
          color: $black;
        }

        &.add {
          background-color: $black;
        }

        &.subtract {
          &:before {
            color: $black;
          }
        }
      }
    }

    .additional_services {
      .perform_additional_services_booking {
        background: transparent linear-gradient(90deg, #456ba7 0%, #0088cc 100%) 0 0 no-repeat padding-box;
      }
    }

    .all_additional_services_wrapper.with_tabs_scroll {
      .additional_services_tab {
        &.active {
          border: 2px solid $corporate-2;

          .main_title {
            color: $corporate-2;
          }
        }
      }

      .category_wrapper.open {
        .additional_services_wrapper {
          .additional_service_element {
            .price_tag {
              background: transparent linear-gradient(90deg, #456ba7 0%, #0088cc 100%) 0 0 no-repeat padding-box;
            }

            .supplement_complete_content {
              .service_selection_wrapper {
                .quantity_button_minus, .quantity_button_plus {
                  background: #0088cc;
                }

                .add_service_button {
                  background: transparent linear-gradient(90deg, #456ba7 0%, #0088cc 100%) 0 0 no-repeat padding-box;
                }
              }
            }
          }
        }
      }
    }
  }


}

.step_2 {
  .lock_rates_wrapper {
    .lock_price {
      .price_bullet {
        background: #456ba7 !important;
      }
    }
  }

  .conditions {
    color: #333333 !important;
    opacity: .8;
  }

  .exchange_message {
    padding-left: 15px;
  }

  .personal_details_form_wrapper .personal_details_form .inputs_wrapper select {
    background-image: url(https://storage.googleapis.com/cdn.paraty.es/parkroyal-corpo/files/arrow_down.svg)
  }

  .worldline_payment_form_container .payment_option {
    &.selected-option {
      background-color: #FDF8FC !important;
      border-color: #9A1F6E !important;

      .msi_payment_select_wrapper .installments_quantity_label {
        background: #FDF8FC !important;
      }
    }

    .payment_option_description {
      padding-top: 5px;
      margin-top: 5px;
    }
  }

  .price {
    color: #172121 !important;

    .accomodation_tax {
      .accomodation_tax_value {
        font-size: 14px;
      }
    }
  }

  input {
    font-family: $text_family;
  }

  #btn-finish-booking {
    background: transparent linear-gradient(90deg, #456ba7 0%, #0088cc 100%) 0 0 no-repeat padding-box;
    font-family: $font-1 !important;
  }

  .tax_inc_wrapper_info {
    width: 100%;
    margin: 0 0 30px;
    padding: 20px 15px;
    background-color: #788995;
    color: white;
    line-height: 1.1;

    .monedaConv,
    .value_elem {
      color: inherit;
    }
  }

  .lock_rates_wrapper {
    background: transparent;
    border: 1px solid $black;

    .lock_content {
      color: $black;
      width: calc(100% - 150px);
    }

    .lock_price {
      .price_bullet {
        background: $corporate_1;
      }
    }
  }

  .personal_details_form_wrapper {
    .personal_details_form {
      .inputs_wrapper {
        background: white;

        .summary_section_title {
          color: $black;
        }

        .input_element.with_label {
          input, select {
            border: 1px solid #c5c5c5;
          }

          label {
            color: $black;
            background: white !important;
          }
        }
      }
    }
  }
}

.body_modal_content {
  .owl-item {
    &.selected {
      border-bottom-color: $corporate-1 !important;
    }
  }

  .modal_container.room_content {
    padding: 0 15px 35px 15px !important;
    height: calc(100% - 360px) !important;

    .room_description {
      ul {
        padding-left: 20px;
        margin-top: 20px;
      }
    }

    .icons_room .room_services .service_element {
      margin-bottom: 20px;
    }

    .service_element {
      i {
        font-size: 35px !important;
        color: $corporate-1 !important;
      }
    }
  }
}

.additional_services {
  .add_service {
    background-color: $black !important;
  }
}

.continue_booking {
  background-color: $corporate-2 !important;
  font-family: $font-1 !important;
}

.booking_widget_wrapper {
  .input_wrapper {
    &::before {
      content: '' !important;
      background-color: $corporate_2;
    }
  }

  .double_button_wrapper {
    padding: 10px;
    justify-content: space-between;

    .close_button {
      background-color: white;
      color: #333;
    }
    .re-search_button{
      background: linear-gradient(76deg, $corporate_2, $corporate_3);
    }
  }

  #entry_date_popup, #departure_date_popup{
      .header_wrapper .banner_title i, .ui-datepicker-title{
        color: $corporate_2;
      }
    }

  #entry_date_popup {
    .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before,
    .booking_widget_wrapper #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before,
    .booking_widget_wrapper #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before,
    .booking_widget_wrapper #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before {
      background-color: $corporate_2;
    }
  }
}


#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.not-available-day,
#calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.not-available-day {
  background-color: $corporate_2;
}

#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr:nth-child(1) th,
#calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr:nth-child(1) th {
  text-align: center;
}

#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper ~ .button, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper ~ .button {
  background-color: $corporate_2;
}

#main_modal {
  .body_modal {
    .body_modal_content {
      .modal_info {
        .modal_container {
          .content_title {
            .title {
              font-size: 20px !important;
            }
          }

          .room_description {
            font-size: 16px;

            .list ul li {
              position: relative;

              &:before {
                content: "\ea06";
                position: absolute;
                left: -22px;
                font-family: "icomoon", sans-serif;
                margin-right: 10px;
                color: $corporate_2;
              }
            }
          }
        }
      }
    }
  }
}

.banner_external_club.mobile.v1 {
  border-color: $grey-3;
  border-radius: 16px;
  font-family: $font_2;

  .external_club_buttons {
    .btn_logout {
      color: $grey-3;
    }

    .btn_profile_link {
      border-color: $corporate_1;
      font-family: $font_1;
      color: $corporate_1;

      &:hover {
        background-color: $corporate_1;
        color: white;
      }
    }
  }
}

.cards_banners_wrapper {
  z-index: 20;

  .card_element_grid.gotrip_club_link {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 3px 6px rgba(black, 0.15);

    .close_card_button {
      top: 0;
    }
  }
}

&.gotrip_user_logged {
  .cards_banners_wrapper {
    .card_element_grid.gotrip_club_link {
      display: none !important;
    }
  }
}

.main_wrapper .room_option_block .room_pack_option .tour_button_wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: transparent;
  width: 55px;
  height: 55px;
  border-radius: 50%;
  background: rgba(56, 56, 56, 0.5);
  top: 120px;

  &:after {
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    speak: none;
    font-family: 'icomoon' !important;
    content: "\ea3c";
    color: white;
    font-size: 30px;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
  }
}

.club_send_password_wrapper {
  .input_wrapper {
    input {
      border: 1px solid #b4b4b4 !important;
    }
  }
}

.modal_wrapper {
  background-color: rgba(black, 0.3);

  .modal_content {
    background-color: white;
  }
}

.modal_wrapper.recover_password_popup {
  z-index: 10002;
  opacity: 1;
  background: rgba($black, .8);

  .modal_content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
    flex-wrap: wrap;
    position: relative;
    padding: 0 15px;
    background-color: rgba(black, 0.3);
    color: white;
    font-weight: 600;
    font-size: 15px;
    text-align: center;

    .modal_content_close {
      position: absolute;
      right: 23px;
      top: 30px;
      width: 23px;
      height: 23px;
      cursor: pointer;

      &:before, &:after {
        content: "";
        width: 100%;
        height: 2px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(45deg);
        background-color: white;
      }

      &:after {
        transform: translate(-50%, -50%) rotate(-45deg);
      }
    }
  }
}

#main_modal.active {
  .body_modal.regime_conditions_modal_wrapper, .body_modal.iframe_modal_wrapper {
    .body_modal_content iframe {
      height: 69vh;
    }
  }
}

#logged_user_info_wrapper {
  margin: 20px 10px 30px;
  box-sizing: border-box;
  width: calc(100% - 20px);
  border-radius: 17px;
  border-color: var(--club-main-color);

  .user_points {
    padding-top: 0 !important;

    .content_wrapper {
      margin-top: 0 !important;
    }
  }
}

#login_wrapper_element {
  margin: 20px 10px 30px;
  box-sizing: border-box;
  width: calc(100% - 20px);
  border-radius: 17px;
  border-color: var(--club-main-color);

  .already_member_wrapper {
    border: 1px solid var(--club-main-color);
  }

  .already_member_wrapper, .join_button_wrapper {
    border-radius: 6px !important;
  }

  .content_login_wrapper {
    justify-content: space-around;

    .icon_image_element {
      max-height: 50px;
    }
  }
}

#popup_login_information {
  .login_tab.active, .register_tab.active {
    color: white;
  }
}

.category_wrapper:not(.upgrade){
    .additional_service_element{
      &.active{
        .preview_wrapper{
          height: 120px;
          img{
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
  }

.category_wrapper:not(.upgrade){
    .additional_service_element{
      &.active{
        .preview_wrapper{
          height: 120px;
          img{
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
  }

  #booking1-app-root .rooms-list.is-mobile .board-list-wrapper .board-element-wrapper .buttons-wrapper .book-button {
    background: transparent linear-gradient(76deg,#456ba7 35%,#81a7e3 100%) 0 0 no-repeat padding-box;
  }

  .room-element-wrapper .room-info-wrapper .room-content .top-room .room-name {
    color: $corporate_7;
  }