import copy
import datetime
import json
import logging

import requests
from flask import request
from flask.views import MethodView

from booking_process.constants.advance_configs_names import DEPARTURE_CLUB_PROMOCODE, MEMBERS_CLUB_NAMESPACE, \
	CLUB_POINTS_NOT_LOGGED
from booking_process.libs.communication import directData<PERSON>rovider
from booking_process.utils.clubs.club_methods import get_loyalty_seeker_url
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.data_management.users_utils import get_user_by_id, get_user_by_email
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.email.email_utils_third_party import notify_exception, send_email
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_web_dictionary
from booking_process.utils.namespaces.namespace_utils import get_all_namespaces, get_namespace, set_namespace, \
	get_application_id
from booking_process.utils.queues.queues_utils import defer
from booking_process.utils.templates.template_utils import buildTemplate
from booking_process.utils.users.transactions_utils import add_booking_bonifications_on_entry_date, \
	user_club_register_booking_transaction, add_booking_bonifications_on_departure_date, retreive_user_transactions
from booking_process.utils.auditing import auditUtils
from booking_process.utils.clubs.club_constants import LOYALTY_USERNAME, LOYALTY_PASSWORD
from booking_process.utils.users.users_methods import check_if_user_exists


class clubBonifications(MethodView):
	def post(self):
		self.get()

	def get(self):
		namespaces_list = get_all_namespaces()

		target_email = request.values.get('force_email')
		target_date = request.values.get('force_date')  # YYYY-MM-DD

		if target_email or target_date:
			actual_namespace = get_namespace()
			send_departure_club_bonification(actual_namespace, target_email, target_date)
			return

		for namespace_name in namespaces_list:
			defer(send_departure_club_bonification, namespace_name)

		if not namespaces_list:
			send_departure_club_bonification(None)


class ClubAddPointsStartDate(MethodView):
	def post(self):
		self.get()

	def get(self):
		namespaces_list = get_all_namespaces()
		custom_dates = request.values.get('custom_dates')

		for namespace_name in namespaces_list:
			defer(add_points_checking_start_date, namespace_name, custom_dates)

		if not namespaces_list:
			add_points_checking_start_date(None)


def add_points_checking_start_date(namespace, custom_dates=None):
	try:
		set_namespace(namespace)
		if add_booking_bonifications_on_entry_date() or add_booking_bonifications_on_departure_date():
			reservations_list = []

			if add_booking_bonifications_on_entry_date():
				logging.info("Will perform the bonification at entry date at hotel: %s" % namespace)
				reservations_list = reservations_at_date(custom_dates)

			if add_booking_bonifications_on_departure_date():
				logging.info("Will perform the bonification at departure date at hotel: %s" % namespace)
				reservations_list = reservations_at_date(custom_dates, query_name='endDate')

			logging.info("Number of reservations found: %s" % len(reservations_list))

			master_namespace = get_config_property_value(MEMBERS_CLUB_NAMESPACE)
			if master_namespace:
				logging.info("Master namespace: %s" % master_namespace)
				set_namespace(master_namespace)

			for reservation_element in reservations_list:
				reservation_extra_info = json.loads(reservation_element.get("extraInfo", "{}"))
				club_points_not_logged = get_config_property_value(CLUB_POINTS_NOT_LOGGED)
				id_member_from_extra = reservation_extra_info.get('clubMember')
				is_agency_reservation = reservation_extra_info.get('agency_id')
				if 'avoid_agency' in club_points_not_logged and is_agency_reservation:
					continue

				if id_member_from_extra or club_points_not_logged:
					user = None
					if id_member_from_extra:
						try:
							id_member_from_extra = int(id_member_from_extra)
							user = get_user_by_id(id_member_from_extra)
						except Exception as e:
							logging.info("Error getting user by idmember: %s" % id_member_from_extra)

					if not user and club_points_not_logged:
						logging.info("User not found by clubMember")
						logging.info("Searching by email: %s" % reservation_element.get("email"))

						user = get_user_by_email(reservation_element.get("email"))

					if user:
						user = dict(user)
						target_identifier = reservation_element.get("identifier")
						points_by_booking = retreive_user_transactions(user.get('idmember'), only_reservation=target_identifier)
						if len(points_by_booking) == 0:
							personalDetails = {
								"email": user.get("email"),
								"personalId": reservation_extra_info.get("personalId", "")
							}

							reservation_element['_identifier'] = reservation_element.get("identifier")
							reservation_element['_price'] = reservation_element.get("price")
							reservation_element['_startDate'] = reservation_element.get("startDate")
							reservation_element['_endDate'] = reservation_element.get("endDate")
							actual_namespace = get_namespace()
							user_club_register_booking_transaction(personalDetails, reservation_element, user.get("email"), namespace)
							set_namespace(actual_namespace)
							logging.info("Bonification performed for reservation: %s" % reservation_element.get("identifier"))

					else:
						logging.info("User not found by idmember: %s or email: %s" % (
						reservation_extra_info.get('clubMember'), reservation_element.get("email")))

	except Exception as e:
		message = auditUtils.makeTraceback()
		logging.error(e)
		logging.error(message)
		notify_exception('Club Adding Points StartDate', message, add_hotel_info=True)


def _build_test_reservation(email):
	testing_reservation = {
		'language': SPANISH,
		'email': email,
		'name': 'Nombre de prueba',
		'surname': 'Apellidos de prueba'
	}

	return [testing_reservation]


def send_departure_club_bonification(namespace, force='', target_date=None):
	try:
		set_namespace(namespace)
		departure_promocode = get_config_property_value(DEPARTURE_CLUB_PROMOCODE)
		if departure_promocode:
			logging.info("Will perform the bonification departure at hotel: %s" % namespace)
			if not force:
				reservations_list = reservations_finished(target_date)
			else:
				reservations_list = _build_test_reservation(force)

			logging.info("Number of reservations found: %s" % len(reservations_list))
			for reservation_element in reservations_list:
				if not force:
					promocode_created = departure_promocode_creation(reservation_element, departure_promocode)
				else:
					promocode_created = 'TEST PROMOCODE'

				if promocode_created:
					send_departure_bonification_email(reservation_element, promocode_created)

	except Exception as e:
		message = auditUtils.makeTraceback()
		logging.error(e)
		logging.error(message)
		notify_exception('Club Departure Bonification Error', message, add_hotel_info=True)


def send_departure_bonification_email(reservation_information, promocode_created):
	target_language = reservation_information['language']
	if not target_language:
		target_language = SPANISH

	context = {
		'booking_information': reservation_information,
		'hide_user_information': True
	}

	email_club_departure = get_section_from_section_spanish_name('_email club departure', target_language)
	context['section_club'] = copy.deepcopy(email_club_departure)  # Needed to avoid references
	context['section_club']['content'] = context['section_club']['content'].replace("@@promocode@@", promocode_created)
	section_advance_properties = get_properties_for_entity(context['section_club'].get('key', False), target_language)

	context.update(get_web_dictionary(target_language))

	email_template = 'templates/email/club_register.html'
	rendered_template = buildTemplate(email_template, context)

	email_subject = section_advance_properties.get('email_subject')
	if not email_subject:
		email_subject = context['T_premiamos_fidelidad']

	logging.info("Sending email with promocode %s to email %s" % (promocode_created, reservation_information['email']))

	send_email(reservation_information['email'], email_subject, rendered_template, rendered_template)


def reservations_finished(target_date=None):
	prior_date = datetime.date.today()
	if not target_date:
		query_date = "%d-%0.2d-%0.2d" % (prior_date.year, prior_date.month, prior_date.day)
	else:
		query_date = target_date
		logging.info("Forcing date to: %s" % query_date)
	logging.info("Search for reservations leaving at date:" + str(query_date))
	reservations = directDataProvider.get("Reservation", {"endDate": query_date})
	reservations_not_cancelled = filter(lambda x: not x.cancelled, reservations)
	reservations_not_cancelled = map(lambda x: x.to_dict(), reservations_not_cancelled)

	return list(reservations_not_cancelled)


def reservations_at_date(custom_dates=None, query_name='startDate'):
	if not custom_dates:
		prior_date = datetime.date.today()
		target_day = prior_date.day

		if query_name == 'endDate':
			target_day = target_day + 1

		query_date = "%d-%0.2d-%0.2d" % (prior_date.year, prior_date.month, target_day)
	else:
		query_date = custom_dates
		logging.info("Forcing date to: %s" % query_date)

	logging.info("Search for reservations starting at date: " + str(query_date))
	reservations = directDataProvider.get("Reservation", {query_name: query_date})

	reservations_not_cancelled = list(filter(lambda x: not x.cancelled, reservations))
	if reservations_not_cancelled:
		reservations_not_cancelled = list(map(lambda x: x.to_dict(), reservations_not_cancelled))

	return reservations_not_cancelled


def departure_promocode_creation(reservation_info, bonification_amount):
	original_namespace = get_namespace()
	parent_club = get_config_property_value(MEMBERS_CLUB_NAMESPACE)
	if parent_club:
		set_namespace(parent_club)

	user_exists = check_if_user_exists(email=reservation_info['email'])
	actual_namespace = get_namespace() if get_namespace() else get_application_id()
	loyalty_endpoint_url = get_loyalty_seeker_url() + '/promocodes/'

	multiplier_bonification = 0
	amount_bonification = 0

	if 'x' in bonification_amount.lower():
		multiplier_bonification = bonification_amount.lower().replace('x', '')
	else:
		amount_bonification = bonification_amount

	set_namespace(original_namespace)

	if actual_namespace and "r__" in actual_namespace:
		actual_namespace = actual_namespace.replace("r__", "")

	if user_exists:
		params = {
			'user': LOYALTY_USERNAME,
			'password': LOYALTY_PASSWORD,
			'server': True,
			'action': 'add_random_promocode',
			'idmember': user_exists.get('idmember'),
			'multiplier': multiplier_bonification,
			'amount': amount_bonification,
			'namespace': actual_namespace,
			'booking_only': True,
			'description': 'Departure bonification'
		}

		loyalty_request = requests.post(loyalty_endpoint_url, data=params)
		if loyalty_request.text:
			# Return the created promocode
			return loyalty_request.text
