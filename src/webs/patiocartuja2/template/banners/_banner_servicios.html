<div id="servicios" class="wrapper_content banner_servicios_wrapper">
    <div class="container12 bg_box right_side">
        {% if  is_mobile %}
            <div class="banner_servicios_column info">
                <div class="card">
                    <div class="content_wrapper">
                        {% if banner_servicios_section.subtitle %}
                            <div class="content_title sub_border">
                                <h3 class="title">
                                    {{ banner_servicios_section.subtitle|safe }}
                                </h3>
                            </div>
                        {% endif %}
                        {% if banner_servicios_section.content %}
                            <div class="desc">
                                {{ banner_servicios_section.content|safe }}
                            </div>
                        {% endif %}
                        {% if banner_servicios_icons %}
                        <div class="icons_wrapper">
                            {% for x in banner_servicios_icons %}
                            <div class="icon_box">
                                <i class="{{ x.description|safe }}"></i>
                                <span class="title">{{ x.title|safe }}</span>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endif %}
        <div class="banner_servicios_column offers owl-carousel">
            {% for x in banner_servicios_pictures %}
                <div class="card">
                    <div class="picture_wrapper">
                        <img src="{{ x.servingUrl|safe }}" alt="{{ x.title|safe }}">
                    </div>
                    <div class="content_wrapper">
                        <div class="content_title">
                            <h3 class="title">
                                {{ x.title|safe }}
                            </h3>
                            <div class="desc">
                                {{ x.description|safe }}
                            </div>
                            {% if not is_mobile %}<a href="#data" class="button-promotion btn btn_secondary">{{ T_reservar }}</a>{% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        {% if not is_mobile %}
            <div class="banner_servicios_column info">
                <div class="card">
                    <div class="content_wrapper">
                        {% if banner_servicios_section.subtitle %}
                            <div class="content_title sub_border">
                                <h3 class="title">
                                    {{ banner_servicios_section.subtitle|safe }}
                                </h3>
                            </div>
                        {% endif %}
                        {% if banner_servicios_section.content %}
                            <div class="desc">
                                {{ banner_servicios_section.content|safe }}
                            </div>
                        {% endif %}
                        {% if banner_servicios_icons %}
                        <div class="icons_wrapper">
                            {% for x in banner_servicios_icons %}
                            <div class="icon_box">
                                <i class="{{ x.description|safe }}"></i>
                                <span class="title">{{ x.title|safe }}</span>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>
<script type="text/javascript">

    $(window).load(function () {

        {% if is_mobile%}

            $("#servicios .banner_servicios_column.offers.owl-carousel").owlCarousel({
                loop: true,
                nav: true,
                navText: ['<i class="fal fa-chevron-left"></i>', '<i class="fal fa-chevron-right"></i>'],
                dots: false,
                items: 1,
                margin: 15,
                smartSpeed: 600,
                fluidSpeed: 600,
                navSpeed: 600,
                dotsSpeed: 600,
                dragEndSpeed: 600,
                autoplay: false
            });

        {% else %}

            $("#servicios .banner_servicios_column.offers.owl-carousel").owlCarousel({
                loop: true,
                nav: true,
                navText: ['<i class="fal fa-chevron-left"></i>', '<i class="fal fa-chevron-right"></i>'],
                dots: false,
                items: 2,
                margin: 15,
                smartSpeed: 600,
                fluidSpeed: 600,
                navSpeed: 600,
                dotsSpeed: 600,
                dragEndSpeed: 600,
                autoplay: true
            });

            var max_height = 0;
            $("#servicios .banner_servicios_column").each(function(){
                var actual_height = $(this).find(".desc").height();
                if (max_height < actual_height) max_height = actual_height;
            });

            $("#servicios .banner_servicios_column .desc").height(max_height);

        {% endif %}

    });

</script>