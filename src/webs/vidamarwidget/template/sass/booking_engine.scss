$corporate_1: white;
$corporate_2: blue;
$corporate_3: green;
$corporate_4: pink;

#ui-datepicker-div{
  z-index: 9990!important;
  top:512px !important;
}

/*=== Fonts ====*/
@font-face {
  font-family: 'franklein';
  src: url('/static_1/fonts/franklein/FranKleinMedium.ttf') format('truetype');
  font-style: normal;
  font-weight: 500;
}

@font-face {
  font-family: 'franklein';
  src: url('/static_1/fonts/franklein/FranKleinBold.ttf') format('truetype');
  font-style: normal;
  font-weight: 700;
}

@font-face {
  font-family: 'franklein';
  src: url('/static_1/fonts/franklein/FranKleinFaces.ttf') format('truetype');
  font-style: normal;
  font-weight: 300;
}

@font-face {
  font-family: 'franklein';
  src: url('/static_1/fonts/franklein/FranKleinBook.ttf') format('truetype');
  font-style: normal;
  font-weight: 100;
}

body {
  font-family: franklein;
}

#full-booking-engine-html {
  display: block;
  width: 100%;
}

//////////////////////////////////////////////
//HORIZONTAL BOOKING ENGINE
//////////////////////////////////////////////
/* A few more changes in the new booking engine */
div#horizontal_booking_selector {
  position: relative;
  width: 100%;
  min-width: 1140px;
  z-index: 35;
  top: 0;
}

.boking_widget_inline .booking_form {
  width: 1095px;
  margin: auto;
}

#full_wrapper_booking {
  background: rgba(25, 25, 25, 0.65);
  padding: 5px 0 9px;

  .boking_widget_inline {
    position: relative;
    padding-top: 0;
  }

  .boking_widget_inline .room_list_wrapper {
    margin-left: 0 !important;
    margin-right: 0 !important;
    position: absolute;
    background: rgba(25, 25, 25, 0.65);
    display: none;
    top: 60px;
    border: 2px solid $corporate_1;
    left: 535px;
    padding: 11px 20px 5px 15px;

    &:before {
      display: none;
      position: absolute;
      left: 0;
      margin: 0 auto;
      right: 0;
      bottom: -13px;
      content: "";
      width: 0;
      height: 0;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-top: 13px solid rgba(25, 25, 25, 0.65);
    }

    &:after {
      display: none;
      position: absolute;
      left: 0;
      margin: 0 auto;
      right: 0;
      bottom: -10px;
      content: "";
      width: 0;
      height: 0;
      border-left: 9px solid transparent;
      border-right: 9px solid transparent;
      border-top: 10px solid transparent;
    }

    ul.room_list {
      display: inline-block;
    }
  }

  .date_box .date_year, .date_box .date_day {
    //color: grey !important;
  }

  .date_box {
    background-color: transparent;
    border-radius: 0;
    border: 2px solid $corporate_1;
    width: 105px;
    height: 55px;
  }

  .destination_field {
    border: 2px solid $corporate_1;
    display: inline-block;
    background: url(/static_1/images/widget/hotel.png) no-repeat 9% center;
  }

  .boking_widget_inline .entry_date_wrapper label, .boking_widget_inline .departure_date_wrapper label, .boking_widget_inline .rooms_number_wrapper label {
    color: $corporate_1 !important;
    font-size: 12px !important;
  }

  .selectric {
    width: 150px;
    background: transparent url("/static_1/images/widget/arrow_down.png") no-repeat 90% 49%;
    background-size: 15px;
    color: grey;
    border-radius: 0;
    border: 2px solid $corporate_1;
    height: 49px;

    .label {
      color: grey !important;
      background: none !important;
    }

    .button {
      display: none;
    }
  }

  .boking_widget_inline .wrapper_booking_button {
    float: none;
    margin-left: 7px;
    margin-top: 5px;
    display: inline-block;
    vertical-align: top;
  }

  .wrapper_booking_button button {
    cursor: pointer;
    background: #b70036;
    border-radius: 0;
    position: relative;
    top: 0;
    margin-top: 0;
    vertical-align: top;
    font-family: 'franklein';
    height: 42px;

    &.cycle_color {
      background: #b70036 url(/static_1/images/widget/1.gif);
      -webkit-background-size: 100%;
      background-size: 100%;
    }
  }

  .wrapper_booking_button button:hover {
    opacity: 0.8;
  }

  .wrapper_booking_button .promocode_text {
    display: none;
  }

  .wrapper_booking_button .promocode_input {
    background-color: transparent !important;
    width: 130px !important;
    border-radius: 0;
    border: 2px solid $corporate_1;
    color: white !important;
    font-size: 14px;
    line-height: 38px;
    margin-top: 0;
    resize: none;
    height: 42px;
    box-sizing: border-box;
    text-align: center;
    padding-bottom: 3px;
    margin-right: 5px;
    padding-top: 1px;
    font-family: 'franklein';
  }

  .promocode_input::-webkit-input-placeholder {
    color: $corporate_1 !important;
    font-size: 11px;
    font-weight: 400;
    text-transform: uppercase;
    font-family: 'franklein';
    //font-family: 'Montserrat', sans-serif;
  }
  .promocode_input::-moz-placeholder {
    color: $corporate_1 !important;
    font-size: 11px;
    font-weight: 300;
    text-transform: uppercase;
    font-family: 'franklein';
    //font-family: 'Montserrat', sans-serif;
  }
  .promocode_input:-moz-placeholder {
    color: $corporate_1 !important;
    font-size: 11px;
    font-weight: 300;
    text-transform: uppercase;
    font-family: 'franklein';
    //font-family: 'Montserrat', sans-serif;
  }
  .promocode_input:-ms-input-placeholder {
    color: $corporate_1 !important;
    font-size: 11px;
    font-weight: 300;
    text-transform: uppercase;
    font-family: 'franklein';
    //font-family: 'Montserrat', sans-serif;
  }

  //A few more changes for the booking engine HORIZONTAL

  .boking_widget_inline {
    background-color: transparent !important;
    height: auto;
    width: 100%;
  }

  .booking_form_title {
    background-color: transparent !important;
  }

  .booking_form {
    background-color: transparent !important;
    width: 100%;
    font-family: 'franklein';
    //font-family: 'Montserrat', sans-serif;
  }

  .boking_widget_inline .room .room_title, .boking_widget_inline .room .adults_selector label, .boking_widget_inline .room .children_selector label {
    color: $corporate_1 !important;
    font-size: 12px;
  }

  .boking_widget_inline .stay_selection {
    //with type room selector must be: margin-left 0
    //margin-left: 0 !important;
    margin-left: 0 !important;
    margin-top: 5px;
  }
}

.entry_date_wrapper,
.departure_date_wrapper {
  margin-right: 7px;
}

.wrapper-new-web-support .web_support_number {
  margin-bottom: -5px;
  display: inline-block;

  &::before, &::before {
    content: "";
    font-family: icomoon;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    margin-top: 4px;
    line-height: 1;
    display: inline-block;
    margin-right: 2px;
    border-image-source: initial;
    border-image-slice: initial;
    border-image-width: initial;
    border-image-outset: initial;
    border-image-repeat: initial;
    font-size: 15px;
    border-width: 1px;
    border-style: solid;
    border-color: initial;
    border-radius: 30px;
    padding: 5px;
    margin: 0px 4px;
  }
}

.destination_wrapper {
  margin-top: 5px;
  margin-right: 7px;

  .right_arrow {
    cursor: pointer;
  }

  div#placeholder {
    color: $corporate_1;
  }

  input {
    color: $corporate_1;
  }

  input::-webkit-input-placeholder {
    color: $corporate_1;
    font-size: 11px;
    font-weight: 400;
    text-transform: uppercase;
    font-family: 'franklein';
    //font-family: 'Montserrat', sans-serif;
  }

  input:-moz-placeholder {
    color: $corporate_1;
    font-size: 11px;
    font-weight: 300;
    text-transform: uppercase;
    font-family: 'franklein';
    //font-family: 'Montserrat', sans-serif;
  }

  input::-moz-placeholder {
    color: $corporate_1;
    font-size: 11px;
    font-weight: 300;
    text-transform: uppercase;
    font-family: 'franklein';
    //font-family: 'Montserrat', sans-serif;
  }

  input:-ms-input-placeholder {
    color: $corporate_1;
    font-size: 11px;
    font-weight: 300;
    text-transform: uppercase;
    font-family: 'franklein';
    //font-family: 'Montserrat', sans-serif;
  }

}

.destination_wrapper {
  input {
    height: 38px;
    width: 215px;
    padding-left: 10px;
    background: transparent;
    border-radius: 0;
    cursor: pointer;

    div#placeholder {
      color: $corporate_1 !important;
    }
  }

  label {
    color: $corporate_1;
    margin-right: 15px;
    display: none;
  }
}

.roomtype_selector {
  left: 168px;
  top: 71px;
}

.roomtype_selector .title_selector {
  background: rgba(0, 0, 0, 0.53);
  padding-right: 27px !important;
  padding-left: 29px;
}

.wrapper_booking_button .spinner_wrapper {
  position: absolute;
  right: -75px;
  bottom: 24px;
}

//booking engine in fancybox (for selects!)

//WEB SUPPORT
.wrapper-new-web-support {
  display: block !important;
  font-size: 14px !important;
  background: $corporate_1 !important;
  color: $corporate_1 !important;
}

/*===== Personalized ====*/
.entry_date_wrapper, .departure_date_wrapper, .rooms_number_wrapper {
  label {
    display: none;
  }
}

.entry_date_wrapper, .departure_date {
  padding-top: 0px;
}

#full_wrapper_booking {
  .rooms_number_wrapper {
    display: inline-block;
    vertical-align: top;
    margin-right: 7px;
    margin-top: 0;

    .selectric {
      width: 73px;
      padding-top: 0;
      background: transparent url("/static_1/images/widget/arrow_down.png") no-repeat 90% 49%;
      background-size: 15px;
      height: 28px;

      p.label {
        color: white !important;
        font-weight: 400;
        margin-top: 0;
        font-family: 'franklein';
        //font-family: 'Montserrat', sans-serif;
        font-size: 16px;
        line-height: 27px;
        margin-left: 15px;
        background: transparent url("/static_1/images/booking_6/down_arrow.png") no-repeat 90%;
      }

      b.button {
        display: none;
      }
    }

    label {
      color: white !important;
      font-size: 12px;
      padding-bottom: 3px;
      display: block;
    }
  }
}

.room .room_title, .room .adults_selector, .room .children_selector, .room .babies_selector {
  label {
    margin-bottom: 3px;
  }
}

.entry_date_wrapper, .departure_date_wrapper, .rooms_number_wrapper {
  width: auto;
  display: inline-block;
  margin-top: 5px;
}

.room .room_title, .room .adults_selector, .room .children_selector, .room .babies_selector {
  .selectric {
    //height: 38px !important;
    height: 24px !important;
  }
}

#full_wrapper_booking .date_box {
  box-sizing: border-box;
  padding-left: 30px;
  height: 42px;
  padding-top: 10px;

  .date_day, .date_year {
    display: inline-block;
  }

  .date_day {
    border-bottom: 0 !important;
    color: $corporate_1 !important;
    font-weight: 400;
    margin-right: -5px;
    font-size: 11px;
  }

  .date_day {
    .day, .month {
      display: inline-block;
    }

    .day {
      margin-right: 0;
    }
  }

  .date_year {
    color: $corporate_1;
    font-size: 11px;
    font-weight: 400;
  }
}

.entry_date, .departure_date_wrapper {
  background: url(/static_1/images/widget/calenadr.png) no-repeat 11%!important;
  margin-right: 0;

  .placeholder {
    color: $corporate_1;
    font-size: 11px;
    font-weight: 400;
    line-height: 21px;
  }

  .placeholder + .date_day {
    display: none !important;

    & + .date_year {
      display: none !important;
    }
  }
}

.boking_widget_inline .room.room1 .room_title {
  margin-top: 37px;
  margin-right: 17px;
  display: none;
}

.boking_widget_inline {
  .room {
    &.room2, &.room3 {
      .adults_selector label, .children_selector label {
        display: none;
      }
    }
  }
}

.boking_widget_inline .room .room_title {
  margin-left: -77px;
  margin-top: 8px;
}

.selectricWrapper.rooms_number {
  width: auto;

  .selectric {
    padding-top: 17px;
    padding-left: 10px;
    box-sizing: border-box;
    color: $corporate_1 !important;
    font-size: 11px;
    text-transform: uppercase;
    font-weight: 400;
  }
}

.guest_selector {
  display: inline-block;
  border: 2px solid $corporate_1;
  background: transparent url("/static_1/images/widget/arrow_down.png") no-repeat 90% 49%;
  background-size: 15px;
  height: 42px;
  margin-left: 7px;
  text-transform: uppercase;
  box-sizing: border-box;
  color: $corporate_1 !important;
  font-size: 11px;
  cursor: pointer;
  font-weight: 400;
  width: 130px;
  padding-left: 17px;
  padding-top: 14px;
}

.room .room_title, .room .adults_selector, .room .children_selector {
  width: 67px;
}

#full_wrapper_booking .selectricWrapper.room_selector .selectric .label {
  font-size: 16px;
  line-height: 27px;
}

#full_wrapper_booking .selectricWrapper.room_selector {
  margin-right: 20px;
  width: 100%;

  .selectric {
    width: auto;

    .label {
      color: white !important;
      font-weight: 400;
      margin-top: 0;
      font-family: 'franklein';
      //font-family: 'Montserrat', sans-serif;
      font-size: 16px;
      line-height: 27px;
      background: transparent url("/static_1/images/booking_6/down_arrow.png") no-repeat 90%;
    }

    .button {
      display: none;
    }
  }
}

.boking_widget_inline .promocode_input {
  margin-right: 8px;
}

//******************** Booking Engine ****************************//

//////BOOKING ENGINE

/* A few more changes in the new booking engine */

/** CALENDAR DATEPICKER**/

h4.booking_title_2 {
  display: block !important;
  font-size: 40px;
  line-height: 30px;
  margin-top: 8px;
}

h4.best_price {
  display: none;
}

h4.booking_title_custom {
  font-weight: 100;
  margin-bottom: -15px;
  text-transform: uppercase;
}

.ui-widget-header {
  background: #b70036;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #b70036;
  color: white;
}

.booking_widget {
  top: 157px;
  label {
    margin-left: 6px !important;
  }
}

.date_box {
  background-color: #edeef0;
}

.date_box .date_year {
  color: #808080;

}

.selectric {
  background-color: #edeef0;

}

.wrapper_booking_button .promocode_input {
  background-color: #edeef0;
  border-radius: 0px;
  width: 115px;
}

.promocode_input::-webkit-input-placeholder {
  color: transparent !important;
  &::before {
    content: "Promocode";
    color: #1b5360 !important;
    padding-left: 14px;
  }
}

.promocode_input::-moz-placeholder {
  color: #1b5360 !important;
}

.promocode_input:-moz-placeholder {
  color: #1b5360 !important;
}

.promocode_input:-ms-input-placeholder {
  color: #1b5360 !important;
}

.booking_widget.interior {
  top: 20px;
}

.selectric .button {
  background: #afb3b2 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  border-radius: 0px;
}

.wrapper_booking_button button {
  background: #1b5360;
  cursor: pointer;
  width: 135px;
  border-radius: 0px;
  font-weight: bold;

  &:hover {
    background: #838587;
    color: $corporate_1;
  }
}

.booking_form {
  background: $corporate_2;
  padding: 20px 20px 8px;
}

.booking_form_title {
  background: $corporate_2;
}

.date_box {
  border-radius: 0px;
  .date_day {
    color: $corporate_3;
  }
}

.selectric {
  border-radius: 0px;
  .label {
    color: $corporate_3;
  }
}

.entry_date_wrapper label, .departure_date_wrapper label, .rooms_number_wrapper label, .room .room_title, .room .adults_selector label, .room .children_selector label {
  color: $corporate_1;
}

h4.best_price {
  font-size: 25px;
}

.room_title {
  text-align: center;
}

/* TICKS */

.tickets {
  text-align: center;
  overflow: hidden;
  margin-bottom: 25px;
}

#ticks_container {
  display: inline-block;
  position: relative;
  &:before,
  &:after {
    content: "";
    position: absolute;
    height: 14px;
    border-bottom: 1px solid $corporate_1;
    top: 5px;
    width: 600px;
  }
  &:before {
    right: 100%;
    margin-right: 15px;
  }
  &:after {
    left: 100%;
    margin-left: 15px;
  }
}

.ticks {
  display: inline-block;
  width: auto;
  text-align: left;
  line-height: 21px;
  margin-top: 5px;
  text-transform: capitalize;
  padding-left: 25px;
  font-size: 13px;
  color: white;
  margin-left: 5px;
  background: url("/static_1/images/ticks/check.png") no-repeat 0;
  background-size: 21px;
  margin-right: 35px;
}

.hotel_selector {
  border-radius: 0px;
  box-shadow: none;
  position: absolute;
  display: none;
  background: #F9F9F9;
  z-index: 1000;
  left: 188px !important;
  top: auto !important;
  bottom: auto !important;
  width: auto;
  max-width: 708px;
  padding: 0;

  .hotel_selector_inner {

    position: relative;

    .close_hotel_selector {
      position: absolute;
      right: -10px;
      background: dimgray url(/static_1/lib/lightbox/images/close.png) no-repeat center center;
      background-size: 12px;
      width: 20px;
      height: 20px;
      top: -11px;
      cursor: pointer;
      border-radius: 26px;
    }
  }

  .hotel_selector_option {
    width: 175px;
    position: relative;
    height: auto;
    float: left;
    cursor: pointer;
    margin-right: 0;
    margin-bottom: 0;
    border: 1px solid #ececec;

    &:hover, &.selected {
      background: #b70036;
      h3 {
        color: white;
      }
    }

    &.right {
      margin-left: 10px;
    }

  }

  .hotel_selector_option_name h3 {
    font-family: nexabold;
    margin-top: 89px;
    background: #E6E6E6;
    color: $corporate-1;
    line-height: 35px;
    text-transform: uppercase;
    padding-left: 37px;

    span {
      font-family: nexaregular;
    }
  }

  .title_selector {
    position: relative;
    bottom: 2px;
    height: auto;
    width: auto;
    font-size: 12px;
    color: #666;
    padding: 8px 8px 3px;
  }
}

.destination {
  background: #A9A9A9;
}

#placeholder {
  color: $corporate_3;
}

.destination_field .right_arrow {
  top: 24px;
}

.destination_wrapper {
  label {
    color: $corporate_1;
  }

  input {
    border-radius: 0;
    padding-left: 40px;
    height: 38px!important;
    box-sizing: border-box;
    color: white;
    text-transform: uppercase;
    background: transparent url("/static_1/images/widget/arrow_down.png") no-repeat 90% 49%;
    background-size: 15px;
  }

  .right_arrow {
    background: #afb3b2 url(/static_1/images/booking/flecha_motor.png) no-repeat center center;
    border-radius: 0;
    display: none;
  }
}

/*===== Hotel Selector =====*/
//
//.hotel_selector .hotel_selector_inner #luna-olympus {
//  background: url(/static_1/images/olympus.jpg) no-repeat
//}
//
//.hotel_selector .hotel_selector_inner #luna-miramar {
//  background: url(/static_1/images/miramar.jpg) no-repeat
//}
//
//.hotel_selector .hotel_selector_inner #luna-oura {
//  background: url(/static_1/images/oura.jpg) no-repeat
//}
//
//.hotel_selector .hotel_selector_inner #luna-fatima {
//  background: url(/static_1/images/fatima.jpg) no-repeat
//}
//
//.hotel_selector .hotel_selector_inner #luna-falesia {
//  background: url(/static_1/images/falesia.jpg) no-repeat
//}
//
//.hotel_selector .hotel_selector_inner #luna-esperansa {
//  background: url(/static_1/images/esperansa.jpg) no-repeat
//}
//
//.hotel_selector .hotel_selector_inner #luna-oceano {
//  background: url(/static_1/images/oceano.jpg) no-repeat
//}
//
//.hotel_selector .hotel_selector_inner #luna-brisamar {
//  background: url(/static_1/images/brisamar.jpg) no-repeat
//}
//
//.hotel_selector .hotel_selector_inner #luna-alvor-village {
//  background: url(/static_1/images/village.jpg) no-repeat
//}
//
//.hotel_selector .hotel_selector_inner #luna-alvor-bay {
//  background: url(/static_1/images/bay.jpg) no-repeat
//}
//
//.hotel_selector .hotel_selector_inner #luna-alpinus {
//  background: url(/static_1/images/alpinus.jpg) no-repeat
//}
//
//.hotel_selector .hotel_selector_inner #luna-zombo {
//  background: url(/static_1/images/zombo.jpg) no-repeat
//}

.hotel_selector {
  ul:not(.all_hotels) {
    display: none;
  }

  .title_group {
    display: none;
  }
}

#full_wrapper_booking .location_group_select {
  width: auto;
  display: inline-block;
  vertical-align: middle;
  margin-right: 4px;
  background: url(/static_1/images/widget/location.png) no-repeat 9% center;

  .selectric {
    height: 42px;
    box-sizing: border-box;
    padding-top: 4px;
    background: transparent url("/static_1/images/widget/arrow_down.png") no-repeat 90% 49%;
    background-size: 15px;
  }

  p.label {
    color: white !important;
    font-size: 11px;
    font-weight: 400;
    text-transform: uppercase;
    font-family: 'franklein';
    //font-family: 'Montserrat', sans-serif;
    margin-left: 35px;
    line-height: 31px;
  }

  b.button {
    display: none;
  }
}

.wrapper_booking_button .spinner_wrapper {
  display: none;
}

.location_group_select {
  .selectricItems {
    //width: 492px !important;
    width: 450px !important;
    border: 0 !important;

    li {
      display: inline-block;
      padding: 5px 8px;
      border: 1px solid #ececec;

      &.selected, &:hover {
        //box-shadow: 0px 0px 5px #313131 inset;
        background: #b70036;
        color: white;
      }
    }
  }
}

input.destination:not([value=""]) {
  background: transparent;
}

a.special_link {
  text-align: right;
  color: white;
  margin-top: 5px;
  margin-right: 4px;
  display: table;
  float: right;
}

/*======== Responsive ======*/
.boking_widget_inline {
  width: 100%;
  max-width: 1070px;

  .booking_form {
    margin: auto;
    width: auto;
  }
}

div#horizontal_booking_selector {
  min-width: auto;
}

div#horizontal_booking_selector #full_wrapper_booking .boking_widget_inline {
  width: 602px;
}

.boking_widget_inline .stay_selection {
  float: none;
}

.guest_selector, .entry_date_wrapper {
  float: left;
}

.guest_selector {
  margin-left: 0;
  margin-top: 5px;
  margin-right: 0px;
  float: none;
  padding-top: 18px;
}

#full_wrapper_booking .boking_widget_inline .room_list_wrapper {
  width: 230px;
  left: 54px;
  top: 113px;
}

#full_wrapper_booking .rooms_number_wrapper {
  margin-top: 0;
}

.hotel_selector {
  width: 456px;
  left: 92px !important;
}

#full_wrapper_booking .location_group_select .selectric, .destination_wrapper input {
  width: 232px;
}

#full_wrapper_booking .location_group_select p.label {
  margin-left: 55px;
}

.destination_wrapper input {
  padding-left: 55px!important;
}

#full_wrapper_booking .wrapper_booking_button button {
  width: 125px !important;
  margin-top: 5px;
}

#full_wrapper_booking .wrapper_booking_button .promocode_input {
  width: 100px !important;
  vertical-align: top;
  margin-top: 5px;
  margin-left: 2px;
}

#full_wrapper_booking .boking_widget_inline .wrapper_booking_button {
  margin-left: 0;
  display: inline;
}

.departure_date_wrapper {
  float: left;
  margin-top: 5px;
  margin-right: 7px;
}

a.special_link {
  text-align: right;
  display: block;
  color: white;
  margin-top: -30px;
  margin-right: 21px;
  float: right;
  z-index: 2;
  position: relative;
}

div#horizontal_booking_selector #full_wrapper_booking .boking_widget_inline {
  width: 480px;
  float: none;
  margin-top: 5px;
  margin-right: auto;
}

.destination_wrapper {
  margin-right: 0;
}

.guest_selector {
  margin-left: 0;
  width: 105px;
  padding-left: 10px;
  margin-right: 0;
  padding-top: 14px;
}

#full_wrapper_booking .boking_widget_inline .wrapper_booking_button {
  display: inline;
  margin-left: 4px;

  button {
    display: block;
    margin-top: 5px;
    width: 475px !important;
  }
}

#full_wrapper_booking .wrapper_booking_button .promocode_input {
  width: 138px !important;
  margin-left: 0;
  margin-right: 0;
  margin-top: 5px;
}

.boking_widget_inline .hotel_selector {
  width: auto;
  left: 63px !important;
  top: 2px !important;

}

#full_wrapper_booking .boking_widget_inline .room_list_wrapper {
  top: 167px;
}

#full_wrapper_booking {
  width: 100%;
  display: table;
  padding-bottom: 0;
}

a.special_link {
  float: none;
  margin-top: -12px;
  text-align: center;
  margin-right: 0;
  margin-bottom: 12px;
}

div#horizontal_booking_selector #full_wrapper_booking .boking_widget_inline {
  width: 378px;
}

#full_wrapper_booking .selectric, .destination_wrapper input, #full_wrapper_booking .date_box {
  width: 179px;
}

.departure_date_wrapper .date_box, #full_wrapper_booking .wrapper_booking_button .promocode_input {
  width: 183px !important;
}

.guest_selector {
  width: 179px;
}

#full_wrapper_booking .boking_widget_inline .wrapper_booking_button button {
  width: 368px !important;
}

.location_group_select .selectricItems {
  width: 296px !important;
  left: 30px;
  top: 23px;
  z-index: 999999;
}

.location_group_select .selectricItems li {
  width: 130px;
  text-align: center;

  &:first-of-type {
    width: 278px;
  }
}

.boking_widget_inline .hotel_selector {
  left: 12px !important;
}

#full_wrapper_booking .location_group_select .selectric {
  width: 150px !important;
}

.destination_wrapper input {
  width: 207px!important;
}

#full_wrapper_booking .date_box {
  padding-left: 44px;
}

#full_wrapper_booking .location_group_select p.label {
  margin-left: 50px;
}

#full_wrapper_booking .boking_widget_inline .wrapper_booking_button {
  margin-left: 2px;
}

#full_wrapper_booking .boking_widget_inline .room_list_wrapper {
  top: 220px;
}
