@charset "UTF-8";
@import url("https://fonts.googleapis.com/css?family=Open+Sans:300,400,700,700i&display=swap");
@font-face {
  font-family: 'icomoon';
  src: url("/static_1/fonts/iconmoon/icomoon.eot?ytm7g4");
  src: url("/static_1/fonts/iconmoon/icomoon.eot?ytm7g4#iefix") format("embedded-opentype"), url("/static_1/fonts/iconmoon/icomoon.ttf?ytm7g4") format("truetype"), url("/static_1/fonts/iconmoon/icomoon.woff?ytm7g4") format("woff"), url("/static_1/fonts/iconmoon/icomoon.svg?ytm7g4#icomoon") format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
/* line 13, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* line 27, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-terrace:before, [class*=" icon-"].icon-terrace:before {
  font-family: 'icomoon';
  content: "\ea52";
}
/* line 31, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-ico-hotelmanager:before, [class*=" icon-"].icon-ico-hotelmanager:before {
  font-family: 'icomoon';
  content: "\ea4c";
}
/* line 35, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-ico-paritymaker:before, [class*=" icon-"].icon-ico-paritymaker:before {
  font-family: 'icomoon';
  content: "\ea4d";
}
/* line 39, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-ico-priceseeker:before, [class*=" icon-"].icon-ico-priceseeker:before {
  font-family: 'icomoon';
  content: "\ea4e";
}
/* line 43, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-ico-ratecheck:before, [class*=" icon-"].icon-ico-ratecheck:before {
  font-family: 'icomoon';
  content: "\ea4f";
}
/* line 47, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-ico-rescueseeker:before, [class*=" icon-"].icon-ico-rescueseeker:before {
  font-family: 'icomoon';
  content: "\ea50";
}
/* line 51, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-ico-reviewseeker:before, [class*=" icon-"].icon-ico-reviewseeker:before {
  font-family: 'icomoon';
  content: "\ea51";
}
/* line 55, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-couponlong:before, [class*=" icon-"].icon-couponlong:before {
  font-family: 'icomoon';
  content: "\ea4a";
}
/* line 59, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-coupon:before, [class*=" icon-"].icon-coupon:before {
  font-family: 'icomoon';
  content: "\ea4b";
}
/* line 63, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-cocktel:before, [class*=" icon-"].icon-cocktel:before {
  font-family: 'icomoon';
  content: "\ea46";
}
/* line 67, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-gafas:before, [class*=" icon-"].icon-gafas:before {
  font-family: 'icomoon';
  content: "\ea47";
}
/* line 71, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-pelota:before, [class*=" icon-"].icon-pelota:before {
  font-family: 'icomoon';
  content: "\ea48";
}
/* line 75, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-piscina:before, [class*=" icon-"].icon-piscina:before {
  font-family: 'icomoon';
  content: "\ea49";
}
/* line 79, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-email2:before, [class*=" icon-"].icon-email2:before {
  font-family: 'icomoon';
  content: "\ea43";
}
/* line 83, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-luggage2:before, [class*=" icon-"].icon-luggage2:before {
  font-family: 'icomoon';
  content: "\ea44";
}
/* line 87, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-nodisturb:before, [class*=" icon-"].icon-nodisturb:before {
  font-family: 'icomoon';
  content: "\ea45";
}
/* line 91, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-percent:before, [class*=" icon-"].icon-percent:before {
  font-family: 'icomoon';
  content: "\61";
}
/* line 95, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-bed:before, [class*=" icon-"].icon-bed:before {
  font-family: 'icomoon';
  content: "\62";
}
/* line 99, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-dots:before, [class*=" icon-"].icon-dots:before {
  font-family: 'icomoon';
  content: "\63";
}
/* line 103, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-buffet:before, [class*=" icon-"].icon-buffet:before {
  font-family: 'icomoon';
  content: "\e900";
}
/* line 107, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-zen:before, [class*=" icon-"].icon-zen:before {
  font-family: 'icomoon';
  content: "\e901";
}
/* line 111, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-drink:before, [class*=" icon-"].icon-drink:before {
  font-family: 'icomoon';
  content: "\e902";
}
/* line 115, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-bike:before, [class*=" icon-"].icon-bike:before {
  font-family: 'icomoon';
  content: "\e903";
}
/* line 119, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-celiac:before, [class*=" icon-"].icon-celiac:before {
  font-family: 'icomoon';
  content: "\e904";
}
/* line 123, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-chart:before, [class*=" icon-"].icon-chart:before {
  font-family: 'icomoon';
  content: "\e905";
}
/* line 127, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-chips:before, [class*=" icon-"].icon-chips:before {
  font-family: 'icomoon';
  content: "\e906";
}
/* line 131, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-clock:before, [class*=" icon-"].icon-clock:before {
  font-family: 'icomoon';
  content: "\e907";
}
/* line 135, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-download:before, [class*=" icon-"].icon-download:before {
  font-family: 'icomoon';
  content: "\e908";
}
/* line 139, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-friends:before, [class*=" icon-"].icon-friends:before {
  font-family: 'icomoon';
  content: "\e909";
}
/* line 143, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-group:before, [class*=" icon-"].icon-group:before {
  font-family: 'icomoon';
  content: "\e90a";
}
/* line 147, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-headset:before, [class*=" icon-"].icon-headset:before {
  font-family: 'icomoon';
  content: "\e90b";
}
/* line 151, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-hipster:before, [class*=" icon-"].icon-hipster:before {
  font-family: 'icomoon';
  content: "\e90c";
}
/* line 155, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-lamp:before, [class*=" icon-"].icon-lamp:before {
  font-family: 'icomoon';
  content: "\e90d";
}
/* line 159, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-like:before, [class*=" icon-"].icon-like:before {
  font-family: 'icomoon';
  content: "\e90e";
}
/* line 163, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-map:before, [class*=" icon-"].icon-map:before {
  font-family: 'icomoon';
  content: "\e90f";
}
/* line 167, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-men:before, [class*=" icon-"].icon-men:before {
  font-family: 'icomoon';
  content: "\e910";
}
/* line 171, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-monument:before, [class*=" icon-"].icon-monument:before {
  font-family: 'icomoon';
  content: "\e911";
}
/* line 175, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-new:before, [class*=" icon-"].icon-new:before {
  font-family: 'icomoon';
  content: "\e912";
}
/* line 179, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-pig:before, [class*=" icon-"].icon-pig:before {
  font-family: 'icomoon';
  content: "\e913";
}
/* line 183, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-pdf:before, [class*=" icon-"].icon-pdf:before {
  font-family: 'icomoon';
  content: "\e914";
}
/* line 187, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-play:before, [class*=" icon-"].icon-play:before {
  font-family: 'icomoon';
  content: "\e915";
}
/* line 191, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-row:before, [class*=" icon-"].icon-row:before {
  font-family: 'icomoon';
  content: "\e916";
}
/* line 195, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-uniE917:before, [class*=" icon-"].icon-uniE917:before {
  font-family: 'icomoon';
  content: "\e917";
}
/* line 199, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-uniE918:before, [class*=" icon-"].icon-uniE918:before {
  font-family: 'icomoon';
  content: "\e918";
}
/* line 203, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-uniE919:before, [class*=" icon-"].icon-uniE919:before {
  font-family: 'icomoon';
  content: "\e919";
}
/* line 207, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-uniE91A:before, [class*=" icon-"].icon-uniE91A:before {
  font-family: 'icomoon';
  content: "\e91a";
}
/* line 211, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-uniE91B:before, [class*=" icon-"].icon-uniE91B:before {
  font-family: 'icomoon';
  content: "\e91b";
}
/* line 215, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-uniE91C:before, [class*=" icon-"].icon-uniE91C:before {
  font-family: 'icomoon';
  content: "\e91c";
}
/* line 219, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-sea:before, [class*=" icon-"].icon-sea:before {
  font-family: 'icomoon';
  content: "\e91d";
}
/* line 223, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-slide:before, [class*=" icon-"].icon-slide:before {
  font-family: 'icomoon';
  content: "\e91e";
}
/* line 227, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-smile:before, [class*=" icon-"].icon-smile:before {
  font-family: 'icomoon';
  content: "\e91f";
}
/* line 231, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-tick:before, [class*=" icon-"].icon-tick:before {
  font-family: 'icomoon';
  content: "\e920";
}
/* line 235, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-ticket:before, [class*=" icon-"].icon-ticket:before {
  font-family: 'icomoon';
  content: "\e921";
}
/* line 239, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-trees:before, [class*=" icon-"].icon-trees:before {
  font-family: 'icomoon';
  content: "\e922";
}
/* line 243, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-upgrade:before, [class*=" icon-"].icon-upgrade:before {
  font-family: 'icomoon';
  content: "\e923";
}
/* line 247, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-watergame:before, [class*=" icon-"].icon-watergame:before {
  font-family: 'icomoon';
  content: "\e924";
}
/* line 251, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-wedding:before, [class*=" icon-"].icon-wedding:before {
  font-family: 'icomoon';
  content: "\e925";
}
/* line 255, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-basketball:before, [class*=" icon-"].icon-basketball:before {
  font-family: 'icomoon';
  content: "\e926";
}
/* line 259, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-books:before, [class*=" icon-"].icon-books:before {
  font-family: 'icomoon';
  content: "\e927";
}
/* line 263, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-calendar:before, [class*=" icon-"].icon-calendar:before {
  font-family: 'icomoon';
  content: "\e928";
}
/* line 267, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-candles:before, [class*=" icon-"].icon-candles:before {
  font-family: 'icomoon';
  content: "\e929";
}
/* line 271, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-coins:before, [class*=" icon-"].icon-coins:before {
  font-family: 'icomoon';
  content: "\e92a";
}
/* line 275, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-cup:before, [class*=" icon-"].icon-cup:before {
  font-family: 'icomoon';
  content: "\e92b";
}
/* line 279, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-cutlery:before, [class*=" icon-"].icon-cutlery:before {
  font-family: 'icomoon';
  content: "\e92c";
}
/* line 283, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-dice:before, [class*=" icon-"].icon-dice:before {
  font-family: 'icomoon';
  content: "\e92d";
}
/* line 287, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-doc:before, [class*=" icon-"].icon-doc:before {
  font-family: 'icomoon';
  content: "\e92e";
}
/* line 291, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-email:before, [class*=" icon-"].icon-email:before {
  font-family: 'icomoon';
  content: "\e92f";
}
/* line 295, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-euro:before, [class*=" icon-"].icon-euro:before {
  font-family: 'icomoon';
  content: "\e930";
}
/* line 299, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-info:before, [class*=" icon-"].icon-info:before {
  font-family: 'icomoon';
  content: "\e931";
}
/* line 303, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-light:before, [class*=" icon-"].icon-light:before {
  font-family: 'icomoon';
  content: "\e932";
}
/* line 307, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-night:before, [class*=" icon-"].icon-night:before {
  font-family: 'icomoon';
  content: "\e933";
}
/* line 311, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-pet:before, [class*=" icon-"].icon-pet:before {
  font-family: 'icomoon';
  content: "\e934";
}
/* line 315, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-shell:before, [class*=" icon-"].icon-shell:before {
  font-family: 'icomoon';
  content: "\e935";
}
/* line 319, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-sofa:before, [class*=" icon-"].icon-sofa:before {
  font-family: 'icomoon';
  content: "\e936";
}
/* line 323, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-star:before, [class*=" icon-"].icon-star:before {
  font-family: 'icomoon';
  content: "\e937";
}
/* line 327, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-user:before, [class*=" icon-"].icon-user:before {
  font-family: 'icomoon';
  content: "\e938";
}
/* line 331, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-wii:before, [class*=" icon-"].icon-wii:before {
  font-family: 'icomoon';
  content: "\e939";
}
/* line 335, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-ball:before, [class*=" icon-"].icon-ball:before {
  font-family: 'icomoon';
  content: "\e93a";
}
/* line 339, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-booking:before, [class*=" icon-"].icon-booking:before {
  font-family: 'icomoon';
  content: "\e93b";
}
/* line 343, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-cleanset:before, [class*=" icon-"].icon-cleanset:before {
  font-family: 'icomoon';
  content: "\e93c";
}
/* line 347, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-comment:before, [class*=" icon-"].icon-comment:before {
  font-family: 'icomoon';
  content: "\e93d";
}
/* line 351, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-ethernet:before, [class*=" icon-"].icon-ethernet:before {
  font-family: 'icomoon';
  content: "\e93e";
}
/* line 355, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-eye:before, [class*=" icon-"].icon-eye:before {
  font-family: 'icomoon';
  content: "\e93f";
}
/* line 359, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-feet:before, [class*=" icon-"].icon-feet:before {
  font-family: 'icomoon';
  content: "\e940";
}
/* line 363, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-fridge:before, [class*=" icon-"].icon-fridge:before {
  font-family: 'icomoon';
  content: "\e941";
}
/* line 367, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-hairdrier:before, [class*=" icon-"].icon-hairdrier:before {
  font-family: 'icomoon';
  content: "\e942";
}
/* line 371, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-handicap:before, [class*=" icon-"].icon-handicap:before {
  font-family: 'icomoon';
  content: "\e943";
}
/* line 375, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-iron:before, [class*=" icon-"].icon-iron:before {
  font-family: 'icomoon';
  content: "\e944";
}
/* line 379, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-key:before, [class*=" icon-"].icon-key:before {
  font-family: 'icomoon';
  content: "\e945";
}
/* line 383, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-lift:before, [class*=" icon-"].icon-lift:before {
  font-family: 'icomoon';
  content: "\e946";
}
/* line 387, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-mapmarker:before, [class*=" icon-"].icon-mapmarker:before {
  font-family: 'icomoon';
  content: "\e947";
}
/* line 391, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-mask:before, [class*=" icon-"].icon-mask:before {
  font-family: 'icomoon';
  content: "\e948";
}
/* line 395, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-mouse:before, [class*=" icon-"].icon-mouse:before {
  font-family: 'icomoon';
  content: "\e949";
}
/* line 399, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-movie:before, [class*=" icon-"].icon-movie:before {
  font-family: 'icomoon';
  content: "\e94a";
}
/* line 403, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-mug:before, [class*=" icon-"].icon-mug:before {
  font-family: 'icomoon';
  content: "\e94b";
}
/* line 407, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-plug:before, [class*=" icon-"].icon-plug:before {
  font-family: 'icomoon';
  content: "\e94c";
}
/* line 411, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-plus:before, [class*=" icon-"].icon-plus:before {
  font-family: 'icomoon';
  content: "\e94d";
}
/* line 415, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-printer:before, [class*=" icon-"].icon-printer:before {
  font-family: 'icomoon';
  content: "\e94e";
}
/* line 419, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-sack:before, [class*=" icon-"].icon-sack:before {
  font-family: 'icomoon';
  content: "\e94f";
}
/* line 423, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-shower:before, [class*=" icon-"].icon-shower:before {
  font-family: 'icomoon';
  content: "\e950";
}
/* line 427, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-solarium:before, [class*=" icon-"].icon-solarium:before {
  font-family: 'icomoon';
  content: "\e951";
}
/* line 431, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-tenis:before, [class*=" icon-"].icon-tenis:before {
  font-family: 'icomoon';
  content: "\e952";
}
/* line 435, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-tv:before, [class*=" icon-"].icon-tv:before {
  font-family: 'icomoon';
  content: "\e953";
}
/* line 439, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-window:before, [class*=" icon-"].icon-window:before {
  font-family: 'icomoon';
  content: "\e954";
}
/* line 443, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-apple:before, [class*=" icon-"].icon-apple:before {
  font-family: 'icomoon';
  content: "\e955";
}
/* line 447, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-bathrobe:before, [class*=" icon-"].icon-bathrobe:before {
  font-family: 'icomoon';
  content: "\e956";
}
/* line 451, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-bell:before, [class*=" icon-"].icon-bell:before {
  font-family: 'icomoon';
  content: "\e957";
}
/* line 455, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-building:before, [class*=" icon-"].icon-building:before {
  font-family: 'icomoon';
  content: "\e958";
}
/* line 459, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-car:before, [class*=" icon-"].icon-car:before {
  font-family: 'icomoon';
  content: "\e959";
}
/* line 463, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-cigar:before, [class*=" icon-"].icon-cigar:before {
  font-family: 'icomoon';
  content: "\e95a";
}
/* line 467, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-comments:before, [class*=" icon-"].icon-comments:before {
  font-family: 'icomoon';
  content: "\e95b";
}
/* line 471, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-coolheart:before, [class*=" icon-"].icon-coolheart:before {
  font-family: 'icomoon';
  content: "\e95c";
}
/* line 475, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-cupboard:before, [class*=" icon-"].icon-cupboard:before {
  font-family: 'icomoon';
  content: "\e95d";
}
/* line 479, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-dimensions:before, [class*=" icon-"].icon-dimensions:before {
  font-family: 'icomoon';
  content: "\e95e";
}
/* line 483, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-family:before, [class*=" icon-"].icon-family:before {
  font-family: 'icomoon';
  content: "\e95f";
}
/* line 487, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-flattv:before, [class*=" icon-"].icon-flattv:before {
  font-family: 'icomoon';
  content: "\e960";
}
/* line 491, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-formaluser:before, [class*=" icon-"].icon-formaluser:before {
  font-family: 'icomoon';
  content: "\e961";
}
/* line 495, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-guarantee:before, [class*=" icon-"].icon-guarantee:before {
  font-family: 'icomoon';
  content: "\e962";
}
/* line 499, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-gift:before, [class*=" icon-"].icon-gift:before {
  font-family: 'icomoon';
  content: "\e963";
}
/* line 503, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-lock:before, [class*=" icon-"].icon-lock:before {
  font-family: 'icomoon';
  content: "\e964";
}
/* line 507, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-movie2:before, [class*=" icon-"].icon-movie2:before {
  font-family: 'icomoon';
  content: "\e965";
}
/* line 511, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-picasa:before, [class*=" icon-"].icon-picasa:before {
  font-family: 'icomoon';
  content: "\e966";
}
/* line 515, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-roulette:before, [class*=" icon-"].icon-roulette:before {
  font-family: 'icomoon';
  content: "\e967";
}
/* line 519, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-sauna:before, [class*=" icon-"].icon-sauna:before {
  font-family: 'icomoon';
  content: "\e968";
}
/* line 523, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-shower2:before, [class*=" icon-"].icon-shower2:before {
  font-family: 'icomoon';
  content: "\e969";
}
/* line 527, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-singlebed:before, [class*=" icon-"].icon-singlebed:before {
  font-family: 'icomoon';
  content: "\e96a";
}
/* line 531, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-ski:before, [class*=" icon-"].icon-ski:before {
  font-family: 'icomoon';
  content: "\e96b";
}
/* line 535, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-smartphone:before, [class*=" icon-"].icon-smartphone:before {
  font-family: 'icomoon';
  content: "\e96c";
}
/* line 539, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-student:before, [class*=" icon-"].icon-student:before {
  font-family: 'icomoon';
  content: "\e96d";
}
/* line 543, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-thermometer:before, [class*=" icon-"].icon-thermometer:before {
  font-family: 'icomoon';
  content: "\e96e";
}
/* line 547, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-washer:before, [class*=" icon-"].icon-washer:before {
  font-family: 'icomoon';
  content: "\e96f";
}
/* line 551, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-drinks:before, [class*=" icon-"].icon-drinks:before {
  font-family: 'icomoon';
  content: "\e970";
}
/* line 555, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-drinks2:before, [class*=" icon-"].icon-drinks2:before {
  font-family: 'icomoon';
  content: "\e971";
}
/* line 559, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-airconditioner:before, [class*=" icon-"].icon-airconditioner:before {
  font-family: 'icomoon';
  content: "\e972";
}
/* line 563, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-arrowdown:before, [class*=" icon-"].icon-arrowdown:before {
  font-family: 'icomoon';
  content: "\e973";
}
/* line 567, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-arrowleft:before, [class*=" icon-"].icon-arrowleft:before {
  font-family: 'icomoon';
  content: "\e974";
}
/* line 571, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-arrowright:before, [class*=" icon-"].icon-arrowright:before {
  font-family: 'icomoon';
  content: "\e975";
}
/* line 575, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-arrowup:before, [class*=" icon-"].icon-arrowup:before {
  font-family: 'icomoon';
  content: "\e976";
}
/* line 579, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-bag:before, [class*=" icon-"].icon-bag:before {
  font-family: 'icomoon';
  content: "\e977";
}
/* line 583, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-bike2:before, [class*=" icon-"].icon-bike2:before {
  font-family: 'icomoon';
  content: "\e978";
}
/* line 587, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-biker:before, [class*=" icon-"].icon-biker:before {
  font-family: 'icomoon';
  content: "\e979";
}
/* line 591, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-briefcase:before, [class*=" icon-"].icon-briefcase:before {
  font-family: 'icomoon';
  content: "\e97a";
}
/* line 595, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-card:before, [class*=" icon-"].icon-card:before {
  font-family: 'icomoon';
  content: "\e97b";
}
/* line 599, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-cocktail:before, [class*=" icon-"].icon-cocktail:before {
  font-family: 'icomoon';
  content: "\e97c";
}
/* line 603, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-cooker:before, [class*=" icon-"].icon-cooker:before {
  font-family: 'icomoon';
  content: "\e97d";
}
/* line 607, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-drop:before, [class*=" icon-"].icon-drop:before {
  font-family: 'icomoon';
  content: "\e97e";
}
/* line 611, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-gym:before, [class*=" icon-"].icon-gym:before {
  font-family: 'icomoon';
  content: "\e97f";
}
/* line 615, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-info2:before, [class*=" icon-"].icon-info2:before {
  font-family: 'icomoon';
  content: "\e980";
}
/* line 619, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-massage:before, [class*=" icon-"].icon-massage:before {
  font-family: 'icomoon';
  content: "\e981";
}
/* line 623, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-moon:before, [class*=" icon-"].icon-moon:before {
  font-family: 'icomoon';
  content: "\e982";
}
/* line 627, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-music:before, [class*=" icon-"].icon-music:before {
  font-family: 'icomoon';
  content: "\e983";
}
/* line 631, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-news:before, [class*=" icon-"].icon-news:before {
  font-family: 'icomoon';
  content: "\e984";
}
/* line 635, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-nosmoke:before, [class*=" icon-"].icon-nosmoke:before {
  font-family: 'icomoon';
  content: "\e985";
}
/* line 639, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-parking:before, [class*=" icon-"].icon-parking:before {
  font-family: 'icomoon';
  content: "\e986";
}
/* line 643, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-phone1:before, [class*=" icon-"].icon-phone1:before {
  font-family: 'icomoon';
  content: "\e987";
}
/* line 647, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-phone2:before, [class*=" icon-"].icon-phone2:before {
  font-family: 'icomoon';
  content: "\e988";
}
/* line 651, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-pictures:before, [class*=" icon-"].icon-pictures:before {
  font-family: 'icomoon';
  content: "\e989";
}
/* line 655, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-plane:before, [class*=" icon-"].icon-plane:before {
  font-family: 'icomoon';
  content: "\e98a";
}
/* line 659, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-shield:before, [class*=" icon-"].icon-shield:before {
  font-family: 'icomoon';
  content: "\e98b";
}
/* line 663, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-spa:before, [class*=" icon-"].icon-spa:before {
  font-family: 'icomoon';
  content: "\e98c";
}
/* line 667, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-sun:before, [class*=" icon-"].icon-sun:before {
  font-family: 'icomoon';
  content: "\e98d";
}
/* line 671, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-ball2:before, [class*=" icon-"].icon-ball2:before {
  font-family: 'icomoon';
  content: "\e98e";
}
/* line 675, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-bubbles:before, [class*=" icon-"].icon-bubbles:before {
  font-family: 'icomoon';
  content: "\e98f";
}
/* line 679, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-cot:before, [class*=" icon-"].icon-cot:before {
  font-family: 'icomoon';
  content: "\e990";
}
/* line 683, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-cutlery2:before, [class*=" icon-"].icon-cutlery2:before {
  font-family: 'icomoon';
  content: "\e991";
}
/* line 687, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-golfplayer:before, [class*=" icon-"].icon-golfplayer:before {
  font-family: 'icomoon';
  content: "\e992";
}
/* line 691, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-heart:before, [class*=" icon-"].icon-heart:before {
  font-family: 'icomoon';
  content: "\e993";
}
/* line 695, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-towels:before, [class*=" icon-"].icon-towels:before {
  font-family: 'icomoon';
  content: "\e994";
}
/* line 699, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-tree:before, [class*=" icon-"].icon-tree:before {
  font-family: 'icomoon';
  content: "\e995";
}
/* line 703, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-wifi:before, [class*=" icon-"].icon-wifi:before {
  font-family: 'icomoon';
  content: "\e996";
}
/* line 707, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-alarmclock:before, [class*=" icon-"].icon-alarmclock:before {
  font-family: 'icomoon';
  content: "\e997";
}
/* line 711, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-amenities:before, [class*=" icon-"].icon-amenities:before {
  font-family: 'icomoon';
  content: "\e998";
}
/* line 715, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-astronomy:before, [class*=" icon-"].icon-astronomy:before {
  font-family: 'icomoon';
  content: "\e999";
}
/* line 719, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-barbecue:before, [class*=" icon-"].icon-barbecue:before {
  font-family: 'icomoon';
  content: "\e99a";
}
/* line 723, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-bells:before, [class*=" icon-"].icon-bells:before {
  font-family: 'icomoon';
  content: "\e99b";
}
/* line 727, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-bottle:before, [class*=" icon-"].icon-bottle:before {
  font-family: 'icomoon';
  content: "\e99c";
}
/* line 731, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-bottle2:before, [class*=" icon-"].icon-bottle2:before {
  font-family: 'icomoon';
  content: "\e99d";
}
/* line 735, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-breakfast:before, [class*=" icon-"].icon-breakfast:before {
  font-family: 'icomoon';
  content: "\e99e";
}
/* line 739, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-broom:before, [class*=" icon-"].icon-broom:before {
  font-family: 'icomoon';
  content: "\e99f";
}
/* line 743, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-buffet2:before, [class*=" icon-"].icon-buffet2:before {
  font-family: 'icomoon';
  content: "\e9a0";
}
/* line 747, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-calendar2:before, [class*=" icon-"].icon-calendar2:before {
  font-family: 'icomoon';
  content: "\e9a1";
}
/* line 751, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-camera:before, [class*=" icon-"].icon-camera:before {
  font-family: 'icomoon';
  content: "\e9a2";
}
/* line 755, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-camera2:before, [class*=" icon-"].icon-camera2:before {
  font-family: 'icomoon';
  content: "\e9a3";
}
/* line 759, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-caravan:before, [class*=" icon-"].icon-caravan:before {
  font-family: 'icomoon';
  content: "\e9a4";
}
/* line 763, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-champagne:before, [class*=" icon-"].icon-champagne:before {
  font-family: 'icomoon';
  content: "\e9a5";
}
/* line 767, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-chocolate:before, [class*=" icon-"].icon-chocolate:before {
  font-family: 'icomoon';
  content: "\e9a6";
}
/* line 771, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-christmasbauble:before, [class*=" icon-"].icon-christmasbauble:before {
  font-family: 'icomoon';
  content: "\e9a7";
}
/* line 775, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-christmasbauble2:before, [class*=" icon-"].icon-christmasbauble2:before {
  font-family: 'icomoon';
  content: "\e9a8";
}
/* line 779, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-christmastree:before, [class*=" icon-"].icon-christmastree:before {
  font-family: 'icomoon';
  content: "\e9a9";
}
/* line 783, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-christmastree2:before, [class*=" icon-"].icon-christmastree2:before {
  font-family: 'icomoon';
  content: "\e9aa";
}
/* line 787, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-chronometer:before, [class*=" icon-"].icon-chronometer:before {
  font-family: 'icomoon';
  content: "\e9ab";
}
/* line 791, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-clic:before, [class*=" icon-"].icon-clic:before {
  font-family: 'icomoon';
  content: "\e9ac";
}
/* line 795, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-cocktail2:before, [class*=" icon-"].icon-cocktail2:before {
  font-family: 'icomoon';
  content: "\e9ad";
}
/* line 799, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-coffee:before, [class*=" icon-"].icon-coffee:before {
  font-family: 'icomoon';
  content: "\e9ae";
}
/* line 803, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-coffee2:before, [class*=" icon-"].icon-coffee2:before {
  font-family: 'icomoon';
  content: "\e9af";
}
/* line 807, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-comment2:before, [class*=" icon-"].icon-comment2:before {
  font-family: 'icomoon';
  content: "\e9b0";
}
/* line 811, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-comment3:before, [class*=" icon-"].icon-comment3:before {
  font-family: 'icomoon';
  content: "\e9b1";
}
/* line 815, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-comments2:before, [class*=" icon-"].icon-comments2:before {
  font-family: 'icomoon';
  content: "\e9b2";
}
/* line 819, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-cushions:before, [class*=" icon-"].icon-cushions:before {
  font-family: 'icomoon';
  content: "\e9b3";
}
/* line 823, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-cushions2:before, [class*=" icon-"].icon-cushions2:before {
  font-family: 'icomoon';
  content: "\e9b4";
}
/* line 827, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-deaf:before, [class*=" icon-"].icon-deaf:before {
  font-family: 'icomoon';
  content: "\e9b5";
}
/* line 831, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-design:before, [class*=" icon-"].icon-design:before {
  font-family: 'icomoon';
  content: "\e9b6";
}
/* line 835, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-desktop:before, [class*=" icon-"].icon-desktop:before {
  font-family: 'icomoon';
  content: "\e9b7";
}
/* line 839, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-dishes:before, [class*=" icon-"].icon-dishes:before {
  font-family: 'icomoon';
  content: "\e9b8";
}
/* line 843, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-dollar:before, [class*=" icon-"].icon-dollar:before {
  font-family: 'icomoon';
  content: "\e9b9";
}
/* line 847, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-download2:before, [class*=" icon-"].icon-download2:before {
  font-family: 'icomoon';
  content: "\e9ba";
}
/* line 851, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-family2:before, [class*=" icon-"].icon-family2:before {
  font-family: 'icomoon';
  content: "\e9bb";
}
/* line 855, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-fireworks:before, [class*=" icon-"].icon-fireworks:before {
  font-family: 'icomoon';
  content: "\e9bc";
}
/* line 859, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-flipflops:before, [class*=" icon-"].icon-flipflops:before {
  font-family: 'icomoon';
  content: "\e9bd";
}
/* line 863, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-friends2:before, [class*=" icon-"].icon-friends2:before {
  font-family: 'icomoon';
  content: "\e9be";
}
/* line 867, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-fruit:before, [class*=" icon-"].icon-fruit:before {
  font-family: 'icomoon';
  content: "\e9bf";
}
/* line 871, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-gender:before, [class*=" icon-"].icon-gender:before {
  font-family: 'icomoon';
  content: "\e9c0";
}
/* line 875, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-gift2:before, [class*=" icon-"].icon-gift2:before {
  font-family: 'icomoon';
  content: "\e9c1";
}
/* line 879, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-gifts:before, [class*=" icon-"].icon-gifts:before {
  font-family: 'icomoon';
  content: "\e9c2";
}
/* line 883, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-gold:before, [class*=" icon-"].icon-gold:before {
  font-family: 'icomoon';
  content: "\e9c3";
}
/* line 887, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-hairdrier2:before, [class*=" icon-"].icon-hairdrier2:before {
  font-family: 'icomoon';
  content: "\e9c4";
}
/* line 891, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-hammock:before, [class*=" icon-"].icon-hammock:before {
  font-family: 'icomoon';
  content: "\e9c5";
}
/* line 895, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-hammock2:before, [class*=" icon-"].icon-hammock2:before {
  font-family: 'icomoon';
  content: "\e9c6";
}
/* line 899, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-heart2:before, [class*=" icon-"].icon-heart2:before {
  font-family: 'icomoon';
  content: "\e9c7";
}
/* line 903, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-homepage:before, [class*=" icon-"].icon-homepage:before {
  font-family: 'icomoon';
  content: "\e9c8";
}
/* line 907, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-hotel:before, [class*=" icon-"].icon-hotel:before {
  font-family: 'icomoon';
  content: "\e9c9";
}
/* line 911, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-ice:before, [class*=" icon-"].icon-ice:before {
  font-family: 'icomoon';
  content: "\e9ca";
}
/* line 915, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-kettle:before, [class*=" icon-"].icon-kettle:before {
  font-family: 'icomoon';
  content: "\e9cb";
}
/* line 919, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-kitchen:before, [class*=" icon-"].icon-kitchen:before {
  font-family: 'icomoon';
  content: "\e9cc";
}
/* line 923, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-latecheckout:before, [class*=" icon-"].icon-latecheckout:before {
  font-family: 'icomoon';
  content: "\e9cd";
}
/* line 927, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-luggage:before, [class*=" icon-"].icon-luggage:before {
  font-family: 'icomoon';
  content: "\e9ce";
}
/* line 931, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-meeting:before, [class*=" icon-"].icon-meeting:before {
  font-family: 'icomoon';
  content: "\e9cf";
}
/* line 935, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-microwave:before, [class*=" icon-"].icon-microwave:before {
  font-family: 'icomoon';
  content: "\e9d0";
}
/* line 939, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-monument2:before, [class*=" icon-"].icon-monument2:before {
  font-family: 'icomoon';
  content: "\e9d1";
}
/* line 943, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-mouse1:before, [class*=" icon-"].icon-mouse1:before {
  font-family: 'icomoon';
  content: "\e9d2";
}
/* line 947, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-nani:before, [class*=" icon-"].icon-nani:before {
  font-family: 'icomoon';
  content: "\e9d3";
}
/* line 951, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-offer2:before, [class*=" icon-"].icon-offer2:before {
  font-family: 'icomoon';
  content: "\e9d4";
}
/* line 955, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-offers:before, [class*=" icon-"].icon-offers:before {
  font-family: 'icomoon';
  content: "\e9d5";
}
/* line 959, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-percent2:before, [class*=" icon-"].icon-percent2:before {
  font-family: 'icomoon';
  content: "\e9d6";
}
/* line 963, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-player:before, [class*=" icon-"].icon-player:before {
  font-family: 'icomoon';
  content: "\e9d7";
}
/* line 967, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-romantic:before, [class*=" icon-"].icon-romantic:before {
  font-family: 'icomoon';
  content: "\e9d8";
}
/* line 971, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-roomservice:before, [class*=" icon-"].icon-roomservice:before {
  font-family: 'icomoon';
  content: "\e9d9";
}
/* line 975, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-santa:before, [class*=" icon-"].icon-santa:before {
  font-family: 'icomoon';
  content: "\e9da";
}
/* line 979, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-satellite:before, [class*=" icon-"].icon-satellite:before {
  font-family: 'icomoon';
  content: "\e9db";
}
/* line 983, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-sea2:before, [class*=" icon-"].icon-sea2:before {
  font-family: 'icomoon';
  content: "\e9dc";
}
/* line 987, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-settings:before, [class*=" icon-"].icon-settings:before {
  font-family: 'icomoon';
  content: "\e9dd";
}
/* line 991, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-shield2:before, [class*=" icon-"].icon-shield2:before {
  font-family: 'icomoon';
  content: "\e9de";
}
/* line 995, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-slippers:before, [class*=" icon-"].icon-slippers:before {
  font-family: 'icomoon';
  content: "\e9df";
}
/* line 999, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-snowflakeeps:before, [class*=" icon-"].icon-snowflakeeps:before {
  font-family: 'icomoon';
  content: "\e9e0";
}
/* line 1003, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-soap:before, [class*=" icon-"].icon-soap:before {
  font-family: 'icomoon';
  content: "\e9e1";
}
/* line 1007, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-sofa2:before, [class*=" icon-"].icon-sofa2:before {
  font-family: 'icomoon';
  content: "\e9e2";
}
/* line 1011, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-specialbreakfast:before, [class*=" icon-"].icon-specialbreakfast:before {
  font-family: 'icomoon';
  content: "\e9e3";
}
/* line 1015, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-specialfamily:before, [class*=" icon-"].icon-specialfamily:before {
  font-family: 'icomoon';
  content: "\e9e4";
}
/* line 1019, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-specialreception:before, [class*=" icon-"].icon-specialreception:before {
  font-family: 'icomoon';
  content: "\e9e5";
}
/* line 1023, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-specialshower:before, [class*=" icon-"].icon-specialshower:before {
  font-family: 'icomoon';
  content: "\e9e6";
}
/* line 1027, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-suit:before, [class*=" icon-"].icon-suit:before {
  font-family: 'icomoon';
  content: "\e9e7";
}
/* line 1031, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-sun2:before, [class*=" icon-"].icon-sun2:before {
  font-family: 'icomoon';
  content: "\e9e8";
}
/* line 1035, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-sun3:before, [class*=" icon-"].icon-sun3:before {
  font-family: 'icomoon';
  content: "\e9e9";
}
/* line 1039, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-tie:before, [class*=" icon-"].icon-tie:before {
  font-family: 'icomoon';
  content: "\e9ea";
}
/* line 1043, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-toaster:before, [class*=" icon-"].icon-toaster:before {
  font-family: 'icomoon';
  content: "\e9eb";
}
/* line 1047, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-toilet:before, [class*=" icon-"].icon-toilet:before {
  font-family: 'icomoon';
  content: "\e9ec";
}
/* line 1051, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-washer2:before, [class*=" icon-"].icon-washer2:before {
  font-family: 'icomoon';
  content: "\e9ed";
}
/* line 1055, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-waterpark:before, [class*=" icon-"].icon-waterpark:before {
  font-family: 'icomoon';
  content: "\e9ee";
}
/* line 1059, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-wine:before, [class*=" icon-"].icon-wine:before {
  font-family: 'icomoon';
  content: "\e9ef";
}
/* line 1063, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-world:before, [class*=" icon-"].icon-world:before {
  font-family: 'icomoon';
  content: "\e9f0";
}
/* line 1067, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-www:before, [class*=" icon-"].icon-www:before {
  font-family: 'icomoon';
  content: "\e9f1";
}
/* line 1071, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-adults:before, [class*=" icon-"].icon-adults:before {
  font-family: 'icomoon';
  content: "\e9f2";
}
/* line 1075, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-percentpig:before, [class*=" icon-"].icon-percentpig:before {
  font-family: 'icomoon';
  content: "\e9f3";
}
/* line 1079, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-specialwifi:before, [class*=" icon-"].icon-specialwifi:before {
  font-family: 'icomoon';
  content: "\e9f4";
}
/* line 1083, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-specialbuilding:before, [class*=" icon-"].icon-specialbuilding:before {
  font-family: 'icomoon';
  content: "\e9f5";
}
/* line 1087, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-speciallunch:before, [class*=" icon-"].icon-speciallunch:before {
  font-family: 'icomoon';
  content: "\e9f6";
}
/* line 1091, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-specialweb:before, [class*=" icon-"].icon-specialweb:before {
  font-family: 'icomoon';
  content: "\e9f7";
}
/* line 1095, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-specialbed:before, [class*=" icon-"].icon-specialbed:before {
  font-family: 'icomoon';
  content: "\e9f8";
}
/* line 1099, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-specialevents:before, [class*=" icon-"].icon-specialevents:before {
  font-family: 'icomoon';
  content: "\e9f9";
}
/* line 1103, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-specialpool:before, [class*=" icon-"].icon-specialpool:before {
  font-family: 'icomoon';
  content: "\e9fa";
}
/* line 1107, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-specialbeds:before, [class*=" icon-"].icon-specialbeds:before {
  font-family: 'icomoon';
  content: "\e9fb";
}
/* line 1111, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-specialcalendar:before, [class*=" icon-"].icon-specialcalendar:before {
  font-family: 'icomoon';
  content: "\e9fc";
}
/* line 1115, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-specialfitness:before, [class*=" icon-"].icon-specialfitness:before {
  font-family: 'icomoon';
  content: "\e9fd";
}
/* line 1119, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-speciallocation:before, [class*=" icon-"].icon-speciallocation:before {
  font-family: 'icomoon';
  content: "\e9fe";
}
/* line 1123, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-settings2:before, [class*=" icon-"].icon-settings2:before {
  font-family: 'icomoon';
  content: "\e9ff";
}
/* line 1127, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-nopets:before, [class*=" icon-"].icon-nopets:before {
  font-family: 'icomoon';
  content: "\ea00";
}
/* line 1131, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-videocamera:before, [class*=" icon-"].icon-videocamera:before {
  font-family: 'icomoon';
  content: "\ea01";
}
/* line 1135, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-window1:before, [class*=" icon-"].icon-window1:before {
  font-family: 'icomoon';
  content: "\ea02";
}
/* line 1139, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-offer:before, [class*=" icon-"].icon-offer:before {
  font-family: 'icomoon';
  content: "\ea03";
}
/* line 1143, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-save:before, [class*=" icon-"].icon-save:before {
  font-family: 'icomoon';
  content: "\ea04";
}
/* line 1147, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-plane2:before, [class*=" icon-"].icon-plane2:before {
  font-family: 'icomoon';
  content: "\ea05";
}
/* line 1151, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-longarrow:before, [class*=" icon-"].icon-longarrow:before {
  font-family: 'icomoon';
  content: "\ea06";
}
/* line 1155, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-paraty:before, [class*=" icon-"].icon-paraty:before {
  font-family: 'icomoon';
  content: "\ea07";
}
/* line 1159, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-horseshoe:before, [class*=" icon-"].icon-horseshoe:before {
  font-family: 'icomoon';
  content: "\ea08";
}
/* line 1163, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-balloons:before, [class*=" icon-"].icon-balloons:before {
  font-family: 'icomoon';
  content: "\ea09";
}
/* line 1167, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-tiger:before, [class*=" icon-"].icon-line-tiger:before {
  font-family: 'icomoon';
  content: "\ea0a";
}
/* line 1171, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-2drinks:before, [class*=" icon-"].icon-line-2drinks:before {
  font-family: 'icomoon';
  content: "\ea0b";
}
/* line 1175, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-bbq:before, [class*=" icon-"].icon-line-bbq:before {
  font-family: 'icomoon';
  content: "\ea0c";
}
/* line 1179, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-disco:before, [class*=" icon-"].icon-line-disco:before {
  font-family: 'icomoon';
  content: "\ea0d";
}
/* line 1183, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-pasta:before, [class*=" icon-"].icon-line-pasta:before {
  font-family: 'icomoon';
  content: "\ea0e";
}
/* line 1187, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-snack:before, [class*=" icon-"].icon-line-snack:before {
  font-family: 'icomoon';
  content: "\ea0f";
}
/* line 1191, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-archery:before, [class*=" icon-"].icon-line-archery:before {
  font-family: 'icomoon';
  content: "\ea10";
}
/* line 1195, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-football:before, [class*=" icon-"].icon-line-football:before {
  font-family: 'icomoon';
  content: "\ea11";
}
/* line 1199, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-gameboard:before, [class*=" icon-"].icon-line-gameboard:before {
  font-family: 'icomoon';
  content: "\ea12";
}
/* line 1203, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-golf:before, [class*=" icon-"].icon-line-golf:before {
  font-family: 'icomoon';
  content: "\ea13";
}
/* line 1207, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-hotbath:before, [class*=" icon-"].icon-line-hotbath:before {
  font-family: 'icomoon';
  content: "\ea14";
}
/* line 1211, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-hotpool:before, [class*=" icon-"].icon-line-hotpool:before {
  font-family: 'icomoon';
  content: "\ea15";
}
/* line 1215, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-iron:before, [class*=" icon-"].icon-line-iron:before {
  font-family: 'icomoon';
  content: "\ea16";
}
/* line 1219, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-jetshower:before, [class*=" icon-"].icon-line-jetshower:before {
  font-family: 'icomoon';
  content: "\ea17";
}
/* line 1223, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-massage:before, [class*=" icon-"].icon-line-massage:before {
  font-family: 'icomoon';
  content: "\ea18";
}
/* line 1227, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-petanque:before, [class*=" icon-"].icon-line-petanque:before {
  font-family: 'icomoon';
  content: "\ea19";
}
/* line 1231, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-phone:before, [class*=" icon-"].icon-line-phone:before {
  font-family: 'icomoon';
  content: "\ea1a";
}
/* line 1235, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-shooting:before, [class*=" icon-"].icon-line-shooting:before {
  font-family: 'icomoon';
  content: "\ea1b";
}
/* line 1239, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-volleyball:before, [class*=" icon-"].icon-line-volleyball:before {
  font-family: 'icomoon';
  content: "\ea1c";
}
/* line 1243, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-balloons:before, [class*=" icon-"].icon-line-balloons:before {
  font-family: 'icomoon';
  content: "\ea1d";
}
/* line 1247, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-bike:before, [class*=" icon-"].icon-line-bike:before {
  font-family: 'icomoon';
  content: "\ea1e";
}
/* line 1251, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-gift:before, [class*=" icon-"].icon-line-gift:before {
  font-family: 'icomoon';
  content: "\ea1f";
}
/* line 1255, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-jacuzzi:before, [class*=" icon-"].icon-line-jacuzzi:before {
  font-family: 'icomoon';
  content: "\ea20";
}
/* line 1259, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-mouse:before, [class*=" icon-"].icon-line-mouse:before {
  font-family: 'icomoon';
  content: "\ea21";
}
/* line 1263, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-movie:before, [class*=" icon-"].icon-line-movie:before {
  font-family: 'icomoon';
  content: "\ea22";
}
/* line 1267, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-playground:before, [class*=" icon-"].icon-line-playground:before {
  font-family: 'icomoon';
  content: "\ea23";
}
/* line 1271, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-playingcards:before, [class*=" icon-"].icon-line-playingcards:before {
  font-family: 'icomoon';
  content: "\ea24";
}
/* line 1275, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-shopping:before, [class*=" icon-"].icon-line-shopping:before {
  font-family: 'icomoon';
  content: "\ea25";
}
/* line 1279, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-shower:before, [class*=" icon-"].icon-line-shower:before {
  font-family: 'icomoon';
  content: "\ea26";
}
/* line 1283, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-sofa:before, [class*=" icon-"].icon-line-sofa:before {
  font-family: 'icomoon';
  content: "\ea27";
}
/* line 1287, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-washing:before, [class*=" icon-"].icon-line-washing:before {
  font-family: 'icomoon';
  content: "\ea28";
}
/* line 1291, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-bills:before, [class*=" icon-"].icon-line-bills:before {
  font-family: 'icomoon';
  content: "\ea29";
}
/* line 1295, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-car:before, [class*=" icon-"].icon-line-car:before {
  font-family: 'icomoon';
  content: "\ea2a";
}
/* line 1299, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-creditcard:before, [class*=" icon-"].icon-line-creditcard:before {
  font-family: 'icomoon';
  content: "\ea2b";
}
/* line 1303, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-drink:before, [class*=" icon-"].icon-line-drink:before {
  font-family: 'icomoon';
  content: "\ea2c";
}
/* line 1307, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-fruit:before, [class*=" icon-"].icon-line-fruit:before {
  font-family: 'icomoon';
  content: "\ea2d";
}
/* line 1311, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-lock:before, [class*=" icon-"].icon-line-lock:before {
  font-family: 'icomoon';
  content: "\ea2e";
}
/* line 1315, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-offer:before, [class*=" icon-"].icon-line-offer:before {
  font-family: 'icomoon';
  content: "\ea2f";
}
/* line 1319, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-spa:before, [class*=" icon-"].icon-line-spa:before {
  font-family: 'icomoon';
  content: "\ea30";
}
/* line 1323, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-tennis:before, [class*=" icon-"].icon-line-tennis:before {
  font-family: 'icomoon';
  content: "\ea31";
}
/* line 1327, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-aircon:before, [class*=" icon-"].icon-line-aircon:before {
  font-family: 'icomoon';
  content: "\ea32";
}
/* line 1331, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-hairdryer:before, [class*=" icon-"].icon-line-hairdryer:before {
  font-family: 'icomoon';
  content: "\ea33";
}
/* line 1335, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-kitchen:before, [class*=" icon-"].icon-line-kitchen:before {
  font-family: 'icomoon';
  content: "\ea34";
}
/* line 1339, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-tv:before, [class*=" icon-"].icon-line-tv:before {
  font-family: 'icomoon';
  content: "\ea35";
}
/* line 1343, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-wifi:before, [class*=" icon-"].icon-line-wifi:before {
  font-family: 'icomoon';
  content: "\ea36";
}
/* line 1347, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-clock:before, [class*=" icon-"].icon-line-clock:before {
  font-family: 'icomoon';
  content: "\ea37";
}
/* line 1351, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-lightning:before, [class*=" icon-"].icon-line-lightning:before {
  font-family: 'icomoon';
  content: "\ea38";
}
/* line 1355, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-piggybank:before, [class*=" icon-"].icon-line-piggybank:before {
  font-family: 'icomoon';
  content: "\ea39";
}
/* line 1359, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-pricetag:before, [class*=" icon-"].icon-line-pricetag:before {
  font-family: 'icomoon';
  content: "\ea3a";
}
/* line 1363, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-creditcard2:before, [class*=" icon-"].icon-line-creditcard2:before {
  font-family: 'icomoon';
  content: "\ea3b";
}
/* line 1367, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-360:before, [class*=" icon-"].icon-360:before {
  font-family: 'icomoon';
  content: "\ea3c";
}
/* line 1371, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-contactless:before, [class*=" icon-"].icon-contactless:before {
  font-family: 'icomoon';
  content: "\ea3d";
}
/* line 1375, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-umbrella:before, [class*=" icon-"].icon-line-umbrella:before {
  font-family: 'icomoon';
  content: "\ea3e";
}
/* line 1379, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-sun:before, [class*=" icon-"].icon-line-sun:before {
  font-family: 'icomoon';
  content: "\ea3f";
}
/* line 1383, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-slippers:before, [class*=" icon-"].icon-line-slippers:before {
  font-family: 'icomoon';
  content: "\ea40";
}
/* line 1387, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-mail:before, [class*=" icon-"].icon-line-mail:before {
  font-family: 'icomoon';
  content: "\ea41";
}
/* line 1391, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"].icon-line-comment:before, [class*=" icon-"].icon-line-comment:before {
  font-family: 'icomoon';
  content: "\ea42";
}

/* line 3, ../../../../../sass/plugins/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 8, ../../../../../sass/plugins/_mixins.scss */
.center_xy_before:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 13, ../../../../../sass/plugins/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 17, ../../../../../sass/plugins/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 21, ../../../../../sass/plugins/_mixins.scss */
.center_y_before:before {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 25, ../../../../../sass/plugins/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 30, ../../../../../sass/plugins/_mixins.scss */
.fs:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
}

/* line 110, ../../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:before, .icon-xcross:after {
  content: '';
  width: 100%;
  height: 2px;
  background: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 124, ../../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -moz-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 61, ../../../../../sass/plugins/_mixins.scss */
.display_flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* line 1, ../../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown {
  margin: 40px 20px 20px;
  text-align: center;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  bottom: 180px;
  left: 48% !important;
  color: white;
}
/* line 9, ../../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown div {
  display: inline-block;
  font-size: 16px;
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 0;
  padding-bottom: 0;
  text-transform: uppercase;
}
/* line 20, ../../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .days {
  font-weight: 600;
}
/* line 23, ../../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .date {
  display: block;
  font-size: 4.5rem;
}
/* line 28, ../../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .title_format {
  font-weight: 600;
}

@media screen and (max-width: 800px) {
  /* line 35, ../../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown {
    display: inline-block;
    width: 380px;
    top: 330px;
    left: 45% !important;
  }
  /* line 40, ../../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown div {
    font-size: 10px;
  }
  /* line 43, ../../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown .date {
    font-size: 2.5rem;
  }
}
/**
 * Owl Carousel v2.2.1
 * Copyright 2013-2017 David Deutsch
 * Licensed under  ()
 */
/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel, .owl-carousel .owl-item {
  -webkit-tap-highlight-color: transparent;
  position: relative;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel {
  display: none;
  width: 100%;
  z-index: 1;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage {
  position: relative;
  -ms-touch-action: pan-Y;
  -moz-backface-visibility: hidden;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage:after {
  content: ".";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage-outer {
  position: relative;
  overflow: hidden;
  -webkit-transform: translate3d(0, 0, 0);
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item, .owl-carousel .owl-wrapper {
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item {
  min-height: 1px;
  float: left;
  -webkit-backface-visibility: hidden;
  -webkit-touch-callout: none;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item img {
  display: block;
  width: 100%;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-dots.disabled, .owl-carousel .owl-nav.disabled {
  display: none;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.no-js .owl-carousel, .owl-carousel.owl-loaded {
  display: block;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-dot, .owl-carousel .owl-nav .owl-next, .owl-carousel .owl-nav .owl-prev {
  cursor: pointer;
  cursor: hand;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-loading {
  opacity: 0;
  display: block;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-hidden {
  opacity: 0;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-refresh .owl-item {
  visibility: hidden;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-drag .owl-item {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-grab {
  cursor: move;
  cursor: grab;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-rtl {
  direction: rtl;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-rtl .owl-item {
  float: right;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-animated-in {
  z-index: 0;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-animated-out {
  z-index: 1;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .fadeOut {
  animation-name: fadeOut;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-height {
  transition: height .5s ease-in-out;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item .owl-lazy {
  opacity: 0;
  transition: opacity .4s ease;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item img.owl-lazy {
  transform-style: preserve-3d;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-wrapper {
  position: relative;
  height: 100%;
  background: #000;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-play-icon {
  position: absolute;
  height: 80px;
  width: 80px;
  left: 50%;
  top: 50%;
  margin-left: -40px;
  margin-top: -40px;
  background: url(owl.video.play.png) no-repeat;
  cursor: pointer;
  z-index: 1;
  -webkit-backface-visibility: hidden;
  transition: transform .1s ease;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-play-icon:hover {
  -ms-transform: scale(1.3, 1.3);
  transform: scale(1.3, 1.3);
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-playing .owl-video-play-icon, .owl-carousel .owl-video-playing .owl-video-tn {
  display: none;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-tn {
  opacity: 0;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: opacity .4s ease;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-frame {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
}

/* line 3, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-hidden {
  display: none;
}

/* line 7, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-clearfix, .ui-icon {
  display: block;
}

/* line 11, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-hidden-accessible {
  position: absolute !important;
  clip: rect(1px 1px 1px 1px);
  clip: rect(1px, 1px, 1px, 1px);
}

/* line 17, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-reset {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  line-height: 1.3;
  text-decoration: none;
  font-size: 100%;
  list-style: none;
}

/* line 28, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

/* line 36, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
* html .ui-helper-clearfix {
  height: 1%;
}

/* line 40, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-zfix, .ui-widget-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* line 48, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-zfix {
  opacity: 0;
  filter: Alpha(Opacity=0);
}

/* line 53, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-disabled {
  cursor: default !important;
}

/* line 57, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon {
  text-indent: -99999px;
  overflow: hidden;
  background-repeat: no-repeat;
}

/* line 63, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget {
  font-family: Lucida Grande, Lucida Sans, Arial, sans-serif;
  font-size: 1.1em;
}

/* line 68, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget .ui-widget {
  font-size: 1em;
}

/* line 72, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget button, .ui-widget input, .ui-widget select, .ui-widget textarea {
  font-family: Lucida Grande, Lucida Sans, Arial, sans-serif;
  font-size: 1em;
}

/* line 76, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-content {
  border: 1px solid #a6c9e2;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_inset-hard_100_fcfdfd_1x100.png) 50% bottom repeat-x #fcfdfd;
  color: #222;
}

/* line 82, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-content a {
  color: #222;
}

/* line 86, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-header {
  border: 1px solid #4297d7;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_gloss-wave_55_5c9ccc_500x100.png) 50% 50% repeat-x #5c9ccc;
  color: #fff;
  font-weight: 700;
}

/* line 93, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-header a {
  color: #fff;
}

/* line 97, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  border: 1px solid #c5dbec;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_glass_85_dfeffc_1x400.png) 50% 50% repeat-x #dfeffc;
  font-weight: 700;
  color: #2e6e9e;
}

/* line 104, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-default a, .ui-state-default a:link, .ui-state-default a:visited {
  color: #2e6e9e;
  text-decoration: none;
}

/* line 109, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-focus, .ui-state-hover, .ui-widget-content .ui-state-focus, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-focus, .ui-widget-header .ui-state-hover {
  border: 1px solid #79b7e7;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_glass_75_d0e5f5_1x400.png) 50% 50% repeat-x #d0e5f5;
  font-weight: 700;
  color: #1d5987;
}

/* line 116, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-hover a, .ui-state-hover a:hover {
  color: #1d5987;
  text-decoration: none;
}

/* line 121, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active {
  border: 1px solid #79b7e7;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_inset-hard_100_f5f8f9_1x100.png) 50% 50% repeat-x #f5f8f9;
  font-weight: 700;
  color: #e17009;
}

/* line 128, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-active a, .ui-state-active a:link, .ui-state-active a:visited {
  color: #e17009;
  text-decoration: none;
}

/* line 133, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget :active {
  outline: 0;
}

/* line 137, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {
  border: 1px solid #fad42e;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_flat_55_fbec88_40x100.png) 50% 50% repeat-x #fbec88;
  color: #363636;
}

/* line 143, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-highlight a, .ui-widget-content .ui-state-highlight a, .ui-widget-header .ui-state-highlight a {
  color: #363636;
}

/* line 147, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-error, .ui-widget-content .ui-state-error, .ui-widget-header .ui-state-error {
  border: 1px solid #cd0a0a;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_glass_95_fef1ec_1x400.png) 50% 50% repeat-x #fef1ec;
  color: #cd0a0a;
}

/* line 153, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-error a, .ui-state-error-text, .ui-widget-content .ui-state-error a, .ui-widget-content .ui-state-error-text, .ui-widget-header .ui-state-error a, .ui-widget-header .ui-state-error-text {
  color: #cd0a0a;
}

/* line 157, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-priority-primary, .ui-widget-content .ui-priority-primary, .ui-widget-header .ui-priority-primary {
  font-weight: 700;
}

/* line 161, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-priority-secondary, .ui-widget-content .ui-priority-secondary, .ui-widget-header .ui-priority-secondary {
  opacity: .7;
  filter: Alpha(Opacity=70);
  font-weight: 400;
}

/* line 167, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled {
  opacity: .35;
  filter: Alpha(Opacity=35);
  background-image: none;
}
/* line 172, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-disabled.ui-datepicker-other-month, .ui-widget-content .ui-state-disabled.ui-datepicker-other-month, .ui-widget-header .ui-state-disabled.ui-datepicker-other-month {
  opacity: 0;
}

/* line 177, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon, .ui-widget-content .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_469bdd_256x240.png);
}

/* line 181, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon {
  width: 16px;
  height: 16px;
}

/* line 186, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-header .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_d8e7f3_256x240.png);
}

/* line 190, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-default .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_6da8d5_256x240.png);
}

/* line 194, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-focus .ui-icon, .ui-state-hover .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_217bc0_256x240.png);
}

/* line 198, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-active .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_f9bd01_256x240.png);
}

/* line 202, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-highlight .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_2e83ff_256x240.png);
}

/* line 206, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-error .ui-icon, .ui-state-error-text .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_cd0a0a_256x240.png);
}

/* line 210, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-n {
  background-position: 0 0;
}

/* line 214, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-ne {
  background-position: -16px 0;
}

/* line 218, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-e {
  background-position: -32px 0;
}

/* line 222, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-se {
  background-position: -48px 0;
}

/* line 226, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-s {
  background-position: -64px 0;
}

/* line 230, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-sw {
  background-position: -80px 0;
}

/* line 234, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-w {
  background-position: -96px 0;
}

/* line 238, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-nw {
  background-position: -112px 0;
}

/* line 242, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-2-n-s {
  background-position: -128px 0;
}

/* line 246, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-2-e-w {
  background-position: -144px 0;
}

/* line 250, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-n {
  background-position: 0 -16px;
}

/* line 254, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-ne {
  background-position: -16px -16px;
}

/* line 258, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-e {
  background-position: -32px -16px;
}

/* line 262, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-se {
  background-position: -48px -16px;
}

/* line 266, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-s {
  background-position: -64px -16px;
}

/* line 270, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-sw {
  background-position: -80px -16px;
}

/* line 274, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-w {
  background-position: -96px -16px;
}

/* line 278, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-nw {
  background-position: -112px -16px;
}

/* line 282, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-2-n-s {
  background-position: -128px -16px;
}

/* line 286, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-2-e-w {
  background-position: -144px -16px;
}

/* line 290, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-n {
  background-position: 0 -32px;
}

/* line 294, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-ne {
  background-position: -16px -32px;
}

/* line 298, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-e {
  background-position: -32px -32px;
}

/* line 302, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-se {
  background-position: -48px -32px;
}

/* line 306, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-s {
  background-position: -64px -32px;
}

/* line 310, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-sw {
  background-position: -80px -32px;
}

/* line 314, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-w {
  background-position: -96px -32px;
}

/* line 318, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-nw {
  background-position: -112px -32px;
}

/* line 322, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-n-s {
  background-position: -128px -32px;
}

/* line 326, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-ne-sw {
  background-position: -144px -32px;
}

/* line 330, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-e-w {
  background-position: -160px -32px;
}

/* line 334, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-se-nw {
  background-position: -176px -32px;
}

/* line 338, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-n {
  background-position: -192px -32px;
}

/* line 342, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-e {
  background-position: -208px -32px;
}

/* line 346, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-s {
  background-position: -224px -32px;
}

/* line 350, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-w {
  background-position: -240px -32px;
}

/* line 354, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-n {
  background-position: 0 -48px;
}

/* line 358, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-ne {
  background-position: -16px -48px;
}

/* line 362, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-e {
  background-position: -32px -48px;
}

/* line 366, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-se {
  background-position: -48px -48px;
}

/* line 370, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-s {
  background-position: -64px -48px;
}

/* line 374, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-sw {
  background-position: -80px -48px;
}

/* line 378, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-w {
  background-position: -96px -48px;
}

/* line 382, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-nw {
  background-position: -112px -48px;
}

/* line 386, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-n-s {
  background-position: -128px -48px;
}

/* line 390, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-ne-sw {
  background-position: -144px -48px;
}

/* line 394, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-e-w {
  background-position: -160px -48px;
}

/* line 398, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-se-nw {
  background-position: -176px -48px;
}

/* line 402, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-n {
  background-position: -192px -48px;
}

/* line 406, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-e {
  background-position: -208px -48px;
}

/* line 410, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-s {
  background-position: -224px -48px;
}

/* line 414, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-w {
  background-position: -240px -48px;
}

/* line 418, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-w {
  background-position: 0 -64px;
}

/* line 422, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-n {
  background-position: -16px -64px;
}

/* line 426, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-e {
  background-position: -32px -64px;
}

/* line 430, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-s {
  background-position: -48px -64px;
}

/* line 434, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-w {
  background-position: -64px -64px;
}

/* line 438, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-n {
  background-position: -80px -64px;
}

/* line 442, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-e {
  background-position: -96px -64px;
}

/* line 446, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-s {
  background-position: -112px -64px;
}

/* line 450, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-w {
  background-position: -128px -64px;
}

/* line 454, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-n {
  background-position: -144px -64px;
}

/* line 458, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-e {
  background-position: -160px -64px;
}

/* line 462, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-s {
  background-position: -176px -64px;
}

/* line 466, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-4 {
  background-position: 0 -80px;
}

/* line 470, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-4-diag {
  background-position: -16px -80px;
}

/* line 474, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-extlink {
  background-position: -32px -80px;
}

/* line 478, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-newwin {
  background-position: -48px -80px;
}

/* line 482, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-refresh {
  background-position: -64px -80px;
}

/* line 486, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-shuffle {
  background-position: -80px -80px;
}

/* line 490, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-transfer-e-w {
  background-position: -96px -80px;
}

/* line 494, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-transferthick-e-w {
  background-position: -112px -80px;
}

/* line 498, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-folder-collapsed {
  background-position: 0 -96px;
}

/* line 502, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-folder-open {
  background-position: -16px -96px;
}

/* line 506, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-document {
  background-position: -32px -96px;
}

/* line 510, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-document-b {
  background-position: -48px -96px;
}

/* line 514, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-note {
  background-position: -64px -96px;
}

/* line 518, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-mail-closed {
  background-position: -80px -96px;
}

/* line 522, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-mail-open {
  background-position: -96px -96px;
}

/* line 526, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-suitcase {
  background-position: -112px -96px;
}

/* line 530, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-comment {
  background-position: -128px -96px;
}

/* line 534, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-person {
  background-position: -144px -96px;
}

/* line 538, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-print {
  background-position: -160px -96px;
}

/* line 542, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-trash {
  background-position: -176px -96px;
}

/* line 546, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-locked {
  background-position: -192px -96px;
}

/* line 550, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-unlocked {
  background-position: -208px -96px;
}

/* line 554, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-bookmark {
  background-position: -224px -96px;
}

/* line 558, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-tag {
  background-position: -240px -96px;
}

/* line 562, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-home {
  background-position: 0 -112px;
}

/* line 566, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-flag {
  background-position: -16px -112px;
}

/* line 570, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-calendar {
  background-position: -32px -112px;
}

/* line 574, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-cart {
  background-position: -48px -112px;
}

/* line 578, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pencil {
  background-position: -64px -112px;
}

/* line 582, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-clock {
  background-position: -80px -112px;
}

/* line 586, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-disk {
  background-position: -96px -112px;
}

/* line 590, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-calculator {
  background-position: -112px -112px;
}

/* line 594, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-zoomin {
  background-position: -128px -112px;
}

/* line 598, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-zoomout {
  background-position: -144px -112px;
}

/* line 602, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-search {
  background-position: -160px -112px;
}

/* line 606, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-wrench {
  background-position: -176px -112px;
}

/* line 610, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-gear {
  background-position: -192px -112px;
}

/* line 614, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-heart {
  background-position: -208px -112px;
}

/* line 618, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-star {
  background-position: -224px -112px;
}

/* line 622, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-link {
  background-position: -240px -112px;
}

/* line 626, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-cancel {
  background-position: 0 -128px;
}

/* line 630, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-plus {
  background-position: -16px -128px;
}

/* line 634, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-plusthick {
  background-position: -32px -128px;
}

/* line 638, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-minus {
  background-position: -48px -128px;
}

/* line 642, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-minusthick {
  background-position: -64px -128px;
}

/* line 646, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-close {
  background-position: -80px -128px;
}

/* line 650, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-closethick {
  background-position: -96px -128px;
}

/* line 654, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-key {
  background-position: -112px -128px;
}

/* line 658, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-lightbulb {
  background-position: -128px -128px;
}

/* line 662, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-scissors {
  background-position: -144px -128px;
}

/* line 666, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-clipboard {
  background-position: -160px -128px;
}

/* line 670, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-copy {
  background-position: -176px -128px;
}

/* line 674, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-contact {
  background-position: -192px -128px;
}

/* line 678, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-image {
  background-position: -208px -128px;
}

/* line 682, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-video {
  background-position: -224px -128px;
}

/* line 686, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-script {
  background-position: -240px -128px;
}

/* line 690, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-alert {
  background-position: 0 -144px;
}

/* line 694, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-info {
  background-position: -16px -144px;
}

/* line 698, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-notice {
  background-position: -32px -144px;
}

/* line 702, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-help {
  background-position: -48px -144px;
}

/* line 706, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-check {
  background-position: -64px -144px;
}

/* line 710, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-bullet {
  background-position: -80px -144px;
}

/* line 714, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-radio-off {
  background-position: -96px -144px;
}

/* line 718, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-radio-on {
  background-position: -112px -144px;
}

/* line 722, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pin-w {
  background-position: -128px -144px;
}

/* line 726, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pin-s {
  background-position: -144px -144px;
}

/* line 730, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-play {
  background-position: 0 -160px;
}

/* line 734, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pause {
  background-position: -16px -160px;
}

/* line 738, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-next {
  background-position: -32px -160px;
}

/* line 742, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-prev {
  background-position: -48px -160px;
}

/* line 746, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-end {
  background-position: -64px -160px;
}

/* line 750, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-first, .ui-icon-seek-start {
  background-position: -80px -160px;
}

/* line 754, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-stop {
  background-position: -96px -160px;
}

/* line 758, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-eject {
  background-position: -112px -160px;
}

/* line 762, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-volume-off {
  background-position: -128px -160px;
}

/* line 766, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-volume-on {
  background-position: -144px -160px;
}

/* line 770, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-power {
  background-position: 0 -176px;
}

/* line 774, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-signal-diag {
  background-position: -16px -176px;
}

/* line 778, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-signal {
  background-position: -32px -176px;
}

/* line 782, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-0 {
  background-position: -48px -176px;
}

/* line 786, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-1 {
  background-position: -64px -176px;
}

/* line 790, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-2 {
  background-position: -80px -176px;
}

/* line 794, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-3 {
  background-position: -96px -176px;
}

/* line 798, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-plus {
  background-position: 0 -192px;
}

/* line 802, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-minus {
  background-position: -16px -192px;
}

/* line 806, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-close {
  background-position: -32px -192px;
}

/* line 810, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-e {
  background-position: -48px -192px;
}

/* line 814, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-s {
  background-position: -64px -192px;
}

/* line 818, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-w {
  background-position: -80px -192px;
}

/* line 822, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-n {
  background-position: -96px -192px;
}

/* line 826, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-e {
  background-position: -112px -192px;
}

/* line 830, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-s {
  background-position: -128px -192px;
}

/* line 834, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-w {
  background-position: -144px -192px;
}

/* line 838, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-n {
  background-position: -160px -192px;
}

/* line 842, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-zoomin {
  background-position: -176px -192px;
}

/* line 846, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-zoomout {
  background-position: -192px -192px;
}

/* line 850, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-check {
  background-position: -208px -192px;
}

/* line 854, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circlesmall-plus {
  background-position: 0 -208px;
}

/* line 858, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circlesmall-minus {
  background-position: -16px -208px;
}

/* line 862, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circlesmall-close {
  background-position: -32px -208px;
}

/* line 866, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-squaresmall-plus {
  background-position: -48px -208px;
}

/* line 870, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-squaresmall-minus {
  background-position: -64px -208px;
}

/* line 874, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-squaresmall-close {
  background-position: -80px -208px;
}

/* line 878, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-dotted-vertical {
  background-position: 0 -224px;
}

/* line 882, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-dotted-horizontal {
  background-position: -16px -224px;
}

/* line 886, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-solid-vertical {
  background-position: -32px -224px;
}

/* line 890, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-solid-horizontal {
  background-position: -48px -224px;
}

/* line 894, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-gripsmall-diagonal-se {
  background-position: -64px -224px;
}

/* line 898, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-diagonal-se {
  background-position: -80px -224px;
}

/* line 902, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-overlay, .ui-widget-shadow {
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_flat_0_aaaaaa_40x100.png) 50% 50% repeat-x #aaa;
  opacity: .3;
  filter: Alpha(Opacity=30);
}

/* line 908, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-left, .ui-corner-tl, .ui-corner-top {
  -moz-border-radius-topleft: 5px;
  -webkit-border-top-left-radius: 5px;
  -khtml-border-top-left-radius: 5px;
  border-top-left-radius: 5px;
}

/* line 915, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-right, .ui-corner-top, .ui-corner-tr {
  -moz-border-radius-topright: 5px;
  -webkit-border-top-right-radius: 5px;
  -khtml-border-top-right-radius: 5px;
  border-top-right-radius: 5px;
}

/* line 922, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-bl, .ui-corner-bottom, .ui-corner-left {
  -moz-border-radius-bottomleft: 5px;
  -webkit-border-bottom-left-radius: 5px;
  -khtml-border-bottom-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

/* line 929, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-bottom, .ui-corner-br, .ui-corner-right {
  -moz-border-radius-bottomright: 5px;
  -webkit-border-bottom-right-radius: 5px;
  -khtml-border-bottom-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

/* line 936, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-shadow {
  margin: -8px 0 0 -8px;
  padding: 8px;
  -moz-border-radius: 8px;
  -khtml-border-radius: 8px;
  -webkit-border-radius: 8px;
  border-radius: 8px;
}

/* line 945, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker {
  width: 17em;
  padding: .2em .2em 0;
  display: none;
}

/* line 951, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-header {
  position: relative;
  padding: 0.2em 0;
}

/* line 956, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next, .ui-datepicker .ui-datepicker-prev {
  position: absolute;
  top: 2px;
  width: 1.8em;
  height: 1.8em;
}

/* line 963, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next-hover, .ui-datepicker .ui-datepicker-prev-hover {
  top: 1px;
}

/* line 967, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-prev {
  left: 2px;
}

/* line 971, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next {
  right: 2px;
}

/* line 975, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-prev-hover {
  left: 1px;
}

/* line 979, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next-hover {
  right: 1px;
}

/* line 983, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next span, .ui-datepicker .ui-datepicker-prev span {
  display: block;
  position: absolute;
  left: 50%;
  margin-left: -8px;
  top: 50%;
  margin-top: -8px;
}

/* line 992, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-title {
  margin: 0 2.3em;
  line-height: 1.8em;
  text-align: center;
}

/* line 998, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-title select {
  font-size: 1em;
  margin: 1px 0;
}

/* line 1003, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker select.ui-datepicker-month-year {
  width: 100%;
}

/* line 1007, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker select.ui-datepicker-month, .ui-datepicker select.ui-datepicker-year {
  width: 49%;
}

/* line 1011, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker table {
  width: 100%;
  font-size: .9em;
  border-collapse: collapse;
  margin: 0 0 0.4em;
}

/* line 1018, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker th {
  padding: .7em .3em;
  text-align: center;
  font-weight: 700;
  border: 0;
}

/* line 1025, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker td {
  border: 0;
  padding: 1px;
}

/* line 1030, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker td a, .ui-datepicker td span {
  display: block;
  padding: .2em;
  text-align: right;
  text-decoration: none;
}

/* line 1037, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-buttonpane {
  background-image: none;
  margin: .7em 0 0;
  padding: 0 .2em;
  border-left: 0;
  border-right: 0;
  border-bottom: 0;
}

/* line 1046, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-buttonpane button {
  float: right;
  margin: .5em .2em .4em;
  cursor: pointer;
  padding: .2em .6em .3em;
  width: auto;
  overflow: visible;
}

/* line 1055, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current, .ui-datepicker-multi .ui-datepicker-group, .ui-datepicker-rtl .ui-datepicker-buttonpane button {
  float: left;
}

/* line 1059, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker.ui-datepicker-multi {
  width: auto;
}

/* line 1063, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi .ui-datepicker-group table {
  width: 95%;
  margin: 0 auto 0.4em;
}

/* line 1068, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi-2 .ui-datepicker-group {
  width: 50%;
}

/* line 1072, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi-3 .ui-datepicker-group {
  width: 33.3%;
}

/* line 1076, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi-4 .ui-datepicker-group {
  width: 25%;
}

/* line 1080, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header, .ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
  border-left-width: 0;
}

/* line 1084, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi .ui-datepicker-buttonpane {
  clear: left;
}

/* line 1088, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-row-break {
  clear: both;
  width: 100%;
  font-size: 0;
}

/* line 1094, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl {
  direction: rtl;
}

/* line 1098, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-prev {
  right: 2px;
  left: auto;
}

/* line 1103, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-next {
  left: 2px;
  right: auto;
}

/* line 1108, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-prev:hover {
  right: 1px;
  left: auto;
}

/* line 1113, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-next:hover {
  left: 1px;
  right: auto;
}

/* line 1118, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-buttonpane {
  clear: right;
}

/* line 1122, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current, .ui-datepicker-rtl .ui-datepicker-group {
  float: right;
}

/* line 1126, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header, .ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {
  border-right-width: 0;
  border-left-width: 1px;
}

/* line 1131, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-cover {
  display: none;
  display: block;
  position: absolute;
  z-index: -1;
  filter: mask();
  top: -4px;
  left: -4px;
  width: 200px;
  height: 200px;
}

@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (-webkit-min-device-pixel-ratio: 2) {
  /* line 1144, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
  div.ui-datepicker {
    font-size: 20px;
  }
}
/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container {
  box-sizing: border-box;
  display: inline-block;
  margin: 0;
  position: relative;
  vertical-align: middle;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-selection--single {
  box-sizing: border-box;
  cursor: pointer;
  display: block;
  height: 28px;
  user-select: none;
  -webkit-user-select: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-selection--single .select2-selection__rendered {
  display: block;
  padding-left: 8px;
  padding-right: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-selection--single .select2-selection__clear {
  position: relative;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container[dir="rtl"] .select2-selection--single .select2-selection__rendered {
  padding-right: 8px;
  padding-left: 20px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-selection--multiple {
  box-sizing: border-box;
  cursor: pointer;
  display: block;
  min-height: 32px;
  user-select: none;
  -webkit-user-select: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-selection--multiple .select2-selection__rendered {
  display: inline-block;
  overflow: hidden;
  padding-left: 8px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-search--inline {
  float: left;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-search--inline .select2-search__field {
  box-sizing: border-box;
  border: none;
  font-size: 100%;
  margin-top: 5px;
  padding: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-search--inline .select2-search__field::-webkit-search-cancel-button {
  -webkit-appearance: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-dropdown {
  background-color: white;
  border: 1px solid #aaa;
  border-radius: 4px;
  box-sizing: border-box;
  display: block;
  position: absolute;
  left: -100000px;
  width: 100%;
  z-index: 1051;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-results {
  display: block;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-results__options {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-results__option {
  padding: 6px;
  user-select: none;
  -webkit-user-select: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-results__option[aria-selected] {
  cursor: pointer;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--open .select2-dropdown {
  left: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--open .select2-dropdown--above {
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--open .select2-dropdown--below {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-search--dropdown {
  display: block;
  padding: 4px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-search--dropdown .select2-search__field {
  padding: 4px;
  width: 100%;
  box-sizing: border-box;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
  -webkit-appearance: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-search--dropdown.select2-search--hide {
  display: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-close-mask {
  border: 0;
  margin: 0;
  padding: 0;
  display: block;
  position: fixed;
  left: 0;
  top: 0;
  min-height: 100%;
  min-width: 100%;
  height: auto;
  width: auto;
  opacity: 0;
  z-index: 99;
  background-color: #fff;
  filter: alpha(opacity=0);
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-hidden-accessible {
  border: 0 !important;
  clip: rect(0 0 0 0) !important;
  -webkit-clip-path: inset(50%) !important;
  clip-path: inset(50%) !important;
  height: 1px !important;
  overflow: hidden !important;
  padding: 0 !important;
  position: absolute !important;
  width: 1px !important;
  white-space: nowrap !important;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--single {
  background-color: #fff;
  border: 1px solid #aaa;
  border-radius: 4px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #444;
  line-height: 28px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--single .select2-selection__clear {
  cursor: pointer;
  float: right;
  font-weight: bold;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #999;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 26px;
  position: absolute;
  top: 1px;
  right: 1px;
  width: 20px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--single .select2-selection__arrow b {
  border-color: #888 transparent transparent transparent;
  border-style: solid;
  border-width: 5px 4px 0 4px;
  height: 0;
  left: 50%;
  margin-left: -4px;
  margin-top: -2px;
  position: absolute;
  top: 50%;
  width: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__clear {
  float: left;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__arrow {
  left: 1px;
  right: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--disabled .select2-selection--single {
  background-color: #eee;
  cursor: default;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__clear {
  display: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #888 transparent;
  border-width: 0 4px 5px 4px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple {
  background-color: white;
  border: 1px solid #aaa;
  border-radius: 4px;
  cursor: text;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple .select2-selection__rendered {
  box-sizing: border-box;
  list-style: none;
  margin: 0;
  padding: 0 5px;
  width: 100%;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple .select2-selection__rendered li {
  list-style: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
  color: #999;
  margin-top: 5px;
  float: left;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple .select2-selection__clear {
  cursor: pointer;
  float: right;
  font-weight: bold;
  margin-top: 5px;
  margin-right: 10px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #e4e4e4;
  border: 1px solid #aaa;
  border-radius: 4px;
  cursor: default;
  float: left;
  margin-right: 5px;
  margin-top: 5px;
  padding: 0 5px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: #999;
  cursor: pointer;
  display: inline-block;
  font-weight: bold;
  margin-right: 2px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #333;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice, .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__placeholder, .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-search--inline {
  float: right;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
  margin-left: 5px;
  margin-right: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
  margin-left: 2px;
  margin-right: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--focus .select2-selection--multiple {
  border: solid black 1px;
  outline: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--disabled .select2-selection--multiple {
  background-color: #eee;
  cursor: default;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--disabled .select2-selection__choice__remove {
  display: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--open.select2-container--above .select2-selection--single, .select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--open.select2-container--below .select2-selection--single, .select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-search--dropdown .select2-search__field {
  border: 1px solid #aaa;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-search--inline .select2-search__field {
  background: transparent;
  border: none;
  outline: 0;
  box-shadow: none;
  -webkit-appearance: textfield;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results > .select2-results__options {
  max-height: 200px;
  overflow-y: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option[role=group] {
  padding: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option[aria-disabled=true] {
  color: #999;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option[aria-selected=true] {
  background-color: #ddd;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option .select2-results__option {
  padding-left: 1em;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option .select2-results__option .select2-results__group {
  padding-left: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -1em;
  padding-left: 2em;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -2em;
  padding-left: 3em;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -3em;
  padding-left: 4em;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -4em;
  padding-left: 5em;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -5em;
  padding-left: 6em;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #5897fb;
  color: white;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__group {
  cursor: default;
  display: block;
  padding: 6px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--single {
  background-color: #f7f7f7;
  border: 1px solid #aaa;
  border-radius: 4px;
  outline: 0;
  background-image: -webkit-linear-gradient(top, #fff 50%, #eee 100%);
  background-image: -o-linear-gradient(top, #fff 50%, #eee 100%);
  background-image: linear-gradient(to bottom, #fff 50%, #eee 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFFFF', endColorstr='#FFEEEEEE', GradientType=0);
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--single:focus {
  border: 1px solid #5897fb;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--single .select2-selection__rendered {
  color: #444;
  line-height: 28px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--single .select2-selection__clear {
  cursor: pointer;
  float: right;
  font-weight: bold;
  margin-right: 10px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--single .select2-selection__placeholder {
  color: #999;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--single .select2-selection__arrow {
  background-color: #ddd;
  border: none;
  border-left: 1px solid #aaa;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  height: 26px;
  position: absolute;
  top: 1px;
  right: 1px;
  width: 20px;
  background-image: -webkit-linear-gradient(top, #eee 50%, #ccc 100%);
  background-image: -o-linear-gradient(top, #eee 50%, #ccc 100%);
  background-image: linear-gradient(to bottom, #eee 50%, #ccc 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFEEEEEE', endColorstr='#FFCCCCCC', GradientType=0);
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--single .select2-selection__arrow b {
  border-color: #888 transparent transparent transparent;
  border-style: solid;
  border-width: 5px 4px 0 4px;
  height: 0;
  left: 50%;
  margin-left: -4px;
  margin-top: -2px;
  position: absolute;
  top: 50%;
  width: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__clear {
  float: left;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__arrow {
  border: none;
  border-right: 1px solid #aaa;
  border-radius: 0;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  left: 1px;
  right: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open .select2-selection--single {
  border: 1px solid #5897fb;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow {
  background: transparent;
  border: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #888 transparent;
  border-width: 0 4px 5px 4px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open.select2-container--above .select2-selection--single {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  background-image: -webkit-linear-gradient(top, #fff 0%, #eee 50%);
  background-image: -o-linear-gradient(top, #fff 0%, #eee 50%);
  background-image: linear-gradient(to bottom, #fff 0%, #eee 50%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFFFF', endColorstr='#FFEEEEEE', GradientType=0);
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open.select2-container--below .select2-selection--single {
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background-image: -webkit-linear-gradient(top, #eee 50%, #fff 100%);
  background-image: -o-linear-gradient(top, #eee 50%, #fff 100%);
  background-image: linear-gradient(to bottom, #eee 50%, #fff 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFEEEEEE', endColorstr='#FFFFFFFF', GradientType=0);
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--multiple {
  background-color: white;
  border: 1px solid #aaa;
  border-radius: 4px;
  cursor: text;
  outline: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--multiple:focus {
  border: 1px solid #5897fb;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--multiple .select2-selection__rendered {
  list-style: none;
  margin: 0;
  padding: 0 5px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--multiple .select2-selection__clear {
  display: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--multiple .select2-selection__choice {
  background-color: #e4e4e4;
  border: 1px solid #aaa;
  border-radius: 4px;
  cursor: default;
  float: left;
  margin-right: 5px;
  margin-top: 5px;
  padding: 0 5px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove {
  color: #888;
  cursor: pointer;
  display: inline-block;
  font-weight: bold;
  margin-right: 2px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #555;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
  float: right;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
  margin-left: 5px;
  margin-right: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
  margin-left: 2px;
  margin-right: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open .select2-selection--multiple {
  border: 1px solid #5897fb;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open.select2-container--above .select2-selection--multiple {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open.select2-container--below .select2-selection--multiple {
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-search--dropdown .select2-search__field {
  border: 1px solid #aaa;
  outline: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-search--inline .select2-search__field {
  outline: 0;
  box-shadow: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-dropdown {
  background-color: #fff;
  border: 1px solid transparent;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-dropdown--above {
  border-bottom: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-dropdown--below {
  border-top: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-results > .select2-results__options {
  max-height: 200px;
  overflow-y: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-results__option[role=group] {
  padding: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-results__option[aria-disabled=true] {
  color: grey;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-results__option--highlighted[aria-selected] {
  background-color: #3875d7;
  color: #fff;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-results__group {
  cursor: default;
  display: block;
  padding: 6px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open .select2-dropdown {
  border-color: #5897fb;
}

/* line 1, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.color_buttons {
  color: #2074ca;
}

/* line 5, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.color_text {
  color: #012379;
}

/* line 9, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.general_title_block {
  padding: 0 20px;
  margin-bottom: 30px;
}
/* line 13, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.general_title_block.centered {
  text-align: center;
}
/* line 17, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.general_title_block.main_section_titles {
  margin-top: 18px;
}
/* line 21, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.general_title_block .mini_title {
  margin: 0;
  text-transform: uppercase;
  color: #424242;
  font-size: 12px;
  letter-spacing: 0.3px;
}
/* line 29, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.general_title_block .main_title {
  margin: 0;
  font-style: italic;
  margin-top: 0;
  font-size: 24px;
  line-height: 25px;
}
/* line 37, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.general_title_block .main_description {
  font-size: 12px;
  color: #424242;
  margin-top: 20px;
  line-height: 20px;
  letter-spacing: 0.2px;
}

/* line 1, ../../../../../sass/styles_mobile/3/_general_styles.scss */
body {
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  margin: 0;
  font-family: 'Roboto', sans-serif;
}
/* line 6, ../../../../../sass/styles_mobile/3/_general_styles.scss */
body.right_move {
  margin-left: 90%;
  margin-right: -90%;
}
/* line 11, ../../../../../sass/styles_mobile/3/_general_styles.scss */
body * {
  box-sizing: border-box;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}
/* line 17, ../../../../../sass/styles_mobile/3/_general_styles.scss */
body b, body strong {
  font-weight: 600;
}
/* line 21, ../../../../../sass/styles_mobile/3/_general_styles.scss */
body a {
  text-decoration: none;
}

/* line 27, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider {
  height: 57vw;
  margin-top: 6vh;
}
/* line 31, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .owl-stage-outer, .main-owlslider .owl-stage, .main-owlslider .owl-item {
  height: 100%;
}
/* line 35, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .owl-item {
  position: relative;
  overflow: hidden;
}
/* line 39, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .owl-item img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  width: auto;
  max-width: none;
}
/* line 48, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .description_text {
  position: absolute;
  top: 50%;
  left: 40px;
  right: 40px;
  text-align: left;
  transform: translateY(-50%);
  font-size: 26px;
  line-height: 24px;
  font-weight: 600;
}
/* line 60, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .owl-dots {
  position: absolute;
  bottom: 10px;
  left: 20px;
  right: 20px;
  display: flex;
}
/* line 67, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .owl-dots:before {
  content: '';
  width: 100%;
  height: 1px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: #012379;
}
/* line 78, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .owl-dots .owl-dot {
  flex-flow: row nowrap;
  height: 6px;
  width: 100%;
  margin-top: -3px;
  -webkit-transition: background-color 0.4s;
  -moz-transition: background-color 0.4s;
  -ms-transition: background-color 0.4s;
  -o-transition: background-color 0.4s;
  transition: background-color 0.4s;
}
/* line 85, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .owl-dots .owl-dot.active {
  background-color: #012379;
}

/* line 93, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_block_bottom {
  margin-top: 40px;
}
/* line 96, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_block_bottom .banners_bottom_title {
  padding: 0 20px;
  margin-bottom: 30px;
}
/* line 100, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_block_bottom .banners_bottom_title .main_title {
  color: #012379;
}
/* line 105, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_block_bottom .bottom_banner_element {
  display: block;
  width: 100%;
  position: relative;
  padding-top: 35%;
  overflow: hidden;
  border-bottom: 1px solid white;
}
/* line 113, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_block_bottom .bottom_banner_element .overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}
/* line 123, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_block_bottom .bottom_banner_element .bottom_block_image {
  position: absolute;
  top: -50%;
  bottom: -50%;
  margin: auto;
  left: -50%;
  right: -50%;
}
/* line 132, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_block_bottom .bottom_banner_element .bottom_banner_title {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: 100%;
  margin: 0;
  z-index: 2;
  text-align: center;
  color: white;
  font-style: italic;
  font-size: 18px;
  text-shadow: -1px 4px 5px rgba(74, 74, 74, 0.55);
}

/* line 148, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper {
  margin: 40px auto;
}
/* line 152, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .banners_bottom_title .main_title {
  color: #012379;
}
/* line 158, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl_banners_carousel {
  position: relative;
}
/* line 160, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl_banners_carousel:before {
  content: '';
  width: 100%;
  background-color: #F4F4F4;
  position: absolute;
  top: 0;
  bottom: 40%;
}
/* line 170, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item {
  padding-left: 15px;
  padding-right: 50px;
}
/* line 174, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element {
  display: inline-block;
  width: 100%;
  position: relative;
  margin-top: 10px;
  border: 1px solid #d0d0d0;
}
/* line 181, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .image_wrapper {
  width: 40%;
  height: 100%;
  display: inline-block;
  vertical-align: top;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
}
/* line 192, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .image_wrapper .since_wrapper {
  position: absolute;
  left: 10px;
  bottom: 10px;
  z-index: 2;
  background: white;
  padding: 8px 7px 9px;
  text-align: right;
  text-transform: uppercase;
  font-size: 19px;
  font-weight: bolder;
  line-height: 16px;
}
/* line 205, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .image_wrapper .since_wrapper .since_label {
  display: block;
  font-size: 11px;
  font-weight: 500;
  color: #4e4e4e;
}
/* line 213, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .image_wrapper .image_element {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  width: auto;
}
/* line 219, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper {
  width: 60%;
  display: inline-block;
  vertical-align: top;
  background-color: #fff;
  padding: 10px;
  float: right;
  margin-bottom: auto;
  border-left: 0;
}
/* line 230, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .mini_title {
  font-size: 10px;
}
/* line 234, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .main_title {
  margin-top: 0;
  font-size: 18px;
  color: #012379;
  height: 48px;
}
/* line 241, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .hotel_category {
  margin-top: -10px;
}
/* line 244, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .hotel_category .category {
  font-size: 10px;
}
/* line 249, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .tags_wrapper {
  width: 100%;
  display: flex;
  flex-flow: wrap;
  margin-top: 0;
}
/* line 255, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .tags_wrapper .tag_element {
  display: inline-table;
  font-style: italic;
  font-size: 10px;
  background-color: #CCCCCC;
  color: white;
  width: auto;
  text-align: center;
  flex-flow: row nowrap;
  margin-right: 10px;
  padding: 0 12px;
  white-space: nowrap;
}
/* line 268, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .tags_wrapper .tag_element:first-of-type {
  margin-bottom: 5px;
}
/* line 272, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .tags_wrapper .tag_element:last-of-type {
  margin-right: 0;
}
/* line 278, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .see_hotel_link {
  display: inline-block;
  width: 100%;
  background-color: #F7BB1E;
  color: white;
  text-transform: uppercase;
  padding: 5px 8px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  margin-top: 8px;
}
/* line 294, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-dots {
  position: relative;
  width: 100%;
  padding: 0 20px;
  display: flex;
  margin-top: 40px;
}
/* line 301, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-dots:before {
  content: '';
  width: calc(100% - 40px);
  height: 1px;
  position: absolute;
  top: 0;
  left: 20px;
  right: 20px;
  background-color: #012379;
}
/* line 312, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-dots .owl-dot {
  position: relative;
  overflow: hidden;
  flex-flow: row nowrap;
  height: 6px;
  width: 100%;
  margin-top: -3px;
}
/* line 319, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-dots .owl-dot:before {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: -101%;
  background-color: #012379;
  -webkit-transition: left 0.4s;
  -moz-transition: left 0.4s;
  -ms-transition: left 0.4s;
  -o-transition: left 0.4s;
  transition: left 0.4s;
}
/* line 332, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-dots .owl-dot.active:before {
  left: 0;
}

/* line 344, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper {
  margin-top: 40px;
}
/* line 348, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .advantages_banner_title .main_title {
  color: #012379;
}
/* line 353, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper {
  background-color: #f4f4f4;
  margin-top: 15px;
  display: inline-block;
  padding: 20px;
}
/* line 359, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper:lang(ru) {
  padding: 10px;
}
/* line 363, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element {
  display: inline-block;
  width: calc(100% / 3);
  float: left;
  text-align: center;
  border-right: 1px solid #012379;
  padding: 0 10px;
}
/* line 371, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element i {
  font-weight: 300;
  font-size: larger;
}
/* line 376, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element.first {
  width: 100%;
  border-bottom: 1px solid #012379;
  padding: 0 0 10px 0;
  margin-bottom: 10px;
  border-right: 0;
}
/* line 383, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element.first .tick_icon, .advantages_banner_wrapper .ticks_wrapper .tick_element.first .tick_text {
  display: inline-block;
  vertical-align: middle;
  margin-top: 0;
}
/* line 389, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element.first .tick_icon {
  margin-right: 15px;
}
/* line 394, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element:last-of-type {
  border-right: 0;
}
/* line 398, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element .tick_icon {
  color: #23588a;
}
/* line 402, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element .tick_text {
  text-transform: uppercase;
  font-size: 9px;
  letter-spacing: 0.7px;
  margin-top: 8px;
}
/* line 411, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .external_advantage_link {
  display: block;
  text-align: center;
  text-transform: uppercase;
  font-size: 12px;
  margin-top: 5px;
  font-weight: 600;
}

/* line 422, ../../../../../sass/styles_mobile/3/_general_styles.scss */
#popup_v3_overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 100%;
  right: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 10;
  -webkit-transition: left 0.5s;
  -moz-transition: left 0.5s;
  -ms-transition: left 0.5s;
  -o-transition: left 0.5s;
  transition: left 0.5s;
}
/* line 436, ../../../../../sass/styles_mobile/3/_general_styles.scss */
#popup_v3_overlay.opened {
  left: 0;
}

/* line 441, ../../../../../sass/styles_mobile/3/_general_styles.scss */
#popup_website_v3 {
  position: fixed;
  top: 50%;
  left: 150%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  background: white;
  z-index: 11;
  width: 80%;
  padding: 30px;
  -webkit-transition: left 0.5s;
  -moz-transition: left 0.5s;
  -ms-transition: left 0.5s;
  -o-transition: left 0.5s;
  transition: left 0.5s;
}
/* line 460, ../../../../../sass/styles_mobile/3/_general_styles.scss */
#popup_website_v3.opened {
  left: 50%;
}
/* line 464, ../../../../../sass/styles_mobile/3/_general_styles.scss */
#popup_website_v3 #close_button_popup_v3 {
  position: absolute;
  top: 10px;
  right: 10px;
}

/* line 1, ../../../../../sass/styles_mobile/3/_header.scss */
header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 10px 0;
  -webkit-box-shadow: 0 3px 9px 0 rgba(0, 0, 0, 0.4);
  -moz-box-shadow: 0 3px 9px 0 rgba(0, 0, 0, 0.4);
  box-shadow: 0 3px 9px 0 rgba(0, 0, 0, 0.4);
  z-index: 4;
  margin-bottom: -2px;
  background: white;
  transition: all 0.5s;
}
/* line 15, ../../../../../sass/styles_mobile/3/_header.scss */
header #menu_button {
  width: 9%;
  height: 30px;
  display: inline-block;
  font-size: 60px;
  position: relative;
  margin: 0 15px;
}
/* line 23, ../../../../../sass/styles_mobile/3/_header.scss */
header #menu_button .menu_icon {
  position: absolute;
  top: 50%;
  right: 0;
  width: 55%;
  height: 2px;
  background: #2074ca;
}
/* line 31, ../../../../../sass/styles_mobile/3/_header.scss */
header #menu_button .menu_icon:before {
  content: '';
  position: absolute;
  width: 150%;
  right: 0;
  top: -9px;
  height: 2px;
  background: #2074ca;
}
/* line 41, ../../../../../sass/styles_mobile/3/_header.scss */
header #menu_button .menu_icon:after {
  content: '';
  position: absolute;
  width: 150%;
  right: 0;
  bottom: -9px;
  height: 2px;
  background: #2074ca;
}
/* line 53, ../../../../../sass/styles_mobile/3/_header.scss */
header .logo {
  display: inline-block;
  width: 38%;
  position: relative;
  vertical-align: top;
  margin-top: 4px;
  margin-left: 10px;
}
/* line 61, ../../../../../sass/styles_mobile/3/_header.scss */
header .logo img {
  width: 100%;
}
/* line 66, ../../../../../sass/styles_mobile/3/_header.scss */
header .right_controlls {
  float: right;
  position: absolute;
  right: 20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 77, ../../../../../sass/styles_mobile/3/_header.scss */
header .right_controlls a {
  margin-left: 7px;
}
/* line 80, ../../../../../sass/styles_mobile/3/_header.scss */
header .right_controlls a i {
  font-size: 23px;
  -webkit-transform: scaleX(-1);
  -moz-transform: scaleX(-1);
  -ms-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  transform: scaleX(-1);
  font-weight: 300;
}

/* line 93, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu {
  width: 90%;
  display: block;
  position: fixed;
  z-index: 3;
  right: 100%;
  top: 0;
  bottom: 0;
  background: white;
  -webkit-transition: right 0.5s;
  -moz-transition: right 0.5s;
  -ms-transition: right 0.5s;
  -o-transition: right 0.5s;
  transition: right 0.5s;
  overflow: auto;
  color: #424242;
}
/* line 110, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu.active {
  right: 10%;
}
/* line 114, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .top_wrapper {
  display: table;
  width: 100%;
  padding: 20px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid #dfdfdf;
}
/* line 123, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .top_wrapper .logo {
  width: 50%;
  float: left;
}
/* line 127, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .top_wrapper .logo img {
  width: 100%;
}
/* line 132, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .top_wrapper .right_controlls {
  float: right;
}
/* line 135, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .top_wrapper .right_controlls .close_button {
  font-size: 31px;
  line-height: 1;
  display: inline-block;
}
/* line 141, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .top_wrapper .right_controlls a {
  font-size: 23px;
  line-height: 1;
  display: inline-block;
  -webkit-transform: scaleX(-1);
  -moz-transform: scaleX(-1);
  -ms-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  margin-right: 12px;
  transform: scaleX(-1);
  vertical-align: top;
  margin-top: 3px;
}
/* line 158, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu + .black_overlay_menu {
  position: fixed;
  right: 100%;
  height: 100vh;
  width: 100vw;
  top: 0;
  z-index: 2;
  background: rgba(45, 45, 45, 0.5);
  transition: right 0.5s;
}
/* line 169, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu.active + .black_overlay_menu {
  right: 0;
}
/* line 173, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper {
  padding: 20px 0;
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border-top: 1px solid #dfdfdf;
  text-transform: uppercase;
  font-weight: bold;
}
/* line 183, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper .preview {
  padding: 0 20px;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 192, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper .selected_language {
  float: right;
}
/* line 196, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper .language_options {
  max-height: 0;
  overflow: hidden;
  -webkit-transition: max-height 0.5s linear;
  -moz-transition: max-height 0.5s linear;
  -ms-transition: max-height 0.5s linear;
  -o-transition: max-height 0.5s linear;
  transition: max-height 0.5s linear;
  background: #f4f4f4;
}
/* line 206, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper .language_options .language_element {
  display: block;
  clear: both;
  margin: 15px 20px 15px 50px;
  text-decoration: none;
  color: gray;
}
/* line 215, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper.active {
  padding-bottom: 0;
}
/* line 218, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper.active .language_options {
  max-height: 400px;
}
/* line 222, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper.active .preview {
  margin-bottom: 20px;
}
/* line 228, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
/* line 233, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul li {
  font-weight: bold;
  text-transform: uppercase;
  padding: 15px 20px;
  border-bottom: 1px solid #dfdfdf;
}
/* line 239, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul li:last-of-type {
  border-bottom: 0;
}
/* line 243, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul li.section_subcontent {
  padding: 15px 0;
}
/* line 246, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul li.section_subcontent.active {
  border-bottom: 0;
}
/* line 251, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul li a {
  text-decoration: none;
  color: #424242;
}
/* line 257, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main {
  position: relative;
  padding-left: 20px;
  padding-right: 20px;
}
/* line 262, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .more, .main_menu .main_ul .submenu_main .less {
  -webkit-transition: opacity 0.5s;
  -moz-transition: opacity 0.5s;
  -ms-transition: opacity 0.5s;
  -o-transition: opacity 0.5s;
  transition: opacity 0.5s;
  position: absolute;
  right: 20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 23px;
  height: 23px;
  color: #012379;
  font-size: 23px;
}
/* line 281, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .more i, .main_menu .main_ul .submenu_main .less i {
  vertical-align: top;
  line-height: 1;
}
/* line 286, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .more span, .main_menu .main_ul .submenu_main .less span {
  margin: auto;
  text-align: center;
  font-weight: lighter;
  font-size: 28px;
  color: white;
  position: absolute;
  top: -50%;
  left: 0;
  right: 0;
}
/* line 298, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .more .circle_wrapper, .main_menu .main_ul .submenu_main .less .circle_wrapper {
  display: block;
  width: 24px;
  height: 24px;
  background: #012379;
  border-radius: 30px;
  position: relative;
}
/* line 306, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .more .circle_wrapper span, .main_menu .main_ul .submenu_main .less .circle_wrapper span {
  font-weight: lighter;
  font-size: 27px;
  position: absolute;
  left: 1px;
  right: 0;
  margin: auto;
  top: 47%;
  transform: translateY(-50%);
}
/* line 319, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .less {
  opacity: 0;
  background: transparent;
}
/* line 323, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .less .circle_wrapper {
  background: transparent;
  color: #012379;
  border: 1px solid;
}
/* line 328, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .less .circle_wrapper span {
  top: 38%;
}
/* line 333, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .less span {
  color: #012379;
}
/* line 339, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main.active .more {
  opacity: 0;
}
/* line 343, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main.active .less {
  opacity: 1;
}
/* line 349, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list {
  max-height: 0;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 358, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list > div {
  padding: 15px 0;
}
/* line 362, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list .submenu_main, .main_menu .main_ul .submenu_list .submenu_element {
  padding-left: 40px;
  color: #a2a2a2;
  font-size: 14px;
}
/* line 369, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level1 .search_container {
  margin-left: 20px;
  margin-bottom: 20px;
  padding: 0;
}
/* line 373, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level1 .search_container i {
  color: #2074ca;
  font-size: 30px;
  vertical-align: bottom;
  margin-right: 15px;
}
/* line 379, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level1 .search_container input.search_subsection {
  border: none;
  border-bottom: 2px solid #2074ca;
  padding: 8px 30px 8px 0;
  color: #a2a2a2;
  font-size: 14px;
  font-weight: lighter;
  width: 70%;
  max-width: 300px;
}
/* line 389, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level1 .search_container input.search_subsection::placeholder {
  color: #a2a2a2;
  font-size: 14px;
  font-weight: lighter;
}
/* line 395, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level1 .search_container input.search_subsection:focus {
  outline: none;
}
/* line 401, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level1 .submenu_element {
  padding-left: 40px;
}
/* line 404, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level1 .submenu_element a {
  color: #a2a2a2;
}
/* line 410, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level2 {
  background: #f4f4f4;
}
/* line 413, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level2 .submenu_element a {
  text-transform: initial;
}
/* line 418, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.active {
  max-height: 1300px;
  padding-top: 10px;
  margin-top: 20px;
}
/* line 425, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list .submenu_list {
  margin-top: 0;
  padding-top: 0;
  padding-bottom: 0;
}
/* line 433, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .class_extra_info_menu {
  margin: 20px;
  background: #f4f4f4;
  padding: 10px 25px 15px;
}
/* line 438, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .class_extra_info_menu .extra_title {
  text-align: center;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 13px;
  border-bottom: 1px solid;
  padding-bottom: 17px;
  margin-bottom: 20px;
  margin-top: 10px;
}
/* line 449, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .class_extra_info_menu .tick_element {
  margin-bottom: 10px;
}
/* line 452, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .class_extra_info_menu .tick_element i {
  margin-right: 10px;
  font-size: 20px;
  vertical-align: middle;
  color: #2074ca;
}
/* line 459, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .class_extra_info_menu .tick_element .tick_title {
  letter-spacing: 1.5px;
  font-size: 12px;
}
/* line 464, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .class_extra_info_menu .tick_element i, .main_menu .class_extra_info_menu .tick_element .tick_title {
  display: inline-block;
  text-transform: uppercase;
  font-weight: lighter;
}

/* line 474, ../../../../../sass/styles_mobile/3/_header.scss */
body.right_move header {
  left: 90%;
}

/* line 479, ../../../../../sass/styles_mobile/3/_header.scss */
.slider_inner_section {
  margin-top: 6vh;
}

/* line 1, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 {
  margin: 20px;
  border: 1px solid #d0d0d0;
}
/* line 5, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .wrapper-new-web-support.booking_form_title {
  margin: 0 10px 10px;
  font-size: 12px;
  text-align: center;
}
/* line 11, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .destination_wrapper {
  border-bottom: 1px solid #d0d0d0;
}
/* line 15, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .dates_selector_wrapper {
  border-bottom: 1px solid #d0d0d0;
}
/* line 19, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .booking_title {
  margin: 13px 20px;
  margin-bottom: 0;
  border-bottom: 1px solid #d0d0d0;
  text-transform: uppercase;
  padding-bottom: 7px;
  position: relative;
  width: calc(100% - 40px);
  font-size: 14px;
}
/* line 29, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .booking_title i {
  position: absolute;
  right: 0;
  top: 45%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  font-size: 19px;
}
/* line 42, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .label_field {
  font-size: 14px;
}
/* line 46, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .booking_value {
  font-weight: bolder;
  color: #5e5e5e;
}
/* line 51, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .submit_button {
  margin: 10px;
  background: #f7bb1e;
  border: 0;
  width: calc(100% - 20px);
  color: white;
  text-transform: uppercase;
  font-weight: bolder;
  font-size: 23px;
  padding: 10px 0;
}

/* line 64, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
#booking_engine_popup {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 100%;
  left: -100%;
  background: white;
  z-index: 4;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 78, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
#booking_engine_popup.active {
  left: 0;
  right: 0;
}
/* line 83, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
#booking_engine_popup .logo_wrapper {
  padding: 17px 17px 0;
  text-align: center;
}
/* line 87, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
#booking_engine_popup .logo_wrapper .logotype_mobile {
  max-height: 30px;
}
/* line 92, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
#booking_engine_popup .close_engine_popup {
  position: absolute;
  right: 25px;
  top: 8px;
  font-size: 29px;
  color: #002578;
}
/* line 100, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
#booking_engine_popup .mobile_engine_v2 {
  margin-top: 5px;
  max-height: 88vh;
  overflow: auto;
}

/* line 107, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.promocode_wrapper .promocode_input {
  width: 100%;
  display: block;
  background: #f4f4f4;
  border: 0;
  text-align: center;
  padding: 12px 0;
}
/* line 16, ../../sass/mobile/styles_mobile_test2.scss */
.promocode_wrapper .promocode_input::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #9a9a9a;
  opacity: 1;
  /* Firefox */
}
/* line 22, ../../sass/mobile/styles_mobile_test2.scss */
.promocode_wrapper .promocode_input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #9a9a9a;
}
/* line 27, ../../sass/mobile/styles_mobile_test2.scss */
.promocode_wrapper .promocode_input::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #9a9a9a;
}

/* line 2, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.hotel_selector .selected_hotel {
  text-align: center;
  margin-bottom: 25px;
  margin-top: 10px;
  font-size: 15px;
  font-weight: bold;
  color: #5e5e5e;
}
/* line 12, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.hotel_selector.active .destination_wrapper {
  max-height: 2000px;
  padding-bottom: 30px;
}

/* line 19, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper {
  padding-bottom: 0;
  max-height: 0;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  overflow: hidden;
}
/* line 29, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_element {
  padding: 9px 0;
}
/* line 33, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_label {
  padding: 0 20px 0 25px;
  color: gray;
  font-size: 16px;
  text-transform: uppercase;
  font-weight: bold;
  position: relative;
}
/* line 41, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_label i {
  position: absolute;
  right: 20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 56, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_label i.more {
  color: #002578;
}
/* line 60, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_label i.less {
  opacity: 0;
}
/* line 66, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_options {
  max-height: 0;
  display: block;
  overflow: hidden;
  background: #f4f4f4;
  padding-left: 25px;
  font-weight: 600;
  font-size: 15px;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 80, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_options.alone_option {
  max-height: none;
}
/* line 84, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_options .hotel_element {
  margin-bottom: 20px;
  color: #5e5e5e;
}
/* line 88, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_options .hotel_element:last-of-type {
  margin-bottom: 0;
}
/* line 92, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_options .hotel_element span {
  margin-right: 7px;
}
/* line 96, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_options .hotel_element i {
  font-size: 10px;
  vertical-align: top;
  margin-top: 4px;
}
/* line 106, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_element.active .group_label .more {
  opacity: 0;
}
/* line 110, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_element.active .group_label .less {
  opacity: 1;
}
/* line 115, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_element.active .group_options {
  max-height: 750px;
  margin-top: 10px;
  padding-top: 20px;
  padding-bottom: 20px;
}

/* line 1, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
.dates_selector_wrapper {
  display: table;
  width: 100%;
}
/* line 5, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
.dates_selector_wrapper .stay_selection {
  margin: 0 20px 20px;
  display: table;
  width: calc(100% - 40px);
}
/* line 10, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
.dates_selector_wrapper .stay_selection .entry_date_wrapper, .dates_selector_wrapper .stay_selection .departure_date_wrapper {
  width: 50%;
  float: left;
  text-align: center;
  padding-top: 5px;
}
/* line 17, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
.dates_selector_wrapper .stay_selection .entry_date_wrapper {
  border-right: 1px solid #cacaca;
}

/* line 23, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
.flexible_dates_wrapper {
  position: absolute;
  top: 16%;
  bottom: 0;
  background: white;
  left: 100%;
  right: -100%;
  z-index: 2;
  transition: all .5s;
  overflow: auto;
}
/* line 34, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
.flexible_dates_wrapper.active {
  left: 0;
  right: 0;
}

/* line 40, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup, #departure_date_popup {
  position: fixed;
  top: 100%;
  bottom: -100%;
  left: 0;
  right: 0;
  z-index: 5;
  background: white;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 54, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup.active, #departure_date_popup.active {
  top: 0;
  bottom: 0;
}
/* line 59, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .header_wrapper, #departure_date_popup .header_wrapper {
  padding: 20px 20px 0;
}
/* line 62, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .header_wrapper .banner_title, #departure_date_popup .header_wrapper .banner_title {
  text-transform: uppercase;
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #424242;
  font-style: italic;
}
/* line 70, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .header_wrapper .banner_title i, #departure_date_popup .header_wrapper .banner_title i {
  margin-right: 10px;
  color: #2074ca;
  font-size: 23px;
  vertical-align: middle;
}
/* line 78, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .header_wrapper .close_popup, #departure_date_popup .header_wrapper .close_popup {
  position: absolute;
  right: 25px;
  top: 8px;
  font-size: 29px;
  color: #2074ca;
}
/* line 87, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller, #departure_date_popup .menu_controller {
  border-top: 1px solid rgba(81, 81, 81, 0.26);
  border-bottom: 1px solid rgba(81, 81, 81, 0.26);
  display: table;
  width: 100%;
  height: 20px;
}
/* line 94, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller .dates_button, #departure_date_popup .menu_controller .dates_button {
  padding: 10px 0;
  width: 50%;
  float: left;
  text-align: center;
  position: relative;
  display: table;
  height: 100%;
  text-transform: uppercase;
  font-size: 13px;
}
/* line 105, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller .dates_button span, #departure_date_popup .menu_controller .dates_button span {
  display: table-cell;
  vertical-align: middle;
}
/* line 110, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller .dates_button.active, #departure_date_popup .menu_controller .dates_button.active {
  position: relative;
}
/* line 113, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller .dates_button.active:after, #departure_date_popup .menu_controller .dates_button.active:after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: calc(100% - 1.5px);
  height: 3px;
  background: black;
  width: 100%;
}
/* line 127, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .ui-datepicker-group, #departure_date_popup .ui-datepicker-group {
  display: block;
  clear: both;
  width: 100%;
  margin-bottom: 10px;
}
/* line 134, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker, #entry_date_popup .end_datepicker, #departure_date_popup .start_datepicker, #departure_date_popup .end_datepicker {
  max-height: calc(80vh - 40px);
  overflow: auto;
}
/* line 138, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-calendar *, #entry_date_popup .end_datepicker .ui-datepicker-calendar *, #departure_date_popup .start_datepicker .ui-datepicker-calendar *, #departure_date_popup .end_datepicker .ui-datepicker-calendar * {
  border: none !important;
  border-collapse: collapse !important;
  padding: 0 !important;
  margin: 0 !important;
}
/* line 145, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline, #entry_date_popup .end_datepicker .ui-datepicker-inline, #departure_date_popup .start_datepicker .ui-datepicker-inline, #departure_date_popup .end_datepicker .ui-datepicker-inline {
  width: 100% !important;
  border: 0;
}
/* line 149, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-prev, #entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-next, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-prev, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-next, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-prev, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-next, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-prev, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-next {
  display: none;
}
/* line 153, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-header, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-header, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-header, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-header {
  background: none;
  border: 0;
  color: #515151;
  font-style: italic;
  font-weight: bold;
  border-bottom: 1px solid rgba(81, 81, 81, 0.26);
  margin-bottom: 20px;
}
/* line 163, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-state-default, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-state-default, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-state-default, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-state-default {
  text-align: center;
  background: none;
  border: 0;
  color: #515151;
  line-height: 41px;
  font-weight: lighter;
}
/* line 172, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .highlight_day, #entry_date_popup .end_datepicker .ui-datepicker-inline .highlight_day, #departure_date_popup .start_datepicker .ui-datepicker-inline .highlight_day, #departure_date_popup .end_datepicker .ui-datepicker-inline .highlight_day {
  background: #bfc0c0;
}
/* line 175, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .highlight_day a, #entry_date_popup .end_datepicker .ui-datepicker-inline .highlight_day a, #departure_date_popup .start_datepicker .ui-datepicker-inline .highlight_day a, #departure_date_popup .end_datepicker .ui-datepicker-inline .highlight_day a {
  color: white;
}
/* line 180, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day, #entry_date_popup .start_datepicker .ui-datepicker-inline .end_date_selection, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day, #entry_date_popup .end_datepicker .ui-datepicker-inline .end_date_selection, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day, #departure_date_popup .start_datepicker .ui-datepicker-inline .end_date_selection, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day, #departure_date_popup .end_datepicker .ui-datepicker-inline .end_date_selection {
  position: relative;
}
/* line 183, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #entry_date_popup .start_datepicker .ui-datepicker-inline .end_date_selection:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .end_date_selection:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .end_date_selection:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .end_date_selection:before {
  content: '';
  position: absolute;
  left: 0;
  width: 50%;
  top: 0;
  bottom: 0;
  background: #bfc0c0;
}
/* line 193, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active, #entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .date_active, #entry_date_popup .start_datepicker .ui-datepicker-inline .end_date_selection .ui-state-active, #entry_date_popup .start_datepicker .ui-datepicker-inline .end_date_selection .date_active, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .date_active, #entry_date_popup .end_datepicker .ui-datepicker-inline .end_date_selection .ui-state-active, #entry_date_popup .end_datepicker .ui-datepicker-inline .end_date_selection .date_active, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .date_active, #departure_date_popup .start_datepicker .ui-datepicker-inline .end_date_selection .ui-state-active, #departure_date_popup .start_datepicker .ui-datepicker-inline .end_date_selection .date_active, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .date_active, #departure_date_popup .end_datepicker .ui-datepicker-inline .end_date_selection .ui-state-active, #departure_date_popup .end_datepicker .ui-datepicker-inline .end_date_selection .date_active {
  color: white;
  border-radius: 50px;
  position: relative;
  z-index: 1;
}
/* line 200, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .date_active:before, #entry_date_popup .start_datepicker .ui-datepicker-inline .end_date_selection .ui-state-active:before, #entry_date_popup .start_datepicker .ui-datepicker-inline .end_date_selection .date_active:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .date_active:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .end_date_selection .ui-state-active:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .end_date_selection .date_active:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .date_active:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .end_date_selection .ui-state-active:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .end_date_selection .date_active:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .date_active:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .end_date_selection .ui-state-active:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .end_date_selection .date_active:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #515151;
  max-width: 41px;
  max-height: 41px;
  z-index: -1;
  margin: auto;
  border-radius: 41px;
}
/* line 217, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection {
  position: relative;
  opacity: 1;
}
/* line 221, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection:has(> .date_active), #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection:has(> .date_active), #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection:has(> .date_active), #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection:has(> .date_active) {
  background: linear-gradient(to right, transparent 50%, rgba(247, 187, 30, 0.5) 50%);
}
/* line 225, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection:before {
  content: '';
  position: absolute;
  right: 0;
  width: 50%;
  top: 0;
  bottom: 0;
  background: #bfc0c0;
  z-index: 0;
}
/* line 236, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span, #entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection a, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection a, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection a, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection a {
  color: white;
  border-radius: 50px;
  position: relative;
  z-index: 1;
}
/* line 244, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span:before, #entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection a:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection a:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection a:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection a:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #515151;
  max-width: 41px;
  max-height: 41px;
  z-index: -1;
  margin: auto;
  border-radius: 41px;
}
/* line 261, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline thead span, #entry_date_popup .end_datepicker .ui-datepicker-inline thead span, #departure_date_popup .start_datepicker .ui-datepicker-inline thead span, #departure_date_popup .end_datepicker .ui-datepicker-inline thead span {
  color: #515151;
}
/* line 268, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-current-day:before, #departure_date_popup .start_datepicker .ui-datepicker-current-day:before {
  display: none;
}
/* line 273, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .step_label, #departure_date_popup .step_label {
  position: absolute;
  bottom: 0;
  background: white;
  width: 100%;
  padding: 15px;
  border-top: 1px solid rgba(81, 81, 81, 0.26);
  font-size: 14px;
}

/* Calendar colors */
/* End */
/* Process buttons colors */
/* End*/
/* Fonts */
/* End */
/* Font sizes */
/* End */
/* line 3, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price, #calendar_price_availability {
  position: relative;
  overflow: hidden;
}
/* line 9, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price .header_wrapper_calendar_availability .graph_calendar_selector, #calendar_price_availability .header_wrapper_calendar_availability .graph_calendar_selector {
  display: none;
}
/* line 13, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price .header_wrapper_calendar_availability .popup_helper_wrapper, #calendar_price_availability .header_wrapper_calendar_availability .popup_helper_wrapper {
  position: fixed;
  bottom: 0;
  background: white;
  width: 100%;
  padding: 15px;
  border-top: 1px solid rgba(81, 81, 81, 0.26);
  font-size: 14px;
  z-index: 2;
}
/* line 23, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price .header_wrapper_calendar_availability .popup_helper_wrapper:after, #calendar_price_availability .header_wrapper_calendar_availability .popup_helper_wrapper:after {
  content: '\f059';
  display: inline-block;
  vertical-align: middle;
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 300;
  font-size: 16px;
  margin-left: 5px;
}
/* line 35, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar, #calendar_price_availability #prices-calendar {
  position: relative;
  margin-bottom: 16px;
}
/* line 39, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector, #calendar_price_availability #prices-calendar .popup_month_selector {
  position: absolute;
  display: flex;
  justify-content: space-between;
  width: 100%;
  top: 15px;
}
/* line 46, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector select, #calendar_price_availability #prices-calendar .popup_month_selector select {
  display: none;
}
/* line 50, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector > div, #calendar_price_availability #prices-calendar .popup_month_selector > div {
  position: relative;
  width: 30px;
  height: 30px;
}
/* line 55, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector > div::before, #calendar_price_availability #prices-calendar .popup_month_selector > div::before {
  position: absolute;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-family: "Font Awesome 5 Pro";
  color: #333;
  font-weight: 300;
  font-size: 30px;
}
/* line 66, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector .previous_month_selector::before, #calendar_price_availability #prices-calendar .popup_month_selector .previous_month_selector::before {
  content: '\f104';
}
/* line 72, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector .next_month_selector::before, #calendar_price_availability #prices-calendar .popup_month_selector .next_month_selector::before {
  content: '\f105';
}
/* line 78, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar #selectorRooms, #calendar_price_availability #prices-calendar #selectorRooms {
  display: none;
}
/* line 86, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar {
  width: 100%;
}
/* line 91, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr {
  border-top: 3px solid white;
  border-bottom: 3px solid white;
}
/* line 96, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr:nth-child(1) th, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr:nth-child(1) th {
  font-size: 1.2rem;
  padding: 15px;
  font-weight: 700;
}
/* line 103, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr:nth-child(2), #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr:nth-child(2) {
  display: none;
}
/* line 107, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td {
  vertical-align: top;
  width: calc(100vw / 7);
  position: relative;
  text-align: center;
  border-left: 3px solid white;
  border-left: 3px solid white;
  background-color: #f0f0f0;
}
/* line 116, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .another-month-day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .another-month-day {
  background-color: #dfdfdf;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
/* line 125, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day {
  font-size: 0.65rem;
  color: white;
  font-weight: 500;
  padding: 3px;
}
/* line 131, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.not-available-day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.not-available-day {
  background-color: #E75354;
}
/* line 135, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.available-day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.available-day {
  background-color: #00ac6b;
}
/* line 139, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.restricted-day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.restricted-day {
  background-color: orange;
}
/* line 143, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.restricted-day + .day-content.available .price:before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.restricted-day + .day-content.available .price:before {
  color: orange;
}
/* line 151, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content img, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content img {
  display: none;
}
/* line 155, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content .price, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content .price {
  padding-top: 15px;
  font-size: 0.65rem;
  height: 30px;
  font-weight: 500;
}
/* line 2, ../../../../../sass/styles_mobile/3/booking_engine/_variables.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.available .price::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.available .price::before {
  content: "";
  position: absolute;
  top: 28px;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: 'Font Awesome 5 Pro', 'icomoon';
  font-size: 20px;
  color: #00ac6b;
}
/* line 167, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.available .restriction-message, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.available .restriction-message {
  font-size: 0.4rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: -.3px;
}
/* line 176, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.no-available .not-available-message, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.no-available .not-available-message {
  height: 30px;
  font-size: 0.5rem;
  font-weight: 500;
  text-transform: uppercase;
  display: flex;
  align-items: center;
}
@media (max-width: 359px) {
  /* line 176, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
  #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.no-available .not-available-message, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.no-available .not-available-message {
    font-size: 0.4rem;
  }
}
/* line 192, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day, #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content {
  background-color: #7ccff4;
}
/* line 198, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content.available .price, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content.available .price {
  color: white;
}
/* line 201, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content.available .price::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content.available .price::before {
  color: white;
}
/* line 207, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content .restriction-message, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content .restriction-message {
  color: white;
}
/* line 213, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection::before, #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection::before {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  top: 50%;
  z-index: 1;
}
/* line 224, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection .day, #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection .day-content, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection .day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection .day-content {
  background-color: #1eadec;
}
/* line 228, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection::before {
  background-color: #1eadec;
  right: 0;
  transform: translate(50%, -50%) rotate(45deg);
}
/* line 236, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection .day, #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection .day-content, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection .day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection .day-content {
  background-color: #118ec6;
}
/* line 240, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection::before {
  background-color: #118ec6;
  left: 0;
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 254, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .graphs_fields_wrapper, #calendar_price_availability #prices-calendar .calendars-section .graphs_fields_wrapper {
  display: none;
}
/* line 258, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend, #calendar_price_availability #prices-calendar .calendars-section .legend {
  padding: 15px;
}
/* line 262, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li, #calendar_price_availability #prices-calendar .calendars-section .legend ul li {
  position: relative;
  padding-left: 20px;
  font-size: 0.8rem;
}
/* line 267, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li::before, #calendar_price_availability #prices-calendar .calendars-section .legend ul li::before {
  position: absolute;
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
/* line 279, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li:nth-child(1)::before, #calendar_price_availability #prices-calendar .calendars-section .legend ul li:nth-child(1)::before {
  background-color: #00ac6b;
}
/* line 285, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li:nth-child(2)::before, #calendar_price_availability #prices-calendar .calendars-section .legend ul li:nth-child(2)::before {
  background-color: orange;
}
/* line 291, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li:nth-child(3)::before, #calendar_price_availability #prices-calendar .calendars-section .legend ul li:nth-child(3)::before {
  background-color: #E75354;
}
/* line 296, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li:nth-child(4), #calendar_price_availability #prices-calendar .calendars-section .legend ul li:nth-child(4) {
  display: none;
}
/* line 304, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper {
  padding: 15px;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  font-size: 0.8rem;
  font-weight: bold;
  margin-bottom: 100px;
  width: 100%;
}
/* line 314, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div {
  width: 33%;
  padding: 5px 15px;
}
/* line 318, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.label_actual_selection, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.label_actual_selection {
  background-color: #efefef;
  color: #333;
}
/* line 323, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.nights_number_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.nights_number_wrapper {
  background-color: #a2a2a2;
  color: white;
}
/* line 327, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.nights_number_wrapper > *:first-child, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.nights_number_wrapper > *:first-child {
  display: block;
}
/* line 332, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.selection_price_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.selection_price_wrapper {
  background-color: #6f6f6f;
  color: white;
}
/* line 337, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.selection_price_wrapper .vertical_center > *:first-child, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.selection_price_wrapper .vertical_center > *:first-child {
  display: block;
}
/* line 345, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .button, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .button {
  position: fixed;
  bottom: 10px;
  left: 0;
  right: 0;
}
/* line 355, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price .modifyButtonCalendar, #calendar_price_availability .modifyButtonCalendar {
  right: 20px !important;
  left: 100% !important;
  bottom: 60px !important;
  border: 0;
  font-size: 17px;
  text-transform: uppercase;
  padding: 10px 30px;
  color: white;
  background: #f7bb1e;
  transition: left .5s, right .5s;
}
/* line 367, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price .modifyButtonCalendar.disabled-button, #calendar_price_availability .modifyButtonCalendar.disabled-button {
  opacity: 0.8;
  background: lightgray;
}

/* line 375, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
.flexible_dates_wrapper.active #calendar_price_availability .modifyButtonCalendar {
  left: auto !important;
  right: 20px !important;
}

/* line 381, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
.select-last-day {
  display: none !important;
}

/* line 2, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
.room_list_wrapper .booking_title {
  margin-bottom: 0;
}
/* line 6, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
.room_list_wrapper .total_occupancy_wrapper {
  margin: 0 20px;
  display: table;
  width: calc(100% - 40px);
  margin-bottom: 20px;
}
/* line 12, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
.room_list_wrapper .total_occupancy_wrapper .rooms_number, .room_list_wrapper .total_occupancy_wrapper .occupancy_number {
  float: left;
  text-align: center;
  padding-top: 5px;
}
/* line 18, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
.room_list_wrapper .total_occupancy_wrapper .rooms_number {
  width: 35%;
  border-right: 1px solid #cacaca;
}
/* line 22, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
.room_list_wrapper .total_occupancy_wrapper .rooms_number .rooms_number_value {
  display: block;
}
/* line 27, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
.room_list_wrapper .total_occupancy_wrapper .occupancy_number {
  width: 65%;
}
/* line 30, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
.room_list_wrapper .total_occupancy_wrapper .occupancy_number .occupancy_number_value {
  display: block;
}

/* line 37, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup {
  position: fixed;
  top: 100%;
  bottom: -100%;
  left: 0;
  right: 0;
  background: white;
  z-index: 3;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 51, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup.active {
  top: 0;
  bottom: 0;
}
/* line 56, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .header_wrapper {
  padding: 20px;
  border-bottom: 1px solid rgba(81, 81, 81, 0.26);
}
/* line 60, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .header_wrapper .banner_title {
  text-transform: uppercase;
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 0;
  color: #424242;
  font-style: italic;
}
/* line 68, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .header_wrapper .banner_title i {
  margin-right: 10px;
}
/* line 73, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .header_wrapper .close_popup {
  position: absolute;
  right: 25px;
  top: 8px;
  font-size: 29px;
  color: #2074ca;
}
/* line 82, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .rooms_wrapper {
  padding: 20px;
  max-height: 90vh;
  overflow: auto;
}
/* line 87, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .rooms_wrapper .rooms_number_wrapper {
  padding: 20px;
}
/* line 90, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .rooms_wrapper .rooms_number_wrapper .rooms_label {
  display: inline-block;
  color: #424242;
}
/* line 97, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .modification_buttons {
  width: 30%;
  float: right;
}
/* line 101, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .modification_buttons i, #occupancy_popup .modification_buttons input {
  display: inline-block;
  width: 30%;
  float: left;
  text-align: center;
  border: 0;
  font-size: 18px;
}
/* line 110, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .modification_buttons i {
  font-size: 21px;
  margin-top: 1px;
}
/* line 114, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .modification_buttons i.disabled {
  opacity: 0.4;
}
/* line 120, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .room_title {
  padding: 20px;
  background: #f4f4f4;
  font-weight: 600;
}
/* line 126, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element {
  padding: 20px;
  border-bottom: 1px solid #e2e2e2;
}
/* line 130, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element .age_selection_wrapper {
  width: 100%;
  overflow: hidden;
}
/* line 134, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element .age_selection_wrapper .age_option {
  display: inline-block;
  text-align: center;
  border: 1px solid black;
  font-size: 10px;
  padding: 3px;
  border-radius: 20px;
  width: 22px;
}
/* line 143, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element .age_selection_wrapper .age_option.active {
  background: black;
  color: white;
}
/* line 149, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element .age_selection_wrapper .block_age_selection {
  border-top: 1px solid #e2e2e2;
  padding: 13px 15px 0;
  margin-top: 15px;
  margin-left: -4%;
  margin-right: -7%;
  overflow: hidden;
}
/* line 157, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element .age_selection_wrapper .block_age_selection.hide {
  padding: 0 15px;
  max-height: 0;
  border-top: 0;
  margin-top: 0;
}
/* line 164, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element .age_selection_wrapper .block_age_selection label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}
/* line 172, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element:last-of-type {
  border-bottom: 0;
}
/* line 179, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .room_list_wrapper_close {
  padding: 15px;
  font-weight: bold;
  color: white;
  text-transform: uppercase;
  background: #002578;
  margin-top: 10px;
  text-align: center;
}
/* line 189, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .room {
  max-height: 0;
  -webkit-transition: max-height 0.5s;
  -moz-transition: max-height 0.5s;
  -ms-transition: max-height 0.5s;
  -o-transition: max-height 0.5s;
  transition: max-height 0.5s;
  overflow: hidden;
}
/* line 198, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .room label {
  color: #424242;
}
/* line 202, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .room.active {
  max-height: 450px;
  overflow: auto;
}

/* line 2, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper {
  position: relative;
}
/* line 5, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .background_image {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  max-width: 100%;
  z-index: -1;
}
/* line 15, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_title .main_title {
  color: #012379;
}
/* line 20, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper {
  padding: 0 20px;
  display: inline-block;
  width: 100%;
  position: relative;
}
/* line 26, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .input_email {
  border: 1px solid gray;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  height: 40px;
  padding: 0 10px;
  float: left;
  margin-right: 5px;
  width: calc(100% - 125px);
  font-family: "Open Sans", sans-serif;
}
/* line 38, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .input_email + .error.error_class {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  position: absolute;
  bottom: calc(100% + 5px);
  left: 20px;
  font-size: 10px;
  padding: 5px 10px;
  border-radius: 3px;
}
/* line 51, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .newsletter_button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: #012379;
  color: white;
  border: 0;
  height: 40px;
  float: left;
  width: 120px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  font-family: "Open Sans", sans-serif;
}
/* line 67, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper {
  display: inline-block;
  width: 100%;
  margin-top: 10px;
}
/* line 72, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper .newsletter_checkbox {
  margin-bottom: 0;
  display: inline-block;
  width: 100%;
  position: relative;
}
/* line 78, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper .newsletter_checkbox input {
  background-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 1px solid gray;
  width: 15px;
  height: 15px;
  vertical-align: top;
  float: left;
  margin-right: 12px;
  position: relative;
}
/* line 91, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper .newsletter_checkbox input:checked:before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background: #012379;
}
/* line 102, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper .newsletter_checkbox .check {
  display: none;
  background-color: #012379;
  position: absolute;
  top: 4px;
  left: 5px;
  width: 13px;
  height: 13px;
  z-index: -1;
}
/* line 113, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper .newsletter_checkbox .error {
  display: none !important;
}
/* line 117, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper .newsletter_checkbox label {
  font-size: 11px;
  color: gray;
  vertical-align: top;
  float: left;
  width: calc(100% - 50px);
  margin-top: 3px;
  line-height: 18px;
}
/* line 126, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper .newsletter_checkbox label.error_class {
  color: #721c24;
}
/* line 134, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .social_newsletter {
  text-align: center;
}
/* line 137, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .social_newsletter a {
  color: #012379;
  font-size: 30px;
  display: inline-block;
  margin-right: 20px;
}
/* line 146, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper {
  margin-top: 10px;
}
/* line 149, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element {
  border-bottom: 1px solid white;
}
/* line 152, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_title {
  position: relative;
  background-color: #f4f4f4;
  padding: 10px 20px;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 14px;
  color: #2d2d2d;
}
/* line 162, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_title.active .circle_icon {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
/* line 171, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_title .icon {
  color: #012379;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 20px;
  font-size: 22px;
}
/* line 177, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_title .icon .circle_icon {
  background: #012379;
  -webkit-transition: transform 0.4s;
  -moz-transition: transform 0.4s;
  -ms-transition: transform 0.4s;
  -o-transition: transform 0.4s;
  transition: transform 0.4s;
  width: 25px;
  height: 25px;
  position: relative;
  border-radius: 25px;
}
/* line 185, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_title .icon .circle_icon i {
  color: white;
  font-size: 16px;
  position: absolute;
  top: 5px;
  bottom: 0;
  left: 5px;
  right: 0;
  display: block;
}
/* line 199, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_elements_wrapper {
  padding: 10px 40px;
  display: none;
}
/* line 203, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_elements_wrapper .section_element {
  display: block;
  margin-bottom: 18px;
  color: #2d2d2d;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.6px;
}
/* line 211, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_elements_wrapper .section_element:last-of-type {
  margin-bottom: 0;
}
/* line 218, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .legal_links {
  -webkit-column-count: 3;
  -moz-column-count: 3;
  column-count: 3;
  padding: 20px;
}
/* line 224, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .legal_links .section_element {
  display: block;
  font-size: 10px;
  color: gray;
  margin-bottom: 8px;
  font-weight: 300;
}
/* line 234, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .legal {
  color: gray;
  font-size: 12px;
  text-align: center;
  padding-bottom: 20px;
}

@media screen and (max-width: 321px) {
  /* line 3, ../../../../../sass/styles_mobile/3/_responsive_styles.scss */
  .main_menu .main_ul .submenu_list .submenu_main {
    font-size: 12px !important;
  }
  /* line 6, ../../../../../sass/styles_mobile/3/_responsive_styles.scss */
  .main_menu .main_ul .submenu_list .submenu_element {
    font-size: 11px;
  }
}
@-webkit-keyframes blink {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* Standard syntax */
@keyframes blink {
  0% {
    margin-bottom: 50px;
    opacity: 0;
  }
  100% {
    margin-bottom: 0;
    opacity: 1;
  }
}
/* line 45, ../../../../../sass/styles_mobile/3/_3.scss */
.container_popup_booking img {
  background-color: #777;
}

/* line 3, ../../sass/mobile/v2/_header.scss */
.main_menu .top_wrapper .logo img {
  width: 135px;
  height: 24px;
}
/* line 10, ../../sass/mobile/v2/_header.scss */
.main_menu .top_wrapper .right_controlls .color_buttons i {
  font-size: 23px;
  font-weight: 400;
}
/* line 14, ../../sass/mobile/v2/_header.scss */
.main_menu .top_wrapper .right_controlls .color_buttons.close_button {
  position: relative;
  width: 30px;
  height: 30px;
}
/* line 18, ../../sass/mobile/v2/_header.scss */
.main_menu .top_wrapper .right_controlls .color_buttons.close_button:before, .main_menu .top_wrapper .right_controlls .color_buttons.close_button:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  display: block;
  width: 30px;
  height: 1px;
  background-color: #2074ca;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}
/* line 29, ../../sass/mobile/v2/_header.scss */
.main_menu .top_wrapper .right_controlls .color_buttons.close_button:after {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
/* line 32, ../../sass/mobile/v2/_header.scss */
.main_menu .top_wrapper .right_controlls .color_buttons.close_button i {
  display: none;
}
/* line 41, ../../sass/mobile/v2/_header.scss */
.main_menu .main_ul .submenu_main .more, .main_menu .main_ul .submenu_main .less {
  -webkit-transition: none;
  -moz-transition: none;
  -ms-transition: none;
  -o-transition: none;
  transition: none;
}
/* line 52, ../../sass/mobile/v2/_header.scss */
.main_menu .submenu_list.level2 .submenu_element a {
  color: #012379 !important;
}
/* line 56, ../../sass/mobile/v2/_header.scss */
.main_menu .submenu_list.level2 .submenu_element i {
  font-size: 11px;
}
/* line 59, ../../sass/mobile/v2/_header.scss */
.main_menu .submenu_list.level2 .submenu_element i:first-of-type {
  margin-left: 5px;
}
/* line 66, ../../sass/mobile/v2/_header.scss */
.main_menu .language_wrapper .selected_language {
  transform: translateX(-15px);
}
/* line 68, ../../sass/mobile/v2/_header.scss */
.main_menu .language_wrapper .selected_language:after {
  content: "\f078";
  font-weight: 300;
  font-family: "Font Awesome 5 Pro";
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  line-height: 1;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: -18px;
  color: #012379;
  transition: all .5s;
}

/* line 91, ../../sass/mobile/v2/_header.scss */
.default_content_wrapper.with_extra_header {
  margin-top: 80px;
}

/* line 95, ../../sass/mobile/v2/_header.scss */
header.with_extra_header {
  top: 30px;
}
/* line 99, ../../sass/mobile/v2/_header.scss */
header.with_extra_header .logo img {
  height: 22px;
}
/* line 104, ../../sass/mobile/v2/_header.scss */
header.with_extra_header .extra_top_header {
  transition: all 0.5s;
  font-size: 12px;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 31px;
  right: 0;
  width: 100%;
  background: #F3D132;
  color: #4b4b4b;
  text-align: center;
}
/* line 119, ../../sass/mobile/v2/_header.scss */
header.with_extra_header .extra_top_header i {
  display: inline-block;
  margin-right: 5px;
}
/* line 123, ../../sass/mobile/v2/_header.scss */
header.with_extra_header .extra_top_header p {
  display: inline-block;
}
/* line 126, ../../sass/mobile/v2/_header.scss */
header.with_extra_header .extra_top_header a {
  position: relative;
  color: #4b4b4b;
  text-decoration: none;
}

/* line 134, ../../sass/mobile/v2/_header.scss */
header.has_countdown,
.main_menu.has_countdown {
  z-index: 10000;
}

/* line 1, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 {
  z-index: 5;
}
/* line 3, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .booking_value {
  color: #23588a;
}
/* line 7, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .label_field {
  color: #2d2d2d;
}
/* line 12, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .booking_title .title_label {
  color: #2d2d2d;
}
/* line 16, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .booking_title i {
  color: #2074ca;
}
/* line 22, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .wrapper_booking_button .promocode_input, .mobile_engine_v2 .wrapper_booking_button .submit_button {
  font-family: "Open Sans", sans-serif;
}
/* line 26, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .wrapper_booking_button .submit_button {
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 28, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .wrapper_booking_button .submit_button.floating_button {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 4;
}
/* line 35, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .wrapper_booking_button .submit_button.hide_btn {
  display: none;
}
/* line 41, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .hotel_element {
  color: #012379 !important;
}
/* line 44, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .destiny_b0 {
  color: #0239c5;
  display: block;
  margin-bottom: 20px;
}

/* line 54, ../../sass/mobile/v2/_booking_widget.scss */
body.right_move .mobile_engine_v2 .wrapper_booking_button .submit_button.floating_button {
  left: 90%;
}
/* line 59, ../../sass/mobile/v2/_booking_widget.scss */
body.right_move header.with_extra_header .extra_top_header {
  left: 90%;
}

/* line 65, ../../sass/mobile/v2/_booking_widget.scss */
.hotel_selector .selected_hotel {
  color: #012379;
}

/* line 70, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .booking_value {
  color: #012379;
}

/* line 75, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .date_box {
  text-transform: lowercase;
}

/* line 80, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup, #departure_date_popup {
  -webkit-transition: none;
  -moz-transition: none;
  -ms-transition: none;
  -o-transition: none;
  transition: none;
}
/* line 86, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .ui-datepicker-title, #departure_date_popup .ui-datepicker-title {
  color: #012379;
}
/* line 90, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .dates_button.active, #departure_date_popup .dates_button.active {
  color: #012379;
}
/* line 93, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .dates_button.active:after, #departure_date_popup .dates_button.active:after {
  background: #23588a !important;
}
/* line 98, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .step_label, #departure_date_popup .step_label {
  color: #012379;
}
/* line 102, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .start_datepicker, #entry_date_popup .end_datepicker, #departure_date_popup .start_datepicker, #departure_date_popup .end_datepicker {
  font-family: "Open Sans", sans-serif;
}
/* line 105, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .start_datepicker .ui-datepicker-title, #entry_date_popup .end_datepicker .ui-datepicker-title, #departure_date_popup .start_datepicker .ui-datepicker-title, #departure_date_popup .end_datepicker .ui-datepicker-title {
  font-family: "Open Sans", sans-serif;
}
/* line 111, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection:before {
  background: #8fb8e3;
}
/* line 115, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span:before, #entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection a:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection a:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection a:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection a:before {
  background: #2074ca;
}
/* line 121, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before {
  background: #8fb8e3;
}
/* line 125, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before {
  background: #2074ca;
}
/* line 132, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .ui-datepicker-title, #entry_date_popup th, #entry_date_popup td, #departure_date_popup .ui-datepicker-title, #departure_date_popup th, #departure_date_popup td {
  font-family: "Open Sans", sans-serif;
}

/* line 140, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .highlight_day, #entry_date_popup .end_datepicker .ui-datepicker-inline .highlight_day, #departure_date_popup .start_datepicker .ui-datepicker-inline .highlight_day, #departure_date_popup .end_datepicker .ui-datepicker-inline .highlight_day {
  background: #8fb8e3;
}

/* line 147, ../../sass/mobile/v2/_booking_widget.scss */
.modification_buttons {
  color: #012379;
}
/* line 150, ../../sass/mobile/v2/_booking_widget.scss */
.modification_buttons input {
  color: #012379;
  font-family: "Open Sans", sans-serif;
  margin-top: -3px;
  font-weight: bold;
}

/* line 158, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup {
  z-index: 5;
}
/* line 162, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .header_wrapper .banner_title i {
  color: #2074ca;
  font-size: 23px;
  vertical-align: middle;
}
/* line 169, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .room_title {
  color: #012379;
}
/* line 174, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .block_age_selection .age_label {
  color: #012379;
}
/* line 178, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .block_age_selection .age_option {
  border: 1px solid #012379 !important;
  color: #012379 !important;
}
/* line 182, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .block_age_selection .age_option.active {
  background: #012379 !important;
  color: white !important;
}
/* line 190, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .modification_buttons input {
  margin-top: 0;
}
/* line 193, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .modification_buttons i.minus, #occupancy_popup .modification_buttons i.plus {
  position: relative;
  display: inline-block;
  width: 24px;
  height: 24px;
  vertical-align: middle;
  border: 1px solid #012379;
  border-radius: 50%;
}
/* line 201, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .modification_buttons i.minus:before, #occupancy_popup .modification_buttons i.plus:before {
  content: '';
  position: absolute;
  top: 10.5px;
  left: 5.5px;
  display: block;
  width: 11.5px;
  height: 1px;
  background-color: #012379;
}
/* line 213, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .modification_buttons i.plus:after {
  content: '';
  position: absolute;
  top: 5.5px;
  left: 10.5px;
  display: block;
  height: 11.5px;
  width: 1px;
  background-color: #012379;
}

/* line 229, ../../sass/mobile/v2/_booking_widget.scss */
.flexible_dates_wrapper {
  color: #012379;
}
/* line 232, ../../sass/mobile/v2/_booking_widget.scss */
.flexible_dates_wrapper table.calendar tr:first-of-type th {
  color: #012379;
  font-style: italic;
}

/* line 3, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .slider_inner_section img {
  width: 100%;
  vertical-align: middle;
}
/* line 9, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_hotel_title {
  text-align: center;
  margin-top: 18px;
}
/* line 13, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_hotel_title .main_title {
  color: #012379;
}
/* line 16, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_hotel_title .main_title i {
  font-size: 9px;
  vertical-align: middle;
  margin-top: -6px;
}
/* line 22, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_hotel_title .button_external_link {
  background: #F7BB1E;
  margin: 20px auto 0;
  text-transform: uppercase;
}
/* line 26, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_hotel_title .button_external_link .fas {
  margin-left: 5px;
}
/* line 32, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_hotel_services_wrapper {
  margin-top: 40px;
}
/* line 36, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .tags_wrapper {
  margin-top: 10px;
}
/* line 39, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .tags_wrapper .tag_element {
  font-size: 11px;
  display: inline-block;
  color: white;
  background: #ccc;
  padding: 5px 15px;
  font-style: italic;
  margin-bottom: 4px;
}
/* line 48, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .tags_wrapper .tag_element hide {
  display: none;
}
/* line 54, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .banner_info {
  width: 100%;
  margin: 10px auto 0;
  border: 1px solid #012379;
}
/* line 59, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .banner_info .banner_info_header {
  background-color: #012379;
  padding: 15px;
  position: relative;
  display: grid;
  grid-template-columns: 1fr minmax(auto, 60%) 1fr;
  justify-content: center;
  align-items: center;
  font-weight: 700;
  font-size: 15px;
  letter-spacing: 0.6px;
  color: white;
  cursor: pointer;
}
/* line 73, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .banner_info .banner_info_header:after {
  content: "\f13a";
  display: block;
  justify-self: end;
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
  font-size: 21px;
  color: white;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  -webkit-font-smoothing: antialiased;
}
/* line 85, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .banner_info .banner_info_header .banner_info_image {
  justify-self: start;
}
/* line 88, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .banner_info .banner_info_header .banner_info_image img {
  display: block;
  height: 100%;
  width: 100%;
  object-fit: contain;
}
/* line 93, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .banner_info .banner_info_header .banner_info_image i {
  font-size: 35px;
}
/* line 98, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .banner_info .banner_info_header .banner_info_title {
  padding: 0 10px;
}
/* line 103, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .banner_info .banner_info_content {
  box-sizing: border-box;
  position: relative;
  width: 100%;
  margin: auto;
  padding: 0 15px;
  max-height: 0;
  overflow: hidden;
  font-weight: 300;
  font-size: 15px;
  line-height: 32px;
  color: #424242;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}
/* line 118, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .banner_info .banner_info_content a {
  color: #F7BB1E;
  font-weight: 600;
}
/* line 126, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .banner_info.active .banner_info_header::after {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
  top: 31%;
}
/* line 131, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .banner_info.active .banner_info_content {
  padding: 15px;
  max-height: 1000px;
}
/* line 137, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .special_banner_container {
  margin: 20px 5%;
}
/* line 139, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .special_banner_container img {
  width: 100%;
}
/* line 144, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .icon_floating_wrapper {
  bottom: 135px;
  position: fixed;
  left: 0;
  z-index: 10;
  text-align: left;
}
/* line 151, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .icon_floating_wrapper .icon_wrapper {
  position: relative;
  display: flex;
  align-items: center;
  padding: 15px 15px 15px 20px;
  background-color: #2074ca;
  color: white;
  letter-spacing: 1.2px;
  font-size: 16px;
  margin-bottom: 10px;
  overflow: hidden;
  max-width: 500px;
  white-space: nowrap;
  -webkit-transition: max-width 0.6s;
  -moz-transition: max-width 0.6s;
  -ms-transition: max-width 0.6s;
  -o-transition: max-width 0.6s;
  transition: max-width 0.6s;
}
/* line 166, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .icon_floating_wrapper .icon_wrapper span {
  vertical-align: middle;
}
/* line 169, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .icon_floating_wrapper .icon_wrapper i {
  vertical-align: middle;
  font-size: 24px;
  margin-right: 15px;
}
/* line 175, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .icon_floating_wrapper .icon_wrapper i:first-of-type {
  border-right: 1px solid white;
  padding-right: 10px;
  margin-right: 15px;
}
/* line 181, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .icon_floating_wrapper .icon_wrapper i:last-of-type {
  border: 1px solid white;
  border-radius: 50%;
  padding: 1px 5px;
  margin-left: 15px;
  margin-right: 0;
  font-size: 18px;
  height: 21px;
}
/* line 191, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .icon_floating_wrapper .icon_wrapper.active {
  max-width: 65px;
}
/* line 193, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .icon_floating_wrapper .icon_wrapper.active i:first-of-type {
  border-right: 0;
  padding-right: 10px;
  margin-right: 15px;
}
/* line 202, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper {
  background: white;
  margin: 20px 5%;
  padding: 3% 8% 8%;
  display: block;
  width: 90%;
  box-sizing: border-box;
  max-height: 256px;
  overflow: hidden;
  position: relative;
  -webkit-transition: max-height 0.85s;
  -moz-transition: max-height 0.85s;
  -ms-transition: max-height 0.85s;
  -o-transition: max-height 0.85s;
  transition: max-height 0.85s;
  border: 1px solid #d0d0d0;
}
/* line 219, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper.active {
  max-height: 1500px;
}
/* line 223, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper.active .see_all_wrapper i {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
/* line 233, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .individual_service_item {
  display: table;
  clear: both;
  width: 100%;
  position: relative;
  border-bottom: 1px solid #d0d0d0;
  padding: 7px 0;
}
/* line 241, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .individual_service_item:first-of-type {
  padding-top: 0;
}
/* line 245, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .individual_service_item:nth-last-of-type(2) {
  padding-bottom: 0;
  border-bottom: 0;
}
/* line 251, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .individual_service_item.has_hidden_description:not(.no_text) .see_more_icon {
  display: inline-block;
}
/* line 257, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .individual_service_item.is_link_pdf .service_description:after {
  content: '\f019';
  font-family: "Font Awesome 5 Pro";
  font-weight: 900;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  font-size: 18px;
  margin-left: 5px;
  vertical-align: middle;
  color: #2074ca;
  position: absolute;
  right: 20px;
}
/* line 278, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .individual_service_item.external_link .service_description:after {
  content: '\f35d';
  font-family: "Font Awesome 5 Pro";
  font-weight: 900;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  font-size: 18px;
  margin-left: 5px;
  vertical-align: middle;
  color: #2074ca;
  position: absolute;
  right: 20px;
}
/* line 299, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_icon_wrapper {
  float: left;
  padding-top: 15%;
  width: 15%;
  position: relative;
}
/* line 305, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_icon_wrapper .service_icon {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}
/* line 314, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_icon_wrapper i {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 21px;
  color: #2074ca;
}
/* line 328, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_description {
  width: 80%;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  text-transform: uppercase;
  text-decoration: none;
  font-size: 12px;
  color: #424141;
  letter-spacing: 0.3px;
  padding-right: 40px;
}
/* line 345, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_description a {
  color: #424141;
  font-weight: 300;
}
/* line 349, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_description a.pdf {
  padding-right: 0;
}
/* line 352, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_description a.pdf:after {
  content: '\f019';
  font-family: "Font Awesome 5 Pro";
  font-weight: 900;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  font-size: 18px;
  margin-left: 10px;
  vertical-align: middle;
  color: #2074ca;
}
/* line 371, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_description hide {
  display: none;
}
/* line 376, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .see_all_wrapper {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #bbbbbb;
  padding: 2% 10%;
  text-transform: uppercase;
  color: white;
  font-weight: bold;
}
/* line 387, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .see_all_wrapper i {
  float: right;
  margin-top: 3px;
}
/* line 393, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .see_more_icon {
  display: none;
  margin-left: 10px;
  background: #2074ca;
  width: 20px;
  height: 20px;
  vertical-align: top;
  border-radius: 20px;
  position: absolute;
  right: 20px;
}
/* line 404, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .see_more_icon i {
  color: white;
  position: absolute;
  left: 0;
  top: 0;
  font-size: 13px;
  text-align: center;
  margin-top: 4px;
  margin-left: 5px;
  line-height: 13px;
}
/* line 420, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .owl-stage-outer {
  background-color: transparent;
  border-top: 30px solid transparent;
  max-height: 200px;
}
/* line 426, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .owl-dots {
  position: relative;
  width: 100%;
  padding: 0 20px;
  display: flex;
  margin-top: 30px;
}
/* line 433, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .owl-dots:before {
  content: '';
  width: calc(100% - 40px);
  height: 1px;
  position: absolute;
  top: 0;
  left: 20px;
  right: 20px;
  background-color: #012379;
}
/* line 444, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .owl-dots .owl-dot {
  flex-flow: row nowrap;
  height: 6px;
  width: 100%;
  margin-top: -3px;
  -webkit-transition: background-color 0.4s;
  -moz-transition: background-color 0.4s;
  -ms-transition: background-color 0.4s;
  -o-transition: background-color 0.4s;
  transition: background-color 0.4s;
}
/* line 451, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .owl-dots .owl-dot.active {
  background-color: #012379;
}
/* line 457, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .gallery_element {
  padding-right: 55px;
  position: relative;
  overflow: hidden;
}
/* line 462, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .gallery_element iframe {
  width: 100%;
  height: 100%;
}
/* line 467, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .gallery_element .vertical {
  margin-top: -50%;
}
/* line 471, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .gallery_element .preview_video {
  position: absolute;
  top: 0;
  right: 14.5%;
  left: 0;
  bottom: 0;
  overflow: hidden;
  -webkit-transition: top 0.5s;
  -moz-transition: top 0.5s;
  -ms-transition: top 0.5s;
  -o-transition: top 0.5s;
  transition: top 0.5s;
}
/* line 484, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .gallery_element .preview_video.hide {
  top: -150%;
  bottom: 150%;
}
/* line 488, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .gallery_element .preview_video .play_icon {
  position: absolute;
  top: 38px;
  left: 50%;
  color: white;
  font-size: 70px;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
}
/* line 505, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper {
  margin-top: 30px;
}
/* line 509, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .rooms_block_title .main_title {
  color: #012379;
}
/* line 514, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element {
  margin-top: 20px;
  position: relative;
}
/* line 518, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element:first-of-type {
  margin-top: 0;
}
/* line 522, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .occupancy_wrapper {
  position: absolute;
  z-index: 2;
  right: 30px;
  top: 30px;
  background: white;
  padding: 4px 11px;
  font-size: 14px;
  color: #424242;
}
/* line 532, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .occupancy_wrapper .label {
  margin-right: 5px;
}
/* line 536, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .occupancy_wrapper .adult {
  font-size: 13px;
}
/* line 540, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .occupancy_wrapper i {
  margin-right: 3px;
}
/* line 545, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery {
  position: relative;
  padding: 20px 20px 0;
}
/* line 549, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: calc(50% - 10px);
  width: 100%;
  background-color: #f4f4f4;
}
/* line 561, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .room_picture {
  position: relative;
  overflow: hidden;
  height: 200px;
}
/* line 566, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .room_picture img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  width: auto;
  max-width: 320px;
}
/* line 572, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .room_picture:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 2;
}
/* line 581, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .owl-nav .owl-prev, .individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .owl-nav .owl-next {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  z-index: 10;
  color: white;
  font-size: 24px;
  left: 10px;
}
/* line 589, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .owl-nav .owl-next {
  left: auto;
  right: 10px;
}
/* line 597, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content {
  margin: 0 20px;
  padding: 20px;
  border: 1px solid #d0d0d0;
  border-top: 0;
}
/* line 603, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_title {
  font-size: 19px;
  font-style: italic;
  color: #012379;
}
/* line 609, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper {
  display: inline-block;
  width: 100%;
  margin-top: 12px;
}
/* line 614, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper .room_service {
  display: inline-block;
  margin-right: 15px;
}
/* line 618, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper .room_service .service_icon {
  display: inline-block;
  margin-right: 5px;
  color: #2074ca;
}
/* line 623, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper .room_service .service_icon img {
  vertical-align: middle;
}
/* line 628, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper .room_service .service_title {
  display: inline-block;
  vertical-align: middle;
  text-transform: uppercase;
  font-size: 12px;
  color: gray;
}
/* line 640, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_description {
  text-align: left;
  font-size: 12px;
  margin-top: 10px;
  line-height: 20px;
  color: #424242;
  letter-spacing: 0.2px;
}
/* line 648, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_description div {
  text-align: justify !important;
}
/* line 652, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_description .hide {
  max-height: 0;
  overflow: hidden;
  margin-bottom: 20px;
  transition: all .4s;
  margin: 0;
}
/* line 659, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_description .hide.visible {
  margin-bottom: 20px;
  max-height: 800px;
}
/* line 665, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_description .see_more_room_description {
  font-weight: 700;
  color: #012379;
}
/* line 671, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .button-promotion {
  background-color: #F7BB1E;
  color: white;
  width: 100%;
  height: 40px;
  text-align: center;
  font-weight: 600;
  font-size: 18px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  margin-top: 20px;
  text-transform: uppercase;
  font-family: "Open Sans", sans-serif;
}
/* line 691, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .location_hotel_wrapper {
  padding: 20px 0;
  margin-top: 20px;
}
/* line 695, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .location_hotel_wrapper .maps {
  margin-bottom: 20px;
}
/* line 698, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .location_hotel_wrapper .maps iframe {
  width: 100%;
}
/* line 703, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .location_hotel_wrapper .address, .individual_hotel_section_wrapper .location_hotel_wrapper .phone, .individual_hotel_section_wrapper .location_hotel_wrapper .email, .individual_hotel_section_wrapper .location_hotel_wrapper .register {
  display: inline-block;
  width: 100%;
  padding: 0 20px;
  margin-bottom: 10px;
}
/* line 709, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .location_hotel_wrapper .address .icon, .individual_hotel_section_wrapper .location_hotel_wrapper .phone .icon, .individual_hotel_section_wrapper .location_hotel_wrapper .email .icon, .individual_hotel_section_wrapper .location_hotel_wrapper .register .icon {
  color: #2074ca;
  display: inline-block;
  font-size: 20px;
  vertical-align: top;
  margin-right: 20px;
  float: left;
  width: 20px;
}
/* line 719, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .location_hotel_wrapper .address .title, .individual_hotel_section_wrapper .location_hotel_wrapper .phone .title, .individual_hotel_section_wrapper .location_hotel_wrapper .email .title, .individual_hotel_section_wrapper .location_hotel_wrapper .register .title {
  display: inline-block;
  color: #424242;
  float: left;
  letter-spacing: 0.2px;
  line-height: 24px;
  font-size: 14px;
  max-width: 85%;
  text-align: left;
}
/* line 731, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .location_hotel_wrapper .register {
  margin-bottom: 0;
}
/* line 736, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper {
  padding: 0;
  border: 1px solid #cacaca;
  margin: 0 20px 20px;
  position: relative;
}
/* line 742, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .trust_informative_title {
  border-bottom: 1px solid #cacaca;
  padding: 20px 40px;
  text-align: left;
}
/* line 747, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .trust_informative_title .title {
  color: #012379;
  font-size: 15px;
  text-transform: uppercase;
  font-weight: 700;
}
/* line 755, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block {
  width: 67%;
  display: inline-block;
  position: relative;
  vertical-align: top;
  padding: 7px 0;
}
/* line 762, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block .trust_small_element {
  zoom: 0.6;
  position: relative;
  display: inline-block;
  margin: 20px 0;
}
/* line 768, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block .trust_small_element .badges i {
  margin-right: 0 !important;
}
/* line 772, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block .trust_small_element .counter {
  display: none !important;
}
/* line 777, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block .partners_wrapper {
  width: 40%;
  display: inline-block;
  vertical-align: middle;
  margin-top: 2px;
  margin-left: 15px;
}
/* line 783, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block .partners_wrapper .partner_element {
  width: 100%;
  display: block;
  padding-right: 15px;
  padding-top: 15px;
}
/* line 791, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block.full_width {
  width: 100%;
  text-align: center;
}
/* line 795, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block.full_width .partners_wrapper {
  width: auto;
}
/* line 798, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block.full_width .partners_wrapper .partner_element {
  display: inline-block;
  width: auto;
  max-height: 75px;
  max-width: 100%;
  margin: 0 auto;
  vertical-align: middle;
}
/* line 806, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block.full_width .partners_wrapper .partner_element:last-of-type {
  margin-bottom: 0;
}
/* line 814, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .right_block {
  width: 30%;
  display: inline-block;
  vertical-align: top;
  border-left: 1px solid #f4f4f4;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
}
/* line 824, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .right_block .link_informative_paper {
  text-align: center;
  font-size: 14px;
  color: gray;
  width: 100%;
  display: block;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 838, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .right_block .link_informative_paper i {
  display: block;
  margin-top: 5px;
  font-size: 18px;
  color: #2074ca;
}

/* line 851, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_services_wrapper.promotions_wrapper .services_individual_hotel_wrapper {
  padding: 3% 10%;
}
/* line 855, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_services_wrapper.promotions_wrapper .service_description {
  position: relative;
  top: auto;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}
/* line 861, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_services_wrapper.promotions_wrapper .individual_service_item {
  padding: 3% 0;
}
/* line 864, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_services_wrapper.promotions_wrapper .individual_service_item:last-of-type {
  border-bottom: 0;
  padding-bottom: 0;
}
/* line 869, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_services_wrapper.promotions_wrapper .individual_service_item:nth-last-of-type(2) {
  padding-bottom: 3%;
  border-bottom: 1px solid #d0d0d0;
}
/* line 875, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_services_wrapper.promotions_wrapper .promotion_description {
  display: none;
}
/* line 879, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_services_wrapper.promotions_wrapper .see_more_icon {
  display: block !important;
  position: absolute;
  left: 100%;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

/* line 888, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper {
  width: calc(100% + 20px);
  margin: 40px -10px;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}
/* line 895, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper * {
  box-sizing: border-box;
}
/* line 900, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .banners_bottom_title .main_title {
  color: #012379;
}
/* line 906, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl_banners_carousel {
  position: relative;
}
/* line 909, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl_banners_carousel:before {
  content: '';
  width: 100%;
  background-color: #F4F4F4;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 40%;
}
/* line 920, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-item {
  padding-left: 15px;
  padding-right: 50px;
}
/* line 924, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-item .banner_element {
  display: inline-block;
  width: 100%;
  position: relative;
  margin-top: 10px;
  border: 1px solid #d0d0d0;
}
/* line 931, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-item .banner_element hide {
  display: none;
}
/* line 935, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-item .banner_element .image_wrapper {
  width: 40%;
  height: 100%;
  display: inline-block;
  vertical-align: top;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
}
/* line 946, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-item .banner_element .image_wrapper .image_element {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
/* line 951, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper {
  width: 60%;
  display: inline-block;
  vertical-align: top;
  background-color: #fff;
  padding: 10px;
  float: right;
  margin-bottom: auto;
  border-left: 0;
}
/* line 962, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .main_title {
  margin-top: 0;
  font-size: 18px;
  color: #012379;
  height: 40px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  /* -> number of lines to show, NO height required in the container <- */
  -webkit-box-orient: vertical;
  font-weight: bold;
  font-style: italic;
  margin-bottom: 10px;
}
/* line 70, ../../../../../sass/plugins/_only_mixins.scss */
.banner_news_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .main_title hr {
  display: none;
}
/* line 974, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .news_description {
  font-size: 12px;
  line-height: 15px;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 10px;
  color: #424242;
}
/* line 986, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .see_more_news {
  font-weight: 700;
  font-size: 12px;
  color: #012379;
}
/* line 992, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .see_news_link {
  display: inline-block;
  width: 100%;
  background-color: #F7BB1E;
  color: white;
  text-transform: uppercase;
  padding: 5px 8px;
  text-align: center;
  font-size: 14px;
  margin-top: 8px;
  text-decoration: none;
  font-weight: 700;
  letter-spacing: 1.3px;
}
/* line 1010, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-dots {
  position: relative;
  width: 100%;
  padding: 0 20px;
  display: flex;
  margin-top: 40px;
}
/* line 1017, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-dots:before {
  content: '';
  width: calc(100% - 40px);
  height: 1px;
  position: absolute;
  top: 0;
  left: 20px;
  right: 20px;
  background-color: #012379;
}
/* line 1028, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-dots .owl-dot {
  position: relative;
  overflow: hidden;
  flex-flow: row nowrap;
  height: 6px;
  width: 100%;
  margin-top: -3px;
}
/* line 1036, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-dots .owl-dot:before {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: -101%;
  background-color: #012379;
  -webkit-transition: left 0.4s;
  -moz-transition: left 0.4s;
  -ms-transition: left 0.4s;
  -o-transition: left 0.4s;
  transition: left 0.4s;
}
/* line 1049, ../../sass/mobile/v2/_individual_hotels.scss */
.banner_news_wrapper .carousel_wrapper .owl-dots .owl-dot.active:before {
  left: 0;
}

/* line 1058, ../../sass/mobile/v2/_individual_hotels.scss */
.see_more_news_content_main_title {
  color: #012379;
  font-size: 18px;
  font-style: italic;
  margin-bottom: 10px;
}

/* line 1064, ../../sass/mobile/v2/_individual_hotels.scss */
.news_description img {
  max-width: 100%;
}

/*=== FAQS ====*/
/* line 1069, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper {
  color: gray;
  margin-bottom: 30px;
}
/* line 1073, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .general_title_block {
  margin-bottom: 20px;
}
/* line 1076, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .general_title_block .main_title {
  color: #012379;
}
/* line 1081, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper {
  margin: 0 20px;
  padding: 7px 0;
}
/* line 1085, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .filters_button {
  margin: 0 0 10px;
  border: 1px solid #d0d0d0;
}
/* line 1090, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element {
  margin-bottom: 10px;
  display: block;
}
/* line 1095, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element.hide {
  max-height: 0;
  overflow: hidden;
  border: 0;
  margin: 0;
}
/* line 1102, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element .question {
  padding: 14px 50px 14px 20px;
  position: relative;
  font-weight: bold;
  border: 1px solid #d0d0d0;
  font-size: 14px;
  color: #424242;
}
/* line 1110, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element .question:after {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  content: "\f13a";
  color: #2074ca;
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
  transition: all 0.5s;
}
/* line 1122, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element .question i {
  position: absolute;
  right: 20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
  color: #2074ca;
}
/* line 1139, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element .question .less {
  opacity: 0;
}
/* line 1145, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element.active .question:after {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
  top: 31%;
}
/* line 1151, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element .answer {
  margin: 0 7px;
  padding: 0 18px;
  background: white;
  max-height: 0;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  font-size: 12px;
  letter-spacing: 0.2px;
  color: #424242;
  line-height: 20px;
}
/* line 1167, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element .answer .faqs_link {
  display: block;
  text-align: center;
  text-transform: uppercase;
  font-size: 12px;
  margin-top: 15px;
  color: #2074ca;
  font-weight: 600;
  letter-spacing: 0.2px;
}
/* line 1180, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element.active .answer {
  padding: 10px 18px;
  max-height: none;
}
/* line 1185, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element.active .less {
  opacity: 1;
}
/* line 1189, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element.active .more {
  opacity: 0;
}

/* line 1198, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery.has_margin {
  padding: 20px;
}
/* line 1201, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery .filters {
  padding: 35px 0;
  display: flex;
  overflow-y: scroll;
}
/* line 1205, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery .filters .filter {
  color: #424242;
  cursor: pointer;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 300;
}
/* line 1212, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery .filters .filter:hover, #hotel_gallery .filters .filter.active {
  color: #2074ca;
  font-weight: bold;
}
/* line 1216, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery .filters .filter:not(:first-of-type), #hotel_gallery .filters .filter:not(:last-of-type) {
  padding-right: 20px;
}
/* line 1222, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery .gallery_individual_blocks .desktop_block_element {
  min-height: 250px;
  max-height: 250px;
  display: flex !important;
}
/* line 1228, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery .gallery_individual_blocks .desktop_block_element.video_element .button_play {
  cursor: pointer;
  position: absolute;
  z-index: 2;
  top: 35%;
  right: 40%;
  border: 1px solid white;
  border-radius: 50%;
  width: 70px;
  height: 70px;
}
/* line 1239, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery .gallery_individual_blocks .desktop_block_element.video_element .button_play i {
  color: white;
  font-weight: 700;
  font-size: 25px;
  transform: translate(-40%, -40%);
  position: absolute;
  top: 50%;
  left: 50%;
}
/* line 1251, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery .gallery_individual_blocks .desktop_block_element .video_container {
  width: 90%;
  margin: 0 auto;
}
/* line 1256, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery .gallery_individual_blocks .desktop_block_element .image_wrapper {
  position: relative;
}
/* line 1258, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery .gallery_individual_blocks .desktop_block_element .image_wrapper img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
/* line 1261, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery .gallery_individual_blocks .desktop_block_element .image_wrapper .picture_text {
  position: absolute;
  filter: drop-shadow(2px 4px 6px rgba(0, 0, 0, 0.5));
  color: white;
  left: 20px;
  bottom: 10px;
}
/* line 1271, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery .gallery_individual_blocks .owl-nav .owl-prev, #hotel_gallery .gallery_individual_blocks .owl-nav .owl-next {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  right: auto;
  left: 15px;
}
/* line 1275, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery .gallery_individual_blocks .owl-nav .owl-prev i, #hotel_gallery .gallery_individual_blocks .owl-nav .owl-next i {
  font-size: 40px;
  color: #2074ca;
}
/* line 1280, ../../sass/mobile/v2/_individual_hotels.scss */
#hotel_gallery .gallery_individual_blocks .owl-nav .owl-next {
  right: -5px;
  left: auto;
}

/* line 1, ../../sass/mobile/v2/_hotels_section.scss */
.filters_button {
  margin: 0 30px;
  border: 1px solid #8e8c8c;
  padding: 15px;
  text-align: center;
  background: white;
  text-transform: uppercase;
  font-weight: bold;
  color: #424242;
  font-size: 12px;
  letter-spacing: 0.6px;
}
/* line 13, ../../sass/mobile/v2/_hotels_section.scss */
.filters_button i {
  color: #2074ca;
  margin-right: 10px;
  font-size: 20px;
  font-weight: 300;
  vertical-align: middle;
}

/* line 23, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_buttons_wrapper {
  display: table;
  width: 100%;
  margin: 30px 0;
  text-align: center;
  border-top: 1px solid #cacaca;
  border-bottom: 1px solid #cacaca;
}
/* line 31, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_buttons_wrapper .view_list, .hotels_buttons_wrapper .view_map {
  width: 50%;
  float: left;
  padding: 15px 0;
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 600;
  color: gray;
  letter-spacing: 0.6px;
}
/* line 41, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_buttons_wrapper .view_list i, .hotels_buttons_wrapper .view_map i {
  font-size: 20px;
  vertical-align: middle;
  margin-right: 9px;
}
/* line 47, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_buttons_wrapper .view_list.active, .hotels_buttons_wrapper .view_map.active {
  color: #012379;
  border-bottom: 3px solid #012379;
}

/* line 54, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_found {
  margin: 30px 20px 0;
  font-style: italic;
  font-size: 24px;
  line-height: 25px;
  color: #012379;
}

/* line 62, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper {
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  max-height: fit-content;
}
/* line 70, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper.hide {
  margin-left: -100%;
  margin-right: 100%;
  max-height: 80vh;
}
/* line 76, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element {
  margin: 0 20px 30px;
  border: 1px solid #cacaca;
}
/* line 80, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element.hide {
  display: none;
}
/* line 84, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_image_wrapper {
  height: 160px;
  position: relative;
  overflow: hidden;
}
/* line 89, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_image_wrapper .since_price {
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 2;
  background: white;
  padding: 8px 7px 9px;
  text-transform: uppercase;
  font-size: 19px;
  font-weight: bolder;
  line-height: 16px;
  color: #012379;
  text-align: center;
}
/* line 103, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_image_wrapper .since_price small {
  display: block;
  font-size: 11px;
  font-weight: 500;
  color: #4e4e4e;
  text-transform: uppercase;
}
/* line 112, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_image_wrapper .hotel_image {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
/* line 117, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_info {
  margin: 20px;
  padding: 0 0 15px;
  border-bottom: 1px solid #d4d4d4;
}
/* line 122, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_info .hotel_destiny {
  margin: 0;
  text-transform: uppercase;
  color: #424242;
  font-size: 11px;
}
/* line 129, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_info .hotel_name {
  margin: 0;
  font-style: italic;
  margin-top: 5px;
  font-size: 21px;
  color: #012379;
}
/* line 136, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_info .hotel_name i {
  font-size: 13px;
  vertical-align: middle;
}
/* line 143, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls {
  margin: 15px;
  text-align: center;
}
/* line 147, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls .inline_button {
  width: auto;
  display: inline-block;
  text-align: center;
  font-size: 11px;
  text-decoration: none;
  color: #2074ca;
  text-transform: uppercase;
  font-weight: 500;
  margin-right: 10px;
}
/* line 158, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls .inline_button:last-of-type {
  margin-right: 0;
}
/* line 162, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls .inline_button i {
  font-size: 20px;
  vertical-align: middle;
  margin-right: 3px;
}
/* line 169, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls .hotel_link {
  text-transform: uppercase;
}
/* line 173, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls .booking_button {
  display: block;
  width: 100%;
  text-align: center;
  color: white;
  margin: 15px 0 10px;
  padding: 13px 0;
  font-weight: bold;
  text-transform: uppercase;
  background: #f6bb33;
}
/* line 185, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls .hotel_link {
  display: block;
  text-align: center;
  color: #2074ca;
  font-weight: bold;
  font-size: 12px;
}
/* line 194, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls.without_promotions .inline_button {
  font-size: 13px;
  width: 32%;
  margin-right: 0;
}
/* line 199, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls.without_promotions .inline_button i {
  font-size: 22px;
}
/* line 206, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_tags {
  margin-top: 10px;
  display: flex;
}
/* line 210, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_tags .tag_element {
  font-size: 9px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 300;
  color: white;
  background: #ccc;
  padding: 5px 10px;
  font-style: italic;
  width: 100%;
  white-space: nowrap;
}
/* line 223, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_tags .tag_element:not(:first-of-type) {
  margin-left: 2px;
}
/* line 226, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_tags .tag_element:nth-child(n+4) {
  display: none;
}

/* line 234, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper {
  position: relative;
  overflow: hidden;
}
/* line 238, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper #maps_wrapper {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  min-height: 80vh;
  z-index: 10;
  background: white;
}
/* line 254, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper #maps_wrapper.hide {
  top: 100%;
  bottom: -100%;
}
/* line 259, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper #maps_wrapper .map_carousel_hotels {
  margin: 0;
  margin-top: -20px;
  background: white;
  position: relative;
}
/* line 265, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper #maps_wrapper .map_carousel_hotels .owl-dots {
  margin-top: 10px;
}
/* line 270, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper #maps_wrapper .header_map {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.23) 0%, transparent 100%);
  z-index: 3;
}
/* line 278, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper #maps_wrapper .header_map #close_map {
  float: right;
  margin: 10px 20px;
  font-size: 30px;
  color: white;
}
/* line 286, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper #maps_wrapper #map {
  width: 100%;
  height: calc(100% - 152px);
}

/* line 293, ../../sass/mobile/v2/_hotels_section.scss */
.popup_hotel_info, .popup_hotel_map, .popup_hotel_offers {
  display: none;
}

/* line 297, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner {
  position: fixed;
  top: 100%;
  left: 0;
  right: 0;
  bottom: -100%;
  background: white;
  z-index: 4;
  padding: 20px;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 312, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner.active {
  top: 0;
  bottom: 0;
}
/* line 317, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner #close_filters {
  position: absolute;
  right: 25px;
  top: 8px;
  font-size: 29px;
  color: #2074ca;
}
/* line 325, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .banner_title {
  text-transform: uppercase;
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #424242;
}
/* line 332, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .banner_title i {
  margin-right: 10px;
  font-size: 23px;
  font-weight: 100;
  color: #2074ca;
  vertical-align: middle;
}
/* line 341, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper {
  border: 1px solid #cacaca;
  max-height: 80vh;
  overflow: auto;
}
/* line 346, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_title {
  border-bottom: 1px solid #cacaca;
  padding: 15px;
  text-transform: uppercase;
  color: #424242;
  font-size: 14px;
  position: relative;
}
/* line 354, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_title .more, #filter_wrapper_banner .availabler_filters_wrapper .filter_title .less {
  position: absolute;
  right: 10px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  color: #012379;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 371, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_title .less {
  opacity: 0;
}
/* line 377, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .destiny_filters_wrapper .filter_title {
  border-top: 0;
}
/* line 383, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .offers_filters_wrapper .filter_title {
  border-bottom: 0;
}
/* line 388, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list {
  padding: 0 15px;
  max-height: 0;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 398, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list.active {
  padding: 8px 15px;
  max-height: 600px;
}
/* line 403, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list .option_element {
  color: gray;
  font-weight: bold;
  text-transform: uppercase;
  margin-bottom: 5px;
}
/* line 409, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list .option_element input {
  margin: 0;
  vertical-align: middle;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 15px;
  height: 15px;
  position: relative;
  border: 0;
}
/* line 425, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list .option_element input + label:before {
  content: '';
  top: 10px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  position: absolute;
  left: -26px;
  width: 16px;
  height: 16px;
  border-radius: 20px;
  border: 1px solid;
}
/* line 442, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list .option_element input:focus {
  outline: 0;
}
/* line 446, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list .option_element input:checked + label {
  color: #012379;
}
/* line 448, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list .option_element input:checked + label:after {
  content: '';
  top: 10px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  position: absolute;
  left: -22px;
  width: 10px;
  height: 10px;
  border-radius: 20px;
  background: #012379;
}
/* line 467, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list .option_element label {
  margin-left: 10px;
  position: relative;
  font-size: 14px;
  line-height: 2;
}
/* line 479, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_block.active .filter_title .less {
  opacity: 1;
}
/* line 483, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_block.active .filter_title .more {
  opacity: 0;
}
/* line 488, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_block.active .options_list {
  padding: 8px 15px;
  max-height: 600px;
  background: #f4f4f4;
  box-shadow: inset 0px -1px 0 #cacaca;
}
/* line 496, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_block.active.offers_filters_wrapper .filter_title {
  border-bottom: 1px solid #cacaca;
}
/* line 500, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_block.active.offers_filters_wrapper .options_list {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
/* line 510, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .filters_buttons_wrapper {
  margin-top: 20px;
}
/* line 513, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .filters_buttons_wrapper #clear_filters_button, #filter_wrapper_banner .filters_buttons_wrapper #apply_filters_button {
  display: inline-block;
  float: left;
  width: 48%;
  font-size: 11px;
  border: 1px solid #9b9b9b;
  text-align: center;
  padding: 10px 0;
  color: #424242;
  font-weight: bold;
  text-transform: uppercase;
}
/* line 526, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .filters_buttons_wrapper #apply_filters_button {
  float: right;
  color: white;
  border: 1px solid #2074ca;
  background: #2074ca;
}

/* line 536, ../../sass/mobile/v2/_hotels_section.scss */
.promotions_list_popup ul {
  list-style: circle;
}

/* line 1, ../../sass/mobile/v2/_contact_section.scss */
.general_title_block.contact_content {
  margin-top: 18px;
  text-align: center;
}
/* line 6, ../../sass/mobile/v2/_contact_section.scss */
.general_title_block.contact_content table td {
  display: block;
  width: 100%;
}

/* line 13, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper {
  margin: 0 20px;
  margin-bottom: 30px;
  border: 1px solid #d0d0d0;
  padding: 20px;
}
/* line 19, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contact_form_title {
  font-style: italic;
  font-size: 24px;
  line-height: 25px;
  color: #012379;
  margin-bottom: 25px;
}
/* line 27, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .separator_title {
  color: #2074ca;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 10px;
  padding-top: 10px;
  border-top: 1px solid #d0d0d0;
  margin-top: 10px;
}
/* line 38, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput {
  border: 1px solid #d0d0d0;
  margin-bottom: 10px;
  padding: 10px;
  position: relative;
}
/* line 44, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput input[type='radio'] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 16px;
  margin-left: 20px;
}
/* line 51, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput input[type='radio']:checked + label:after {
  content: '';
  top: 8px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  position: absolute;
  left: -22px;
  width: 10px;
  height: 10px;
  border-radius: 20px;
  background: #012379;
}
/* line 68, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput input[type='radio'] + label:before {
  content: '';
  top: 8px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  position: absolute;
  left: -26px;
  width: 16px;
  height: 16px;
  border-radius: 20px;
  border: 1px solid;
}
/* line 86, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput label {
  font-weight: bold;
  font-size: 12px;
  color: #424242;
  display: block;
  position: relative;
}
/* line 93, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput label.error {
  color: red;
  font-weight: lighter;
  font-size: 11px;
  position: absolute;
  bottom: 0;
  top: 100%;
}
/* line 103, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput input {
  width: 100%;
  padding: 10px 0;
  font-family: "Open Sans", sans-serif;
}
/* line 61, ../../../../../sass/compass/css3/_user-interface.scss */
.contact_wrapper .contInput input:-moz-placeholder {
  font-style: italic;
  color: #9e9e9e;
}
/* line 64, ../../../../../sass/compass/css3/_user-interface.scss */
.contact_wrapper .contInput input::-moz-placeholder {
  font-style: italic;
  color: #9e9e9e;
}
/* line 67, ../../../../../sass/compass/css3/_user-interface.scss */
.contact_wrapper .contInput input:-ms-input-placeholder {
  font-style: italic;
  color: #9e9e9e;
}
/* line 56, ../../../../../sass/compass/css3/_user-interface.scss */
.contact_wrapper .contInput input::-webkit-input-placeholder {
  font-style: italic;
  color: #9e9e9e;
}
/* line 113, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput input[type='radio'] {
  display: inline-table;
  width: auto;
  margin-right: 5px;
}
/* line 118, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput input[type='radio'] + label {
  display: inline-block;
  margin-right: 40px;
}
/* line 125, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput.input-error, .contact_wrapper .contInput .input-error {
  border: 1px solid red;
}
/* line 129, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput select {
  width: 100%;
  font-family: "Open Sans", sans-serif;
}
/* line 134, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput textarea {
  display: block;
  width: 100%;
}
/* line 140, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .g-recaptcha {
  display: block;
  width: 100%;
  margin-bottom: 10px;
}
/* line 146, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .g-recaptcha.error iframe {
  outline: 1px solid red;
}
/* line 152, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .legend_label {
  display: block;
  font-weight: lighter;
  font-size: 11px;
  color: #424242;
  font-style: italic;
  margin-top: 10px;
  text-align: right;
}
/* line 162, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper a.myFancyPopup {
  font-weight: bold;
  font-size: 12px;
  color: #424242;
  display: inline-block;
  text-decoration: underline;
}
/* line 170, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper #contact-button {
  background-color: #012379;
  color: white;
  border: 0;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  font-family: "Open Sans", sans-serif;
  text-align: center;
  padding: 10px 0;
  margin-bottom: 15px;
}
/* line 183, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .check_privacy {
  background-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 1px solid gray;
  width: 15px;
  height: 15px;
  float: left;
  margin-right: 12px;
  position: relative;
  vertical-align: middle;
  margin-top: 6px;
}
/* line 197, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .check_privacy:checked:before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background: #012379;
}

/* line 212, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .privacy_wrapper {
  display: block;
  width: 100%;
  clear: both;
}
/* line 217, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .privacy_wrapper input[type='checkbox'] {
  vertical-align: middle;
}
/* line 222, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .option_block {
  display: table;
  width: 100%;
  margin-bottom: 20px;
}
/* line 227, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .option_block:last-of-type {
  margin-bottom: 0;
}
/* line 231, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .option_block label {
  width: 55%;
  float: left;
  margin-top: 3px;
}
/* line 237, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .option_block .controlls {
  width: 25%;
  float: right;
}
/* line 241, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .option_block .controlls .less, .groups_contact .option_block .controlls input, .groups_contact .option_block .controlls .plus {
  display: inline-block;
  vertical-align: middle;
  color: #012379;
}
/* line 246, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .option_block .controlls .less.disabled, .groups_contact .option_block .controlls input.disabled, .groups_contact .option_block .controlls .plus.disabled {
  opacity: 0.4;
}
/* line 251, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .option_block .controlls input {
  width: 20px;
  text-align: center;
  padding: 0;
  vertical-align: middle;
  font-weight: bold;
  font-size: 15px;
}
/* line 264, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .contInput .tooltip {
  opacity: 0;
  height: 0;
  transition: all .3s ease-in-out;
  position: absolute;
  top: -30px;
  left: 0;
  width: 100%;
  background: #F7BB1E;
  color: white;
  text-align: center;
  padding: 5px;
  border-radius: 5px;
  z-index: 2;
  font-size: 12px;
  font-weight: 400;
}
/* line 281, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .contInput .tooltip::after {
  position: absolute;
  left: 50%;
  bottom: -5px;
  content: "";
  width: 15px;
  height: 15px;
  border-radius: 2px;
  transform: rotate(45deg) translate(-25%, 25%);
  background-color: #F7BB1E;
  z-index: -1;
}
/* line 294, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .contInput .tooltip.show {
  opacity: 1;
  height: auto;
  transform: translateY(15px);
}
/* line 301, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .contInput.disabled {
  opacity: .7;
  position: relative;
}

/* line 1, ../../sass/mobile/v2/_advance_configs.scss */
.top_ticks_icons_wrapper {
  margin: 0 20px 30px;
  background: #f4f4f4;
  padding: 16px 0;
}
/* line 6, ../../sass/mobile/v2/_advance_configs.scss */
.top_ticks_icons_wrapper .top_tick_element {
  width: 32%;
  display: inline-block;
  text-align: center;
  font-size: 9px;
  text-transform: uppercase;
  padding: 0 20px;
  position: relative;
}
/* line 15, ../../sass/mobile/v2/_advance_configs.scss */
.top_ticks_icons_wrapper .top_tick_element:after {
  content: '';
  width: 1px;
  height: 50px;
  display: block;
  background: gray;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 32, ../../sass/mobile/v2/_advance_configs.scss */
.top_ticks_icons_wrapper .top_tick_element:last-of-type:after {
  display: none;
}
/* line 37, ../../sass/mobile/v2/_advance_configs.scss */
.top_ticks_icons_wrapper .top_tick_element .tick_image_element {
  max-height: 20px;
  margin-bottom: 5px;
}
/* line 45, ../../sass/mobile/v2/_advance_configs.scss */
.top_ticks_icons_wrapper:lang(nl) .top_tick_element .tick_title {
  font-size: 8px;
}

/* line 52, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info {
  margin-bottom: 30px;
}
/* line 55, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info .deploy_banner_block {
  margin: 0 20px 0;
}
/* line 58, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info .deploy_banner_block .deploy_element {
  border-bottom: 2px solid white;
  margin-bottom: 10px;
}
/* line 62, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info .deploy_banner_block .deploy_element .deploy_title {
  padding: 14px 50px 14px 20px;
  position: relative;
  font-weight: bold;
  border: 1px solid #d0d0d0;
  font-size: 14px;
  color: #424242;
}
/* line 70, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info .deploy_banner_block .deploy_element .deploy_title:after {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  content: "\f13a";
  color: #2074ca;
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
  transition: all 0.5s;
}
/* line 83, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info .deploy_banner_block .deploy_element .deploy_description {
  padding: 0 20px;
  font-size: 13px;
  color: gray;
  max-height: 0;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 97, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info .deploy_banner_block .deploy_element.active .deploy_title:after {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
  top: 31%;
}
/* line 102, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info .deploy_banner_block .deploy_element.active .deploy_description {
  max-height: 1000px;
  padding: 20px;
}
/* line 110, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info #popup_best_price_link {
  text-align: center;
  text-transform: uppercase;
  font-size: 12px;
  margin-top: 15px;
  color: #2074ca;
  font-weight: 600;
  letter-spacing: 0.2px;
}

/* line 121, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price {
  position: fixed;
  top: 100%;
  left: 0;
  bottom: -100%;
  right: 0;
  background: white;
  z-index: 4;
  padding: 0;
  overflow: scroll;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 137, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .popup_best_price_wrapper {
  padding: 20px;
}
/* line 141, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price.active {
  top: 0;
  bottom: 0;
}
/* line 146, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price #close_best_price {
  position: absolute;
  right: 25px;
  top: 8px;
  font-size: 29px;
  color: #002578;
}
/* line 154, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .hidden {
  display: none;
}
/* line 158, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .popup_title {
  text-transform: uppercase;
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #424242;
}
/* line 165, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .popup_title i {
  margin-right: 10px;
  color: #317abe;
  font-size: 23px;
  vertical-align: middle;
  font-weight: 100;
}
/* line 174, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price #best-price-contact-form {
  max-height: 90vh;
}
/* line 178, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price h4 {
  text-transform: uppercase;
  border-bottom: 1px solid lightgray;
  padding-bottom: 10px;
  margin-bottom: 15px;
  font-size: 14px;
  color: #424242;
  letter-spacing: 0.2px;
}
/* line 188, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block {
  border: 1px solid lightgray;
  padding: 10px;
  margin-bottom: 10px;
  display: table;
  width: 100%;
}
/* line 195, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block label, #popup_best_price .form_block .question_label {
  display: block;
  font-weight: bold;
  color: #424242;
  font-size: 14px;
  margin-left: 5px;
}
/* line 203, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block input {
  width: 100%;
  padding: 6px 0;
  font-style: italic;
}
/* line 16, ../../sass/mobile/styles_mobile_test2.scss */
#popup_best_price .form_block input::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #b6b6b6;
  opacity: 1;
  /* Firefox */
}
/* line 22, ../../sass/mobile/styles_mobile_test2.scss */
#popup_best_price .form_block input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #b6b6b6;
}
/* line 27, ../../sass/mobile/styles_mobile_test2.scss */
#popup_best_price .form_block input::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #b6b6b6;
}
/* line 61, ../../../../../sass/compass/css3/_user-interface.scss */
#popup_best_price .form_block input:-moz-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 64, ../../../../../sass/compass/css3/_user-interface.scss */
#popup_best_price .form_block input::-moz-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 67, ../../../../../sass/compass/css3/_user-interface.scss */
#popup_best_price .form_block input:-ms-input-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 56, ../../../../../sass/compass/css3/_user-interface.scss */
#popup_best_price .form_block input::-webkit-input-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 214, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block textarea {
  display: block;
  width: 100%;
}
/* line 219, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block + h4 {
  margin-top: 25px;
}
/* line 223, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block select {
  width: 100%;
  padding: 7px 0 0;
  color: #b6b6b6;
  font-style: italic;
}
/* line 230, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block .radio_block_x2 {
  display: inline-block;
  width: 50%;
  float: left;
  margin-top: 10px;
}
/* line 236, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block .radio_block_x2 input, #popup_best_price .form_block .radio_block_x2 label {
  display: inline-block;
  width: auto;
  position: relative;
}
/* line 242, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block .radio_block_x2 input[type='radio'] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 16px;
}
/* line 248, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block .radio_block_x2 input[type='radio'] + label:before {
  content: '';
  top: 11px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  position: absolute;
  left: -26px;
  width: 16px;
  height: 16px;
  border-radius: 20px;
  border: 1px solid;
}
/* line 264, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block .radio_block_x2 input[type='radio']:focus {
  outline: 0;
}
/* line 268, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block .radio_block_x2 input[type='radio']:checked + label {
  color: #012379;
}
/* line 271, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block .radio_block_x2 input[type='radio']:checked + label:after {
  content: '';
  top: 11px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  position: absolute;
  left: -22px;
  width: 10px;
  height: 10px;
  border-radius: 20px;
  background: #012379;
}
/* line 290, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block #upload_photo_button {
  display: table;
  margin: 15px auto 10px;
  padding: 13px 35px;
  border: 1px solid gray;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 12px;
  color: #424242;
  letter-spacing: 0.2px;
}
/* line 303, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price #submit_button {
  width: 100%;
  background: #2074ca;
  color: white;
  text-align: center;
  padding: 15px 0;
  text-transform: uppercase;
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 10px;
}
/* line 315, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .small_info {
  color: #c7c7c7;
  font-style: italic;
  font-weight: lighter;
  text-align: center;
  display: block;
  font-size: 12px;
  margin-bottom: 20px;
}

/* line 327, ../../sass/mobile/v2/_advance_configs.scss */
.carousel_wrapper .since_wrapper {
  color: #012379;
}

/* line 332, ../../sass/mobile/v2/_advance_configs.scss */
.banner_marcas_wrapper {
  margin: 70px 0;
}
/* line 336, ../../sass/mobile/v2/_advance_configs.scss */
.banner_marcas_wrapper .container12 .top_banner {
  text-align: left;
  margin-bottom: 40px;
  margin-left: 20px;
}
/* line 342, ../../sass/mobile/v2/_advance_configs.scss */
.banner_marcas_wrapper .container12 .top_banner .content_title .title {
  margin: 0;
  font-style: italic;
  font-size: 24px;
  line-height: 25px;
  color: #012379;
  font-weight: bolder;
}
/* line 350, ../../sass/mobile/v2/_advance_configs.scss */
.banner_marcas_wrapper .container12 .top_banner .content_title .title span {
  margin: 0;
  text-transform: uppercase;
  color: #424242;
  font-size: 12px;
  font-weight: 300;
  letter-spacing: 0.3px;
  display: block;
  font-style: normal;
}
/* line 364, ../../sass/mobile/v2/_advance_configs.scss */
.banner_marcas_wrapper .container12 .banner {
  background-color: #f4f4f4;
  position: relative;
  padding: 50px 30px;
}
/* line 369, ../../sass/mobile/v2/_advance_configs.scss */
.banner_marcas_wrapper .container12 .banner .content_wrapper {
  margin-bottom: 40px;
}
/* line 372, ../../sass/mobile/v2/_advance_configs.scss */
.banner_marcas_wrapper .container12 .banner .content_wrapper:not(:first-child) {
  border-top: solid 1px #012379;
  padding-top: 40px;
  margin-bottom: 0;
}
/* line 378, ../../sass/mobile/v2/_advance_configs.scss */
.banner_marcas_wrapper .container12 .banner .content_wrapper .logo_wrapper {
  margin-bottom: 30px;
}
/* line 381, ../../sass/mobile/v2/_advance_configs.scss */
.banner_marcas_wrapper .container12 .banner .content_wrapper .logo_wrapper img {
  max-height: 70px;
  width: auto;
}
/* line 387, ../../sass/mobile/v2/_advance_configs.scss */
.banner_marcas_wrapper .container12 .banner .content_wrapper .content_title {
  margin-bottom: 20px;
}
/* line 390, ../../sass/mobile/v2/_advance_configs.scss */
.banner_marcas_wrapper .container12 .banner .content_wrapper .content_title .title {
  font-weight: 700;
  color: #012379;
}
/* line 396, ../../sass/mobile/v2/_advance_configs.scss */
.banner_marcas_wrapper .container12 .banner .content_wrapper .desc {
  font-weight: 300;
  position: relative;
  font-size: 15px;
  line-height: 22px;
  margin-bottom: 20px;
}
/* line 405, ../../sass/mobile/v2/_advance_configs.scss */
.banner_marcas_wrapper .container12 .banner .content_wrapper .links_wrapper .link_more {
  font-size: 13px;
  text-transform: uppercase;
  font-weight: 700;
  color: #012379;
  text-decoration: none;
}

/* line 422, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper_carousel .general_title_block .mini_title {
  text-align: left;
}
/* line 427, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper_carousel .general_title_block .main_title {
  text-align: left;
}
/* line 430, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper_carousel .general_title_block .main_title .desc {
  margin-top: 20px;
}
/* line 433, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper_carousel .general_title_block .main_title .desc > div {
  display: none;
}
/* line 438, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper_carousel .general_title_block .main_title .desc a span {
  display: inline-block !important;
  color: #012379;
  font-weight: 700;
  font-size: 15px;
  letter-spacing: 0.6px;
  font-style: normal;
}
/* line 447, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper_carousel .general_title_block .main_title .desc a img {
  display: none;
}
/* line 455, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper_carousel .ticks_wrapper {
  background: #f4f4f4;
  margin-top: 20px;
}
/* line 459, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper_carousel .ticks_wrapper .tick_element {
  text-align: center;
  padding: 20px 20px 60px;
  background-repeat: no-repeat;
  background-size: 120px auto;
  background-position: center 10px;
}
/* line 466, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper_carousel .ticks_wrapper .tick_element .tick_text {
  margin-bottom: 20px;
}
/* line 469, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper_carousel .ticks_wrapper .tick_element .tick_text strong.corporate_1 {
  color: #012379;
  font-weight: 700;
  font-size: 35px;
}
/* line 477, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper_carousel .ticks_wrapper .owl-nav {
  position: absolute;
  bottom: 20px;
  display: flex;
  left: 50%;
  transform: translateX(-50%);
}
/* line 484, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper_carousel .ticks_wrapper .owl-nav > div {
  margin: 0 5px;
}
/* line 487, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper_carousel .ticks_wrapper .owl-nav > div i {
  font-size: 22px;
}

/* line 497, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper .tick_element.first {
  margin-bottom: 15px;
  padding-bottom: 15px;
}
/* line 502, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper .tick_icon {
  color: #2074ca !important;
}
/* line 506, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper .external_advantage_link {
  color: #2074ca;
}

/* line 511, ../../sass/mobile/v2/_advance_configs.scss */
.banners_block_bottom .bottom_banner_element .bottom_block_image {
  max-height: 112%;
}

/* line 516, ../../sass/mobile/v2/_advance_configs.scss */
.carousel_wrapper, .banners_carousel_wrapper {
  position: relative;
  overflow: hidden;
  width: 95vw;
}
/* line 522, ../../sass/mobile/v2/_advance_configs.scss */
.carousel_wrapper .general_title_block hide, .banners_carousel_wrapper .general_title_block hide {
  display: none;
}
/* line 527, ../../sass/mobile/v2/_advance_configs.scss */
.carousel_wrapper .subtitle_wrapper, .banners_carousel_wrapper .subtitle_wrapper {
  padding: 0 20px;
  font-size: 12px;
  color: #424242;
  margin-top: 20px;
  margin-bottom: 10px;
  line-height: 20px;
  letter-spacing: 0.2px;
}

/* line 538, ../../sass/mobile/v2/_advance_configs.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .tags_wrapper .tag_element {
  padding: 0 7px;
}
/* line 541, ../../sass/mobile/v2/_advance_configs.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .tags_wrapper .tag_element:lang(nl) {
  font-size: 8px;
}

/* line 547, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper {
  margin: 0 20px;
}
/* line 550, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block {
  margin-bottom: 50px;
}
/* line 553, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block .category_title {
  font-weight: bold;
  color: #2074ca;
  margin-bottom: 30px;
}
/* line 559, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block .collapse_block {
  margin-bottom: 10px;
}
/* line 562, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block .collapse_block .collapse_title {
  padding: 14px 50px 14px 20px;
  position: relative;
  font-weight: bold;
  border: 1px solid #d0d0d0;
  font-size: 14px;
  color: #424242;
}
/* line 570, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block .collapse_block .collapse_title:after {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  content: "\f13a";
  color: #2074ca;
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
  transition: all 0.5s;
}
/* line 583, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block .collapse_block .element_description {
  padding: 0 18px;
  background: white;
  max-height: 0;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  font-size: 12px;
  letter-spacing: 0.2px;
  color: #424242;
  line-height: 20px;
  margin: 0 20px;
}
/* line 601, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block .collapse_block.show .collapse_title:after {
  top: 26%;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
/* line 607, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block .collapse_block.show .collapse_description .element_description {
  max-height: 300px;
  margin: 20px;
  color: #646464;
}

/* line 620, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_title .tick_text {
  color: #2d2d2d;
}

/* line 626, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper,
.cycle_banners_wrapper_2 {
  margin: 0 20px;
  display: block;
}
/* line 631, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element,
.cycle_banners_wrapper_2 .cycle_element {
  margin-bottom: 30px;
}
/* line 634, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .image_wrapper,
.cycle_banners_wrapper_2 .cycle_element .image_wrapper {
  width: 100%;
  overflow: hidden;
  height: 170px;
  position: relative;
}
/* line 640, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .image_wrapper .image_element,
.cycle_banners_wrapper_2 .cycle_element .image_wrapper .image_element {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  margin: auto;
  min-width: 100%;
  min-height: 100%;
  width: 100%;
}
/* line 653, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .cycle_content,
.cycle_banners_wrapper_2 .cycle_element .cycle_content {
  border: 1px solid #cacaca;
  border-top: 0;
  padding: 20px;
}
/* line 658, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .cycle_content .cycle_title,
.cycle_banners_wrapper_2 .cycle_element .cycle_content .cycle_title {
  font-style: italic;
  font-size: 21px;
  color: #012379;
  margin-bottom: 20px;
  border-bottom: 1px solid #cacaca;
  padding-bottom: 10px;
}
/* line 667, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .cycle_content .read_more,
.cycle_banners_wrapper_2 .cycle_element .cycle_content .read_more {
  display: none;
}
/* line 671, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .cycle_content .cycle_description,
.cycle_banners_wrapper_2 .cycle_element .cycle_content .cycle_description {
  font-size: 12px;
  color: #424242;
  line-height: 20px;
  letter-spacing: 0.2px;
  overflow: hidden;
  transition: max-height 0.5s;
}
/* line 679, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .cycle_content .cycle_description.exceded,
.cycle_banners_wrapper_2 .cycle_element .cycle_content .cycle_description.exceded {
  max-height: 80px;
  overflow: hidden;
  -webkit-transition: max-height 0.5s;
  -moz-transition: max-height 0.5s;
  -ms-transition: max-height 0.5s;
  -o-transition: max-height 0.5s;
  transition: max-height 0.5s;
}
/* line 684, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .cycle_content .cycle_description.exceded + a + .read_more, .cycle_banners_wrapper .cycle_element .cycle_content .cycle_description.exceded + .read_more,
.cycle_banners_wrapper_2 .cycle_element .cycle_content .cycle_description.exceded + a + .read_more,
.cycle_banners_wrapper_2 .cycle_element .cycle_content .cycle_description.exceded + .read_more {
  display: inline-block;
  color: #012379;
  text-decoration: none;
  margin-top: 20px;
  -webkit-transition: color 0.6s;
  -moz-transition: color 0.6s;
  -ms-transition: color 0.6s;
  -o-transition: color 0.6s;
  transition: color 0.6s;
  font-weight: 700;
  font-size: 15px;
  letter-spacing: 0.6px;
  margin-left: 30px;
  height: 20px;
  vertical-align: top;
  overflow: hidden;
  cursor: pointer;
}
/* line 699, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .cycle_content .cycle_description.exceded + a + .read_more:hover, .cycle_banners_wrapper .cycle_element .cycle_content .cycle_description.exceded + .read_more:hover,
.cycle_banners_wrapper_2 .cycle_element .cycle_content .cycle_description.exceded + a + .read_more:hover,
.cycle_banners_wrapper_2 .cycle_element .cycle_content .cycle_description.exceded + .read_more:hover {
  color: #012379;
}
/* line 703, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .cycle_content .cycle_description.exceded + a + .read_more .more, .cycle_banners_wrapper .cycle_element .cycle_content .cycle_description.exceded + .read_more .more,
.cycle_banners_wrapper_2 .cycle_element .cycle_content .cycle_description.exceded + a + .read_more .more,
.cycle_banners_wrapper_2 .cycle_element .cycle_content .cycle_description.exceded + .read_more .more {
  transition: margin 0.5s;
}
/* line 708, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .cycle_content .cycle_description.exceded + a + .read_more.active .more, .cycle_banners_wrapper .cycle_element .cycle_content .cycle_description.exceded + .read_more.active .more,
.cycle_banners_wrapper_2 .cycle_element .cycle_content .cycle_description.exceded + a + .read_more.active .more,
.cycle_banners_wrapper_2 .cycle_element .cycle_content .cycle_description.exceded + .read_more.active .more {
  margin-top: -20px;
}
/* line 714, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .cycle_content .cycle_description.exceded + .read_more,
.cycle_banners_wrapper_2 .cycle_element .cycle_content .cycle_description.exceded + .read_more {
  margin-left: 0;
}
/* line 718, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .cycle_content .cycle_description.exceded.visible,
.cycle_banners_wrapper_2 .cycle_element .cycle_content .cycle_description.exceded.visible {
  max-height: 1300px;
}
/* line 725, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .see_more_button,
.cycle_banners_wrapper_2 .cycle_element .see_more_button {
  font-style: italic;
  font-size: 14px;
  color: #012379;
  margin-top: 20px;
  display: none;
  text-align: right;
}
/* line 735, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element.exceded_height .cycle_description,
.cycle_banners_wrapper_2 .cycle_element.exceded_height .cycle_description {
  max-height: 440px;
}
/* line 739, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element.exceded_height .see_more_button,
.cycle_banners_wrapper_2 .cycle_element.exceded_height .see_more_button {
  display: block;
}
/* line 745, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element.deploy .cycle_description,
.cycle_banners_wrapper_2 .cycle_element.deploy .cycle_description {
  max-height: 3000px;
}
/* line 749, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element.deploy .see_more_button,
.cycle_banners_wrapper_2 .cycle_element.deploy .see_more_button {
  display: none;
}

/* line 759, ../../sass/mobile/v2/_advance_configs.scss */
.carousel_wrapper .promotion_element .main_title {
  height: auto !important;
}
/* line 763, ../../sass/mobile/v2/_advance_configs.scss */
.carousel_wrapper .promotion_element .description {
  color: #424242;
  font-size: 12px;
  letter-spacing: 0.3px;
  max-height: 20px;
  overflow: hidden;
}
/* line 771, ../../sass/mobile/v2/_advance_configs.scss */
.carousel_wrapper .promotion_element .content_wrapper {
  height: 135px;
  position: relative;
}
/* line 775, ../../sass/mobile/v2/_advance_configs.scss */
.carousel_wrapper .promotion_element .content_wrapper .see_hotel_link {
  position: absolute;
  bottom: 15px;
  left: 10px;
  right: 10px;
  width: auto !important;
}

/* line 788, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper {
  margin-bottom: 40px;
}
/* line 791, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper .carousel_banner_element {
  display: block;
  margin: auto;
  width: 80%;
  background-color: white;
  border: 1px solid #cacaca;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
/* line 801, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper .carousel_banner_element .image_wrapper {
  position: relative;
  width: 100%;
  height: 270px;
  overflow: hidden;
}
/* line 807, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper .carousel_banner_element .image_wrapper img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  max-height: 130%;
  width: auto;
}
/* line 814, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper .carousel_banner_element .carousel_content {
  padding: 13px 17px 85px;
  text-align: left;
  position: relative;
}
/* line 819, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper .carousel_banner_element .carousel_content .mini_title {
  margin: 0;
  color: #424242;
  font-size: 11px;
  text-align: left;
}
/* line 826, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper .carousel_banner_element .carousel_content .main_title {
  margin: 0;
  font-style: italic;
  font-weight: 700;
  color: #012379;
  height: auto;
  font-size: 25px;
  padding-bottom: 0;
  line-height: 30px;
  min-height: 44px;
}
/* line 838, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper .carousel_banner_element .carousel_content .carousel_description {
  font-size: 12px;
  color: #2d2d2d;
  height: 30px;
  overflow: hidden;
  margin-bottom: 10px;
}
/* line 846, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper .carousel_banner_element .carousel_content .carousel_link {
  display: block;
  text-align: center;
  background: #F7BB1E;
  text-decoration: none;
  width: 50%;
  color: white;
  text-transform: uppercase;
  letter-spacing: 1.3px;
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  font-size: 20px;
  font-weight: 700;
  padding: 8px;
  cursor: pointer;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 865, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper .carousel_banner_element .carousel_content .carousel_link:hover {
  background: #a97c06;
}
/* line 872, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper .banner_carousel_wrapper {
  width: 100%;
  clear: both;
  text-align: center;
  display: table;
}
/* line 878, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper .banner_carousel_wrapper.owl-carousel {
  display: block;
}
/* line 885, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper .owl-nav .owl-prev, .banner_carousel_full_wrapper .owl-nav .owl-next {
  position: absolute;
  left: 12px;
  top: 50%;
  font-size: 30px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 892, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper .owl-nav .owl-prev i, .banner_carousel_full_wrapper .owl-nav .owl-next i {
  color: #a5a5a5;
}
/* line 897, ../../sass/mobile/v2/_advance_configs.scss */
.banner_carousel_full_wrapper .owl-nav .owl-next {
  left: auto;
  right: 14px;
}

/* line 905, ../../sass/mobile/v2/_advance_configs.scss */
.missing_links {
  margin-bottom: 40px;
}
/* line 908, ../../sass/mobile/v2/_advance_configs.scss */
.missing_links .link_element {
  color: white;
  background: #012379;
  display: block;
  text-align: center;
  width: 70%;
  padding: 10px 20px;
  margin: 0 auto 20px;
}

/* line 921, ../../sass/mobile/v2/_advance_configs.scss */
.hotel_review_selector {
  width: 90%;
  display: block;
  margin: auto;
  position: relative;
}
/* line 927, ../../sass/mobile/v2/_advance_configs.scss */
.hotel_review_selector:after {
  position: absolute;
  right: 20px;
  top: 35%;
  transform: translateY(-50%);
  content: "\f13a";
  color: #2074ca;
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
  transition: all 0.5s;
  font-size: 21px;
}
/* line 940, ../../sass/mobile/v2/_advance_configs.scss */
.hotel_review_selector select {
  width: 100%;
  margin: auto;
  display: block;
  -webkit-appearance: none;
  background: white;
  border: 0;
  border-bottom: 1px solid;
  text-align: center;
  padding-bottom: 10px;
  color: #2074ca;
  font-size: 16px;
}
/* line 953, ../../sass/mobile/v2/_advance_configs.scss */
.hotel_review_selector select option {
  text-align: center;
}

/* line 959, ../../sass/mobile/v2/_advance_configs.scss */
.trust_wrapper_element {
  padding: 0 5%;
}
/* line 962, ../../sass/mobile/v2/_advance_configs.scss */
.trust_wrapper_element iframe {
  height: 2479px;
}

/* line 1, ../../sass/mobile/v2/_minigallery.scss */
#minigallery_pictures {
  position: relative;
  margin: 30px 0;
}
/* line 5, ../../sass/mobile/v2/_minigallery.scss */
#minigallery_pictures .minigallery_element {
  height: 300px;
  position: relative;
  overflow: hidden;
}
/* line 10, ../../sass/mobile/v2/_minigallery.scss */
#minigallery_pictures .minigallery_element .mini_gallery_elem_picture {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  max-height: 320px;
}
/* line 17, ../../sass/mobile/v2/_minigallery.scss */
#minigallery_pictures .flex-direction-nav .flex-prev, #minigallery_pictures .flex-direction-nav .flex-next {
  top: 0 !important;
  display: block;
  opacity: 1;
  height: 100%;
}
/* line 23, ../../sass/mobile/v2/_minigallery.scss */
#minigallery_pictures .flex-direction-nav .flex-prev .fa, #minigallery_pictures .flex-direction-nav .flex-next .fa {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}
/* line 27, ../../sass/mobile/v2/_minigallery.scss */
#minigallery_pictures .flex-direction-nav .flex-prev:before, #minigallery_pictures .flex-direction-nav .flex-next:before {
  display: none;
}
/* line 31, ../../sass/mobile/v2/_minigallery.scss */
#minigallery_pictures .flex-direction-nav .flex-prev .fa {
  left: 10px;
}
/* line 35, ../../sass/mobile/v2/_minigallery.scss */
#minigallery_pictures .flex-direction-nav .flex-next .fa {
  right: 10px;
}
/* line 40, ../../sass/mobile/v2/_minigallery.scss */
#minigallery_pictures .flex-nav-next, #minigallery_pictures .flex-nav-prev {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  background: rgba(56, 56, 56, 0.6);
  width: 50px;
}
/* line 50, ../../sass/mobile/v2/_minigallery.scss */
#minigallery_pictures .flex-nav-prev {
  right: auto;
  left: 0;
}
/* line 55, ../../sass/mobile/v2/_minigallery.scss */
#minigallery_pictures .right_arrow_types, #minigallery_pictures .left_arrow_types {
  color: white;
  font-size: 24px;
  margin-right: 5px;
}
/* line 61, ../../sass/mobile/v2/_minigallery.scss */
#minigallery_pictures .left_arrow_types {
  margin-left: 0;
  margin-left: 5px;
}

/* line 3, ../../sass/mobile/v2/_general_styles.scss */
.big_title_corporate {
  color: #012379;
  font-style: italic;
  letter-spacing: 1px;
}

/* line 9, ../../sass/mobile/v2/_general_styles.scss */
.waves {
  width: 80px;
  height: 30px;
  margin: 7px 0;
  background: url("https://lh3.googleusercontent.com/JZmInHjOiTjGzz-EcAJaquGA0zpHIUi5SEuJdmldOrXmFRJ_bSyq8OnHSCVGWwtDP9FyeRJW7yQCpdV370fpGvj7") no-repeat center;
  background-size: contain;
}

/* line 17, ../../sass/mobile/v2/_general_styles.scss */
.corporate_button1 {
  font-size: 13px;
  color: white;
  background: #012379;
  display: table;
  padding: 5px 15px;
}

/* line 25, ../../sass/mobile/v2/_general_styles.scss */
#popup_website_v3 .popup_content {
  font-size: 13px;
  max-height: 80vh;
  overflow: auto;
}
/* line 30, ../../sass/mobile/v2/_general_styles.scss */
#popup_website_v3 .popup_content .popup_hotel_info_title {
  font-size: 18px;
  color: #2074ca;
  font-weight: bold;
  font-style: italic;
  margin-bottom: 16px;
}

/* line 39, ../../sass/mobile/v2/_general_styles.scss */
.slider_inner_section {
  height: 75px;
  overflow: hidden;
  position: relative;
}
/* line 44, ../../sass/mobile/v2/_general_styles.scss */
.slider_inner_section img {
  position: absolute;
  top: -50%;
  bottom: -50%;
  left: 0;
  right: 0;
  margin: auto;
}

/* line 54, ../../sass/mobile/v2/_general_styles.scss */
a {
  color: #2074ca;
  font-weight: bold;
}

/* line 59, ../../sass/mobile/v2/_general_styles.scss */
#popup_website_v3 {
  background: transparent;
}
/* line 64, ../../sass/mobile/v2/_general_styles.scss */
#popup_website_v3 .popup_content .promotions_list_popup .title_promotion {
  font-size: 16px;
  margin-bottom: 8px;
  display: grid;
  grid-template-columns: auto 20px;
  align-items: center;
}
/* line 71, ../../sass/mobile/v2/_general_styles.scss */
#popup_website_v3 .popup_content .promotions_list_popup .title_promotion i {
  color: #2074ca;
}
/* line 76, ../../sass/mobile/v2/_general_styles.scss */
#popup_website_v3 .popup_content .promotions_list_popup .description_promotion {
  display: none;
  margin-bottom: 8px;
  margin-left: 8px;
}

/* line 85, ../../sass/mobile/v2/_general_styles.scss */
#popup_v3_overlay {
  background: rgba(255, 255, 255, 0.97);
}

/* line 89, ../../sass/mobile/v2/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .image_wrapper .image_element {
  max-height: 100%;
}

/* line 93, ../../sass/mobile/v2/_general_styles.scss */
.corporate_icon {
  margin-right: 5px;
  color: #012379;
}

/* line 100, ../../sass/mobile/v2/_general_styles.scss */
.map_iframe_fancybox .fancybox-stage .fancybox-slide {
  padding-left: 0;
  padding-right: 0;
}

/* line 107, ../../sass/mobile/v2/_general_styles.scss */
.breadcrumbs_wrapper {
  margin-top: 10px;
  padding: 0 20px;
}
/* line 110, ../../sass/mobile/v2/_general_styles.scss */
.breadcrumbs_wrapper .breadcrumb_element, .breadcrumbs_wrapper .separator {
  display: inline-block;
  font-weight: bold;
  font-size: 12px;
  letter-spacing: 0.6px;
  margin-right: 5px;
  color: #2074ca;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  text-decoration: none;
  padding-bottom: 3px;
}
/* line 123, ../../sass/mobile/v2/_general_styles.scss */
.breadcrumbs_wrapper .active, .breadcrumbs_wrapper .breadcrumb_element:hover {
  border-bottom: 1px solid;
}

/* line 128, ../../sass/mobile/v2/_general_styles.scss */
#booking_engine_popup .mobile_engine_v2 .booking_engine_mobile {
  padding-bottom: 60px;
}

/* line 1, ../../sass/mobile/v2/_my_reservation.scss */
.my_reservation_section {
  margin-top: 18px;
  text-align: center;
}

/* line 7, ../../sass/mobile/v2/_my_reservation.scss */
.general_title_block.my_bookings {
  margin-bottom: 15px;
}
/* line 10, ../../sass/mobile/v2/_my_reservation.scss */
.general_title_block + .my_reservation_form {
  margin-top: 0;
}

/* line 15, ../../sass/mobile/v2/_my_reservation.scss */
.my-reservation-form {
  margin: 20px;
  border: 1px solid #d0d0d0;
  padding: 13px;
}
/* line 20, ../../sass/mobile/v2/_my_reservation.scss */
.my-reservation-form .form_block {
  border: 1px solid lightgray;
  padding: 10px;
  margin-bottom: 10px;
  display: table;
  width: 100%;
}
/* line 27, ../../sass/mobile/v2/_my_reservation.scss */
.my-reservation-form .form_block label {
  display: block;
  font-weight: bold;
  color: #424242;
  font-size: 14px;
  margin-left: 5px;
}
/* line 35, ../../sass/mobile/v2/_my_reservation.scss */
.my-reservation-form .form_block input {
  width: 100%;
  padding: 6px 0;
  font-style: italic;
}
/* line 16, ../../sass/mobile/styles_mobile_test2.scss */
.my-reservation-form .form_block input::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #b6b6b6;
  opacity: 1;
  /* Firefox */
}
/* line 22, ../../sass/mobile/styles_mobile_test2.scss */
.my-reservation-form .form_block input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #b6b6b6;
}
/* line 27, ../../sass/mobile/styles_mobile_test2.scss */
.my-reservation-form .form_block input::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #b6b6b6;
}
/* line 61, ../../../../../sass/compass/css3/_user-interface.scss */
.my-reservation-form .form_block input:-moz-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 64, ../../../../../sass/compass/css3/_user-interface.scss */
.my-reservation-form .form_block input::-moz-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 67, ../../../../../sass/compass/css3/_user-interface.scss */
.my-reservation-form .form_block input:-ms-input-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 56, ../../../../../sass/compass/css3/_user-interface.scss */
.my-reservation-form .form_block input::-webkit-input-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 47, ../../sass/mobile/v2/_my_reservation.scss */
.my-reservation-form a[data-role='button'] {
  background: #012379;
  color: white;
  float: right;
  padding: 10px 30px;
  font-size: 14px;
  text-transform: uppercase;
}

/* line 1, ../../sass/mobile/v2/_work_blocks_section.scss */
.job_offers_found {
  margin: 30px 20px;
  font-style: italic;
  font-size: 22px;
  line-height: 28px;
  color: #012379;
}

/* line 9, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper {
  margin: 0 20px;
}
/* line 12, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element {
  border: 1px solid #cacaca;
  margin-bottom: 20px;
  padding: 20px;
  display: table;
}
/* line 18, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element.hide {
  display: none;
}
/* line 22, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .destiny_title {
  text-transform: uppercase;
  color: #424242;
  font-size: 12px;
  letter-spacing: 0.3px;
  font-weight: 600;
}
/* line 30, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .job_title {
  margin: 0;
  font-style: italic;
  font-size: 24px;
  line-height: 25px;
  color: #012379;
  margin-bottom: 5px;
}
/* line 38, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .read_more {
  display: none;
}
/* line 41, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .job_description {
  font-size: 12px;
  color: #424242;
  margin-top: 20px;
  line-height: 20px;
  letter-spacing: 0.2px;
}
/* line 47, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .job_description.exceded {
  max-height: 80px;
  overflow: hidden;
  -webkit-transition: max-height 0.5s;
  -moz-transition: max-height 0.5s;
  -ms-transition: max-height 0.5s;
  -o-transition: max-height 0.5s;
  transition: max-height 0.5s;
}
/* line 52, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .job_description.exceded + a + .read_more, .work_wrapper .job_element .job_description.exceded + .read_more {
  display: inline-block;
  color: #012379;
  text-decoration: none;
  margin-top: 20px;
  -webkit-transition: color 0.6s;
  -moz-transition: color 0.6s;
  -ms-transition: color 0.6s;
  -o-transition: color 0.6s;
  transition: color 0.6s;
  font-weight: 700;
  font-size: 15px;
  letter-spacing: 0.6px;
  margin-left: 30px;
  height: 20px;
  vertical-align: top;
  overflow: hidden;
  cursor: pointer;
}
/* line 67, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .job_description.exceded + a + .read_more:hover, .work_wrapper .job_element .job_description.exceded + .read_more:hover {
  color: #F7BB1E;
}
/* line 71, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .job_description.exceded + a + .read_more .more, .work_wrapper .job_element .job_description.exceded + .read_more .more {
  transition: margin 0.5s;
}
/* line 76, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .job_description.exceded + a + .read_more.active .more, .work_wrapper .job_element .job_description.exceded + .read_more.active .more {
  margin-top: -20px;
}
/* line 82, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .job_description.exceded + .read_more {
  margin-left: 0;
}
/* line 86, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .job_description.exceded.visible {
  max-height: 1300px;
}
/* line 92, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .job_header {
  padding-bottom: 20px;
  border-bottom: 1px solid #cacaca;
}
/* line 97, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .subscribe_job {
  display: block;
  width: 100%;
  text-align: center;
  color: white;
  margin: 15px 0 10px;
  padding: 13px 0;
  font-weight: bold;
  text-transform: uppercase;
  background: #012379;
  font-family: "Open Sans", sans-serif;
  border: 0;
  font-size: 16px;
}
/* line 112, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .hotels_wrapper {
  font-size: 11px;
  color: #2074ca;
}
/* line 116, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .hotels_wrapper .hotel_name {
  margin-right: 10px;
}
/* line 121, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .more_info_job_wrapper {
  display: none;
}
/* line 125, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .see_more_job_description {
  display: block;
  text-align: center;
  color: #2074ca;
  font-weight: bold;
  font-size: 12px;
}
/* line 134, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .work_filters_button {
  width: 100%;
  margin: 0;
  margin-bottom: 20px;
  border: 1px solid #cacaca;
}

/* line 143, ../../sass/mobile/v2/_work_blocks_section.scss */
.laboral_offer_type .filter_title {
  border-bottom: 0 !important;
}

/* line 1, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info {
  padding: 0 20px 20px;
}
/* line 3, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block {
  border: 1px solid grey;
}
/* line 5, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block .step_title {
  padding: 10px 20px;
  border-bottom: 1px solid grey;
  font-size: 18px;
  font-weight: 700;
  color: grey;
}
/* line 12, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block .steps_follow {
  padding: 10px;
}
/* line 14, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block .steps_follow .step_number {
  position: relative;
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: top;
  margin-right: 15px;
  border-radius: 50%;
  background-color: #2074ca;
  color: white;
  font-size: 14px;
  line-height: 1;
  font-weight: 700;
}
/* line 27, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block .steps_follow .step_number .number {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 31, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block .steps_follow .step_description {
  font-size: 12px;
  color: #424242;
  line-height: 20px;
  letter-spacing: 0.2px;
  display: inline-block;
  vertical-align: top;
  margin-bottom: 15px;
  width: calc(100% - 45px);
}
/* line 41, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block .steps_follow .image_club {
  display: block;
  max-width: 80%;
  margin: 10px auto;
}
/* line 46, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block .steps_follow .conditions_step {
  font-size: 12px;
  color: #424242;
  line-height: 20px;
  letter-spacing: 0.2px;
  display: block;
}
/* line 56, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .advatages_block {
  margin-top: 20px;
  border: 1px solid grey;
}
/* line 59, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .advatages_block .advantages_title {
  padding: 10px 20px;
  border-bottom: 1px solid grey;
  font-size: 18px;
  font-weight: 700;
  color: grey;
}
/* line 66, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .advatages_block .advantages_description {
  font-size: 12px;
  color: #424242;
  line-height: 20px;
  letter-spacing: 0.2px;
  padding: 10px;
}
/* line 72, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .advatages_block .advantages_description ul {
  list-style: none;
}
/* line 74, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .advatages_block .advantages_description ul li {
  position: relative;
  margin-bottom: 15px;
  display: block;
  padding-left: 40px;
}
/* line 79, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .advatages_block .advantages_description ul li:before {
  content: '\f00c';
  font-family: "Font Awesome 5 Pro";
  font-weight: 700;
  display: inline-block;
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #2074ca;
  color: #2074ca;
  font-size: 13px;
  padding: 1px 2px;
  box-sizing: border-box;
}

/* line 103, ../../sass/mobile/v2/_club_user.scss */
.form_suscribe_wrapper .extra_info span {
  font-size: 12px;
  color: #424242;
  line-height: 20px;
  letter-spacing: 0.2px;
}
/* line 110, ../../sass/mobile/v2/_club_user.scss */
.form_suscribe_wrapper .checkbox_wrapper .referal_label {
  font-weight: bold;
  font-size: 12px;
  color: #424242;
  display: block;
  margin-bottom: 5px;
}
/* line 117, ../../sass/mobile/v2/_club_user.scss */
.form_suscribe_wrapper .checkbox_wrapper input {
  width: auto;
  display: inline-block;
  vertical-align: middle;
  margin: 8px 5px;
}
/* line 124, ../../sass/mobile/v2/_club_user.scss */
.form_suscribe_wrapper #contact-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: block;
  width: 100%;
}
/* line 131, ../../sass/mobile/v2/_club_user.scss */
.form_suscribe_wrapper .privacy_wrapper {
  font-weight: bold;
  font-size: 12px;
  color: #424242;
  display: inline-block;
}

/* line 139, ../../sass/mobile/v2/_club_user.scss */
.user_register_conditions {
  padding: 20px;
  font-size: 12px;
  color: #424242;
  line-height: 20px;
  letter-spacing: 0.2px;
}

/* line 1, ../../sass/mobile/v2/_reservationSummary.scss */
.grid_12.alpha.my-bookings-booking-info {
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box;
}

/* line 7, ../../sass/mobile/v2/_reservationSummary.scss */
fieldset.fResumenReserva {
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box;
}

/* line 47, ../../sass/mobile/styles_mobile_test2.scss */
.general_title_block .main_title {
  color: #012379;
}

/* line 52, ../../sass/mobile/styles_mobile_test2.scss */
.slider_inner_section {
  width: 100%;
}
/* line 55, ../../sass/mobile/styles_mobile_test2.scss */
.slider_inner_section img {
  width: 100%;
}

/* line 60, ../../sass/mobile/styles_mobile_test2.scss */
body {
  font-family: "Open Sans", sans-serif;
  padding-bottom: 52px;
}
