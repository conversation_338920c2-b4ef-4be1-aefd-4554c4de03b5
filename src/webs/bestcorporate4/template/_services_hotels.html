</div>
<div class="hotels_wrapper services_hotels_wrapper">
    {% for destiny in services_hotels %}
        <div class="hotels_at_destiny" destiny="{{ destiny.destiny_class_name }}" style="background: {{ destiny.color|safe }}">

            <div class="container12" style="padding: 0 140px;box-sizing: border-box">

            <div class="background_destiny_element" style="background: {{ destiny.color|safe }}"></div>

            <h3 class="main_hotels_title" style="color: {{ destiny.color|safe }}">{{ T_hotels_at }} {{ destiny.title|safe }} (<span class="hotels_number">{{ destiny.hotels_sections|length }}</span>)</h3>

            {% for hotel_element in destiny.hotels_sections %}
                {% if not hotel_element.disabled %}
                <div class="hotel_element" category="{{ hotel_element.category }}" services="{% for hotel_service in hotel_element.services %}{{ hotel_service }}{% if not forloop.last %};{% endif %}{% endfor %}">

                    <a href="{{ hotel_element.friendlyUrlInternational|safe }}">
                        <div class="exceded">
                            <img class="hotel_background_image" src="{{ hotel_element.pictures.0.servingUrl }}=s1140">
                        </div>
                    </a>

                    <div class="full_info_wrapper">

                        <div class="hotel_info">

                            <div class="background_overlay" style="background: {{ destiny.color|safe }}"></div>
                            <h2 class="hotel_name">{{ hotel_element.title|safe }}
                                {% if hotel_element.category %}
                                    {% if hotel_element.keys_category %}
                                        {% for star in hotel_element.category|create_range %}<i class="fa fa-key" aria-hidden="true"></i>{% endfor %}
                                    {% else %}
                                        {% for star in hotel_element.category|create_range %}*{% endfor %}
                                    {% endif %}
                                {% endif %}
                            </h2>

                            <h3 class="hotel_location">{{ hotel_element.site|safe }}</h3>

                            <div class="general_location_info">
                                {{ hotel_element.address|safe }}
                            </div>

                            <div class="since_price">
                                <small>{{ T_desde }}</small>
                                {{ hotel_element.since|safe }}€*
                            </div>
                        </div>


                        <div class="general_contact_info_wrapper">
                            <div class="phones">
                                {% for phone_element in hotel_element.phones %}
                                    T: {{ phone_element }}<br>
                                {% endfor %}
                                {% for phone_element in hotel_element.fax %}
                                    F: {{ phone_element }}
                                    {% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </div>

                            {% if hotel_element.email %}
                            <div class="send_email">
                                <a href="mailto:{{ hotel_element.email|safe }}">{{ T_enviar_email }}</a>
                            </div>
                            {% endif %}

                            <div class="see_pictures">
                                {% load replace_string %}
                                <a href="javascript:showGallery2([ {% for picture in hotel_element.pictures %} {href : '{{ picture.servingUrl|safe }}=s1900', title : '{{ hotel_element.title|addslashes }}'}, {% endfor %} ]);">
                                    {{ T_ver_fotos }}
                                </a>
                            </div>

                            <div class="see_map" href="#see_map_{{ forloop.parentloop.counter }}_{{ forloop.counter }}">
                                {{ T_ver_en_mapa }}
                            </div>

                            <div class="map_popup" id="see_map_{{ forloop.parentloop.counter }}_{{ forloop.counter }}" style="display: none;">
                                {{ hotel_element.map|safe }}
                            </div>

                            {% if not hotel_element.website %}
                                <a href="{{ hotel_element.friendlyUrlInternational|safe }}" class="know_more">
                            {% else %}
                                <a href="{{ hotel_element.website|safe }}" class="know_more">
                            {% endif %}
                                <span>
                                    {{ T_ver_mas_info }}
                                </span>
                            </a>

                            <a href="#data" class="button_promotion">{{ T_reservar }}</a>
                        </div>

                        {% if hotel_element.small_txt %}
                            <div class="small_text">{{ hotel_element.small_txt|safe }}</div>
                        {% endif %}
                    </div>

                </div>
                {% endif %}
            {% endfor %}

            </div>
        </div>
    {% endfor %}
</div>
<div id="wrapper_content" class="container12">

<script async="async">
    $(".hotels_at_destiny").each(function(){
        if (!$(this).find(".hotel_element").length){
            $(this).remove();
        }
    })
</script>