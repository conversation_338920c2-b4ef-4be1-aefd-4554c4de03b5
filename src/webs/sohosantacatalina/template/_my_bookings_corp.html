{# Copied from source/templates/secciones/my_booking/my_booking_question_corp.html #}

{% if real_modify_reservation %}
<link rel="stylesheet" type="text/css" href="/static_1/css/templates/secciones/styles_booking_widget_modify.css">
 {% endif %}

{% if use_h1 %}
    {% if not subtitle %}
        <h1 class="section-title">{{ title|safe }}</h1>
    {% endif %}
{% else %}
    {% if not subtitle %}
        <h3 class="section-title">{{ title|safe }}</h3>
    {% endif %}
{% endif %}

{% if custom_message_my_booking %}
    {{ custom_message_my_booking|safe }}
{% endif %}

{% if not disable_content %}
    {{content|safe}}
{% endif %}

<form name="contact" method="post" id="my-bookings-form" class="question_cancell">

    <input type="hidden" name="action" id="action" value="reservation"/>

    <div id="my-bookings-form-fields">

        {% if namespaces_list %}
            <input type="hidden" id="namespaces_list" value="{{ namespaces_list }}">
        {% else %}
            <label id="my-bookings-hotel-label" for="hotel">{{ T_seleccionar_hotel }}</label>
            <select id="hotelSelectCorporate" name="hotel" class="bordeSelect">
                {% if selectOptions %}
                    {% for hotel in selectOptions %}
                        <option value="{{ hotel.domain }}"
                                {% if hotel.selected %}selected="selected" {% endif %}
                                {% if hotel.namespaces_list %}namespaces_list="{{ hotel.namespaces_list }}"{% endif %}
                        >
                            {{ hotel.value|safe }}
                        </option>
                    {% endfor %}
                {% endif %}
                {% if groupedSelectOptions %}
                    {% for hotel_group in groupedSelectOptions %}
                        <optgroup label="{{ hotel_group.group_id|safe }}">
                            {% for hotel in hotel_group.group_list %}
                                <option value="{{ hotel.domain|safe }}"
                                        namespace="{{ hotel.namespace }}"
                                        {% if hotel.selected %}selected{% endif %}>
                                    {{ hotel.value|safe }}
                                </option>
                            {% endfor %}
                        </optgroup>
                    {% endfor %}
                {% endif %}
            </select>
        {% endif %}



        <label for="email" id="my-bookings-email-label">E-mail</label>
        <input type="text" id="emailInput" name="email" class="bordeInput" value=""/>
        <label for="localizador" id="my-bookings-localizador-label">{{T_localizador|safe}}</label>
        <input type="text" id="localizadorInput" name="localizador" class="bordeInput" value=""/>
        {% if custom_input_html %}
            {{ custom_input_html|safe }}
        {% endif %}

        {% if modify_reservation %}
        <ul>

             {% if custom_button %}
             <li><a href="{{ custom_url }}" class="custom-button" target="_blank">{{ custom_button_text }}</a></li>
             {% endif %}

             {% if real_modify_reservation %}
                <li><button class="modify-reservation" onClick="javascript:searchForReservation('onlineModification');return false;">{% if custom_modify_text %}{{ custom_modify_text }}{% else %}{{T_modificar_reserva|safe}}{% endif %}</button></li>
             {% else %}
                <li><button class="modify-reservation" onClick="javascript:searchForReservation('modify');return false;">{{T_modificar_reserva|safe}}</button></li>
             {% endif %}


            <li><button class="searchForReservation" onClick="javascript:searchForReservation();return false;">{% if custom_search_text %}{{ custom_search_text }}{% else %}{{T_buscar|safe}}{% endif %}</button></li>
        </ul>
        {% else %}
            <button type="submit" id="my-bookings-form-search-button" onClick="javascript:searchForReservation();return false;">{{T_buscar|safe}}</button>
        {% endif %}

         {% if upgrading_button %}
            <ul> <li style="width: 300px;"><button class="modify-reservation upgrading-button" style="background:#a0bb31" onClick="javascript:upgradingReservation(); return false;">{{T_mejora_experiencia|safe}}</button></li></ul>
        {% endif %}

    </div>

    <div id="reservation" style="margin-top: 40px; clear: both"></div>

    <div id="cancel-button-container">
	    <button type="submit" id="cancelButton">{{T_cancelar_reserva|safe}}</button>
    </div>
    {% if modify_reservation and not real_modify_reservation%}
         <div id="modify-button-container">
                <a href="#modify-popup" id="modifyButton" class="myFancyPopupModify">{{T_modificar_reserva|safe}}</a>
            </div>
        {% endif %}
</form>

<div class="cancel_booking_questions" style="display: none;">

    <span class="cancellation_reason_title">{{ T_razones_cancelacion_2 }}</span>

    <div class="questions_labels">
        <div class="radio_wrapper_cancellation"><input type="radio" name="cancellation_reason" value="{{ M_no_viajare }}"><label class="label_cancellation">{{ T_no_viajare }}</label></div>
        <div class="radio_wrapper_cancellation"><input type="radio" name="cancellation_reason" value="{{ M_reserva_hotel_cambio }}" placeholder_value="{{ T_hotel_porque }}"><label class="label_cancellation">{{ T_reserva_hotel_cambio }}</label></div>
        <div class="radio_wrapper_cancellation"><input type="radio" name="cancellation_reason" value="{{ M_precio_barato }}" placeholder_value="{{ T_donde }}?"><label class="label_cancellation">{{ T_precio_barato }}</label></div>
        <div class="radio_wrapper_cancellation"><input type="radio" name="cancellation_reason" value="{{ M_otra_reserva }}"><label class="label_cancellation">{{ T_otra_reserva }}</label></div>
        <div class="radio_wrapper_cancellation"><input type="radio" name="cancellation_reason" value="{{ M_other }}" placeholder_value="{{ T_comentarios }}"><label class="label_cancellation">{{ T_other }}</label></div>
    </div>

    <textarea id="cancellation-reasons" rows="4" cols="75" style="display: none;"></textarea>

    <div class="cancel_booking_reasons_wrapper">
        <button type="submit" id="cancelButton_reasons" disabled="disabled">{{T_cancelar_reserva|safe}}</button>
    </div>

</div>

{% if modify_reservation_content %}
<div id="modify-popup" style="display: none">
    <div class="content">
        <div class="image-box">
            {% if modify_reservation_content.pictures %}
                <img src="{{ modify_reservation_content.pictures.0|safe }}=s800" alt=""/>
            {% endif %}
        </div>
        <div class="description">
            <h3>{{ modify_reservation_content.subtitle|safe }}</h3>
            {{ modify_reservation_content.content|safe }}
        </div>
    </div>
</div>
{% endif %}

<script>
    if (!window.jQuery) {
        var script = document.createElement('script');
        script.type = "text/javascript";
        script.src = "/static_1/lib/jquery-1.7.min.js";
        document.getElementsByTagName('head')[0].appendChild(script);

        var script = document.createElement('script');
        script.type = "text/javascript";
        script.src = "/static_1/lib/spin.min.js";
        document.getElementsByTagName('head')[0].appendChild(script);
    }

    function searchForReservation(buton_type) {

        let email = $('#emailInput').val(),
            localizador = $('#localizadorInput').val(),
            hotel_selector = $("#hotelSelectCorporate"),
            hotel = null,
            namespaces_list = null;

        if (hotel_selector.length) {
            let selected_option = hotel_selector.find('option:selected');

            hotel = hotel_selector.val();
            namespaces_list = selected_option.attr('namespaces_list') || selected_option.attr('namespace');
        } else if ($("input#namespaces_list").length) {
            namespaces_list = $("input#namespaces_list").val();
        }


        if (!email && !localizador) {
            let emailArray = $("input[name='email']");
            let localizadorArray = $("input[name='localizador']");

            email = '';
            localizador = '';

            for (let i = 0; i < emailArray.length; i++) {
                let aux = $(emailArray[i]).val();
                if (aux !== '') {
                    email = aux;
                    break;
                }
            }

            for (let i = 0; i < localizadorArray.length; i++) {
                let aux = $(localizadorArray[i]).val();
                if (aux !== '') {
                    localizador = aux;
                    break;
                }
            }

        }

        let ajax_url_utils = '';
        if (buton_type && buton_type == "onlineModification") {
            ajax_url_utils = '/utils?action=modifyReservation';
        } else {
            ajax_url_utils = '/utils?action=searchReservation';
        }

        if (hotel) {
            ajax_url_utils = hotel + ajax_url_utils;
        }

        $.ajax({
            url: ajax_url_utils,
            data: {'email': email, 'localizador': localizador, 'namespaces_list': namespaces_list, include_analytics_data: true},
            success: function (data) {

                if (data != '') {

                    if (buton_type && buton_type == "modify") {
                        $('#reservation').html(data);
                        $('html, body').animate({
                            scrollTop: $("#reservation").offset().top - 100
                        }, 1000);
                        $('#modifyButton').css('display', 'block');
                        $('#cancelButton').css('display', 'none');
                    } else {
                        $('#reservation').html(data);
                        $('html, body').animate({
                            scrollTop: $("#reservation").offset().top - 100
                        }, 1000);
                        $('#cancelButton').css('display', 'block');
                        $('#modifyButton').css('display', 'none');
                    }
                } else {
                    alert("{{T_reserva_no_encontrada|safe}}");
                }

            }
        });
    }


    function upgradingReservation() {


        ajax_url_utils = '/utils?action=getUpgradingLink';

        var email = $('#emailInput').removeClass('error').val();
        var localizador = $('#localizadorInput').removeClass('error').val();

        var data = {
            email: email,
            localizador: localizador
        };

        if ($("input#namespaces_list").length) {
            data['namespaces_list'] = $("input#namespaces_list").val();
        }

        $.ajax({
            url: ajax_url_utils,
            data: data,
            success: function (data) {

                if (data != '') {
                    console.log("redirecting to:" + data)
                    window.location.href = data;

                } else {
                    alert("{{T_reserva_no_encontrada|safe}}");
                }

            }
        });
    }


    $(function () {

        function cancelReservation() {

            var comments_cancel = $("input[name='cancellation_reason']:checked").val();

            $("textarea#cancellation-reasons").each(function () {
                if ($(this).val() != '') {
                    comments_cancel = comments_cancel + ": " + $(this).val();
                }
            });

            var namespace_selection = $("select#hotelSelect option:selected").attr('namespace');

            if (!namespace_selection) {
                namespace_selection = $(".multiple_hotels_my_booking_namespace").val();
            }

            var hotel_url = "";
            if ($("#hotelSelectCorporate").length && $("#hotelSelectCorporate").val()) {
                hotel_url = $("#hotelSelectCorporate").val();
            }

            $.post(
                hotel_url + "/utils?action=cancelReservation",
                {
                    reservationKey: $('#reservationId').val(),
                    comments: comments_cancel,
                    namespace: namespace_selection
                },
                function (data) {
                    $.fancybox.close();
                    $("#cancellation-confirmation-button").css("display", "block");
                    if(data === $.i18n._("T_reserva_cancelada") && typeof sendRefundEvent === 'function'){
                        sendRefundEvent();
                    }
                    alert(data);
                    setTimeout(function () {
                        window.location.href = window.location.href;
                    });
                    $("#cancelButton_reasons").show();
                    $("#cancelButton_reasons").next(".spinner_loading_my_booking").remove();

                }
            );
        }

        $("#cancelButton").click(function () {
            prepare_questions_cancellation();
            return false;
        });

        $("#cancellation-cancellation-button").click(function () {
            $.fancybox.close();
        });


        var cancel_reason_mandatory = true;
        {% if no_cancel_reason_mandatory %}
            cancel_reason_mandatory = false;
        {% endif %}

        $("#cancellation-confirmation-button").click(function () {
            var reasons_cancell = false;
            $("textarea#cancellation-reasons").each(function () {
                if ($(this).val() != '') {
                    reasons_cancell = true;
                }
            });

            if (cancel_reason_mandatory && !reasons_cancell) {
                alert($.i18n._("campo_obligatorio"));
                return false;
            } else {
                cancelReservation();
            }
        });

        $("input[name='cancellation_reason']").change(function () {
            $("#cancelButton_reasons").removeAttr('disabled');
            var selected_placeholder = $(this).attr('placeholder_value');


            $(".cancel_booking_questions #cancellation-reasons").hide('500', function () {
                if (selected_placeholder) {
                    $(".cancel_booking_questions #cancellation-reasons").attr('placeholder', selected_placeholder);
                    $(".cancel_booking_questions #cancellation-reasons").show('500');
                }
            });

        });

        var opts = {
            lines: 13, // The number of lines to draw
            length: 4, // The length of each line
            width: 3, // The line thickness
            radius: 10, // The radius of the inner circle
            rotate: 0, // The rotation offset
            color: '#000000', // #rgb or #rrggbb
            speed: 1.5, // Rounds per second
            trail: 60, // Afterglow percentage
            shadow: false, // Whether to render a shadow
            hwaccel: false, // Whether to use hardware acceleration
            className: 'spinner', // The CSS class to assign to the spinner
            zIndex: 2e9, // The z-index (defaults to 2000000000)
            top: 'auto', // Top position relative to parent in px
            left: 'auto' // Left position relative to parent in px
        };


        $("#cancelButton_reasons").click(function () {
            if (!$(this).attr('disabled')) {
                $(this).hide();
                var spinner_html = $("<div class='spinner_loading_my_booking'></div>");
                spinner_html.insertAfter($(this));

                try {
                    var spinner = new Spinner(opts).spin($(".spinner_loading_my_booking").get(0));
                } catch (err) {
                    console.log("Spinner couldn´t be initialized")
                }

                cancelReservation();
            }
        })
    });


    function prepare_questions_cancellation() {

        $("#my-bookings-form-fields").slideUp();
        $("#reservation").slideUp();
        $("#cancel-button-container").slideUp();
        $(".cancel_booking_questions").slideDown(function () {
            $('html, body').animate({
                scrollTop: $(".cancel_booking_questions").offset().top - 100
            }, 1000);
        });


    }

    $(document).ready(function() {
        var classToCheck = document.documentElement;

        var observer = new MutationObserver(mutation => {
          mutation.forEach(mut => {
            if (mut.type === 'attributes' && mut.attributeName === 'class') {
              if (classToCheck.classList.contains('fancybox-lock')) {
                classToCheck.classList.remove('fancybox-lock');
              }
              if (classToCheck.classList.contains('fancybox-margin')) {
                classToCheck.classList.remove('fancybox-margin');
              }
            }
          });
        });

        var config = { attributes: true, attributeFilter: ['class'] };

        observer.observe(classToCheck, config);
    });
</script>


<style>
    .fancybox-overlay {
        display: none !important;
    }
    .cancel_booking_questions {
        background: white;
        padding: 40px 55px;
        border: 1px solid lightgray;
        box-sizing: border-box;
        display: table;
        width: 100%;
    }

    .spinner_loading_my_booking {
        margin-top: 30px;
    }

    .cancellation_reason_title {
        font-size: 27px;
        margin-bottom: 15px;
    }

    label.label_cancellation {
        font-size: 16px;
    }

    .radio_wrapper_cancellation {
        margin-bottom: 7px;
    }

    .radio_wrapper_cancellation input {
        margin: 0 17px 0 0;
        vertical-align: top;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        width: 25px;
        height: 25px;
        border-radius: 99px;
        border: 1px solid lightgrey;
    }

    .radio_wrapper_cancellation input:checked {
        background: #00ce00;
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAMAAAD04JH5AAAAA3NCSVQICAjb4U/gAAAACXBIWXMAAALlAAAC5QEb/l57AAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAAMlQTFRF////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALJMpoAAAAEJ0Uk5TAAoLDA0ODxAREhMUFRYXGBkaGxwdHh8gISIjnb6/wMHCw8TFxsfIycrLzM3Oz9DR0tPU1dbX2Nna29zd3t/g4eLji/uFbQAAAwdJREFUeNrFm9d26jAQRQ+9V0MIBKf3CulOj/7/o+5LyGVkQ1wkHb1hSd57gW0szQwAAPC2cnDaclve8kcvUFOnBrmpCjzBV04NclOllgy8QCmllO/MIOcrtWTww3dn8MNfGPzylfLzLvh5/xcYeIKv1LYDg/z2EjDw4Cvl1EDwlfIx+RYHdiwb5HcE7nsCtwYRfKcGkXxg8iUO7xZs8Qu7AvQ1WXSM3Rjo/PH/Ls1gz4pBYW8lHxh/WjfQ+J9j2b1p20Dnb+oDNIN9wwaF/T/4YYOiSX7xbz4w+hCDDgwaFA/EqT9G0cOsGcTkWzOIzQdG72LooRGD4qE46fto3eANzaCUnV/S+Bvrhxs3SMgPGRxlNCgdJeQDwzeDBhr/bRhnkmZwnMGgdJyCHzYop+WX0/GB4asRA43/Oow/dSANTlIZlE8kf5BksgGDTHwDBhn5wOBFnOC0kmx65VRMfxkk/wq9LAY630tzEWkGZwkMKmcG+Nq6NYmBxg9S8lMbGOMDfWlwXo0zqXou+X1kaP3nxAYa/zkTP4WBYT7QkwYXfxhULyS/Bzg1sMAHek/SoLZ6aE3yn4zwQwaXKw1ql1b4QDeegc7vwljrPsYw0PiPBvkhg6sIg9qVRX7YoK4PqNvlA50HAbjWDOrXovuhAzg1cMBfa+CED3TuBeamseho3IiOe0t8oB1toPPbgFMDh3ygfSdgswbQmIlDd1b5EQaO+UBLM9D4LVhvrVu1st064K8zcMNfbeCKD7TmUfy5Mz7QjDCYNwGmgVs+0JS3n5o55tMF2D8B+yJk34bsBxH7Ucz+M2L/HbNfSNivZOyXUvZrOXthwl6asRen7OU5e4OCvUXD3qRib9OxNyrZW7XszWr2dj07YMEO2bCDVuywHTtwyQ7dsoPX7PA9O4GBncLBTmJhp/GwE5nYqVzsZDZ2Oh87oZGd0slOamWn9bITm9mp3ezkdnZ6P7vAgV3iwS5yYZf5sAud2KVe7GI3erkfveCRXvJJL3rll/3yC5/5pd/84ndm+f8/Ei19Fnqb9F0AAAAASUVORK5CYII=) no-repeat center;
        background-size: cover;
        border: 2px solid lightgray;
    }

    .radio_wrapper_cancellation .label_cancellation {
        margin-top: 4px;
        display: inline-block;
    }

    .cancel_booking_reasons_wrapper #cancelButton_reasons {
        width: 260px;
        color: white;
        padding: 10px 0;
        border: none;
        font-size: 18px;
        cursor: pointer;
        background: #5f5f5f;
        float: right;
    }

    .cancel_booking_reasons_wrapper #cancelButton_reasons[disabled] {
        opacity: 0.2;
    }

    .cancel_booking_reasons_wrapper {
        display: table;
        width: 50%;
        margin-top: 20px;
        float: right;
    }

    .cancel_booking_questions #cancellation-reasons {
        resize: none;
        margin-top: 20px;
        display: block;
        border: 1px solid lightgrey;
        float: left;
        width: 500px;
    }


</style>