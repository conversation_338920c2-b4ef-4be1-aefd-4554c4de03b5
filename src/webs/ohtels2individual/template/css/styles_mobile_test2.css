@charset "UTF-8";
@import url(//fonts.googleapis.com/css?family=Montserrat:300,400,600|Source+Sans+Pro:400,300,700,600&display=swap);
@font-face {
  font-family: 'icomoon';
  src: url("/static_1/fonts/iconmoon/icomoon.eot?ytm7g4");
  src: url("/static_1/fonts/iconmoon/icomoon.eot?ytm7g4#iefix") format("embedded-opentype"), url("/static_1/fonts/iconmoon/icomoon.ttf?ytm7g4") format("truetype"), url("/static_1/fonts/iconmoon/icomoon.woff?ytm7g4") format("woff"), url("/static_1/fonts/iconmoon/icomoon.svg?ytm7g4#icomoon") format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
/* line 13, ../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* line 28, ../../../../sass/plugins/_iconmoon.scss */
.icon-terrace:before {
  content: "\ea52";
}

/* line 31, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-hotelmanager:before {
  content: "\ea4c";
}

/* line 34, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-paritymaker:before {
  content: "\ea4d";
}

/* line 37, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-priceseeker:before {
  content: "\ea4e";
}

/* line 40, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-ratecheck:before {
  content: "\ea4f";
}

/* line 43, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-rescueseeker:before {
  content: "\ea50";
}

/* line 46, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-reviewseeker:before {
  content: "\ea51";
}

/* line 49, ../../../../sass/plugins/_iconmoon.scss */
.icon-couponlong:before {
  content: "\ea4a";
}

/* line 52, ../../../../sass/plugins/_iconmoon.scss */
.icon-coupon:before {
  content: "\ea4b";
}

/* line 55, ../../../../sass/plugins/_iconmoon.scss */
.icon-cocktel:before {
  content: "\ea46";
}

/* line 58, ../../../../sass/plugins/_iconmoon.scss */
.icon-gafas:before {
  content: "\ea47";
}

/* line 61, ../../../../sass/plugins/_iconmoon.scss */
.icon-pelota:before {
  content: "\ea48";
}

/* line 64, ../../../../sass/plugins/_iconmoon.scss */
.icon-piscina:before {
  content: "\ea49";
}

/* line 67, ../../../../sass/plugins/_iconmoon.scss */
.icon-email2:before {
  content: "\ea43";
}

/* line 70, ../../../../sass/plugins/_iconmoon.scss */
.icon-luggage2:before {
  content: "\ea44";
}

/* line 73, ../../../../sass/plugins/_iconmoon.scss */
.icon-nodisturb:before {
  content: "\ea45";
}

/* line 76, ../../../../sass/plugins/_iconmoon.scss */
.icon-percent:before {
  content: "\61";
}

/* line 79, ../../../../sass/plugins/_iconmoon.scss */
.icon-bed:before {
  content: "\62";
}

/* line 82, ../../../../sass/plugins/_iconmoon.scss */
.icon-dots:before {
  content: "\63";
}

/* line 85, ../../../../sass/plugins/_iconmoon.scss */
.icon-buffet:before {
  content: "\e900";
}

/* line 88, ../../../../sass/plugins/_iconmoon.scss */
.icon-zen:before {
  content: "\e901";
}

/* line 91, ../../../../sass/plugins/_iconmoon.scss */
.icon-drink:before {
  content: "\e902";
}

/* line 94, ../../../../sass/plugins/_iconmoon.scss */
.icon-bike:before {
  content: "\e903";
}

/* line 97, ../../../../sass/plugins/_iconmoon.scss */
.icon-celiac:before {
  content: "\e904";
}

/* line 100, ../../../../sass/plugins/_iconmoon.scss */
.icon-chart:before {
  content: "\e905";
}

/* line 103, ../../../../sass/plugins/_iconmoon.scss */
.icon-chips:before {
  content: "\e906";
}

/* line 106, ../../../../sass/plugins/_iconmoon.scss */
.icon-clock:before {
  content: "\e907";
}

/* line 109, ../../../../sass/plugins/_iconmoon.scss */
.icon-download:before {
  content: "\e908";
}

/* line 112, ../../../../sass/plugins/_iconmoon.scss */
.icon-friends:before {
  content: "\e909";
}

/* line 115, ../../../../sass/plugins/_iconmoon.scss */
.icon-group:before {
  content: "\e90a";
}

/* line 118, ../../../../sass/plugins/_iconmoon.scss */
.icon-headset:before {
  content: "\e90b";
}

/* line 121, ../../../../sass/plugins/_iconmoon.scss */
.icon-hipster:before {
  content: "\e90c";
}

/* line 124, ../../../../sass/plugins/_iconmoon.scss */
.icon-lamp:before {
  content: "\e90d";
}

/* line 127, ../../../../sass/plugins/_iconmoon.scss */
.icon-like:before {
  content: "\e90e";
}

/* line 130, ../../../../sass/plugins/_iconmoon.scss */
.icon-map:before {
  content: "\e90f";
}

/* line 133, ../../../../sass/plugins/_iconmoon.scss */
.icon-men:before {
  content: "\e910";
}

/* line 136, ../../../../sass/plugins/_iconmoon.scss */
.icon-monument:before {
  content: "\e911";
}

/* line 139, ../../../../sass/plugins/_iconmoon.scss */
.icon-new:before {
  content: "\e912";
}

/* line 142, ../../../../sass/plugins/_iconmoon.scss */
.icon-pig:before {
  content: "\e913";
}

/* line 145, ../../../../sass/plugins/_iconmoon.scss */
.icon-pdf:before {
  content: "\e914";
}

/* line 148, ../../../../sass/plugins/_iconmoon.scss */
.icon-play:before {
  content: "\e915";
}

/* line 151, ../../../../sass/plugins/_iconmoon.scss */
.icon-row:before {
  content: "\e916";
}

/* line 154, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE917:before {
  content: "\e917";
}

/* line 157, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE918:before {
  content: "\e918";
}

/* line 160, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE919:before {
  content: "\e919";
}

/* line 163, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE91A:before {
  content: "\e91a";
}

/* line 166, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE91B:before {
  content: "\e91b";
}

/* line 169, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE91C:before {
  content: "\e91c";
}

/* line 172, ../../../../sass/plugins/_iconmoon.scss */
.icon-sea:before {
  content: "\e91d";
}

/* line 175, ../../../../sass/plugins/_iconmoon.scss */
.icon-slide:before {
  content: "\e91e";
}

/* line 178, ../../../../sass/plugins/_iconmoon.scss */
.icon-smile:before {
  content: "\e91f";
}

/* line 181, ../../../../sass/plugins/_iconmoon.scss */
.icon-tick:before {
  content: "\e920";
}

/* line 184, ../../../../sass/plugins/_iconmoon.scss */
.icon-ticket:before {
  content: "\e921";
}

/* line 187, ../../../../sass/plugins/_iconmoon.scss */
.icon-trees:before {
  content: "\e922";
}

/* line 190, ../../../../sass/plugins/_iconmoon.scss */
.icon-upgrade:before {
  content: "\e923";
}

/* line 193, ../../../../sass/plugins/_iconmoon.scss */
.icon-watergame:before {
  content: "\e924";
}

/* line 196, ../../../../sass/plugins/_iconmoon.scss */
.icon-wedding:before {
  content: "\e925";
}

/* line 199, ../../../../sass/plugins/_iconmoon.scss */
.icon-basketball:before {
  content: "\e926";
}

/* line 202, ../../../../sass/plugins/_iconmoon.scss */
.icon-books:before {
  content: "\e927";
}

/* line 205, ../../../../sass/plugins/_iconmoon.scss */
.icon-calendar:before {
  content: "\e928";
}

/* line 208, ../../../../sass/plugins/_iconmoon.scss */
.icon-candles:before {
  content: "\e929";
}

/* line 211, ../../../../sass/plugins/_iconmoon.scss */
.icon-coins:before {
  content: "\e92a";
}

/* line 214, ../../../../sass/plugins/_iconmoon.scss */
.icon-cup:before {
  content: "\e92b";
}

/* line 217, ../../../../sass/plugins/_iconmoon.scss */
.icon-cutlery:before {
  content: "\e92c";
}

/* line 220, ../../../../sass/plugins/_iconmoon.scss */
.icon-dice:before {
  content: "\e92d";
}

/* line 223, ../../../../sass/plugins/_iconmoon.scss */
.icon-doc:before {
  content: "\e92e";
}

/* line 226, ../../../../sass/plugins/_iconmoon.scss */
.icon-email:before {
  content: "\e92f";
}

/* line 229, ../../../../sass/plugins/_iconmoon.scss */
.icon-euro:before {
  content: "\e930";
}

/* line 232, ../../../../sass/plugins/_iconmoon.scss */
.icon-info:before {
  content: "\e931";
}

/* line 235, ../../../../sass/plugins/_iconmoon.scss */
.icon-light:before {
  content: "\e932";
}

/* line 238, ../../../../sass/plugins/_iconmoon.scss */
.icon-night:before {
  content: "\e933";
}

/* line 241, ../../../../sass/plugins/_iconmoon.scss */
.icon-pet:before {
  content: "\e934";
}

/* line 244, ../../../../sass/plugins/_iconmoon.scss */
.icon-shell:before {
  content: "\e935";
}

/* line 247, ../../../../sass/plugins/_iconmoon.scss */
.icon-sofa:before {
  content: "\e936";
}

/* line 250, ../../../../sass/plugins/_iconmoon.scss */
.icon-star:before {
  content: "\e937";
}

/* line 253, ../../../../sass/plugins/_iconmoon.scss */
.icon-user:before {
  content: "\e938";
}

/* line 256, ../../../../sass/plugins/_iconmoon.scss */
.icon-wii:before {
  content: "\e939";
}

/* line 259, ../../../../sass/plugins/_iconmoon.scss */
.icon-ball:before {
  content: "\e93a";
}

/* line 262, ../../../../sass/plugins/_iconmoon.scss */
.icon-booking:before {
  content: "\e93b";
}

/* line 265, ../../../../sass/plugins/_iconmoon.scss */
.icon-cleanset:before {
  content: "\e93c";
}

/* line 268, ../../../../sass/plugins/_iconmoon.scss */
.icon-comment:before {
  content: "\e93d";
}

/* line 271, ../../../../sass/plugins/_iconmoon.scss */
.icon-ethernet:before {
  content: "\e93e";
}

/* line 274, ../../../../sass/plugins/_iconmoon.scss */
.icon-eye:before {
  content: "\e93f";
}

/* line 277, ../../../../sass/plugins/_iconmoon.scss */
.icon-feet:before {
  content: "\e940";
}

/* line 280, ../../../../sass/plugins/_iconmoon.scss */
.icon-fridge:before {
  content: "\e941";
}

/* line 283, ../../../../sass/plugins/_iconmoon.scss */
.icon-hairdrier:before {
  content: "\e942";
}

/* line 286, ../../../../sass/plugins/_iconmoon.scss */
.icon-handicap:before {
  content: "\e943";
}

/* line 289, ../../../../sass/plugins/_iconmoon.scss */
.icon-iron:before {
  content: "\e944";
}

/* line 292, ../../../../sass/plugins/_iconmoon.scss */
.icon-key:before {
  content: "\e945";
}

/* line 295, ../../../../sass/plugins/_iconmoon.scss */
.icon-lift:before {
  content: "\e946";
}

/* line 298, ../../../../sass/plugins/_iconmoon.scss */
.icon-mapmarker:before {
  content: "\e947";
}

/* line 301, ../../../../sass/plugins/_iconmoon.scss */
.icon-mask:before {
  content: "\e948";
}

/* line 304, ../../../../sass/plugins/_iconmoon.scss */
.icon-mouse:before {
  content: "\e949";
}

/* line 307, ../../../../sass/plugins/_iconmoon.scss */
.icon-movie:before {
  content: "\e94a";
}

/* line 310, ../../../../sass/plugins/_iconmoon.scss */
.icon-mug:before {
  content: "\e94b";
}

/* line 313, ../../../../sass/plugins/_iconmoon.scss */
.icon-plug:before {
  content: "\e94c";
}

/* line 316, ../../../../sass/plugins/_iconmoon.scss */
.icon-plus:before {
  content: "\e94d";
}

/* line 319, ../../../../sass/plugins/_iconmoon.scss */
.icon-printer:before {
  content: "\e94e";
}

/* line 322, ../../../../sass/plugins/_iconmoon.scss */
.icon-sack:before {
  content: "\e94f";
}

/* line 325, ../../../../sass/plugins/_iconmoon.scss */
.icon-shower:before {
  content: "\e950";
}

/* line 328, ../../../../sass/plugins/_iconmoon.scss */
.icon-solarium:before {
  content: "\e951";
}

/* line 331, ../../../../sass/plugins/_iconmoon.scss */
.icon-tenis:before {
  content: "\e952";
}

/* line 334, ../../../../sass/plugins/_iconmoon.scss */
.icon-tv:before {
  content: "\e953";
}

/* line 337, ../../../../sass/plugins/_iconmoon.scss */
.icon-window:before {
  content: "\e954";
}

/* line 340, ../../../../sass/plugins/_iconmoon.scss */
.icon-apple:before {
  content: "\e955";
}

/* line 343, ../../../../sass/plugins/_iconmoon.scss */
.icon-bathrobe:before {
  content: "\e956";
}

/* line 346, ../../../../sass/plugins/_iconmoon.scss */
.icon-bell:before {
  content: "\e957";
}

/* line 349, ../../../../sass/plugins/_iconmoon.scss */
.icon-building:before {
  content: "\e958";
}

/* line 352, ../../../../sass/plugins/_iconmoon.scss */
.icon-car:before {
  content: "\e959";
}

/* line 355, ../../../../sass/plugins/_iconmoon.scss */
.icon-cigar:before {
  content: "\e95a";
}

/* line 358, ../../../../sass/plugins/_iconmoon.scss */
.icon-comments:before {
  content: "\e95b";
}

/* line 361, ../../../../sass/plugins/_iconmoon.scss */
.icon-coolheart:before {
  content: "\e95c";
}

/* line 364, ../../../../sass/plugins/_iconmoon.scss */
.icon-cupboard:before {
  content: "\e95d";
}

/* line 367, ../../../../sass/plugins/_iconmoon.scss */
.icon-dimensions:before {
  content: "\e95e";
}

/* line 370, ../../../../sass/plugins/_iconmoon.scss */
.icon-family:before {
  content: "\e95f";
}

/* line 373, ../../../../sass/plugins/_iconmoon.scss */
.icon-flattv:before {
  content: "\e960";
}

/* line 376, ../../../../sass/plugins/_iconmoon.scss */
.icon-formaluser:before {
  content: "\e961";
}

/* line 379, ../../../../sass/plugins/_iconmoon.scss */
.icon-guarantee:before {
  content: "\e962";
}

/* line 382, ../../../../sass/plugins/_iconmoon.scss */
.icon-gift:before {
  content: "\e963";
}

/* line 385, ../../../../sass/plugins/_iconmoon.scss */
.icon-lock:before {
  content: "\e964";
}

/* line 388, ../../../../sass/plugins/_iconmoon.scss */
.icon-movie2:before {
  content: "\e965";
}

/* line 391, ../../../../sass/plugins/_iconmoon.scss */
.icon-picasa:before {
  content: "\e966";
}

/* line 394, ../../../../sass/plugins/_iconmoon.scss */
.icon-roulette:before {
  content: "\e967";
}

/* line 397, ../../../../sass/plugins/_iconmoon.scss */
.icon-sauna:before {
  content: "\e968";
}

/* line 400, ../../../../sass/plugins/_iconmoon.scss */
.icon-shower2:before {
  content: "\e969";
}

/* line 403, ../../../../sass/plugins/_iconmoon.scss */
.icon-singlebed:before {
  content: "\e96a";
}

/* line 406, ../../../../sass/plugins/_iconmoon.scss */
.icon-ski:before {
  content: "\e96b";
}

/* line 409, ../../../../sass/plugins/_iconmoon.scss */
.icon-smartphone:before {
  content: "\e96c";
}

/* line 412, ../../../../sass/plugins/_iconmoon.scss */
.icon-student:before {
  content: "\e96d";
}

/* line 415, ../../../../sass/plugins/_iconmoon.scss */
.icon-thermometer:before {
  content: "\e96e";
}

/* line 418, ../../../../sass/plugins/_iconmoon.scss */
.icon-washer:before {
  content: "\e96f";
}

/* line 421, ../../../../sass/plugins/_iconmoon.scss */
.icon-drinks:before {
  content: "\e970";
}

/* line 424, ../../../../sass/plugins/_iconmoon.scss */
.icon-drinks2:before {
  content: "\e971";
}

/* line 427, ../../../../sass/plugins/_iconmoon.scss */
.icon-airconditioner:before {
  content: "\e972";
}

/* line 430, ../../../../sass/plugins/_iconmoon.scss */
.icon-arrowdown:before {
  content: "\e973";
}

/* line 433, ../../../../sass/plugins/_iconmoon.scss */
.icon-arrowleft:before {
  content: "\e974";
}

/* line 436, ../../../../sass/plugins/_iconmoon.scss */
.icon-arrowright:before {
  content: "\e975";
}

/* line 439, ../../../../sass/plugins/_iconmoon.scss */
.icon-arrowup:before {
  content: "\e976";
}

/* line 442, ../../../../sass/plugins/_iconmoon.scss */
.icon-bag:before {
  content: "\e977";
}

/* line 445, ../../../../sass/plugins/_iconmoon.scss */
.icon-bike2:before {
  content: "\e978";
}

/* line 448, ../../../../sass/plugins/_iconmoon.scss */
.icon-biker:before {
  content: "\e979";
}

/* line 451, ../../../../sass/plugins/_iconmoon.scss */
.icon-briefcase:before {
  content: "\e97a";
}

/* line 454, ../../../../sass/plugins/_iconmoon.scss */
.icon-card:before {
  content: "\e97b";
}

/* line 457, ../../../../sass/plugins/_iconmoon.scss */
.icon-cocktail:before {
  content: "\e97c";
}

/* line 460, ../../../../sass/plugins/_iconmoon.scss */
.icon-cooker:before {
  content: "\e97d";
}

/* line 463, ../../../../sass/plugins/_iconmoon.scss */
.icon-drop:before {
  content: "\e97e";
}

/* line 466, ../../../../sass/plugins/_iconmoon.scss */
.icon-gym:before {
  content: "\e97f";
}

/* line 469, ../../../../sass/plugins/_iconmoon.scss */
.icon-info2:before {
  content: "\e980";
}

/* line 472, ../../../../sass/plugins/_iconmoon.scss */
.icon-massage:before {
  content: "\e981";
}

/* line 475, ../../../../sass/plugins/_iconmoon.scss */
.icon-moon:before {
  content: "\e982";
}

/* line 478, ../../../../sass/plugins/_iconmoon.scss */
.icon-music:before {
  content: "\e983";
}

/* line 481, ../../../../sass/plugins/_iconmoon.scss */
.icon-news:before {
  content: "\e984";
}

/* line 484, ../../../../sass/plugins/_iconmoon.scss */
.icon-nosmoke:before {
  content: "\e985";
}

/* line 487, ../../../../sass/plugins/_iconmoon.scss */
.icon-parking:before {
  content: "\e986";
}

/* line 490, ../../../../sass/plugins/_iconmoon.scss */
.icon-phone1:before {
  content: "\e987";
}

/* line 493, ../../../../sass/plugins/_iconmoon.scss */
.icon-phone2:before {
  content: "\e988";
}

/* line 496, ../../../../sass/plugins/_iconmoon.scss */
.icon-pictures:before {
  content: "\e989";
}

/* line 499, ../../../../sass/plugins/_iconmoon.scss */
.icon-plane:before {
  content: "\e98a";
}

/* line 502, ../../../../sass/plugins/_iconmoon.scss */
.icon-shield:before {
  content: "\e98b";
}

/* line 505, ../../../../sass/plugins/_iconmoon.scss */
.icon-spa:before {
  content: "\e98c";
}

/* line 508, ../../../../sass/plugins/_iconmoon.scss */
.icon-sun:before {
  content: "\e98d";
}

/* line 511, ../../../../sass/plugins/_iconmoon.scss */
.icon-ball2:before {
  content: "\e98e";
}

/* line 514, ../../../../sass/plugins/_iconmoon.scss */
.icon-bubbles:before {
  content: "\e98f";
}

/* line 517, ../../../../sass/plugins/_iconmoon.scss */
.icon-cot:before {
  content: "\e990";
}

/* line 520, ../../../../sass/plugins/_iconmoon.scss */
.icon-cutlery2:before {
  content: "\e991";
}

/* line 523, ../../../../sass/plugins/_iconmoon.scss */
.icon-golfplayer:before {
  content: "\e992";
}

/* line 526, ../../../../sass/plugins/_iconmoon.scss */
.icon-heart:before {
  content: "\e993";
}

/* line 529, ../../../../sass/plugins/_iconmoon.scss */
.icon-towels:before {
  content: "\e994";
}

/* line 532, ../../../../sass/plugins/_iconmoon.scss */
.icon-tree:before {
  content: "\e995";
}

/* line 535, ../../../../sass/plugins/_iconmoon.scss */
.icon-wifi:before {
  content: "\e996";
}

/* line 538, ../../../../sass/plugins/_iconmoon.scss */
.icon-alarmclock:before {
  content: "\e997";
}

/* line 541, ../../../../sass/plugins/_iconmoon.scss */
.icon-amenities:before {
  content: "\e998";
}

/* line 544, ../../../../sass/plugins/_iconmoon.scss */
.icon-astronomy:before {
  content: "\e999";
}

/* line 547, ../../../../sass/plugins/_iconmoon.scss */
.icon-barbecue:before {
  content: "\e99a";
}

/* line 550, ../../../../sass/plugins/_iconmoon.scss */
.icon-bells:before {
  content: "\e99b";
}

/* line 553, ../../../../sass/plugins/_iconmoon.scss */
.icon-bottle:before {
  content: "\e99c";
}

/* line 556, ../../../../sass/plugins/_iconmoon.scss */
.icon-bottle2:before {
  content: "\e99d";
}

/* line 559, ../../../../sass/plugins/_iconmoon.scss */
.icon-breakfast:before {
  content: "\e99e";
}

/* line 562, ../../../../sass/plugins/_iconmoon.scss */
.icon-broom:before {
  content: "\e99f";
}

/* line 565, ../../../../sass/plugins/_iconmoon.scss */
.icon-buffet2:before {
  content: "\e9a0";
}

/* line 568, ../../../../sass/plugins/_iconmoon.scss */
.icon-calendar2:before {
  content: "\e9a1";
}

/* line 571, ../../../../sass/plugins/_iconmoon.scss */
.icon-camera:before {
  content: "\e9a2";
}

/* line 574, ../../../../sass/plugins/_iconmoon.scss */
.icon-camera2:before {
  content: "\e9a3";
}

/* line 577, ../../../../sass/plugins/_iconmoon.scss */
.icon-caravan:before {
  content: "\e9a4";
}

/* line 580, ../../../../sass/plugins/_iconmoon.scss */
.icon-champagne:before {
  content: "\e9a5";
}

/* line 583, ../../../../sass/plugins/_iconmoon.scss */
.icon-chocolate:before {
  content: "\e9a6";
}

/* line 586, ../../../../sass/plugins/_iconmoon.scss */
.icon-christmasbauble:before {
  content: "\e9a7";
}

/* line 589, ../../../../sass/plugins/_iconmoon.scss */
.icon-christmasbauble2:before {
  content: "\e9a8";
}

/* line 592, ../../../../sass/plugins/_iconmoon.scss */
.icon-christmastree:before {
  content: "\e9a9";
}

/* line 595, ../../../../sass/plugins/_iconmoon.scss */
.icon-christmastree2:before {
  content: "\e9aa";
}

/* line 598, ../../../../sass/plugins/_iconmoon.scss */
.icon-chronometer:before {
  content: "\e9ab";
}

/* line 601, ../../../../sass/plugins/_iconmoon.scss */
.icon-clic:before {
  content: "\e9ac";
}

/* line 604, ../../../../sass/plugins/_iconmoon.scss */
.icon-cocktail2:before {
  content: "\e9ad";
}

/* line 607, ../../../../sass/plugins/_iconmoon.scss */
.icon-coffee:before {
  content: "\e9ae";
}

/* line 610, ../../../../sass/plugins/_iconmoon.scss */
.icon-coffee2:before {
  content: "\e9af";
}

/* line 613, ../../../../sass/plugins/_iconmoon.scss */
.icon-comment2:before {
  content: "\e9b0";
}

/* line 616, ../../../../sass/plugins/_iconmoon.scss */
.icon-comment3:before {
  content: "\e9b1";
}

/* line 619, ../../../../sass/plugins/_iconmoon.scss */
.icon-comments2:before {
  content: "\e9b2";
}

/* line 622, ../../../../sass/plugins/_iconmoon.scss */
.icon-cushions:before {
  content: "\e9b3";
}

/* line 625, ../../../../sass/plugins/_iconmoon.scss */
.icon-cushions2:before {
  content: "\e9b4";
}

/* line 628, ../../../../sass/plugins/_iconmoon.scss */
.icon-deaf:before {
  content: "\e9b5";
}

/* line 631, ../../../../sass/plugins/_iconmoon.scss */
.icon-design:before {
  content: "\e9b6";
}

/* line 634, ../../../../sass/plugins/_iconmoon.scss */
.icon-desktop:before {
  content: "\e9b7";
}

/* line 637, ../../../../sass/plugins/_iconmoon.scss */
.icon-dishes:before {
  content: "\e9b8";
}

/* line 640, ../../../../sass/plugins/_iconmoon.scss */
.icon-dollar:before {
  content: "\e9b9";
}

/* line 643, ../../../../sass/plugins/_iconmoon.scss */
.icon-download2:before {
  content: "\e9ba";
}

/* line 646, ../../../../sass/plugins/_iconmoon.scss */
.icon-family2:before {
  content: "\e9bb";
}

/* line 649, ../../../../sass/plugins/_iconmoon.scss */
.icon-fireworks:before {
  content: "\e9bc";
}

/* line 652, ../../../../sass/plugins/_iconmoon.scss */
.icon-flipflops:before {
  content: "\e9bd";
}

/* line 655, ../../../../sass/plugins/_iconmoon.scss */
.icon-friends2:before {
  content: "\e9be";
}

/* line 658, ../../../../sass/plugins/_iconmoon.scss */
.icon-fruit:before {
  content: "\e9bf";
}

/* line 661, ../../../../sass/plugins/_iconmoon.scss */
.icon-gender:before {
  content: "\e9c0";
}

/* line 664, ../../../../sass/plugins/_iconmoon.scss */
.icon-gift2:before {
  content: "\e9c1";
}

/* line 667, ../../../../sass/plugins/_iconmoon.scss */
.icon-gifts:before {
  content: "\e9c2";
}

/* line 670, ../../../../sass/plugins/_iconmoon.scss */
.icon-gold:before {
  content: "\e9c3";
}

/* line 673, ../../../../sass/plugins/_iconmoon.scss */
.icon-hairdrier2:before {
  content: "\e9c4";
}

/* line 676, ../../../../sass/plugins/_iconmoon.scss */
.icon-hammock:before {
  content: "\e9c5";
}

/* line 679, ../../../../sass/plugins/_iconmoon.scss */
.icon-hammock2:before {
  content: "\e9c6";
}

/* line 682, ../../../../sass/plugins/_iconmoon.scss */
.icon-heart2:before {
  content: "\e9c7";
}

/* line 685, ../../../../sass/plugins/_iconmoon.scss */
.icon-homepage:before {
  content: "\e9c8";
}

/* line 688, ../../../../sass/plugins/_iconmoon.scss */
.icon-hotel:before {
  content: "\e9c9";
}

/* line 691, ../../../../sass/plugins/_iconmoon.scss */
.icon-ice:before {
  content: "\e9ca";
}

/* line 694, ../../../../sass/plugins/_iconmoon.scss */
.icon-kettle:before {
  content: "\e9cb";
}

/* line 697, ../../../../sass/plugins/_iconmoon.scss */
.icon-kitchen:before {
  content: "\e9cc";
}

/* line 700, ../../../../sass/plugins/_iconmoon.scss */
.icon-latecheckout:before {
  content: "\e9cd";
}

/* line 703, ../../../../sass/plugins/_iconmoon.scss */
.icon-luggage:before {
  content: "\e9ce";
}

/* line 706, ../../../../sass/plugins/_iconmoon.scss */
.icon-meeting:before {
  content: "\e9cf";
}

/* line 709, ../../../../sass/plugins/_iconmoon.scss */
.icon-microwave:before {
  content: "\e9d0";
}

/* line 712, ../../../../sass/plugins/_iconmoon.scss */
.icon-monument2:before {
  content: "\e9d1";
}

/* line 715, ../../../../sass/plugins/_iconmoon.scss */
.icon-mouse1:before {
  content: "\e9d2";
}

/* line 718, ../../../../sass/plugins/_iconmoon.scss */
.icon-nani:before {
  content: "\e9d3";
}

/* line 721, ../../../../sass/plugins/_iconmoon.scss */
.icon-offer2:before {
  content: "\e9d4";
}

/* line 724, ../../../../sass/plugins/_iconmoon.scss */
.icon-offers:before {
  content: "\e9d5";
}

/* line 727, ../../../../sass/plugins/_iconmoon.scss */
.icon-percent2:before {
  content: "\e9d6";
}

/* line 730, ../../../../sass/plugins/_iconmoon.scss */
.icon-player:before {
  content: "\e9d7";
}

/* line 733, ../../../../sass/plugins/_iconmoon.scss */
.icon-romantic:before {
  content: "\e9d8";
}

/* line 736, ../../../../sass/plugins/_iconmoon.scss */
.icon-roomservice:before {
  content: "\e9d9";
}

/* line 739, ../../../../sass/plugins/_iconmoon.scss */
.icon-santa:before {
  content: "\e9da";
}

/* line 742, ../../../../sass/plugins/_iconmoon.scss */
.icon-satellite:before {
  content: "\e9db";
}

/* line 745, ../../../../sass/plugins/_iconmoon.scss */
.icon-sea2:before {
  content: "\e9dc";
}

/* line 748, ../../../../sass/plugins/_iconmoon.scss */
.icon-settings:before {
  content: "\e9dd";
}

/* line 751, ../../../../sass/plugins/_iconmoon.scss */
.icon-shield2:before {
  content: "\e9de";
}

/* line 754, ../../../../sass/plugins/_iconmoon.scss */
.icon-slippers:before {
  content: "\e9df";
}

/* line 757, ../../../../sass/plugins/_iconmoon.scss */
.icon-snowflakeeps:before {
  content: "\e9e0";
}

/* line 760, ../../../../sass/plugins/_iconmoon.scss */
.icon-soap:before {
  content: "\e9e1";
}

/* line 763, ../../../../sass/plugins/_iconmoon.scss */
.icon-sofa2:before {
  content: "\e9e2";
}

/* line 766, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialbreakfast:before {
  content: "\e9e3";
}

/* line 769, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialfamily:before {
  content: "\e9e4";
}

/* line 772, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialreception:before {
  content: "\e9e5";
}

/* line 775, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialshower:before {
  content: "\e9e6";
}

/* line 778, ../../../../sass/plugins/_iconmoon.scss */
.icon-suit:before {
  content: "\e9e7";
}

/* line 781, ../../../../sass/plugins/_iconmoon.scss */
.icon-sun2:before {
  content: "\e9e8";
}

/* line 784, ../../../../sass/plugins/_iconmoon.scss */
.icon-sun3:before {
  content: "\e9e9";
}

/* line 787, ../../../../sass/plugins/_iconmoon.scss */
.icon-tie:before {
  content: "\e9ea";
}

/* line 790, ../../../../sass/plugins/_iconmoon.scss */
.icon-toaster:before {
  content: "\e9eb";
}

/* line 793, ../../../../sass/plugins/_iconmoon.scss */
.icon-toilet:before {
  content: "\e9ec";
}

/* line 796, ../../../../sass/plugins/_iconmoon.scss */
.icon-washer2:before {
  content: "\e9ed";
}

/* line 799, ../../../../sass/plugins/_iconmoon.scss */
.icon-waterpark:before {
  content: "\e9ee";
}

/* line 802, ../../../../sass/plugins/_iconmoon.scss */
.icon-wine:before {
  content: "\e9ef";
}

/* line 805, ../../../../sass/plugins/_iconmoon.scss */
.icon-world:before {
  content: "\e9f0";
}

/* line 808, ../../../../sass/plugins/_iconmoon.scss */
.icon-www:before {
  content: "\e9f1";
}

/* line 811, ../../../../sass/plugins/_iconmoon.scss */
.icon-adults:before {
  content: "\e9f2";
}

/* line 814, ../../../../sass/plugins/_iconmoon.scss */
.icon-percentpig:before {
  content: "\e9f3";
}

/* line 817, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialwifi:before {
  content: "\e9f4";
}

/* line 820, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialbuilding:before {
  content: "\e9f5";
}

/* line 823, ../../../../sass/plugins/_iconmoon.scss */
.icon-speciallunch:before {
  content: "\e9f6";
}

/* line 826, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialweb:before {
  content: "\e9f7";
}

/* line 829, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialbed:before {
  content: "\e9f8";
}

/* line 832, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialevents:before {
  content: "\e9f9";
}

/* line 835, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialpool:before {
  content: "\e9fa";
}

/* line 838, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialbeds:before {
  content: "\e9fb";
}

/* line 841, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialcalendar:before {
  content: "\e9fc";
}

/* line 844, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialfitness:before {
  content: "\e9fd";
}

/* line 847, ../../../../sass/plugins/_iconmoon.scss */
.icon-speciallocation:before {
  content: "\e9fe";
}

/* line 850, ../../../../sass/plugins/_iconmoon.scss */
.icon-settings2:before {
  content: "\e9ff";
}

/* line 853, ../../../../sass/plugins/_iconmoon.scss */
.icon-nopets:before {
  content: "\ea00";
}

/* line 856, ../../../../sass/plugins/_iconmoon.scss */
.icon-videocamera:before {
  content: "\ea01";
}

/* line 859, ../../../../sass/plugins/_iconmoon.scss */
.icon-window1:before {
  content: "\ea02";
}

/* line 862, ../../../../sass/plugins/_iconmoon.scss */
.icon-offer:before {
  content: "\ea03";
}

/* line 865, ../../../../sass/plugins/_iconmoon.scss */
.icon-save:before {
  content: "\ea04";
}

/* line 868, ../../../../sass/plugins/_iconmoon.scss */
.icon-plane2:before {
  content: "\ea05";
}

/* line 871, ../../../../sass/plugins/_iconmoon.scss */
.icon-longarrow:before {
  content: "\ea06";
}

/* line 874, ../../../../sass/plugins/_iconmoon.scss */
.icon-paraty:before {
  content: "\ea07";
}

/* line 877, ../../../../sass/plugins/_iconmoon.scss */
.icon-horseshoe:before {
  content: "\ea08";
}

/* line 880, ../../../../sass/plugins/_iconmoon.scss */
.icon-balloons:before {
  content: "\ea09";
}

/* line 883, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-tiger:before {
  content: "\ea0a";
}

/* line 886, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-2drinks:before {
  content: "\ea0b";
}

/* line 889, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-bbq:before {
  content: "\ea0c";
}

/* line 892, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-disco:before {
  content: "\ea0d";
}

/* line 895, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-pasta:before {
  content: "\ea0e";
}

/* line 898, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-snack:before {
  content: "\ea0f";
}

/* line 901, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-archery:before {
  content: "\ea10";
}

/* line 904, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-football:before {
  content: "\ea11";
}

/* line 907, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-gameboard:before {
  content: "\ea12";
}

/* line 910, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-golf:before {
  content: "\ea13";
}

/* line 913, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-hotbath:before {
  content: "\ea14";
}

/* line 916, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-hotpool:before {
  content: "\ea15";
}

/* line 919, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-iron:before {
  content: "\ea16";
}

/* line 922, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-jetshower:before {
  content: "\ea17";
}

/* line 925, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-massage:before {
  content: "\ea18";
}

/* line 928, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-petanque:before {
  content: "\ea19";
}

/* line 931, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-phone:before {
  content: "\ea1a";
}

/* line 934, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-shooting:before {
  content: "\ea1b";
}

/* line 937, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-volleyball:before {
  content: "\ea1c";
}

/* line 940, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-balloons:before {
  content: "\ea1d";
}

/* line 943, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-bike:before {
  content: "\ea1e";
}

/* line 946, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-gift:before {
  content: "\ea1f";
}

/* line 949, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-jacuzzi:before {
  content: "\ea20";
}

/* line 952, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-mouse:before {
  content: "\ea21";
}

/* line 955, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-movie:before {
  content: "\ea22";
}

/* line 958, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-playground:before {
  content: "\ea23";
}

/* line 961, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-playingcards:before {
  content: "\ea24";
}

/* line 964, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-shopping:before {
  content: "\ea25";
}

/* line 967, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-shower:before {
  content: "\ea26";
}

/* line 970, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-sofa:before {
  content: "\ea27";
}

/* line 973, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-washing:before {
  content: "\ea28";
}

/* line 976, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-bills:before {
  content: "\ea29";
}

/* line 979, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-car:before {
  content: "\ea2a";
}

/* line 982, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-creditcard:before {
  content: "\ea2b";
}

/* line 985, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-drink:before {
  content: "\ea2c";
}

/* line 988, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-fruit:before {
  content: "\ea2d";
}

/* line 991, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-lock:before {
  content: "\ea2e";
}

/* line 994, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-offer:before {
  content: "\ea2f";
}

/* line 997, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-spa:before {
  content: "\ea30";
}

/* line 1000, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-tennis:before {
  content: "\ea31";
}

/* line 1003, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-aircon:before {
  content: "\ea32";
}

/* line 1006, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-hairdryer:before {
  content: "\ea33";
}

/* line 1009, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-kitchen:before {
  content: "\ea34";
}

/* line 1012, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-tv:before {
  content: "\ea35";
}

/* line 1015, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-wifi:before {
  content: "\ea36";
}

/* line 1018, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-clock:before {
  content: "\ea37";
}

/* line 1021, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-lightning:before {
  content: "\ea38";
}

/* line 1024, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-piggybank:before {
  content: "\ea39";
}

/* line 1027, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-pricetag:before {
  content: "\ea3a";
}

/* line 1030, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-creditcard2:before {
  content: "\ea3b";
}

/* line 1033, ../../../../sass/plugins/_iconmoon.scss */
.icon-360:before {
  content: "\ea3c";
}

/* line 1036, ../../../../sass/plugins/_iconmoon.scss */
.icon-contactless:before {
  content: "\ea3d";
}

/* line 1039, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-umbrella:before {
  content: "\ea3e";
}

/* line 1042, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-sun:before {
  content: "\ea3f";
}

/* line 1045, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-slippers:before {
  content: "\ea40";
}

/* line 1048, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-mail:before {
  content: "\ea41";
}

/* line 1051, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-comment:before {
  content: "\ea42";
}

/* line 3, ../../../../sass/plugins/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 8, ../../../../sass/plugins/_mixins.scss */
.center_xy_before:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 13, ../../../../sass/plugins/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 17, ../../../../sass/plugins/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 21, ../../../../sass/plugins/_mixins.scss */
.center_y_before:before {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 25, ../../../../sass/plugins/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 30, ../../../../sass/plugins/_mixins.scss */
.fs:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
}

/* line 110, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:before, .icon-xcross:after {
  content: '';
  width: 100%;
  height: 2px;
  background: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 124, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -moz-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 61, ../../../../sass/plugins/_mixins.scss */
.display_flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* line 1, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown {
  margin: 40px 20px 20px;
  text-align: center;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  bottom: 180px;
  left: 48% !important;
  color: white;
}
/* line 9, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown div {
  display: inline-block;
  font-size: 16px;
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 0;
  padding-bottom: 0;
  text-transform: uppercase;
}
/* line 20, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .days {
  font-weight: 600;
}
/* line 23, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .date {
  display: block;
  font-size: 4.5rem;
}
/* line 28, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .title_format {
  font-weight: 600;
}

@media screen and (max-width: 800px) {
  /* line 35, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown {
    display: inline-block;
    width: 380px;
    top: 330px;
    left: 45% !important;
  }
  /* line 40, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown div {
    font-size: 10px;
  }
  /* line 43, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown .date {
    font-size: 2.5rem;
  }
}
/**
 * Owl Carousel v2.2.1
 * Copyright 2013-2017 David Deutsch
 * Licensed under  ()
 */
/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel, .owl-carousel .owl-item {
  -webkit-tap-highlight-color: transparent;
  position: relative;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel {
  display: none;
  width: 100%;
  z-index: 1;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage {
  position: relative;
  -ms-touch-action: pan-Y;
  -moz-backface-visibility: hidden;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage:after {
  content: ".";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage-outer {
  position: relative;
  overflow: hidden;
  -webkit-transform: translate3d(0, 0, 0);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item, .owl-carousel .owl-wrapper {
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item {
  min-height: 1px;
  float: left;
  -webkit-backface-visibility: hidden;
  -webkit-touch-callout: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item img {
  display: block;
  width: 100%;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-dots.disabled, .owl-carousel .owl-nav.disabled {
  display: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.no-js .owl-carousel, .owl-carousel.owl-loaded {
  display: block;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-dot, .owl-carousel .owl-nav .owl-next, .owl-carousel .owl-nav .owl-prev {
  cursor: pointer;
  cursor: hand;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-loading {
  opacity: 0;
  display: block;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-hidden {
  opacity: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-refresh .owl-item {
  visibility: hidden;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-drag .owl-item {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-grab {
  cursor: move;
  cursor: grab;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-rtl {
  direction: rtl;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-rtl .owl-item {
  float: right;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-animated-in {
  z-index: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-animated-out {
  z-index: 1;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .fadeOut {
  animation-name: fadeOut;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-height {
  transition: height .5s ease-in-out;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item .owl-lazy {
  opacity: 0;
  transition: opacity .4s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item img.owl-lazy {
  transform-style: preserve-3d;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-wrapper {
  position: relative;
  height: 100%;
  background: #000;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-play-icon {
  position: absolute;
  height: 80px;
  width: 80px;
  left: 50%;
  top: 50%;
  margin-left: -40px;
  margin-top: -40px;
  background: url(owl.video.play.png) no-repeat;
  cursor: pointer;
  z-index: 1;
  -webkit-backface-visibility: hidden;
  transition: transform .1s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-play-icon:hover {
  -ms-transform: scale(1.3, 1.3);
  transform: scale(1.3, 1.3);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-playing .owl-video-play-icon, .owl-carousel .owl-video-playing .owl-video-tn {
  display: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-tn {
  opacity: 0;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: opacity .4s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-frame {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
body.compensate-for-scrollbar {
  overflow: hidden;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-active {
  height: auto;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-is-hidden {
  left: -9999px;
  margin: 0;
  position: absolute !important;
  top: -9999px;
  visibility: hidden;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-container {
  -webkit-backface-visibility: hidden;
  height: 100%;
  left: 0;
  outline: none;
  position: fixed;
  -webkit-tap-highlight-color: transparent;
  top: 0;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  transform: translateZ(0);
  width: 100%;
  z-index: 99992;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-container * {
  box-sizing: border-box;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-bg, .fancybox-inner, .fancybox-outer, .fancybox-stage {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-outer {
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-bg {
  background: #1e1e1e;
  opacity: 0;
  transition-duration: inherit;
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.47, 0, 0.74, 0.71);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-is-open .fancybox-bg {
  opacity: .9;
  transition-timing-function: cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-caption, .fancybox-infobar, .fancybox-navigation .fancybox-button, .fancybox-toolbar {
  direction: ltr;
  opacity: 0;
  position: absolute;
  transition: opacity .25s ease,visibility 0s ease .25s;
  visibility: hidden;
  z-index: 99997;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-show-caption .fancybox-caption, .fancybox-show-infobar .fancybox-infobar, .fancybox-show-nav .fancybox-navigation .fancybox-button, .fancybox-show-toolbar .fancybox-toolbar {
  opacity: 1;
  transition: opacity .25s ease 0s,visibility 0s ease 0s;
  visibility: visible;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-infobar {
  color: #ccc;
  font-size: 13px;
  -webkit-font-smoothing: subpixel-antialiased;
  height: 44px;
  left: 0;
  line-height: 44px;
  min-width: 44px;
  mix-blend-mode: difference;
  padding: 0 10px;
  pointer-events: none;
  top: 0;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-toolbar {
  right: 0;
  top: 0;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-stage {
  direction: ltr;
  overflow: visible;
  transform: translateZ(0);
  z-index: 99994;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-is-open .fancybox-stage {
  overflow: hidden;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-slide {
  -webkit-backface-visibility: hidden;
  display: none;
  height: 100%;
  left: 0;
  outline: none;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  padding: 44px;
  position: absolute;
  text-align: center;
  top: 0;
  transition-property: transform,opacity;
  white-space: normal;
  width: 100%;
  z-index: 99994;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-slide:before {
  content: "";
  display: inline-block;
  font-size: 0;
  height: 100%;
  vertical-align: middle;
  width: 0;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-is-sliding .fancybox-slide, .fancybox-slide--current, .fancybox-slide--next, .fancybox-slide--previous {
  display: block;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-slide--image {
  overflow: hidden;
  padding: 44px 0;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-slide--image:before {
  display: none;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-slide--html {
  padding: 6px;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-content {
  background: #fff;
  display: inline-block;
  margin: 0;
  max-width: 100%;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  padding: 44px;
  position: relative;
  text-align: left;
  vertical-align: middle;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-slide--image .fancybox-content {
  animation-timing-function: cubic-bezier(0.5, 0, 0.14, 1);
  -webkit-backface-visibility: hidden;
  background: transparent;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  left: 0;
  max-width: none;
  overflow: visible;
  padding: 0;
  position: absolute;
  top: 0;
  transform-origin: top left;
  transition-property: transform,opacity;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  z-index: 99995;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-can-zoomOut .fancybox-content {
  cursor: zoom-out;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-can-zoomIn .fancybox-content {
  cursor: zoom-in;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-can-pan .fancybox-content, .fancybox-can-swipe .fancybox-content {
  cursor: grab;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-is-grabbing .fancybox-content {
  cursor: grabbing;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-container [data-selectable=true] {
  cursor: text;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-image, .fancybox-spaceball {
  background: transparent;
  border: 0;
  height: 100%;
  left: 0;
  margin: 0;
  max-height: none;
  max-width: none;
  padding: 0;
  position: absolute;
  top: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 100%;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-spaceball {
  z-index: 1;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-slide--iframe .fancybox-content, .fancybox-slide--map .fancybox-content, .fancybox-slide--pdf .fancybox-content, .fancybox-slide--video .fancybox-content {
  height: 100%;
  overflow: visible;
  padding: 0;
  width: 100%;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-slide--video .fancybox-content {
  background: #000;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-slide--map .fancybox-content {
  background: #e5e3df;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-slide--iframe .fancybox-content {
  background: #fff;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-iframe, .fancybox-video {
  background: transparent;
  border: 0;
  display: block;
  height: 100%;
  margin: 0;
  overflow: hidden;
  padding: 0;
  width: 100%;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-iframe {
  left: 0;
  position: absolute;
  top: 0;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-error {
  background: #fff;
  cursor: default;
  max-width: 400px;
  padding: 40px;
  width: 100%;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-error p {
  color: #444;
  font-size: 16px;
  line-height: 20px;
  margin: 0;
  padding: 0;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-button {
  background: rgba(30, 30, 30, 0.6);
  border: 0;
  border-radius: 0;
  box-shadow: none;
  cursor: pointer;
  display: inline-block;
  height: 44px;
  margin: 0;
  padding: 10px;
  position: relative;
  transition: color .2s;
  vertical-align: top;
  visibility: inherit;
  width: 44px;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-button, .fancybox-button:link, .fancybox-button:visited {
  color: #ccc;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-button:hover {
  color: #fff;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-button:focus {
  outline: none;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-button.fancybox-focus {
  outline: 1px dotted;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-button[disabled], .fancybox-button[disabled]:hover {
  color: #888;
  cursor: default;
  outline: none;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-button div {
  height: 100%;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-button svg {
  display: block;
  height: 100%;
  overflow: visible;
  position: relative;
  width: 100%;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-button svg path {
  fill: currentColor;
  stroke-width: 0;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-button--fsenter svg:nth-child(2), .fancybox-button--fsexit svg:first-child, .fancybox-button--pause svg:first-child, .fancybox-button--play svg:nth-child(2) {
  display: none;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-progress {
  background: #ff5268;
  height: 2px;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  transform: scaleX(0);
  transform-origin: 0;
  transition-property: transform;
  transition-timing-function: linear;
  z-index: 99998;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-close-small {
  background: transparent;
  border: 0;
  border-radius: 0;
  color: #ccc;
  cursor: pointer;
  opacity: .8;
  padding: 8px;
  position: absolute;
  right: -12px;
  top: -44px;
  z-index: 401;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-close-small:hover {
  color: #fff;
  opacity: 1;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-slide--html .fancybox-close-small {
  color: currentColor;
  padding: 10px;
  right: 0;
  top: 0;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-slide--image.fancybox-is-scaling .fancybox-content {
  overflow: hidden;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-is-scaling .fancybox-close-small, .fancybox-is-zoomable.fancybox-can-pan .fancybox-close-small {
  display: none;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-navigation .fancybox-button {
  background-clip: content-box;
  height: 100px;
  opacity: 0;
  position: absolute;
  top: calc(50% - 50px);
  width: 70px;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-navigation .fancybox-button div {
  padding: 7px;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-navigation .fancybox-button--arrow_left {
  left: 0;
  left: env(safe-area-inset-left);
  padding: 31px 26px 31px 6px;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-navigation .fancybox-button--arrow_right {
  padding: 31px 6px 31px 26px;
  right: 0;
  right: env(safe-area-inset-right);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-caption {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.85) 0, rgba(0, 0, 0, 0.3) 50%, rgba(0, 0, 0, 0.15) 65%, rgba(0, 0, 0, 0.075) 75.5%, rgba(0, 0, 0, 0.037) 82.85%, rgba(0, 0, 0, 0.019) 88%, transparent);
  bottom: 0;
  color: #eee;
  font-size: 14px;
  font-weight: 400;
  left: 0;
  line-height: 1.5;
  padding: 75px 44px 25px;
  pointer-events: none;
  right: 0;
  text-align: center;
  z-index: 99996;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-caption--separate {
  margin-top: -50px;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-caption__body {
  max-height: 50vh;
  overflow: auto;
  pointer-events: all;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-caption a, .fancybox-caption a:link, .fancybox-caption a:visited {
  color: #ccc;
  text-decoration: none;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-caption a:hover {
  color: #fff;
  text-decoration: underline;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-loading {
  animation: a 1s linear infinite;
  background: transparent;
  border: 4px solid #888;
  border-bottom-color: #fff;
  border-radius: 50%;
  height: 50px;
  left: 50%;
  margin: -25px 0 0 -25px;
  opacity: .7;
  padding: 0;
  position: absolute;
  top: 50%;
  width: 50px;
  z-index: 99999;
}

@keyframes a {
  to {
    transform: rotate(1turn);
  }
}
/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-animated {
  transition-timing-function: cubic-bezier(0, 0, 0.25, 1);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-slide.fancybox-slide--previous {
  opacity: 0;
  transform: translate3d(-100%, 0, 0);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-slide.fancybox-slide--next {
  opacity: 0;
  transform: translate3d(100%, 0, 0);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-slide.fancybox-slide--current {
  opacity: 1;
  transform: translateZ(0);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-fade.fancybox-slide--next, .fancybox-fx-fade.fancybox-slide--previous {
  opacity: 0;
  transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-fade.fancybox-slide--current {
  opacity: 1;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-zoom-in-out.fancybox-slide--previous {
  opacity: 0;
  transform: scale3d(1.5, 1.5, 1.5);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-zoom-in-out.fancybox-slide--next {
  opacity: 0;
  transform: scale3d(0.5, 0.5, 0.5);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-zoom-in-out.fancybox-slide--current {
  opacity: 1;
  transform: scaleX(1);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-rotate.fancybox-slide--previous {
  opacity: 0;
  transform: rotate(-1turn);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-rotate.fancybox-slide--next {
  opacity: 0;
  transform: rotate(1turn);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-rotate.fancybox-slide--current {
  opacity: 1;
  transform: rotate(0deg);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-circular.fancybox-slide--previous {
  opacity: 0;
  transform: scale3d(0, 0, 0) translate3d(-100%, 0, 0);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-circular.fancybox-slide--next {
  opacity: 0;
  transform: scale3d(0, 0, 0) translate3d(100%, 0, 0);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-circular.fancybox-slide--current {
  opacity: 1;
  transform: scaleX(1) translateZ(0);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-tube.fancybox-slide--previous {
  transform: translate3d(-100%, 0, 0) scale(0.1) skew(-10deg);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-tube.fancybox-slide--next {
  transform: translate3d(100%, 0, 0) scale(0.1) skew(10deg);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-fx-tube.fancybox-slide--current {
  transform: translateZ(0) scale(1);
}

@media (max-height: 576px) {
  /* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
  .fancybox-slide {
    padding-left: 6px;
    padding-right: 6px;
  }

  /* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
  .fancybox-slide--image {
    padding: 6px 0;
  }

  /* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
  .fancybox-close-small {
    right: -6px;
  }

  /* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
  .fancybox-slide--image .fancybox-close-small {
    background: #4e4e4e;
    color: #f2f4f6;
    height: 36px;
    opacity: 1;
    padding: 6px;
    right: 0;
    top: 0;
    width: 36px;
  }

  /* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
  .fancybox-caption {
    padding-left: 12px;
    padding-right: 12px;
  }
}
/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share {
  background: #f4f4f4;
  border-radius: 3px;
  max-width: 90%;
  padding: 30px;
  text-align: center;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share h1 {
  color: #222;
  font-size: 35px;
  font-weight: 700;
  margin: 0 0 20px;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share p {
  margin: 0;
  padding: 0;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share__button {
  border: 0;
  border-radius: 3px;
  display: inline-block;
  font-size: 14px;
  font-weight: 700;
  line-height: 40px;
  margin: 0 5px 10px;
  min-width: 130px;
  padding: 0 15px;
  text-decoration: none;
  transition: all .2s;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share__button:link, .fancybox-share__button:visited {
  color: #fff;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share__button:hover {
  text-decoration: none;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share__button--fb {
  background: #3b5998;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share__button--fb:hover {
  background: #344e86;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share__button--pt {
  background: #bd081d;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share__button--pt:hover {
  background: #aa0719;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share__button--tw {
  background: #1da1f2;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share__button--tw:hover {
  background: #0d95e8;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share__button svg {
  height: 25px;
  margin-right: 7px;
  position: relative;
  top: -1px;
  vertical-align: middle;
  width: 25px;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share__button svg path {
  fill: #fff;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-share__input {
  background: transparent;
  border: 0;
  border-bottom: 1px solid #d7d7d7;
  border-radius: 0;
  color: #5d5b5b;
  font-size: 14px;
  margin: 10px 0 0;
  outline: none;
  padding: 10px 15px;
  width: 100%;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-thumbs {
  background: #ddd;
  bottom: 0;
  display: none;
  margin: 0;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  padding: 2px 2px 4px;
  position: absolute;
  right: 0;
  -webkit-tap-highlight-color: transparent;
  top: 0;
  width: 212px;
  z-index: 99995;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-thumbs-x {
  overflow-x: auto;
  overflow-y: hidden;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-show-thumbs .fancybox-thumbs {
  display: block;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-show-thumbs .fancybox-inner {
  right: 212px;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-thumbs__list {
  font-size: 0;
  height: 100%;
  list-style: none;
  margin: 0;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0;
  position: absolute;
  position: relative;
  white-space: nowrap;
  width: 100%;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-thumbs-x .fancybox-thumbs__list {
  overflow: hidden;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-thumbs-y .fancybox-thumbs__list::-webkit-scrollbar {
  width: 7px;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-thumbs-y .fancybox-thumbs__list::-webkit-scrollbar-track {
  background: #fff;
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-thumbs-y .fancybox-thumbs__list::-webkit-scrollbar-thumb {
  background: #2a2a2a;
  border-radius: 10px;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-thumbs__list a {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  background-color: rgba(0, 0, 0, 0.1);
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  cursor: pointer;
  float: left;
  height: 75px;
  margin: 2px;
  max-height: calc(100% - 8px);
  max-width: calc(50% - 4px);
  outline: none;
  overflow: hidden;
  padding: 0;
  position: relative;
  -webkit-tap-highlight-color: transparent;
  width: 100px;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-thumbs__list a:before {
  border: 6px solid #ff5268;
  bottom: 0;
  content: "";
  left: 0;
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 99991;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-thumbs__list a:focus:before {
  opacity: .5;
}

/* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.fancybox-thumbs__list a.fancybox-thumbs-active:before {
  opacity: 1;
}

@media (max-width: 576px) {
  /* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
  .fancybox-thumbs {
    width: 110px;
  }

  /* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
  .fancybox-show-thumbs .fancybox-inner {
    right: 110px;
  }

  /* line 1, ../../../../sass/plugins/_fancybox_3_5_7.scss */
  .fancybox-thumbs__list a {
    max-width: calc(100% - 10px);
  }
}
/* line 5, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.jquery_migration .fancybox-slide > * {
  overflow: visible;
  padding: 15px;
}
/* line 9, ../../../../sass/plugins/_fancybox_3_5_7.scss */
.jquery_migration .fancybox-button {
  width: 36px;
  height: 36px;
  background: transparent url("/static_1/lib/fancybox/source/fancybox_sprite.png");
}

@media only screen and (max-device-width: 600px) {
  /* line 18, ../../../../sass/plugins/_fancybox_3_5_7.scss */
  .fancybox-slide--iframe .fancybox-content {
    max-height: 100%;
  }
}
/* line 3, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-hidden {
  display: none;
}

/* line 7, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-clearfix, .ui-icon {
  display: block;
}

/* line 11, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-hidden-accessible {
  position: absolute !important;
  clip: rect(1px 1px 1px 1px);
  clip: rect(1px, 1px, 1px, 1px);
}

/* line 17, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-reset {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  line-height: 1.3;
  text-decoration: none;
  font-size: 100%;
  list-style: none;
}

/* line 28, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

/* line 36, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
* html .ui-helper-clearfix {
  height: 1%;
}

/* line 40, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-zfix, .ui-widget-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* line 48, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-zfix {
  opacity: 0;
  filter: Alpha(Opacity=0);
}

/* line 53, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-disabled {
  cursor: default !important;
}

/* line 57, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon {
  text-indent: -99999px;
  overflow: hidden;
  background-repeat: no-repeat;
}

/* line 63, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget {
  font-family: Lucida Grande, Lucida Sans, Arial, sans-serif;
  font-size: 1.1em;
}

/* line 68, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget .ui-widget {
  font-size: 1em;
}

/* line 72, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget button, .ui-widget input, .ui-widget select, .ui-widget textarea {
  font-family: Lucida Grande, Lucida Sans, Arial, sans-serif;
  font-size: 1em;
}

/* line 76, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-content {
  border: 1px solid #a6c9e2;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_inset-hard_100_fcfdfd_1x100.png) 50% bottom repeat-x #fcfdfd;
  color: #222;
}

/* line 82, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-content a {
  color: #222;
}

/* line 86, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-header {
  border: 1px solid #4297d7;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_gloss-wave_55_5c9ccc_500x100.png) 50% 50% repeat-x #5c9ccc;
  color: #fff;
  font-weight: 700;
}

/* line 93, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-header a {
  color: #fff;
}

/* line 97, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  border: 1px solid #c5dbec;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_glass_85_dfeffc_1x400.png) 50% 50% repeat-x #dfeffc;
  font-weight: 700;
  color: #2e6e9e;
}

/* line 104, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-default a, .ui-state-default a:link, .ui-state-default a:visited {
  color: #2e6e9e;
  text-decoration: none;
}

/* line 109, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-focus, .ui-state-hover, .ui-widget-content .ui-state-focus, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-focus, .ui-widget-header .ui-state-hover {
  border: 1px solid #79b7e7;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_glass_75_d0e5f5_1x400.png) 50% 50% repeat-x #d0e5f5;
  font-weight: 700;
  color: #1d5987;
}

/* line 116, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-hover a, .ui-state-hover a:hover {
  color: #1d5987;
  text-decoration: none;
}

/* line 121, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active {
  border: 1px solid #79b7e7;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_inset-hard_100_f5f8f9_1x100.png) 50% 50% repeat-x #f5f8f9;
  font-weight: 700;
  color: #e17009;
}

/* line 128, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-active a, .ui-state-active a:link, .ui-state-active a:visited {
  color: #e17009;
  text-decoration: none;
}

/* line 133, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget :active {
  outline: 0;
}

/* line 137, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {
  border: 1px solid #fad42e;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_flat_55_fbec88_40x100.png) 50% 50% repeat-x #fbec88;
  color: #363636;
}

/* line 143, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-highlight a, .ui-widget-content .ui-state-highlight a, .ui-widget-header .ui-state-highlight a {
  color: #363636;
}

/* line 147, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-error, .ui-widget-content .ui-state-error, .ui-widget-header .ui-state-error {
  border: 1px solid #cd0a0a;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_glass_95_fef1ec_1x400.png) 50% 50% repeat-x #fef1ec;
  color: #cd0a0a;
}

/* line 153, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-error a, .ui-state-error-text, .ui-widget-content .ui-state-error a, .ui-widget-content .ui-state-error-text, .ui-widget-header .ui-state-error a, .ui-widget-header .ui-state-error-text {
  color: #cd0a0a;
}

/* line 157, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-priority-primary, .ui-widget-content .ui-priority-primary, .ui-widget-header .ui-priority-primary {
  font-weight: 700;
}

/* line 161, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-priority-secondary, .ui-widget-content .ui-priority-secondary, .ui-widget-header .ui-priority-secondary {
  opacity: .7;
  filter: Alpha(Opacity=70);
  font-weight: 400;
}

/* line 167, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled {
  opacity: .35;
  filter: Alpha(Opacity=35);
  background-image: none;
}
/* line 172, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-disabled.ui-datepicker-other-month, .ui-widget-content .ui-state-disabled.ui-datepicker-other-month, .ui-widget-header .ui-state-disabled.ui-datepicker-other-month {
  opacity: 0;
}

/* line 177, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon, .ui-widget-content .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_469bdd_256x240.png);
}

/* line 181, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon {
  width: 16px;
  height: 16px;
}

/* line 186, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-header .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_d8e7f3_256x240.png);
}

/* line 190, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-default .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_6da8d5_256x240.png);
}

/* line 194, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-focus .ui-icon, .ui-state-hover .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_217bc0_256x240.png);
}

/* line 198, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-active .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_f9bd01_256x240.png);
}

/* line 202, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-highlight .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_2e83ff_256x240.png);
}

/* line 206, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-error .ui-icon, .ui-state-error-text .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_cd0a0a_256x240.png);
}

/* line 210, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-n {
  background-position: 0 0;
}

/* line 214, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-ne {
  background-position: -16px 0;
}

/* line 218, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-e {
  background-position: -32px 0;
}

/* line 222, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-se {
  background-position: -48px 0;
}

/* line 226, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-s {
  background-position: -64px 0;
}

/* line 230, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-sw {
  background-position: -80px 0;
}

/* line 234, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-w {
  background-position: -96px 0;
}

/* line 238, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-nw {
  background-position: -112px 0;
}

/* line 242, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-2-n-s {
  background-position: -128px 0;
}

/* line 246, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-2-e-w {
  background-position: -144px 0;
}

/* line 250, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-n {
  background-position: 0 -16px;
}

/* line 254, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-ne {
  background-position: -16px -16px;
}

/* line 258, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-e {
  background-position: -32px -16px;
}

/* line 262, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-se {
  background-position: -48px -16px;
}

/* line 266, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-s {
  background-position: -64px -16px;
}

/* line 270, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-sw {
  background-position: -80px -16px;
}

/* line 274, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-w {
  background-position: -96px -16px;
}

/* line 278, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-nw {
  background-position: -112px -16px;
}

/* line 282, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-2-n-s {
  background-position: -128px -16px;
}

/* line 286, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-2-e-w {
  background-position: -144px -16px;
}

/* line 290, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-n {
  background-position: 0 -32px;
}

/* line 294, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-ne {
  background-position: -16px -32px;
}

/* line 298, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-e {
  background-position: -32px -32px;
}

/* line 302, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-se {
  background-position: -48px -32px;
}

/* line 306, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-s {
  background-position: -64px -32px;
}

/* line 310, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-sw {
  background-position: -80px -32px;
}

/* line 314, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-w {
  background-position: -96px -32px;
}

/* line 318, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-nw {
  background-position: -112px -32px;
}

/* line 322, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-n-s {
  background-position: -128px -32px;
}

/* line 326, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-ne-sw {
  background-position: -144px -32px;
}

/* line 330, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-e-w {
  background-position: -160px -32px;
}

/* line 334, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-se-nw {
  background-position: -176px -32px;
}

/* line 338, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-n {
  background-position: -192px -32px;
}

/* line 342, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-e {
  background-position: -208px -32px;
}

/* line 346, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-s {
  background-position: -224px -32px;
}

/* line 350, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-w {
  background-position: -240px -32px;
}

/* line 354, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-n {
  background-position: 0 -48px;
}

/* line 358, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-ne {
  background-position: -16px -48px;
}

/* line 362, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-e {
  background-position: -32px -48px;
}

/* line 366, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-se {
  background-position: -48px -48px;
}

/* line 370, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-s {
  background-position: -64px -48px;
}

/* line 374, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-sw {
  background-position: -80px -48px;
}

/* line 378, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-w {
  background-position: -96px -48px;
}

/* line 382, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-nw {
  background-position: -112px -48px;
}

/* line 386, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-n-s {
  background-position: -128px -48px;
}

/* line 390, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-ne-sw {
  background-position: -144px -48px;
}

/* line 394, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-e-w {
  background-position: -160px -48px;
}

/* line 398, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-se-nw {
  background-position: -176px -48px;
}

/* line 402, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-n {
  background-position: -192px -48px;
}

/* line 406, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-e {
  background-position: -208px -48px;
}

/* line 410, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-s {
  background-position: -224px -48px;
}

/* line 414, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-w {
  background-position: -240px -48px;
}

/* line 418, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-w {
  background-position: 0 -64px;
}

/* line 422, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-n {
  background-position: -16px -64px;
}

/* line 426, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-e {
  background-position: -32px -64px;
}

/* line 430, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-s {
  background-position: -48px -64px;
}

/* line 434, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-w {
  background-position: -64px -64px;
}

/* line 438, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-n {
  background-position: -80px -64px;
}

/* line 442, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-e {
  background-position: -96px -64px;
}

/* line 446, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-s {
  background-position: -112px -64px;
}

/* line 450, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-w {
  background-position: -128px -64px;
}

/* line 454, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-n {
  background-position: -144px -64px;
}

/* line 458, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-e {
  background-position: -160px -64px;
}

/* line 462, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-s {
  background-position: -176px -64px;
}

/* line 466, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-4 {
  background-position: 0 -80px;
}

/* line 470, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-4-diag {
  background-position: -16px -80px;
}

/* line 474, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-extlink {
  background-position: -32px -80px;
}

/* line 478, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-newwin {
  background-position: -48px -80px;
}

/* line 482, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-refresh {
  background-position: -64px -80px;
}

/* line 486, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-shuffle {
  background-position: -80px -80px;
}

/* line 490, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-transfer-e-w {
  background-position: -96px -80px;
}

/* line 494, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-transferthick-e-w {
  background-position: -112px -80px;
}

/* line 498, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-folder-collapsed {
  background-position: 0 -96px;
}

/* line 502, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-folder-open {
  background-position: -16px -96px;
}

/* line 506, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-document {
  background-position: -32px -96px;
}

/* line 510, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-document-b {
  background-position: -48px -96px;
}

/* line 514, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-note {
  background-position: -64px -96px;
}

/* line 518, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-mail-closed {
  background-position: -80px -96px;
}

/* line 522, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-mail-open {
  background-position: -96px -96px;
}

/* line 526, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-suitcase {
  background-position: -112px -96px;
}

/* line 530, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-comment {
  background-position: -128px -96px;
}

/* line 534, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-person {
  background-position: -144px -96px;
}

/* line 538, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-print {
  background-position: -160px -96px;
}

/* line 542, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-trash {
  background-position: -176px -96px;
}

/* line 546, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-locked {
  background-position: -192px -96px;
}

/* line 550, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-unlocked {
  background-position: -208px -96px;
}

/* line 554, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-bookmark {
  background-position: -224px -96px;
}

/* line 558, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-tag {
  background-position: -240px -96px;
}

/* line 562, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-home {
  background-position: 0 -112px;
}

/* line 566, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-flag {
  background-position: -16px -112px;
}

/* line 570, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-calendar {
  background-position: -32px -112px;
}

/* line 574, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-cart {
  background-position: -48px -112px;
}

/* line 578, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pencil {
  background-position: -64px -112px;
}

/* line 582, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-clock {
  background-position: -80px -112px;
}

/* line 586, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-disk {
  background-position: -96px -112px;
}

/* line 590, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-calculator {
  background-position: -112px -112px;
}

/* line 594, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-zoomin {
  background-position: -128px -112px;
}

/* line 598, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-zoomout {
  background-position: -144px -112px;
}

/* line 602, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-search {
  background-position: -160px -112px;
}

/* line 606, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-wrench {
  background-position: -176px -112px;
}

/* line 610, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-gear {
  background-position: -192px -112px;
}

/* line 614, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-heart {
  background-position: -208px -112px;
}

/* line 618, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-star {
  background-position: -224px -112px;
}

/* line 622, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-link {
  background-position: -240px -112px;
}

/* line 626, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-cancel {
  background-position: 0 -128px;
}

/* line 630, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-plus {
  background-position: -16px -128px;
}

/* line 634, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-plusthick {
  background-position: -32px -128px;
}

/* line 638, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-minus {
  background-position: -48px -128px;
}

/* line 642, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-minusthick {
  background-position: -64px -128px;
}

/* line 646, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-close {
  background-position: -80px -128px;
}

/* line 650, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-closethick {
  background-position: -96px -128px;
}

/* line 654, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-key {
  background-position: -112px -128px;
}

/* line 658, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-lightbulb {
  background-position: -128px -128px;
}

/* line 662, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-scissors {
  background-position: -144px -128px;
}

/* line 666, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-clipboard {
  background-position: -160px -128px;
}

/* line 670, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-copy {
  background-position: -176px -128px;
}

/* line 674, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-contact {
  background-position: -192px -128px;
}

/* line 678, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-image {
  background-position: -208px -128px;
}

/* line 682, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-video {
  background-position: -224px -128px;
}

/* line 686, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-script {
  background-position: -240px -128px;
}

/* line 690, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-alert {
  background-position: 0 -144px;
}

/* line 694, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-info {
  background-position: -16px -144px;
}

/* line 698, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-notice {
  background-position: -32px -144px;
}

/* line 702, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-help {
  background-position: -48px -144px;
}

/* line 706, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-check {
  background-position: -64px -144px;
}

/* line 710, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-bullet {
  background-position: -80px -144px;
}

/* line 714, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-radio-off {
  background-position: -96px -144px;
}

/* line 718, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-radio-on {
  background-position: -112px -144px;
}

/* line 722, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pin-w {
  background-position: -128px -144px;
}

/* line 726, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pin-s {
  background-position: -144px -144px;
}

/* line 730, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-play {
  background-position: 0 -160px;
}

/* line 734, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pause {
  background-position: -16px -160px;
}

/* line 738, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-next {
  background-position: -32px -160px;
}

/* line 742, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-prev {
  background-position: -48px -160px;
}

/* line 746, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-end {
  background-position: -64px -160px;
}

/* line 750, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-first, .ui-icon-seek-start {
  background-position: -80px -160px;
}

/* line 754, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-stop {
  background-position: -96px -160px;
}

/* line 758, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-eject {
  background-position: -112px -160px;
}

/* line 762, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-volume-off {
  background-position: -128px -160px;
}

/* line 766, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-volume-on {
  background-position: -144px -160px;
}

/* line 770, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-power {
  background-position: 0 -176px;
}

/* line 774, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-signal-diag {
  background-position: -16px -176px;
}

/* line 778, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-signal {
  background-position: -32px -176px;
}

/* line 782, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-0 {
  background-position: -48px -176px;
}

/* line 786, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-1 {
  background-position: -64px -176px;
}

/* line 790, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-2 {
  background-position: -80px -176px;
}

/* line 794, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-3 {
  background-position: -96px -176px;
}

/* line 798, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-plus {
  background-position: 0 -192px;
}

/* line 802, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-minus {
  background-position: -16px -192px;
}

/* line 806, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-close {
  background-position: -32px -192px;
}

/* line 810, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-e {
  background-position: -48px -192px;
}

/* line 814, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-s {
  background-position: -64px -192px;
}

/* line 818, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-w {
  background-position: -80px -192px;
}

/* line 822, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-n {
  background-position: -96px -192px;
}

/* line 826, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-e {
  background-position: -112px -192px;
}

/* line 830, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-s {
  background-position: -128px -192px;
}

/* line 834, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-w {
  background-position: -144px -192px;
}

/* line 838, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-n {
  background-position: -160px -192px;
}

/* line 842, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-zoomin {
  background-position: -176px -192px;
}

/* line 846, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-zoomout {
  background-position: -192px -192px;
}

/* line 850, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-check {
  background-position: -208px -192px;
}

/* line 854, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circlesmall-plus {
  background-position: 0 -208px;
}

/* line 858, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circlesmall-minus {
  background-position: -16px -208px;
}

/* line 862, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circlesmall-close {
  background-position: -32px -208px;
}

/* line 866, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-squaresmall-plus {
  background-position: -48px -208px;
}

/* line 870, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-squaresmall-minus {
  background-position: -64px -208px;
}

/* line 874, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-squaresmall-close {
  background-position: -80px -208px;
}

/* line 878, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-dotted-vertical {
  background-position: 0 -224px;
}

/* line 882, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-dotted-horizontal {
  background-position: -16px -224px;
}

/* line 886, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-solid-vertical {
  background-position: -32px -224px;
}

/* line 890, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-solid-horizontal {
  background-position: -48px -224px;
}

/* line 894, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-gripsmall-diagonal-se {
  background-position: -64px -224px;
}

/* line 898, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-diagonal-se {
  background-position: -80px -224px;
}

/* line 902, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-overlay, .ui-widget-shadow {
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_flat_0_aaaaaa_40x100.png) 50% 50% repeat-x #aaa;
  opacity: .3;
  filter: Alpha(Opacity=30);
}

/* line 908, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-left, .ui-corner-tl, .ui-corner-top {
  -moz-border-radius-topleft: 5px;
  -webkit-border-top-left-radius: 5px;
  -khtml-border-top-left-radius: 5px;
  border-top-left-radius: 5px;
}

/* line 915, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-right, .ui-corner-top, .ui-corner-tr {
  -moz-border-radius-topright: 5px;
  -webkit-border-top-right-radius: 5px;
  -khtml-border-top-right-radius: 5px;
  border-top-right-radius: 5px;
}

/* line 922, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-bl, .ui-corner-bottom, .ui-corner-left {
  -moz-border-radius-bottomleft: 5px;
  -webkit-border-bottom-left-radius: 5px;
  -khtml-border-bottom-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

/* line 929, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-bottom, .ui-corner-br, .ui-corner-right {
  -moz-border-radius-bottomright: 5px;
  -webkit-border-bottom-right-radius: 5px;
  -khtml-border-bottom-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

/* line 936, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-shadow {
  margin: -8px 0 0 -8px;
  padding: 8px;
  -moz-border-radius: 8px;
  -khtml-border-radius: 8px;
  -webkit-border-radius: 8px;
  border-radius: 8px;
}

/* line 945, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker {
  width: 17em;
  padding: .2em .2em 0;
  display: none;
}

/* line 951, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-header {
  position: relative;
  padding: 0.2em 0;
}

/* line 956, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next, .ui-datepicker .ui-datepicker-prev {
  position: absolute;
  top: 2px;
  width: 1.8em;
  height: 1.8em;
}

/* line 963, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next-hover, .ui-datepicker .ui-datepicker-prev-hover {
  top: 1px;
}

/* line 967, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-prev {
  left: 2px;
}

/* line 971, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next {
  right: 2px;
}

/* line 975, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-prev-hover {
  left: 1px;
}

/* line 979, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next-hover {
  right: 1px;
}

/* line 983, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next span, .ui-datepicker .ui-datepicker-prev span {
  display: block;
  position: absolute;
  left: 50%;
  margin-left: -8px;
  top: 50%;
  margin-top: -8px;
}

/* line 992, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-title {
  margin: 0 2.3em;
  line-height: 1.8em;
  text-align: center;
}

/* line 998, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-title select {
  font-size: 1em;
  margin: 1px 0;
}

/* line 1003, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker select.ui-datepicker-month-year {
  width: 100%;
}

/* line 1007, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker select.ui-datepicker-month, .ui-datepicker select.ui-datepicker-year {
  width: 49%;
}

/* line 1011, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker table {
  width: 100%;
  font-size: .9em;
  border-collapse: collapse;
  margin: 0 0 0.4em;
}

/* line 1018, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker th {
  padding: .7em .3em;
  text-align: center;
  font-weight: 700;
  border: 0;
}

/* line 1025, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker td {
  border: 0;
  padding: 1px;
}

/* line 1030, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker td a, .ui-datepicker td span {
  display: block;
  padding: .2em;
  text-align: right;
  text-decoration: none;
}

/* line 1037, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-buttonpane {
  background-image: none;
  margin: .7em 0 0;
  padding: 0 .2em;
  border-left: 0;
  border-right: 0;
  border-bottom: 0;
}

/* line 1046, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-buttonpane button {
  float: right;
  margin: .5em .2em .4em;
  cursor: pointer;
  padding: .2em .6em .3em;
  width: auto;
  overflow: visible;
}

/* line 1055, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current, .ui-datepicker-multi .ui-datepicker-group, .ui-datepicker-rtl .ui-datepicker-buttonpane button {
  float: left;
}

/* line 1059, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker.ui-datepicker-multi {
  width: auto;
}

/* line 1063, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi .ui-datepicker-group table {
  width: 95%;
  margin: 0 auto 0.4em;
}

/* line 1068, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi-2 .ui-datepicker-group {
  width: 50%;
}

/* line 1072, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi-3 .ui-datepicker-group {
  width: 33.3%;
}

/* line 1076, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi-4 .ui-datepicker-group {
  width: 25%;
}

/* line 1080, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header, .ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
  border-left-width: 0;
}

/* line 1084, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi .ui-datepicker-buttonpane {
  clear: left;
}

/* line 1088, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-row-break {
  clear: both;
  width: 100%;
  font-size: 0;
}

/* line 1094, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl {
  direction: rtl;
}

/* line 1098, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-prev {
  right: 2px;
  left: auto;
}

/* line 1103, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-next {
  left: 2px;
  right: auto;
}

/* line 1108, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-prev:hover {
  right: 1px;
  left: auto;
}

/* line 1113, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-next:hover {
  left: 1px;
  right: auto;
}

/* line 1118, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-buttonpane {
  clear: right;
}

/* line 1122, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current, .ui-datepicker-rtl .ui-datepicker-group {
  float: right;
}

/* line 1126, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header, .ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {
  border-right-width: 0;
  border-left-width: 1px;
}

/* line 1131, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-cover {
  display: none;
  display: block;
  position: absolute;
  z-index: -1;
  filter: mask();
  top: -4px;
  left: -4px;
  width: 200px;
  height: 200px;
}

@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (-webkit-min-device-pixel-ratio: 2) {
  /* line 1144, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
  div.ui-datepicker {
    font-size: 20px;
  }
}
/* line 1, ../../../../sass/extra/popup_landscape.scss */
.popup_landscape_wrapper {
  display: none;
  color: white;
  background: rgba(0, 0, 0, 0.95);
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1004;
  top: 0;
}
/* line 11, ../../../../sass/extra/popup_landscape.scss */
.popup_landscape_wrapper .center_xy {
  width: 100%;
}
/* line 13, ../../../../sass/extra/popup_landscape.scss */
.popup_landscape_wrapper .center_xy .popup_landscape_icon {
  width: 100px;
  margin: auto;
  position: relative;
  height: 115px;
  text-align: center;
}
/* line 19, ../../../../sass/extra/popup_landscape.scss */
.popup_landscape_wrapper .center_xy .popup_landscape_icon .fal {
  position: absolute;
}
/* line 21, ../../../../sass/extra/popup_landscape.scss */
.popup_landscape_wrapper .center_xy .popup_landscape_icon .fal.fa-mobile {
  bottom: 0;
  top: 0;
  font-size: 80px;
}
/* line 25, ../../../../sass/extra/popup_landscape.scss */
.popup_landscape_wrapper .center_xy .popup_landscape_icon .fal.fa-mobile.fa-rotate-270 {
  opacity: 0.5;
  margin-left: -40px;
  margin-top: 30px;
}
/* line 32, ../../../../sass/extra/popup_landscape.scss */
.popup_landscape_wrapper .center_xy .popup_landscape_icon .fas.fa-share {
  position: absolute;
  font-size: 40px;
  left: 5px;
}
/* line 38, ../../../../sass/extra/popup_landscape.scss */
.popup_landscape_wrapper .center_xy .popup_landscape_text {
  font-size: 18px;
  text-align: center;
  width: 80%;
  margin: auto;
  text-align: center;
}
/* line 46, ../../../../sass/extra/popup_landscape.scss */
.popup_landscape_wrapper .button_landscape_exit {
  font-size: 12px;
  text-align: left;
  bottom: 20px;
  position: absolute;
  width: 190px;
  left: 5%;
}

@media (orientation: landscape) {
  /* line 57, ../../../../sass/extra/popup_landscape.scss */
  .popup_landscape_wrapper.enable {
    display: block;
  }
}
/* line 3, ../../../../sass/booking/_booking_engine_7.scss */
.booking_widget {
  position: relative;
}

/* line 7, ../../../../sass/booking/_booking_engine_7.scss */
#full-booking-engine-html-7 {
  width: 275px;
  display: block;
}
/* line 11, ../../../../sass/booking/_booking_engine_7.scss */
#full-booking-engine-html-7 form.booking_form {
  padding: 0 !important;
}
/* line 15, ../../../../sass/booking/_booking_engine_7.scss */
#full-booking-engine-html-7 .booking_form_title {
  background: #383838;
}

/* line 21, ../../../../sass/booking/_booking_engine_7.scss */
.destination_wrapper {
  border-bottom: 1px solid lightgray;
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat 92% 70% !important;
}
/* line 25, ../../../../sass/booking/_booking_engine_7.scss */
.destination_wrapper label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
  display: inline-block;
  margin: 10px 0 0 10px;
}

/* line 34, ../../../../sass/booking/_booking_engine_7.scss */
.destination_wrapper input {
  border: none;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 25px;
  font-weight: 600;
  background: white;
}
/* line 41, ../../../../sass/booking/_booking_engine_7.scss */
.destination_wrapper input::-webkit-input-placeholder {
  color: #585d63;
}
/* line 45, ../../../../sass/booking/_booking_engine_7.scss */
.destination_wrapper input:-moz-placeholder {
  /* Firefox 18- */
  color: #585d63;
}
/* line 50, ../../../../sass/booking/_booking_engine_7.scss */
.destination_wrapper input::-moz-placeholder {
  /* Firefox 19+ */
  color: #585d63;
}
/* line 55, ../../../../sass/booking/_booking_engine_7.scss */
.destination_wrapper input:-ms-input-placeholder {
  color: #585d63;
}

/* line 60, ../../../../sass/booking/_booking_engine_7.scss */
.entry_date_wrapper {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 66.6%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}

/* line 75, ../../../../sass/booking/_booking_engine_7.scss */
.departure_date_wrapper, .half_size {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 50%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}
/* line 89, ../../../../sass/booking/_booking_engine_7.scss */
.departure_date_wrapper .date_day, .half_size .date_day {
  font-size: 21px !important;
}

/* line 94, ../../../../sass/booking/_booking_engine_7.scss */
.nights_number_wrapper {
  float: right;
  height: 70px;
  border-bottom: 1px solid lightgrey;
  box-sizing: border-box;
  width: 33.3%;
  padding: 5px 10px;
}

/* line 103, ../../../../sass/booking/_booking_engine_7.scss */
.num_nights_label, .entry_date_label, .children_label, .rooms_label, .adults_label, .promocode_label, .departure_date_label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/* line 109, ../../../../sass/booking/_booking_engine_7.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 30%;
}

/*===== Entry date =====*/
/* line 116, ../../../../sass/booking/_booking_engine_7.scss */
.date_box.entry_date, .date_box.departure_date {
  font-size: 20px;
  background: url(/static_1/images/booking_5/calendar.png) no-repeat center right;
  margin-top: 9px;
  background-size: 29px;
}
/* line 122, ../../../../sass/booking/_booking_engine_7.scss */
.date_box.entry_date .date_day, .date_box.departure_date .date_day {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 25px;
  font-weight: 600;
}

/*====== Booking button ====*/
/* line 131, ../../../../sass/booking/_booking_engine_7.scss */
button.submit_button {
  width: 100%;
  border: 0;
  border-radius: 0;
  background: #ffd600;
  height: 55px;
  text-transform: uppercase;
  font-weight: bolder;
  font-size: 21px;
  color: #565656;
  cursor: pointer;
}

/*====== Rooms section ======*/
/* line 145, ../../../../sass/booking/_booking_engine_7.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 33.3%;
  padding: 7px 10px 5px;
  box-sizing: border-box;
}
/* line 152, ../../../../sass/booking/_booking_engine_7.scss */
.rooms_number_wrapper .room_list_wrapper li.room {
  float: right;
  width: 69%;
}
/* line 158, ../../../../sass/booking/_booking_engine_7.scss */
.rooms_number_wrapper .adults_selector {
  height: 70px;
  float: left;
  border-right: 1px solid gray;
  width: 46.5%;
}

/* line 166, ../../../../sass/booking/_booking_engine_7.scss */
.room_list_wrapper {
  display: table;
  float: right;
  width: 66.6%;
}
/* line 171, ../../../../sass/booking/_booking_engine_7.scss */
.room_list_wrapper .room_title {
  display: none;
}
/* line 175, ../../../../sass/booking/_booking_engine_7.scss */
.room_list_wrapper .adults_selector {
  border-right: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
}
/* line 180, ../../../../sass/booking/_booking_engine_7.scss */
.room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
  width: 50%;
  display: inline-block;
  height: 70px;
  box-sizing: border-box;
  padding: 7px 10px 5px;
}
/* line 188, ../../../../sass/booking/_booking_engine_7.scss */
.room_list_wrapper .children_selector {
  width: 50% !important;
  padding: 7px 10px 5px;
  float: right;
}

/* line 195, ../../../../sass/booking/_booking_engine_7.scss */
.room_list .room {
  height: 70px;
}

/* line 199, ../../../../sass/booking/_booking_engine_7.scss */
.room_list .room.room3, .room_list .room.room2 {
  border-top: 1px solid lightgrey;
}

/*==== Promocode section  ====*/
/* line 204, ../../../../sass/booking/_booking_engine_7.scss */
.promocode_wrapper {
  display: table;
  width: 100%;
  padding: 10px 10px 5px;
  border-top: 1px solid lightgrey;
  box-sizing: border-box;
}

/* line 212, ../../../../sass/booking/_booking_engine_7.scss */
label.promocode_label {
  display: block;
}

/* line 216, ../../../../sass/booking/_booking_engine_7.scss */
input.promocode_input {
  border: 0;
  width: 100%;
  display: block;
  height: 30px;
  font-size: 13px;
  font-weight: lighter;
  margin-top: 2px;
  font-family: sans-serif;
}

/*====== Selectric ======*/
/* line 228, ../../../../sass/booking/_booking_engine_7.scss */
.selectricItems {
  overflow: auto !important;
  width: 92px !important;
  margin-left: -11px;
  margin-top: 2px;
}
/* line 234, ../../../../sass/booking/_booking_engine_7.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 240, ../../../../sass/booking/_booking_engine_7.scss */
.selectricItems li {
  font-weight: 500;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAADAQMAAABs5if8AAAABlBMVEXOzs7y8vKNS741AAAADklEQVQI12M4wPSAWQAABoQBtgsaY5kAAAAASUVORK5CYII=);
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  line-height: 29px !important;
  font-size: 18px !important;
  font-family: 'Source Sans Pro', sans-serif;
}
/* line 250, ../../../../sass/booking/_booking_engine_7.scss */
.selectricItems li:hover {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 257, ../../../../sass/booking/_booking_engine_7.scss */
.selectricItems ul {
  z-index: 40;
}
/* line 261, ../../../../sass/booking/_booking_engine_7.scss */
.selectricItems .room {
  padding-top: 17px;
}

/* line 267, ../../../../sass/booking/_booking_engine_7.scss */
#full-booking-engine-html-7 .selectric .button {
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 23px;
  margin-left: 0;
  margin-top: 8px;
  text-indent: 999px;
  height: 22px;
  font-weight: 600;
}

/* line 280, ../../../../sass/booking/_booking_engine_7.scss */
.selectricWrapper .selectric .label {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 28px;
  margin-left: 0;
  font-weight: 600;
}

/* line 288, ../../../../sass/booking/_booking_engine_7.scss */
.selectricWrapper .selectric {
  margin-top: 3px;
}

/* line 292, ../../../../sass/booking/_booking_engine_7.scss */
.selectricWrapper {
  width: 78px !important;
}

/*======= Phone web support =======*/
/* line 297, ../../../../sass/booking/_booking_engine_7.scss */
.wrapper-new-web-support.booking_form_title {
  border-radius: 0;
  padding: 5px 10px;
  font-family: 'Montserrat', sans-serif;
  font-size: 11px !important;
  margin-top: 11px;
}

/* line 305, ../../../../sass/booking/_booking_engine_7.scss */
.wrapper-new-web-support .web_support_number, .web_support_label_1 {
  font-size: 11px !important;
  margin-left: 5px;
}

/* line 310, ../../../../sass/booking/_booking_engine_7.scss */
img.phone_support_image {
  float: left;
  width: 28px;
  margin-top: 1px;
  margin-right: 12px;
}

/*======= Header title ======*/
/* line 318, ../../../../sass/booking/_booking_engine_7.scss */
.booking_title_1, .booking_title_2, .best_price {
  display: none;
}

/* line 322, ../../../../sass/booking/_booking_engine_7.scss */
.promocode_header {
  font-family: 'Montserrat', sans-serif;
  padding: 10px;
  display: table;
  width: 100%;
  box-sizing: border-box;
  cursor: pointer;
}
/* line 330, ../../../../sass/booking/_booking_engine_7.scss */
.promocode_header:hover {
  opacity: 0.8;
}
/* line 334, ../../../../sass/booking/_booking_engine_7.scss */
.promocode_header img.booking_header_discount {
  float: left;
  margin-right: 15px;
}
/* line 339, ../../../../sass/booking/_booking_engine_7.scss */
.promocode_header p.first_offer_name {
  font-size: 12px;
  color: #8096c7;
  margin-top: 9px;
}
/* line 345, ../../../../sass/booking/_booking_engine_7.scss */
.promocode_header p.second_offer_name {
  font-size: 10px;
  margin-top: 3px;
  color: gray;
}

/* line 352, ../../../../sass/booking/_booking_engine_7.scss */
.ui-datepicker {
  width: 283px;
}
/* line 355, ../../../../sass/booking/_booking_engine_7.scss */
.ui-datepicker .ui-widget-header {
  background: none !important;
  border: 0;
}
/* line 359, ../../../../sass/booking/_booking_engine_7.scss */
.ui-datepicker .ui-widget-header .ui-datepicker-title {
  color: #646464 !important;
  font-family: Verdana, Arial, sans-serif;
  font-weight: 300;
}

/* line 368, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-datepicker th {
  font-weight: 300;
  font-size: 14px;
}
/* line 373, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-state-default, body .ui-widget-content .ui-state-default, body .ui-widget-header .ui-state-default {
  background: transparent !important;
  border: 0 !important;
  color: #646464 !important;
  font-weight: 400;
  font-family: Circular, "Helvetica Neue", Helvetica, Arial, sans-serif;
  text-align: center;
  font-size: 13px;
}
/* line 383, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-widget-content .ui-state-hover {
  border: 0;
  background: #102F57 !important;
  color: white !important;
}
/* line 389, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-widget-content {
  border: 0;
  border-radius: 0;
  padding-bottom: 40px;
}
/* line 395, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-state-default {
  padding: 8px;
}
/* line 399, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-datepicker-start_date {
  opacity: 1 !important;
}
/* line 402, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-datepicker-start_date .ui-state-default {
  border: 0;
  background: #102F57 !important;
  color: white !important;
}
/* line 409, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-datepicker-highlighted .ui-state-default {
  border: 0;
  background: rgba(40, 96, 144, 0.25) !important;
}
/* line 414, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-widget-content .ui-state-active {
  border: 0;
  background: #102F57 !important;
  color: white !important;
}
/* line 420, ../../../../sass/booking/_booking_engine_7.scss */
body span.ui-icon.ui-icon-circle-triangle-e, body span.ui-icon.ui-icon-circle-triangle-w {
  /* background: url(/static_1/images/mobile_img/renovation/flecha_der.png) no-repeat center;*/
  background: none;
  text-indent: 0;
  color: transparent;
  font-size: 0;
}
/* line 426, ../../../../sass/booking/_booking_engine_7.scss */
body span.ui-icon.ui-icon-circle-triangle-e:before, body span.ui-icon.ui-icon-circle-triangle-w:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  content: '\f105';
  font-family: "FontAwesome", sans-serif;
  font-size: 20px;
  color: black;
}
/* line 436, ../../../../sass/booking/_booking_engine_7.scss */
body span.ui-icon.ui-icon-circle-triangle-w:before {
  content: '\f104';
}
/* line 441, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-datepicker .ui-datepicker-next, body .ui-datepicker .ui-datepicker-prev {
  background: none;
}
/* line 445, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-datepicker-next.ui-state-hover, body .ui-datepicker-prev.ui-datepicker-prev-hover {
  border: 1px solid #636363;
}

/* line 452, ../../../../sass/booking/_booking_engine_7.scss */
.datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-e:before, .datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-w:before {
  display: none;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

/* line 463, ../../../../sass/booking/_booking_engine_7.scss */
.start_end_date_wrapper {
  width: 290px;
  height: 47px;
  display: inline-block;
  background: white url(/img/ohtel/calendar_ico.png?v=1) no-repeat 4px center;
  font-weight: 300;
  font-size: 14px;
  padding: 15px 33px 12px;
  box-sizing: border-box;
  cursor: pointer;
  position: relative;
}

/* line 476, ../../../../sass/booking/_booking_engine_7.scss */
.dates_selector_personalized {
  display: inline-block;
  float: left;
  margin-right: 5px;
  position: relative;
}

/* line 483, ../../../../sass/booking/_booking_engine_7.scss */
.nights_number_wrapper_personalized {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 55px;
  background: #F3D132;
  line-height: 16px;
}
/* line 492, ../../../../sass/booking/_booking_engine_7.scss */
.nights_number_wrapper_personalized span.days_number_datepicker {
  display: block;
  text-align: center;
  color: white;
  font-size: 14px;
  font-weight: 500;
  margin-top: 8px;
}
/* line 501, ../../../../sass/booking/_booking_engine_7.scss */
.nights_number_wrapper_personalized span.night_label {
  color: white;
  font-size: 12px;
  text-align: center;
  display: block;
}
/* line 508, ../../../../sass/booking/_booking_engine_7.scss */
.nights_number_wrapper_personalized .ui-datepicker td {
  border: 0;
  padding: 1px 0;
}

/* line 514, ../../../../sass/booking/_booking_engine_7.scss */
.stay_selection {
  display: none !important;
}

/* line 518, ../../../../sass/booking/_booking_engine_7.scss */
label.dates_selector_label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/* line 524, ../../../../sass/booking/_booking_engine_7.scss */
.start_date_datepicker, .departure_datepicker {
  position: absolute;
  top: 100%;
}

/* line 529, ../../../../sass/booking/_booking_engine_7.scss */
.close_calendar {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40px;
  height: 40px;
  cursor: pointer;
  background: #535454;
}
/* line 538, ../../../../sass/booking/_booking_engine_7.scss */
.close_calendar:before {
  content: "\f00d";
  font-family: FontAwesome;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: white;
}

/* line 546, ../../../../sass/booking/_booking_engine_7.scss */
.entry_label_calendar, .departure_label_calendar {
  position: absolute;
  bottom: 0;
  font-family: 'Roboto', sans-serif;
  left: 0;
  font-weight: 300;
  color: white;
  font-size: 13px;
  background: #315390;
  width: 250px;
  line-height: 40px;
  padding-left: 18px;
  box-sizing: border-box;
}

/* line 2, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 {
  border-radius: 0;
}
/* line 4, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-skin {
  border-radius: 0;
}
/* line 7, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide > * {
  padding: 0 !important;
  background-color: transparent;
}
/* line 11, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide .calendar_popup_wrapper {
  background-color: transparent;
}
/* line 15, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .full_screen_engine .container_popup_booking {
  background-color: transparent !important;
  padding: 24px !important;
}
/* line 20, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  color: gray;
}
/* line 23, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading {
  width: 50px;
  height: 50px;
  margin: auto;
  position: absolute;
  left: 0;
  right: 0;
  top: 100px;
  bottom: 0;
}
/* line 10, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:before {
  content: '';
  width: 50px;
  height: 5px;
  background: #000;
  opacity: 0.3;
  position: absolute;
  top: 59px;
  left: 0;
  border-radius: 50%;
  animation: box_shadow .5s linear infinite;
}
/* line 22, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:after {
  content: '';
  width: 50px;
  height: 50px;
  background: white;
  animation: box_animate .5s linear infinite;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 3px;
}
@keyframes box_animate {
  17% {
    border-bottom-right-radius: 3px;
  }
  25% {
    transform: translateY(9px) rotate(22.5deg);
  }
  50% {
    transform: translateY(18px) scale(1, 0.9) rotate(45deg);
    border-bottom-right-radius: 40px;
  }
  75% {
    transform: translateY(9px) rotate(67.5deg);
  }
  100% {
    transform: translateY(0) rotate(90deg);
  }
}
@keyframes box_shadow {
  0%, 100% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.2, 1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader {
  padding: 60px 0 70px;
}
/* line 3, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot {
  width: 24px;
  height: 24px;
  margin: 0 3px;
  background: #000;
  border-radius: 100%;
  display: inline-block;
  animation: slide 1s infinite;
}
/* line 11, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(1) {
  animation-delay: 0.1s;
}
/* line 14, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(2) {
  animation-delay: 0.2s;
}
/* line 17, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(3) {
  animation-delay: 0.3s;
}
/* line 20, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(4) {
  animation-delay: 0.4s;
}
/* line 23, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(5) {
  animation-delay: 0.5s;
}
@-moz-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-webkit-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-o-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube {
  margin: 20px auto;
  width: 40px;
  height: 40px;
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}
/* line 7, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube {
  float: left;
  width: 50%;
  height: 50%;
  position: relative;
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}
/* line 15, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  -webkit-animation: sk-foldCubeAngle 2.4s infinite linear both;
  animation: sk-foldCubeAngle 2.4s infinite linear both;
  -webkit-transform-origin: 100% 100%;
  -ms-transform-origin: 100% 100%;
  transform-origin: 100% 100%;
}
/* line 30, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2 {
  -webkit-transform: scale(1.1) rotateZ(90deg);
  transform: scale(1.1) rotateZ(90deg);
}
/* line 34, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3 {
  -webkit-transform: scale(1.1) rotateZ(270deg);
  transform: scale(1.1) rotateZ(270deg);
}
/* line 38, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4 {
  -webkit-transform: scale(1.1) rotateZ(180deg);
  transform: scale(1.1) rotateZ(180deg);
}
/* line 43, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2:before {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
/* line 49, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3:before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
}
/* line 55, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4:before {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
}
@-webkit-keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
@keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
/* line 1, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots {
  padding: 40px 0;
  text-align: center;
  animation: dots-rotate 2s linear infinite;
}
/* line 5, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  content: '';
  animation: dots-margin 2s linear infinite;
}
/* line 9, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots span, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  display: inline-block;
  vertical-align: middle;
  width: 30px;
  height: 30px;
  background-color: white;
  border-radius: 50%;
  margin: 5px;
}
@keyframes dots-margin {
  0% {
    margin: 5px;
  }
  12% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  25% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  50% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  62% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  75% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  100% {
    margin: 5px;
  }
}
@keyframes dots-rotate {
  0% {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  25% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  50% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  75% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/* line 33, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_top_popup_booking {
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: lighter;
  color: gray;
}
/* line 41, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  font-weight: bold;
  text-transform: uppercase;
}
/* line 46, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .container_popup_booking {
  width: 555px;
  display: block;
  padding: 30px 0;
  box-sizing: border-box;
  margin: 7px;
  border: 1px solid #3483b2;
}

/* line 57, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine {
  width: auto !important;
  height: auto !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}
/* line 65, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-outer {
  background: transparent;
}
/* line 70, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine.custom_color_overlay .fancybox-skin {
  background: transparent;
}
/* line 75, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-skin {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}
/* line 84, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-inner {
  overflow: visible;
}
/* line 88, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 101, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  border: 0 !important;
}
/* line 105, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: lighter;
}
/* line 108, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking strong {
  font-weight: bold;
  text-decoration: underline;
  font-size: 14px;
}

/* line 116, ../../../../sass/booking/_booking_popupv2.scss */
.fancy-booking-search_v2 {
  border-radius: 0;
}
/* line 119, ../../../../sass/booking/_booking_popupv2.scss */
.fancy-booking-search_v2 .fancybox-skin {
  border-radius: 0;
}
/* line 123, ../../../../sass/booking/_booking_popupv2.scss */
.fancy-booking-search_v2 .gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}
/* line 129, ../../../../sass/booking/_booking_popupv2.scss */
.fancy-booking-search_v2 .gif_wrapper .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
/* line 142, ../../../../sass/booking/_booking_popupv2.scss */
.fancy-booking-search_v2 .description_top_popup_booking {
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: lighter;
  color: gray;
}
/* line 150, ../../../../sass/booking/_booking_popupv2.scss */
.fancy-booking-search_v2 .description_bottom_popup_booking {
  font-weight: bold;
  text-transform: uppercase;
  color: gray;
}
/* line 156, ../../../../sass/booking/_booking_popupv2.scss */
.fancy-booking-search_v2 .container_popup_booking {
  width: 555px;
  display: block;
  padding: 30px 0;
  box-sizing: border-box;
  margin: 7px;
  border: 1px solid #3483b2;
}

/* line 166, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine {
  width: auto !important;
  height: auto !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}
/* line 174, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-outer {
  background: transparent;
}
/* line 178, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-skin {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}
/* line 187, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-inner {
  overflow: visible;
}
/* line 191, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 204, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  border: 0 !important;
}
/* line 208, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: lighter;
}
/* line 211, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking strong {
  font-weight: bold;
  text-decoration: underline;
  font-size: 14px;
}

/* line 1, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.booking_widget {
  position: relative;
}

/* line 5, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full-booking-engine-html-7 {
  width: 275px;
  display: block;
}
/* line 9, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full-booking-engine-html-7 form.booking_form {
  padding: 0 !important;
}
/* line 13, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full-booking-engine-html-7 .booking_form_title {
  background: #383838;
}

/* line 19, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper {
  border-bottom: 1px solid lightgray;
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat 92% 70% !important;
}
/* line 23, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
  display: inline-block;
  margin: 10px 0 0 10px;
}

/* line 32, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper input {
  border: none;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 25px;
  font-weight: 600;
  background: white;
}
/* line 39, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper input::-webkit-input-placeholder {
  color: #585d63;
}
/* line 43, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper input:-moz-placeholder {
  /* Firefox 18- */
  color: #585d63;
}
/* line 48, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper input::-moz-placeholder {
  /* Firefox 19+ */
  color: #585d63;
}
/* line 53, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper input:-ms-input-placeholder {
  color: #585d63;
}

/* line 58, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.nights_number_wrapper {
  float: right;
  height: 70px;
  border-bottom: 1px solid lightgrey;
  box-sizing: border-box;
  width: 33.3%;
  padding: 5px 10px;
}

/* line 67, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.num_nights_label, .entry_date_label, .children_label, .rooms_label, .adults_label, .promocode_label, .departure_date_label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/* line 73, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 30%;
}

/*===== Entry date =====*/
/* line 80, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.date_box.entry_date, .date_box.departure_date {
  font-size: 20px;
  background: url(/static_1/images/booking_5/calendar.png) no-repeat center right;
  margin-top: 9px;
  background-size: 29px;
}
/* line 86, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.date_box.entry_date .date_day, .date_box.departure_date .date_day {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 25px;
  font-weight: 600;
}

/*====== Booking button ====*/
/* line 95, ../../../../sass/styles_mobile/2/_booking_engine.scss */
button.submit_button {
  width: 100%;
  border: 0;
  border-radius: 0;
  background: #ffd600;
  height: 55px;
  text-transform: uppercase;
  font-weight: bolder;
  font-size: 21px;
  color: #565656;
  cursor: pointer;
}

/*====== Rooms section ======*/
/* line 109, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 33.3%;
  padding: 7px 10px 5px;
  box-sizing: border-box;
}
/* line 116, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.rooms_number_wrapper .room_list_wrapper li.room {
  float: right;
  width: 69%;
}
/* line 122, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.rooms_number_wrapper .adults_selector {
  height: 70px;
  float: left;
  border-right: 1px solid gray;
  width: 46.5%;
}

/* line 130, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.room_list_wrapper {
  display: table;
  float: right;
  width: 66.6%;
}
/* line 135, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.room_list_wrapper .room_title {
  display: none;
}
/* line 139, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.room_list_wrapper .adults_selector {
  border-right: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
}
/* line 144, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
  width: 50%;
  display: inline-block;
  height: 70px;
  box-sizing: border-box;
  padding: 7px 10px 5px;
}
/* line 152, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.room_list_wrapper .children_selector {
  width: 50% !important;
  padding: 7px 10px 5px;
  float: right;
}

/* line 159, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.room_list .room {
  height: 70px;
}

/* line 163, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.room_list .room.room3, .room_list .room.room2 {
  border-top: 1px solid lightgrey;
}

/*==== Promocode section  ====*/
/* line 168, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.promocode_wrapper {
  display: table;
  width: 100%;
  padding: 10px 10px 5px;
  border-top: 1px solid lightgrey;
  box-sizing: border-box;
}

/* line 176, ../../../../sass/styles_mobile/2/_booking_engine.scss */
label.promocode_label {
  display: block;
}

/* line 180, ../../../../sass/styles_mobile/2/_booking_engine.scss */
input.promocode_input {
  border: 0;
  width: 100%;
  display: block;
  height: 30px;
  font-size: 13px;
  font-weight: lighter;
  margin-top: 2px;
  font-family: sans-serif;
}

/*====== Selectric ======*/
/* line 192, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.selectricItems {
  overflow: auto !important;
  width: 92px !important;
  margin-left: -11px;
  margin-top: 2px;
}
/* line 198, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 204, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.selectricItems li {
  font-weight: 500;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAADAQMAAABs5if8AAAABlBMVEXOzs7y8vKNS741AAAADklEQVQI12M4wPSAWQAABoQBtgsaY5kAAAAASUVORK5CYII=);
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  line-height: 29px !important;
  font-size: 18px !important;
  font-family: 'Source Sans Pro', sans-serif;
}
/* line 214, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.selectricItems li:hover {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 221, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.selectricItems ul {
  z-index: 40;
}
/* line 225, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.selectricItems .room {
  padding-top: 17px;
}

/* line 231, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full-booking-engine-html-7 .selectric .button {
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 23px;
  margin-left: 0;
  margin-top: 8px;
  text-indent: 999px;
  height: 22px;
  font-weight: 600;
}

/* line 244, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.selectricWrapper .selectric .label {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 28px;
  margin-left: 0;
  font-weight: 600;
}

/* line 252, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.selectricWrapper .selectric {
  margin-top: 3px;
}

/* line 256, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.selectricWrapper {
  width: 78px !important;
}

/*======= Phone web support =======*/
/* line 261, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.wrapper-new-web-support.booking_form_title {
  border-radius: 0;
  padding: 5px 10px;
  font-family: 'Montserrat', sans-serif;
  font-size: 11px !important;
  margin-top: 11px;
}

/* line 269, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.wrapper-new-web-support .web_support_number, .web_support_label_1 {
  font-size: 11px !important;
}

/* line 273, ../../../../sass/styles_mobile/2/_booking_engine.scss */
img.phone_support_image {
  float: left;
  width: 28px;
  margin-top: 1px;
  margin-right: 12px;
}

/*======= Header title ======*/
/* line 281, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.booking_title_1, .booking_title_2, .best_price {
  display: none;
}

/* line 285, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.promocode_header {
  font-family: 'Montserrat', sans-serif;
  padding: 10px;
  display: table;
  width: 100%;
  box-sizing: border-box;
  cursor: pointer;
}
/* line 293, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.promocode_header:hover {
  opacity: 0.8;
}
/* line 297, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.promocode_header img.booking_header_discount {
  float: left;
  margin-right: 15px;
}
/* line 302, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.promocode_header p.first_offer_name {
  font-size: 12px;
  color: #8096c7;
  margin-top: 9px;
}
/* line 308, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.promocode_header p.second_offer_name {
  font-size: 10px;
  margin-top: 3px;
  color: gray;
}

/* line 315, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.ui-datepicker {
  width: 283px;
}
/* line 318, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.ui-datepicker .ui-widget-header {
  background: none !important;
  border: 0;
}
/* line 322, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.ui-datepicker .ui-widget-header .ui-datepicker-title {
  color: #646464 !important;
  font-family: Verdana, Arial, sans-serif;
  font-weight: 300;
}

/* line 331, ../../../../sass/styles_mobile/2/_booking_engine.scss */
body .ui-datepicker th {
  font-weight: 300;
  font-size: 14px;
}
/* line 336, ../../../../sass/styles_mobile/2/_booking_engine.scss */
body .ui-state-default, body .ui-widget-content .ui-state-default, body .ui-widget-header .ui-state-default {
  background: transparent !important;
  border: 0 !important;
  color: #646464 !important;
  font-weight: 400;
  font-family: Circular, "Helvetica Neue", Helvetica, Arial, sans-serif;
  text-align: center;
  font-size: 13px;
}
/* line 346, ../../../../sass/styles_mobile/2/_booking_engine.scss */
body .ui-widget-content .ui-state-hover {
  border: 0;
  background: #102F57 !important;
  color: white !important;
}
/* line 352, ../../../../sass/styles_mobile/2/_booking_engine.scss */
body .ui-widget-content {
  border: 0;
  border-radius: 0;
  padding-bottom: 40px;
}
/* line 358, ../../../../sass/styles_mobile/2/_booking_engine.scss */
body .ui-state-default {
  padding: 8px !important;
}
/* line 362, ../../../../sass/styles_mobile/2/_booking_engine.scss */
body .ui-datepicker-start_date {
  opacity: 1 !important;
}
/* line 365, ../../../../sass/styles_mobile/2/_booking_engine.scss */
body .ui-datepicker-start_date .ui-state-default {
  border: 0;
  background: #102F57 !important;
  color: white !important;
}
/* line 372, ../../../../sass/styles_mobile/2/_booking_engine.scss */
body .ui-datepicker-highlighted .ui-state-default {
  border: 0;
  background: rgba(40, 96, 144, 0.25) !important;
}
/* line 377, ../../../../sass/styles_mobile/2/_booking_engine.scss */
body .ui-widget-content .ui-state-active {
  border: 0;
  background: #102F57 !important;
  color: white !important;
}
/* line 383, ../../../../sass/styles_mobile/2/_booking_engine.scss */
body span.ui-icon.ui-icon-circle-triangle-e, body span.ui-icon.ui-icon-circle-triangle-w {
  background: url(/static_1/images/mobile_img/renovation/flecha_der.png) no-repeat center;
}
/* line 387, ../../../../sass/styles_mobile/2/_booking_engine.scss */
body span.ui-icon.ui-icon-circle-triangle-w {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
/* line 395, ../../../../sass/styles_mobile/2/_booking_engine.scss */
body .ui-datepicker .ui-datepicker-next, body .ui-datepicker .ui-datepicker-prev {
  background: none;
}
/* line 399, ../../../../sass/styles_mobile/2/_booking_engine.scss */
body .ui-datepicker-next.ui-state-hover, body .ui-datepicker-prev.ui-datepicker-prev-hover {
  border: 1px solid #636363;
}

/* line 404, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.start_end_date_wrapper {
  width: 290px;
  height: 47px;
  display: inline-block;
  background: white url(/img/ohtel/calendar_ico.png) no-repeat 4px center;
  font-weight: 300;
  font-size: 14px;
  padding: 15px 33px 12px;
  box-sizing: border-box;
  cursor: pointer;
  position: relative;
}

/* line 417, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.dates_selector_personalized {
  display: inline-block;
  float: left;
  margin-right: 5px;
  position: relative;
}

/* line 424, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.nights_number_wrapper_personalized {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 55px;
  background: #F3D132;
  line-height: 16px;
}
/* line 433, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.nights_number_wrapper_personalized span.days_number_datepicker {
  display: block;
  text-align: center;
  color: white;
  font-size: 14px;
  font-weight: 500;
  margin-top: 8px;
}
/* line 442, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.nights_number_wrapper_personalized span.night_label {
  color: white;
  font-size: 12px;
  text-align: center;
  display: block;
}
/* line 449, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.nights_number_wrapper_personalized .ui-datepicker td {
  border: 0;
  padding: 1px 0;
}

/* line 455, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.stay_selection {
  display: none !important;
}

/* line 459, ../../../../sass/styles_mobile/2/_booking_engine.scss */
label.dates_selector_label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/* line 465, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.start_date_datepicker, .departure_datepicker {
  position: absolute;
  top: 100%;
}

/* line 470, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.close_calendar {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40px;
  height: 40px;
  cursor: pointer;
  background: #535454;
}
/* line 479, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.close_calendar:before {
  content: "\f00d";
  font-family: "FontAwesome", sans-serif;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: white;
}

/* line 491, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.entry_label_calendar, .departure_label_calendar {
  position: absolute;
  bottom: 0;
  font-family: 'Roboto', sans-serif;
  left: 0;
  font-weight: 300;
  color: white;
  font-size: 13px;
  background: #315390;
  width: 250px;
  line-height: 40px;
  padding-left: 18px;
  box-sizing: border-box;
}

/* line 506, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking {
  padding: 40px 10px 10px;
  width: 100%;
  max-width: 100%;
  font-family: 'Montserrat', sans-serif;
  /*======== Booking Widget =======*/
}
/* line 511, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .menu_full_screen {
  display: none;
}
/* line 517, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .selectricItems {
  overflow: auto !important;
}
/* line 520, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking select {
  appearance: none;
  -moz-appearance: none;
  /* Firefox */
  -webkit-appearance: none;
  /* Safari and Chrome */
  background: transparent;
  border-width: 0;
}
/* line 528, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-mobile {
  width: 100%;
  display: table;
  margin: auto !important;
  position: relative;
  padding-bottom: 30px;
}
/* line 535, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-mobile .promocode_header {
  display: none;
}
/* line 540, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-mobile form.booking_form {
  background: transparent;
  position: relative;
}
/* line 544, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .booking_form_title {
  background: transparent;
}
/* line 547, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .booking_form_title .best_price {
  display: block;
  color: white;
  font-size: 14px;
  padding: 20px;
  margin-top: -30px;
  margin-bottom: 0;
  font-weight: 100;
  text-align: left;
}
/* line 556, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .booking_form_title .best_price i {
  margin-right: 10px;
}
/* line 563, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .promocode_header p.first_offer_name {
  color: black;
}
/* line 566, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .dates_selector_personalized {
  width: calc(100% - 20px);
}
/* line 568, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .dates_selector_personalized label.dates_selector_label {
  display: block;
  width: 100%;
  height: 0;
  z-index: 2;
  position: relative;
  text-align: center;
}
/* line 575, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .dates_selector_personalized label.dates_selector_label span {
  color: #AAA;
  letter-spacing: 1px;
  padding: 0;
  position: absolute;
  width: calc(50% - 15px);
  text-align: center;
  bottom: -17px;
}
/* line 583, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .dates_selector_personalized label.dates_selector_label span:nth-child(odd) {
  left: auto;
  right: calc(50% + 10px);
}
/* line 587, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .dates_selector_personalized label.dates_selector_label span:nth-child(even) {
  left: calc(50% + 10px);
  right: auto;
}
/* line 593, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper {
  display: table;
  width: 100%;
  height: 100px;
  text-align: center;
  font-size: 0;
  padding: 0 0 20px;
  background: transparent;
}
/* line 601, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper:before {
  display: none;
}
/* line 604, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .start_date_personalized,
#full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .end_date_personalized {
  background-color: white;
  color: #4B4B4B;
  padding: 15px 10px 5px;
  border: 0 solid transparent;
  display: inline-block;
  position: absolute;
  left: auto;
  width: calc(50% - 25px);
  right: calc(50% + 5px);
}
/* line 615, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .start_date_personalized .month, #full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .start_date_personalized .year,
#full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .end_date_personalized .month,
#full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .end_date_personalized .year {
  display: inline-block;
  text-transform: uppercase;
  padding: 0 2px;
  font-size: 10px;
  font-weight: bolder;
  line-height: 10px;
  color: #666;
}
/* line 624, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .start_date_personalized .year,
#full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .end_date_personalized .year {
  color: #AAA;
}
/* line 627, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .start_date_personalized .day,
#full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .end_date_personalized .day {
  font-size: 80px;
  line-height: 70px;
  font-weight: bolder;
  text-align: center;
}
/* line 634, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .end_date_personalized {
  left: calc(50% + 5px);
  right: auto;
}
/* line 637, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .end_date_personalized .month {
  font-size: 10px;
  text-align: left;
}
/* line 642, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .nights_number_wrapper_personalized {
  display: none;
}
/* line 647, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .start_date_datepicker,
#full_wrapper_booking .departure_datepicker {
  z-index: 200;
  top: auto;
  bottom: 100%;
  background-color: white;
}
/* line 654, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .start_date_datepicker,
#full_wrapper_booking .departure_datepicker {
  position: fixed;
  top: 50% !important;
  bottom: auto !important;
  -webkit-transform: translate(0, -50%);
  -moz-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  -o-transform: translate(0, -50%);
  transform: translate(0, -50%);
  height: auto;
}
/* line 669, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .booking_widget .date_box, #full_wrapper_booking .booking_widget .selectricWrapper, #full_wrapper_booking #booking_widget_popup .date_box, #full_wrapper_booking #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 673, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .booking_widget .date_box .date_day, #full_wrapper_booking #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 677, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .selectric {
  height: 38px;
  background: transparent;
}
/* line 682, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .adults_selector, #full_wrapper_booking .room_list_wrapper .children_selector, #full_wrapper_booking .room_list_wrapper .babies_selector {
  height: auto;
  float: left;
  box-sizing: border-box;
}
/* line 688, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .booking_widget .web_support_label_1, #full_wrapper_booking .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 693, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support .web_support_number, #full_wrapper_booking .web_support_label_1 {
  line-height: 15px !important;
  font-size: 14px !important;
}
/* line 698, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title {
  text-align: center;
  background: none;
  opacity: 1;
  font-size: 13px !important;
  color: white;
  margin: 0 auto;
  padding: 0;
}
/* line 707, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title .tick_element {
  display: block;
  font-size: 17px;
}
/* line 712, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title .web_support_label_2 {
  display: inline-block;
  margin: 0 10px;
}
/* line 717, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title .phone_support_image {
  display: none;
}
/* line 722, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date, #full_wrapper_booking .date_box.departure_date {
  margin-top: 6px;
  background-position-x: left;
}
/* line 726, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date .date_year, #full_wrapper_booking .date_box.departure_date .date_year {
  display: none;
}
/* line 730, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date .date_day, #full_wrapper_booking .date_box.departure_date .date_day {
  border-bottom: 0 !important;
  font-weight: 300;
  font-size: 16px !important;
  color: black;
}
/* line 738, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .date_box.departure_date {
  background-position-x: left;
}
/* line 742, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .selectricWrapper {
  width: 100% !important;
}
/* line 745, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .selectricWrapper .selectric {
  margin-top: 0;
}
/* line 750, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking #slider_inner_container #full-booking-engine-html-mobile {
  margin-top: -17px !important;
}
/* line 754, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .promocode_text {
  display: none;
}
/* line 758, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .stay_selection {
  display: inline-block;
  vertical-align: top;
  float: left;
}
/* line 763, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .stay_selection .entry_date_wrapper, #full_wrapper_booking .stay_selection .departure_date_wrapper {
  display: inline-block;
  vertical-align: top;
  float: left;
  margin-right: 5px;
  border: 0 !important;
  background: white;
  width: 212px;
  height: 47px;
}
/* line 774, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .stay_selection .departure_date_wrapper {
  border-left: 0;
  border-right: 0;
}
/* line 779, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .stay_selection .nights_number_wrapper {
  display: inline-block;
  width: 95px;
  float: left;
  vertical-align: top;
  border-top: 1px solid lightgrey;
}
/* line 788, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .rooms_number_wrapper {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 115px;
  height: 47px;
  margin-right: 5px;
  background: white;
  position: relative;
}
/* line 798, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .rooms_number_wrapper .rooms_number {
  padding-left: 45px;
  box-sizing: border-box;
  background-position-y: 40%;
}
/* line 805, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper {
  display: none;
  vertical-align: top;
  float: left;
  background: #4b4b4b;
  width: 100%;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 500;
  overflow-y: scroll;
}
/* line 819, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper:before {
  content: '\f0a8';
  font-family: "FontAwesome", sans-serif;
  font-size: 20px;
  color: white;
  position: absolute;
  bottom: 20px;
  right: 20px;
}
/* line 832, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .rooms_wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 80%;
}
/* line 837, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .rooms_wrapper.with_babies .children_selector:before, #full_wrapper_booking .room_list_wrapper .rooms_wrapper.with_babies .babies_selector:before, #full_wrapper_booking .room_list_wrapper .rooms_wrapper.with_babies .adults_selector:before {
  display: none !important;
}
/* line 843, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list_wrapper_close {
  font-size: 22px;
  color: white;
  padding: 10px;
  width: 100%;
  text-align: center;
  background-color: #102F57;
  box-sizing: border-box;
}
/* line 853, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .rooms_number_wrapper {
  float: none;
  background: transparent;
  border: 2px solid white;
  border-right-width: 47px;
  height: 45px;
  width: 100%;
}
/* line 860, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .rooms_number_wrapper:before {
  content: "\f078";
  font-family: "FontAwesome", sans-serif;
  font-size: 18px;
  padding-top: 3px;
  color: white;
  float: right;
}
/* line 872, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .rooms_number_wrapper label {
  display: inline-block;
  font-size: 18px;
  font-weight: 100;
  color: white;
  vertical-align: middle;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}
/* line 881, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .rooms_number_wrapper .rooms_counter {
  position: absolute;
  top: 0;
  bottom: 0;
  right: -45px;
  width: 45px;
  text-align: center;
  font-size: 20px;
  color: #4b4b4b;
  padding: 10px 0;
  left: auto;
}
/* line 893, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .rooms_number_wrapper .rooms_number {
  background: transparent;
  border: 0;
  border-radius: 0;
  height: 45px;
  width: calc(100% + 45px);
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  padding: 3px 0 3px 100%;
  text-align: center;
  right: -47px;
  font-size: 0;
  color: transparent;
  outline: none;
}
/* line 910, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list {
  padding: 0;
  margin: 0;
  width: 100%;
  list-style-type: none;
}
/* line 916, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room {
  display: table;
  box-sizing: border-box;
  width: 100%;
  height: 45px;
}
/* line 922, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1, #full_wrapper_booking .room_list_wrapper .room_list .room.room2, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 {
  border: 2px solid white;
  margin-bottom: 5px;
}
/* line 925, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1.show_room_babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2.show_room_babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3.show_room_babies_selector {
  border: 0;
}
/* line 927, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1.show_room_babies_selector .children_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room1.show_room_babies_selector .adults_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room1.show_room_babies_selector .show_babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2.show_room_babies_selector .children_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2.show_room_babies_selector .adults_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2.show_room_babies_selector .show_babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3.show_room_babies_selector .children_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3.show_room_babies_selector .adults_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3.show_room_babies_selector .show_babies_selector {
  border-top: 2px solid white;
  border-bottom: 2px solid white;
  border-left: 2px solid white;
}
/* line 934, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .children_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room1 .adults_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room1 .show_babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room1 .pets_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room1 .babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .children_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .adults_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .show_babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .pets_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .children_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .adults_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .show_babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .pets_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .babies_selector {
  position: relative;
  display: table-cell;
  width: 50% !important;
  text-align: center;
  border-width: 0;
  border-right: 45px solid white;
  height: 45px;
}
/* line 942, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .children_selector:before, #full_wrapper_booking .room_list_wrapper .room_list .room.room1 .adults_selector:before, #full_wrapper_booking .room_list_wrapper .room_list .room.room1 .show_babies_selector:before, #full_wrapper_booking .room_list_wrapper .room_list .room.room1 .pets_selector:before, #full_wrapper_booking .room_list_wrapper .room_list .room.room1 .babies_selector:before, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .children_selector:before, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .adults_selector:before, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .show_babies_selector:before, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .pets_selector:before, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .babies_selector:before, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .children_selector:before, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .adults_selector:before, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .show_babies_selector:before, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .pets_selector:before, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .babies_selector:before {
  content: "\f078";
  font-family: "FontAwesome", sans-serif;
  font-size: 18px;
  color: white;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 5px;
}
/* line 956, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .babies_selector {
  display: none;
}
/* line 960, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .pets_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .pets_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .pets_selector {
  width: 100% !important;
  display: inline-block;
  box-sizing: border-box;
  vertical-align: middle;
  border-top: 2px solid white;
}
/* line 967, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .pets_selector .pets_label, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .pets_selector .pets_label, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .pets_selector .pets_label {
  padding-left: 10px;
  text-transform: uppercase;
}
/* line 972, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .adults_selector.show_babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .adults_selector.show_babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .adults_selector.show_babies_selector {
  width: 100% !important;
  margin-bottom: 5px;
}
/* line 976, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .children_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .children_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .children_selector {
  float: right;
}
/* line 979, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .children_selector.show_babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .children_selector.show_babies_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .children_selector.show_babies_selector {
  float: left;
}
/* line 983, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .adults_counter, #full_wrapper_booking .room_list_wrapper .room_list .room.room1 .children_counter, #full_wrapper_booking .room_list_wrapper .room_list .room.room1 .baby_counter, #full_wrapper_booking .room_list_wrapper .room_list .room.room1 .pets_counter, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .adults_counter, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .children_counter, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .baby_counter, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .pets_counter, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .adults_counter, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .children_counter, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .baby_counter, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .pets_counter {
  position: absolute;
  top: 0;
  bottom: 0;
  right: -45px;
  width: 45px;
  text-align: center;
  font-size: 20px;
  color: #4b4b4b;
  padding: 10px 0;
  left: auto;
}
/* line 995, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .room_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .room_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .room_selector {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: calc(100% + 45px);
  right: -45px;
  font-size: 14px;
  color: transparent;
  outline: none;
  padding: 5px 0 5px calc(100% + 15px);
}
/* line 1005, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .room_ages_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .room_ages_selector, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .room_ages_selector {
  display: table;
  width: 100%;
  height: 0;
  overflow: hidden;
  clear: both;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 1013, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .room_ages_selector td, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .room_ages_selector td, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .room_ages_selector td {
  position: relative;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 1016, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .room_ages_selector td h3, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .room_ages_selector td h3, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .room_ages_selector td h3 {
  margin: 0;
  padding: 0;
  color: white;
  background-color: rgba(255, 255, 255, 0.3);
  padding: 5px;
  font-weight: 100;
  text-align: center;
}
/* line 1026, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .room_ages_selector .age_counter, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .room_ages_selector .age_counter, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .room_ages_selector .age_counter {
  background-color: rgba(255, 255, 255, 0.2);
  font-size: 16px;
  padding: 5px;
  color: white;
  text-align: center;
  -webkit-transition: all 1;
  -moz-transition: all 1;
  -ms-transition: all 1;
  -o-transition: all 1;
  transition: all 1;
}
/* line 1034, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 .room_ages_selector .kidAgesSelect, #full_wrapper_booking .room_list_wrapper .room_list .room.room2 .room_ages_selector .kidAgesSelect, #full_wrapper_booking .room_list_wrapper .room_list .room.room3 .room_ages_selector .kidAgesSelect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  width: 100%;
  outline: none;
  color: transparent;
  display: table-cell;
}
/* line 1049, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room1 {
  margin-top: 5px;
}
/* line 1052, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room label {
  width: 100%;
  text-align: left;
  font-size: 12px;
  color: white;
  position: absolute;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}
/* line 1064, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button {
  display: inline-block;
  width: 100%;
  float: left;
  height: 47px;
}
/* line 1070, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button label.promocode_label {
  display: none;
}
/* line 1074, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .promocode_wrapper {
  display: inline-block;
  vertical-align: top;
  float: left;
  width: calc(50% - 10px);
  margin-top: 10px;
  height: 47px;
  background: none;
  border: 2px solid white;
  position: relative;
  padding-top: 5px;
}
/* line 1087, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .submit_button {
  width: calc(100% - 20px);
  height: 47px;
  display: inline-block;
  vertical-align: top;
  margin-top: 10px;
  float: left;
  color: white;
  font-size: 15px;
  background: #F3D132;
  font-weight: 500;
  -webkit-transition: border-radius 0.6s;
  -moz-transition: border-radius 0.6s;
  -ms-transition: border-radius 0.6s;
  -o-transition: border-radius 0.6s;
  transition: border-radius 0.6s;
}
/* line 1100, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .submit_button:hover {
  border-radius: 10px;
}

/* line 1109, ../../../../sass/styles_mobile/2/_booking_engine.scss */
span.ui-icon.ui-icon-circle-triangle-e:before,
span.ui-icon.ui-icon-circle-triangle-w:before {
  content: '' !important;
}

/* line 1114, ../../../../sass/styles_mobile/2/_booking_engine.scss */
body.home_section #full_wrapper_booking.fixed_booking {
  width: 100%;
}

/* line 1118, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.babies_selector {
  width: 33.3%;
  display: inline-block;
  padding: 7px 10px 5px;
  box-sizing: border-box;
}
/* line 1124, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.babies_selector label {
  text-transform: uppercase;
  font-size: 10px;
}

/*=== Ocupancy selector ====*/
/* line 1131, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.guest_selector {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: calc(50% - 10px);
  height: 47px;
  padding: 7px 10px 5px;
  margin: 10px auto 0;
  box-sizing: border-box;
  cursor: pointer;
  background: white;
  position: relative;
}
/* line 1144, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.guest_selector span.placeholder_text {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 10px;
  font-size: 14px;
  font-weight: 300;
  display: block;
  padding-left: 23px;
  box-sizing: border-box;
  background-position-y: 0;
}
/* line 1153, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.guest_selector span.placeholder_text:before {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-family: "FontAwesome", sans-serif;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\f0c0";
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 0;
  color: rgba(0, 0, 0, 0.6);
}
/* line 1170, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.guest_selector span.placeholder_text.selected_value {
  color: #585d63;
  font-size: 21px;
  padding-top: 3px;
  background-position-y: 8px;
  font-weight: 600;
}
/* line 1179, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.guest_selector > label {
  text-transform: uppercase;
  font-size: 10px;
  cursor: pointer;
}
/* line 1185, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.guest_selector b.button {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 10px;
  background: none;
  line-height: 0 !important;
  height: 0;
}
/* line 1192, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.guest_selector b.button:before {
  content: "\f078";
  font-family: "FontAwesome", sans-serif;
  font-size: 18px;
  color: #4b4b4b;
}

/* line 1205, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#booking label {
  cursor: pointer;
}

/* line 1209, ../../../../sass/styles_mobile/2/_booking_engine.scss */
input.promocode_input {
  margin-top: 0;
  color: white;
  background: none;
  text-align: center;
}
/* line 1215, ../../../../sass/styles_mobile/2/_booking_engine.scss */
input.promocode_input::-webkit-input-placeholder {
  color: white;
  font-size: 11px;
  font-weight: 300;
  text-transform: capitalize;
}
/* line 1221, ../../../../sass/styles_mobile/2/_booking_engine.scss */
input.promocode_input::-moz-placeholder {
  color: white;
  font-size: 11px;
  font-weight: 300;
  text-transform: capitalize;
}
/* line 1227, ../../../../sass/styles_mobile/2/_booking_engine.scss */
input.promocode_input:-ms-input-placeholder {
  color: white;
  font-size: 11px;
  font-weight: 300;
  text-transform: capitalize;
}
/* line 1233, ../../../../sass/styles_mobile/2/_booking_engine.scss */
input.promocode_input:-moz-placeholder {
  color: white;
  font-size: 11px;
  font-weight: 300;
  text-transform: capitalize;
}

/* line 1241, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.selectricWrapper .selectric .label {
  font-weight: 300;
  font-size: 16px;
  line-height: 37px;
  color: black;
}

/* line 1248, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#booking .room_list label {
  display: block !important;
}

/* line 1254, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#full_wrapper_booking .rooms_number .selectricItems {
  width: 113px !important;
  margin-left: -10px !important;
}

/* line 1261, ../../../../sass/styles_mobile/2/_booking_engine.scss */
#booking label {
  display: none;
}

/* line 1265, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.hotel_selector {
  display: none;
}

/* line 1269, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper {
  display: inline-block;
  float: left;
  margin-right: 5px;
  cursor: pointer;
  overflow: hidden;
}
/* line 1276, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper input {
  height: 46px;
  box-sizing: border-box;
  font-weight: 300;
  font-size: 13px;
  padding-left: 15px;
  cursor: pointer;
  color: black;
  width: 220px;
}
/* line 1286, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper .destination_field {
  position: relative;
  z-index: 3;
}
/* line 1290, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper .destination_field:after {
  color: #585d63;
  font-size: 23px;
  margin-left: 0;
  text-indent: 999px;
  font-weight: 600;
  float: right;
  width: 30px;
  height: 30px;
  position: absolute;
  top: 10px;
  right: 10px;
  content: '';
  display: block;
  z-index: -1;
}
/* line 1307, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper.flight_hotel_wrapper {
  border-bottom: 0;
}
/* line 1310, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper.flight_hotel_wrapper:before {
  display: none !important;
}
/* line 1315, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper .hotel_flight_selector {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 100px;
  z-index: 10;
}
/* line 1323, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper .hotel_flight_selector .only_hotel, .destination_wrapper .hotel_flight_selector .hotel_flight {
  width: 50%;
  text-align: center;
  float: left;
  padding: 14px 0;
  box-sizing: border-box;
  background: #102F57;
}
/* line 1331, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper .hotel_flight_selector .only_hotel i, .destination_wrapper .hotel_flight_selector .only_hotel .destination_label, .destination_wrapper .hotel_flight_selector .hotel_flight i, .destination_wrapper .hotel_flight_selector .hotel_flight .destination_label {
  font-size: 17px;
  color: white;
}
/* line 1336, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper .hotel_flight_selector .only_hotel .destination_label, .destination_wrapper .hotel_flight_selector .hotel_flight .destination_label {
  font-size: 14px;
}
/* line 1341, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper .hotel_flight_selector .hotel_flight {
  left: auto;
  right: 0;
  background: #b5b5b5;
}
/* line 1346, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper .hotel_flight_selector .hotel_flight .destination_label {
  display: none;
}
/* line 1352, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper .hotel_flight_selector.active .only_hotel {
  background: #b5b5b5;
}
/* line 1356, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper .hotel_flight_selector.active .hotel_flight {
  display: block;
  padding: 6px 0;
  background: #102F57;
}
/* line 1361, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper .hotel_flight_selector.active .hotel_flight .destination_label {
  display: block;
}
/* line 1370, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper .flight_hotel .flight_hotel_origin #flight_hotel_origin {
  position: absolute;
  left: 50px;
  width: 50px !important;
  z-index: 11;
  color: transparent;
}
/* line 1378, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.destination_wrapper .flight_hotel + select {
  padding-left: 110px !important;
}

/* line 1384, ../../../../sass/styles_mobile/2/_booking_engine.scss */
div#full_wrapper_booking.floating_booking.showed {
  position: fixed;
  top: 0;
  bottom: auto;
  width: 100%;
}
/* line 1391, ../../../../sass/styles_mobile/2/_booking_engine.scss */
div#full_wrapper_booking.floating_booking.showed .has_transition_600 {
  -webkit-transition: all 600ms cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: all 600ms cubic-bezier(0.165, 0.84, 0.44, 1);
  will-change: transform, opacity;
}
/* line 1397, ../../../../sass/styles_mobile/2/_booking_engine.scss */
div#full_wrapper_booking.floating_booking.showed #menu_controller {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: auto;
  left: 25px;
  cursor: pointer;
  padding: 15px 10px;
  z-index: 999999;
  display: block;
}
/* line 1406, ../../../../sass/styles_mobile/2/_booking_engine.scss */
div#full_wrapper_booking.floating_booking.showed #menu_controller.opened #lines {
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}
/* line 1411, ../../../../sass/styles_mobile/2/_booking_engine.scss */
div#full_wrapper_booking.floating_booking.showed #menu_controller:hover, div#full_wrapper_booking.floating_booking.showed #menu_controller.opened {
  border: 1px solid #fff;
}
/* line 1415, ../../../../sass/styles_mobile/2/_booking_engine.scss */
div#full_wrapper_booking.floating_booking.showed #menu_controller:hover hr._1, div#full_wrapper_booking.floating_booking.showed #menu_controller.opened hr._1 {
  width: 25px;
  -webkit-transform: rotate(90deg) translate3d(7px, -1px, 0) !important;
  transform: rotate(90deg) translate3d(7px, 0px, 0) !important;
}
/* line 1421, ../../../../sass/styles_mobile/2/_booking_engine.scss */
div#full_wrapper_booking.floating_booking.showed #menu_controller:hover hr._2, div#full_wrapper_booking.floating_booking.showed #menu_controller.opened hr._2 {
  opacity: 0;
}
/* line 1425, ../../../../sass/styles_mobile/2/_booking_engine.scss */
div#full_wrapper_booking.floating_booking.showed #menu_controller:hover hr._3, div#full_wrapper_booking.floating_booking.showed #menu_controller.opened hr._3 {
  -webkit-transform: translate3d(0px, -9px, 0) !important;
  transform: translate3d(0px, -9px, 0) !important;
  width: 25px;
}
/* line 1431, ../../../../sass/styles_mobile/2/_booking_engine.scss */
div#full_wrapper_booking.floating_booking.showed #menu_controller hr {
  width: 25px;
  height: 0;
  border: none;
  border-bottom: 1px solid #FFF;
  margin: 0;
  margin-top: 6px;
  margin-left: 0;
}
/* line 1441, ../../../../sass/styles_mobile/2/_booking_engine.scss */
div#full_wrapper_booking.floating_booking.showed #menu_controller hr {
  border-bottom: 2px solid #FFFFFF;
}
/* line 1445, ../../../../sass/styles_mobile/2/_booking_engine.scss */
div#full_wrapper_booking.floating_booking.showed #menu_controller hr.hidden {
  transform: scale(0, 1);
}
/* line 1449, ../../../../sass/styles_mobile/2/_booking_engine.scss */
div#full_wrapper_booking.floating_booking.showed #menu_controller hr:first-child {
  margin-top: 0;
}

/* line 1455, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine {
  position: fixed;
  bottom: 60px;
  right: 0;
  z-index: 100;
  height: 0;
  width: 100%;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 1463, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine .booking_engine_mobile {
  margin-top: 300px;
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}
/* line 1467, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine .mobile_engine_action {
  position: fixed;
  right: 20px;
  bottom: 70px;
  background-color: #102F57;
  color: white;
  width: calc(100% - 40px);
  height: auto;
  padding: 10px 0;
  border-radius: 60px;
  text-align: center;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.25);
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}
/* line 1480, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine .mobile_engine_action i, .mobile_engine .mobile_engine_action .fa {
  font-size: 22px;
  vertical-align: middle;
}
/* line 1484, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine .mobile_engine_action span {
  margin-left: 10px;
  vertical-align: middle;
  font-size: 16px;
}
/* line 1490, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine.open {
  background-color: rgba(0, 0, 0, 0.9);
  height: 294px;
}
/* line 1493, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine.open .booking_engine_mobile {
  margin-top: 0;
}
/* line 1497, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine.open.has_web_support {
  height: 345px;
}
/* line 1500, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine.open.has_web_support .mobile_engine_action {
  bottom: 345px;
  z-index: 2;
}
/* line 1506, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine.open .mobile_engine_action {
  width: 50px;
  height: 50px;
  padding: 0;
  bottom: 332px;
}
/* line 1511, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine.open .mobile_engine_action i:before {
  content: '\f00d';
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 1515, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine.open .mobile_engine_action span {
  display: none;
}
/* line 1519, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine.open .entry_label_calendar, .mobile_engine.open .departure_label_calendar {
  width: calc(100% - 40px);
}
/* line 1522, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine.open .start_date_datepicker, .mobile_engine.open .departure_datepicker {
  width: calc(100vw - 20px);
}
/* line 1525, ../../../../sass/styles_mobile/2/_booking_engine.scss */
.mobile_engine.open .ui-datepicker {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

@media (orientation: landscape) {
  /* line 1536, ../../../../sass/styles_mobile/2/_booking_engine.scss */
  .mobile_engine.open .entry_label_calendar {
    width: calc(100% - 40px);
  }
  /* line 1539, ../../../../sass/styles_mobile/2/_booking_engine.scss */
  .mobile_engine.open .start_date_datepicker, .mobile_engine.open .departure_datepicker {
    position: fixed;
    top: 0 !important;
    left: 0;
    right: 0;
    bottom: 0 !important;
    width: 100vw;
    max-width: calc(100vh - 40px);
    margin: auto;
    height: calc(100vh - 40px);
    z-index: 10000000000;
  }
}
/* line 1, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_action.engine_v2 {
  position: fixed;
  right: 20px;
  bottom: 80px;
  z-index: 100;
  background-color: #102F57;
  color: white;
  width: calc(100% - 40px);
  height: auto;
  padding: 10px 0;
  border-radius: 60px;
  text-align: center;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.25);
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
  font-family: 'Open Sans', sans-serif;
}
/* line 16, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_action.engine_v2 i {
  font-size: 22px;
  vertical-align: middle;
  margin-right: 10px;
}
/* line 21, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_action.engine_v2 span {
  vertical-align: middle;
  font-size: 26px;
  text-transform: uppercase;
  font-weight: bold;
}
/* line 27, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_action.engine_v2.active {
  bottom: -150px;
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}

/* line 33, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_v2 {
  font-family: 'Open Sans', sans-serif;
}
/* line 34, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_v2 .booking_engine_mobile {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
}
/* line 38, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_v2 * {
  font-family: 'Open Sans', sans-serif;
}
/* line 41, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_v2 .wrapper-new-web-support.booking_form_title {
  margin: 0 10px 10px;
  font-size: 12px;
  text-align: center;
}
/* line 47, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_v2 .destination_wrapper {
  border-bottom: 1px solid #d0d0d0;
}
/* line 51, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_v2 .dates_selector_wrapper {
  border-bottom: 1px solid #d0d0d0;
}
/* line 55, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_v2 .booking_title {
  margin: 13px 20px;
  margin-bottom: 0;
  border-bottom: 1px solid #d0d0d0;
  text-transform: uppercase;
  padding-bottom: 7px;
  position: relative;
  width: calc(100% - 40px);
  font-size: 12px;
  font-weight: 500;
}
/* line 66, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_v2 .booking_title i {
  position: absolute;
  right: 0;
  top: 45%;
  color: #F2B849;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  font-size: 22px;
  font-weight: 300;
}
/* line 81, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_v2 .label_field {
  font-size: 11px;
}
/* line 85, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_v2 .booking_value {
  font-weight: bolder;
  color: #2E2E2E;
  font-size: 14px;
}
/* line 91, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
.mobile_engine_v2 .submit_button {
  margin: 10px;
  background: #F2B849 !important;
  border: 0;
  width: calc(100% - 20px);
  color: white;
  text-transform: uppercase;
  font-weight: bolder;
  font-size: 23px;
  padding: 10px 0;
}

/* line 104, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
#booking_engine_popup {
  position: fixed;
  top: 100%;
  bottom: 0;
  right: 0;
  left: 0;
  background: white;
  z-index: 101;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 118, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
#booking_engine_popup.active {
  top: 0;
}
/* line 122, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
#booking_engine_popup .logo_wrapper {
  padding: 17px 17px 15px;
  max-width: 80%;
  text-align: center;
  position: relative;
}
/* line 128, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
#booking_engine_popup .logo_wrapper .logotype_mobile {
  max-height: 60px;
  max-width: 100%;
}
/* line 132, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
#booking_engine_popup .logo_wrapper:before {
  content: "";
  width: 90%;
  height: 1px;
  background: #d0d0d0;
  display: block;
  top: 100%;
  position: absolute;
}
/* line 143, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
#booking_engine_popup .close_engine_popup {
  position: absolute;
  right: 25px;
  top: 18px;
  font-size: 29px;
  color: #4B5459;
}
/* line 151, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
#booking_engine_popup .mobile_engine_v2 {
  max-height: 88vh;
  overflow: auto;
}
/* line 1, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .room_list_wrapper {
  display: block;
  vertical-align: top;
  float: none;
  background: white;
  width: 100%;
  position: relative;
  left: auto;
  right: auto;
  top: auto;
  bottom: auto;
  z-index: 0;
  overflow-y: visible;
}
/* line 15, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .room_list_wrapper:before {
  display: none;
}
/* line 18, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .room_list_wrapper .booking_title {
  margin-bottom: 0;
}
/* line 22, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .room_list_wrapper .total_occupancy_wrapper {
  margin: 0 20px 20px;
  display: flex;
  width: calc(100% - 40px);
}
/* line 27, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .room_list_wrapper .total_occupancy_wrapper .rooms_number, #booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .room_list_wrapper .total_occupancy_wrapper .occupancy_number {
  float: left;
  text-align: center;
  padding-top: 5px;
}
/* line 31, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .room_list_wrapper .total_occupancy_wrapper .rooms_number .label_field, #booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .room_list_wrapper .total_occupancy_wrapper .occupancy_number .label_field {
  font-size: 11px;
}
/* line 36, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .room_list_wrapper .total_occupancy_wrapper .rooms_number {
  width: 35%;
  border-right: 1px solid #cacaca;
}
/* line 40, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .room_list_wrapper .total_occupancy_wrapper .rooms_number .rooms_number_value {
  display: block;
}
/* line 45, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .room_list_wrapper .total_occupancy_wrapper .occupancy_number {
  width: 65%;
}
/* line 48, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .room_list_wrapper .total_occupancy_wrapper .occupancy_number .occupancy_number_value {
  display: block;
}
/* line 55, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup {
  position: fixed;
  top: 100%;
  bottom: -100%;
  left: 0;
  right: 0;
  background: white;
  z-index: 3;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 69, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup.active {
  top: 0;
  bottom: 0;
}
/* line 74, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .header_wrapper {
  padding: 20px;
  border-bottom: 1px solid rgba(81, 81, 81, 0.26);
}
/* line 78, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .header_wrapper .banner_title {
  text-transform: uppercase;
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 0;
  color: #424242;
  font-style: italic;
}
/* line 86, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .header_wrapper .banner_title i {
  margin-right: 10px;
}
/* line 91, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .header_wrapper .close_popup {
  position: absolute;
  right: 25px;
  top: 8px;
  font-size: 29px;
  color: #F2B849;
}
/* line 100, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .rooms_wrapper {
  padding: 20px;
  max-height: 90vh;
  overflow: auto;
}
/* line 105, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .rooms_wrapper .rooms_number_wrapper {
  padding: 20px;
  float: none;
  display: block;
  width: auto;
  height: auto;
  margin-right: 0;
}
/* line 113, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .rooms_wrapper .rooms_number_wrapper .rooms_label {
  display: inline-block;
  color: #424242;
  font-size: 15px;
}
/* line 119, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .rooms_wrapper .room_list {
  padding: 0;
  list-style: none;
}
/* line 122, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .rooms_wrapper .room_list .room {
  height: auto;
}
/* line 124, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .rooms_wrapper .room_list .room .room_title {
  font-size: 16px;
}
/* line 127, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .rooms_wrapper .room_list .room label {
  font-size: 15px;
  display: inline-block !important;
}
/* line 131, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .rooms_wrapper .room_list .room .babies_selector {
  width: 100%;
}
/* line 138, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .modification_buttons {
  width: 30%;
  float: right;
}
/* line 142, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .modification_buttons input {
  display: inline-block;
  width: 30%;
  float: left;
  text-align: center;
  border: 0;
  font-size: 18px;
}
/* line 151, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .modification_buttons i {
  font-size: 21px;
  margin-top: 1px;
}
/* line 155, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .modification_buttons i.disabled {
  opacity: 0.4;
}
/* line 160, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .modification_buttons {
  width: 30%;
  float: right;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}
/* line 167, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .modification_buttons .plus, #booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .modification_buttons .minus {
  border-radius: 50%;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
}
/* line 176, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .modification_buttons .plus {
  background: #F2B849;
}
/* line 178, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .modification_buttons .plus::before {
  content: '';
  -webkit-mask-image: url("/static_1/images/mobile_img/plus.svg");
  mask-image: url("/static_1/images/mobile_img/plus.svg");
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  background-color: white;
  width: 13px;
  height: 13px;
  -webkit-mask-position: 50% 50%;
  mask-position: 50% 50%;
}
/* line 192, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .modification_buttons .minus {
  border: solid 1px #F2B849;
  padding: 3px 5px;
}
/* line 195, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .modification_buttons .minus::before {
  content: '';
  -webkit-mask-image: url("/static_1/images/mobile_img/minus.svg");
  mask-image: url("/static_1/images/mobile_img/minus.svg");
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  background-color: #F2B849;
  width: 11px;
  height: 15px;
  -webkit-mask-position: 50% 50%;
  mask-position: 50% 50%;
}
/* line 210, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .room_title {
  padding: 20px;
  background: #f4f4f4;
  font-weight: 600;
}
/* line 216, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .occupancy_block_element {
  padding: 20px;
  border-bottom: 1px solid #e2e2e2;
}
/* line 220, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .occupancy_block_element .age_selection_wrapper {
  width: 100%;
  overflow: hidden;
}
/* line 224, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .occupancy_block_element .age_selection_wrapper .age_option {
  display: inline-block;
  text-align: center;
  border: 1px solid black;
  font-size: 10px;
  padding: 3px;
  border-radius: 20px;
  width: 22px;
}
/* line 233, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .occupancy_block_element .age_selection_wrapper .age_option.active {
  background: black;
  color: white;
}
/* line 239, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .occupancy_block_element .age_selection_wrapper .block_age_selection {
  border-top: 1px solid #e2e2e2;
  padding: 13px 15px 0;
  margin-top: 15px;
  margin-left: -4%;
  margin-right: -7%;
  overflow: hidden;
}
/* line 247, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .occupancy_block_element .age_selection_wrapper .block_age_selection.hide {
  padding: 0 15px;
  max-height: 0;
  border-top: 0;
  margin-top: 0;
}
/* line 254, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .occupancy_block_element .age_selection_wrapper .block_age_selection label {
  display: block !important;
  margin-bottom: 5px;
  font-size: 14px;
}
/* line 262, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .occupancy_block_element:last-of-type {
  border-bottom: 0;
}
/* line 269, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .room_list_wrapper_close {
  padding: 15px;
  font-weight: bold;
  color: white;
  text-transform: uppercase;
  background: #102F57;
  margin-top: 10px;
  text-align: center;
  font-size: 14px;
}
/* line 280, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .room {
  max-height: 0;
  -webkit-transition: max-height 0.5s;
  -moz-transition: max-height 0.5s;
  -ms-transition: max-height 0.5s;
  -o-transition: max-height 0.5s;
  transition: max-height 0.5s;
  overflow: hidden;
}
/* line 289, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .room label {
  color: #424242;
}
/* line 293, ../../../../sass/styles_mobile/2/booking_engine/_occupancy_selector.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup .room.active {
  max-height: 450px;
  overflow: auto;
}
/* line 158, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .wrapper_booking_button {
  height: auto;
  float: none;
  width: 100%;
}
/* line 163, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .wrapper_booking_button .promocode_wrapper {
  display: table;
  width: 100%;
  padding: 10px 10px 5px;
  border-top: 1px solid lightgrey;
  box-sizing: border-box;
}
/* line 170, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .wrapper_booking_button .promocode_wrapper .promocode_input {
  width: 100%;
  display: block;
  color: #adadad;
  background: #f4f4f4;
  border: 0;
  text-align: center;
  padding: 12px 0;
  font-family: 'Open Sans', sans-serif;
}
/* line 180, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .wrapper_booking_button .promocode_wrapper .promocode_input::placeholder {
  color: #adadad;
}
/* line 185, ../../../../sass/styles_mobile/2/booking_engine/booking_styles.scss */
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .wrapper_booking_button .submit_button {
  border-radius: 50px;
  font-weight: bold;
  font-size: 24px;
}

/* line 3, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .individual_selector .destination_wrapper {
  display: block;
  float: none;
  border: none;
}
/* line 9, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .individual_selector .destination_wrapper .destination_field input.destination {
  display: none;
}
/* line 13, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .individual_selector .destination_wrapper .destination_field .selected_hotel {
  margin: 10px 0 15px 33px;
  font-size: 15px;
  font-weight: bold;
  color: #2E2E2E;
}
/* line 22, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .individual_selector .hotel_selector {
  display: block;
  max-height: 0;
  padding-bottom: 0;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  overflow: hidden;
  border-bottom: 1px solid #d0d0d0;
}
/* line 34, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .individual_selector .hotel_selector.active {
  max-height: 2000px;
  padding-bottom: 30px;
}
/* line 40, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .individual_selector .hotel_selector .hotel_selector_inner ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
/* line 45, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .individual_selector .hotel_selector .hotel_selector_inner ul .hotel_selector_option {
  padding: 0 20px 0 25px;
  color: #2E2E2E;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: bold;
}
/* line 53, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .individual_selector .hotel_selector .hotel_selector_inner ul .hotel_selector_option ~ .hotel_selector_option {
  margin-top: 10px;
}
/* line 62, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .hotel_selector {
  display: block;
}
/* line 64, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .hotel_selector .selected_hotel {
  text-align: left;
  margin: 10px 0 15px 33px;
  font-size: 15px;
  font-weight: bold;
  color: #2E2E2E;
}
/* line 70, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .hotel_selector .selected_hotel.accepted {
  color: #F2B849;
  text-transform: uppercase;
}
/* line 77, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .hotel_selector.active .destination_wrapper {
  max-height: 2000px;
  padding-bottom: 30px;
}
/* line 84, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper {
  padding-bottom: 0;
  max-height: 0;
  width: 100%;
  background: transparent !important;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  overflow: hidden;
}
/* line 96, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_element {
  padding: 9px 0;
}
/* line 98, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_element:first-of-type {
  padding-top: 0;
}
/* line 103, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_label {
  padding: 0 20px 0 25px;
  color: #2E2E2E;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: bold;
  position: relative;
}
/* line 111, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_label i {
  width: 22px;
  height: 22px;
  font-size: 1.1rem;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 133, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_label i.more {
  border: none;
  background: #F2B849;
}
/* line 136, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_label i.more::before {
  content: '';
  -webkit-mask-image: url("/static_1/images/mobile_img/plus.svg");
  mask-image: url("/static_1/images/mobile_img/plus.svg");
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  background-color: white;
  width: 11px;
  height: 11px;
  -webkit-mask-position: 50% 50%;
  mask-position: 50% 50%;
}
/* line 150, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_label i.less {
  border: 1px solid #F2B849;
  opacity: 0;
  width: 20px;
  height: 20px;
}
/* line 155, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_label i.less::before {
  content: '';
  -webkit-mask-image: url("/static_1/images/mobile_img/minus.svg");
  mask-image: url("/static_1/images/mobile_img/minus.svg");
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  background-color: #F2B849;
  width: 8px;
  height: 11px;
  -webkit-mask-position: 50% 50%;
  mask-position: 50% 50%;
}
/* line 171, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_options {
  max-height: 0;
  display: block;
  overflow: hidden;
  background: #f4f4f4;
  padding-left: 25px;
  font-weight: 600;
  font-size: 15px;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 185, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_options.alone_option {
  max-height: none;
}
/* line 188, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_options .destiny_b0 {
  margin-bottom: 20px;
}
/* line 191, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_options .hotel_element {
  margin-bottom: 20px;
  color: #2e2e2e;
}
/* line 195, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_options .hotel_element:last-of-type {
  margin-bottom: 0;
}
/* line 199, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_options .hotel_element span {
  margin-right: 7px;
}
/* line 203, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_options .hotel_element i {
  font-size: 10px;
  vertical-align: top;
  margin-top: 4px;
}
/* line 213, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_element.active .group_label .more {
  opacity: 0;
}
/* line 217, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_element.active .group_label .less {
  opacity: 1;
}
/* line 222, ../../../../sass/styles_mobile/2/booking_engine/_hotel_selector.scss */
#booking .grouped_selector .destination_wrapper .group_element.active .group_options {
  max-height: 750px;
  margin-top: 10px;
  padding-top: 20px;
  padding-bottom: 20px;
}

/* line 1, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#full_wrapper_booking .dates_selector_wrapper {
  display: table;
  width: 100%;
}
/* line 5, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#full_wrapper_booking .dates_selector_wrapper .stay_selection {
  margin: 0 20px 20px;
  display: table !important;
  width: calc(100% - 40px);
}
/* line 10, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#full_wrapper_booking .dates_selector_wrapper .stay_selection .entry_date_wrapper, #full_wrapper_booking .dates_selector_wrapper .stay_selection .departure_date_wrapper {
  width: 50%;
  float: left;
  text-align: center;
  padding-top: 10px;
  height: auto;
  margin-right: 0;
}
/* line 17, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#full_wrapper_booking .dates_selector_wrapper .stay_selection .entry_date_wrapper .booking_value, #full_wrapper_booking .dates_selector_wrapper .stay_selection .departure_date_wrapper .booking_value {
  background: none;
  font-size: 14px;
  font-weight: bold;
  color: #2E2E2E;
}
/* line 23, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#full_wrapper_booking .dates_selector_wrapper .stay_selection .entry_date_wrapper .label_field, #full_wrapper_booking .dates_selector_wrapper .stay_selection .departure_date_wrapper .label_field {
  display: block !important;
  text-transform: none;
  font-size: 11px;
}
/* line 30, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#full_wrapper_booking .dates_selector_wrapper .stay_selection .entry_date_wrapper {
  border-right: 1px solid #cacaca !important;
}

/* line 36, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
.flexible_dates_wrapper {
  position: absolute;
  top: 16%;
  bottom: 0;
  background: white;
  left: 100%;
  right: -100%;
  z-index: 2;
  transition: all .5s;
  overflow: auto;
}
/* line 47, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
.flexible_dates_wrapper.active {
  left: 0;
  right: 0;
}

/* line 53, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup, #departure_date_popup {
  position: fixed;
  top: 100%;
  bottom: -100%;
  left: 0;
  right: 0;
  z-index: 5;
  background: white;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 67, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup.active, #departure_date_popup.active {
  top: 0;
  bottom: 0;
}
/* line 72, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .header_wrapper, #departure_date_popup .header_wrapper {
  padding: 20px 20px 0;
}
/* line 75, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .header_wrapper .banner_title, #departure_date_popup .header_wrapper .banner_title {
  text-transform: uppercase;
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #2e2e2e;
}
/* line 82, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .header_wrapper .banner_title i, #departure_date_popup .header_wrapper .banner_title i {
  margin-right: 10px;
  color: #F2B849;
  font-size: 23px;
  vertical-align: middle;
}
/* line 90, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .header_wrapper .close_popup, #departure_date_popup .header_wrapper .close_popup {
  position: absolute;
  right: 25px;
  top: 8px;
  font-size: 29px;
  color: #F2B849;
}
/* line 99, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller, #departure_date_popup .menu_controller {
  border-top: 1px solid rgba(81, 81, 81, 0.26);
  border-bottom: 1px solid rgba(81, 81, 81, 0.26);
  display: table;
  width: 100%;
  height: 20px;
}
/* line 106, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller .dates_button, #departure_date_popup .menu_controller .dates_button {
  padding: 10px 0;
  width: 50%;
  float: left;
  text-align: center;
  z-index: 4;
  position: relative;
  display: table;
  height: 100%;
  text-transform: uppercase;
  font-size: 13px;
}
/* line 118, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller .dates_button span, #departure_date_popup .menu_controller .dates_button span {
  display: table-cell;
  vertical-align: middle;
}
/* line 123, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller .dates_button.active, #departure_date_popup .menu_controller .dates_button.active {
  position: relative;
}
/* line 126, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller .dates_button.active:after, #departure_date_popup .menu_controller .dates_button.active:after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: calc(100% - 3.5px);
  height: 3px;
  background: black;
  width: 100%;
}
/* line 140, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .ui-datepicker-group, #departure_date_popup .ui-datepicker-group {
  display: block;
  clear: both;
  width: 100%;
  margin-bottom: 10px;
}
/* line 147, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker, #entry_date_popup .end_datepicker, #departure_date_popup .start_datepicker, #departure_date_popup .end_datepicker {
  max-height: calc(80vh - 40px);
  overflow: auto;
  overflow-x: hidden;
}
/* line 152, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-calendar *, #entry_date_popup .end_datepicker .ui-datepicker-calendar *, #departure_date_popup .start_datepicker .ui-datepicker-calendar *, #departure_date_popup .end_datepicker .ui-datepicker-calendar * {
  border: none !important;
  border-collapse: collapse !important;
  padding: 0 !important;
  margin: 0 !important;
}
/* line 159, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline, #entry_date_popup .end_datepicker .ui-datepicker-inline, #departure_date_popup .start_datepicker .ui-datepicker-inline, #departure_date_popup .end_datepicker .ui-datepicker-inline {
  width: 100% !important;
  border: 0;
}
/* line 163, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-prev, #entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-next, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-prev, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-next, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-prev, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-next, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-prev, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-next {
  display: none;
}
/* line 167, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-header, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-header, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-header, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-header {
  background: none;
  border: 0;
  color: #F2B849;
  font-weight: bold;
  border-bottom: 1px solid rgba(81, 81, 81, 0.26);
  margin-bottom: 20px;
  padding: 12px 0;
}
/* line 175, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-header .ui-datepicker-title, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-header .ui-datepicker-title, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-header .ui-datepicker-title, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-header .ui-datepicker-title {
  font-family: inherit;
}
/* line 180, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-state-default, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-state-default, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-state-default, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-state-default {
  text-align: center;
  background: none;
  border: 0;
  color: #F2B849;
  background: #F2B849;
  line-height: 41px;
  font-weight: bold;
  font-size: 15px;
}
/* line 190, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .highlight_day, #entry_date_popup .end_datepicker .ui-datepicker-inline .highlight_day, #departure_date_popup .start_datepicker .ui-datepicker-inline .highlight_day, #departure_date_popup .end_datepicker .ui-datepicker-inline .highlight_day {
  background: #F2B849;
  opacity: .5;
}
/* line 193, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .highlight_day a, #entry_date_popup .end_datepicker .ui-datepicker-inline .highlight_day a, #departure_date_popup .start_datepicker .ui-datepicker-inline .highlight_day a, #departure_date_popup .end_datepicker .ui-datepicker-inline .highlight_day a {
  color: white !important;
}
/* line 198, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day {
  position: relative;
}
/* line 201, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before {
  content: '';
  position: absolute;
  left: 0;
  width: 50%;
  top: 0;
  bottom: 0;
  background: #F2B849;
  opacity: .5;
}
/* line 212, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active {
  background: transparent !important;
  color: white;
  border-radius: 50px;
  position: relative;
  z-index: 1;
}
/* line 219, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #F2B849;
  max-width: 41px;
  max-height: 41px;
  z-index: -1;
  margin: auto;
  border-radius: 41px;
}
/* line 236, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection {
  position: relative;
  opacity: 1;
}
/* line 240, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection:before {
  content: '';
  position: absolute;
  right: 0;
  width: 50%;
  top: 0;
  bottom: 0;
  background: #F2B849;
  opacity: .5;
  z-index: 0;
}
/* line 252, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span {
  color: white !important;
  border-radius: 50px;
  position: relative;
  z-index: 1;
}
/* line 259, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #F2B849;
  max-width: 41px;
  max-height: 41px;
  z-index: -1;
  margin: auto;
  border-radius: 41px;
}
/* line 276, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline thead span, #entry_date_popup .end_datepicker .ui-datepicker-inline thead span, #departure_date_popup .start_datepicker .ui-datepicker-inline thead span, #departure_date_popup .end_datepicker .ui-datepicker-inline thead span {
  color: #515151;
}
/* line 283, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-current-day:before, #departure_date_popup .start_datepicker .ui-datepicker-current-day:before {
  display: none;
}
/* line 287, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .buttons_bottom, #departure_date_popup .buttons_bottom {
  position: absolute;
  bottom: 0;
  background: white;
  width: 100%;
  padding: 15px;
  box-shadow: 0px -3px 6px #00000029;
  border-top: 1px solid rgba(81, 81, 81, 0.26);
  font-size: 14px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  z-index: 1;
}
/* line 301, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .buttons_bottom .step_label, #departure_date_popup .buttons_bottom .step_label {
  width: 35%;
  text-align: left;
  font-size: 13px;
  display: inline-block;
  color: #303948;
  padding: 0 15px 0 10px;
}
/* line 308, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .buttons_bottom .step_label.close, #entry_date_popup .buttons_bottom .step_label.close_popup, #departure_date_popup .buttons_bottom .step_label.close, #departure_date_popup .buttons_bottom .step_label.close_popup {
  text-align: center;
  font-size: 15px;
  text-transform: uppercase;
}
/* line 313, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .buttons_bottom .step_label:before, #departure_date_popup .buttons_bottom .step_label:before {
  display: none;
}
/* line 317, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .buttons_bottom .accept_button, #departure_date_popup .buttons_bottom .accept_button {
  width: 35%;
  display: inline-block;
  border-radius: 50px;
  border: none;
  font-size: 14px;
  text-transform: uppercase;
  text-align: center;
  padding: 10px 20px;
  color: white;
  font-weight: bold;
  text-decoration: none;
  margin: 0;
  background: #F2B849;
}
/* line 331, ../../../../sass/styles_mobile/2/booking_engine/_dates_selector.scss */
#entry_date_popup .buttons_bottom .accept_button.disabled, #departure_date_popup .buttons_bottom .accept_button.disabled {
  opacity: .5;
}

/* Calendar colors */
/* End */
/* Process buttons colors */
/* End*/
/* Fonts */
/* End */
/* Font sizes */
/* End */
/* line 3, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price, #calendar_price_availability {
  position: relative;
  overflow: hidden;
}
/* line 9, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price .header_wrapper_calendar_availability .graph_calendar_selector, #calendar_price_availability .header_wrapper_calendar_availability .graph_calendar_selector {
  display: none;
}
/* line 13, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price .header_wrapper_calendar_availability .popup_helper_wrapper, #calendar_price_availability .header_wrapper_calendar_availability .popup_helper_wrapper {
  display: none;
}
/* line 18, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar, #calendar_price_availability #prices-calendar {
  position: relative;
  margin-bottom: 16px;
}
/* line 22, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector, #calendar_price_availability #prices-calendar .popup_month_selector {
  position: absolute;
  display: flex;
  justify-content: space-between;
  width: 100%;
  top: 15px;
}
/* line 29, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector select, #calendar_price_availability #prices-calendar .popup_month_selector select {
  display: none;
}
/* line 33, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector > div, #calendar_price_availability #prices-calendar .popup_month_selector > div {
  position: relative;
  width: 30px;
  height: 30px;
}
/* line 38, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector > div::before, #calendar_price_availability #prices-calendar .popup_month_selector > div::before {
  position: absolute;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-family: "Font Awesome 5 Pro";
  color: #333;
  font-weight: 300;
  font-size: 30px;
}
/* line 49, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector .previous_month_selector::before, #calendar_price_availability #prices-calendar .popup_month_selector .previous_month_selector::before {
  content: '\f104';
}
/* line 55, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector .next_month_selector::before, #calendar_price_availability #prices-calendar .popup_month_selector .next_month_selector::before {
  content: '\f105';
}
/* line 61, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar #selectorRooms, #calendar_price_availability #prices-calendar #selectorRooms {
  display: none;
}
/* line 69, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar {
  width: 100%;
}
/* line 74, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr {
  border-top: 3px solid white;
  border-bottom: 3px solid white;
}
/* line 79, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr:nth-child(1) th, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr:nth-child(1) th {
  font-size: 1.2rem;
  padding: 15px;
  font-weight: 700;
}
/* line 86, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr:nth-child(2), #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr:nth-child(2) {
  display: none;
}
/* line 90, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td {
  vertical-align: top;
  width: calc(100vw / 7);
  position: relative;
  text-align: center;
  border-left: 3px solid white;
  border-left: 3px solid white;
  background-color: #f0f0f0;
}
/* line 99, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .another-month-day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .another-month-day {
  background-color: #dfdfdf;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
/* line 108, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day {
  font-size: 0.65rem;
  color: white;
  font-weight: 500;
  padding: 3px;
}
/* line 114, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.not-available-day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.not-available-day {
  background-color: #E75354;
}
/* line 118, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.available-day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.available-day {
  background-color: #00ac6b;
}
/* line 122, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.restricted-day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.restricted-day {
  background-color: orange;
}
/* line 126, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.restricted-day + .day-content.available .price:before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.restricted-day + .day-content.available .price:before {
  color: orange;
}
/* line 134, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content img, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content img {
  display: none;
}
/* line 138, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content .price, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content .price {
  padding-top: 15px;
  font-size: 0.65rem;
  height: 30px;
  font-weight: 500;
}
/* line 2, ../../../../sass/styles_mobile/2/booking_engine/_variables.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.available .price::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.available .price::before {
  content: "";
  position: absolute;
  top: 28px;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: 'Font Awesome 5 Pro', 'icomoon';
  font-size: 20px;
  color: #00ac6b;
}
/* line 150, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.available .restriction-message, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.available .restriction-message {
  font-size: 0.4rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: -.3px;
}
/* line 159, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.no-available .not-available-message, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.no-available .not-available-message {
  height: 30px;
  word-break: break-word;
  font-size: 0.5rem;
  font-weight: 500;
  text-transform: uppercase;
  display: flex;
  align-items: center;
}
@media (max-width: 359px) {
  /* line 159, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
  #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.no-available .not-available-message, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.no-available .not-available-message {
    font-size: 0.4rem;
  }
}
/* line 176, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day, #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content {
  background-color: #7ccff4;
}
/* line 182, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content.available .price, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content.available .price {
  color: white;
}
/* line 185, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content.available .price::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content.available .price::before {
  color: white;
}
/* line 191, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content .restriction-message, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content .restriction-message {
  color: white;
}
/* line 197, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection::before, #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection::before {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  top: 50%;
  z-index: 1;
}
/* line 208, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection .day, #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection .day-content, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection .day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection .day-content {
  background-color: #1eadec;
}
/* line 212, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection::before {
  background-color: #1eadec;
  right: 0;
  transform: translate(50%, -50%) rotate(45deg);
}
/* line 220, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection .day, #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection .day-content, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection .day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection .day-content {
  background-color: #118ec6;
}
/* line 224, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection::before {
  background-color: #118ec6;
  left: 0;
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 238, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .graphs_fields_wrapper, #calendar_price_availability #prices-calendar .calendars-section .graphs_fields_wrapper {
  display: none;
}
/* line 242, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend, #calendar_price_availability #prices-calendar .calendars-section .legend {
  padding: 15px;
}
/* line 246, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li, #calendar_price_availability #prices-calendar .calendars-section .legend ul li {
  position: relative;
  padding-left: 20px;
  font-size: 0.8rem;
}
/* line 251, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li::before, #calendar_price_availability #prices-calendar .calendars-section .legend ul li::before {
  position: absolute;
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
/* line 263, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li:nth-child(1)::before, #calendar_price_availability #prices-calendar .calendars-section .legend ul li:nth-child(1)::before {
  background-color: #00ac6b;
}
/* line 269, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li:nth-child(2)::before, #calendar_price_availability #prices-calendar .calendars-section .legend ul li:nth-child(2)::before {
  background-color: orange;
}
/* line 275, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li:nth-child(3)::before, #calendar_price_availability #prices-calendar .calendars-section .legend ul li:nth-child(3)::before {
  background-color: #E75354;
}
/* line 280, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li:nth-child(4), #calendar_price_availability #prices-calendar .calendars-section .legend ul li:nth-child(4) {
  display: none;
}
/* line 288, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper {
  padding: 15px;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  font-size: 0.8rem;
  font-weight: bold;
  margin-bottom: 100px;
  width: 100%;
}
/* line 298, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div {
  width: 33%;
  padding: 5px 15px;
}
/* line 302, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.label_actual_selection, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.label_actual_selection {
  background-color: #efefef;
  color: #333;
}
/* line 307, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.nights_number_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.nights_number_wrapper {
  background-color: #a2a2a2;
  color: white;
}
/* line 311, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.nights_number_wrapper > *:first-child, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.nights_number_wrapper > *:first-child {
  display: block;
}
/* line 316, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.selection_price_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.selection_price_wrapper {
  background-color: #6f6f6f;
  color: white;
}
/* line 321, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.selection_price_wrapper .vertical_center > *:first-child, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.selection_price_wrapper .vertical_center > *:first-child {
  display: block;
}
/* line 329, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .button, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .button {
  position: fixed;
  bottom: 10px;
  left: 0;
  right: 0;
}
/* line 339, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price .modifyButtonCalendar, #calendar_price_availability .modifyButtonCalendar {
  right: 20px !important;
  left: 100% !important;
  bottom: 60px !important;
  border: 0;
  font-size: 17px;
  text-transform: uppercase;
  padding: 10px 30px;
  color: white;
  background: #f7bb1e;
  transition: left .5s, right .5s;
}
/* line 351, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
#calendar_price .modifyButtonCalendar.disabled-button, #calendar_price_availability .modifyButtonCalendar.disabled-button {
  opacity: 0.8;
  background: lightgray;
}

/* line 359, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
.flexible_dates_wrapper.active #calendar_price_availability .modifyButtonCalendar {
  left: auto !important;
  right: 20px !important;
}

/* line 367, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
.flexible_dates_wrapper #calendar_price_availability #prices-calendar .calendars-section .bottom_buttons_wrapper {
  left: 0;
  right: 0;
  bottom: 0;
  position: absolute !important;
  box-shadow: 0px -3px 6px #00000029;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  background: white;
  height: 75px;
  z-index: -10;
}
/* line 380, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
.flexible_dates_wrapper #calendar_price_availability #prices-calendar .calendars-section .bottom_buttons_wrapper .close_popup {
  width: 45%;
  text-align: left;
  font-size: 14px;
  padding: 0 30px 0 10px;
}
/* line 386, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
.flexible_dates_wrapper #calendar_price_availability #prices-calendar .calendars-section .bottom_buttons_wrapper .close_popup.close {
  text-align: center;
  font-size: 15px;
  text-transform: uppercase;
}
/* line 392, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
.flexible_dates_wrapper #calendar_price_availability #prices-calendar .calendars-section .bottom_buttons_wrapper .close_popup:before {
  display: none;
}
/* line 397, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
.flexible_dates_wrapper #calendar_price_availability #prices-calendar .calendars-section .bottom_buttons_wrapper .button {
  width: 35%;
  display: inline-block;
  border-radius: 50px;
  border: none;
  font-size: 14px;
  text-transform: uppercase;
  text-align: center;
  position: unset;
  padding: 10px 20px;
  color: white;
  font-weight: bold;
  text-decoration: none;
  margin: 0;
  background: #F2B849;
}
/* line 413, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
.flexible_dates_wrapper #calendar_price_availability #prices-calendar .calendars-section .bottom_buttons_wrapper .button.disabled-button {
  opacity: .5;
}
/* line 420, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
.flexible_dates_wrapper.active #calendar_price_availability #prices-calendar .calendars-section .bottom_buttons_wrapper {
  z-index: 10;
  position: fixed !important;
}

/* line 427, ../../../../sass/styles_mobile/2/booking_engine/_calendar_price.scss */
.select-last-day {
  display: none !important;
}

/* line 1, ../../../../sass/styles_mobile/2/_header.scss */
header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: white;
  box-shadow: 0 5px 8px rgba(0, 0, 0, 0.25);
  height: 80px;
}
/* line 8, ../../../../sass/styles_mobile/2/_header.scss */
header a {
  text-decoration: none;
  display: inline-block;
}
/* line 12, ../../../../sass/styles_mobile/2/_header.scss */
header .logo {
  display: block;
  margin: auto;
  max-height: 100%;
  max-width: 200px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 18, ../../../../sass/styles_mobile/2/_header.scss */
header .logo img {
  max-height: 70px;
  max-width: 100%;
}
/* line 23, ../../../../sass/styles_mobile/2/_header.scss */
header .mailto, header .telefono, header .whatsapp {
  position: relative;
  width: 80px;
  height: 80px;
}
/* line 27, ../../../../sass/styles_mobile/2/_header.scss */
header .mailto i, header .telefono i, header .whatsapp i {
  font-size: 28px;
  color: #102F57;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 33, ../../../../sass/styles_mobile/2/_header.scss */
header .mailto {
  float: left;
}
/* line 36, ../../../../sass/styles_mobile/2/_header.scss */
header .telefono, header .whatsapp {
  float: right;
}
/* line 41, ../../../../sass/styles_mobile/2/_header.scss */
header.with_whatsapp .logo img {
  max-width: 85%;
}
/* line 45, ../../../../sass/styles_mobile/2/_header.scss */
header.with_whatsapp .telefono, header.with_whatsapp .whatsapp {
  width: 50px;
}

/* line 50, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  text-align: center;
  width: 100%;
  background-color: #F2B849;
  z-index: 100;
  height: 0;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 59, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu {
  height: 100%;
}
/* line 61, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu .social_menu, .main_menu.open_menu ul.main_ul {
  margin-top: 0;
}
/* line 65, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu .social_menu a, .main_menu.open_menu .social_menu li, .main_menu.open_menu ul.main_ul a, .main_menu.open_menu ul.main_ul li {
  animation-name: smooth_right;
  animation-duration: 3s;
}
/* line 68, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu .social_menu a:nth-child(1), .main_menu.open_menu .social_menu li:nth-child(1), .main_menu.open_menu ul.main_ul a:nth-child(1), .main_menu.open_menu ul.main_ul li:nth-child(1) {
  animation-duration: .75s;
}
/* line 71, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu .social_menu a:nth-child(2), .main_menu.open_menu .social_menu li:nth-child(2), .main_menu.open_menu ul.main_ul a:nth-child(2), .main_menu.open_menu ul.main_ul li:nth-child(2) {
  animation-duration: 1s;
}
/* line 74, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu .social_menu a:nth-child(3), .main_menu.open_menu .social_menu li:nth-child(3), .main_menu.open_menu ul.main_ul a:nth-child(3), .main_menu.open_menu ul.main_ul li:nth-child(3) {
  animation-duration: 1.25s;
}
/* line 77, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu .social_menu a:nth-child(4), .main_menu.open_menu .social_menu li:nth-child(4), .main_menu.open_menu ul.main_ul a:nth-child(4), .main_menu.open_menu ul.main_ul li:nth-child(4) {
  animation-duration: 1.5s;
}
/* line 80, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu .social_menu a:nth-child(5), .main_menu.open_menu .social_menu li:nth-child(5), .main_menu.open_menu ul.main_ul a:nth-child(5), .main_menu.open_menu ul.main_ul li:nth-child(5) {
  animation-duration: 1.75s;
}
/* line 83, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu .social_menu a:nth-child(6), .main_menu.open_menu .social_menu li:nth-child(6), .main_menu.open_menu ul.main_ul a:nth-child(6), .main_menu.open_menu ul.main_ul li:nth-child(6) {
  animation-duration: 2s;
}
/* line 86, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu .social_menu a:nth-child(7), .main_menu.open_menu .social_menu li:nth-child(7), .main_menu.open_menu ul.main_ul a:nth-child(7), .main_menu.open_menu ul.main_ul li:nth-child(7) {
  animation-duration: 2.25s;
}
/* line 89, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu .social_menu a:nth-child(8), .main_menu.open_menu .social_menu li:nth-child(8), .main_menu.open_menu ul.main_ul a:nth-child(8), .main_menu.open_menu ul.main_ul li:nth-child(8) {
  animation-duration: 2.5s;
}
/* line 92, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu .social_menu a:nth-child(9), .main_menu.open_menu .social_menu li:nth-child(9), .main_menu.open_menu ul.main_ul a:nth-child(9), .main_menu.open_menu ul.main_ul li:nth-child(9) {
  animation-duration: 2.75s;
}
/* line 95, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu .social_menu a:nth-child(10), .main_menu.open_menu .social_menu li:nth-child(10), .main_menu.open_menu ul.main_ul a:nth-child(10), .main_menu.open_menu ul.main_ul li:nth-child(10) {
  animation-duration: 3s;
}
/* line 98, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu .social_menu a:nth-child(11), .main_menu.open_menu .social_menu li:nth-child(11), .main_menu.open_menu ul.main_ul a:nth-child(11), .main_menu.open_menu ul.main_ul li:nth-child(11) {
  animation-duration: 3.25s;
}
/* line 101, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu.open_menu .social_menu a:nth-child(12), .main_menu.open_menu .social_menu li:nth-child(12), .main_menu.open_menu ul.main_ul a:nth-child(12), .main_menu.open_menu ul.main_ul li:nth-child(12) {
  animation-duration: 3.5s;
}
/* line 107, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu .social_menu {
  background-color: rgba(0, 0, 0, 0.2);
  display: table;
  width: 100%;
  box-sizing: border-box;
  margin: auto;
  margin-top: 1000px;
  text-align: right;
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}
/* line 116, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu .social_menu a {
  display: inline-block;
  position: relative;
  text-align: center;
  box-sizing: border-box;
  padding: 7px 0 5px;
  margin: 0 5px;
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}
/* line 125, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu .social_menu a.mailto {
  padding: 0;
  margin: 0;
  float: left;
}
/* line 129, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu .social_menu a.mailto i {
  border-radius: 0;
  width: 40px;
  height: 41px;
  padding: 10px 0;
}
/* line 136, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu .social_menu a i {
  background-color: #102F57;
  width: 30px;
  height: 30px;
  box-sizing: border-box;
  border-radius: 50%;
  font-size: 20px;
  padding: 5px 0;
  color: white;
}
/* line 148, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu ul {
  height: calc(100% - 150px);
  overflow: auto;
  list-style-type: none;
  margin: 0;
  padding: 20px 20px 50px;
}
/* line 154, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu ul li {
  position: relative;
  color: white;
  text-align: left;
  border-bottom: 1px solid white;
}
/* line 159, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu ul li:last-of-type {
  margin-bottom: 30px;
}
/* line 162, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu ul li a, .main_menu ul li span {
  font-size: 18px;
  font-weight: 100;
  display: block;
  color: white;
  padding: 10px;
  text-decoration: none;
}
/* line 171, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu ul li .fa-plus {
  position: absolute;
  top: 16px;
  right: 20px;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}
/* line 176, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu ul li .fa-plus.fa-rotate-45 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
/* line 183, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu ul li .submenu_list {
  display: none;
}
/* line 186, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu ul li ul {
  height: auto;
  padding: 0 20px;
  margin: 0;
  overflow: inherit;
}
/* line 191, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu ul li ul li {
  border-bottom-width: 0;
}
/* line 192, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu ul li ul a {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}
/* line 198, ../../../../sass/styles_mobile/2/_header.scss */
.main_menu ul .language_selector_main_menu {
  display: none;
}

/* line 203, ../../../../sass/styles_mobile/2/_header.scss */
nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: table;
  text-align: center;
  width: 100%;
  background-color: white;
  border-top: 1px solid gainsboro;
  z-index: 100;
  height: 60px;
}
/* line 213, ../../../../sass/styles_mobile/2/_header.scss */
nav .separator {
  width: 1px;
  border-left: 1px solid rgba(0, 0, 0, 0.1);
}
/* line 217, ../../../../sass/styles_mobile/2/_header.scss */
nav a {
  display: table-cell;
  height: 40px;
  position: relative;
  color: #4b4b4b;
}
/* line 218, ../../../../sass/styles_mobile/2/_header.scss */
nav a.mailto, nav a.phone_to {
  color: #102F57;
}
/* line 225, ../../../../sass/styles_mobile/2/_header.scss */
nav a .flag {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 231, ../../../../sass/styles_mobile/2/_header.scss */
nav a i, nav a .fa {
  font-size: 22px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  position: absolute !important;
}
/* line 235, ../../../../sass/styles_mobile/2/_header.scss */
nav a i.fa-times, nav a i.fa-bars, nav a .fa.fa-times, nav a .fa.fa-bars {
  color: white;
}
/* line 239, ../../../../sass/styles_mobile/2/_header.scss */
nav a.active {
  color: #102F57;
}
/* line 243, ../../../../sass/styles_mobile/2/_header.scss */
nav .lang_selector {
  appearance: none;
  -moz-appearance: none;
  /* Firefox */
  -webkit-appearance: none;
  /* Safari and Chrome */
  background: transparent;
  border-width: 0;
  font-size: 0;
  outline: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

@-webkit-keyframes smooth_up {
  0% {
    margin-top: 100vh;
  }
  100% {
    margin-top: 0;
  }
}
@keyframes smooth_up {
  0% {
    margin-top: 100vh;
  }
  100% {
    margin-top: 0;
  }
}
@-webkit-keyframes smooth_left {
  0% {
    margin-left: 100vw;
  }
  100% {
    margin-top: 0;
  }
}
@keyframes smooth_left {
  0% {
    margin-left: 100vw;
  }
  100% {
    margin-top: 0;
  }
}
@-webkit-keyframes smooth_right {
  0% {
    -webkit-transform: translate(100vw, 0);
    -moz-transform: translate(100vw, 0);
    -ms-transform: translate(100vw, 0);
    -o-transform: translate(100vw, 0);
    transform: translate(100vw, 0);
  }
  100% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}
@keyframes smooth_right {
  0% {
    -webkit-transform: translate(100vw, 0);
    -moz-transform: translate(100vw, 0);
    -ms-transform: translate(100vw, 0);
    -o-transform: translate(100vw, 0);
    transform: translate(100vw, 0);
  }
  100% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}
/* line 1, ../../../../sass/styles_mobile/2/_breadbrumbs.scss */
.breadcrumbs {
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  z-index: 99;
  white-space: nowrap;
  background-color: #102F57;
}
/* line 9, ../../../../sass/styles_mobile/2/_breadbrumbs.scss */
.breadcrumbs a, .breadcrumbs i {
  color: white;
  display: inline-block;
  font-size: 12px;
  padding: 10px;
}
/* line 15, ../../../../sass/styles_mobile/2/_breadbrumbs.scss */
.breadcrumbs a {
  white-space: nowrap;
}
/* line 16, ../../../../sass/styles_mobile/2/_breadbrumbs.scss */
.breadcrumbs i {
  padding: 10px 0;
}

/* line 1, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper {
  position: relative;
  top: -57px;
  left: 0;
  right: 0;
  bottom: 40px;
  z-index: 10;
  width: 100vw;
  height: auto;
  background-color: black;
  padding-bottom: 30px;
}
/* line 13, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .offer_background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: .8;
  -webkit-filter: blur(10px);
  /* Safari 6.0 - 9.0 */
  filter: blur(10px);
  background-size: cover;
}
/* line 24, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .offer_content {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin-top: -5%;
  width: 70%;
  height: 40%;
  padding-bottom: 30px;
  box-sizing: border-box;
  z-index: 2;
  text-align: left;
  overflow: hidden;
  border-radius: 10px;
  background-color: white;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 37, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .offer_content .picture {
  position: relative;
  z-index: 2;
  width: 100%;
  border-radius: 10px 10px 0 0;
  height: 50%;
  overflow: hidden;
}
/* line 44, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .offer_content .picture img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 51, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .offer_content h3 {
  position: relative;
  z-index: 5;
  padding: 10px 100px 0 10px;
  border-top: 20px solid white;
  margin: 0;
  background-color: white;
  font-size: 16px;
  overflow: hidden;
  height: auto;
}
/* line 62, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .offer_content .desc {
  position: relative;
  z-index: 5;
  width: 100%;
  height: 0;
  box-sizing: border-box;
  overflow: hidden;
  padding: 0;
  font-size: 12px;
  border-bottom: 10px solid white;
  border-radius: 0 0 10px 10px;
  background-color: white;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}
/* line 77, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .offer_content .offer_links_wrapper {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  top: 50%;
  bottom: auto;
  display: none;
  width: 80%;
  text-align: center;
  border-radius: 5px 0 5px 5px;
  overflow: hidden;
  -webkit-transition: all 2s;
  -moz-transition: all 2s;
  -ms-transition: all 2s;
  -o-transition: all 2s;
  transition: all 2s;
}
/* line 89, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .offer_content a.offer_link {
  padding: 6px 10px;
  display: inline-block;
  vertical-align: middle;
  margin-right: -5px;
  text-transform: uppercase;
  width: 25%;
  box-sizing: border-box;
  text-align: center;
  font-size: 20px;
  background-color: #F2B849;
  color: white;
}
/* line 104, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .offer_content .button-promotion {
  padding: 8px 10px;
  box-sizing: border-box;
  text-transform: uppercase;
  font-size: 16px;
  background-color: #102F57;
  color: white;
  text-align: center;
  width: 75%;
  border-radius: 5px;
  display: inline-block;
  vertical-align: middle;
}
/* line 117, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .offer_content .button-promotion .fa {
  float: left;
  font-size: 20px;
}
/* line 124, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .owl-stage-outer {
  min-height: calc(100vh - 120px);
}
/* line 126, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .owl-stage-outer .owl-item {
  min-height: calc(100vh - 120px);
}
/* line 128, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .owl-stage-outer .owl-item .offer_content {
  overflow: inherit;
  left: 30%;
}
/* line 132, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .owl-stage-outer .owl-item.active {
  overflow: auto;
  /*.button-promotion, a.offer_link {
    display: block;
    z-index: 10;
    margin-top: -33px;
    animation-name: promotion_down;
    animation-duration: 2s;
  }*/
}
/* line 134, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .owl-stage-outer .owl-item.active .offer_content {
  overflow: inherit;
  min-height: 55%;
  left: 50%;
}
/* line 138, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .owl-stage-outer .owl-item.active .offer_content h3 {
  height: auto;
}
/* line 141, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .owl-stage-outer .owl-item.active .offer_content .desc {
  height: auto;
  padding: 10px;
}
/* line 154, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .owl-stage-outer .owl-item.active .offer_links_wrapper {
  display: block;
  z-index: 10;
  margin-top: -33px;
  animation-name: promotion_down;
  animation-duration: 2s;
}
/* line 164, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .owl-prev, .promotions_wrapper .owl-next {
  position: absolute;
  top: 0;
  left: 0;
  right: auto;
  bottom: 0;
  width: 10%;
  font-size: 0;
  background-color: transparent;
  display: inline-block;
  color: transparent;
  font: normal normal normal 14px/1 FontAwesome;
  font-family: "FontAwesome", sans-serif;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* line 186, ../../../../sass/styles_mobile/2/_promotions.scss */
.promotions_wrapper .owl-next {
  left: auto;
  right: 0;
}

@-webkit-keyframes promotion_down {
  0% {
    z-index: 3;
    margin-top: 50px;
    opacity: 0;
  }
  1% {
    z-index: 3;
    margin-top: 50px;
    opacity: 0;
  }
  50% {
    z-index: 3;
    margin-top: 50px;
    opacity: 0;
  }
  80% {
    z-index: 3;
    margin-top: -66px;
    opacity: 1;
  }
  100% {
    z-index: 10;
    margin-top: -33px;
    opacity: 1;
  }
}
@keyframes promotion_down {
  0% {
    z-index: 3;
    margin-top: 50px;
    opacity: 0;
  }
  1% {
    z-index: 3;
    margin-top: 50px;
    opacity: 0;
  }
  50% {
    z-index: 3;
    margin-top: 50px;
    opacity: 0;
  }
  80% {
    z-index: 3;
    margin-top: -66px;
    opacity: 1;
  }
  100% {
    z-index: 10;
    margin-top: -33px;
    opacity: 1;
  }
}
/* line 1, ../../../../sass/styles_mobile/2/_news.scss */
.news_wrapper {
  width: 100%;
  box-sizing: border-box;
  padding: 20px;
}
/* line 5, ../../../../sass/styles_mobile/2/_news.scss */
.news_wrapper .entry_widget {
  margin-bottom: 30px;
  border-radius: 5px;
  border: 1px solid #DDD;
}
/* line 10, ../../../../sass/styles_mobile/2/_news.scss */
.news_wrapper .entry_widget .image img {
  border-radius: 5px 5px 0 0;
  width: 100%;
}
/* line 15, ../../../../sass/styles_mobile/2/_news.scss */
.news_wrapper .entry_widget .tags {
  position: absolute;
  display: inline-block;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
}
/* line 22, ../../../../sass/styles_mobile/2/_news.scss */
.news_wrapper .entry_widget .title {
  text-align: left;
  font-weight: bold;
  padding: 0 10px 10px;
  font-size: 150%;
}
/* line 27, ../../../../sass/styles_mobile/2/_news.scss */
.news_wrapper .entry_widget .title a {
  color: #4b4b4b;
}
/* line 31, ../../../../sass/styles_mobile/2/_news.scss */
.news_wrapper .entry_widget .date {
  text-align: left;
  padding: 0 10px;
  color: #CCC;
  font-size: 70%;
}
/* line 36, ../../../../sass/styles_mobile/2/_news.scss */
.news_wrapper .entry_widget .date i {
  display: inline-block;
  vertical-align: middle;
  font-size: 150%;
  margin-right: 5px;
}
/* line 42, ../../../../sass/styles_mobile/2/_news.scss */
.news_wrapper .entry_widget .date span {
  display: inline-block;
  vertical-align: middle;
}
/* line 47, ../../../../sass/styles_mobile/2/_news.scss */
.news_wrapper .entry_widget .content {
  text-align: left;
  padding: 10px;
  font-size: 80%;
}
/* line 52, ../../../../sass/styles_mobile/2/_news.scss */
.news_wrapper .entry_widget .read_more {
  position: relative;
  top: -25px;
  display: inline-block;
  background-color: #F2B849;
  border-radius: 5px;
  color: white;
  padding: 10px;
  text-align: center;
  text-transform: uppercase;
}
/* line 62, ../../../../sass/styles_mobile/2/_news.scss */
.news_wrapper .entry_widget .read_more i {
  font-size: 20px;
  margin: 0 10px 0 0;
}

/* line 71, ../../../../sass/styles_mobile/2/_news.scss */
.entry_wrapper .sectionImages {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
  box-sizing: border-box;
}
/* line 77, ../../../../sass/styles_mobile/2/_news.scss */
.entry_wrapper .sectionImages img {
  @inclide center_xy;
  width: 100%;
}
/* line 81, ../../../../sass/styles_mobile/2/_news.scss */
.entry_wrapper .sectionImages .background_overlay {
  background-color: rgba(0, 0, 0, 0.3);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
/* line 86, ../../../../sass/styles_mobile/2/_news.scss */
.entry_wrapper .sectionImages h1 {
  position: absolute;
  bottom: 20px;
  left: 20px;
  color: white;
  margin: 0;
  font-weight: bold;
  font-size: 30px;
  text-shadow: 0 0 0 rgba(0, 0, 0, 0.3);
  text-align: left;
}
/* line 98, ../../../../sass/styles_mobile/2/_news.scss */
.entry_wrapper .date {
  text-align: left;
  padding: 10px 20px;
  color: #CCC;
  font-size: 70%;
}
/* line 103, ../../../../sass/styles_mobile/2/_news.scss */
.entry_wrapper .date i {
  display: inline-block;
  vertical-align: middle;
  font-size: 150%;
  margin-right: 5px;
}
/* line 109, ../../../../sass/styles_mobile/2/_news.scss */
.entry_wrapper .date span {
  display: inline-block;
  vertical-align: middle;
}
/* line 114, ../../../../sass/styles_mobile/2/_news.scss */
.entry_wrapper .sectionContent {
  padding: 0 20px;
  font-size: 80%;
}

/* line 1, ../../../../sass/styles_mobile/2/_rooms.scss */
.rooms_background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  -webkit-filter: blur(10px);
  /* Safari 6.0 - 9.0 */
  filter: blur(10px);
  background-color: #000;
  background-position: center;
  background-size: cover;
}

/* line 14, ../../../../sass/styles_mobile/2/_rooms.scss */
.rooms_wrapper .room_block {
  position: relative;
  width: 90%;
  display: table;
  margin: 30px auto;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
  border: 0 solid #DDD;
  background-color: white;
}
/* line 25, ../../../../sass/styles_mobile/2/_rooms.scss */
.rooms_wrapper .room_block .room_picture {
  position: relative;
  width: 100%;
  height: 200px;
  display: inline-block;
  vertical-align: middle;
  overflow: hidden;
}
/* line 32, ../../../../sass/styles_mobile/2/_rooms.scss */
.rooms_wrapper .room_block .room_picture img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 39, ../../../../sass/styles_mobile/2/_rooms.scss */
.rooms_wrapper .room_block .room_info {
  display: inline-block;
  width: 100%;
  padding-top: 20px;
  vertical-align: middle;
}
/* line 44, ../../../../sass/styles_mobile/2/_rooms.scss */
.rooms_wrapper .room_block .room_info h1 {
  text-align: left;
  padding: 10px;
  font-weight: bold;
  margin: 0;
  font-size: 16px;
}
/* line 51, ../../../../sass/styles_mobile/2/_rooms.scss */
.rooms_wrapper .room_block .room_info .room_description {
  font-size: 12px;
  padding: 1em;
}
/* line 56, ../../../../sass/styles_mobile/2/_rooms.scss */
.rooms_wrapper .room_block .buttons {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  width: 100%;
  top: 180px;
}
/* line 60, ../../../../sass/styles_mobile/2/_rooms.scss */
.rooms_wrapper .room_block .buttons a {
  display: inline-block;
  background-color: #F2B849;
  padding: 6px 20px;
  border-radius: 5px 0 0 5px;
  color: white;
  font-size: 20px;
  text-decoration: none;
}
/* line 69, ../../../../sass/styles_mobile/2/_rooms.scss */
.rooms_wrapper .room_block .buttons a:nth-child(2) {
  font-size: 16px;
  padding: 8px 40px 7px 20px;
  text-transform: uppercase;
  background-color: #102F57;
  border-radius: 0 5px 5px 0;
}
/* line 75, ../../../../sass/styles_mobile/2/_rooms.scss */
.rooms_wrapper .room_block .buttons a:nth-child(2) i {
  font-size: 20px;
  margin-right: 15px;
}

/* line 1, ../../../../sass/styles_mobile/2/_room_individual.scss */
.individual_room_picture {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
}
/* line 6, ../../../../sass/styles_mobile/2/_room_individual.scss */
.individual_room_picture .individual_room_title {
  font-size: 40px;
  font-weight: normal;
  color: white;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: left;
  margin: 0;
  padding: 20px;
  z-index: 2;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  background: rgba(0, 0, 0, 0.3);
  /* For browsers that do not support gradients */
  background: -webkit-linear-gradient(transparent, rgba(0, 0, 0, 0.5));
  /* For Safari 5.1 to 6.0 */
  background: -o-linear-gradient(transparent, rgba(0, 0, 0, 0.5));
  /* For Opera 11.1 to 12.0 */
  background: -moz-linear-gradient(transparent, rgba(0, 0, 0, 0.5));
  /* For Firefox 3.6 to 15 */
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.5));
  /* Standard syntax */
}
/* line 25, ../../../../sass/styles_mobile/2/_room_individual.scss */
.individual_room_picture img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 33, ../../../../sass/styles_mobile/2/_room_individual.scss */
.booking_room_button_element {
  display: table;
  margin: auto;
  background: #102F57;
  color: white;
  margin-top: 20px;
  padding: 8px 70px;
  font-size: 20px;
  text-transform: uppercase;
  border-radius: 5px;
}

/* line 45, ../../../../sass/styles_mobile/2/_room_individual.scss */
.individual_room_description {
  padding: 10px 30px;
}

/* line 48, ../../../../sass/styles_mobile/2/_room_individual.scss */
.individual_room_gallery {
  padding: 10px 30px;
  width: 100%;
  box-sizing: border-box;
  display: table;
}
/* line 53, ../../../../sass/styles_mobile/2/_room_individual.scss */
.individual_room_gallery .room_picture_element {
  display: inline-block;
  vertical-align: middle;
  width: calc(100% / 3);
  height: 100px;
  overflow: hidden;
  position: relative;
}
/* line 60, ../../../../sass/styles_mobile/2/_room_individual.scss */
.individual_room_gallery .room_picture_element img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  max-height: 120%;
}

/* line 2, ../../../../sass/styles_mobile/2/_location.scss */
.location_content .contact_content_element {
  display: table;
  width: 100%;
  margin-bottom: 10px;
}
/* line 7, ../../../../sass/styles_mobile/2/_location.scss */
.location_content .contact_content_element > div {
  width: 100% !important;
}

/* line 14, ../../../../sass/styles_mobile/2/_location.scss */
.location_wrapper .map iframe {
  width: 100%;
}

/* line 19, ../../../../sass/styles_mobile/2/_location.scss */
.contact_section_wrapper {
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  margin-top: 1em;
}

/* line 26, ../../../../sass/styles_mobile/2/_location.scss */
.contact_content_element {
  line-height: 1.4em;
  padding-bottom: 1em;
  text-align: center;
  margin-top: 2em;
}

/* line 33, ../../../../sass/styles_mobile/2/_location.scss */
.contact_button_wrapper {
  margin-top: 8em;
  margin-bottom: 0.3em;
  padding: 2em;
  background: #ECEAEB;
}

/* line 41, ../../../../sass/styles_mobile/2/_location.scss */
.info input, .info textarea {
  font-size: 15px;
  height: 2.5em;
  background: #eaeaea;
  width: 100%;
  box-sizing: border-box;
  border: 0;
  border-radius: 5px;
  margin-top: 1em;
  padding: 1.6em 2em;
  font-family: "Arial", sans-serif;
}
/* line 53, ../../../../sass/styles_mobile/2/_location.scss */
.info .fieldContain:first-of-type input {
  margin-top: 0;
}
/* line 57, ../../../../sass/styles_mobile/2/_location.scss */
.info textarea {
  height: 8em;
  padding: 1em 2em;
}
/* line 62, ../../../../sass/styles_mobile/2/_location.scss */
.info .check_element {
  margin-top: 1em;
}
/* line 65, ../../../../sass/styles_mobile/2/_location.scss */
.info input[type=checkbox] {
  display: inline-block;
  vertical-align: middle;
  width: auto;
  height: auto;
  margin: auto;
}
/* line 72, ../../../../sass/styles_mobile/2/_location.scss */
.info .title {
  display: inline;
  vertical-align: middle;
}
/* line 75, ../../../../sass/styles_mobile/2/_location.scss */
.info .title a {
  color: #102F57;
  font-size: 0.8em;
}
/* line 80, ../../../../sass/styles_mobile/2/_location.scss */
.info label[for="promotions"] {
  font-size: 0.8em;
}
/* line 85, ../../../../sass/styles_mobile/2/_location.scss */
.info input::-webkit-input-placeholder, .info textarea::-webkit-input-placeholder {
  font-weight: lighter;
  color: gray;
}
/* line 90, ../../../../sass/styles_mobile/2/_location.scss */
.info input::-webkit-input-placeholder, .info textarea::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  font-weight: lighter;
  color: gray;
}
/* line 95, ../../../../sass/styles_mobile/2/_location.scss */
.info input::-moz-placeholder, .info textarea::-moz-placeholder {
  /* Firefox 19+ */
  font-weight: lighter;
  color: gray;
}
/* line 100, ../../../../sass/styles_mobile/2/_location.scss */
.info input:-ms-input-placeholder, .info textarea:-ms-input-placeholder {
  /* IE 10+ */
  font-weight: lighter;
  color: gray;
}
/* line 105, ../../../../sass/styles_mobile/2/_location.scss */
.info input:-moz-placeholder, .info textarea:-moz-placeholder {
  /* Firefox 18- */
  font-weight: lighter;
  color: gray;
}
/* line 111, ../../../../sass/styles_mobile/2/_location.scss */
.info label.error {
  font-size: 0.8em;
  color: red;
}
/* line 116, ../../../../sass/styles_mobile/2/_location.scss */
.info .g-recaptcha div {
  margin: 0 auto;
  width: 100%;
}

/* line 123, ../../../../sass/styles_mobile/2/_location.scss */
#contact {
  padding: 0 20px;
}
/* line 126, ../../../../sass/styles_mobile/2/_location.scss */
#contact .contact_button_wrapper {
  padding: 0;
  width: 100%;
  margin: 20px auto 60px;
}
/* line 131, ../../../../sass/styles_mobile/2/_location.scss */
#contact .contact_button_wrapper #contact-button {
  background-color: #102F57;
  margin: 0 auto;
  width: 100%;
  border: none;
  border-radius: 5px;
  color: white;
  font-size: 22px;
  text-transform: uppercase;
  padding: 10px 0;
  font-family: 'Montserrat', sans-serif;
  -webkit-appearance: none;
  display: block;
  text-align: center;
  line-height: 1;
}

/* line 150, ../../../../sass/styles_mobile/2/_location.scss */
.iframe_google_maps {
  margin: 2em 0;
}

/* line 1, ../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_divided_title {
  position: absolute;
  display: block;
  width: 100%;
  height: 300px;
  box-sizing: border-box;
  z-index: 2;
  color: white;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  background: rgba(0, 0, 0, 0.3);
  /* For browsers that do not support gradients */
  background: -webkit-linear-gradient(transparent, transparent, rgba(0, 0, 0, 0.5));
  /* For Safari 5.1 to 6.0 */
  background: -o-linear-gradient(transparent, transparent, rgba(0, 0, 0, 0.5));
  /* For Opera 11.1 to 12.0 */
  background: -moz-linear-gradient(transparent, transparent, rgba(0, 0, 0, 0.5));
  /* For Firefox 3.6 to 15 */
  background: linear-gradient(transparent, transparent, rgba(0, 0, 0, 0.5));
  /* Standard syntax */
  margin: 0;
  padding: 0;
}
/* line 17, ../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_divided_title span {
  position: absolute;
  bottom: 20px;
  right: 20px;
  left: 20px;
  text-align: right;
}

/* line 25, ../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_1:last-child {
  margin-bottom: -40px;
}

/* line 28, ../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_1 {
  list-style-type: none;
  margin: 0;
  position: relative;
  padding: 0;
  width: 100%;
  display: table;
}
/* line 35, ../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_1 li {
  display: inline-block;
  width: calc(100% / 3);
  height: 150px;
  float: left;
  overflow: hidden;
  position: relative;
}
/* line 42, ../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_1 li.notshow {
  display: none;
}
/* line 45, ../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_1 li img, .gallery_1 li iframe, .gallery_1 li .overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-height: 100%;
  min-width: 100%;
  max-width: none;
}
/* line 51, ../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_1 li .overlay {
  width: 100%;
  height: 100%;
  z-index: 10;
}
/* line 56, ../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_1 li:first-of-type {
  width: 100%;
  height: 300px;
}

/*============== Bottom Pop-up ============*/
/* line 2, ../../../../sass/styles_mobile/2/_newsletter.scss */
.bottom_popup {
  position: fixed;
  width: 100%;
  height: auto;
  background: #fcf0d8;
  left: 0;
  bottom: 0;
  z-index: 1000;
}

/* line 12, ../../../../sass/styles_mobile/2/_newsletter.scss */
.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

/* line 18, ../../../../sass/styles_mobile/2/_newsletter.scss */
.bottom_popup .bottom_popup_text {
  width: 100%;
  color: white;
  font-family: Arial, sans-serif;
  font-size: 14px;
  padding: 10px 5%;
  box-sizing: border-box;
  line-height: 21px;
  text-align: center;
}

/* line 29, ../../../../sass/styles_mobile/2/_newsletter.scss */
.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

/* line 33, ../../../../sass/styles_mobile/2/_newsletter.scss */
.close_button {
  float: right;
  cursor: pointer;
}

/* line 38, ../../../../sass/styles_mobile/2/_newsletter.scss */
button.bottom_popup_button, button.button-promotion {
  width: 120px;
  background: white;
  border: 0;
  height: 36px;
  color: #008BAC;
  background-position: center;
  border-radius: 5px;
  cursor: pointer;
  bottom: 12px;
  font-size: 16px;
  margin: 5px auto 20px;
  display: block;
}

/* line 53, ../../../../sass/styles_mobile/2/_newsletter.scss */
#wrapper2 {
  width: 100%;
  margin: 0 auto;
}

/* line 58, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial {
  background-size: cover !important;
}
/* line 61, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial .fancybox-close-small:after {
  color: white;
}
/* line 65, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial .email, #popup_inicial .discount, #popup_inicial .compra {
  text-align: center;
}
/* line 68, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial .compra {
  padding-top: 15px;
  color: white;
  font-size: 18px;
  line-height: 18px;
  font-weight: lighter;
}
/* line 76, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial .discount {
  padding-top: 15px;
  color: white;
  font-size: 18px;
  line-height: 18px;
  text-transform: uppercase;
  font-family: 'Oswald', sans-serif;
  font-weight: bolder;
}
/* line 86, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial .email {
  padding-top: 39px;
  color: white;
  font-size: 18px;
  line-height: 18px;
  font-weight: lighter;
}
/* line 93, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial form.form_popup {
  text-align: center;
  padding-top: 50px;
}
/* line 97, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial form.form_popup ul {
  list-style: none;
  padding: 0;
}
/* line 102, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial form.form_popup li {
  text-align: center;
  color: white;
}
/* line 105, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial form.form_popup li #promotions {
  display: inline-block;
  vertical-align: middle;
}
/* line 110, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial form.form_popup input#id_email {
  height: 26px;
  text-align: center;
  width: 245px;
  font-size: 17px;
  border: 0;
  color: #008BAC;
  margin-bottom: 5px;
}
/* line 119, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial form.form_popup #accept-term {
  display: inline-block;
  vertical-align: middle;
}
/* line 123, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial form.form_popup a.myFancyPopupPolicy {
  color: white;
  display: inline-block;
  vertical-align: middle;
}
/* line 128, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial form.form_popup button.popup_button {
  margin: 7px auto 0px;
  width: 245px;
  height: 40px;
  background: #008BAC;
  font-size: 17px;
  border: 0px;
  text-transform: uppercase;
  color: white;
  cursor: pointer;
}
/* line 140, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial .spinner_wrapper_faldon {
  padding-top: 20px;
}
/* line 143, ../../../../sass/styles_mobile/2/_newsletter.scss */
#popup_inicial .popup_message {
  color: white;
  padding-top: 25px;
  font-size: 18px;
  line-height: 18px;
  font-weight: lighter;
}

/* line 1, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget {
  border: 1px solid lightgray;
  padding: 15px 0;
  font-size: 15px;
}
/* line 6, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #booking_engine_title {
  display: none;
}
/* line 10, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #contador_noches {
  display: none;
}
/* line 14, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget .colocar_fechas {
  font-size: 15px;
}
/* line 17, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget .colocar_fechas label {
  margin-bottom: 8px;
  display: block;
}
/* line 22, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget .colocar_fechas input {
  text-align: center !important;
}
/* line 27, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #fecha_entrada {
  float: left;
  width: 48%;
}
/* line 32, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #fecha_salida {
  float: right;
  width: 48%;
}
/* line 37, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget fieldset {
  border: 0;
}
/* line 41, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #contenedor_habitaciones {
  width: 100%;
  display: table;
  box-sizing: border-box;
  padding: 20px;
  position: relative;
}
/* line 48, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #contenedor_habitaciones:before, .modify_reservation_widget #contenedor_habitaciones:after {
  content: '';
  position: absolute;
  top: 0;
  left: 20%;
  right: 20%;
  border-top: 1px solid lightgray;
}
/* line 57, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #contenedor_habitaciones:after {
  top: auto;
  bottom: 0;
}
/* line 62, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #contenedor_habitaciones label {
  float: left;
  margin-top: 10px;
}
/* line 67, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #contenedor_habitaciones select {
  float: right;
  padding: 10px 50px;
  vertical-align: middle;
  text-align: right;
  background-color: #eaeaea;
  border: 0;
  border-radius: 5px;
}
/* line 78, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #contenedor_opciones {
  width: 100%;
  display: table;
  box-sizing: border-box;
  padding: 20px;
  position: relative;
}
/* line 85, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #contenedor_opciones > div[style='display: block;'] {
  display: table !important;
  width: 100%;
  padding-top: 15px;
}
/* line 93, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #contenedor_opciones .numero_habitacion {
  text-decoration: underline;
  margin-bottom: 10px;
  display: block;
}
/* line 99, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #contenedor_opciones .numero_personas {
  float: left;
  width: 33%;
  text-align: left;
  position: relative;
}
/* line 105, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #contenedor_opciones .numero_personas select {
  display: block;
  padding: 10px 0;
  vertical-align: middle;
  text-align: right;
  background-color: #eaeaea;
  border: 0;
  border-radius: 5px;
  box-sizing: border-box;
  width: 90%;
}
/* line 117, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #contenedor_opciones .numero_personas #info_ninos {
  position: absolute;
  top: 4px;
  font-size: 10px;
  left: 50%;
}
/* line 126, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #envio {
  padding-top: 20px;
  position: relative;
}
/* line 130, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #envio:before {
  content: '';
  position: absolute;
  top: 0;
  left: 20%;
  right: 20%;
  border-top: 1px solid lightgray;
}
/* line 140, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget input[type=text] {
  padding: 15px 30px !important;
}
/* line 145, ../../../../sass/styles_mobile/2/_modification_my_reservation.scss */
.modify_reservation_widget #search-button {
  display: block;
  padding: 10px 0;
  box-sizing: border-box;
  font-size: 22px;
  text-transform: uppercase;
  width: 100%;
  border-radius: 5px;
  margin: auto;
  background-color: #0A80A8;
  color: white;
  margin-bottom: 10px;
  border: 0;
}

@-webkit-keyframes blink {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* Standard syntax */
@keyframes blink {
  0% {
    margin-bottom: 50px;
    opacity: 0;
  }
  100% {
    margin-bottom: 0;
    opacity: 1;
  }
}
/* line 48, ../../../../sass/styles_mobile/2/2.scss */
body {
  padding: 80px 0 100px 0;
  margin: 0;
  font-size: 10px;
  font-family: 'Montserrat', sans-serif;
}
/* line 53, ../../../../sass/styles_mobile/2/2.scss */
body a {
  text-decoration: none;
  outline: 0;
}
/* line 58, ../../../../sass/styles_mobile/2/2.scss */
body .main-owlslider {
  height: calc(100vh - 120px);
}
/* line 60, ../../../../sass/styles_mobile/2/2.scss */
body .main-owlslider .owl-stage-outer, body .main-owlslider .owl-stage, body .main-owlslider .owl-item {
  height: 100%;
}
/* line 63, ../../../../sass/styles_mobile/2/2.scss */
body .main-owlslider .owl-item {
  position: relative;
  overflow: hidden;
}
/* line 66, ../../../../sass/styles_mobile/2/2.scss */
body .main-owlslider .owl-item img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  width: auto;
  max-width: none;
}
/* line 74, ../../../../sass/styles_mobile/2/2.scss */
body .main-owlslider .description_text {
  position: absolute;
  top: 10px;
  left: 20px;
  right: 20px;
  text-align: center;
}
/* line 83, ../../../../sass/styles_mobile/2/2.scss */
body .scrolldown {
  position: absolute;
  bottom: 10vh;
  left: 0;
  right: 0;
  z-index: 20;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  text-align: center;
  color: white;
  font-size: 30px;
  cursor: pointer;
  animation-name: blink;
  animation-duration: 2s;
  animation-iteration-count: infinite;
}
/* line 99, ../../../../sass/styles_mobile/2/2.scss */
body .banners_scrool {
  padding-top: 5px;
  max-height: 50vw;
  overflow: hidden;
}
/* line 103, ../../../../sass/styles_mobile/2/2.scss */
body .banners_scrool .owl-item {
  position: relative;
}
/* line 106, ../../../../sass/styles_mobile/2/2.scss */
body .banners_scrool .banner_element {
  text-decoration: none;
  padding: 0;
}
/* line 109, ../../../../sass/styles_mobile/2/2.scss */
body .banners_scrool .banner_element img.banner_image {
  width: 100%;
  margin: auto;
}
/* line 113, ../../../../sass/styles_mobile/2/2.scss */
body .banners_scrool .banner_element .banner_bottom_title {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  text-transform: uppercase;
  background-color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  padding: 5px 10px;
  text-align: center;
  color: white;
}
/* line 125, ../../../../sass/styles_mobile/2/2.scss */
body .section_content {
  color: #666;
  text-align: center;
  font-size: 18px;
  padding-top: 35px;
}
/* line 130, ../../../../sass/styles_mobile/2/2.scss */
body .section_content h1, body .section_content h2.section_title {
  font-weight: 100;
  font-size: 38px;
  margin-top: 0;
  margin-bottom: 20px;
  text-align: center;
}
/* line 136, ../../../../sass/styles_mobile/2/2.scss */
body .section_content h1 span, body .section_content h2.section_title span {
  color: #102F57;
}
/* line 140, ../../../../sass/styles_mobile/2/2.scss */
body .section_content .content {
  padding: 20px;
}
/* line 145, ../../../../sass/styles_mobile/2/2.scss */
body .normal_section_mobile .section-content {
  padding: 0 30px;
}
/* line 148, ../../../../sass/styles_mobile/2/2.scss */
body .normal_section_mobile .section-content ul {
  padding-left: 0;
}
/* line 150, ../../../../sass/styles_mobile/2/2.scss */
body .normal_section_mobile .section-content ul li {
  list-style: none;
}
/* line 156, ../../../../sass/styles_mobile/2/2.scss */
body .my_reservation_section {
  padding-bottom: 60px;
}
/* line 158, ../../../../sass/styles_mobile/2/2.scss */
body .my_reservation_section .my-reservation-form {
  padding: 0 20px;
}
/* line 160, ../../../../sass/styles_mobile/2/2.scss */
body .my_reservation_section .my-reservation-form input[type=text] {
  font-size: 15px;
  padding: 1.6em 2em;
  border-width: 0;
  background-color: #eaeaea;
  box-sizing: border-box;
  width: 100%;
  border-radius: 5px;
  text-align: left;
  margin: auto;
  margin-bottom: 10px;
}
/* line 171, ../../../../sass/styles_mobile/2/2.scss */
body .my_reservation_section .my-reservation-form input[type=text]::-webkit-input-placeholder {
  font-weight: lighter;
  color: gray;
}
/* line 175, ../../../../sass/styles_mobile/2/2.scss */
body .my_reservation_section .my-reservation-form input[type=text]::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  font-weight: lighter;
  color: gray;
}
/* line 180, ../../../../sass/styles_mobile/2/2.scss */
body .my_reservation_section .my-reservation-form input[type=text]::-moz-placeholder {
  /* Firefox 19+ */
  font-weight: lighter;
  color: gray;
}
/* line 185, ../../../../sass/styles_mobile/2/2.scss */
body .my_reservation_section .my-reservation-form input[type=text]:-ms-input-placeholder {
  /* IE 10+ */
  font-weight: lighter;
  color: gray;
}
/* line 190, ../../../../sass/styles_mobile/2/2.scss */
body .my_reservation_section .my-reservation-form input[type=text]:-moz-placeholder {
  /* Firefox 18- */
  font-weight: lighter;
  color: gray;
}
/* line 196, ../../../../sass/styles_mobile/2/2.scss */
body .my_reservation_section .my-reservation-form a[data-role=button] {
  display: block;
  padding: 10px 0;
  box-sizing: border-box;
  font-size: 22px;
  text-transform: uppercase;
  width: 100%;
  border-radius: 5px;
  margin: auto;
  background-color: #102F57;
  color: white;
  margin-bottom: 10px;
}
/* line 209, ../../../../sass/styles_mobile/2/2.scss */
body .my_reservation_section .my-reservation-form #cancelButton a[data-role=button] {
  background-color: #900;
}
/* line 212, ../../../../sass/styles_mobile/2/2.scss */
body .my_reservation_section .my-reservation-form .my-bookings-booking-info, body .my_reservation_section .my-reservation-form .fResumenReserva {
  max-width: 100%;
  box-sizing: border-box;
  margin: 10px 0;
}
/* line 220, ../../../../sass/styles_mobile/2/2.scss */
body .social_footer {
  margin: 50px auto;
  text-align: center;
}
/* line 223, ../../../../sass/styles_mobile/2/2.scss */
body .social_footer a {
  display: inline-block;
  position: relative;
  background-color: #F2B849;
  width: 50px;
  height: 50px;
  box-sizing: border-box;
  margin: 0 10px;
}
/* line 231, ../../../../sass/styles_mobile/2/2.scss */
body .social_footer a .fa {
  font-size: 20px;
  padding: 15px 0;
  color: white;
}
/* line 239, ../../../../sass/styles_mobile/2/2.scss */
body .default_reservation_text {
  padding: 0 30px;
}
/* line 242, ../../../../sass/styles_mobile/2/2.scss */
body .ventajas_btn {
  position: fixed;
  right: 0;
  top: 30%;
  z-index: 30;
}
/* line 249, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper {
  padding: 30px;
  box-sizing: border-box;
  margin-top: 20px;
  background: #ececec;
}
/* line 255, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper .cycle_element {
  width: 100%;
  display: inline-block;
  margin-bottom: 40px;
}
/* line 260, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper .cycle_element:last-of-type {
  margin-bottom: 10px;
}
/* line 266, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper .cycle_element:nth-child(even) .cycle_image_wrapper .cycle_image {
  float: left;
  border-radius: 5px 0 0 0px;
}
/* line 271, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper .cycle_element:nth-child(even) .cycle_image_wrapper .cycle_title {
  float: right;
  border-radius: 0px 5px 0px 0;
}
/* line 276, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper .cycle_element:nth-child(even) .cycle_image_wrapper .cycle_see_wrapper {
  right: 0;
  left: auto;
}
/* line 283, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper .cycle_element .cycle_image_wrapper {
  display: inline-block;
  width: 100%;
  float: left;
  position: relative;
}
/* line 289, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper .cycle_element .cycle_image_wrapper .cycle_image {
  display: inline-block;
  width: 40%;
  height: 150px;
  float: right;
  position: relative;
  overflow: hidden;
  border-radius: 0px 5px 0px 0;
}
/* line 298, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper .cycle_element .cycle_image_wrapper .cycle_image img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 303, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper .cycle_element .cycle_image_wrapper .cycle_title {
  display: inline-block;
  width: 60%;
  float: left;
  background: #F9F8F6;
  height: 150px;
  position: relative;
  overflow: hidden;
  border-radius: 5px 0 0 0px;
}
/* line 313, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper .cycle_element .cycle_image_wrapper .cycle_title span {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: 100%;
  left: 0;
  padding: 0 10px;
  box-sizing: border-box;
}
/* line 322, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper .cycle_element .cycle_image_wrapper .cycle_see_wrapper {
  position: absolute;
  bottom: 15px;
  width: 60%;
  display: inline-block;
  left: 0;
}
/* line 329, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper .cycle_element .cycle_image_wrapper .cycle_see_wrapper a {
  color: white;
  background: #383838;
  padding: 5px 10px;
  border-radius: 5px;
  display: table;
}
/* line 336, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper .cycle_element .cycle_image_wrapper .cycle_see_wrapper a i {
  margin-right: 5px;
}
/* line 343, ../../../../sass/styles_mobile/2/2.scss */
body .cycle_banners_wrapper .cycle_element .cycle_description {
  font-size: 14px;
  text-align: left;
  display: inline-block;
  float: left;
  background: white;
  padding: 30px 20px 20px;
  display: none;
  overflow: hidden;
  border-radius: 0 0 5px 5px;
}
/* line 357, ../../../../sass/styles_mobile/2/2.scss */
body .minigallery_wrapper {
  position: relative;
  margin-top: 20px;
}
/* line 361, ../../../../sass/styles_mobile/2/2.scss */
body .minigallery_wrapper ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
/* line 367, ../../../../sass/styles_mobile/2/2.scss */
body .minigallery_wrapper .minigallery_carousel {
  position: relative;
  height: 300px;
}
/* line 371, ../../../../sass/styles_mobile/2/2.scss */
body .minigallery_wrapper .minigallery_carousel .owl-item {
  height: 300px;
  overflow: hidden;
}
/* line 376, ../../../../sass/styles_mobile/2/2.scss */
body .minigallery_wrapper .minigallery_carousel img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  height: 100%;
  max-width: none;
  width: auto;
}
/* line 384, ../../../../sass/styles_mobile/2/2.scss */
body .minigallery_wrapper .owl-prev {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  width: 50px;
}
/* line 392, ../../../../sass/styles_mobile/2/2.scss */
body .minigallery_wrapper .owl-prev .fa {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: white;
  font-size: 20px;
}
/* line 399, ../../../../sass/styles_mobile/2/2.scss */
body .minigallery_wrapper .owl-next {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  width: 50px;
}
/* line 407, ../../../../sass/styles_mobile/2/2.scss */
body .minigallery_wrapper .owl-next .fa {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: white;
  font-size: 20px;
}

@media (max-width: 380px) {
  /* line 420, ../../../../sass/styles_mobile/2/2.scss */
  body .cycle_banners_wrapper .cycle_element .cycle_image_wrapper .cycle_image {
    width: 100%;
    border-radius: 0 !important;
  }
  /* line 425, ../../../../sass/styles_mobile/2/2.scss */
  body .cycle_banners_wrapper .cycle_element .cycle_image_wrapper .cycle_title {
    width: 100%;
    height: auto;
    padding: 10px 15px;
    box-sizing: border-box;
    border-radius: 5px 5px 0 0 !important;
  }
  /* line 432, ../../../../sass/styles_mobile/2/2.scss */
  body .cycle_banners_wrapper .cycle_element .cycle_image_wrapper .cycle_title span {
    position: relative;
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    top: 0;
    padding: 0;
  }
}
/* line 445, ../../../../sass/styles_mobile/2/2.scss */
.container_popup_booking img {
  background-color: #777;
}

/* line 451, ../../../../sass/styles_mobile/2/2.scss */
.popup_booking_general .container_popup_booking_general {
  text-align: center;
}
/* line 454, ../../../../sass/styles_mobile/2/2.scss */
.popup_booking_general .container_popup_booking_general img {
  background: #102F57;
  max-width: 80px;
}
/* line 459, ../../../../sass/styles_mobile/2/2.scss */
.popup_booking_general .container_popup_booking_general .description_popup_booking_general {
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
}

/*=== Slider Small ===*/
/* line 470, ../../../../sass/styles_mobile/2/2.scss */
.main-owlslider.slider-small {
  height: calc(48vh - 90px);
}

/*=== Increase button to fit iphone5 ===*/
@media screen and (device-aspect-ratio: 40 / 71) {
  /* line 481, ../../../../sass/styles_mobile/2/2.scss */
  .promotions_wrapper .offer_content .offer_links_wrapper:lang(en) {
    width: 90%;
  }
}
/* line 1, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 9000;
  background-color: rgba(16, 47, 87, 0.8);
}
/* line 6, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .overlay {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
/* line 10, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 800px;
  max-height: 90%;
  background-color: white;
}
/* line 15, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer {
  clear: both;
  display: table;
  width: 100%;
  box-sizing: border-box;
  padding: 10px;
  background-color: #102F57;
}
/* line 22, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer .search_hotels {
  position: relative;
  width: 50%;
  box-sizing: border-box;
  padding: 0 30px 0 20px;
  display: inline-block;
}
/* line 28, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer .search_hotels .search_hotels_selector {
  font-size: 14px;
  width: 100%;
  outline: 0;
  border-width: 0;
  color: #4695C7;
  background-color: white;
  box-sizing: border-box;
  padding: 9px 10px 8px;
}
/* line 36, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer .search_hotels .search_hotels_selector::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #4695C7;
}
/* line 39, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer .search_hotels .search_hotels_selector::-moz-placeholder {
  /* Firefox 19+ */
  color: #4695C7;
}
/* line 42, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer .search_hotels .search_hotels_selector:-ms-input-placeholder {
  /* IE 10+ */
  color: #4695C7;
}
/* line 45, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer .search_hotels .search_hotels_selector:-moz-placeholder {
  /* Firefox 18- */
  color: #4695C7;
}
/* line 49, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer .search_hotels:after {
  content: '\f107';
  font-family: "fontawesome", sans-serif;
  display: block;
  background-color: #4695C7;
  padding: 9px 12px;
  color: white;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 30px;
}
/* line 59, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer .search_hotels .all_hotels_list_search {
  position: absolute;
  left: 20px;
  z-index: 3;
  padding: 10px;
  bottom: 35px;
  width: 340px;
  box-sizing: border-box;
  background-color: #F0F0F0;
  overflow: auto;
}
/* line 69, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer .search_hotels .all_hotels_list_search a {
  position: relative;
  display: inline-block;
  cursor: pointer;
  color: #4695C7;
  padding: 0 3px 3px;
  font-size: 11px;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 77, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer .search_hotels .all_hotels_list_search a br {
  display: none;
}
/* line 78, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer .search_hotels .all_hotels_list_search a strong {
  font-weight: normal;
  color: #ccc;
}
/* line 79, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer .search_hotels .all_hotels_list_search a:hover {
  color: #F2B849;
}
/* line 81, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer .search_hotels .all_hotels_list_search a:hover strong {
  color: #ccc;
}
/* line 88, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .selector_view_footer .close_hotel_selector {
  float: right;
  background-color: #4695C7;
  padding: 7px 12px;
  color: white;
  cursor: pointer;
}
/* line 96, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .group {
  width: 50%;
  float: left;
}
/* line 99, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .group .group_image {
  position: relative;
  background-color: black;
  width: 100%;
  height: 100px;
  overflow: hidden;
}
/* line 105, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .group .group_image img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  opacity: .5;
}
/* line 109, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .group .group_image h2 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 80%;
  text-transform: uppercase;
  font-weight: lighter;
  text-align: center;
  border: 1px solid white;
  padding: 10px 0;
  color: white;
  font-size: 16px;
}
/* line 123, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .group .group_destiny {
  width: 100%;
  margin-top: 20px;
  height: 405px;
  overflow: auto;
}
/* line 129, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .group .destiny {
  padding: 5px 30px;
}
/* line 131, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .group .destiny h3 {
  position: relative;
  text-align: center;
  border: 1px solid #4695C7;
  padding: 5px;
  margin-bottom: 3px;
  font-size: 14px;
  color: #4695C7;
}
/* line 140, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .group .destiny a {
  position: relative;
  display: block;
  cursor: pointer;
  color: #F2B849;
  padding: 2px;
  font-size: 12px;
  text-align: center;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 149, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .group .destiny a br {
  display: none;
}
/* line 150, ../sass/mobile/_hotel_list_mobile.scss */
.hotel_list_wrapper .selector_view .group .destiny a:hover {
  color: #102F57;
}

/* line 158, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_filter_wrapper {
  position: absolute;
  top: 100px;
  left: 0;
  right: 0;
  padding-top: 10px;
  width: 100%;
  background: rgba(0, 0, 0, 0.8);
  /* For browsers that do not support gradients */
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  /* Standard syntax (must be last) */
}
/* line 168, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_filter_wrapper a {
  display: inline-block;
  color: white;
  font-size: 0.588em;
  text-transform: uppercase;
  padding: 2em 0 1em;
  margin-right: 1em;
  border-bottom: 5px solid transparent;
}
/* line 176, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_filter_wrapper a.filter.active, .hotels_filter_wrapper a.filter:hover {
  border-bottom-color: #F2B849;
}
/* line 179, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_filter_wrapper a.right_link {
  position: relative;
  float: right;
  padding: 8px 35px 4px 0;
}
/* line 183, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_filter_wrapper a.right_link .fa {
  position: absolute;
  top: 0;
  right: 0;
  background-color: white;
  padding: 10px;
  color: #4b4b4b;
}
/* line 191, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_filter_wrapper a.right_link:hover .fa, .hotels_filter_wrapper a.right_link.active .fa {
  color: #F2B849;
}

/* line 199, ../sass/mobile/_hotel_list_mobile.scss */
.map {
  margin: 75px 0;
}

/* line 204, ../sass/mobile/_hotel_list_mobile.scss */
.all_hotels_map_wrapper .gm-style-iw strong {
  font-weight: bold;
}
/* line 207, ../sass/mobile/_hotel_list_mobile.scss */
.all_hotels_map_wrapper .gm-style-iw a {
  margin-top: 10px;
  display: block;
  padding: 5px 10px;
  background-color: #F2B849;
  color: white !important;
  text-decoration: none;
  text-align: center;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 216, ../sass/mobile/_hotel_list_mobile.scss */
.all_hotels_map_wrapper .gm-style-iw a:hover {
  background-color: #4695C7;
}

/* line 223, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper {
  padding: 75px 0;
}
/* line 225, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel {
  display: block;
  vertical-align: top;
  margin: 0 auto;
  width: 100%;
}
/* line 230, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_image {
  background-color: black;
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 237, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_image img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 245, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info {
  background-color: white;
  padding: 20px;
  font-size: 12px;
  position: relative;
  color: #4b4b4b;
  margin: -6.250em 4.688em 1.875em 1.250em;
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}
/* line 253, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info .new_icon {
  position: absolute;
  top: 20px;
  right: 20px;
}
/* line 258, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info h2 {
  color: #102F57;
  text-transform: uppercase;
  font-weight: lighter;
  font-size: 20px;
  margin-bottom: 5px;
}
/* line 264, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info h2 br {
  display: none;
}
/* line 266, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info h3 {
  color: #666;
  font-size: 16px;
  font-weight: normal;
  margin-bottom: 10px;
}
/* line 272, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info p {
  font-size: 12px;
  font-family: "Open Sans", sans-serif;
}
/* line 276, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info .half {
  display: inline-block;
  vertical-align: bottom;
  width: calc(50% - 2px);
}
/* line 280, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info .half.servis {
  color: #4695C7;
}
/* line 282, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info .half.servis a {
  color: #4695C7;
}
/* line 284, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info .half.servis a:hover {
  text-decoration: underline;
}
/* line 288, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info .half.servis .fa {
  display: inline-block;
  vertical-align: middle;
  margin-right: 3px;
  margin-bottom: 3px;
  position: relative;
  width: 15px;
  height: 15px;
  border: 1px solid #F2B849;
  color: #F2B849;
  border-radius: 50%;
}
/* line 299, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info .half.servis .fa:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 304, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info .half .price {
  font-size: 14px;
  color: #4695C7;
}
/* line 307, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info .half .price span {
  color: #4b4b4b;
  font-size: 30px;
  font-weight: bold;
}
/* line 313, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info .half .button-promotion {
  display: block;
  padding: 5px 10px;
  text-align: center;
  text-transform: uppercase;
  color: white;
  margin-top: 10px;
  background-color: #F2B849;
}
/* line 321, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel .hotel_info .half .button-promotion:hover {
  background-color: #102F57;
}
/* line 329, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel:hover .hotel_image img {
  -webkit-filter: blur(5px);
  /* Safari */
  filter: blur(5px);
  opacity: .6;
  transform: translate(-50%, -50%) scale(1.3);
}
/* line 336, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel:hover .hotel_info {
  margin-top: -200px;
}
/* line 341, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel.destacado .hotel_info {
  background-color: #102F57;
}
/* line 343, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel.destacado .hotel_info h2 {
  color: #4695C7;
}
/* line 346, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel.destacado .hotel_info h3, .hotels_wrapper .hotel.destacado .hotel_info p {
  color: #ddd;
}
/* line 350, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel.destacado .hotel_info .price span {
  color: white;
}
/* line 355, ../sass/mobile/_hotel_list_mobile.scss */
.hotels_wrapper .hotel.destacado .hotel_info .button-promotion:hover {
  background-color: #4695C7;
}

/* line 1, ../sass/mobile/_destiny_list_mobile.scss */
.destiny_wrapper {
  padding-top: 0;
  background-color: #f6f6f6;
}
/* line 4, ../sass/mobile/_destiny_list_mobile.scss */
.destiny_wrapper .destiny {
  display: inline-block;
  vertical-align: top;
  margin: 0 12px;
  width: 100%;
}
/* line 9, ../sass/mobile/_destiny_list_mobile.scss */
.destiny_wrapper .destiny .destiny_image {
  position: relative;
  width: 100%;
  height: 400px;
}
/* line 13, ../sass/mobile/_destiny_list_mobile.scss */
.destiny_wrapper .destiny .destiny_image img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 20, ../sass/mobile/_destiny_list_mobile.scss */
.destiny_wrapper .destiny .destiny_info {
  background-color: white;
  padding: 30px;
  font-size: 12px;
  font-family: "Open Sans", sans-serif;
  position: relative;
  color: #4b4b4b;
  margin: -250px 50px 30px 50px;
}
/* line 28, ../sass/mobile/_destiny_list_mobile.scss */
.destiny_wrapper .destiny .destiny_info .new_icon {
  position: absolute;
  top: 20px;
  right: 20px;
}
/* line 33, ../sass/mobile/_destiny_list_mobile.scss */
.destiny_wrapper .destiny .destiny_info h2 {
  color: #102F57;
  text-transform: uppercase;
  font-weight: lighter;
  font-size: 20px;
  margin-bottom: 5px;
}
/* line 39, ../sass/mobile/_destiny_list_mobile.scss */
.destiny_wrapper .destiny .destiny_info h2 br {
  display: none;
}
/* line 41, ../sass/mobile/_destiny_list_mobile.scss */
.destiny_wrapper .destiny .destiny_info p {
  font-size: 12px;
}
/* line 44, ../sass/mobile/_destiny_list_mobile.scss */
.destiny_wrapper .destiny .destiny_info a.toggle_hotels {
  display: inline-block;
  color: #F2B849;
  border-bottom: 1px solid #F2B849;
  text-transform: uppercase;
  margin: 20px 0 0;
}
/* line 50, ../sass/mobile/_destiny_list_mobile.scss */
.destiny_wrapper .destiny .destiny_info a.toggle_hotels:after {
  content: '\f107';
  font-family: "FontAwesome", sans-serif;
  font-size: 130%;
  margin-right: -20px;
  float: right;
}

/* line 2, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .content_subtitle_description {
  background-position: center 300px;
  background-size: cover;
}
/* line 7, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .ventajas_club {
  padding: 0 0 1em;
  text-align: center;
}
/* line 10, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .ventajas_club h2 {
  font-size: 20px;
  color: #102F57;
  text-transform: uppercase;
  padding: 2em 1em 1em;
}
/* line 16, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .ventajas_club ul.list_style_ohtels {
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
}
/* line 20, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .ventajas_club ul.list_style_ohtels li {
  display: block;
  vertical-align: top;
  font-size: 14px;
  font-family: "Open Sans", sans-serif;
  line-height: 16px;
  text-align: left;
  margin: 0 auto;
  color: #666;
  width: 70%;
}
/* line 30, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .ventajas_club ul.list_style_ohtels li:before {
  content: '';
  display: inline-block;
  vertical-align: middle;
  width: 18px;
  height: 18px;
  margin-left: -21px;
  background-position: center;
  background-size: cover;
  background-image: url("/img/ohtel/liststyle_ohtels.png");
}
/* line 44, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .form_club {
  padding: 0 0 1em;
  color: white;
  text-align: center;
}
/* line 48, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .form_club h3 {
  font-size: 60px;
  font-weight: lighter;
  margin-bottom: 30px;
}
/* line 53, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .form_club p {
  font-size: 14px;
  font-family: "Open Sans", sans-serif;
  padding: 0 1em;
}
/* line 59, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .alta_club {
  margin: auto;
  width: 100%;
  padding: 0 1em;
  box-sizing: border-box;
}
/* line 64, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .alta_club .contInput {
  width: 100%;
  display: block;
}
/* line 67, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .alta_club .contInput label {
  color: white;
  font-size: 14px;
  display: block;
  margin-top: 20px;
  margin-bottom: 5px;
  text-transform: uppercase;
}
/* line 75, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .alta_club .contInput .error {
  color: #F2B849;
}
/* line 78, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .alta_club .contInput input[type=text], .section_club_wrapper .alta_club .contInput select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
  border: 1px solid white;
  background: transparent;
  color: white;
  padding: 0 10px;
  box-sizing: border-box;
}
/* line 89, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .alta_club .contInput input[type=text] {
  width: 100%;
  font-size: 12px;
  padding: 10px;
}
/* line 94, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .alta_club .contInput select[name=birthDay], .section_club_wrapper .alta_club .contInput select[name=birthMonth], .section_club_wrapper .alta_club .contInput select[name=birthYear] {
  width: calc(33% - 5px);
  margin-right: 5px;
  height: 37px;
  float: left;
}
/* line 100, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .alta_club .contInput select[name=birthYear] {
  width: 33%;
  margin-right: 0;
}
/* line 104, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .alta_club .contInput.fullInput {
  width: 100%;
}
/* line 108, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .alta_club #response_alta {
  color: white;
}
/* line 111, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .alta_club .capcha, .section_club_wrapper .alta_club #contact-button-wrapper {
  margin-top: 30px;
}
/* line 114, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .alta_club #contact-button-wrapper {
  width: 100%;
  margin: 0 auto 1em;
  box-sizing: border-box;
  display: block;
}
/* line 119, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .alta_club #contact-button-wrapper #contact-button {
  cursor: pointer;
  background: white;
  padding: 28px 10px;
  text-transform: uppercase;
  text-align: center;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 126, ../sass/mobile/_section_club_mobile.scss */
.section_club_wrapper .alta_club #contact-button-wrapper #contact-button:hover {
  padding-right: 80px;
  background-color: #F2B849;
  background-image: url("/img/ohtel/sending_email_2.gif");
  background-position: right center;
  background-size: contain;
  background-repeat: no-repeat;
}

/* line 1, ../sass/mobile/_bannerx3_mobile.scss */
.bannerx3_wrapper {
  display: flex;
  width: 100%;
  padding: 1em 1em 0;
  justify-content: space-between;
  box-sizing: border-box;
}
/* line 7, ../sass/mobile/_bannerx3_mobile.scss */
.bannerx3_wrapper .banner {
  display: block;
  padding: 0 1em;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  box-sizing: border-box;
}
/* line 14, ../sass/mobile/_bannerx3_mobile.scss */
.bannerx3_wrapper .banner h2 {
  color: #102F57;
  font-size: 0.7em;
  padding: 10px 0;
}
/* line 19, ../sass/mobile/_bannerx3_mobile.scss */
.bannerx3_wrapper .banner p {
  font-size: 12px;
  font-family: "Open Sans", sans-serif;
  line-height: 18px;
  color: #4b4b4b;
  padding: 0 20px;
}

/* line 1, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper {
  display: block;
  margin: 0 auto;
  padding-bottom: 15px;
  width: 100%;
}
/* line 7, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotion_wrapper {
  width: 860px;
  float: right;
  height: auto;
  overflow: hidden;
}
/* line 14, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .see_more_offers_button {
  display: block;
  clear: both;
  color: #4695C7;
  width: 1140px;
  text-align: center;
  text-transform: uppercase;
  padding: 10px 0;
  font-size: 21px;
  cursor: pointer;
  margin: auto;
  box-sizing: border-box;
  padding-left: 280px;
  position: relative;
  margin-bottom: 40px;
}
/* line 30, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .see_more_offers_button:before {
  content: '';
  position: absolute;
  width: 280px;
  top: 0;
  left: 0;
  bottom: 0;
  background: white;
}
/* line 40, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .see_more_offers_button:after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 860px;
  border: 1px solid #4695C7;
}
/* line 51, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotion_element {
  width: 100%;
  height: 360px;
  position: relative;
  overflow: hidden;
  margin-bottom: 1em;
}
/* line 58, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotion_element:nth-of-type(3n) {
  margin-right: 0;
}
/* line 62, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotion_element .promotion_background {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 66, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotion_element h3.promotion_title {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: 3;
  width: 100%;
  text-align: center;
  padding: 7px 0 50px;
  color: white;
  font-size: 20px;
}
/* line 75, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotion_element .promotion_description {
  position: absolute;
  top: 90px;
  z-index: 3;
  color: white;
  text-align: center;
  padding: 0 30px;
  font-size: 13px;
  line-height: 20px;
  font-weight: 100;
  opacity: 0;
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  -ms-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
  transition: all 0.5s linear;
}
/* line 89, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotion_element .promotion_overlay {
  background: black;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 3;
  opacity: .5;
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  -ms-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
  transition: all 0.5s linear;
}
/* line 101, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotion_element .hotels_available {
  position: absolute;
  bottom: 5px;
  left: 5px;
  right: 5px;
  text-align: center;
  background: #F2B849;
  z-index: 3;
  font-size: 14px;
  font-weight: 500;
  color: white;
  line-height: 45px;
}
/* line 116, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotion_element:hover .promotion_overlay {
  opacity: 0.8;
}
/* line 119, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotion_element:hover .hotels_available {
  background-color: #4695C7;
}
/* line 122, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotion_element:hover .promotion_description {
  opacity: 1;
}
/* line 128, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotions_filters_wrapper {
  width: 100%;
  /*float: left;*/
  background-color: #102F57;
}
/* line 133, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotions_filters_wrapper .filters_offers_title {
  background: #317abe;
}
/* line 136, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotions_filters_wrapper .filters_offers_title .zoom_ico, .full_promotions_wrapper .promotions_filters_wrapper .filters_offers_title .filter_title_element {
  display: inline-block;
  vertical-align: middle;
}
/* line 141, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotions_filters_wrapper .filters_offers_title .zoom_ico {
  margin: 5px 10px;
}
/* line 146, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotions_filters_wrapper .destiny_offer_filter, .full_promotions_wrapper .promotions_filters_wrapper .types_offer_filter {
  padding: 15px 15px;
  text-transform: uppercase;
  color: white;
  font-size: 20px;
}
/* line 153, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotions_filters_wrapper p.filter_main {
  margin-bottom: 20px;
  font-weight: 500;
}
/* line 158, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotions_filters_wrapper .filter_title_element {
  font-size: 20px;
  padding: 20px;
  text-transform: uppercase;
  color: white;
}
/* line 165, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotions_filters_wrapper .filter {
  margin-bottom: 4px;
  cursor: pointer;
  display: inline-block;
}
/* line 170, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotions_filters_wrapper .filter .destiny_title {
  display: inline-block;
  color: rgba(255, 255, 255, 0.8);
  text-transform: none;
  font-weight: 300;
  font-size: 14px;
  vertical-align: middle;
}
/* line 178, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotions_filters_wrapper .filter .select_box {
  width: 15px;
  height: 15px;
  border: 1px solid lightgray;
  border-radius: 5px;
  vertical-align: middle;
  display: inline-block;
  margin-right: 4px;
  position: relative;
}
/* line 188, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .promotions_filters_wrapper .filter .select_box.active:before {
  content: "\f00c";
  font-family: "fontawesome", sans-serif;
  font-size: 8px;
  color: #F2B849;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 200, ../sass/mobile/_promotions_section_mobile.scss */
.full_promotions_wrapper .main_promotions_title {
  font-size: 16px;
  font-weight: 400;
  color: #0b5294;
  margin-bottom: 12px;
  text-decoration: none;
}

/* line 1, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper {
  position: relative;
  width: 100%;
  margin: 1em auto 0;
  padding: 1em 0;
  text-align: center;
  border: 1px solid lightgrey;
  box-sizing: border-box;
}
/* line 9, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper .divInput {
  display: block;
  vertical-align: top;
  width: 70%;
  margin: 0 auto;
}
/* line 14, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper .divInput label {
  color: #4B4B4B;
  font-size: 12px;
}
/* line 17, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper .divInput label.error {
  display: inline-block;
  padding: 5px 10px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 5px;
  color: #721c24;
}
/* line 24, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper .divInput label.error .fa {
  color: black;
  font-size: 16px;
  margin-right: 10px;
}
/* line 30, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper .divInput label strong {
  font-weight: bold;
}
/* line 34, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper .divInput input[type=text], .cv_form_wrapper .divInput input[type=file], .cv_form_wrapper .divInput select {
  display: block;
  width: 100%;
  padding: 10px;
  height: 33px;
  box-sizing: border-box;
  margin-bottom: 10px;
  border-width: 0;
  border-radius: 3px;
  color: #4B4B4B;
  background: #ededed;
}
/* line 45, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper .divInput input[type=text].error, .cv_form_wrapper .divInput input[type=file].error, .cv_form_wrapper .divInput select.error {
  border: 1px solid red;
}
/* line 49, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper .divInput input[type=file] {
  height: auto;
  border: 2px dashed lightgrey;
}
/* line 54, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper button {
  display: block;
  width: 50%;
  margin: 20px auto 0;
  padding: 20px 18px;
  color: white;
  letter-spacing: 1px;
  font-size: 20px;
  background-color: #F2B849;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 64, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper button:hover {
  background-color: #102F57;
  padding-right: 80px;
  background-image: url("/img/ohtel/sending_email_1.gif");
  background-position: right center;
  background-size: contain;
  background-repeat: no-repeat;
}
/* line 73, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper .fa-spinner, .cv_form_wrapper .overlay {
  display: none;
}
/* line 76, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper.sending {
  background-color: #999;
}
/* line 78, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper.sending #cv_form {
  opacity: .3;
}
/* line 81, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper.sending .overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
/* line 85, ../sass/mobile/_cv_form_mobile.scss */
.cv_form_wrapper.sending .fa-spinner {
  display: block;
  color: white;
  position: absolute;
  bottom: 30px;
  left: 0;
  right: 0;
  margin: auto;
}

/* line 1, ../sass/mobile/banners/custom_contact_form.scss */
.custom_contact_form_wrapper {
  margin: 60px auto;
  color: #4b4b4b;
  box-sizing: border-box;
  font-size: 12px;
  width: 90%;
  text-align: center;
}
/* line 9, ../sass/mobile/banners/custom_contact_form.scss */
.custom_contact_form_wrapper .title_info {
  color: #102F57;
  text-transform: uppercase;
  font-weight: lighter;
  font-size: 20px;
  margin-bottom: 5px;
}
/* line 17, ../sass/mobile/banners/custom_contact_form.scss */
.custom_contact_form_wrapper .content_info {
  margin-top: 20px;
  font-size: 16px;
}
/* line 22, ../sass/mobile/banners/custom_contact_form.scss */
.custom_contact_form_wrapper .form_wrapper {
  width: 90%;
  display: inline-block;
  margin: auto;
  text-align: left;
  margin-top: 40px;
  position: relative;
}
/* line 30, ../sass/mobile/banners/custom_contact_form.scss */
.custom_contact_form_wrapper .form_wrapper .input_wrapper {
  width: 100%;
}
/* line 33, ../sass/mobile/banners/custom_contact_form.scss */
.custom_contact_form_wrapper .form_wrapper .input_wrapper label {
  text-transform: uppercase;
  color: #4b4b4b;
}
/* line 38, ../sass/mobile/banners/custom_contact_form.scss */
.custom_contact_form_wrapper .form_wrapper .input_wrapper input[type=text], .custom_contact_form_wrapper .form_wrapper .input_wrapper textarea, .custom_contact_form_wrapper .form_wrapper .input_wrapper select {
  display: block;
  width: 100%;
  padding: 15px;
  height: 30px;
  box-sizing: border-box;
  margin-top: 5px;
  margin-bottom: 20px;
  border-radius: 0;
  color: #4b4b4b;
  background-color: transparent;
  border: 1px solid #4b4b4b;
}
/* line 52, ../sass/mobile/banners/custom_contact_form.scss */
.custom_contact_form_wrapper .form_wrapper .input_wrapper select {
  height: 30px;
}
/* line 57, ../sass/mobile/banners/custom_contact_form.scss */
.custom_contact_form_wrapper .form_wrapper .input_wrapper.contTextarea textarea {
  height: 250px;
}
/* line 64, ../sass/mobile/banners/custom_contact_form.scss */
.custom_contact_form_wrapper .form_wrapper .button_wrapper {
  margin-top: 20px;
  clear: both;
  display: table;
  width: 100%;
  position: relative;
  padding-bottom: 40px;
}
/* line 72, ../sass/mobile/banners/custom_contact_form.scss */
.custom_contact_form_wrapper .form_wrapper .button_wrapper .send_button {
  color: white;
  transition: all 0.6s;
  float: right;
  background-color: #102F57;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-align: center;
  font-size: 16px;
  width: 21%;
  font-weight: 500;
  padding: 23px 70px 22px;
  margin-top: 6px;
  cursor: pointer;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}
/* line 94, ../sass/mobile/banners/custom_contact_form.scss */
.custom_contact_form_wrapper .form_wrapper .button_wrapper .send_button:hover {
  background-color: #F2B849;
  color: white;
}

/* line 11, ../sass/styles_mobile.scss */
header {
  background: #102F57;
}
/* line 14, ../sass/styles_mobile.scss */
header .mailto i {
  color: white;
}

/* line 20, ../sass/styles_mobile.scss */
header.with_extra_header {
  top: 40px !important;
}
/* line 22, ../sass/styles_mobile.scss */
header.with_extra_header .extra_top_header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  font-size: 10px;
  width: 100%;
  background: #F2B849;
  color: #4b4b4b;
  text-align: center;
}
/* line 36, ../sass/styles_mobile.scss */
header.with_extra_header .extra_top_header i {
  display: inline-block;
  margin-right: 5px;
}
/* line 40, ../sass/styles_mobile.scss */
header.with_extra_header .extra_top_header p {
  display: inline-block;
}
/* line 43, ../sass/styles_mobile.scss */
header.with_extra_header .extra_top_header a {
  position: relative;
  color: #4b4b4b;
  text-decoration: none;
}

/* line 50, ../sass/styles_mobile.scss */
.default_content_wrapper .main-owlslider.with_extra_header {
  margin-top: 40px;
}

/* line 54, ../sass/styles_mobile.scss */
.popup_inicio.fancybox-content {
  padding: 0;
}

/* line 59, ../sass/styles_mobile.scss */
.mobile_engine.open {
  height: 260px;
}
/* line 61, ../sass/styles_mobile.scss */
.mobile_engine.open .mobile_engine_action {
  bottom: 290px;
}

/* line 67, ../sass/styles_mobile.scss */
#full_wrapper_booking {
  padding-top: 30px;
}
/* line 69, ../sass/styles_mobile.scss */
#full_wrapper_booking .booking_form_title {
  display: none;
}
/* line 72, ../sass/styles_mobile.scss */
#full_wrapper_booking .destination_wrapper {
  background: white !important;
  margin-bottom: 10px;
  width: calc(100% - 20px);
  position: relative;
}
/* line 77, ../sass/styles_mobile.scss */
#full_wrapper_booking .destination_wrapper:before {
  content: '\f278';
  display: block;
  font-family: "FontAwesome", sans-serif;
  font-size: 14px;
  color: #666;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 7px;
  z-index: 2;
}
/* line 87, ../sass/styles_mobile.scss */
#full_wrapper_booking .destination_wrapper:after {
  content: '\f078';
  display: block;
  font-family: "FontAwesome", sans-serif;
  font-size: 18px;
  color: #666;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 7px;
  z-index: 2;
}
/* line 97, ../sass/styles_mobile.scss */
#full_wrapper_booking .destination_wrapper select {
  width: 100%;
  height: 45px;
  padding-left: 35px;
  box-sizing: border-box;
}

/* line 106, ../sass/styles_mobile.scss */
#contactContent {
  margin: 0;
  color: #4b4b4b;
  box-sizing: border-box;
  font-size: 12px;
}
/* line 111, ../sass/styles_mobile.scss */
#contactContent form {
  padding: 30px;
  background-image: url("/img/ohtel/landscape_blur.jpg");
  background-size: cover;
  background-color: #4695C7;
}
/* line 117, ../sass/styles_mobile.scss */
#contactContent .contInput {
  width: 100%;
}
/* line 119, ../sass/styles_mobile.scss */
#contactContent .contInput label {
  text-transform: uppercase;
  color: white;
}
/* line 123, ../sass/styles_mobile.scss */
#contactContent .contInput input[type=text], #contactContent .contInput textarea, #contactContent .contInput select {
  display: block;
  width: 100%;
  padding: 15px;
  height: 30px;
  box-sizing: border-box;
  margin-top: 5px;
  margin-bottom: 20px;
  border-radius: 0;
  color: white;
  background-color: transparent;
  border: 1px solid white;
}
/* line 136, ../sass/styles_mobile.scss */
#contactContent .contInput select {
  height: 30px;
}
/* line 139, ../sass/styles_mobile.scss */
#contactContent .contInput.contTextarea {
  clear: none;
  float: right;
}
/* line 142, ../sass/styles_mobile.scss */
#contactContent .contInput.contTextarea textarea {
  height: 250px;
}
/* line 147, ../sass/styles_mobile.scss */
#contactContent .checkbox_wrapper {
  margin-top: 20px;
  clear: both;
}
/* line 151, ../sass/styles_mobile.scss */
#contactContent a {
  color: #F2B849;
}
/* line 153, ../sass/styles_mobile.scss */
#contactContent a:hover {
  text-decoration: underline;
}
/* line 158, ../sass/styles_mobile.scss */
#contactContent #contact-button-wrapper #contact-button {
  display: block;
  background-color: white;
  color: #F2B849;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 16px;
  padding: 20px 60px;
  margin: 1em 0;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 168, ../sass/styles_mobile.scss */
#contactContent #contact-button-wrapper #contact-button:hover {
  padding-right: 120px;
  background-color: #F2B849;
  color: white;
  background-image: url("/img/ohtel/sending_email_2.gif");
  background-position: right center;
  background-size: contain;
  background-repeat: no-repeat;
}

/* line 181, ../sass/styles_mobile.scss */
#my-bookings-form-search-button {
  display: block;
  padding: 10px 0;
  box-sizing: border-box;
  font-size: 22px;
  text-transform: uppercase;
  width: 100%;
  border-radius: 5px;
  margin: auto;
  background-color: #102F57;
  color: white;
}

/* line 195, ../sass/styles_mobile.scss */
.container_popup_booking img {
  background-color: #102F57;
}

/* line 200, ../sass/styles_mobile.scss */
.selectHotel {
  font-size: 15px;
  padding: 1em;
  border-width: 0;
  background-color: white;
  box-sizing: border-box;
  width: 100%;
  border-radius: 5px;
  text-align: left;
  margin: 1em auto 0;
  border: 0.5em solid #F9F9F9;
}

/* line 216, ../sass/styles_mobile.scss */
#my-bookings-form-fields #hotelSelect optgroup {
  background-color: #102F57;
}

/* line 222, ../sass/styles_mobile.scss */
.filter_offers_group_wrapper {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  z-index: 20;
  width: 70%;
  display: inline-block;
}
/* line 228, ../sass/styles_mobile.scss */
.filter_offers_group_wrapper:before {
  content: "\f078";
  font-family: "FontAwesome";
  color: #102F57;
  font-size: 12px;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 10px;
}
/* line 237, ../sass/styles_mobile.scss */
.filter_offers_group_wrapper select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  background: white;
  border: 0;
  padding: 5px 10px;
  box-sizing: border-box;
}

/* line 251, ../sass/styles_mobile.scss */
body .banners_scrool .banner_element .banner_bottom_title {
  font-size: 16px;
}

/* line 259, ../sass/styles_mobile.scss */
.all_hotels_map_wrapper {
  width: 100%;
}
/* line 262, ../sass/styles_mobile.scss */
.all_hotels_map_wrapper iframe {
  width: 100%;
}

/* line 267, ../sass/styles_mobile.scss */
body #widgetContainer {
  z-index: 100 !important;
}
/* line 269, ../sass/styles_mobile.scss */
body #widgetContainer .launcher-container {
  right: auto !important;
  bottom: 63px !important;
}

/* line 275, ../sass/styles_mobile.scss */
body .mobile_engine.has_web_support.open {
  z-index: 101 !important;
}

/* line 279, ../sass/styles_mobile.scss */
#checkin_subtitle {
  display: none;
}

/* line 283, ../sass/styles_mobile.scss */
#precheckin_iframe {
  border: none;
}

/* line 287, ../sass/styles_mobile.scss */
.floating_buttons.precheckin {
  position: fixed;
  bottom: 122px;
  left: 20px;
  z-index: 100;
  display: block;
  width: 50px;
  font-size: 30px;
  background: #F2B849;
  height: 50px;
  border-radius: 50%;
  webkit-box-shadow: 2px 2px 20px 0px rgba(0, 0, 0, 0.4);
  -moz-box-shadow: 2px 2px 20px 0px rgba(0, 0, 0, 0.4);
  box-shadow: 2px 2px 20px 0px rgba(0, 0, 0, 0.4);
}
/* line 303, ../sass/styles_mobile.scss */
.floating_buttons.precheckin .btn_precheckin i {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: white;
  font-size: 25px;
}

/* line 311, ../sass/styles_mobile.scss */
.automatic_floating_picture.floating_mobile {
  bottom: 180px !important;
}

/* line 315, ../sass/styles_mobile.scss */
.fancybox-container .description_popup_booking {
  display: none !important;
}

/* line 4, ../sass/styles_mobile_test2.scss */
.mobile_engine .mobile_engine_action {
  background-color: #F2B849;
}

/* line 11, ../sass/styles_mobile_test2.scss */
#full_wrapper_booking .wrapper_booking_button .submit_button {
  background: #F2B849;
}
