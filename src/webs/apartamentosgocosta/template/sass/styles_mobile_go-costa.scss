$fontawesome5: true;

@import "defaults";
@import "styles_mobile/2/2";

body {
    font-family: $primary_font;
    font-weight: 400;
    font-size: 17px;
    color: $body_text_color;

    * {
        box-sizing: border-box;
        text-rendering: auto;
        -webkit-font-smoothing: antialiased;
    }

    h1, h2, h3, h4, h5, h6 {
        margin: 0;
    }

    a {
        color: $links_color;
        text-decoration: none;
        font-weight: 500;
        letter-spacing: 1px;
    }

    .section_content {

        .section_title {

            color: $black;
        }
    }

    .content_title {
        position: relative;

        .title {
            font-size: $font_lg;
            margin: 15px 0 10px 0;
            font-weight: 300;
            text-transform: uppercase;
            letter-spacing: 1px;

            &.big {
                font-size: $font_lg;

                span {
                    font-size: 1.3em;
                }
            }

            span {
                font-weight: 700;
            }
        }

        &.sub_border {
            padding-bottom: 10px;

            &:before {
                position: absolute;
                content: '';
                height: 1px;
                height: 1px;
                background-color: $corporate_2;
                width: 60px;
                bottom: 0;
                left: 0;
            }

            &.center {

                &:before {
                    left: 50%;
                    transform: translateX(-50%);
                }
            }
        }
    }

    .container {
        width: 100%;
    }

    /* Forms Styles */
    input, select, textarea {

        &.outline_rounded {
            border: solid 1px $corporate_2;
            text-align: center;
            padding: 8px 25px 5px;
            border-radius: 20px;
            font-size: $font_lg;
            font-weight: 300;
            text-transform: uppercase;
            background-color: transparent;
        }
    }
    /* End */

    header {

        .mailto {

            i {
                color: $black;
            }
        }

        .logo {
            right: 15px;
            left: auto;
            transform: translate(0%, -50%);

            img {
                max-height: 75px;
                margin-top: 2px;
            }
        }
        .telefono {
            position: absolute;
            right: 95px;
            left: auto;
        }
    }


    .main_menu {

        ul {

            li {

                a, span {

                    color: white;
                    font-weight: 400;
                }

                border-color: #1b6890;

                &.section_subcontent {

                    i {
                        right: 0;
                    }
                }
            }
        }

        .social_menu {

            i {
                background-color: transparent;
                color: $corporate_1;
            }
        }
    }

    .default_content_wrapper {

        .content {

            .main-owlslider {

                .owl-item {

                    > div {

                        .description_text {
                            padding-top: 50px;
                            color: white;
                            z-index: 1;

                            .content_title {

                                .title {

                                }
                            }
                        }

                        .black_overlay {
                            position: absolute;
                            top: 0;
                            bottom: 0;
                            left: 0;
                            right: 0;

                            &::before {
                                position: absolute;
                                content: '';
                                top: 0;
                                left: 0;
                                right: 0;
                                bottom: 0;
                                background: rgb(0, 30, 68);
                                background: linear-gradient(0deg, rgba(0, 30, 68, 0) 18%, rgba(0, 30, 68, 0.8939950980392157) 100%);
                            }
                        }
                    }
                }
            }

            .scrolldown {

                bottom: 125px;
            }

            .breadcrumbs {
                background-color: $corporate_2;
            }

            .section_content {
                color: $body_text_color;

                h2.section_title {
                    margin-top: 20px;
                }
                .main_wrapper {
                    .banner_top_icons {
                        &.sec_pad {
                            padding: 0 $main_padding;
                        }
                        .banner {
                            .icon_box {
                                .content_title {
                                    padding-top: 25px;
                                    i {
                                        &::before {
                                            top: 7%;
                                            left: 55%;
                                        }
                                    }
                                    .title {
                                        font-weight: bolder;
                                        small {
                                            font-weight: lighter;
                                            display: block;
                                        }
                                    }
                                }
                                &::before {
                                    top: 0;
                                    left: 50%;
                                }
                            }
                        }
                    }

                    .banner_destinations {
                        background-color: $corporate_3;
                        position: relative;

                        .owl-carousel {

                            .owl-item {
                                position: relative;
                                min-height: 500px;

                                .banner {
                                    position: absolute;
                                    top: 0;
                                    bottom: 0;
                                    left: 0;
                                    right: 0;

                                    background-color: orange;

                                    .content_wrapper {

                                        .content {
                                            position: relative;
                                            z-index: 60;
                                            display: flex;
                                            flex-flow: column nowrap;
                                            padding: 40px $main_padding 70px;

                                            .content_title {
                                                line-height: 35px;

                                                .title {
                                                    color: white;
                                                    font-weight: 400;
                                                }
                                            }

                                            .desc {
                                                color: white;
                                                line-height: 22px;
                                                margin-bottom: 25px;
                                                font-weight: 400;
                                            }
                                        }
                                    }

                                    .img_out_wrapper {

                                        .img_in_wrapper {
                                            position: absolute;
                                            top: 0;
                                            bottom: 0;
                                            left: 0;
                                            right: 0;

                                            img {
                                                width: 100%;
                                                height: 100%;
                                                object-fit: cover;
                                            }

                                            &::before {
                                                position: absolute;
                                                content: '';
                                                top: 0;
                                                left: 0;
                                                right: 0;
                                                bottom: 0;
                                                background-color: $black;
                                                opacity: .5;
                                            }
                                        }
                                    }
                                }
                            }

                            .owl-dots {

                                position: absolute;
                                bottom: 15px;
                                left: 0;
                                right: 0;

                                .owl-dot {
                                    background-color: white;
                                    opacity: .5;

                                    &.active {
                                        background-color: white;
                                        opacity: 1;
                                    }
                                }
                            }
                        }
                    }

                    .testimonials_section {
                        padding-bottom: 25px;

                        .content_title {

                            span {
                                font-size: 2.5rem;
                            }

                            img {

                                width: 100px;
                            }
                        }

                        .owl-carousel {

                            .owl-item {
                                text-align: center;
                                font-size: 1.2rem;

                                .testimonial {
                                    padding-bottom: $main_padding;

                                    .opinion_img {
                                        text-align: center;
                                        img {
                                            display: inline-block;
                                            max-width: 100px;
                                        }
                                    }

                                    .icon {

                                        i {
                                            font-size: 5rem;
                                            color: $corporate_1;
                                        }
                                    }

                                    .testimonial_desc_wrapper {
                                        margin-bottom: 25px;

                                        .desc {

                                        }
                                    }

                                    .testimonial_review {

                                        > p {
                                            margin: 0 0 5px;
                                        }

                                        .review {
                                            font-weight: 700;

                                            img {
                                                height: 15px;
                                                width: auto;
                                                display: inline-block;
                                                margin-left: 5px;
                                            }
                                        }
                                    }
                                }
                            }

                            .owl-dots {
                                text-align: center;
                                width: 100%;

                                .owl-dot {
                                    background-color: $corporate_3;

                                    &.active {
                                        background-color: $black;
                                    }
                                }
                            }
                        }

                    }
                }
            }

            .bannersx5_wrapper {

                .banner_blocks_text {

                    .row_flex {

                        .col_item {
                            width: 100%;
                            margin-bottom: 50px;
                        }
                    }
                }
            }

            .split_banner {

                .banner {
                    position: relative;
                    min-height: 300px;

                    .img_out_wrapper {

                        .img_in_wrapper {
                            position: absolute;
                            top: 0;
                            bottom: 0;
                            left: 0;
                            right: 0;

                            img {
                                height: 100%;
                                width: 100%;
                                object-fit: cover;
                            }

                            &::before {
                                position: absolute;
                                content: '';
                                top: 0;
                                left: 0;
                                right: 0;
                                bottom: 0;
                                background-color: #001e44;
                                opacity: .5;
                            }
                        }
                    }

                    .content_wrapper {
                        position: relative;
                        z-index: 1;

                        .content {
                            padding: 70px $main_padding;

                            .content_title {
                                line-height: 35px;

                                .title {
                                    color: white;
                                    font-weight: 400;
                                }
                            }

                            .desc {
                                color: white;
                                line-height: 22px;
                                margin-bottom: 25px;
                                font-weight: 400;
                            }
                        }
                    }
                }

                &.img_left {

                }

                &.img_right {

                }
            }

            .apartments_gallery_section {

                .banner_title {
                    overflow: hidden;
                    padding: 0 20px;

                    .content_title {
                        margin: 0 auto;
                        text-align: center;
                        padding: $main_padding;
                        width: 400px;

                        .title {
                            font-weight: bolder;
                            position: relative;
                            small {
                                font-weight: lighter;
                                display: block;
                            }
                        }
                        .title_overlay {
                            position: absolute;
                            top: 5px;
                            right: 15px;
                            z-index: -1;
                        }
                    }
                    @media (max-width: 450px) {

                        .content_title {
                            width: 300px;

                            &::before {
                                width: 300px;
                                height: 300px;
                            }
                        }
                    }
                }

                .gallery_wrapper {

                    .gallery_filter_menu {
                        width: 100%;
                        margin: 0 auto;
                        display: none;

                        .gallery_menu_nav {
                            list-style: none;
                            display: flex;
                            justify-content: center;
                            flex-flow: row nowrap;
                            padding: 0;

                            a {
                                width: 33%;

                                &.all {

                                    .nav_item {
                                        background-color: $black;
                                        color: white;
                                    }
                                }

                                &.alicante {

                                    .nav_item {
                                        background-color: $corporate_1;
                                        color: $black;
                                    }
                                }

                                &.madrid {

                                    .nav_item {
                                        background-color: $corporate_4;
                                        color: $black;
                                    }
                                }

                                .nav_item {
                                    margin: 0 2.5px;
                                    padding: 5px;
                                    text-decoration: none;
                                    background-color: $black;
                                    color: white;
                                    opacity: .5;
                                    font-size: $font_sm;
                                    letter-spacing: 0;
                                }

                                &.active {

                                    .nav_item {
                                        opacity: 1;
                                    }
                                }
                            }
                        }
                    }

                    .gallery_content {
                        /* display: flex;
                         flex-flow: row wrap;*/

                        .gallery_item {
                            height: 300px;
                            margin-bottom: 90px;
                            position: relative;
                            display: none;
                            padding: $main_padding 0;

                            &.showed {
                                display: block;
                                margin-bottom: 100px;
                            }

                            .content_title {
                                text-align: center;
                                line-height: 25px;

                                .title {
                                    font-size: $font_lg;
                                    margin-bottom: 20px;
                                }
                            }

                            .label_detail {
                                position: absolute;
                                left: 50%;
                                transform: translate(-50%, -50%);
                                background-color: $black;
                                color: $corporate_1;
                                border-radius: $md-radius;
                                padding: 3px $main_padding 0;
                                text-transform: uppercase;
                                z-index: 3;
                                white-space: nowrap;
                            }

                            .img_wrapper {
                                position: relative;
                                overflow: hidden;
                                height: 235px;

                                img {
                                    height: 100%;
                                    width: 100%;
                                    object-fit: cover;

                                }

//                                 &:hover {
//
//                                     .item_overlay {
//                                         opacity: 0.8;
//                                         visibility: visible;
//                                         transform: scale(1);
//                                     }
//
//                                     .item_overlay_info {
//                                         opacity: 1;
//                                         visibility: visible;
//                                         transform: scale(1);
//                                     }
//                                 }

                                .item_overlay {
                                    position: absolute;
                                    top: 0;
                                    right: 0;
                                    bottom: 0;
                                    left: 0;
                                    text-align: center;
                                    background-color: $corporate_2;
                                    color: #fff;
                                    transform: translateY(20px);
                                    transition: all .3s ease;
                                    opacity: 0;
                                    visibility: hidden;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                }


                                .item_overlay_info {
                                    position: absolute;
                                    top: 0;
                                    right: 0;
                                    bottom: 0;
                                    left: 0;
                                    transform: translateY(20px);
                                    transition: all .3s ease;
                                    transition-delay: .3s;
                                    opacity: 0;
                                    visibility: hidden;
                                    text-align: center;
                                    display: flex;
                                    flex-direction: column;
                                    justify-content: center;

                                    a {
                                        margin: 0 80px 7.5px;
                                        display: block;
                                        padding: 5px $main_padding 3px;
                                        text-decoration: none;
                                        border-radius: $md_radius;
                                        font-size: 1rem;
                                        font-weight: 700;
                                        color: $black;
                                        text-transform: uppercase;

                                        &:first-child {
                                            background-color: white;
                                        }

                                        &:last-child {
                                            background-color: $corporate_1;
                                        }

                                        &:hover {
                                            box-shadow: 0 15px 15px rgba(0, 0, 0, 0.15);
                                        }
                                    }
                                }
                                .owl-nav {
                                    @include center_xy;
                                    height: 0;
                                    width: 100%;

                                    .owl-prev, .owl-next {
                                        @include center_y;
                                        left: 0;
                                        right: auto;
                                        height: 50px;
                                        width: 50px;
                                        border-radius: 50%;
                                        cursor: pointer;
                                        @include transition(all, .6s);

                                        i {
                                            @include center_xy;
                                            color: $corporate_2;
                                            top: 30px;
                                            font-size: 38px;
                                        }

                                        &:hover {
                                            i {
                                                color: $corporate_1;
                                            }
                                        }
                                    }

                                    .owl-next {
                                        right: 0;
                                        left: auto;
                                    }
                                }
                            }
                            .description_wrapper {
                                text-align: center;
                                padding: 10px;
                                color: $corporate_2;
                                font-size: 12px;
                                background-color:$corporate_1;
                            }
                            &.madrid {

                                .content_title {

                                    .title {

                                        span {
                                            color: $corporate_4;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            @import "banners/banner_float";
            .banner_float_left {
                bottom: 130px;
                z-index: 20;
                .banner_float {
                    .image_wrapper {
                        width: 50px;
                        height: 50px;
                        i:before {
                            font-size: 32px;
                        }
                    }
                }
            }
            .faq_questions_options_wrapper {
                margin: 0 auto 30px;
                @include transition(all, .2s);

                &.sec_pad {
                    padding-top: 0;
                }

                .option_element {
                    display: block;
                    margin-bottom: 10px;
                    width: 100%;
                    text-align: left;
                    font-size: $font_sm;

                    .option_title {
                        position: relative;
                        background-color: $corporate_2;
                        color: white;
                        min-height: 40px;
                        padding: 7.5px 15px 7.5px 55px;

                        &:before {
                            @include center_y;
                            right: 20px;
                            font-size: 20px;
                            @include transition(all, .2s);
                        }

                        .option_number {
                            background-color: $corporate_1;
                            color: $corporate_2;
                            padding-top: 15px;
                            margin-right: 15px;
                            width: 40px;
                            text-align: center;
                            position: absolute;
                            top: 0;
                            bottom: 0;
                            left: 0;
                            font-weight: 700;
                            font-size: 1rem;
                        }

                        &.active {

                            &:before {
                                transform: rotate(180deg);
                                margin-top: -10px;
                            }
                        }
                    }

                    .option_description {
                        display: none;
                        padding: $main_padding;
                        font-weight: 300;
                        background-color: $lightgrey;
                        margin-bottom: 5px;
                    }
                }
            }

            .promotions_wrapper {
                background-color: $corporate_2;

                .offer_content {
                    border-radius: $xs-radius;

                    .picture {
                        border-radius: $xs-radius $xs-radius 0 0;
                    }

                    h3 {
                        text-transform: uppercase;
                    }

                    .desc {
                        margin-top: 0;
                        margin-bottom: 80px;
                        border-radius: 0 0 $xs-radius $xs-radius;
                    }
                }
            }
        }
    }

    .banner_apartment_detail {
        position: relative;
        border-bottom: 15px solid white;

        .owl-dots {
            @include center_x;
            width: 100%;
            bottom: 5px;

            .owl-dot {
                opacity: .5;
                background-color: white;

                &.active {
                    opacity: 1;
                    background-color: white;
                }
            }
        }

        .container_fluid.banner .content {
            padding: 20px;
            color: white;

            .title.big {
                line-height: 35px;
            }

            .desc {
                line-height: 15px;
            }
        }

        .features {
            padding-top: $lg_padding;

            i {
                margin-right: 7px;
            }
        }

    }

    .icon_box {
        position: relative;

        .content_title {
            padding: $main_padding;

            i {
                &::before {
                    position: absolute;
                    font-size: 2.3rem;
                    top: -25px;
                    left: 50%;
                    transform: translateX(-50%);
                    z-index: 1;
                }
            }

            .title {
                font-weight: 700;
                text-transform: none;
                text-align: center;
            }
        }

        &::before {
            position: absolute;
            content: '';
            width: 40px;
            height: 40px;
            background-color: $corporate_1;
            top: -25px;
            left: 50%;
            border-radius: 50%;
            transform: translateX(-50%);
        }

        &:nth-child(1) {

            &::before {
                top: -35px;
                left: 42%;
            }
        }

        &:nth-child(2) {

            &::before {
                top: -15px;
                left: 60%;
            }
        }

        &:nth-child(3) {

            &::before {
                top: -35px;
                left: 42%;
            }
        }

        &:nth-child(4) {

            &::before {
                top: -15px;
                left: 60%;
            }
        }

        &:nth-child(5) {

            &::before {
                top: -35px;
                left: 62%;
            }
        }
    }

    .contact_form_wrapper {
        padding-bottom: 50px;

        #contact {
            width: 100%;
            background-color: transparent;
            padding: 0;

            .info {
                margin-top: 0;

                .contInput {
                    position: relative;

                    i {
                        position: absolute;
                        top: 30px;
                        left: 15px;

                    }

                    input, textarea {
                        text-transform: uppercase;
                        border-radius: $xs-radius;
                        background-color: white;
                        padding-left: 45px;

                        &::placeholder {
                            color: $corporate_2;
                        }
                    }
                }

                #contact-button {
                    border: none;
                    border-radius: $xs-radius;
                    margin-bottom: 15px;
                    font-size: $font_lg;
                    font-weight: 700;
                    display: block;
                    background: #fcd791;
                    color: white;
                    text-transform: uppercase;
                    margin-bottom: 10px;
                    margin-top: 10px;
                    cursor: pointer;
                    width: 100%;
                    padding: 10px 0;
                }

                .policy-terms {
                    font-size: $font_sm;
                }
            }
        }

        h1, h2, h3, h4, h5 {

            &::after {
                display: none;
            }
        }

        .content_title {
            margin-bottom: 30px;
        }
    }

    .mobile_engine {

        &.open {
            height: 350px;
            background-color: $corporate_2;

            .mobile_engine_action {
                bottom: 390px;
            }
        }
        .mobile_engine_action span {
            font-weight: bolder;
        }
        .booking_engine_mobile {

            #full_wrapper_booking {

                .booking_form_title {

                    & > * {
                        font-weight: 400;
                    }
                }

                .destination_wrapper {
                    background: white !important;
                    margin-bottom: 10px;
                    width: 100%;
                    position: relative;

                    &:before {
                        content: '\f279';
                        display: block;
                        font-family: "Font Awesome 5 Pro", sans-serif;
                        font-size: 14px;
                        color: #666;
                        @include center_y;
                        left: 7px;
                        z-index: 2;
                    }

                    &:after {
                        content: '\f078';
                        display: block;
                        font-family: "Font Awesome 5 Pro", sans-serif;
                        font-size: 18px;
                        color: #666;
                        @include center_y;
                        right: 7px;
                        z-index: 2;
                    }

                    select {
                        width: 100%;
                        height: 45px;
                        padding-left: 35px;
                        box-sizing: border-box;
                    }
                }

                .dates_selector_personalized {
                    width: calc(100% - 5px);

                    .start_end_date_wrapper {

                        .start_date_personalized {
                            width: 50%;
                            left: 0;
                        }

                        .end_date_personalized {
                            width: 50%;
                            right: 0;
                        }
                    }
                }

                .wrapper_booking_button {

                    .guest_selector {
                        width: 50%;
                    }

                    .promocode_wrapper {
                        width: 50%;
                    }

                    .submit_button {
                        width: 100%;
                        background-color: $corporate_1;
                    }
                }

                .room_list_wrapper {
                    background-color: $corporate_2;
                }
            }
        }

        .mobile_engine_action {

            text-transform: uppercase;
            font-size: $font_lg;

        }
    }
    .banner_btn {
        padding: 50px 0;
        .btn_wrapper {
            .btn_link {
                background: $corporate_1;
                padding: 10px 20px;
                display: block;
                margin: 20px 10px;
                color: white;
                border-radius: 4px;
                @include transition(background, 0.5s);
                &:hover {
                    background: $corporate_2;
                }
            }
        }
    }
    #my-bookings-form-fields  {
        #hotelSelect {
            font-size: 15px;
            padding: 1.6em 2em;
            border-width: 0;
            background-color: #eaeaea;
            box-sizing: border-box;
            width: 100%;
            border-radius: 5px;
            text-align: left;
            margin: auto;
            margin-bottom: 10px;
            appearance: none;
            -moz-appearance: none;
            -webkit-appearance: none;
        }
        #my-bookings-form-search-button {
            appearance: none;
            -moz-appearance: none;
            -webkit-appearance: none;
            border: none;
            background: $corporate_2;
            color: white;
            padding: 5px 40px;
            font-weight: bolder;
            font-size: 18px;
            border-radius: 5px;
        }
    }
}

/* utils */
.text_left {
    text-align: left;
}

.text_right {
    text-align: right;
}

.text_center {
    text-align: center;
}

.text_colored {
    color: $corporate_4 !important;
}

.desc {
    margin: 15px 0 10px 0;
}

.lead {
    font-weight: 300;
    font-size: 1.3em;
}

.sec_pad {
    padding: $lg_padding $main_padding;
}

.btn {
    position: relative;
    border: none;
    text-align: center;
    padding: 8px 25px 5px;
    text-decoration: none;
    border-radius: 20px;
    font-size: $font_lg;
    font-weight: 700;
    text-transform: uppercase;
    transition: all .5s;

    &.btn_primary {
        background-color: $corporate_1;
    }

    &.btn_secondary {
        background-color: $corporate_2;
    }

    &.btn_tertiary {
        background-color: $corporate_3;
    }

    &.btn_white {
        background-color: white;
    }

    &.btn_light {
        background-color: $lightgrey;
    }

    &.btn_dark {
        background-color: $black;
        color: white;
    }

    &.btn_block {
        width: 100%;
    }

    /* luego insertar unicode correspondiente en el before. EJ en los Process Buttons */
    &.btn_icon {
        position: relative;

        &::before {
            position: absolute;
            font-size: 1.2em;
            left: 15px;
            font-family: 'icomoon', 'Font Awesome 5 Pro';
        }
    }
}

.bg_primary {
    background-color: $corporate_1;
}

.bg_secondary {
    background-color: $corporate_2;

    > * {
        color: white !important;
    }
}

.bg_grey {
    background-color: $lightgrey;
}

/* Tooltips */
.has_tooltip {
    display: inline-block;
}

[data-tooltip] {
    cursor: pointer;
    position: relative;

    &:before, &:after {
        position: absolute;
        visibility: hidden;
        opacity: 0;
        pointer-events: none;
        transition: all 0.15s cubic-bezier(0.5, 1, 0.25, 1);
        z-index: 1;
    }

    &:before {
        padding: 5px;
        width: 110px;
        border-radius: 3px;
        background: $corporate_2;
        color: $corporate_1;
        content: attr(data-tooltip);
        text-align: center;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.2;
    }

    &:after {
        border: 8px solid transparent;
        width: 0;
        content: "";
        font-size: 0;
        line-height: 0;
    }

    &:hover {

        &:before, &:after {
            visibility: visible;
            opacity: 1;
        }
    }

    &.has_tooltip {

        &:before {
            bottom: 100%;
            left: 50%;
            margin-bottom: 5px;
            transform: translateX(-50%);
        }

        &:after {
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border-top: 8px solid $corporate_2;
            border-bottom: none;
        }

        &:hover {
            &:before, &:after {
                transform: translateX(-50%) translateY(-5px);
            }
        }
    }
}

.bg_secondary {

    [data-tooltip] {

        &:before {
            background: $corporate_1;
            color: $corporate_2;
        }

        &.has_tooltip {

            &:after {
                border-top: 8px solid $corporate_1;
            }
        }
    }
}

/* End tooltips*/
/* End utils */

.owl-carousel {

    .owl-dots {

        .owl-dot {
            display: inline-block;
            background-color: $corporate_3;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin: 3px;

            &.active {
                background-color: $black;
            }
        }
    }

}

.white_block_wrapper {
  padding: 20px;
  text-align: center;
  .title {
    font-family: $primary_font;
    font-size: $font_xl;
    margin: 15px 0 10px 0;
    letter-spacing: 2.5px;
  }
  .text {
    @include text_styles;
  }
}



/*
#full_wrapper_booking {
    .wrapper_booking_button {
        .submit_button {
            background-color: $corporate_1;
        }
    }
}

.section_content {
    .location_content .section-title {
        display: none;
    }
    > h1, .content_subtitle_title, .section_title, .location_content .section-subtitle {
        @include title_styles;
    }
    div.content, div.content_subtitle_description, .section-content, .contact_content_element {
        @include text_styles;
        width: auto;
        padding: 0 20px;
    }
}*/



.wrapper_content.banner_destinations{
    .container_fluid{
        .owl-stage{
            height: 750px !important;
            .owl-item{
                min-height: 750px !important;
            }
        }
    }
}


body .default_content_wrapper .content .apartments_gallery_section .gallery_wrapper .gallery_content .gallery_item .img_wrapper{
    overflow: visible !important;
    .item_overlay_info{
        opacity: 1 !important;
        bottom: auto !important;
        top: 230px !important;
        visibility: visible !important;
        .hotel_info_link{
            background-color: #fb8397 !important;
        }
        a{
            color: white;
            &:not(:first-of-type){
                display:none;
            }
        }

    }
}


@media screen and (max-width: 320px){
    body .content_title .title.big span{
        font-size: 1.1em;
    }
}
