.banner_ofertas_full_wrapper {
  padding: 0 calc((100% - 1140px) / 2) 50px;
  h3 {
    font-family: $title_family;
    font-size: 32px;
    line-height: 46px;
    font-weight: 300;
    color: $black;
    margin: 50px 0;
    text-align: center;
    big {
      font-size: 44px;
      font-weight: 700;
      color: $corporate_2;
      display: block;
    }
  }
  .banner_ofertas_wrapper {
    text-align: center;

    .image {
      position: relative;
      width: 600px;
      height: 400px;
      display: inline-block;
      overflow: hidden;
      img {
        @include center_image;
      }
    }
    .content {
      width: 350px;
      height: 400px;
      background-color: $corporate_1;
      display: inline-block;
      vertical-align: top;
      color: white;
      h4 {
        padding: 40px 0 20px;
        font-family: $title_family;
        font-size: 30px;
        line-height: 32px;
      }
      p {
        padding: 20px 30px;
        font-size: 16px;
        line-height: 22px;
        font-family: $text_family;
      }
    }
  }

  .owl-nav {
    @include center_y;
    width: 100%;
    .owl-prev, .owl-next {
      @include center_y;
      left: 40px;
      right: auto;
      height: 50px;
      width: 50px;
      border-radius: 50%;
      cursor: pointer;
      @include transition(all, .6s);
      i {
        @include center_xy;
        color: $corporate_2;
        font-size: 32px;
      }
      &:hover {
        i {
          color: $corporate_1;
        }
      }
    }
    .owl-next {
      right: 40px;
      left: auto;
    }
  }

  .owl-dots {
    display: inline-block;
    border: 2px solid $corporate_2;
    border-radius: 30px;
    padding: 5px 15px 7px;
    margin-top: 30px;
    .owl-dot {
      display: inline-block;
      vertical-align: middle;
      background: transparent;
      border: 2px solid $corporate_1;
      width: 15px;
      height: 15px;
      border-radius: 50%;
      margin: 0 2px;
      &.active {
        background: $corporate_1;
      }
    }
  }
}