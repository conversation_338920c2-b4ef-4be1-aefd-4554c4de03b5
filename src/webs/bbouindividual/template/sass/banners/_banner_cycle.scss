.banner_cycle_wrapper {
  padding-bottom: 50px;
  padding-top: 20px;

  .banner_cycle_content {
    padding: 100px calc((100% - 900px) / 2) 50px;
    background-image: url("/img/#{$base_web}/rama.png");
    background-repeat: no-repeat;
    background-position: calc(50% + 400px) 50px;
    text-align: center;
    .title {
      @include title_styles;
      h1 {
        display: inline-block;
      }
    }
    .desc {
      @include text_styles;
      padding: 0 100px;
      &.active {
        hide {
          height: auto;
          max-height: 500px;
        }
      }
      hide {
        display: block;
        max-height: 0;
        height: 0;
        overflow: hidden;
        @include transition(all, 1s);
      }
      modal {
        display: none;
      }
      .banner_link {
        position: relative;
        text-transform: uppercase;
        display: block;
        color: $corporate_1;
        padding: 30px 20px 20px;
        @include transition(all, 1s);
        &.active {
          span {
            font-size: 0;
            &:before {
              display: block;
              text-align: center;
              content: attr(read-less-text);
              font-size: 16px;
            }
          }
        }
        &:hover {
          color: $black;
          &:before {
            background: $black;
          }
        }
        &:before {
          content: '';
          width: 1px;
          height: 20px;
          @include center_x;
          top: 0;
          background: $corporate_1;
          @include transition(all, 1s);
        }
      }
    }
  }
  .banners_filter {
    //position: sticky;
    //top: 75px;
    //z-index: 100;
    background: $corporate_1;
    text-align: center;
    overflow: hidden;
    &:before, &:after {
      content: '';
      @include center_y;
      width: 50%;
      height: 100px;
      background-size: contain;
      background-repeat: no-repeat;
      opacity: .3;
    }
    &:before {
      background-image: url("/img/#{$base_web}/back-2l.png");
      left: 0;
    }
    &:after {
      background-image: url("/img/#{$base_web}/back-2r.png");
      background-position: center right;
      right: 0;
    }
    a {
      position: relative;
      z-index: 10;
      display: inline-block;
      padding: 17px;
      margin: 0 10px;
      color: white;
      font-family: $title_family;
      font-weight: bold;
      &:hover, &.active {
        &:before {
          opacity: .3;
          margin-left: -50%;
        }
        &:after {
          opacity: .3;
          margin-left: 50%;
        }
      }
      &:before, &:after {
        @include center_xy;
        font-size: 50px;
        letter-spacing: 100%;
        font-family: $title_family;
        font-weight: normal;
        opacity: 0;
        @include transition(all, .6s);
      }
      &:before {
        content: '{';
      }
      &:after {
        content: '}';
      }
    }

    &.fixed {
      position: fixed;
      top: 75px;
      left: 0;
      right: 0;
      z-index: 10;
    }
  }
  .banner_cycle {
    .banner:nth-child(even) {
      .extra_text {
        background-color: $black;
        background-image: url("/img/#{$base_web}/rama3.png");
        background-position: 50px bottom;
        background-size: 80px;
      }
    }
    .banner_picture, .banner_content {
      display: inline-block;
      vertical-align: middle;
      width: 50%;
    }
    .banner_picture {
      height: 450px;
      overflow: hidden;
      position: relative;
      img {
        @include center_image;
        object-fit: cover;
      }
    }

    .banner_content {
      padding: 20px 50px 20px calc((100% - 1040px) / 2);
      .title {
        h2 {
          font-size: 30px;
          font-family: $title_family;
          font-weight: bold;
          padding: 0 0 30px;
        }
      }
      .desc {
        @include text_styles;
        padding-right: 150px;
        modal {
          display: none;
        }
      }
      .banner_link {
        @include btn_styles;
        display: inline-block;
        margin-top: 30px;
        color: $corporate_1;
        &:hover {
          color: $black;
        }
        &:before {
          background: $corporate_1;
        }
        &:after {
          border-left-color: transparent;
        }
      }
    }
    .cycle_minigallery {
      position: relative;
      width: 1040px;
      margin: 50px auto 100px;
      .pic {
        display: block;
        background: $black;
        border-radius: 3px;
        &:hover {
          img {
            opacity: .5;
          }
        }
        img {
          border-radius: 3px;
          vertical-align: middle;
        }
      }
      .owl-nav {
        display: block;
        @include center_y;
        left: -25px;
        right: -25px;
        height: 0;
        .owl-prev, .owl-next {
          .friangulo {
            &:before {
              border-top-color: $corporate_1;
            }
          }
        }
        .owl-prev {
          float: left;
          .friangulo {
            display: block;
            -webkit-transform: rotate(90deg);
            -moz-transform: rotate(90deg);
            -ms-transform: rotate(90deg);
            -o-transform: rotate(90deg);
            transform: rotate(90deg);
          }
        }
        .owl-next {
          float: right;
          .friangulo {
            display: block;
            -webkit-transform: rotate(-90deg);
            -moz-transform: rotate(-90deg);
            -ms-transform: rotate(-90deg);
            -o-transform: rotate(-90deg);
            transform: rotate(-90deg);
          }
        }
      }
    }
    .extra_text {
      width: 1040px;
      margin: 50px auto;
      background-color: $corporate_1;
      background-image: url("/img/#{$base_web}/rama2.png");
      background-position: center right;
      background-size: 150px;
      background-repeat: no-repeat;
      text-align: center;
      padding: 50px 200px;
      @include text_styles;
      color: white;
    }

    .minibanner {
      padding: 60px 0;

      .title_minibanner {
        font-size: 22px;
        font-family: $title_family;
        font-weight: bold;
        text-align: center;
        color: $corporate_1;
        padding-bottom: 20px;
      }

      .blocks {
        .block_ind {
          .img_container {
            position: relative;
            overflow: hidden;
            img {
              @include center_image;
            }
          }
          .content_container {
            background: #F5F4F0;

            .title {
              font-size: 22px;
              font-family: $title_family;
              font-weight: bold;
              text-align: left;
              padding-bottom: 20px;
            }

            .desc {
              @include text_styles;
            }

            .link {
              @include btn_styles;
              display: inline-block;
              margin-top: 30px;
              color: $corporate_1;

              &:hover {
                color: $black;
              }

              &:before {
                background: $corporate_1;
              }

              &:after {
                border-left-color: transparent;
              }
            }
          }
        }
      }

      &.minibanner_small {
        .blocks {
          display: flex;
          .block_ind {
            width: 50%;
            &:first-of-type {
              margin-right: 10px;
            }
            &:last-of-type {
              margin-left: 10px;
            }
            .img_container {
              width: 100%;
              height: 360px;
            }
            .content_container {
              padding: 40px 30px;
            }
          }
        }
      }

      &.minibanner_big {
        .blocks {
          .block_ind {
            width: 100%;
            display: flex;
            flex-direction: row;

            &:nth-child(even) {
              flex-direction: row-reverse;
            }

            &:not(:last-of-type) {
              margin-bottom: 20px;
            }

            .img_container {
              width: 35%;
            }

            .content_container {
              padding: 55px 60px;
              width: 65%;
            }
          }
        }
      }
    }
  }
}