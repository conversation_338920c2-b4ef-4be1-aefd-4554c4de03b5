@import url(https://fonts.googleapis.com/css?family=Montserrat);
@import url(https://fonts.googleapis.com/css?family=Source+Sans+Pro:400,300,700,600);
/* line 4, ../../../../sass/booking/_booking_engine_5.scss */
.booking_widget {
  position: relative;
}

/* line 8, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 {
  width: 275px;
  display: block;
}
/* line 12, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 form.booking_form {
  padding: 0 !important;
}
/* line 16, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 .booking_form_title {
  background: #383838;
}

/* line 21, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper {
  border-bottom: 1px solid lightgray;
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat 92% 70% !important;
}
/* line 25, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
  display: inline-block;
  margin: 10px 0 0 10px;
}

/* line 33, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input {
  border: none;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 25px;
  font-weight: 600;
  background: white;
}
/* line 41, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input::-webkit-input-placeholder {
  color: #585d63;
}
/* line 45, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input:-moz-placeholder {
  /* Firefox 18- */
  color: #585d63;
}
/* line 49, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input::-moz-placeholder {
  /* Firefox 19+ */
  color: #585d63;
}
/* line 53, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input:-ms-input-placeholder {
  color: #585d63;
}

/* line 58, ../../../../sass/booking/_booking_engine_5.scss */
.entry_date_wrapper {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 66.6%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}

/* line 73, ../../../../sass/booking/_booking_engine_5.scss */
.departure_date_wrapper, .half_size {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 50%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}
/* line 87, ../../../../sass/booking/_booking_engine_5.scss */
.departure_date_wrapper .date_day, .half_size .date_day {
  font-size: 21px !important;
}

/* line 92, ../../../../sass/booking/_booking_engine_5.scss */
.nights_number_wrapper {
  float: right;
  height: 70px;
  border-bottom: 1px solid lightgrey;
  box-sizing: border-box;
  width: 33.3%;
  padding: 5px 10px;
}

/* line 101, ../../../../sass/booking/_booking_engine_5.scss */
.num_nights_label, .entry_date_label, .children_label, .rooms_label, .adults_label, .promocode_label, .departure_date_label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/* line 107, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 30%;
}

/*===== Entry date =====*/
/* line 114, ../../../../sass/booking/_booking_engine_5.scss */
.date_box.entry_date, .date_box.departure_date {
  font-size: 20px;
  background: url(/static_1/images/booking_5/calendar.png) no-repeat center right;
  margin-top: 9px;
  background-size: 29px;
}
/* line 120, ../../../../sass/booking/_booking_engine_5.scss */
.date_box.entry_date .date_day, .date_box.departure_date .date_day {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 25px;
  font-weight: 600;
}

/*====== Booking button ====*/
/* line 129, ../../../../sass/booking/_booking_engine_5.scss */
button.submit_button {
  width: 100%;
  border: 0;
  border-radius: 0;
  background: #ffd600;
  height: 55px;
  text-transform: uppercase;
  font-weight: bolder;
  font-size: 21px;
  color: #565656;
  cursor: pointer;
}

/*====== Rooms section ======*/
/* line 143, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 33.3%;
  padding: 7px 10px 5px;
  box-sizing: border-box;
}
/* line 150, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper .room_list_wrapper li.room {
  float: right;
  width: 69%;
}
/* line 156, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper .adults_selector {
  height: 70px;
  float: left;
  border-right: 1px solid gray;
  width: 46.5%;
}

/* line 164, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper {
  display: table;
  float: right;
  width: 66.6%;
}
/* line 169, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .room_title {
  display: none;
}
/* line 173, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .adults_selector {
  border-right: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
}
/* line 178, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
  width: 50%;
  display: inline-block;
  height: 70px;
  box-sizing: border-box;
  padding: 7px 10px 5px;
}
/* line 186, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .children_selector {
  width: 50% !important;
  padding: 7px 10px 5px;
  float: right;
}

/* line 193, ../../../../sass/booking/_booking_engine_5.scss */
.room_list .room {
  height: 70px;
}

/* line 197, ../../../../sass/booking/_booking_engine_5.scss */
.room_list .room.room3, .room_list .room.room2 {
  border-top: 1px solid lightgrey;
}

/*==== Promocode section  ====*/
/* line 202, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_wrapper {
  display: table;
  width: 100%;
  padding: 10px 10px 5px;
  border-top: 1px solid lightgrey;
  box-sizing: border-box;
}

/* line 210, ../../../../sass/booking/_booking_engine_5.scss */
label.promocode_label {
  display: block;
}

/* line 214, ../../../../sass/booking/_booking_engine_5.scss */
input.promocode_input {
  border: 0;
  width: 100%;
  display: block;
  height: 30px;
  font-size: 13px;
  font-weight: lighter;
  margin-top: 2px;
  font-family: sans-serif;
}

/*====== Selectric ======*/
/* line 226, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems {
  overflow: scroll !important;
  width: 92px !important;
  margin-left: -11px;
  margin-top: 2px;
}
/* line 232, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 238, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li {
  font-weight: 500;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAADAQMAAABs5if8AAAABlBMVEXOzs7y8vKNS741AAAADklEQVQI12M4wPSAWQAABoQBtgsaY5kAAAAASUVORK5CYII=);
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  line-height: 29px !important;
  font-size: 18px !important;
  font-family: 'Source Sans Pro', sans-serif;
}
/* line 248, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li:hover {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 255, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems ul {
  z-index: 40;
}
/* line 259, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems .room {
  padding-top: 17px;
}

/* line 265, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 .selectric .button {
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 23px;
  margin-left: 0;
  margin-top: 8px;
  text-indent: 999px;
  height: 22px;
  font-weight: 600;
}

/* line 278, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper .selectric .label {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 28px;
  margin-left: 0;
  font-weight: 600;
}

/* line 286, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper .selectric {
  margin-top: 3px;
}

/* line 290, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper {
  width: 78px !important;
}

/*======= Phone web support =======*/
/* line 295, ../../../../sass/booking/_booking_engine_5.scss */
.wrapper-new-web-support.booking_form_title {
  border-radius: 0;
  padding: 5px 10px;
  font-family: 'Montserrat', sans-serif;
  font-size: 11px !important;
  margin-top: 11px;
}

/* line 303, ../../../../sass/booking/_booking_engine_5.scss */
.wrapper-new-web-support .web_support_number, .web_support_label_1 {
  font-size: 11px !important;
  margin-left: 5px;
}

/* line 308, ../../../../sass/booking/_booking_engine_5.scss */
img.phone_support_image {
  float: left;
  width: 28px;
  margin-top: 1px;
  margin-right: 12px;
}

/*======= Header title ======*/
/* line 316, ../../../../sass/booking/_booking_engine_5.scss */
.booking_title_1, .booking_title_2, .best_price {
  display: none;
}

/* line 320, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header {
  font-family: 'Montserrat', sans-serif;
  padding: 10px;
  display: table;
  width: 100%;
  box-sizing: border-box;
  cursor: pointer;
}
/* line 328, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header:hover {
  opacity: 0.8;
}
/* line 332, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header img.booking_header_discount {
  float: left;
  margin-right: 15px;
}
/* line 337, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header p.first_offer_name {
  font-size: 12px;
  color: #8096c7;
  margin-top: 9px;
}
/* line 343, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header p.second_offer_name {
  font-size: 10px;
  margin-top: 3px;
  color: gray;
}

/* line 1, ../sass/_booking_engine.scss */
#full_wrapper_booking {
  /*======== Booking Widget =======*/
}
/* line 3, ../sass/_booking_engine.scss */
#full_wrapper_booking div#wrapper_booking {
  position: absolute;
  height: auto;
  top: 145px;
  left: 0px;
  right: 0px;
  z-index: 35;
}
/* line 11, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget {
  position: absolute;
  right: 0px;
  top: 100px;
}
/* line 17, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 {
  width: 299px;
}
/* line 21, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 form.booking_form {
  background: white;
}
/* line 25, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form_title .best_price {
  display: block;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}
/* line 33, ../sass/_booking_engine.scss */
#full_wrapper_booking .promocode_header p.first_offer_name {
  color: white;
}
/* line 36, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .date_box, #full_wrapper_booking .booking_widget .selectricWrapper, #full_wrapper_booking #booking_widget_popup .date_box, #full_wrapper_booking #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 40, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .date_box .date_day, #full_wrapper_booking #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 44, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectric {
  height: 38px;
  background: transparent;
}
/* line 49, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .adults_selector, #full_wrapper_booking .room_list_wrapper .children_selector {
  width: 49.8%;
}
/* line 53, ../sass/_booking_engine.scss */
#full_wrapper_booking button.submit_button {
  background: #FCD430 !important;
}
/* line 58, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .web_support_label_1, #full_wrapper_booking .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 63, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support .web_support_number, #full_wrapper_booking .web_support_label_1 {
  line-height: 15px !important;
}
/* line 67, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title {
  background: gray !important;
}
/* line 71, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 {
  margin-top: 20px !important;
}
/* line 75, ../sass/_booking_engine.scss */
#full_wrapper_booking #data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}
/* line 79, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date {
  margin-top: 6px;
}
/* line 82, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectricWrapper .selectric {
  margin-top: 0px;
}
/* line 86, ../sass/_booking_engine.scss */
#full_wrapper_booking #slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}
/* line 90, ../sass/_booking_engine.scss */
#full_wrapper_booking .promocode_text {
  display: none;
}

/* line 95, ../sass/_booking_engine.scss */
#inline_wrapper_booking {
  /*======== Booking Widget =======*/
}
/* line 97, ../sass/_booking_engine.scss */
#inline_wrapper_booking div#wrapper_booking {
  position: absolute;
  height: 70px;
  top: 331px;
  left: 0px;
  right: 0px;
  z-index: 35;
  width: 1121px;
}
/* line 107, ../sass/_booking_engine.scss */
#inline_wrapper_booking .promocode_header {
  display: none;
}
/* line 111, ../sass/_booking_engine.scss */
#inline_wrapper_booking .booking_widget {
  position: absolute;
  left: 0px;
}
/* line 116, ../sass/_booking_engine.scss */
#inline_wrapper_booking #full-booking-engine-html-5 {
  width: 1121px;
}
/* line 120, ../sass/_booking_engine.scss */
#inline_wrapper_booking #full-booking-engine-html-5 form.booking_form {
  background: white;
}
/* line 124, ../sass/_booking_engine.scss */
#inline_wrapper_booking .booking_form_title .best_price {
  display: none;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}
/* line 132, ../sass/_booking_engine.scss */
#inline_wrapper_booking .promocode_header p.first_offer_name {
  color: white;
}
/* line 135, ../sass/_booking_engine.scss */
#inline_wrapper_booking .booking_widget .date_box, #inline_wrapper_booking .booking_widget .selectricWrapper, #inline_wrapper_booking #booking_widget_popup .date_box, #inline_wrapper_booking #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 139, ../sass/_booking_engine.scss */
#inline_wrapper_booking .booking_widget .date_box .date_day, #inline_wrapper_booking #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 143, ../sass/_booking_engine.scss */
#inline_wrapper_booking .selectric {
  height: 38px;
  background: transparent;
}
/* line 148, ../sass/_booking_engine.scss */
#inline_wrapper_booking .room_list_wrapper .adults_selector, #inline_wrapper_booking .room_list_wrapper .children_selector {
  width: 49.8%;
}
/* line 152, ../sass/_booking_engine.scss */
#inline_wrapper_booking button.submit_button {
  background-color: #FCD430 !important;
  color: #73a841 !important;
}
/* line 160, ../sass/_booking_engine.scss */
#inline_wrapper_booking .booking_widget .web_support_label_1, #inline_wrapper_booking .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 165, ../sass/_booking_engine.scss */
#inline_wrapper_booking .wrapper-new-web-support .web_support_number, #inline_wrapper_booking .web_support_label_1 {
  line-height: 15px !important;
}
/* line 169, ../sass/_booking_engine.scss */
#inline_wrapper_booking .wrapper-new-web-support.booking_form_title {
  background: gray !important;
}
/* line 177, ../sass/_booking_engine.scss */
#inline_wrapper_booking #data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}
/* line 181, ../sass/_booking_engine.scss */
#inline_wrapper_booking .date_box.entry_date {
  margin-top: 6px;
}
/* line 184, ../sass/_booking_engine.scss */
#inline_wrapper_booking .selectricWrapper .selectric {
  margin-top: 0px;
}
/* line 188, ../sass/_booking_engine.scss */
#inline_wrapper_booking #slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}
/* line 192, ../sass/_booking_engine.scss */
#inline_wrapper_booking .promocode_text {
  display: none;
}
/* line 196, ../sass/_booking_engine.scss */
#inline_wrapper_booking .stay_selection {
  display: inline-block;
  vertical-align: top;
  float: left;
}
/* line 201, ../sass/_booking_engine.scss */
#inline_wrapper_booking .stay_selection .entry_date_wrapper {
  display: inline-block;
  vertical-align: top;
  width: 192px;
  float: left;
  border-top: 1px solid lightgrey;
}
/* line 209, ../sass/_booking_engine.scss */
#inline_wrapper_booking .stay_selection .nights_number_wrapper {
  display: inline-block;
  width: 95px;
  float: left;
  vertical-align: top;
  border-top: 1px solid lightgrey;
}
/* line 218, ../sass/_booking_engine.scss */
#inline_wrapper_booking .rooms_number_wrapper {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 95px;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
}
/* line 228, ../sass/_booking_engine.scss */
#inline_wrapper_booking .room_list_wrapper {
  width: 190px;
  display: inline-block;
  vertical-align: top;
  float: left;
  border-bottom: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
  height: 68px;
}
/* line 238, ../sass/_booking_engine.scss */
#inline_wrapper_booking .wrapper_booking_button {
  display: inline-block;
  width: 548px;
  float: left;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  height: 69px;
}
/* line 246, ../sass/_booking_engine.scss */
#inline_wrapper_booking .wrapper_booking_button .promocode_wrapper {
  display: inline-block;
  width: 285px;
  vertical-align: top;
  float: left;
  height: 70px;
}
/* line 254, ../sass/_booking_engine.scss */
#inline_wrapper_booking .wrapper_booking_button .submit_button {
  width: 263px;
  display: inline-block;
  vertical-align: top;
  float: left;
  height: 70px;
  border: 1px solid lightgrey;
}

/* line 1, ../sass/_booking_widget_modal.scss */
.fancybox-wrap {
  /*======== Booking Widget =======*/
}
/* line 4, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .fancybox-outer {
  padding: 0 !important;
}
/* line 8, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .fancybox-inner {
  width: auto !important;
}
/* line 13, ../sass/_booking_widget_modal.scss */
.fancybox-wrap div#wrapper_booking {
  position: absolute;
  height: 420px;
  top: 145px;
  left: 0px;
  right: 0px;
  z-index: 35;
}
/* line 21, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget {
  position: absolute;
  left: 0px;
}
/* line 26, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 {
  width: 305px;
}
/* line 30, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 form.booking_form {
  background: white;
}
/* line 34, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_form_title .best_price {
  display: block;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}
/* line 42, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.first_offer_name {
  color: white;
}
/* line 45, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .date_box, .fancybox-wrap .booking_widget .selectricWrapper, .fancybox-wrap #booking_widget_popup .date_box, .fancybox-wrap #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 49, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .date_box .date_day, .fancybox-wrap #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 53, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .selectric {
  height: 38px;
  background: transparent;
}
/* line 58, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .room_list_wrapper .adults_selector, .fancybox-wrap .room_list_wrapper .children_selector {
  width: 49.8%;
}
/* line 62, ../sass/_booking_widget_modal.scss */
.fancybox-wrap button.submit_button {
  background: #FCD430 !important;
}
/* line 67, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .web_support_label_1, .fancybox-wrap .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 72, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .wrapper-new-web-support .web_support_number, .fancybox-wrap .web_support_label_1 {
  line-height: 15px !important;
}
/* line 76, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .wrapper-new-web-support.booking_form_title {
  background: gray !important;
}
/* line 80, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 {
  margin-top: 20px !important;
}
/* line 84, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}
/* line 88, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .date_box.entry_date {
  margin-top: 6px;
}
/* line 91, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .selectricWrapper .selectric {
  margin-top: 0px;
}
/* line 95, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}
/* line 99, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_text {
  display: none;
}

/*=====================================================================
  Selectric
======================================================================*/
/* line 5, ../../../../sass/booking/_selectric.scss */
.selectricWrapper {
  position: relative;
  margin: 0 0 0px;
  width: 80px;
  cursor: pointer;
}

/* line 12, ../../../../sass/booking/_selectric.scss */
.selectricDisabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 23, ../../../../sass/booking/_selectric.scss */
.selectricOpen {
  z-index: 9999;
}

/* line 27, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 31, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

/* line 38, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect select {
  position: absolute;
  left: -100%;
  display: none;
}

/* line 44, ../../../../sass/booking/_selectric.scss */
.selectricInput {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  _font: 0/0 a;
  background: none !important;
}

/* line 60, ../../../../sass/booking/_selectric.scss */
.selectricTempShow {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* line 67, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectric {
  z-index: 9999;
}

/* line 72, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 76, ../../../../sass/booking/_selectric.scss */
.selectric {
  background: white;
  position: relative;
  border-radius: 6px;
  height: 40px;
}

/* line 83, ../../../../sass/booking/_selectric.scss */
.selectric .label {
  display: block;
  overflow: hidden;
  font-size: 16px;
  line-height: 40px;
  color: #73a841;
  text-align: left;
  margin-left: 20px;
}

/* line 93, ../../../../sass/booking/_selectric.scss */
.selectric .button {
  zoom: 1;
  position: absolute;
  font: 0/0 a;
  overflow: hidden;
  margin: auto;
  top: 0;
  right: 2px;
  bottom: 0;
  width: 35px;
  height: 35px;
  border: 0;
  background: #73a841 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  padding: 0;
}

/* line 109, ../../../../sass/booking/_selectric.scss */
.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */
/* line 115, ../../../../sass/booking/_selectric.scss */
.selectricItems {
  display: none;
  position: absolute;
  overflow: hidden;
  top: 100%;
  left: 0;
  background: #F9F9F9;
  border: 1px solid #CCC;
  z-index: 9998;
  -webkit-box-shadow: 0 0 10px -6px;
  box-shadow: 0 0 10px -6px;
}

/* line 128, ../../../../sass/booking/_selectric.scss */
.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* line 138, ../../../../sass/booking/_selectric.scss */
.selectricItems li {
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 146, ../../../../sass/booking/_selectric.scss */
.selectricItems li:hover {
  background: #F0F0F0;
  color: #444;
}
/* line 151, ../../../../sass/booking/_selectric.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}

/* line 158, ../../../../sass/booking/_selectric.scss */
.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;
  cursor: default;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/
/* line 5, ../sass/_template_specific.scss */
.ui-state-default {
  border: 1px solid white !important;
}

/* line 8, ../sass/_template_specific.scss */
.ui-datepicker-title {
  color: white !important;
}

/* line 11, ../sass/_template_specific.scss */
.ui-widget-header {
  background: #73a841 !important;
}

/* line 15, ../sass/_template_specific.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #73a841 !important;
  color: white;
}

/* line 20, ../sass/_template_specific.scss */
.cleardiv:before,
.cleardiv:after {
  content: " ";
  display: table;
}

/* line 26, ../sass/_template_specific.scss */
.cleardiv:after {
  clear: both;
}

/* line 30, ../sass/_template_specific.scss */
body {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 16px;
  color: #5a5a5a;
}

/* line 36, ../sass/_template_specific.scss */
header {
  background: #73a841;
  color: white;
  padding: 15px 0px 5px;
  position: relative;
  z-index: 22;
}

/* line 43, ../sass/_template_specific.scss */
.wrapper-top-header {
  position: relative;
}

/* line 46, ../sass/_template_specific.scss */
#logoDiv {
  position: absolute;
  top: -20px;
  margin-left: 0px;
}

/* line 52, ../sass/_template_specific.scss */
#logoDiv img {
  box-shadow: 0px 0px 12px black;
}

/* line 55, ../sass/_template_specific.scss */
.header-right-block {
  float: right;
}

/* line 58, ../sass/_template_specific.scss */
.top-line div,
.bottom-line div {
  float: right;
  font-weight: 300;
  text-transform: uppercase;
}
/* line 64, ../sass/_template_specific.scss */
.top-line div a,
.bottom-line div a {
  color: white;
  text-decoration: none;
}

/* line 69, ../sass/_template_specific.scss */
.web-oficial {
  margin-right: 20px;
}

/* line 72, ../sass/_template_specific.scss */
.bottom-line div {
  text-transform: initial;
  margin-top: 15px;
  margin-left: 20px;
}

/* line 77, ../sass/_template_specific.scss */
#top-sections,
.phone {
  padding-top: 10px;
}

/* line 81, ../sass/_template_specific.scss */
#top-sections a:hover,
#top-sections a#section-active {
  text-decoration: underline;
}

/* line 86, ../sass/_template_specific.scss */
#lang a:hover,
#lang a#selected {
  text-decoration: underline;
}

/* line 91, ../sass/_template_specific.scss */
#main_menu {
  clear: both;
  float: right;
  position: relative;
  z-index: 21;
  background: white;
  width: 100%;
}

/* line 100, ../sass/_template_specific.scss */
#mainMenuDiv {
  font-size: 15px;
  z-index: 99;
  clear: both;
  height: 40px;
}

/* line 107, ../sass/_template_specific.scss */
#mainMenuDiv a {
  padding: 5px 8px 5px;
  text-decoration: none;
  text-transform: uppercase;
  color: #5a5a5a;
  display: inline-block;
}

/* line 116, ../sass/_template_specific.scss */
#mainMenuDiv a:hover {
  background: white;
  color: #73a841;
}

/* line 121, ../sass/_template_specific.scss */
#section-active a {
  padding: 5px 3px 5px;
  text-decoration: none;
  text-transform: uppercase;
  background: white;
  color: #73a841;
  display: inline-block;
}

/* line 130, ../sass/_template_specific.scss */
#mainMenuDiv .book-menu a {
  color: #8a2140;
}

/* line 134, ../sass/_template_specific.scss */
#main-sections-inner ul {
  display: none;
}

/* line 138, ../sass/_template_specific.scss */
#main-sections-inner div:hover > ul {
  display: block;
}

/* line 142, ../sass/_template_specific.scss */
#main-sections-inner div ul {
  position: absolute;
}

/* line 146, ../sass/_template_specific.scss */
#main-sections-inner li ul {
  position: absolute;
}

/* line 150, ../sass/_template_specific.scss */
#main-sections-inner div li {
  float: none;
  display: block;
}

/* line 155, ../sass/_template_specific.scss */
#main-sections-inner {
  text-align: justify;
  height: 40px;
  width: 850px;
  float: right;
}

/* line 162, ../sass/_template_specific.scss */
#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

/* line 169, ../sass/_template_specific.scss */
#main-sections-inner > div {
  display: inline-block;
}

/* line 173, ../sass/_template_specific.scss */
.main-section-div-wrapper a {
  line-height: 30px;
  text-transform: uppercase;
  font-size: 15px;
}

/* line 179, ../sass/_template_specific.scss */
.main-section-div-wrapper a:hover {
  color: #63923b;
}

/* line 183, ../sass/_template_specific.scss */
#mainMenuDiv .link-reservar a {
  color: white !important;
  background: #73a841;
}
/* line 187, ../sass/_template_specific.scss */
#mainMenuDiv .link-reservar a:hover {
  background: #63923b;
}

/*============= SLIDER AND BOOKING ENGINE ==================*/
/* line 194, ../sass/_template_specific.scss */
#slider_container {
  position: relative;
}

/* line 198, ../sass/_template_specific.scss */
#slider_inner_container {
  position: relative;
}
/* line 201, ../sass/_template_specific.scss */
#slider_inner_container .image-slider-fixed {
  height: 430px;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  position: relative;
  overflow: hidden;
  width: 100%;
}
/* line 211, ../sass/_template_specific.scss */
#slider_inner_container .image-slider-fixed img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  max-width: initial;
}
/* line 223, ../sass/_template_specific.scss */
#slider_inner_container .slogan-container {
  position: relative;
  height: 430px;
}
/* line 227, ../sass/_template_specific.scss */
#slider_inner_container .slider_text {
  display: block;
  box-sizing: border-box;
  width: 100%;
  text-align: center;
  color: white;
  font-size: 57px;
  line-height: 70px;
  text-transform: uppercase;
  font-weight: lighter;
  text-shadow: 1px 0 8px #323232;
  position: absolute;
  top: 100px;
}

/* line 243, ../sass/_template_specific.scss */
#full_wrapper_booking {
  position: absolute;
  height: 100%;
  top: 40px;
  left: 0px;
  right: 0px;
  z-index: 35;
  height: 580px;
}

/* line 253, ../sass/_template_specific.scss */
#slider_inner_container #full_wrapper_booking {
  height: 400px;
}
/* line 256, ../sass/_template_specific.scss */
#slider_inner_container #full_wrapper_booking .booking_widget {
  top: 30px;
}

/* line 260, ../sass/_template_specific.scss */
#map-layer {
  display: block;
  position: absolute;
  top: -732px;
  width: 100%;
  z-index: 20;
}

/* line 268, ../sass/_template_specific.scss */
.button-map {
  position: absolute;
  bottom: 0px;
  right: 0px;
  background: rgba(0, 0, 0, 0.7);
  text-align: center;
  width: 300px;
  padding: 10px 0px;
  color: white;
  text-transform: uppercase;
  font-size: 16px;
  font-weight: 300;
  cursor: pointer;
}
/* line 282, ../sass/_template_specific.scss */
.button-map:hover {
  color: #73a841;
}

/* line 287, ../sass/_template_specific.scss */
#slider_inner_container #map-layer {
  top: 0px;
}

/*============= CONTENT ==================*/
/* line 294, ../sass/_template_specific.scss */
#content {
  background: white;
  padding: 60px 0px;
}

/* line 298, ../sass/_template_specific.scss */
.content_subtitle_wrapper {
  text-align: center;
  width: 800px;
  margin: 0 auto;
}

/* line 303, ../sass/_template_specific.scss */
h3.content_subtitle_title {
  font-size: 36px;
  text-transform: initial;
  color: #787878;
  margin-bottom: 40px;
}

/* line 310, ../sass/_template_specific.scss */
.minigallery {
  text-align: center;
  margin-top: 40px;
}
/* line 314, ../sass/_template_specific.scss */
.minigallery h4 {
  font-size: 30px;
  color: #787878;
  text-transform: uppercase;
}
/* line 320, ../sass/_template_specific.scss */
.minigallery li {
  float: left;
  width: 226px;
  height: 143px;
  overflow: hidden;
  margin-left: 5px;
  position: relative;
}
/* line 328, ../sass/_template_specific.scss */
.minigallery li.big {
  width: 440px;
  height: 291px;
}
/* line 332, ../sass/_template_specific.scss */
.minigallery li:nth-child(2),
.minigallery li:nth-child(3),
.minigallery li:nth-child(4) {
  margin-bottom: 5px;
}
/* line 337, ../sass/_template_specific.scss */
.minigallery li img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  webkit-transition: all 0.7s ease;
  -moz-transition: all 0.7s ease;
  -o-transition: all 0.7s ease;
  transition: all 0.7s ease;
}
/* line 351, ../sass/_template_specific.scss */
.minigallery li img:hover {
  webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}

/* line 358, ../sass/_template_specific.scss */
.separator {
  width: 100px;
  display: inline-block;
  border-bottom: 4px solid #787878;
  margin: 25px 0px 30px;
}

/* line 365, ../sass/_template_specific.scss */
.bottom-banners {
  background: #f7f7f7;
  padding: 40px 0px;
}
/* line 370, ../sass/_template_specific.scss */
.bottom-banners .block-banner {
  height: 360px;
  margin-bottom: 20px;
}
/* line 375, ../sass/_template_specific.scss */
.bottom-banners .column-left {
  float: left;
  text-align: center;
  width: 570px;
  height: 360px;
  background: white;
  position: relative;
}
/* line 383, ../sass/_template_specific.scss */
.bottom-banners .column-left .description {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
/* line 389, ../sass/_template_specific.scss */
.bottom-banners .column-left .description #title {
  color: #73a841;
  font-size: 24px;
  font-weight: 600;
}
/* line 395, ../sass/_template_specific.scss */
.bottom-banners .column-left .description .banner-link {
  display: inline-block;
  margin-top: 40px;
  color: #5a5a5a;
  text-decoration: none;
  font-weight: 600;
}
/* line 402, ../sass/_template_specific.scss */
.bottom-banners .column-left .description .banner-link:hover {
  color: #73a841;
}
/* line 407, ../sass/_template_specific.scss */
.bottom-banners .column-right {
  float: right;
  width: 570px;
  height: 360px;
  position: relative;
  overflow: hidden;
}
/* line 414, ../sass/_template_specific.scss */
.bottom-banners .column-right img {
  webkit-transition: all 0.7s ease;
  -moz-transition: all 0.7s ease;
  -o-transition: all 0.7s ease;
  transition: all 0.7s ease;
}
/* line 420, ../sass/_template_specific.scss */
.bottom-banners .column-right img:hover {
  webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}
/* line 427, ../sass/_template_specific.scss */
.bottom-banners #reverse .column-left {
  float: right;
}
/* line 430, ../sass/_template_specific.scss */
.bottom-banners #reverse .column-right {
  float: left;
}

/* line 437, ../sass/_template_specific.scss */
.rooms-wrapper {
  margin-top: 40px;
  text-align: center;
}

/* line 442, ../sass/_template_specific.scss */
.room-block {
  background: #ebebeb;
  display: inline-block;
  float: none;
  vertical-align: top;
}
/* line 448, ../sass/_template_specific.scss */
.room-block .box-image {
  width: 360px;
  height: 220px;
  overflow: hidden;
  position: relative;
}
/* line 454, ../sass/_template_specific.scss */
.room-block .box-image img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  webkit-transition: all 0.7s ease;
  -moz-transition: all 0.7s ease;
  -o-transition: all 0.7s ease;
  transition: all 0.7s ease;
}
/* line 468, ../sass/_template_specific.scss */
.room-block .box-image:hover img {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}
/* line 475, ../sass/_template_specific.scss */
.room-block .box-description {
  box-sizing: border-box;
  padding: 20px;
  min-height: 435px;
  position: relative;
}
/* line 481, ../sass/_template_specific.scss */
.room-block h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 24px;
  font-weight: 500;
  color: #73a841;
}
/* line 488, ../sass/_template_specific.scss */
.room-block h4 {
  font-size: 16px;
}
/* line 491, ../sass/_template_specific.scss */
.room-block hr {
  margin-bottom: 20px;
}
/* line 494, ../sass/_template_specific.scss */
.room-block p {
  line-height: 22px;
}
/* line 497, ../sass/_template_specific.scss */
.room-block .box-description a {
  text-decoration: none;
  text-transform: uppercase;
  font-size: 16px;
  font-weight: 500;
  color: white;
  padding: 8px 15px;
  background: #73a841;
  display: block;
  width: 120px;
  text-align: center;
  position: absolute;
  bottom: 30px;
  left: 0px;
  right: 0px;
  margin: auto;
}
/* line 514, ../sass/_template_specific.scss */
.room-block .box-description a:hover {
  background: #5a8333;
}

/************************* PROMOTIONS ************************/
/* line 522, ../sass/_template_specific.scss */
.scapes-blocks {
  overflow: hidden;
  margin-top: 40px;
}

/* line 527, ../sass/_template_specific.scss */
.scapes-blocks .block {
  background: #f6f7f8;
  margin-bottom: 20px;
  width: 560px;
  float: left;
  position: relative;
}
/* line 534, ../sass/_template_specific.scss */
.scapes-blocks .block a.enlace_offer {
  display: inline-block;
  height: 300px;
  overflow: hidden;
  position: relative;
  width: 100%;
}
/* line 542, ../sass/_template_specific.scss */
.scapes-blocks .block a img {
  margin-bottom: -5px;
  width: 100%;
  position: absolute;
  margin: auto;
  top: 0;
  bottom: 0;
  webkit-transition: all 0.7s ease;
  -moz-transition: all 0.7s ease;
  -o-transition: all 0.7s ease;
  transition: all 0.7s ease;
}
/* line 554, ../sass/_template_specific.scss */
.scapes-blocks .block a:hover img {
  webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}
/* line 561, ../sass/_template_specific.scss */
.scapes-blocks .block .description {
  padding: 20px;
  position: relative;
  background-color: #e6e6e6;
  padding-right: 160px;
}
/* line 567, ../sass/_template_specific.scss */
.scapes-blocks .block .description .title-module {
  font-size: 20px;
  color: #73a841;
  font-weight: normal;
}
/* line 572, ../sass/_template_specific.scss */
.scapes-blocks .block .description .title-module .offer-subtitle {
  font-weight: 300;
  font-size: 18px;
}
/* line 578, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul {
  position: absolute;
  width: 200px;
  right: 0;
  top: 26px;
  text-align: right;
  padding-right: 10px;
}
/* line 586, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li {
  display: inline-block;
}
/* line 589, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a {
  background: #73a841;
  color: white;
  text-decoration: none;
  padding: 8px 15px;
}
/* line 595, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a:hover {
  background: #5a8333;
}
/* line 600, ../sass/_template_specific.scss */
.scapes-blocks .block .description p {
  margin-bottom: 0;
}

/* line 606, ../sass/_template_specific.scss */
.scapes-blocks .row1 {
  margin-right: 10px;
}

/* line 610, ../sass/_template_specific.scss */
.scapes-blocks .row2 {
  margin-left: 10px;
}

/* line 614, ../sass/_template_specific.scss */
.scapes-bottom-content {
  background: #63923b;
  padding: 20px;
}

/* line 619, ../sass/_template_specific.scss */
.event-modal h3 {
  font-size: 22px;
  color: #73a841;
  margin-bottom: 20px;
}

/* line 624, ../sass/_template_specific.scss */
.event-modal {
  line-height: 22px;
}

/* line 632, ../sass/_template_specific.scss */
.location-info-and-form-wrapper h1 {
  font-size: 36px;
  text-transform: initial;
  color: #787878;
  text-align: left;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  line-height: normal;
}

/* line 643, ../sass/_template_specific.scss */
.location-info-and-form-wrapper p, .location-info-and-form-wrapper strong {
  color: dimgrey;
}

/* line 648, ../sass/_template_specific.scss */
.location-info {
  line-height: 22px;
}
/* line 650, ../sass/_template_specific.scss */
.location-info p {
  margin-bottom: 10px;
  font-weight: 300;
}
/* line 654, ../sass/_template_specific.scss */
.location-info strong {
  font-weight: bold;
}

/* line 659, ../sass/_template_specific.scss */
#location-description-intro {
  margin-bottom: 30px;
}

/* line 666, ../sass/_template_specific.scss */
.form-contact #title {
  display: none !important;
}

/* line 670, ../sass/_template_specific.scss */
.form-contact #google-plus {
  display: none !important;
}

/* line 674, ../sass/_template_specific.scss */
.form-contact .fb-like {
  display: none !important;
}

/* line 678, ../sass/_template_specific.scss */
.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

/* line 683, ../sass/_template_specific.scss */
#contactContent .info {
  margin-top: 30px;
  overflow: hidden;
}

/* line 688, ../sass/_template_specific.scss */
.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: dimgrey;
  font-weight: 300;
}

/* line 696, ../sass/_template_specific.scss */
.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px !important;
  height: 30px;
  background-color: #e1e1e1;
  color: dimgrey;
  padding-left: 3px;
}

/* line 707, ../sass/_template_specific.scss */
.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px;
  background-color: #e1e1e1;
  color: dimgrey;
  text-align: left;
  font-size: 15px;
  padding: 5px;
}

/* line 719, ../sass/_template_specific.scss */
.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: -5px;
}

/* line 724, ../sass/_template_specific.scss */
.form-contact #contact-button {
  border-radius: 5px !important;
  height: 30px !important;
  width: 130px !important;
  background: #73a841 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;
}

/* line 740, ../sass/_template_specific.scss */
.form-contact #contact-button:hover {
  background-color: #63923b !important;
}

/* line 744, ../sass/_template_specific.scss */
.iframe_maps {
  display: table;
  padding: 20px 0px;
}

/* line 749, ../sass/_template_specific.scss */
.location-info-and-form-wrapper {
  margin: 0px;
  background: white;
  box-sizing: border-box;
  display: table;
  padding: 0px;
}

/* line 757, ../sass/_template_specific.scss */
#contactContent .contInput {
  width: 94%;
}

/* line 761, ../sass/_template_specific.scss */
.form-contact #contact-button-wrapper {
  margin-right: 0px;
}

/* line 765, ../sass/_template_specific.scss */
.menu_right_wrapper {
  width: 963px !important;
  margin-right: 0;
}

/* line 772, ../sass/_template_specific.scss */
.content-access {
  margin: 0 auto;
  text-align: center;
}
/* line 776, ../sass/_template_specific.scss */
.content-access .content-my-bookings {
  margin: 0 auto;
  width: 800px;
  -webkit-column-count: 2;
  -moz-column-count: 2;
  -ms-column-count: 2;
  -o-column-count: 2;
  column-count: 2;
  -webkit-column-gap: 40px;
  -moz-column-gap: 40px;
  -ms-column-gap: 40px;
  -o-column-gap: 40px;
  column-gap: 40px;
  text-align: justify;
  margin-bottom: 40px;
}
/* line 792, ../sass/_template_specific.scss */
.content-access p {
  line-height: 22px;
}
/* line 796, ../sass/_template_specific.scss */
.content-access .section-title {
  font-size: 36px;
  text-transform: initial;
  color: #787878;
  margin-bottom: 40px;
}
/* line 803, ../sass/_template_specific.scss */
.content-access #my-bookings-form-search-button {
  border-radius: 0px !important;
  height: 32px !important;
  width: 137px !important;
  background-color: #73a841;
  color: white;
  margin: auto;
  width: 150px;
  text-transform: uppercase;
  border-width: 0;
  display: block;
  margin-top: 20px;
  cursor: pointer;
}
/* line 817, ../sass/_template_specific.scss */
.content-access #my-bookings-form-search-button:hover {
  background-color: #63923b;
}
/* line 822, ../sass/_template_specific.scss */
.content-access #cancel-button-container #cancelButton {
  border-radius: 0px !important;
  height: 32px !important;
  width: 137px !important;
  background-color: #73a841;
  color: white;
  margin: auto;
  width: 150px;
  text-transform: uppercase;
  border-width: 0;
  display: none;
  margin-top: 20px;
}
/* line 836, ../sass/_template_specific.scss */
.content-access #reservation {
  margin-top: 0px !important;
}
/* line 840, ../sass/_template_specific.scss */
.content-access label {
  display: block;
  margin-top: 5px;
}
/* line 845, ../sass/_template_specific.scss */
.content-access input {
  display: block;
  margin: 0 auto;
  width: 216px;
}
/* line 850, ../sass/_template_specific.scss */
.content-access .my-bookings-booking-info {
  margin: 0 auto !important;
}

/*================= FOOTER =============*/
/* line 859, ../sass/_template_specific.scss */
footer {
  background: #5b5b5b;
  color: white;
  padding: 30px 0px;
  color: #f7f7f7;
  font-weight: 300;
}
/* line 866, ../sass/_template_specific.scss */
footer .footer_column_title,
footer #title_newsletter {
  margin-bottom: 5px;
  font-weight: 400;
}
/* line 871, ../sass/_template_specific.scss */
footer .main-section-div-wrapper a {
  color: white;
  line-height: 24px;
  text-decoration: none;
  text-transform: initial;
}
/* line 877, ../sass/_template_specific.scss */
footer .main-section-div-wrapper a:hover {
  text-decoration: underline;
  background: none;
  color: white;
  text-transform: initial;
}
/* line 884, ../sass/_template_specific.scss */
footer #section-active a {
  background: none;
  text-decoration: underline;
  color: white;
  padding: 0px !important;
  text-transform: initial;
}
/* line 892, ../sass/_template_specific.scss */
footer #suscEmail {
  margin-top: 10px;
  background: #787878;
  border: none;
  color: white;
  width: 180px;
  height: 20px;
}
/* line 900, ../sass/_template_specific.scss */
footer button {
  margin-top: 10px;
  width: 100px;
  height: 25px;
  text-transform: uppercase;
  cursor: pointer;
}
/* line 907, ../sass/_template_specific.scss */
footer button:hover {
  background: #bebebe;
  border: none;
}
/* line 912, ../sass/_template_specific.scss */
footer .full-copyright {
  text-align: center;
}
/* line 915, ../sass/_template_specific.scss */
footer .full-copyright a {
  color: #f7f7f7;
  text-decoration: none;
}
/* line 921, ../sass/_template_specific.scss */
footer .share {
  margin: 20px auto;
  text-align: center;
}
/* line 925, ../sass/_template_specific.scss */
footer .share div {
  display: inline-block;
}
/* line 928, ../sass/_template_specific.scss */
footer #google_plus_one {
  position: relative;
  top: 5px;
}

/*================ Hidden Menu ================*/
/* line 939, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu {
  background: white;
  position: fixed !important;
  top: 0;
  display: none;
  z-index: 401;
  width: 100%;
  box-shadow: 0 2px 6px -2px gray;
  height: 100px;
}
/* line 949, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu #main-sections-inner {
  text-align: center;
}
/* line 953, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu div#logoDiv {
  position: relative;
  z-index: 3;
  margin-top: 18px;
  margin-bottom: 5px;
  width: 147px;
}
/* line 960, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu div#logoDiv img {
  box-shadow: none;
}
/* line 965, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu a.deploy_booking {
  display: inline-block;
  float: right;
  border: 0;
  background: #73a841;
  color: white;
  height: 100%;
  text-transform: uppercase;
  line-height: 40px;
  padding: 0 10px;
  position: relative;
  z-index: 3;
  cursor: pointer;
  font-size: 14px;
  outline: none;
  border-bottom: 10px solid white;
  border-top: 10px solid white;
  width: 90px !important;
  margin-top: 22px;
  text-align: center;
}
/* line 986, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu a.deploy_booking:hover {
  background: #63923b;
}
/* line 991, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu #main_menu {
  background: white;
  margin-top: 30px;
  width: 830px !important;
  clear: none;
  float: left;
}
/* line 998, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu .book-menu {
  display: none;
}
/* line 1001, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu #mainMenuDiv {
  border-top: none;
}
/* line 1004, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu #mainMenuDiv a {
  padding: 10px 15px;
}
/* line 1007, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu #section-active a,
#wrapper-header.hidden_top_menu #mainMenuDiv a:hover {
  color: #73a841;
}
/* line 1011, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu .main-section-div-wrapper {
  vertical-align: top;
}
/* line 1014, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu .main-section-div-wrapper ul {
  position: absolute;
}
/* line 1017, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu .main-section-div-wrapper ul a {
  background: #73a841;
  color: white !important;
}

/* line 1026, ../sass/_template_specific.scss */
html[lang="pt"] .room-block .box-description {
  min-height: 455px;
}
