# -*- coding: utf-8 -*-
import copy
import logging
import urllib
from collections import OrderedDict

import re

import requests

from booking_process.utils.country import ALL_COUNTRIES_LIST

__author__ = 'adrian'

from booking_process.libs.communication import directDataProvider
from paraty_commons_3.content.web_content_utils import convertEntityWebPropertyListToMap
from booking_process.utils.booking.normalizationUtils import normalizeForClassName

from booking_process.constants.advance_configs_names import EMAIL_CONTACT_FORMS, ROOMS_ICONS

from booking_process.utils.data_management.configs_utils import get_config_property_value

from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name, getPicturesForKey
from booking_process.utils.data_management.promotions_utils import get_offers
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name, get_sections_from_type
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity, getRelatedWebPageProperties
from booking_process.utils.development.dev_booking_utils import DEV
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_language_code, get_web_dictionary
from booking_process.utils.namespaces.namespace_utils import set_namespace, get_namespace
from utils.managers_cache.manager_cache import managers_cache


def _retreive_hotels_promotions(all_promotions, language):
	promotions_dict = {}
	for promotion_element in all_promotions:
		promotions_hotels = promotion_element.get('extra_properties', {}).get('hotels', '').split(";")
		promotion_pictures = getPicturesForKey(language, promotion_element.get('offerKey'), [])
		pictures_with_titles = list(filter(lambda x: x.get('title'), promotion_pictures))
		hotels_description_dict = dict(map(lambda x: (x.get('title'), x.get('description')), pictures_with_titles))
		for hotel in promotions_hotels:
			target_text_popup = unescape(promotion_element['extra_properties'].get('popup_info', ''))
			if hotels_description_dict.get(hotel):
				target_text_popup = hotels_description_dict.get(hotel)
			promotion_element['extra_properties']['popup_info'] = target_text_popup
			promotions_dict.setdefault(hotel, []).append(copy.deepcopy(promotion_element))


	return promotions_dict


def _get_sections_from_namespaces_config(namespaces_list, sections_namespaces, all_sections, language):
	all_hotels_match = []
	for namespace_element in namespaces_list:
		filtered_webpageproperty = list(filter(lambda x: x.value == namespace_element, sections_namespaces))
		if filtered_webpageproperty:
			section_key = filtered_webpageproperty[0].entityKey
			hotel_section = list(filter(lambda x: x.get('key') == section_key, all_sections))
			if hotel_section:
				hotel_section = hotel_section[0]
				_get_hotel_section_properties(hotel_section, language)
				all_hotels_match.append(hotel_section)

	return all_hotels_match


def _get_hotel_section_properties(hotel_section, language):
	hotel_advance_config = getSectionAdvanceProperties(hotel_section, language)
	hotel_section.update(hotel_advance_config)
	hotel_section['since'] = hotel_advance_config.get('price', '').replace(',','.')
	hotel_section['category'] = hotel_advance_config.get('category', '').replace("LL", "")
	hotel_section['keys_category'] = "LL" in hotel_advance_config.get('category', '')
	hotel_section['phones'] = hotel_advance_config.get('phone', '').split(";")
	hotel_section['fax'] = hotel_advance_config.get('fax', '').split(";") if hotel_advance_config.get('fax') else None
	hotel_section['map'] = unescape(hotel_advance_config.get('map'))
	hotel_section['address'] = unescape(hotel_advance_config.get('address'))
	hotel_section['extra_description'] = unescape(hotel_advance_config.get('extra_description'))
	hotel_section['register'] = unescape(hotel_advance_config.get('register'))
	hotel_section['services'] = hotel_advance_config.get('services', '').split(";")
	hotel_section['services_processed'] = _retreive_hotel_services(hotel_section['services'], language)
	hotel_section['keywords'] = _retreive_hotel_keywords(hotel_advance_config.get('keywords'), language)
	hotel_section['partners'] = _get_hotels_patners(hotel_advance_config.get('partners', ''))
	hotel_section['trust_you'] = unescape(hotel_advance_config.get('trust_big', ''))
	hotel_section['trust_you_small'] = get_trust_you_info(unescape(hotel_advance_config.get('trust_small', '')), language) if hotel_advance_config.get('trust_small') else None
	hotel_section['mobile_room_title'] = unescape(hotel_advance_config.get('mobile_room_title'))
	hotel_section['desktop_room_title'] = unescape(hotel_advance_config.get('desktop_room_title'))
	hotel_section['mobile_room_description'] = unescape(hotel_advance_config.get('mobile_room_description'))
	hotel_section['extra_info'] = unescape(hotel_advance_config.get('extra_info'))
	hotel_section['video'] = unescape(hotel_advance_config.get('video'))
	hotel_section['location_title_mobile'] = unescape(hotel_advance_config.get('location_title_mobile', "")).split("@")
	hotel_section['location_title_desktop'] = unescape(hotel_advance_config.get('location_title_desktop', "")).split("@")
	hotel_section['faqs'] = _retreive_hotel_faqs(hotel_advance_config.get('faqs'), language)
	if hotel_section.get('faqs'):
		hotel_section['faqs']['faqs_link'] = _get_faqs_section_link(language)
	hotel_section['informative_paper'] = unescape(hotel_advance_config.get('informative_paper', ""))

	poi_information = list(filter(lambda x:x.get('title') == hotel_section['hotel_namespace'], get_poi_hotels(language)))

	if poi_information:
		hotel_section['coordinates'] = {'lat': poi_information[0].get('lat'),'lng': poi_information[0].get('lng')}

	if hotel_advance_config.get('minigallery'):
		hotel_section['minigallery'] = get_pictures_from_section_name(hotel_advance_config['minigallery'], language)


def _retreive_hotel_keywords(hotel_keywords, language):
	if not hotel_keywords:
		return []

	hotel_keywords_list = hotel_keywords.split(";")
	available_keywords = get_pictures_from_section_name('_keywords_blocks', language)
	all_services_mapped = dict(map(lambda x: (x.get('title'), x), available_keywords))
	processed_keywords = map(lambda x: all_services_mapped.get(x), hotel_keywords_list)
	return list(filter(lambda x: x, processed_keywords))


def _retreive_hotel_services(hotel_services, language):
	all_services = get_pictures_from_section_name('_services_blocks', language)
	all_services_mapped = dict(map(lambda x: (x.get('title'), x), all_services))
	return map(lambda x: all_services_mapped.get(x), hotel_services)


def _retreive_hotel_faqs(faqs_numbers, language):
	if not faqs_numbers:
		return []

	all_faqs_pictures = get_pictures_from_section_name('_faqs_hotels_group', language)

	builded_dict = {
		'section_info': get_section_from_section_spanish_name('_faqs_hotels_group', language),
		'section_pictures': list(filter(lambda x: x.get('altText') and x.get('altText') in faqs_numbers, all_faqs_pictures))
	}

	return builded_dict


def _get_faqs_section_link(language):
	faqs_link = ""
	faqs_section = get_sections_from_type(u"Atención al cliente", language)
	if faqs_section:
		faqs_link = faqs_section[0].get("friendlyUrlInternational", "")

	return faqs_link


def get_poi_hotels(language):

	poi_hoteles = get_pictures_from_section_name("poi_hoteles", language)

	for poi in poi_hoteles:
		poi['description'] = poi['description'].replace('\n', '').replace('\r', '') if poi['description'] else ''
		poi_advance_properties = getSectionAdvanceProperties(poi, language)
		if poi_advance_properties.get('coordinates'):
			poi_cordinates = poi_advance_properties.get('coordinates').split(',')
			if len(poi_cordinates) == 2:
				poi['lat'] = poi_cordinates[0]
				poi['lng'] = poi_cordinates[1]

	return poi_hoteles


def _get_hotels_patners(partners_selected):
	all_partners = get_pictures_from_section_name('partners', SPANISH)
	selected_partners = partners_selected.split(";")
	partners_match = []
	for partner_element in all_partners:
		filter_partners = list(filter(lambda x: x == partner_element.get('title'), selected_partners))
		if filter_partners:
			partners_match.append(partner_element)

	return partners_match


def _get_destiny_of_hotel(namespace, destiny_info):
	for destiny_element in destiny_info:
		destiny_all_namespaces = map(lambda x: x.get('hotel_namespace'), destiny_element.get('hotels_sections'))
		if namespace in destiny_all_namespaces:
			return destiny_element


def getSectionAdvanceProperties(sectionToUse, language):
	if not sectionToUse:
		return {}

	return get_properties_for_entity(sectionToUse.get('key', False), language)


def _get_promotions_info(all_promotions, language):
	for promotion in all_promotions:
		promotion_pictures = getPicturesForKey(language, str(promotion.get('offerKey')), [])
		if promotion_pictures:
			picture_info = getSectionAdvanceProperties(promotion_pictures[0], language)
			if picture_info.get('link_personalized'):
				promotion["link_personalized"] = picture_info.get('link_personalized')

			if picture_info.get('hotels'):
				promotion['hotels_availables'] = picture_info.get('hotels', '').split(";")

			if picture_info.get('filter'):
				promotion['promotion_filters'] = picture_info.get('filter', '').split("@")

	return all_promotions


def _custom_contact_email_getter():
	contact_email = get_config_property_value(EMAIL_CONTACT_FORMS)
	if contact_email:
		return re.split(r'[,;]+', contact_email)
	else:
		manager_email = get_config_property_value("Email reservas")
		if manager_email:
			return  re.split(r'[,;]+', manager_email)


@managers_cache(entities='WebPageProperty')
def _get_rooms_elements(language):

	return _build_room_info_no_cache(language)


def build_destiny_blocks_config(blocks_section_config, language, hotels_info):
	dict_builded = {}

	all_hotels_unified = []
	for destiny_element in hotels_info:
		all_hotels_unified += destiny_element.get('hotels_sections', [])

	section_information = get_section_from_section_spanish_name(blocks_section_config, language)
	pictures_information = get_pictures_from_section_name(blocks_section_config, language)

	for picture_element in pictures_information:
		picture_properties = getSectionAdvanceProperties(picture_element, language)
		if picture_properties.get('color'):
			picture_element['color'] = picture_properties['color']

		if picture_properties.get('no_booking'):
			picture_element['no_booking'] = True

		if picture_properties.get('hotels'):
			available_hotels = list(filter(lambda x: x.get('hotel_namespace') in picture_properties['hotels'], all_hotels_unified))
			picture_element['namespaces'] = picture_properties.get('hotels')
			picture_element['hotels_list'] = available_hotels
			for hotel_element in picture_element['hotels_list']:
				target_query = 'text_' + hotel_element['hotel_namespace']
				if picture_properties.get(target_query):
					hotel_element['custom_additional_text'] = picture_properties[target_query]

	dict_builded['destiny_blocks_inline'] = pictures_information
	dict_builded['destiny_blocks_inline_section'] = section_information
	return dict_builded


def getPicturesPropertiesInner(language, section_name, params_list=None):
	pictures = get_pictures_from_section_name(section_name, language)
	if params_list:
		for img in pictures:
			advance_properties = getSectionAdvanceProperties(img, language)
			for key, value in advance_properties.items():
				if key in params_list:
					img[key] = value

	return pictures


def _process_advance_properties(advance_properties, language, result_params_dict, sectionToUse, all_sections):
	if advance_properties.get('hotels_with_services'):
		del result_params_dict['destiny_hotels']

		if advance_properties.get("hotels_map"):
			result_params_dict['hotels_map'] = True

	if advance_properties.get('hotels_with_keywords'):
		del result_params_dict['destiny_hotels']
		result_params_dict['keywords_hotels'] = True

		if advance_properties.get("hotels_map"):
			result_params_dict['hotels_map'] = True

	if advance_properties.get('info_blocks') or advance_properties.get('info_blocks_inline'):

		if advance_properties.get('info_blocks_inline'):
			target_section = advance_properties.get('info_blocks_inline')
			result_params_dict['info_blocks_inline'] = True
		else:
			target_section = advance_properties.get('info_blocks')

		info_blocks_pictures = get_pictures_from_section_name(target_section, language)
		for info_element in info_blocks_pictures:
			info_element_properties = getSectionAdvanceProperties(info_element,language)
			if info_element_properties.get('video'):
				info_element['video'] = unescape(info_element_properties['video'])

			if info_element.get('linkUrl'):
				info_element['info_blocks_pictures'] = get_pictures_from_section_name(info_element['linkUrl'], language)

		result_params_dict['info_blocks'] = info_blocks_pictures

	if advance_properties.get('banner_collapse'):
		banner_colapse_pictures = getPicturesPropertiesInner(language, advance_properties.get('banner_collapse'), ['no_collapse'])
		banner_colapse_categories = {}
		for banner_colapse_element in banner_colapse_pictures:
			alt_text = banner_colapse_element.get('altText')
			title_text = banner_colapse_element.get('title')
			no_collapse = banner_colapse_element.get("no_collapse", False)

			banner_colapse_categories.setdefault(alt_text, {'no_collapse': False, 'phones': OrderedDict()})
			banner_colapse_categories[alt_text]['phones'].setdefault(title_text, []).append(banner_colapse_element)

			if no_collapse:
				banner_colapse_categories[alt_text]['no_collapse'] = True

		result_params_dict['banner_collapse'] = OrderedDict(sorted(banner_colapse_categories.items(), key=lambda kv: kv[1]['no_collapse'], reverse=False))

	if advance_properties.get('group_form'):
		result_params_dict['group_form'] = True

	if advance_properties.get('disable_widget_popup'):
		result_params_dict['disable_widget_popup'] = True

	if advance_properties.get('service_hotels'):
		selected_service = advance_properties.get('service_hotels')
		result_params_dict['services_hotels'] = result_params_dict['destiny_hotels']
		for destiny in result_params_dict['services_hotels']:
			for hotel_element in destiny['hotels_sections']:
				hotel_services = hotel_element.get('services')
				if hotel_services and not selected_service in hotel_services:
					hotel_element['disabled'] = True

	if advance_properties.get('review_section'):
		result_params_dict['review_section'] = True
		result_params_dict['hotels_review'] = []

		for location_element in result_params_dict['destiny_hotels']:
			for hotel_element in location_element.get('hotels_sections', []):
				if hotel_element.get('trust_you'):
					result_params_dict['hotels_review'].append(hotel_element)

	if advance_properties.get('club_search') or advance_properties.get('club_retreive'):
		result_params_dict['club_search_section'] = True

	if advance_properties.get('modify_profile'):
		result_params_dict['club_profile_section'] = True

	if advance_properties.get('security_message'):
		result_params_dict['security_message'] = advance_properties.get('security_message')

	if advance_properties.get('remove_message'):
		result_params_dict['remove_message'] = advance_properties.get('remove_message')

	if advance_properties.get('subject_email'):
		result_params_dict['subject_club_mail'] = advance_properties['subject_email']

	if advance_properties.get('thanks'):
		result_params_dict['club_thanks_message'] = advance_properties.get('thanks')

	if advance_properties.get('destiny_blocks_inline'):
		hotels_information = result_params_dict.get('destiny_hotels')
		destiny_blocks_dict = build_destiny_blocks_config(advance_properties['destiny_blocks_inline'], language, hotels_information)
		result_params_dict.update(destiny_blocks_dict)

	if advance_properties.get('minigallery'):
		result_params_dict['minigallery'] = get_pictures_from_section_name(advance_properties['minigallery'], language)

	if advance_properties.get("promocode"):
		result_params_dict['promocode'] = advance_properties.get("promocode")

	if advance_properties.get("colors"):
		colors = advance_properties.get("colors").split(";")

		if len(colors) == 2:
			result_params_dict['color_1'] = colors[0]
			result_params_dict['color_2'] = colors[1]

		elif len(colors) == 1:
			result_params_dict['color_1'] = colors[0]

	if advance_properties.get("countdown"):
		result_params_dict['countdown'] = advance_properties.get("countdown")

	if advance_properties.get("christmas_landing"):
		result_params_dict['christmas_landing'] = advance_properties.get("christmas_landing")

	if advance_properties.get("bannersx3"):
		result_params_dict['bannerx3_content'] = get_section_from_section_spanish_name(advance_properties.get("bannersx3"), language)
		result_params_dict['bannersx3'] = getPicturesPropertiesInner(language, advance_properties.get("bannersx3"), ['gallery'])
		for pic in result_params_dict['bannersx3']:
			if pic.get('gallery'):
				pic['images'] = get_pictures_from_section_name(pic.get('gallery'), language)

	if advance_properties.get("slider_height"):
		result_params_dict['slider_height'] = advance_properties.get("slider_height")

	if advance_properties.get("hide_title"):
		result_params_dict['hide_title'] = advance_properties.get("hide_title")

	if advance_properties.get("logotype"):
		result_params_dict['custom_logo'] = advance_properties.get("logotype")

	section_to_use = advance_properties.get("bannerbooking")
	if section_to_use:
		actual_namespace = get_namespace()

		try:
			namespace = advance_properties.get("hotel_namespace")

			if namespace:
				if DEV:
					namespace = "r__" + namespace
				try:
					set_namespace(namespace)
					bannerbooking = getPicturesPropertiesInner(language, section_to_use, ['nomobile'])

					result_params_dict['bannerbooking'] = list(filter(lambda x: x.get("nomobile"), bannerbooking))

				except Exception as e:
					logging.info("Error setting namespace in individual offers")
					logging.info(e)

		except Exception as e:
			logging.info("Something was wrong trying to get individual offers")
			logging.info(e)
		finally:
			logging.info("Setting corporate namespace")
			set_namespace(actual_namespace)

	if advance_properties.get("special_extra_banner"):
		special_extra_banner = getPicturesPropertiesInner(language, advance_properties.get("special_extra_banner"), ['nomobile'])
		result_params_dict['special_extra_banner'] = list(filter(lambda x: x.get("nomobile"), special_extra_banner))

	if advance_properties.get('blocks_desktop'):
		result_params_dict['blocks_desktop'] = {
			'section': get_section_from_section_spanish_name(advance_properties.get('blocks_desktop'), language),
			'pictures': getPicturesPropertiesInner(language, advance_properties['blocks_desktop'], [])
		}

	if advance_properties.get('hotels_carousel'):
		result_params_dict['all_hotels_link'] = get_section_from_section_spanish_name('destinos y hoteles', language)
		result_params_dict['hotels_carousel'] = retreive_specific_hotels_info(advance_properties['hotels_carousel'], result_params_dict['destiny_hotels'])

	if advance_properties.get('destiny_hotels'):
		result_params_dict['destiny_hotels'] = [{'hotels_sections': retreive_specific_hotels_info('destiny=' + advance_properties.get('destiny_hotels'), result_params_dict['destiny_hotels'])}]
		result_params_dict['keywords_hotels'] = True
		if advance_properties.get("hotels_map"):
			result_params_dict['hotels_map'] = True

	if advance_properties.get('advantages_blocks'):
		result_params_dict['advantages_blocks'] = build_advantages_blocks(advance_properties['advantages_blocks'], language)

	if advance_properties.get('cycle_banner'):
		cycle_banner_content = get_section_from_section_spanish_name(advance_properties['cycle_banner'], language)
		actual_subtitle = cycle_banner_content.get('subtitle', '')
		if '@' in actual_subtitle:
			mini_title, title = actual_subtitle.split("@")
			cycle_banner_content['mini_title'] = mini_title
			cycle_banner_content['subtitle'] = title

		result_params_dict['cycle_banner_content'] = cycle_banner_content
		result_params_dict['cycle_banner'] = getPicturesPropertiesInner(language, advance_properties['cycle_banner'], ['gallery'])
		for banner in result_params_dict['cycle_banner']:
			if banner.get("gallery"):
				banner['images'] = get_pictures_from_section_name(banner['gallery'], language)

	if advance_properties.get('faqs'):
		result_params_dict['faqs_config'] = retreive_all_faqs(advance_properties.get('faqs'), language)

	if advance_properties.get('404_page'):
		result_params_dict['404_page'] = get_pictures_from_section_name(sectionToUse.get('sectionName'), language)

	if advance_properties.get('gallery_block'):
		result_params_dict['gallery_block'] = get_pictures_from_section_name(advance_properties['gallery_block'], language)

	if advance_properties.get('partners'):
		result_params_dict['partners'] = get_pictures_from_section_name(advance_properties['partners'], language)

	deploy_blocks = advance_properties.get('deploy_blocks')
	if deploy_blocks:
		target_section = get_section_from_section_spanish_name(deploy_blocks, language)
		if target_section:
			result_params_dict['deploy_blocks'] = {'section': target_section,
											   'pictures': get_pictures_from_section_name(deploy_blocks, language)}

			section_title = result_params_dict['deploy_blocks']['section']['subtitle']
			result_params_dict['deploy_blocks']['section']['subtitle'] = section_title.split("@")
			result_params_dict['countries_list'] = ALL_COUNTRIES_LIST.values()

	offers_carousel = advance_properties.get('offers_carousel')
	if offers_carousel:
		offers_carousel_title = offers_carousel.split("@")
		result_params_dict['offers_carousel_mini_title'] = offers_carousel_title[0]
		if len(offers_carousel_title) > 1:
			result_params_dict['offers_carousel_main_title'] = offers_carousel_title[1]
		available_offers = get_offers(language)
		result_params_dict['promotions_carousel'] = available_offers
		result_params_dict['all_offers_link'] = get_sections_from_type(u"Ofertas", language)
		for promotion_element in available_offers:
			promotion_properties = get_properties_for_entity(promotion_element.get('offerKey'), language)
			promotion_pictures = getPicturesForKey(language, promotion_element.get('offerKey'), all_sections)
			if promotion_pictures:
				promotion_element['linkUrl'] = promotion_pictures[0]['linkUrl']

			if promotion_properties.get('type'):
				promotion_element['type'] = promotion_properties['type']
			if promotion_properties.get("popup_info"):
				promotion_element['popup_info'] = unescape(promotion_properties['popup_info'])


def _register_club_users(result_params_dict, language, sectionToUse):
	result_params_dict['users_register'] = True
	result_params_dict['advantage_info'] = get_section_from_section_spanish_name('user_register_upgrades', language)
	result_params_dict['steps_user_register'] = {
		'section': get_section_from_section_spanish_name('user_register_info', language),
		'pictures': get_pictures_from_section_name('user_register_info', language)
	}

	result_params_dict['register_conditions'] = get_section_from_section_spanish_name('user_register_conditions', language)


def _build_job_promotions(result_params_dict, language):
	result_params_dict['job_promotions'] = get_pictures_from_section_name('_work_blocks', language)
	for job_element in result_params_dict['job_promotions']:

		try:
			job_title_url = re.sub("(<strong>.*</strong>)", '', job_element.get('title', ''))
			job_element['title_url'] = urllib.parse.quote(job_title_url, safe='')
		except Exception as e:
			logging.info("Cant generate url for job offer: %s" % e)

		advance_properties_job = getSectionAdvanceProperties(job_element, language)
		if advance_properties_job.get('type'):
			job_element['type'] = advance_properties_job['type']
			_get_all_jobs_types(result_params_dict)

		if advance_properties_job.get('popup_info'):
			job_element['popup_info'] = unescape(advance_properties_job['popup_info'])

		if advance_properties_job.get('hotels'):
			job_element['hotels'] = advance_properties_job['hotels']
			job_element['destiny_info'] = []

			_get_destinies_of_jobs(result_params_dict)


def _get_all_jobs_types(result_params_dict):
	available_types = []
	for job_element in result_params_dict['job_promotions']:
		if job_element.get('type'):
			available_types.append(job_element['type'])

	result_params_dict['jobs_types'] = set(available_types)


def _get_destinies_of_jobs(result_params_dict):
	job_destiny_elements = map(lambda x: x.get('destiny_info'), result_params_dict['job_promotions'])
	job_destiny_elements = list(filter(lambda x: x, job_destiny_elements))
	job_destiny_merge = []
	for destiny_block in job_destiny_elements:
		job_destiny_merge += destiny_block

	result_params_dict['job_destinies'] = []
	for destiny_element in job_destiny_merge:
		actual_destinies = map(lambda x: x.get('title'), result_params_dict['job_destinies'])
		if not destiny_element.get('title') in actual_destinies:
			destiny_element['jobs_number'] = 1
			result_params_dict['job_destinies'].append(destiny_element)
		else:
			for destiny_added in result_params_dict['job_destinies']:
				if destiny_added.get("title") == destiny_element.get("title"):
					destiny_added['jobs_number'] += 1


def _build_individual_offer(result_params_dict, sectionToUse, language, allSections):
	promotion_info = sectionToUse
	promotion_info['pictures'] = getPicturesForKey(language, str(promotion_info.get('offerKey')), allSections)
	pictures_promotion = list(filter(lambda x: not x.get('title'), promotion_info['pictures']))

	result_params_dict['individual_offer'] = promotion_info


def _build_room_info_no_cache(language):
	rooms = directDataProvider.getEntityMap('RoomType')

	webPageProperties = []
	getRelatedWebPageProperties(rooms, webPageProperties, language)
	webPagePropertiesMap = convertEntityWebPropertyListToMap(webPageProperties)

	service_icons_section = get_config_property_value(ROOMS_ICONS)
	if service_icons_section:
		service_icons_section = getPicturesPropertiesInner(language, service_icons_section, ['room_icon', 'show_always'])

	result = []
	all_available_services = []

	for key, room in rooms.items():
		if room.removed:
			continue

		currentDict = {}
		currentDict['pictures'] = directDataProvider.get("Picture", {"mainKey": key})
		currentDict['pictures'] = sorted(currentDict['pictures'], key=lambda x:x.priorityInWeb)
		currentDict['picture'] = room.previewPicture
		currentDict['name'] = webPagePropertiesMap.get(key + "roomName", "")
		currentDict['description'] = webPagePropertiesMap.get(key + "roomDescription","")
		currentDict['visibleInWeb'] = room.visibleInWeb
		currentDict['spanishName'] = room.name
		currentDict['max_occupancy'] = webPagePropertiesMap.get(key + "max-occupancy","")
		currentDict['description_mobile'] = webPagePropertiesMap.get(key + "description_mobile","")

		room_properties = list(filter(lambda xy: key in xy[0], webPagePropertiesMap.items()))
		room_properties = dict(map(lambda xy: (xy[0].replace(key, ''), xy[1]), room_properties))

		if currentDict.get('max_occupancy'):
			adults, kids, babies = currentDict['max_occupancy'].split("-")
			currentDict['max_occupancy'] = {'adults': int(adults), 'kids': int(kids), 'babies': (babies)}

		if room_properties.get("icons") and service_icons_section:
			room_icons = room_properties['icons'].split(";")
			filtered_services_icons = []

			for room_icon in room_icons:
				for service in service_icons_section:
					if service.get("title") == room_icon:
						filtered_services_icons.append(service)
						break

			currentDict['services'] = filtered_services_icons
			all_available_services += filtered_services_icons

		currentDict['room_order'] = int(room_properties.get('order', 999))

		if currentDict.get('name'):
			result.append(currentDict)

	result = sorted(result, key=lambda x:x['room_order'])

	for room in result:
		room['exclusive_services'] = list(filter(lambda x: all_available_services.count(x) == 1 or x.get('show_always'), room.get('services', [])))

	return result


def _prepare_destinies_mobile(all_destinies_info, language):

	all_translations_dict = get_web_dictionary(language)

	all_hotels_united = []
	all_hotels_separated = [destiny_info['hotels_sections'] for destiny_info in all_destinies_info]
	for hotel_chunk in all_hotels_separated:
		for hotel_element in hotel_chunk:
			if not hotel_element.get("external_booking"):
				all_hotels_united.append(hotel_element)

	all_hotels_dict = {
		'group_id': '',
		'group_list': [
			{
				'id': 'https://best-corporate-dot-best-hoteles.appspot.com/booking1',
				'applicationId': ';'.join(map(lambda x: x.get('hotel_namespace'), all_hotels_united)),
				'value': all_translations_dict.get('T_todos_hoteles')
			}
		]
	}

	all_destinies_processed = [all_hotels_dict]

	for destiny_element in all_destinies_info:
		destiny_dict = {
			'group_id': destiny_element.get('title'),
			'group_list': []
		}

		for hotel_element in destiny_element.get('hotels_sections'):
			target_application_id = 'best-hoteles' if not 'eco-' in hotel_element.get('hotel_namespace') else 'eco-hotels'
			target_url_appId = '-dot-%s.appspot.com/booking1' % (target_application_id)
			hotel_booking_url = 'https://' + hotel_element.get('hotel_namespace') + target_url_appId
			hotel_dict = {
				'id': hotel_booking_url,
				'applicationId': hotel_element.get('hotel_namespace'),
				'value': hotel_element.get('title'),
				'kidsAgeRange': hotel_element.get("age_kids")
			}

			if hotel_element.get("short_name"):
				hotel_dict['value'] = hotel_element['short_name']

			if hotel_element.get("external_booking"):
				hotel_dict['id'] = hotel_element.get('website', '')

			if hotel_element.get('category'):
				if hotel_element.get('keys_category'):
					target_icon = '<i class="fas fa-key"></i>'
				else:
					target_icon = '<i class="fas fa-star"></i>'

				hotel_dict['value'] = "<span>%s</span>" % hotel_dict['value'] + target_icon * int(hotel_element['category'])

			destiny_dict['group_list'].append(hotel_dict)

		all_destinies_processed.append(destiny_dict)

	return all_destinies_processed


@managers_cache(key_generator=lambda f, a, k: f"trust_you_{a[0].replace('.', '').replace(':', '').replace('/', '')}-{a[1]}", entities='WebPageProperty')
def get_trust_you_info(url, language):
	language_code = get_language_code(language)
	try:
		if url:
			target_url = url
			if target_url and not '&lang=' in target_url:
				target_url = target_url + '&lang=' + language_code
			response = requests.get(target_url, timeout=6)
			return response.content
	except:
		logging.info("Cant get the following url: %s" % (url + '&lang=' + language_code))


@managers_cache(entities="WebPageProperty")
def retreive_hotel_rich_snippets(hotel_namespace):
	actual_hotel_namespace = get_namespace()
	rich_snippets = {}

	try:
		set_namespace(hotel_namespace)
		rich_snippets = get_section_from_section_spanish_name('rich snippet', SPANISH)
	finally:
		set_namespace(actual_hotel_namespace)

	return rich_snippets


def retreive_specific_hotels_info(hotels_to_retreive, all_destinies):
	all_hotels = []
	for destiny in all_destinies:
		all_hotels += destiny.get('hotels_sections')

	if "destiny" in hotels_to_retreive:
		destiny_selected = normalizeForClassName(hotels_to_retreive.replace("destiny=", ""))
		filtered_hotels = list(filter(lambda x: x.get('destiny') == destiny_selected, all_hotels))
	elif hotels_to_retreive == 'all':
		filtered_hotels = all_hotels
	else:
		filtered_hotels = list(filter(lambda x: x.get('hotel_namespace') in hotels_to_retreive, all_hotels))

	return filtered_hotels


def build_advantages_blocks(config_value, language):
	context_dict = {
		'section': get_section_from_section_spanish_name(config_value, language)
	}

	advantages_pictures = getPicturesPropertiesInner(language, config_value, ['icon'])
	context_dict['ticks'] = list(filter(lambda x: 'external_link' not in x.get('title'), advantages_pictures))
	context_dict['external_link'] = list(filter(lambda x: 'external_link' in x.get('title'), advantages_pictures))

	for picture in context_dict.get("ticks", []):
		picture.update(get_properties_for_entity(picture.get("key"), language))

	return context_dict


def retreive_all_faqs(faq_property, language):
	all_faqs = getPicturesPropertiesInner(language, '_faqs_hotels_group', ['category'])
	if faq_property == 'all':
		return all_faqs
