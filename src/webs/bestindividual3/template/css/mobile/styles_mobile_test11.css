@charset "UTF-8";
@import url("https://fonts.googleapis.com/css?family=Open+Sans:300,400,700,700i&display=swap");
@font-face {
  font-family: 'icomoon';
  src: url("/static_1/fonts/iconmoon/icomoon.eot?ytm7g4");
  src: url("/static_1/fonts/iconmoon/icomoon.eot?ytm7g4#iefix") format("embedded-opentype"), url("/static_1/fonts/iconmoon/icomoon.ttf?ytm7g4") format("truetype"), url("/static_1/fonts/iconmoon/icomoon.woff?ytm7g4") format("woff"), url("/static_1/fonts/iconmoon/icomoon.svg?ytm7g4#icomoon") format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
/* line 13, ../../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* line 28, ../../../../../sass/plugins/_iconmoon.scss */
.icon-terrace:before {
  content: "\ea52";
}

/* line 31, ../../../../../sass/plugins/_iconmoon.scss */
.icon-ico-hotelmanager:before {
  content: "\ea4c";
}

/* line 34, ../../../../../sass/plugins/_iconmoon.scss */
.icon-ico-paritymaker:before {
  content: "\ea4d";
}

/* line 37, ../../../../../sass/plugins/_iconmoon.scss */
.icon-ico-priceseeker:before {
  content: "\ea4e";
}

/* line 40, ../../../../../sass/plugins/_iconmoon.scss */
.icon-ico-ratecheck:before {
  content: "\ea4f";
}

/* line 43, ../../../../../sass/plugins/_iconmoon.scss */
.icon-ico-rescueseeker:before {
  content: "\ea50";
}

/* line 46, ../../../../../sass/plugins/_iconmoon.scss */
.icon-ico-reviewseeker:before {
  content: "\ea51";
}

/* line 49, ../../../../../sass/plugins/_iconmoon.scss */
.icon-couponlong:before {
  content: "\ea4a";
}

/* line 52, ../../../../../sass/plugins/_iconmoon.scss */
.icon-coupon:before {
  content: "\ea4b";
}

/* line 55, ../../../../../sass/plugins/_iconmoon.scss */
.icon-cocktel:before {
  content: "\ea46";
}

/* line 58, ../../../../../sass/plugins/_iconmoon.scss */
.icon-gafas:before {
  content: "\ea47";
}

/* line 61, ../../../../../sass/plugins/_iconmoon.scss */
.icon-pelota:before {
  content: "\ea48";
}

/* line 64, ../../../../../sass/plugins/_iconmoon.scss */
.icon-piscina:before {
  content: "\ea49";
}

/* line 67, ../../../../../sass/plugins/_iconmoon.scss */
.icon-email2:before {
  content: "\ea43";
}

/* line 70, ../../../../../sass/plugins/_iconmoon.scss */
.icon-luggage2:before {
  content: "\ea44";
}

/* line 73, ../../../../../sass/plugins/_iconmoon.scss */
.icon-nodisturb:before {
  content: "\ea45";
}

/* line 76, ../../../../../sass/plugins/_iconmoon.scss */
.icon-percent:before {
  content: "\61";
}

/* line 79, ../../../../../sass/plugins/_iconmoon.scss */
.icon-bed:before {
  content: "\62";
}

/* line 82, ../../../../../sass/plugins/_iconmoon.scss */
.icon-dots:before {
  content: "\63";
}

/* line 85, ../../../../../sass/plugins/_iconmoon.scss */
.icon-buffet:before {
  content: "\e900";
}

/* line 88, ../../../../../sass/plugins/_iconmoon.scss */
.icon-zen:before {
  content: "\e901";
}

/* line 91, ../../../../../sass/plugins/_iconmoon.scss */
.icon-drink:before {
  content: "\e902";
}

/* line 94, ../../../../../sass/plugins/_iconmoon.scss */
.icon-bike:before {
  content: "\e903";
}

/* line 97, ../../../../../sass/plugins/_iconmoon.scss */
.icon-celiac:before {
  content: "\e904";
}

/* line 100, ../../../../../sass/plugins/_iconmoon.scss */
.icon-chart:before {
  content: "\e905";
}

/* line 103, ../../../../../sass/plugins/_iconmoon.scss */
.icon-chips:before {
  content: "\e906";
}

/* line 106, ../../../../../sass/plugins/_iconmoon.scss */
.icon-clock:before {
  content: "\e907";
}

/* line 109, ../../../../../sass/plugins/_iconmoon.scss */
.icon-download:before {
  content: "\e908";
}

/* line 112, ../../../../../sass/plugins/_iconmoon.scss */
.icon-friends:before {
  content: "\e909";
}

/* line 115, ../../../../../sass/plugins/_iconmoon.scss */
.icon-group:before {
  content: "\e90a";
}

/* line 118, ../../../../../sass/plugins/_iconmoon.scss */
.icon-headset:before {
  content: "\e90b";
}

/* line 121, ../../../../../sass/plugins/_iconmoon.scss */
.icon-hipster:before {
  content: "\e90c";
}

/* line 124, ../../../../../sass/plugins/_iconmoon.scss */
.icon-lamp:before {
  content: "\e90d";
}

/* line 127, ../../../../../sass/plugins/_iconmoon.scss */
.icon-like:before {
  content: "\e90e";
}

/* line 130, ../../../../../sass/plugins/_iconmoon.scss */
.icon-map:before {
  content: "\e90f";
}

/* line 133, ../../../../../sass/plugins/_iconmoon.scss */
.icon-men:before {
  content: "\e910";
}

/* line 136, ../../../../../sass/plugins/_iconmoon.scss */
.icon-monument:before {
  content: "\e911";
}

/* line 139, ../../../../../sass/plugins/_iconmoon.scss */
.icon-new:before {
  content: "\e912";
}

/* line 142, ../../../../../sass/plugins/_iconmoon.scss */
.icon-pig:before {
  content: "\e913";
}

/* line 145, ../../../../../sass/plugins/_iconmoon.scss */
.icon-pdf:before {
  content: "\e914";
}

/* line 148, ../../../../../sass/plugins/_iconmoon.scss */
.icon-play:before {
  content: "\e915";
}

/* line 151, ../../../../../sass/plugins/_iconmoon.scss */
.icon-row:before {
  content: "\e916";
}

/* line 154, ../../../../../sass/plugins/_iconmoon.scss */
.icon-uniE917:before {
  content: "\e917";
}

/* line 157, ../../../../../sass/plugins/_iconmoon.scss */
.icon-uniE918:before {
  content: "\e918";
}

/* line 160, ../../../../../sass/plugins/_iconmoon.scss */
.icon-uniE919:before {
  content: "\e919";
}

/* line 163, ../../../../../sass/plugins/_iconmoon.scss */
.icon-uniE91A:before {
  content: "\e91a";
}

/* line 166, ../../../../../sass/plugins/_iconmoon.scss */
.icon-uniE91B:before {
  content: "\e91b";
}

/* line 169, ../../../../../sass/plugins/_iconmoon.scss */
.icon-uniE91C:before {
  content: "\e91c";
}

/* line 172, ../../../../../sass/plugins/_iconmoon.scss */
.icon-sea:before {
  content: "\e91d";
}

/* line 175, ../../../../../sass/plugins/_iconmoon.scss */
.icon-slide:before {
  content: "\e91e";
}

/* line 178, ../../../../../sass/plugins/_iconmoon.scss */
.icon-smile:before {
  content: "\e91f";
}

/* line 181, ../../../../../sass/plugins/_iconmoon.scss */
.icon-tick:before {
  content: "\e920";
}

/* line 184, ../../../../../sass/plugins/_iconmoon.scss */
.icon-ticket:before {
  content: "\e921";
}

/* line 187, ../../../../../sass/plugins/_iconmoon.scss */
.icon-trees:before {
  content: "\e922";
}

/* line 190, ../../../../../sass/plugins/_iconmoon.scss */
.icon-upgrade:before {
  content: "\e923";
}

/* line 193, ../../../../../sass/plugins/_iconmoon.scss */
.icon-watergame:before {
  content: "\e924";
}

/* line 196, ../../../../../sass/plugins/_iconmoon.scss */
.icon-wedding:before {
  content: "\e925";
}

/* line 199, ../../../../../sass/plugins/_iconmoon.scss */
.icon-basketball:before {
  content: "\e926";
}

/* line 202, ../../../../../sass/plugins/_iconmoon.scss */
.icon-books:before {
  content: "\e927";
}

/* line 205, ../../../../../sass/plugins/_iconmoon.scss */
.icon-calendar:before {
  content: "\e928";
}

/* line 208, ../../../../../sass/plugins/_iconmoon.scss */
.icon-candles:before {
  content: "\e929";
}

/* line 211, ../../../../../sass/plugins/_iconmoon.scss */
.icon-coins:before {
  content: "\e92a";
}

/* line 214, ../../../../../sass/plugins/_iconmoon.scss */
.icon-cup:before {
  content: "\e92b";
}

/* line 217, ../../../../../sass/plugins/_iconmoon.scss */
.icon-cutlery:before {
  content: "\e92c";
}

/* line 220, ../../../../../sass/plugins/_iconmoon.scss */
.icon-dice:before {
  content: "\e92d";
}

/* line 223, ../../../../../sass/plugins/_iconmoon.scss */
.icon-doc:before {
  content: "\e92e";
}

/* line 226, ../../../../../sass/plugins/_iconmoon.scss */
.icon-email:before {
  content: "\e92f";
}

/* line 229, ../../../../../sass/plugins/_iconmoon.scss */
.icon-euro:before {
  content: "\e930";
}

/* line 232, ../../../../../sass/plugins/_iconmoon.scss */
.icon-info:before {
  content: "\e931";
}

/* line 235, ../../../../../sass/plugins/_iconmoon.scss */
.icon-light:before {
  content: "\e932";
}

/* line 238, ../../../../../sass/plugins/_iconmoon.scss */
.icon-night:before {
  content: "\e933";
}

/* line 241, ../../../../../sass/plugins/_iconmoon.scss */
.icon-pet:before {
  content: "\e934";
}

/* line 244, ../../../../../sass/plugins/_iconmoon.scss */
.icon-shell:before {
  content: "\e935";
}

/* line 247, ../../../../../sass/plugins/_iconmoon.scss */
.icon-sofa:before {
  content: "\e936";
}

/* line 250, ../../../../../sass/plugins/_iconmoon.scss */
.icon-star:before {
  content: "\e937";
}

/* line 253, ../../../../../sass/plugins/_iconmoon.scss */
.icon-user:before {
  content: "\e938";
}

/* line 256, ../../../../../sass/plugins/_iconmoon.scss */
.icon-wii:before {
  content: "\e939";
}

/* line 259, ../../../../../sass/plugins/_iconmoon.scss */
.icon-ball:before {
  content: "\e93a";
}

/* line 262, ../../../../../sass/plugins/_iconmoon.scss */
.icon-booking:before {
  content: "\e93b";
}

/* line 265, ../../../../../sass/plugins/_iconmoon.scss */
.icon-cleanset:before {
  content: "\e93c";
}

/* line 268, ../../../../../sass/plugins/_iconmoon.scss */
.icon-comment:before {
  content: "\e93d";
}

/* line 271, ../../../../../sass/plugins/_iconmoon.scss */
.icon-ethernet:before {
  content: "\e93e";
}

/* line 274, ../../../../../sass/plugins/_iconmoon.scss */
.icon-eye:before {
  content: "\e93f";
}

/* line 277, ../../../../../sass/plugins/_iconmoon.scss */
.icon-feet:before {
  content: "\e940";
}

/* line 280, ../../../../../sass/plugins/_iconmoon.scss */
.icon-fridge:before {
  content: "\e941";
}

/* line 283, ../../../../../sass/plugins/_iconmoon.scss */
.icon-hairdrier:before {
  content: "\e942";
}

/* line 286, ../../../../../sass/plugins/_iconmoon.scss */
.icon-handicap:before {
  content: "\e943";
}

/* line 289, ../../../../../sass/plugins/_iconmoon.scss */
.icon-iron:before {
  content: "\e944";
}

/* line 292, ../../../../../sass/plugins/_iconmoon.scss */
.icon-key:before {
  content: "\e945";
}

/* line 295, ../../../../../sass/plugins/_iconmoon.scss */
.icon-lift:before {
  content: "\e946";
}

/* line 298, ../../../../../sass/plugins/_iconmoon.scss */
.icon-mapmarker:before {
  content: "\e947";
}

/* line 301, ../../../../../sass/plugins/_iconmoon.scss */
.icon-mask:before {
  content: "\e948";
}

/* line 304, ../../../../../sass/plugins/_iconmoon.scss */
.icon-mouse:before {
  content: "\e949";
}

/* line 307, ../../../../../sass/plugins/_iconmoon.scss */
.icon-movie:before {
  content: "\e94a";
}

/* line 310, ../../../../../sass/plugins/_iconmoon.scss */
.icon-mug:before {
  content: "\e94b";
}

/* line 313, ../../../../../sass/plugins/_iconmoon.scss */
.icon-plug:before {
  content: "\e94c";
}

/* line 316, ../../../../../sass/plugins/_iconmoon.scss */
.icon-plus:before {
  content: "\e94d";
}

/* line 319, ../../../../../sass/plugins/_iconmoon.scss */
.icon-printer:before {
  content: "\e94e";
}

/* line 322, ../../../../../sass/plugins/_iconmoon.scss */
.icon-sack:before {
  content: "\e94f";
}

/* line 325, ../../../../../sass/plugins/_iconmoon.scss */
.icon-shower:before {
  content: "\e950";
}

/* line 328, ../../../../../sass/plugins/_iconmoon.scss */
.icon-solarium:before {
  content: "\e951";
}

/* line 331, ../../../../../sass/plugins/_iconmoon.scss */
.icon-tenis:before {
  content: "\e952";
}

/* line 334, ../../../../../sass/plugins/_iconmoon.scss */
.icon-tv:before {
  content: "\e953";
}

/* line 337, ../../../../../sass/plugins/_iconmoon.scss */
.icon-window:before {
  content: "\e954";
}

/* line 340, ../../../../../sass/plugins/_iconmoon.scss */
.icon-apple:before {
  content: "\e955";
}

/* line 343, ../../../../../sass/plugins/_iconmoon.scss */
.icon-bathrobe:before {
  content: "\e956";
}

/* line 346, ../../../../../sass/plugins/_iconmoon.scss */
.icon-bell:before {
  content: "\e957";
}

/* line 349, ../../../../../sass/plugins/_iconmoon.scss */
.icon-building:before {
  content: "\e958";
}

/* line 352, ../../../../../sass/plugins/_iconmoon.scss */
.icon-car:before {
  content: "\e959";
}

/* line 355, ../../../../../sass/plugins/_iconmoon.scss */
.icon-cigar:before {
  content: "\e95a";
}

/* line 358, ../../../../../sass/plugins/_iconmoon.scss */
.icon-comments:before {
  content: "\e95b";
}

/* line 361, ../../../../../sass/plugins/_iconmoon.scss */
.icon-coolheart:before {
  content: "\e95c";
}

/* line 364, ../../../../../sass/plugins/_iconmoon.scss */
.icon-cupboard:before {
  content: "\e95d";
}

/* line 367, ../../../../../sass/plugins/_iconmoon.scss */
.icon-dimensions:before {
  content: "\e95e";
}

/* line 370, ../../../../../sass/plugins/_iconmoon.scss */
.icon-family:before {
  content: "\e95f";
}

/* line 373, ../../../../../sass/plugins/_iconmoon.scss */
.icon-flattv:before {
  content: "\e960";
}

/* line 376, ../../../../../sass/plugins/_iconmoon.scss */
.icon-formaluser:before {
  content: "\e961";
}

/* line 379, ../../../../../sass/plugins/_iconmoon.scss */
.icon-guarantee:before {
  content: "\e962";
}

/* line 382, ../../../../../sass/plugins/_iconmoon.scss */
.icon-gift:before {
  content: "\e963";
}

/* line 385, ../../../../../sass/plugins/_iconmoon.scss */
.icon-lock:before {
  content: "\e964";
}

/* line 388, ../../../../../sass/plugins/_iconmoon.scss */
.icon-movie2:before {
  content: "\e965";
}

/* line 391, ../../../../../sass/plugins/_iconmoon.scss */
.icon-picasa:before {
  content: "\e966";
}

/* line 394, ../../../../../sass/plugins/_iconmoon.scss */
.icon-roulette:before {
  content: "\e967";
}

/* line 397, ../../../../../sass/plugins/_iconmoon.scss */
.icon-sauna:before {
  content: "\e968";
}

/* line 400, ../../../../../sass/plugins/_iconmoon.scss */
.icon-shower2:before {
  content: "\e969";
}

/* line 403, ../../../../../sass/plugins/_iconmoon.scss */
.icon-singlebed:before {
  content: "\e96a";
}

/* line 406, ../../../../../sass/plugins/_iconmoon.scss */
.icon-ski:before {
  content: "\e96b";
}

/* line 409, ../../../../../sass/plugins/_iconmoon.scss */
.icon-smartphone:before {
  content: "\e96c";
}

/* line 412, ../../../../../sass/plugins/_iconmoon.scss */
.icon-student:before {
  content: "\e96d";
}

/* line 415, ../../../../../sass/plugins/_iconmoon.scss */
.icon-thermometer:before {
  content: "\e96e";
}

/* line 418, ../../../../../sass/plugins/_iconmoon.scss */
.icon-washer:before {
  content: "\e96f";
}

/* line 421, ../../../../../sass/plugins/_iconmoon.scss */
.icon-drinks:before {
  content: "\e970";
}

/* line 424, ../../../../../sass/plugins/_iconmoon.scss */
.icon-drinks2:before {
  content: "\e971";
}

/* line 427, ../../../../../sass/plugins/_iconmoon.scss */
.icon-airconditioner:before {
  content: "\e972";
}

/* line 430, ../../../../../sass/plugins/_iconmoon.scss */
.icon-arrowdown:before {
  content: "\e973";
}

/* line 433, ../../../../../sass/plugins/_iconmoon.scss */
.icon-arrowleft:before {
  content: "\e974";
}

/* line 436, ../../../../../sass/plugins/_iconmoon.scss */
.icon-arrowright:before {
  content: "\e975";
}

/* line 439, ../../../../../sass/plugins/_iconmoon.scss */
.icon-arrowup:before {
  content: "\e976";
}

/* line 442, ../../../../../sass/plugins/_iconmoon.scss */
.icon-bag:before {
  content: "\e977";
}

/* line 445, ../../../../../sass/plugins/_iconmoon.scss */
.icon-bike2:before {
  content: "\e978";
}

/* line 448, ../../../../../sass/plugins/_iconmoon.scss */
.icon-biker:before {
  content: "\e979";
}

/* line 451, ../../../../../sass/plugins/_iconmoon.scss */
.icon-briefcase:before {
  content: "\e97a";
}

/* line 454, ../../../../../sass/plugins/_iconmoon.scss */
.icon-card:before {
  content: "\e97b";
}

/* line 457, ../../../../../sass/plugins/_iconmoon.scss */
.icon-cocktail:before {
  content: "\e97c";
}

/* line 460, ../../../../../sass/plugins/_iconmoon.scss */
.icon-cooker:before {
  content: "\e97d";
}

/* line 463, ../../../../../sass/plugins/_iconmoon.scss */
.icon-drop:before {
  content: "\e97e";
}

/* line 466, ../../../../../sass/plugins/_iconmoon.scss */
.icon-gym:before {
  content: "\e97f";
}

/* line 469, ../../../../../sass/plugins/_iconmoon.scss */
.icon-info2:before {
  content: "\e980";
}

/* line 472, ../../../../../sass/plugins/_iconmoon.scss */
.icon-massage:before {
  content: "\e981";
}

/* line 475, ../../../../../sass/plugins/_iconmoon.scss */
.icon-moon:before {
  content: "\e982";
}

/* line 478, ../../../../../sass/plugins/_iconmoon.scss */
.icon-music:before {
  content: "\e983";
}

/* line 481, ../../../../../sass/plugins/_iconmoon.scss */
.icon-news:before {
  content: "\e984";
}

/* line 484, ../../../../../sass/plugins/_iconmoon.scss */
.icon-nosmoke:before {
  content: "\e985";
}

/* line 487, ../../../../../sass/plugins/_iconmoon.scss */
.icon-parking:before {
  content: "\e986";
}

/* line 490, ../../../../../sass/plugins/_iconmoon.scss */
.icon-phone1:before {
  content: "\e987";
}

/* line 493, ../../../../../sass/plugins/_iconmoon.scss */
.icon-phone2:before {
  content: "\e988";
}

/* line 496, ../../../../../sass/plugins/_iconmoon.scss */
.icon-pictures:before {
  content: "\e989";
}

/* line 499, ../../../../../sass/plugins/_iconmoon.scss */
.icon-plane:before {
  content: "\e98a";
}

/* line 502, ../../../../../sass/plugins/_iconmoon.scss */
.icon-shield:before {
  content: "\e98b";
}

/* line 505, ../../../../../sass/plugins/_iconmoon.scss */
.icon-spa:before {
  content: "\e98c";
}

/* line 508, ../../../../../sass/plugins/_iconmoon.scss */
.icon-sun:before {
  content: "\e98d";
}

/* line 511, ../../../../../sass/plugins/_iconmoon.scss */
.icon-ball2:before {
  content: "\e98e";
}

/* line 514, ../../../../../sass/plugins/_iconmoon.scss */
.icon-bubbles:before {
  content: "\e98f";
}

/* line 517, ../../../../../sass/plugins/_iconmoon.scss */
.icon-cot:before {
  content: "\e990";
}

/* line 520, ../../../../../sass/plugins/_iconmoon.scss */
.icon-cutlery2:before {
  content: "\e991";
}

/* line 523, ../../../../../sass/plugins/_iconmoon.scss */
.icon-golfplayer:before {
  content: "\e992";
}

/* line 526, ../../../../../sass/plugins/_iconmoon.scss */
.icon-heart:before {
  content: "\e993";
}

/* line 529, ../../../../../sass/plugins/_iconmoon.scss */
.icon-towels:before {
  content: "\e994";
}

/* line 532, ../../../../../sass/plugins/_iconmoon.scss */
.icon-tree:before {
  content: "\e995";
}

/* line 535, ../../../../../sass/plugins/_iconmoon.scss */
.icon-wifi:before {
  content: "\e996";
}

/* line 538, ../../../../../sass/plugins/_iconmoon.scss */
.icon-alarmclock:before {
  content: "\e997";
}

/* line 541, ../../../../../sass/plugins/_iconmoon.scss */
.icon-amenities:before {
  content: "\e998";
}

/* line 544, ../../../../../sass/plugins/_iconmoon.scss */
.icon-astronomy:before {
  content: "\e999";
}

/* line 547, ../../../../../sass/plugins/_iconmoon.scss */
.icon-barbecue:before {
  content: "\e99a";
}

/* line 550, ../../../../../sass/plugins/_iconmoon.scss */
.icon-bells:before {
  content: "\e99b";
}

/* line 553, ../../../../../sass/plugins/_iconmoon.scss */
.icon-bottle:before {
  content: "\e99c";
}

/* line 556, ../../../../../sass/plugins/_iconmoon.scss */
.icon-bottle2:before {
  content: "\e99d";
}

/* line 559, ../../../../../sass/plugins/_iconmoon.scss */
.icon-breakfast:before {
  content: "\e99e";
}

/* line 562, ../../../../../sass/plugins/_iconmoon.scss */
.icon-broom:before {
  content: "\e99f";
}

/* line 565, ../../../../../sass/plugins/_iconmoon.scss */
.icon-buffet2:before {
  content: "\e9a0";
}

/* line 568, ../../../../../sass/plugins/_iconmoon.scss */
.icon-calendar2:before {
  content: "\e9a1";
}

/* line 571, ../../../../../sass/plugins/_iconmoon.scss */
.icon-camera:before {
  content: "\e9a2";
}

/* line 574, ../../../../../sass/plugins/_iconmoon.scss */
.icon-camera2:before {
  content: "\e9a3";
}

/* line 577, ../../../../../sass/plugins/_iconmoon.scss */
.icon-caravan:before {
  content: "\e9a4";
}

/* line 580, ../../../../../sass/plugins/_iconmoon.scss */
.icon-champagne:before {
  content: "\e9a5";
}

/* line 583, ../../../../../sass/plugins/_iconmoon.scss */
.icon-chocolate:before {
  content: "\e9a6";
}

/* line 586, ../../../../../sass/plugins/_iconmoon.scss */
.icon-christmasbauble:before {
  content: "\e9a7";
}

/* line 589, ../../../../../sass/plugins/_iconmoon.scss */
.icon-christmasbauble2:before {
  content: "\e9a8";
}

/* line 592, ../../../../../sass/plugins/_iconmoon.scss */
.icon-christmastree:before {
  content: "\e9a9";
}

/* line 595, ../../../../../sass/plugins/_iconmoon.scss */
.icon-christmastree2:before {
  content: "\e9aa";
}

/* line 598, ../../../../../sass/plugins/_iconmoon.scss */
.icon-chronometer:before {
  content: "\e9ab";
}

/* line 601, ../../../../../sass/plugins/_iconmoon.scss */
.icon-clic:before {
  content: "\e9ac";
}

/* line 604, ../../../../../sass/plugins/_iconmoon.scss */
.icon-cocktail2:before {
  content: "\e9ad";
}

/* line 607, ../../../../../sass/plugins/_iconmoon.scss */
.icon-coffee:before {
  content: "\e9ae";
}

/* line 610, ../../../../../sass/plugins/_iconmoon.scss */
.icon-coffee2:before {
  content: "\e9af";
}

/* line 613, ../../../../../sass/plugins/_iconmoon.scss */
.icon-comment2:before {
  content: "\e9b0";
}

/* line 616, ../../../../../sass/plugins/_iconmoon.scss */
.icon-comment3:before {
  content: "\e9b1";
}

/* line 619, ../../../../../sass/plugins/_iconmoon.scss */
.icon-comments2:before {
  content: "\e9b2";
}

/* line 622, ../../../../../sass/plugins/_iconmoon.scss */
.icon-cushions:before {
  content: "\e9b3";
}

/* line 625, ../../../../../sass/plugins/_iconmoon.scss */
.icon-cushions2:before {
  content: "\e9b4";
}

/* line 628, ../../../../../sass/plugins/_iconmoon.scss */
.icon-deaf:before {
  content: "\e9b5";
}

/* line 631, ../../../../../sass/plugins/_iconmoon.scss */
.icon-design:before {
  content: "\e9b6";
}

/* line 634, ../../../../../sass/plugins/_iconmoon.scss */
.icon-desktop:before {
  content: "\e9b7";
}

/* line 637, ../../../../../sass/plugins/_iconmoon.scss */
.icon-dishes:before {
  content: "\e9b8";
}

/* line 640, ../../../../../sass/plugins/_iconmoon.scss */
.icon-dollar:before {
  content: "\e9b9";
}

/* line 643, ../../../../../sass/plugins/_iconmoon.scss */
.icon-download2:before {
  content: "\e9ba";
}

/* line 646, ../../../../../sass/plugins/_iconmoon.scss */
.icon-family2:before {
  content: "\e9bb";
}

/* line 649, ../../../../../sass/plugins/_iconmoon.scss */
.icon-fireworks:before {
  content: "\e9bc";
}

/* line 652, ../../../../../sass/plugins/_iconmoon.scss */
.icon-flipflops:before {
  content: "\e9bd";
}

/* line 655, ../../../../../sass/plugins/_iconmoon.scss */
.icon-friends2:before {
  content: "\e9be";
}

/* line 658, ../../../../../sass/plugins/_iconmoon.scss */
.icon-fruit:before {
  content: "\e9bf";
}

/* line 661, ../../../../../sass/plugins/_iconmoon.scss */
.icon-gender:before {
  content: "\e9c0";
}

/* line 664, ../../../../../sass/plugins/_iconmoon.scss */
.icon-gift2:before {
  content: "\e9c1";
}

/* line 667, ../../../../../sass/plugins/_iconmoon.scss */
.icon-gifts:before {
  content: "\e9c2";
}

/* line 670, ../../../../../sass/plugins/_iconmoon.scss */
.icon-gold:before {
  content: "\e9c3";
}

/* line 673, ../../../../../sass/plugins/_iconmoon.scss */
.icon-hairdrier2:before {
  content: "\e9c4";
}

/* line 676, ../../../../../sass/plugins/_iconmoon.scss */
.icon-hammock:before {
  content: "\e9c5";
}

/* line 679, ../../../../../sass/plugins/_iconmoon.scss */
.icon-hammock2:before {
  content: "\e9c6";
}

/* line 682, ../../../../../sass/plugins/_iconmoon.scss */
.icon-heart2:before {
  content: "\e9c7";
}

/* line 685, ../../../../../sass/plugins/_iconmoon.scss */
.icon-homepage:before {
  content: "\e9c8";
}

/* line 688, ../../../../../sass/plugins/_iconmoon.scss */
.icon-hotel:before {
  content: "\e9c9";
}

/* line 691, ../../../../../sass/plugins/_iconmoon.scss */
.icon-ice:before {
  content: "\e9ca";
}

/* line 694, ../../../../../sass/plugins/_iconmoon.scss */
.icon-kettle:before {
  content: "\e9cb";
}

/* line 697, ../../../../../sass/plugins/_iconmoon.scss */
.icon-kitchen:before {
  content: "\e9cc";
}

/* line 700, ../../../../../sass/plugins/_iconmoon.scss */
.icon-latecheckout:before {
  content: "\e9cd";
}

/* line 703, ../../../../../sass/plugins/_iconmoon.scss */
.icon-luggage:before {
  content: "\e9ce";
}

/* line 706, ../../../../../sass/plugins/_iconmoon.scss */
.icon-meeting:before {
  content: "\e9cf";
}

/* line 709, ../../../../../sass/plugins/_iconmoon.scss */
.icon-microwave:before {
  content: "\e9d0";
}

/* line 712, ../../../../../sass/plugins/_iconmoon.scss */
.icon-monument2:before {
  content: "\e9d1";
}

/* line 715, ../../../../../sass/plugins/_iconmoon.scss */
.icon-mouse1:before {
  content: "\e9d2";
}

/* line 718, ../../../../../sass/plugins/_iconmoon.scss */
.icon-nani:before {
  content: "\e9d3";
}

/* line 721, ../../../../../sass/plugins/_iconmoon.scss */
.icon-offer2:before {
  content: "\e9d4";
}

/* line 724, ../../../../../sass/plugins/_iconmoon.scss */
.icon-offers:before {
  content: "\e9d5";
}

/* line 727, ../../../../../sass/plugins/_iconmoon.scss */
.icon-percent2:before {
  content: "\e9d6";
}

/* line 730, ../../../../../sass/plugins/_iconmoon.scss */
.icon-player:before {
  content: "\e9d7";
}

/* line 733, ../../../../../sass/plugins/_iconmoon.scss */
.icon-romantic:before {
  content: "\e9d8";
}

/* line 736, ../../../../../sass/plugins/_iconmoon.scss */
.icon-roomservice:before {
  content: "\e9d9";
}

/* line 739, ../../../../../sass/plugins/_iconmoon.scss */
.icon-santa:before {
  content: "\e9da";
}

/* line 742, ../../../../../sass/plugins/_iconmoon.scss */
.icon-satellite:before {
  content: "\e9db";
}

/* line 745, ../../../../../sass/plugins/_iconmoon.scss */
.icon-sea2:before {
  content: "\e9dc";
}

/* line 748, ../../../../../sass/plugins/_iconmoon.scss */
.icon-settings:before {
  content: "\e9dd";
}

/* line 751, ../../../../../sass/plugins/_iconmoon.scss */
.icon-shield2:before {
  content: "\e9de";
}

/* line 754, ../../../../../sass/plugins/_iconmoon.scss */
.icon-slippers:before {
  content: "\e9df";
}

/* line 757, ../../../../../sass/plugins/_iconmoon.scss */
.icon-snowflakeeps:before {
  content: "\e9e0";
}

/* line 760, ../../../../../sass/plugins/_iconmoon.scss */
.icon-soap:before {
  content: "\e9e1";
}

/* line 763, ../../../../../sass/plugins/_iconmoon.scss */
.icon-sofa2:before {
  content: "\e9e2";
}

/* line 766, ../../../../../sass/plugins/_iconmoon.scss */
.icon-specialbreakfast:before {
  content: "\e9e3";
}

/* line 769, ../../../../../sass/plugins/_iconmoon.scss */
.icon-specialfamily:before {
  content: "\e9e4";
}

/* line 772, ../../../../../sass/plugins/_iconmoon.scss */
.icon-specialreception:before {
  content: "\e9e5";
}

/* line 775, ../../../../../sass/plugins/_iconmoon.scss */
.icon-specialshower:before {
  content: "\e9e6";
}

/* line 778, ../../../../../sass/plugins/_iconmoon.scss */
.icon-suit:before {
  content: "\e9e7";
}

/* line 781, ../../../../../sass/plugins/_iconmoon.scss */
.icon-sun2:before {
  content: "\e9e8";
}

/* line 784, ../../../../../sass/plugins/_iconmoon.scss */
.icon-sun3:before {
  content: "\e9e9";
}

/* line 787, ../../../../../sass/plugins/_iconmoon.scss */
.icon-tie:before {
  content: "\e9ea";
}

/* line 790, ../../../../../sass/plugins/_iconmoon.scss */
.icon-toaster:before {
  content: "\e9eb";
}

/* line 793, ../../../../../sass/plugins/_iconmoon.scss */
.icon-toilet:before {
  content: "\e9ec";
}

/* line 796, ../../../../../sass/plugins/_iconmoon.scss */
.icon-washer2:before {
  content: "\e9ed";
}

/* line 799, ../../../../../sass/plugins/_iconmoon.scss */
.icon-waterpark:before {
  content: "\e9ee";
}

/* line 802, ../../../../../sass/plugins/_iconmoon.scss */
.icon-wine:before {
  content: "\e9ef";
}

/* line 805, ../../../../../sass/plugins/_iconmoon.scss */
.icon-world:before {
  content: "\e9f0";
}

/* line 808, ../../../../../sass/plugins/_iconmoon.scss */
.icon-www:before {
  content: "\e9f1";
}

/* line 811, ../../../../../sass/plugins/_iconmoon.scss */
.icon-adults:before {
  content: "\e9f2";
}

/* line 814, ../../../../../sass/plugins/_iconmoon.scss */
.icon-percentpig:before {
  content: "\e9f3";
}

/* line 817, ../../../../../sass/plugins/_iconmoon.scss */
.icon-specialwifi:before {
  content: "\e9f4";
}

/* line 820, ../../../../../sass/plugins/_iconmoon.scss */
.icon-specialbuilding:before {
  content: "\e9f5";
}

/* line 823, ../../../../../sass/plugins/_iconmoon.scss */
.icon-speciallunch:before {
  content: "\e9f6";
}

/* line 826, ../../../../../sass/plugins/_iconmoon.scss */
.icon-specialweb:before {
  content: "\e9f7";
}

/* line 829, ../../../../../sass/plugins/_iconmoon.scss */
.icon-specialbed:before {
  content: "\e9f8";
}

/* line 832, ../../../../../sass/plugins/_iconmoon.scss */
.icon-specialevents:before {
  content: "\e9f9";
}

/* line 835, ../../../../../sass/plugins/_iconmoon.scss */
.icon-specialpool:before {
  content: "\e9fa";
}

/* line 838, ../../../../../sass/plugins/_iconmoon.scss */
.icon-specialbeds:before {
  content: "\e9fb";
}

/* line 841, ../../../../../sass/plugins/_iconmoon.scss */
.icon-specialcalendar:before {
  content: "\e9fc";
}

/* line 844, ../../../../../sass/plugins/_iconmoon.scss */
.icon-specialfitness:before {
  content: "\e9fd";
}

/* line 847, ../../../../../sass/plugins/_iconmoon.scss */
.icon-speciallocation:before {
  content: "\e9fe";
}

/* line 850, ../../../../../sass/plugins/_iconmoon.scss */
.icon-settings2:before {
  content: "\e9ff";
}

/* line 853, ../../../../../sass/plugins/_iconmoon.scss */
.icon-nopets:before {
  content: "\ea00";
}

/* line 856, ../../../../../sass/plugins/_iconmoon.scss */
.icon-videocamera:before {
  content: "\ea01";
}

/* line 859, ../../../../../sass/plugins/_iconmoon.scss */
.icon-window1:before {
  content: "\ea02";
}

/* line 862, ../../../../../sass/plugins/_iconmoon.scss */
.icon-offer:before {
  content: "\ea03";
}

/* line 865, ../../../../../sass/plugins/_iconmoon.scss */
.icon-save:before {
  content: "\ea04";
}

/* line 868, ../../../../../sass/plugins/_iconmoon.scss */
.icon-plane2:before {
  content: "\ea05";
}

/* line 871, ../../../../../sass/plugins/_iconmoon.scss */
.icon-longarrow:before {
  content: "\ea06";
}

/* line 874, ../../../../../sass/plugins/_iconmoon.scss */
.icon-paraty:before {
  content: "\ea07";
}

/* line 877, ../../../../../sass/plugins/_iconmoon.scss */
.icon-horseshoe:before {
  content: "\ea08";
}

/* line 880, ../../../../../sass/plugins/_iconmoon.scss */
.icon-balloons:before {
  content: "\ea09";
}

/* line 883, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-tiger:before {
  content: "\ea0a";
}

/* line 886, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-2drinks:before {
  content: "\ea0b";
}

/* line 889, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-bbq:before {
  content: "\ea0c";
}

/* line 892, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-disco:before {
  content: "\ea0d";
}

/* line 895, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-pasta:before {
  content: "\ea0e";
}

/* line 898, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-snack:before {
  content: "\ea0f";
}

/* line 901, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-archery:before {
  content: "\ea10";
}

/* line 904, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-football:before {
  content: "\ea11";
}

/* line 907, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-gameboard:before {
  content: "\ea12";
}

/* line 910, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-golf:before {
  content: "\ea13";
}

/* line 913, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-hotbath:before {
  content: "\ea14";
}

/* line 916, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-hotpool:before {
  content: "\ea15";
}

/* line 919, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-iron:before {
  content: "\ea16";
}

/* line 922, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-jetshower:before {
  content: "\ea17";
}

/* line 925, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-massage:before {
  content: "\ea18";
}

/* line 928, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-petanque:before {
  content: "\ea19";
}

/* line 931, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-phone:before {
  content: "\ea1a";
}

/* line 934, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-shooting:before {
  content: "\ea1b";
}

/* line 937, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-volleyball:before {
  content: "\ea1c";
}

/* line 940, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-balloons:before {
  content: "\ea1d";
}

/* line 943, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-bike:before {
  content: "\ea1e";
}

/* line 946, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-gift:before {
  content: "\ea1f";
}

/* line 949, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-jacuzzi:before {
  content: "\ea20";
}

/* line 952, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-mouse:before {
  content: "\ea21";
}

/* line 955, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-movie:before {
  content: "\ea22";
}

/* line 958, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-playground:before {
  content: "\ea23";
}

/* line 961, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-playingcards:before {
  content: "\ea24";
}

/* line 964, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-shopping:before {
  content: "\ea25";
}

/* line 967, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-shower:before {
  content: "\ea26";
}

/* line 970, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-sofa:before {
  content: "\ea27";
}

/* line 973, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-washing:before {
  content: "\ea28";
}

/* line 976, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-bills:before {
  content: "\ea29";
}

/* line 979, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-car:before {
  content: "\ea2a";
}

/* line 982, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-creditcard:before {
  content: "\ea2b";
}

/* line 985, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-drink:before {
  content: "\ea2c";
}

/* line 988, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-fruit:before {
  content: "\ea2d";
}

/* line 991, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-lock:before {
  content: "\ea2e";
}

/* line 994, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-offer:before {
  content: "\ea2f";
}

/* line 997, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-spa:before {
  content: "\ea30";
}

/* line 1000, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-tennis:before {
  content: "\ea31";
}

/* line 1003, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-aircon:before {
  content: "\ea32";
}

/* line 1006, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-hairdryer:before {
  content: "\ea33";
}

/* line 1009, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-kitchen:before {
  content: "\ea34";
}

/* line 1012, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-tv:before {
  content: "\ea35";
}

/* line 1015, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-wifi:before {
  content: "\ea36";
}

/* line 1018, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-clock:before {
  content: "\ea37";
}

/* line 1021, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-lightning:before {
  content: "\ea38";
}

/* line 1024, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-piggybank:before {
  content: "\ea39";
}

/* line 1027, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-pricetag:before {
  content: "\ea3a";
}

/* line 1030, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-creditcard2:before {
  content: "\ea3b";
}

/* line 1033, ../../../../../sass/plugins/_iconmoon.scss */
.icon-360:before {
  content: "\ea3c";
}

/* line 1036, ../../../../../sass/plugins/_iconmoon.scss */
.icon-contactless:before {
  content: "\ea3d";
}

/* line 1039, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-umbrella:before {
  content: "\ea3e";
}

/* line 1042, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-sun:before {
  content: "\ea3f";
}

/* line 1045, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-slippers:before {
  content: "\ea40";
}

/* line 1048, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-mail:before {
  content: "\ea41";
}

/* line 1051, ../../../../../sass/plugins/_iconmoon.scss */
.icon-line-comment:before {
  content: "\ea42";
}

/* line 3, ../../../../../sass/plugins/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 8, ../../../../../sass/plugins/_mixins.scss */
.center_xy_before:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 13, ../../../../../sass/plugins/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 17, ../../../../../sass/plugins/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 21, ../../../../../sass/plugins/_mixins.scss */
.center_y_before:before {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 25, ../../../../../sass/plugins/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 30, ../../../../../sass/plugins/_mixins.scss */
.fs:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
}

/* line 110, ../../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:before, .icon-xcross:after {
  content: '';
  width: 100%;
  height: 2px;
  background: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 124, ../../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -moz-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 61, ../../../../../sass/plugins/_mixins.scss */
.display_flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* line 1, ../../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown {
  margin: 40px 20px 20px;
  text-align: center;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  bottom: 180px;
  left: 48% !important;
  color: white;
}
/* line 9, ../../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown div {
  display: inline-block;
  font-size: 16px;
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 0;
  padding-bottom: 0;
  text-transform: uppercase;
}
/* line 20, ../../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .days {
  font-weight: 600;
}
/* line 23, ../../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .date {
  display: block;
  font-size: 4.5rem;
}
/* line 28, ../../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .title_format {
  font-weight: 600;
}

@media screen and (max-width: 800px) {
  /* line 35, ../../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown {
    display: inline-block;
    width: 380px;
    top: 330px;
    left: 45% !important;
  }
  /* line 40, ../../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown div {
    font-size: 10px;
  }
  /* line 43, ../../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown .date {
    font-size: 2.5rem;
  }
}
/**
 * Owl Carousel v2.2.1
 * Copyright 2013-2017 David Deutsch
 * Licensed under  ()
 */
/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel, .owl-carousel .owl-item {
  -webkit-tap-highlight-color: transparent;
  position: relative;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel {
  display: none;
  width: 100%;
  z-index: 1;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage {
  position: relative;
  -ms-touch-action: pan-Y;
  -moz-backface-visibility: hidden;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage:after {
  content: ".";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage-outer {
  position: relative;
  overflow: hidden;
  -webkit-transform: translate3d(0, 0, 0);
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item, .owl-carousel .owl-wrapper {
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item {
  min-height: 1px;
  float: left;
  -webkit-backface-visibility: hidden;
  -webkit-touch-callout: none;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item img {
  display: block;
  width: 100%;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-dots.disabled, .owl-carousel .owl-nav.disabled {
  display: none;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.no-js .owl-carousel, .owl-carousel.owl-loaded {
  display: block;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-dot, .owl-carousel .owl-nav .owl-next, .owl-carousel .owl-nav .owl-prev {
  cursor: pointer;
  cursor: hand;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-loading {
  opacity: 0;
  display: block;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-hidden {
  opacity: 0;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-refresh .owl-item {
  visibility: hidden;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-drag .owl-item {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-grab {
  cursor: move;
  cursor: grab;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-rtl {
  direction: rtl;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-rtl .owl-item {
  float: right;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-animated-in {
  z-index: 0;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-animated-out {
  z-index: 1;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .fadeOut {
  animation-name: fadeOut;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-height {
  transition: height .5s ease-in-out;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item .owl-lazy {
  opacity: 0;
  transition: opacity .4s ease;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item img.owl-lazy {
  transform-style: preserve-3d;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-wrapper {
  position: relative;
  height: 100%;
  background: #000;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-play-icon {
  position: absolute;
  height: 80px;
  width: 80px;
  left: 50%;
  top: 50%;
  margin-left: -40px;
  margin-top: -40px;
  background: url(owl.video.play.png) no-repeat;
  cursor: pointer;
  z-index: 1;
  -webkit-backface-visibility: hidden;
  transition: transform .1s ease;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-play-icon:hover {
  -ms-transform: scale(1.3, 1.3);
  transform: scale(1.3, 1.3);
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-playing .owl-video-play-icon, .owl-carousel .owl-video-playing .owl-video-tn {
  display: none;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-tn {
  opacity: 0;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: opacity .4s ease;
}

/* line 6, ../../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-frame {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
}

/* line 3, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-hidden {
  display: none;
}

/* line 7, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-clearfix, .ui-icon {
  display: block;
}

/* line 11, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-hidden-accessible {
  position: absolute !important;
  clip: rect(1px 1px 1px 1px);
  clip: rect(1px, 1px, 1px, 1px);
}

/* line 17, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-reset {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  line-height: 1.3;
  text-decoration: none;
  font-size: 100%;
  list-style: none;
}

/* line 28, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

/* line 36, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
* html .ui-helper-clearfix {
  height: 1%;
}

/* line 40, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-zfix, .ui-widget-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* line 48, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-zfix {
  opacity: 0;
  filter: Alpha(Opacity=0);
}

/* line 53, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-disabled {
  cursor: default !important;
}

/* line 57, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon {
  text-indent: -99999px;
  overflow: hidden;
  background-repeat: no-repeat;
}

/* line 63, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget {
  font-family: Lucida Grande, Lucida Sans, Arial, sans-serif;
  font-size: 1.1em;
}

/* line 68, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget .ui-widget {
  font-size: 1em;
}

/* line 72, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget button, .ui-widget input, .ui-widget select, .ui-widget textarea {
  font-family: Lucida Grande, Lucida Sans, Arial, sans-serif;
  font-size: 1em;
}

/* line 76, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-content {
  border: 1px solid #a6c9e2;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_inset-hard_100_fcfdfd_1x100.png) 50% bottom repeat-x #fcfdfd;
  color: #222;
}

/* line 82, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-content a {
  color: #222;
}

/* line 86, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-header {
  border: 1px solid #4297d7;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_gloss-wave_55_5c9ccc_500x100.png) 50% 50% repeat-x #5c9ccc;
  color: #fff;
  font-weight: 700;
}

/* line 93, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-header a {
  color: #fff;
}

/* line 97, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  border: 1px solid #c5dbec;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_glass_85_dfeffc_1x400.png) 50% 50% repeat-x #dfeffc;
  font-weight: 700;
  color: #2e6e9e;
}

/* line 104, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-default a, .ui-state-default a:link, .ui-state-default a:visited {
  color: #2e6e9e;
  text-decoration: none;
}

/* line 109, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-focus, .ui-state-hover, .ui-widget-content .ui-state-focus, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-focus, .ui-widget-header .ui-state-hover {
  border: 1px solid #79b7e7;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_glass_75_d0e5f5_1x400.png) 50% 50% repeat-x #d0e5f5;
  font-weight: 700;
  color: #1d5987;
}

/* line 116, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-hover a, .ui-state-hover a:hover {
  color: #1d5987;
  text-decoration: none;
}

/* line 121, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active {
  border: 1px solid #79b7e7;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_inset-hard_100_f5f8f9_1x100.png) 50% 50% repeat-x #f5f8f9;
  font-weight: 700;
  color: #e17009;
}

/* line 128, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-active a, .ui-state-active a:link, .ui-state-active a:visited {
  color: #e17009;
  text-decoration: none;
}

/* line 133, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget :active {
  outline: 0;
}

/* line 137, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {
  border: 1px solid #fad42e;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_flat_55_fbec88_40x100.png) 50% 50% repeat-x #fbec88;
  color: #363636;
}

/* line 143, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-highlight a, .ui-widget-content .ui-state-highlight a, .ui-widget-header .ui-state-highlight a {
  color: #363636;
}

/* line 147, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-error, .ui-widget-content .ui-state-error, .ui-widget-header .ui-state-error {
  border: 1px solid #cd0a0a;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_glass_95_fef1ec_1x400.png) 50% 50% repeat-x #fef1ec;
  color: #cd0a0a;
}

/* line 153, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-error a, .ui-state-error-text, .ui-widget-content .ui-state-error a, .ui-widget-content .ui-state-error-text, .ui-widget-header .ui-state-error a, .ui-widget-header .ui-state-error-text {
  color: #cd0a0a;
}

/* line 157, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-priority-primary, .ui-widget-content .ui-priority-primary, .ui-widget-header .ui-priority-primary {
  font-weight: 700;
}

/* line 161, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-priority-secondary, .ui-widget-content .ui-priority-secondary, .ui-widget-header .ui-priority-secondary {
  opacity: .7;
  filter: Alpha(Opacity=70);
  font-weight: 400;
}

/* line 167, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled {
  opacity: .35;
  filter: Alpha(Opacity=35);
  background-image: none;
}
/* line 172, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-disabled.ui-datepicker-other-month, .ui-widget-content .ui-state-disabled.ui-datepicker-other-month, .ui-widget-header .ui-state-disabled.ui-datepicker-other-month {
  opacity: 0;
}

/* line 177, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon, .ui-widget-content .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_469bdd_256x240.png);
}

/* line 181, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon {
  width: 16px;
  height: 16px;
}

/* line 186, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-header .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_d8e7f3_256x240.png);
}

/* line 190, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-default .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_6da8d5_256x240.png);
}

/* line 194, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-focus .ui-icon, .ui-state-hover .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_217bc0_256x240.png);
}

/* line 198, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-active .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_f9bd01_256x240.png);
}

/* line 202, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-highlight .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_2e83ff_256x240.png);
}

/* line 206, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-error .ui-icon, .ui-state-error-text .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_cd0a0a_256x240.png);
}

/* line 210, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-n {
  background-position: 0 0;
}

/* line 214, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-ne {
  background-position: -16px 0;
}

/* line 218, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-e {
  background-position: -32px 0;
}

/* line 222, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-se {
  background-position: -48px 0;
}

/* line 226, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-s {
  background-position: -64px 0;
}

/* line 230, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-sw {
  background-position: -80px 0;
}

/* line 234, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-w {
  background-position: -96px 0;
}

/* line 238, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-nw {
  background-position: -112px 0;
}

/* line 242, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-2-n-s {
  background-position: -128px 0;
}

/* line 246, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-2-e-w {
  background-position: -144px 0;
}

/* line 250, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-n {
  background-position: 0 -16px;
}

/* line 254, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-ne {
  background-position: -16px -16px;
}

/* line 258, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-e {
  background-position: -32px -16px;
}

/* line 262, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-se {
  background-position: -48px -16px;
}

/* line 266, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-s {
  background-position: -64px -16px;
}

/* line 270, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-sw {
  background-position: -80px -16px;
}

/* line 274, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-w {
  background-position: -96px -16px;
}

/* line 278, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-nw {
  background-position: -112px -16px;
}

/* line 282, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-2-n-s {
  background-position: -128px -16px;
}

/* line 286, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-2-e-w {
  background-position: -144px -16px;
}

/* line 290, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-n {
  background-position: 0 -32px;
}

/* line 294, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-ne {
  background-position: -16px -32px;
}

/* line 298, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-e {
  background-position: -32px -32px;
}

/* line 302, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-se {
  background-position: -48px -32px;
}

/* line 306, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-s {
  background-position: -64px -32px;
}

/* line 310, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-sw {
  background-position: -80px -32px;
}

/* line 314, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-w {
  background-position: -96px -32px;
}

/* line 318, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-nw {
  background-position: -112px -32px;
}

/* line 322, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-n-s {
  background-position: -128px -32px;
}

/* line 326, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-ne-sw {
  background-position: -144px -32px;
}

/* line 330, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-e-w {
  background-position: -160px -32px;
}

/* line 334, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-se-nw {
  background-position: -176px -32px;
}

/* line 338, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-n {
  background-position: -192px -32px;
}

/* line 342, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-e {
  background-position: -208px -32px;
}

/* line 346, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-s {
  background-position: -224px -32px;
}

/* line 350, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-w {
  background-position: -240px -32px;
}

/* line 354, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-n {
  background-position: 0 -48px;
}

/* line 358, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-ne {
  background-position: -16px -48px;
}

/* line 362, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-e {
  background-position: -32px -48px;
}

/* line 366, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-se {
  background-position: -48px -48px;
}

/* line 370, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-s {
  background-position: -64px -48px;
}

/* line 374, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-sw {
  background-position: -80px -48px;
}

/* line 378, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-w {
  background-position: -96px -48px;
}

/* line 382, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-nw {
  background-position: -112px -48px;
}

/* line 386, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-n-s {
  background-position: -128px -48px;
}

/* line 390, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-ne-sw {
  background-position: -144px -48px;
}

/* line 394, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-e-w {
  background-position: -160px -48px;
}

/* line 398, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-se-nw {
  background-position: -176px -48px;
}

/* line 402, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-n {
  background-position: -192px -48px;
}

/* line 406, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-e {
  background-position: -208px -48px;
}

/* line 410, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-s {
  background-position: -224px -48px;
}

/* line 414, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-w {
  background-position: -240px -48px;
}

/* line 418, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-w {
  background-position: 0 -64px;
}

/* line 422, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-n {
  background-position: -16px -64px;
}

/* line 426, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-e {
  background-position: -32px -64px;
}

/* line 430, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-s {
  background-position: -48px -64px;
}

/* line 434, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-w {
  background-position: -64px -64px;
}

/* line 438, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-n {
  background-position: -80px -64px;
}

/* line 442, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-e {
  background-position: -96px -64px;
}

/* line 446, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-s {
  background-position: -112px -64px;
}

/* line 450, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-w {
  background-position: -128px -64px;
}

/* line 454, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-n {
  background-position: -144px -64px;
}

/* line 458, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-e {
  background-position: -160px -64px;
}

/* line 462, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-s {
  background-position: -176px -64px;
}

/* line 466, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-4 {
  background-position: 0 -80px;
}

/* line 470, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-4-diag {
  background-position: -16px -80px;
}

/* line 474, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-extlink {
  background-position: -32px -80px;
}

/* line 478, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-newwin {
  background-position: -48px -80px;
}

/* line 482, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-refresh {
  background-position: -64px -80px;
}

/* line 486, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-shuffle {
  background-position: -80px -80px;
}

/* line 490, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-transfer-e-w {
  background-position: -96px -80px;
}

/* line 494, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-transferthick-e-w {
  background-position: -112px -80px;
}

/* line 498, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-folder-collapsed {
  background-position: 0 -96px;
}

/* line 502, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-folder-open {
  background-position: -16px -96px;
}

/* line 506, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-document {
  background-position: -32px -96px;
}

/* line 510, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-document-b {
  background-position: -48px -96px;
}

/* line 514, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-note {
  background-position: -64px -96px;
}

/* line 518, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-mail-closed {
  background-position: -80px -96px;
}

/* line 522, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-mail-open {
  background-position: -96px -96px;
}

/* line 526, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-suitcase {
  background-position: -112px -96px;
}

/* line 530, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-comment {
  background-position: -128px -96px;
}

/* line 534, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-person {
  background-position: -144px -96px;
}

/* line 538, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-print {
  background-position: -160px -96px;
}

/* line 542, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-trash {
  background-position: -176px -96px;
}

/* line 546, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-locked {
  background-position: -192px -96px;
}

/* line 550, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-unlocked {
  background-position: -208px -96px;
}

/* line 554, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-bookmark {
  background-position: -224px -96px;
}

/* line 558, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-tag {
  background-position: -240px -96px;
}

/* line 562, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-home {
  background-position: 0 -112px;
}

/* line 566, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-flag {
  background-position: -16px -112px;
}

/* line 570, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-calendar {
  background-position: -32px -112px;
}

/* line 574, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-cart {
  background-position: -48px -112px;
}

/* line 578, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pencil {
  background-position: -64px -112px;
}

/* line 582, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-clock {
  background-position: -80px -112px;
}

/* line 586, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-disk {
  background-position: -96px -112px;
}

/* line 590, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-calculator {
  background-position: -112px -112px;
}

/* line 594, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-zoomin {
  background-position: -128px -112px;
}

/* line 598, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-zoomout {
  background-position: -144px -112px;
}

/* line 602, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-search {
  background-position: -160px -112px;
}

/* line 606, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-wrench {
  background-position: -176px -112px;
}

/* line 610, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-gear {
  background-position: -192px -112px;
}

/* line 614, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-heart {
  background-position: -208px -112px;
}

/* line 618, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-star {
  background-position: -224px -112px;
}

/* line 622, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-link {
  background-position: -240px -112px;
}

/* line 626, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-cancel {
  background-position: 0 -128px;
}

/* line 630, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-plus {
  background-position: -16px -128px;
}

/* line 634, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-plusthick {
  background-position: -32px -128px;
}

/* line 638, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-minus {
  background-position: -48px -128px;
}

/* line 642, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-minusthick {
  background-position: -64px -128px;
}

/* line 646, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-close {
  background-position: -80px -128px;
}

/* line 650, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-closethick {
  background-position: -96px -128px;
}

/* line 654, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-key {
  background-position: -112px -128px;
}

/* line 658, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-lightbulb {
  background-position: -128px -128px;
}

/* line 662, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-scissors {
  background-position: -144px -128px;
}

/* line 666, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-clipboard {
  background-position: -160px -128px;
}

/* line 670, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-copy {
  background-position: -176px -128px;
}

/* line 674, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-contact {
  background-position: -192px -128px;
}

/* line 678, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-image {
  background-position: -208px -128px;
}

/* line 682, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-video {
  background-position: -224px -128px;
}

/* line 686, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-script {
  background-position: -240px -128px;
}

/* line 690, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-alert {
  background-position: 0 -144px;
}

/* line 694, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-info {
  background-position: -16px -144px;
}

/* line 698, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-notice {
  background-position: -32px -144px;
}

/* line 702, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-help {
  background-position: -48px -144px;
}

/* line 706, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-check {
  background-position: -64px -144px;
}

/* line 710, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-bullet {
  background-position: -80px -144px;
}

/* line 714, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-radio-off {
  background-position: -96px -144px;
}

/* line 718, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-radio-on {
  background-position: -112px -144px;
}

/* line 722, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pin-w {
  background-position: -128px -144px;
}

/* line 726, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pin-s {
  background-position: -144px -144px;
}

/* line 730, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-play {
  background-position: 0 -160px;
}

/* line 734, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pause {
  background-position: -16px -160px;
}

/* line 738, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-next {
  background-position: -32px -160px;
}

/* line 742, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-prev {
  background-position: -48px -160px;
}

/* line 746, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-end {
  background-position: -64px -160px;
}

/* line 750, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-first, .ui-icon-seek-start {
  background-position: -80px -160px;
}

/* line 754, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-stop {
  background-position: -96px -160px;
}

/* line 758, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-eject {
  background-position: -112px -160px;
}

/* line 762, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-volume-off {
  background-position: -128px -160px;
}

/* line 766, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-volume-on {
  background-position: -144px -160px;
}

/* line 770, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-power {
  background-position: 0 -176px;
}

/* line 774, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-signal-diag {
  background-position: -16px -176px;
}

/* line 778, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-signal {
  background-position: -32px -176px;
}

/* line 782, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-0 {
  background-position: -48px -176px;
}

/* line 786, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-1 {
  background-position: -64px -176px;
}

/* line 790, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-2 {
  background-position: -80px -176px;
}

/* line 794, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-3 {
  background-position: -96px -176px;
}

/* line 798, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-plus {
  background-position: 0 -192px;
}

/* line 802, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-minus {
  background-position: -16px -192px;
}

/* line 806, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-close {
  background-position: -32px -192px;
}

/* line 810, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-e {
  background-position: -48px -192px;
}

/* line 814, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-s {
  background-position: -64px -192px;
}

/* line 818, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-w {
  background-position: -80px -192px;
}

/* line 822, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-n {
  background-position: -96px -192px;
}

/* line 826, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-e {
  background-position: -112px -192px;
}

/* line 830, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-s {
  background-position: -128px -192px;
}

/* line 834, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-w {
  background-position: -144px -192px;
}

/* line 838, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-n {
  background-position: -160px -192px;
}

/* line 842, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-zoomin {
  background-position: -176px -192px;
}

/* line 846, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-zoomout {
  background-position: -192px -192px;
}

/* line 850, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-check {
  background-position: -208px -192px;
}

/* line 854, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circlesmall-plus {
  background-position: 0 -208px;
}

/* line 858, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circlesmall-minus {
  background-position: -16px -208px;
}

/* line 862, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circlesmall-close {
  background-position: -32px -208px;
}

/* line 866, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-squaresmall-plus {
  background-position: -48px -208px;
}

/* line 870, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-squaresmall-minus {
  background-position: -64px -208px;
}

/* line 874, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-squaresmall-close {
  background-position: -80px -208px;
}

/* line 878, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-dotted-vertical {
  background-position: 0 -224px;
}

/* line 882, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-dotted-horizontal {
  background-position: -16px -224px;
}

/* line 886, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-solid-vertical {
  background-position: -32px -224px;
}

/* line 890, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-solid-horizontal {
  background-position: -48px -224px;
}

/* line 894, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-gripsmall-diagonal-se {
  background-position: -64px -224px;
}

/* line 898, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-diagonal-se {
  background-position: -80px -224px;
}

/* line 902, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-overlay, .ui-widget-shadow {
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_flat_0_aaaaaa_40x100.png) 50% 50% repeat-x #aaa;
  opacity: .3;
  filter: Alpha(Opacity=30);
}

/* line 908, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-left, .ui-corner-tl, .ui-corner-top {
  -moz-border-radius-topleft: 5px;
  -webkit-border-top-left-radius: 5px;
  -khtml-border-top-left-radius: 5px;
  border-top-left-radius: 5px;
}

/* line 915, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-right, .ui-corner-top, .ui-corner-tr {
  -moz-border-radius-topright: 5px;
  -webkit-border-top-right-radius: 5px;
  -khtml-border-top-right-radius: 5px;
  border-top-right-radius: 5px;
}

/* line 922, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-bl, .ui-corner-bottom, .ui-corner-left {
  -moz-border-radius-bottomleft: 5px;
  -webkit-border-bottom-left-radius: 5px;
  -khtml-border-bottom-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

/* line 929, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-bottom, .ui-corner-br, .ui-corner-right {
  -moz-border-radius-bottomright: 5px;
  -webkit-border-bottom-right-radius: 5px;
  -khtml-border-bottom-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

/* line 936, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-shadow {
  margin: -8px 0 0 -8px;
  padding: 8px;
  -moz-border-radius: 8px;
  -khtml-border-radius: 8px;
  -webkit-border-radius: 8px;
  border-radius: 8px;
}

/* line 945, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker {
  width: 17em;
  padding: .2em .2em 0;
  display: none;
}

/* line 951, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-header {
  position: relative;
  padding: 0.2em 0;
}

/* line 956, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next, .ui-datepicker .ui-datepicker-prev {
  position: absolute;
  top: 2px;
  width: 1.8em;
  height: 1.8em;
}

/* line 963, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next-hover, .ui-datepicker .ui-datepicker-prev-hover {
  top: 1px;
}

/* line 967, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-prev {
  left: 2px;
}

/* line 971, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next {
  right: 2px;
}

/* line 975, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-prev-hover {
  left: 1px;
}

/* line 979, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next-hover {
  right: 1px;
}

/* line 983, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next span, .ui-datepicker .ui-datepicker-prev span {
  display: block;
  position: absolute;
  left: 50%;
  margin-left: -8px;
  top: 50%;
  margin-top: -8px;
}

/* line 992, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-title {
  margin: 0 2.3em;
  line-height: 1.8em;
  text-align: center;
}

/* line 998, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-title select {
  font-size: 1em;
  margin: 1px 0;
}

/* line 1003, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker select.ui-datepicker-month-year {
  width: 100%;
}

/* line 1007, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker select.ui-datepicker-month, .ui-datepicker select.ui-datepicker-year {
  width: 49%;
}

/* line 1011, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker table {
  width: 100%;
  font-size: .9em;
  border-collapse: collapse;
  margin: 0 0 0.4em;
}

/* line 1018, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker th {
  padding: .7em .3em;
  text-align: center;
  font-weight: 700;
  border: 0;
}

/* line 1025, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker td {
  border: 0;
  padding: 1px;
}

/* line 1030, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker td a, .ui-datepicker td span {
  display: block;
  padding: .2em;
  text-align: right;
  text-decoration: none;
}

/* line 1037, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-buttonpane {
  background-image: none;
  margin: .7em 0 0;
  padding: 0 .2em;
  border-left: 0;
  border-right: 0;
  border-bottom: 0;
}

/* line 1046, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-buttonpane button {
  float: right;
  margin: .5em .2em .4em;
  cursor: pointer;
  padding: .2em .6em .3em;
  width: auto;
  overflow: visible;
}

/* line 1055, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current, .ui-datepicker-multi .ui-datepicker-group, .ui-datepicker-rtl .ui-datepicker-buttonpane button {
  float: left;
}

/* line 1059, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker.ui-datepicker-multi {
  width: auto;
}

/* line 1063, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi .ui-datepicker-group table {
  width: 95%;
  margin: 0 auto 0.4em;
}

/* line 1068, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi-2 .ui-datepicker-group {
  width: 50%;
}

/* line 1072, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi-3 .ui-datepicker-group {
  width: 33.3%;
}

/* line 1076, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi-4 .ui-datepicker-group {
  width: 25%;
}

/* line 1080, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header, .ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
  border-left-width: 0;
}

/* line 1084, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi .ui-datepicker-buttonpane {
  clear: left;
}

/* line 1088, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-row-break {
  clear: both;
  width: 100%;
  font-size: 0;
}

/* line 1094, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl {
  direction: rtl;
}

/* line 1098, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-prev {
  right: 2px;
  left: auto;
}

/* line 1103, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-next {
  left: 2px;
  right: auto;
}

/* line 1108, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-prev:hover {
  right: 1px;
  left: auto;
}

/* line 1113, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-next:hover {
  left: 1px;
  right: auto;
}

/* line 1118, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-buttonpane {
  clear: right;
}

/* line 1122, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current, .ui-datepicker-rtl .ui-datepicker-group {
  float: right;
}

/* line 1126, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header, .ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {
  border-right-width: 0;
  border-left-width: 1px;
}

/* line 1131, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-cover {
  display: none;
  display: block;
  position: absolute;
  z-index: -1;
  filter: mask();
  top: -4px;
  left: -4px;
  width: 200px;
  height: 200px;
}

@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (-webkit-min-device-pixel-ratio: 2) {
  /* line 1144, ../../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
  div.ui-datepicker {
    font-size: 20px;
  }
}
/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container {
  box-sizing: border-box;
  display: inline-block;
  margin: 0;
  position: relative;
  vertical-align: middle;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-selection--single {
  box-sizing: border-box;
  cursor: pointer;
  display: block;
  height: 28px;
  user-select: none;
  -webkit-user-select: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-selection--single .select2-selection__rendered {
  display: block;
  padding-left: 8px;
  padding-right: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-selection--single .select2-selection__clear {
  position: relative;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container[dir="rtl"] .select2-selection--single .select2-selection__rendered {
  padding-right: 8px;
  padding-left: 20px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-selection--multiple {
  box-sizing: border-box;
  cursor: pointer;
  display: block;
  min-height: 32px;
  user-select: none;
  -webkit-user-select: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-selection--multiple .select2-selection__rendered {
  display: inline-block;
  overflow: hidden;
  padding-left: 8px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-search--inline {
  float: left;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-search--inline .select2-search__field {
  box-sizing: border-box;
  border: none;
  font-size: 100%;
  margin-top: 5px;
  padding: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container .select2-search--inline .select2-search__field::-webkit-search-cancel-button {
  -webkit-appearance: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-dropdown {
  background-color: white;
  border: 1px solid #aaa;
  border-radius: 4px;
  box-sizing: border-box;
  display: block;
  position: absolute;
  left: -100000px;
  width: 100%;
  z-index: 1051;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-results {
  display: block;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-results__options {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-results__option {
  padding: 6px;
  user-select: none;
  -webkit-user-select: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-results__option[aria-selected] {
  cursor: pointer;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--open .select2-dropdown {
  left: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--open .select2-dropdown--above {
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--open .select2-dropdown--below {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-search--dropdown {
  display: block;
  padding: 4px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-search--dropdown .select2-search__field {
  padding: 4px;
  width: 100%;
  box-sizing: border-box;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
  -webkit-appearance: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-search--dropdown.select2-search--hide {
  display: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-close-mask {
  border: 0;
  margin: 0;
  padding: 0;
  display: block;
  position: fixed;
  left: 0;
  top: 0;
  min-height: 100%;
  min-width: 100%;
  height: auto;
  width: auto;
  opacity: 0;
  z-index: 99;
  background-color: #fff;
  filter: alpha(opacity=0);
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-hidden-accessible {
  border: 0 !important;
  clip: rect(0 0 0 0) !important;
  -webkit-clip-path: inset(50%) !important;
  clip-path: inset(50%) !important;
  height: 1px !important;
  overflow: hidden !important;
  padding: 0 !important;
  position: absolute !important;
  width: 1px !important;
  white-space: nowrap !important;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--single {
  background-color: #fff;
  border: 1px solid #aaa;
  border-radius: 4px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #444;
  line-height: 28px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--single .select2-selection__clear {
  cursor: pointer;
  float: right;
  font-weight: bold;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #999;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 26px;
  position: absolute;
  top: 1px;
  right: 1px;
  width: 20px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--single .select2-selection__arrow b {
  border-color: #888 transparent transparent transparent;
  border-style: solid;
  border-width: 5px 4px 0 4px;
  height: 0;
  left: 50%;
  margin-left: -4px;
  margin-top: -2px;
  position: absolute;
  top: 50%;
  width: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__clear {
  float: left;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__arrow {
  left: 1px;
  right: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--disabled .select2-selection--single {
  background-color: #eee;
  cursor: default;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__clear {
  display: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #888 transparent;
  border-width: 0 4px 5px 4px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple {
  background-color: white;
  border: 1px solid #aaa;
  border-radius: 4px;
  cursor: text;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple .select2-selection__rendered {
  box-sizing: border-box;
  list-style: none;
  margin: 0;
  padding: 0 5px;
  width: 100%;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple .select2-selection__rendered li {
  list-style: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
  color: #999;
  margin-top: 5px;
  float: left;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple .select2-selection__clear {
  cursor: pointer;
  float: right;
  font-weight: bold;
  margin-top: 5px;
  margin-right: 10px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #e4e4e4;
  border: 1px solid #aaa;
  border-radius: 4px;
  cursor: default;
  float: left;
  margin-right: 5px;
  margin-top: 5px;
  padding: 0 5px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: #999;
  cursor: pointer;
  display: inline-block;
  font-weight: bold;
  margin-right: 2px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #333;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice, .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__placeholder, .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-search--inline {
  float: right;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
  margin-left: 5px;
  margin-right: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
  margin-left: 2px;
  margin-right: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--focus .select2-selection--multiple {
  border: solid black 1px;
  outline: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--disabled .select2-selection--multiple {
  background-color: #eee;
  cursor: default;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--disabled .select2-selection__choice__remove {
  display: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--open.select2-container--above .select2-selection--single, .select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default.select2-container--open.select2-container--below .select2-selection--single, .select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-search--dropdown .select2-search__field {
  border: 1px solid #aaa;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-search--inline .select2-search__field {
  background: transparent;
  border: none;
  outline: 0;
  box-shadow: none;
  -webkit-appearance: textfield;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results > .select2-results__options {
  max-height: 200px;
  overflow-y: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option[role=group] {
  padding: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option[aria-disabled=true] {
  color: #999;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option[aria-selected=true] {
  background-color: #ddd;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option .select2-results__option {
  padding-left: 1em;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option .select2-results__option .select2-results__group {
  padding-left: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -1em;
  padding-left: 2em;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -2em;
  padding-left: 3em;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -3em;
  padding-left: 4em;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -4em;
  padding-left: 5em;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  margin-left: -5em;
  padding-left: 6em;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #5897fb;
  color: white;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--default .select2-results__group {
  cursor: default;
  display: block;
  padding: 6px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--single {
  background-color: #f7f7f7;
  border: 1px solid #aaa;
  border-radius: 4px;
  outline: 0;
  background-image: -webkit-linear-gradient(top, #fff 50%, #eee 100%);
  background-image: -o-linear-gradient(top, #fff 50%, #eee 100%);
  background-image: linear-gradient(to bottom, #fff 50%, #eee 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFFFF', endColorstr='#FFEEEEEE', GradientType=0);
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--single:focus {
  border: 1px solid #5897fb;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--single .select2-selection__rendered {
  color: #444;
  line-height: 28px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--single .select2-selection__clear {
  cursor: pointer;
  float: right;
  font-weight: bold;
  margin-right: 10px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--single .select2-selection__placeholder {
  color: #999;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--single .select2-selection__arrow {
  background-color: #ddd;
  border: none;
  border-left: 1px solid #aaa;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  height: 26px;
  position: absolute;
  top: 1px;
  right: 1px;
  width: 20px;
  background-image: -webkit-linear-gradient(top, #eee 50%, #ccc 100%);
  background-image: -o-linear-gradient(top, #eee 50%, #ccc 100%);
  background-image: linear-gradient(to bottom, #eee 50%, #ccc 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFEEEEEE', endColorstr='#FFCCCCCC', GradientType=0);
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--single .select2-selection__arrow b {
  border-color: #888 transparent transparent transparent;
  border-style: solid;
  border-width: 5px 4px 0 4px;
  height: 0;
  left: 50%;
  margin-left: -4px;
  margin-top: -2px;
  position: absolute;
  top: 50%;
  width: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__clear {
  float: left;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__arrow {
  border: none;
  border-right: 1px solid #aaa;
  border-radius: 0;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  left: 1px;
  right: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open .select2-selection--single {
  border: 1px solid #5897fb;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow {
  background: transparent;
  border: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #888 transparent;
  border-width: 0 4px 5px 4px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open.select2-container--above .select2-selection--single {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  background-image: -webkit-linear-gradient(top, #fff 0%, #eee 50%);
  background-image: -o-linear-gradient(top, #fff 0%, #eee 50%);
  background-image: linear-gradient(to bottom, #fff 0%, #eee 50%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFFFF', endColorstr='#FFEEEEEE', GradientType=0);
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open.select2-container--below .select2-selection--single {
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background-image: -webkit-linear-gradient(top, #eee 50%, #fff 100%);
  background-image: -o-linear-gradient(top, #eee 50%, #fff 100%);
  background-image: linear-gradient(to bottom, #eee 50%, #fff 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFEEEEEE', endColorstr='#FFFFFFFF', GradientType=0);
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--multiple {
  background-color: white;
  border: 1px solid #aaa;
  border-radius: 4px;
  cursor: text;
  outline: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--multiple:focus {
  border: 1px solid #5897fb;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--multiple .select2-selection__rendered {
  list-style: none;
  margin: 0;
  padding: 0 5px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--multiple .select2-selection__clear {
  display: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--multiple .select2-selection__choice {
  background-color: #e4e4e4;
  border: 1px solid #aaa;
  border-radius: 4px;
  cursor: default;
  float: left;
  margin-right: 5px;
  margin-top: 5px;
  padding: 0 5px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove {
  color: #888;
  cursor: pointer;
  display: inline-block;
  font-weight: bold;
  margin-right: 2px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #555;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
  float: right;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
  margin-left: 5px;
  margin-right: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
  margin-left: 2px;
  margin-right: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open .select2-selection--multiple {
  border: 1px solid #5897fb;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open.select2-container--above .select2-selection--multiple {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open.select2-container--below .select2-selection--multiple {
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-search--dropdown .select2-search__field {
  border: 1px solid #aaa;
  outline: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-search--inline .select2-search__field {
  outline: 0;
  box-shadow: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-dropdown {
  background-color: #fff;
  border: 1px solid transparent;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-dropdown--above {
  border-bottom: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-dropdown--below {
  border-top: none;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-results > .select2-results__options {
  max-height: 200px;
  overflow-y: auto;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-results__option[role=group] {
  padding: 0;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-results__option[aria-disabled=true] {
  color: grey;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-results__option--highlighted[aria-selected] {
  background-color: #3875d7;
  color: #fff;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic .select2-results__group {
  cursor: default;
  display: block;
  padding: 6px;
}

/* line 1, ../../../../../sass/plugins/_select2.scss */
.select2-container--classic.select2-container--open .select2-dropdown {
  border-color: #5897fb;
}

/* line 1, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.color_buttons {
  color: #2BB0A3;
}

/* line 5, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.color_text {
  color: #012379;
}

/* line 9, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.general_title_block {
  padding: 0 20px;
  margin-bottom: 30px;
}
/* line 13, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.general_title_block.centered {
  text-align: center;
}
/* line 17, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.general_title_block.main_section_titles {
  margin-top: 18px;
}
/* line 21, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.general_title_block .mini_title {
  margin: 0;
  text-transform: uppercase;
  color: #424242;
  font-size: 12px;
  letter-spacing: 0.3px;
}
/* line 29, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.general_title_block .main_title {
  margin: 0;
  font-style: italic;
  margin-top: 0;
  font-size: 24px;
  line-height: 25px;
}
/* line 37, ../../../../../sass/styles_mobile/3/_general_classes.scss */
.general_title_block .main_description {
  font-size: 12px;
  color: #424242;
  margin-top: 20px;
  line-height: 20px;
  letter-spacing: 0.2px;
}

/* line 1, ../../../../../sass/styles_mobile/3/_general_styles.scss */
body {
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  margin: 0;
  font-family: 'Roboto', sans-serif;
}
/* line 6, ../../../../../sass/styles_mobile/3/_general_styles.scss */
body.right_move {
  margin-left: 90%;
  margin-right: -90%;
}
/* line 11, ../../../../../sass/styles_mobile/3/_general_styles.scss */
body * {
  box-sizing: border-box;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}
/* line 17, ../../../../../sass/styles_mobile/3/_general_styles.scss */
body b, body strong {
  font-weight: 600;
}
/* line 21, ../../../../../sass/styles_mobile/3/_general_styles.scss */
body a {
  text-decoration: none;
}

/* line 27, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider {
  height: 57vw;
  margin-top: 6vh;
}
/* line 31, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .owl-stage-outer, .main-owlslider .owl-stage, .main-owlslider .owl-item {
  height: 100%;
}
/* line 35, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .owl-item {
  position: relative;
  overflow: hidden;
}
/* line 39, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .owl-item img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  width: auto;
  max-width: none;
}
/* line 48, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .description_text {
  position: absolute;
  top: 50%;
  left: 40px;
  right: 40px;
  text-align: left;
  transform: translateY(-50%);
  font-size: 26px;
  line-height: 24px;
  font-weight: 600;
}
/* line 60, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .owl-dots {
  position: absolute;
  bottom: 10px;
  left: 20px;
  right: 20px;
  display: flex;
}
/* line 67, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .owl-dots:before {
  content: '';
  width: 100%;
  height: 1px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: #2BB0A3;
}
/* line 78, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .owl-dots .owl-dot {
  flex-flow: row nowrap;
  height: 6px;
  width: 100%;
  margin-top: -3px;
  -webkit-transition: background-color 0.4s;
  -moz-transition: background-color 0.4s;
  -ms-transition: background-color 0.4s;
  -o-transition: background-color 0.4s;
  transition: background-color 0.4s;
}
/* line 85, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.main-owlslider .owl-dots .owl-dot.active {
  background-color: #2BB0A3;
}

/* line 93, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_block_bottom {
  margin-top: 40px;
}
/* line 96, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_block_bottom .banners_bottom_title {
  padding: 0 20px;
  margin-bottom: 30px;
}
/* line 100, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_block_bottom .banners_bottom_title .main_title {
  color: #2BB0A3;
}
/* line 105, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_block_bottom .bottom_banner_element {
  display: block;
  width: 100%;
  position: relative;
  padding-top: 35%;
  overflow: hidden;
  border-bottom: 1px solid white;
}
/* line 113, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_block_bottom .bottom_banner_element .overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}
/* line 123, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_block_bottom .bottom_banner_element .bottom_block_image {
  position: absolute;
  top: -50%;
  bottom: -50%;
  margin: auto;
  left: -50%;
  right: -50%;
}
/* line 132, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_block_bottom .bottom_banner_element .bottom_banner_title {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: 100%;
  margin: 0;
  z-index: 2;
  text-align: center;
  color: white;
  font-style: italic;
  font-size: 18px;
  text-shadow: -1px 4px 5px rgba(74, 74, 74, 0.55);
}

/* line 148, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper {
  margin: 40px auto;
}
/* line 152, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .banners_bottom_title .main_title {
  color: #2BB0A3;
}
/* line 158, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl_banners_carousel {
  position: relative;
}
/* line 160, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl_banners_carousel:before {
  content: '';
  width: 100%;
  background-color: #F4F4F4;
  position: absolute;
  top: 0;
  bottom: 40%;
}
/* line 170, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item {
  padding-left: 15px;
  padding-right: 50px;
}
/* line 174, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element {
  display: inline-block;
  width: 100%;
  position: relative;
  margin-top: 10px;
  border: 1px solid #d0d0d0;
}
/* line 181, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .image_wrapper {
  width: 40%;
  height: 100%;
  display: inline-block;
  vertical-align: top;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
}
/* line 192, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .image_wrapper .since_wrapper {
  position: absolute;
  left: 10px;
  bottom: 10px;
  z-index: 2;
  background: white;
  padding: 8px 7px 9px;
  text-align: right;
  text-transform: uppercase;
  font-size: 19px;
  font-weight: bolder;
  line-height: 16px;
}
/* line 205, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .image_wrapper .since_wrapper .since_label {
  display: block;
  font-size: 11px;
  font-weight: 500;
  color: #4e4e4e;
}
/* line 213, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .image_wrapper .image_element {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  width: auto;
}
/* line 219, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper {
  width: 60%;
  display: inline-block;
  vertical-align: top;
  background-color: #fff;
  padding: 10px;
  float: right;
  margin-bottom: auto;
  border-left: 0;
}
/* line 230, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .mini_title {
  font-size: 10px;
}
/* line 234, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .main_title {
  margin-top: 0;
  font-size: 18px;
  color: #2BB0A3;
  height: 48px;
}
/* line 241, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .hotel_category {
  margin-top: -10px;
}
/* line 244, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .hotel_category .category {
  font-size: 10px;
}
/* line 249, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .tags_wrapper {
  width: 100%;
  display: flex;
  flex-flow: wrap;
  margin-top: 0;
}
/* line 255, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .tags_wrapper .tag_element {
  display: inline-table;
  font-style: italic;
  font-size: 10px;
  background-color: #CCCCCC;
  color: white;
  width: auto;
  text-align: center;
  flex-flow: row nowrap;
  margin-right: 10px;
  padding: 0 12px;
  white-space: nowrap;
}
/* line 268, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .tags_wrapper .tag_element:first-of-type {
  margin-bottom: 5px;
}
/* line 272, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .tags_wrapper .tag_element:last-of-type {
  margin-right: 0;
}
/* line 278, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .see_hotel_link {
  display: inline-block;
  width: 100%;
  background-color: #F7BB1E;
  color: white;
  text-transform: uppercase;
  padding: 5px 8px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  margin-top: 8px;
}
/* line 294, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-dots {
  position: relative;
  width: 100%;
  padding: 0 20px;
  display: flex;
  margin-top: 40px;
}
/* line 301, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-dots:before {
  content: '';
  width: calc(100% - 40px);
  height: 1px;
  position: absolute;
  top: 0;
  left: 20px;
  right: 20px;
  background-color: #2BB0A3;
}
/* line 312, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-dots .owl-dot {
  position: relative;
  overflow: hidden;
  flex-flow: row nowrap;
  height: 6px;
  width: 100%;
  margin-top: -3px;
}
/* line 319, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-dots .owl-dot:before {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: -101%;
  background-color: #2BB0A3;
  -webkit-transition: left 0.4s;
  -moz-transition: left 0.4s;
  -ms-transition: left 0.4s;
  -o-transition: left 0.4s;
  transition: left 0.4s;
}
/* line 332, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-dots .owl-dot.active:before {
  left: 0;
}

/* line 344, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper {
  margin-top: 40px;
}
/* line 348, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .advantages_banner_title .main_title {
  color: #2BB0A3;
}
/* line 353, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper {
  background-color: #f4f4f4;
  margin-top: 15px;
  display: inline-block;
  padding: 20px;
}
/* line 359, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper:lang(ru) {
  padding: 10px;
}
/* line 363, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element {
  display: inline-block;
  width: calc(100% / 3);
  float: left;
  text-align: center;
  border-right: 1px solid #2BB0A3;
  padding: 0 10px;
}
/* line 371, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element i {
  font-weight: 300;
  font-size: larger;
}
/* line 376, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element.first {
  width: 100%;
  border-bottom: 1px solid #2BB0A3;
  padding: 0 0 10px 0;
  margin-bottom: 10px;
  border-right: 0;
}
/* line 383, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element.first .tick_icon, .advantages_banner_wrapper .ticks_wrapper .tick_element.first .tick_text {
  display: inline-block;
  vertical-align: middle;
  margin-top: 0;
}
/* line 389, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element.first .tick_icon {
  margin-right: 15px;
}
/* line 394, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element:last-of-type {
  border-right: 0;
}
/* line 398, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element .tick_icon {
  color: #23588a;
}
/* line 402, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .ticks_wrapper .tick_element .tick_text {
  text-transform: uppercase;
  font-size: 9px;
  letter-spacing: 0.7px;
  margin-top: 8px;
}
/* line 411, ../../../../../sass/styles_mobile/3/_general_styles.scss */
.advantages_banner_wrapper .external_advantage_link {
  display: block;
  text-align: center;
  text-transform: uppercase;
  font-size: 12px;
  margin-top: 5px;
  font-weight: 600;
}

/* line 422, ../../../../../sass/styles_mobile/3/_general_styles.scss */
#popup_v3_overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 100%;
  right: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 10;
  -webkit-transition: left 0.5s;
  -moz-transition: left 0.5s;
  -ms-transition: left 0.5s;
  -o-transition: left 0.5s;
  transition: left 0.5s;
}
/* line 436, ../../../../../sass/styles_mobile/3/_general_styles.scss */
#popup_v3_overlay.opened {
  left: 0;
}

/* line 441, ../../../../../sass/styles_mobile/3/_general_styles.scss */
#popup_website_v3 {
  position: fixed;
  top: 50%;
  left: 150%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  background: white;
  z-index: 11;
  width: 80%;
  padding: 30px;
  -webkit-transition: left 0.5s;
  -moz-transition: left 0.5s;
  -ms-transition: left 0.5s;
  -o-transition: left 0.5s;
  transition: left 0.5s;
}
/* line 460, ../../../../../sass/styles_mobile/3/_general_styles.scss */
#popup_website_v3.opened {
  left: 50%;
}
/* line 464, ../../../../../sass/styles_mobile/3/_general_styles.scss */
#popup_website_v3 #close_button_popup_v3 {
  position: absolute;
  top: 10px;
  right: 10px;
}

/* line 1, ../../../../../sass/styles_mobile/3/_header.scss */
header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 10px 0;
  -webkit-box-shadow: 0 3px 9px 0 rgba(0, 0, 0, 0.4);
  -moz-box-shadow: 0 3px 9px 0 rgba(0, 0, 0, 0.4);
  box-shadow: 0 3px 9px 0 rgba(0, 0, 0, 0.4);
  z-index: 4;
  margin-bottom: -2px;
  background: white;
  transition: all 0.5s;
}
/* line 15, ../../../../../sass/styles_mobile/3/_header.scss */
header #menu_button {
  width: 9%;
  height: 30px;
  display: inline-block;
  font-size: 60px;
  position: relative;
  margin: 0 15px;
}
/* line 23, ../../../../../sass/styles_mobile/3/_header.scss */
header #menu_button .menu_icon {
  position: absolute;
  top: 50%;
  right: 0;
  width: 55%;
  height: 2px;
  background: #2BB0A3;
}
/* line 31, ../../../../../sass/styles_mobile/3/_header.scss */
header #menu_button .menu_icon:before {
  content: '';
  position: absolute;
  width: 150%;
  right: 0;
  top: -9px;
  height: 2px;
  background: #2BB0A3;
}
/* line 41, ../../../../../sass/styles_mobile/3/_header.scss */
header #menu_button .menu_icon:after {
  content: '';
  position: absolute;
  width: 150%;
  right: 0;
  bottom: -9px;
  height: 2px;
  background: #2BB0A3;
}
/* line 53, ../../../../../sass/styles_mobile/3/_header.scss */
header .logo {
  display: inline-block;
  width: 38%;
  position: relative;
  vertical-align: top;
  margin-top: 4px;
  margin-left: 10px;
}
/* line 61, ../../../../../sass/styles_mobile/3/_header.scss */
header .logo img {
  width: 100%;
}
/* line 66, ../../../../../sass/styles_mobile/3/_header.scss */
header .right_controlls {
  float: right;
  position: absolute;
  right: 20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 77, ../../../../../sass/styles_mobile/3/_header.scss */
header .right_controlls a {
  margin-left: 7px;
}
/* line 80, ../../../../../sass/styles_mobile/3/_header.scss */
header .right_controlls a i {
  font-size: 23px;
  -webkit-transform: scaleX(-1);
  -moz-transform: scaleX(-1);
  -ms-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  transform: scaleX(-1);
  font-weight: 300;
}

/* line 93, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu {
  width: 90%;
  display: block;
  position: fixed;
  z-index: 3;
  right: 100%;
  top: 0;
  bottom: 0;
  background: white;
  -webkit-transition: right 0.5s;
  -moz-transition: right 0.5s;
  -ms-transition: right 0.5s;
  -o-transition: right 0.5s;
  transition: right 0.5s;
  overflow: auto;
  color: #424242;
}
/* line 110, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu.active {
  right: 10%;
}
/* line 114, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .top_wrapper {
  display: table;
  width: 100%;
  padding: 20px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid #dfdfdf;
}
/* line 123, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .top_wrapper .logo {
  width: 50%;
  float: left;
}
/* line 127, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .top_wrapper .logo img {
  width: 100%;
}
/* line 132, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .top_wrapper .right_controlls {
  float: right;
}
/* line 135, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .top_wrapper .right_controlls .close_button {
  font-size: 31px;
  line-height: 1;
  display: inline-block;
}
/* line 141, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .top_wrapper .right_controlls a {
  font-size: 23px;
  line-height: 1;
  display: inline-block;
  -webkit-transform: scaleX(-1);
  -moz-transform: scaleX(-1);
  -ms-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  margin-right: 12px;
  transform: scaleX(-1);
  vertical-align: top;
  margin-top: 3px;
}
/* line 158, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu + .black_overlay_menu {
  position: fixed;
  right: 100%;
  height: 100vh;
  width: 100vw;
  top: 0;
  z-index: 2;
  background: rgba(45, 45, 45, 0.5);
  transition: right 0.5s;
}
/* line 169, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu.active + .black_overlay_menu {
  right: 0;
}
/* line 173, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper {
  padding: 20px 0;
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border-top: 1px solid #dfdfdf;
  text-transform: uppercase;
  font-weight: bold;
}
/* line 183, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper .preview {
  padding: 0 20px;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 192, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper .selected_language {
  float: right;
}
/* line 196, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper .language_options {
  max-height: 0;
  overflow: hidden;
  -webkit-transition: max-height 0.5s linear;
  -moz-transition: max-height 0.5s linear;
  -ms-transition: max-height 0.5s linear;
  -o-transition: max-height 0.5s linear;
  transition: max-height 0.5s linear;
  background: #f4f4f4;
}
/* line 206, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper .language_options .language_element {
  display: block;
  clear: both;
  margin: 15px 20px 15px 50px;
  text-decoration: none;
  color: gray;
}
/* line 215, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper.active {
  padding-bottom: 0;
}
/* line 218, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper.active .language_options {
  max-height: 400px;
}
/* line 222, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .language_wrapper.active .preview {
  margin-bottom: 20px;
}
/* line 228, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
/* line 233, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul li {
  font-weight: bold;
  text-transform: uppercase;
  padding: 15px 20px;
  border-bottom: 1px solid #dfdfdf;
}
/* line 239, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul li:last-of-type {
  border-bottom: 0;
}
/* line 243, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul li.section_subcontent {
  padding: 15px 0;
}
/* line 246, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul li.section_subcontent.active {
  border-bottom: 0;
}
/* line 251, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul li a {
  text-decoration: none;
  color: #424242;
}
/* line 257, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main {
  position: relative;
  padding-left: 20px;
  padding-right: 20px;
}
/* line 262, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .more, .main_menu .main_ul .submenu_main .less {
  -webkit-transition: opacity 0.5s;
  -moz-transition: opacity 0.5s;
  -ms-transition: opacity 0.5s;
  -o-transition: opacity 0.5s;
  transition: opacity 0.5s;
  position: absolute;
  right: 20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 23px;
  height: 23px;
  color: #2BB0A3;
  font-size: 23px;
}
/* line 281, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .more i, .main_menu .main_ul .submenu_main .less i {
  vertical-align: top;
  line-height: 1;
}
/* line 286, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .more span, .main_menu .main_ul .submenu_main .less span {
  margin: auto;
  text-align: center;
  font-weight: lighter;
  font-size: 28px;
  color: white;
  position: absolute;
  top: -50%;
  left: 0;
  right: 0;
}
/* line 298, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .more .circle_wrapper, .main_menu .main_ul .submenu_main .less .circle_wrapper {
  display: block;
  width: 24px;
  height: 24px;
  background: #2BB0A3;
  border-radius: 30px;
  position: relative;
}
/* line 306, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .more .circle_wrapper span, .main_menu .main_ul .submenu_main .less .circle_wrapper span {
  font-weight: lighter;
  font-size: 27px;
  position: absolute;
  left: 1px;
  right: 0;
  margin: auto;
  top: 47%;
  transform: translateY(-50%);
}
/* line 319, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .less {
  opacity: 0;
  background: transparent;
}
/* line 323, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .less .circle_wrapper {
  background: transparent;
  color: #2BB0A3;
  border: 1px solid;
}
/* line 328, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .less .circle_wrapper span {
  top: 38%;
}
/* line 333, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main .less span {
  color: #2BB0A3;
}
/* line 339, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main.active .more {
  opacity: 0;
}
/* line 343, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_main.active .less {
  opacity: 1;
}
/* line 349, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list {
  max-height: 0;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 358, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list > div {
  padding: 15px 0;
}
/* line 362, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list .submenu_main, .main_menu .main_ul .submenu_list .submenu_element {
  padding-left: 40px;
  color: #a2a2a2;
  font-size: 14px;
}
/* line 369, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level1 .search_container {
  margin-left: 20px;
  margin-bottom: 20px;
  padding: 0;
}
/* line 373, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level1 .search_container i {
  color: #2BB0A3;
  font-size: 30px;
  vertical-align: bottom;
  margin-right: 15px;
}
/* line 379, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level1 .search_container input.search_subsection {
  border: none;
  border-bottom: 2px solid #2BB0A3;
  padding: 8px 30px 8px 0;
  color: #a2a2a2;
  font-size: 14px;
  font-weight: lighter;
  width: 70%;
  max-width: 300px;
}
/* line 389, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level1 .search_container input.search_subsection::placeholder {
  color: #a2a2a2;
  font-size: 14px;
  font-weight: lighter;
}
/* line 395, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level1 .search_container input.search_subsection:focus {
  outline: none;
}
/* line 401, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level1 .submenu_element {
  padding-left: 40px;
}
/* line 404, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level1 .submenu_element a {
  color: #a2a2a2;
}
/* line 410, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level2 {
  background: #f4f4f4;
}
/* line 413, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.level2 .submenu_element a {
  text-transform: initial;
}
/* line 418, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list.active {
  max-height: 1300px;
  padding-top: 10px;
  margin-top: 20px;
}
/* line 425, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .main_ul .submenu_list .submenu_list {
  margin-top: 0;
  padding-top: 0;
  padding-bottom: 0;
}
/* line 433, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .class_extra_info_menu {
  margin: 20px;
  background: #f4f4f4;
  padding: 10px 25px 15px;
}
/* line 438, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .class_extra_info_menu .extra_title {
  text-align: center;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 13px;
  border-bottom: 1px solid;
  padding-bottom: 17px;
  margin-bottom: 20px;
  margin-top: 10px;
}
/* line 449, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .class_extra_info_menu .tick_element {
  margin-bottom: 10px;
}
/* line 452, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .class_extra_info_menu .tick_element i {
  margin-right: 10px;
  font-size: 20px;
  vertical-align: middle;
  color: #2BB0A3;
}
/* line 459, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .class_extra_info_menu .tick_element .tick_title {
  letter-spacing: 1.5px;
  font-size: 12px;
}
/* line 464, ../../../../../sass/styles_mobile/3/_header.scss */
.main_menu .class_extra_info_menu .tick_element i, .main_menu .class_extra_info_menu .tick_element .tick_title {
  display: inline-block;
  text-transform: uppercase;
  font-weight: lighter;
}

/* line 474, ../../../../../sass/styles_mobile/3/_header.scss */
body.right_move header {
  left: 90%;
}

/* line 479, ../../../../../sass/styles_mobile/3/_header.scss */
.slider_inner_section {
  margin-top: 6vh;
}

/* line 1, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 {
  margin: 20px;
  border: 1px solid #d0d0d0;
}
/* line 5, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .wrapper-new-web-support.booking_form_title {
  margin: 0 10px 10px;
  font-size: 12px;
  text-align: center;
}
/* line 11, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .destination_wrapper {
  border-bottom: 1px solid #d0d0d0;
}
/* line 15, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .dates_selector_wrapper {
  border-bottom: 1px solid #d0d0d0;
}
/* line 19, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .booking_title {
  margin: 13px 20px;
  margin-bottom: 0;
  border-bottom: 1px solid #d0d0d0;
  text-transform: uppercase;
  padding-bottom: 7px;
  position: relative;
  width: calc(100% - 40px);
  font-size: 14px;
}
/* line 29, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .booking_title i {
  position: absolute;
  right: 0;
  top: 45%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  font-size: 19px;
}
/* line 42, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .label_field {
  font-size: 14px;
}
/* line 46, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .booking_value {
  font-weight: bolder;
  color: #5e5e5e;
}
/* line 51, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.mobile_engine_v2 .submit_button {
  margin: 10px;
  background: #f7bb1e;
  border: 0;
  width: calc(100% - 20px);
  color: white;
  text-transform: uppercase;
  font-weight: bolder;
  font-size: 23px;
  padding: 10px 0;
}

/* line 64, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
#booking_engine_popup {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 100%;
  left: -100%;
  background: white;
  z-index: 4;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 78, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
#booking_engine_popup.active {
  left: 0;
  right: 0;
}
/* line 83, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
#booking_engine_popup .logo_wrapper {
  padding: 17px 17px 0;
  text-align: center;
}
/* line 87, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
#booking_engine_popup .logo_wrapper .logotype_mobile {
  max-height: 30px;
}
/* line 92, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
#booking_engine_popup .close_engine_popup {
  position: absolute;
  right: 25px;
  top: 8px;
  font-size: 29px;
  color: #002578;
}
/* line 100, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
#booking_engine_popup .mobile_engine_v2 {
  margin-top: 5px;
  max-height: 88vh;
  overflow: auto;
}

/* line 107, ../../../../../sass/styles_mobile/3/booking_engine/booking_styles.scss */
.promocode_wrapper .promocode_input {
  width: 100%;
  display: block;
  background: #f4f4f4;
  border: 0;
  text-align: center;
  padding: 12px 0;
}
/* line 16, ../../sass/mobile/styles_mobile_test11.scss */
.promocode_wrapper .promocode_input::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #9a9a9a;
  opacity: 1;
  /* Firefox */
}
/* line 22, ../../sass/mobile/styles_mobile_test11.scss */
.promocode_wrapper .promocode_input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #9a9a9a;
}
/* line 27, ../../sass/mobile/styles_mobile_test11.scss */
.promocode_wrapper .promocode_input::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #9a9a9a;
}

/* line 2, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.hotel_selector .selected_hotel {
  text-align: center;
  margin-bottom: 25px;
  margin-top: 10px;
  font-size: 15px;
  font-weight: bold;
  color: #5e5e5e;
}
/* line 12, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.hotel_selector.active .destination_wrapper {
  max-height: 2000px;
  padding-bottom: 30px;
}

/* line 19, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper {
  padding-bottom: 0;
  max-height: 0;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  overflow: hidden;
}
/* line 29, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_element {
  padding: 9px 0;
}
/* line 33, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_label {
  padding: 0 20px 0 25px;
  color: gray;
  font-size: 16px;
  text-transform: uppercase;
  font-weight: bold;
  position: relative;
}
/* line 41, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_label i {
  position: absolute;
  right: 20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 56, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_label i.more {
  color: #002578;
}
/* line 60, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_label i.less {
  opacity: 0;
}
/* line 66, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_options {
  max-height: 0;
  display: block;
  overflow: hidden;
  background: #f4f4f4;
  padding-left: 25px;
  font-weight: 600;
  font-size: 15px;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 80, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_options.alone_option {
  max-height: none;
}
/* line 84, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_options .hotel_element {
  margin-bottom: 20px;
  color: #5e5e5e;
}
/* line 88, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_options .hotel_element:last-of-type {
  margin-bottom: 0;
}
/* line 92, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_options .hotel_element span {
  margin-right: 7px;
}
/* line 96, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_options .hotel_element i {
  font-size: 10px;
  vertical-align: top;
  margin-top: 4px;
}
/* line 106, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_element.active .group_label .more {
  opacity: 0;
}
/* line 110, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_element.active .group_label .less {
  opacity: 1;
}
/* line 115, ../../../../../sass/styles_mobile/3/booking_engine/_hotel_selector.scss */
.destination_wrapper .group_element.active .group_options {
  max-height: 750px;
  margin-top: 10px;
  padding-top: 20px;
  padding-bottom: 20px;
}

/* line 1, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
.dates_selector_wrapper {
  display: table;
  width: 100%;
}
/* line 5, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
.dates_selector_wrapper .stay_selection {
  margin: 0 20px 20px;
  display: table;
  width: calc(100% - 40px);
}
/* line 10, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
.dates_selector_wrapper .stay_selection .entry_date_wrapper, .dates_selector_wrapper .stay_selection .departure_date_wrapper {
  width: 50%;
  float: left;
  text-align: center;
  padding-top: 5px;
}
/* line 17, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
.dates_selector_wrapper .stay_selection .entry_date_wrapper {
  border-right: 1px solid #cacaca;
}

/* line 23, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
.flexible_dates_wrapper {
  position: absolute;
  top: 16%;
  bottom: 0;
  background: white;
  left: 100%;
  right: -100%;
  z-index: 2;
  transition: all .5s;
  overflow: auto;
}
/* line 34, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
.flexible_dates_wrapper.active {
  left: 0;
  right: 0;
}

/* line 40, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup, #departure_date_popup {
  position: fixed;
  top: 100%;
  bottom: -100%;
  left: 0;
  right: 0;
  z-index: 5;
  background: white;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 54, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup.active, #departure_date_popup.active {
  top: 0;
  bottom: 0;
}
/* line 59, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .header_wrapper, #departure_date_popup .header_wrapper {
  padding: 20px 20px 0;
}
/* line 62, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .header_wrapper .banner_title, #departure_date_popup .header_wrapper .banner_title {
  text-transform: uppercase;
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #424242;
  font-style: italic;
}
/* line 70, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .header_wrapper .banner_title i, #departure_date_popup .header_wrapper .banner_title i {
  margin-right: 10px;
  color: #2BB0A3;
  font-size: 23px;
  vertical-align: middle;
}
/* line 78, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .header_wrapper .close_popup, #departure_date_popup .header_wrapper .close_popup {
  position: absolute;
  right: 25px;
  top: 8px;
  font-size: 29px;
  color: #2BB0A3;
}
/* line 87, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller, #departure_date_popup .menu_controller {
  border-top: 1px solid rgba(81, 81, 81, 0.26);
  border-bottom: 1px solid rgba(81, 81, 81, 0.26);
  display: table;
  width: 100%;
  height: 20px;
}
/* line 94, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller .dates_button, #departure_date_popup .menu_controller .dates_button {
  padding: 10px 0;
  width: 50%;
  float: left;
  text-align: center;
  position: relative;
  display: table;
  height: 100%;
  text-transform: uppercase;
  font-size: 13px;
}
/* line 105, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller .dates_button span, #departure_date_popup .menu_controller .dates_button span {
  display: table-cell;
  vertical-align: middle;
}
/* line 110, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller .dates_button.active, #departure_date_popup .menu_controller .dates_button.active {
  position: relative;
}
/* line 113, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .menu_controller .dates_button.active:after, #departure_date_popup .menu_controller .dates_button.active:after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: calc(100% - 1.5px);
  height: 3px;
  background: black;
  width: 100%;
}
/* line 127, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .ui-datepicker-group, #departure_date_popup .ui-datepicker-group {
  display: block;
  clear: both;
  width: 100%;
  margin-bottom: 10px;
}
/* line 134, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker, #entry_date_popup .end_datepicker, #departure_date_popup .start_datepicker, #departure_date_popup .end_datepicker {
  max-height: calc(80vh - 40px);
  overflow: auto;
}
/* line 138, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-calendar *, #entry_date_popup .end_datepicker .ui-datepicker-calendar *, #departure_date_popup .start_datepicker .ui-datepicker-calendar *, #departure_date_popup .end_datepicker .ui-datepicker-calendar * {
  border: none !important;
  border-collapse: collapse !important;
  padding: 0 !important;
  margin: 0 !important;
}
/* line 145, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline, #entry_date_popup .end_datepicker .ui-datepicker-inline, #departure_date_popup .start_datepicker .ui-datepicker-inline, #departure_date_popup .end_datepicker .ui-datepicker-inline {
  width: 100% !important;
  border: 0;
}
/* line 149, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-prev, #entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-next, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-prev, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-next, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-prev, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-next, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-prev, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-next {
  display: none;
}
/* line 153, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-header, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-header, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-header, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-header {
  background: none;
  border: 0;
  color: #515151;
  font-style: italic;
  font-weight: bold;
  border-bottom: 1px solid rgba(81, 81, 81, 0.26);
  margin-bottom: 20px;
}
/* line 163, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-state-default, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-state-default, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-state-default, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-state-default {
  text-align: center;
  background: none;
  border: 0;
  color: #515151;
  line-height: 41px;
  font-weight: lighter;
}
/* line 172, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .highlight_day, #entry_date_popup .end_datepicker .ui-datepicker-inline .highlight_day, #departure_date_popup .start_datepicker .ui-datepicker-inline .highlight_day, #departure_date_popup .end_datepicker .ui-datepicker-inline .highlight_day {
  background: #bfc0c0;
}
/* line 175, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .highlight_day a, #entry_date_popup .end_datepicker .ui-datepicker-inline .highlight_day a, #departure_date_popup .start_datepicker .ui-datepicker-inline .highlight_day a, #departure_date_popup .end_datepicker .ui-datepicker-inline .highlight_day a {
  color: white;
}
/* line 180, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day {
  position: relative;
}
/* line 183, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before {
  content: '';
  position: absolute;
  left: 0;
  width: 50%;
  top: 0;
  bottom: 0;
  background: #bfc0c0;
}
/* line 193, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active {
  color: white;
  border-radius: 50px;
  position: relative;
  z-index: 1;
}
/* line 200, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #515151;
  max-width: 41px;
  max-height: 41px;
  z-index: -1;
  margin: auto;
  border-radius: 41px;
}
/* line 217, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection {
  position: relative;
  opacity: 1;
}
/* line 221, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection:before {
  content: '';
  position: absolute;
  right: 0;
  width: 50%;
  top: 0;
  bottom: 0;
  background: #bfc0c0;
  z-index: 0;
}
/* line 232, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span, #entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection a, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection a, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection a, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection a {
  color: white;
  border-radius: 50px;
  position: relative;
  z-index: 1;
}
/* line 240, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span:before, #entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection a:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection a:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection a:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection a:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #515151;
  max-width: 41px;
  max-height: 41px;
  z-index: -1;
  margin: auto;
  border-radius: 41px;
}
/* line 257, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline thead span, #entry_date_popup .end_datepicker .ui-datepicker-inline thead span, #departure_date_popup .start_datepicker .ui-datepicker-inline thead span, #departure_date_popup .end_datepicker .ui-datepicker-inline thead span {
  color: #515151;
}
/* line 264, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .start_datepicker .ui-datepicker-current-day:before, #departure_date_popup .start_datepicker .ui-datepicker-current-day:before {
  display: none;
}
/* line 269, ../../../../../sass/styles_mobile/3/booking_engine/_dates_selector.scss */
#entry_date_popup .step_label, #departure_date_popup .step_label {
  position: absolute;
  bottom: 0;
  background: white;
  width: 100%;
  padding: 15px;
  border-top: 1px solid rgba(81, 81, 81, 0.26);
  font-size: 14px;
}

/* Calendar colors */
/* End */
/* Process buttons colors */
/* End*/
/* Fonts */
/* End */
/* Font sizes */
/* End */
/* line 3, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price, #calendar_price_availability {
  position: relative;
  overflow: hidden;
}
/* line 9, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price .header_wrapper_calendar_availability .graph_calendar_selector, #calendar_price_availability .header_wrapper_calendar_availability .graph_calendar_selector {
  display: none;
}
/* line 13, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price .header_wrapper_calendar_availability .popup_helper_wrapper, #calendar_price_availability .header_wrapper_calendar_availability .popup_helper_wrapper {
  position: fixed;
  bottom: 0;
  background: white;
  width: 100%;
  padding: 15px;
  border-top: 1px solid rgba(81, 81, 81, 0.26);
  font-size: 14px;
  z-index: 2;
}
/* line 23, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price .header_wrapper_calendar_availability .popup_helper_wrapper:after, #calendar_price_availability .header_wrapper_calendar_availability .popup_helper_wrapper:after {
  content: '\f059';
  display: inline-block;
  vertical-align: middle;
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 300;
  font-size: 16px;
  margin-left: 5px;
}
/* line 35, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar, #calendar_price_availability #prices-calendar {
  position: relative;
  margin-bottom: 16px;
}
/* line 39, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector, #calendar_price_availability #prices-calendar .popup_month_selector {
  position: absolute;
  display: flex;
  justify-content: space-between;
  width: 100%;
  top: 15px;
}
/* line 46, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector select, #calendar_price_availability #prices-calendar .popup_month_selector select {
  display: none;
}
/* line 50, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector > div, #calendar_price_availability #prices-calendar .popup_month_selector > div {
  position: relative;
  width: 30px;
  height: 30px;
}
/* line 55, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector > div::before, #calendar_price_availability #prices-calendar .popup_month_selector > div::before {
  position: absolute;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-family: "Font Awesome 5 Pro";
  color: #333;
  font-weight: 300;
  font-size: 30px;
}
/* line 66, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector .previous_month_selector::before, #calendar_price_availability #prices-calendar .popup_month_selector .previous_month_selector::before {
  content: '\f104';
}
/* line 72, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .popup_month_selector .next_month_selector::before, #calendar_price_availability #prices-calendar .popup_month_selector .next_month_selector::before {
  content: '\f105';
}
/* line 78, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar #selectorRooms, #calendar_price_availability #prices-calendar #selectorRooms {
  display: none;
}
/* line 86, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar {
  width: 100%;
}
/* line 91, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr {
  border-top: 3px solid white;
  border-bottom: 3px solid white;
}
/* line 96, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr:nth-child(1) th, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr:nth-child(1) th {
  font-size: 1.2rem;
  padding: 15px;
  font-weight: 700;
}
/* line 103, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr:nth-child(2), #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr:nth-child(2) {
  display: none;
}
/* line 107, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td {
  vertical-align: top;
  width: calc(100vw / 7);
  position: relative;
  text-align: center;
  border-left: 3px solid white;
  border-left: 3px solid white;
  background-color: #f0f0f0;
}
/* line 116, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .another-month-day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .another-month-day {
  background-color: #dfdfdf;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
/* line 125, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day {
  font-size: 0.65rem;
  color: white;
  font-weight: 500;
  padding: 3px;
}
/* line 131, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.not-available-day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.not-available-day {
  background-color: #E75354;
}
/* line 135, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.available-day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.available-day {
  background-color: #00ac6b;
}
/* line 139, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.restricted-day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.restricted-day {
  background-color: orange;
}
/* line 143, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.restricted-day + .day-content.available .price:before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day.restricted-day + .day-content.available .price:before {
  color: orange;
}
/* line 151, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content img, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content img {
  display: none;
}
/* line 155, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content .price, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content .price {
  padding-top: 15px;
  font-size: 0.65rem;
  height: 30px;
  font-weight: 500;
}
/* line 2, ../../../../../sass/styles_mobile/3/booking_engine/_variables.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.available .price::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.available .price::before {
  content: "";
  position: absolute;
  top: 28px;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: 'Font Awesome 5 Pro', 'icomoon';
  font-size: 20px;
  color: #00ac6b;
}
/* line 167, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.available .restriction-message, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.available .restriction-message {
  font-size: 0.4rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: -.3px;
}
/* line 176, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.no-available .not-available-message, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.no-available .not-available-message {
  height: 30px;
  font-size: 0.5rem;
  font-weight: 500;
  text-transform: uppercase;
  display: flex;
  align-items: center;
}
@media (max-width: 359px) {
  /* line 176, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
  #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.no-available .not-available-message, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td .day-content.no-available .not-available-message {
    font-size: 0.4rem;
  }
}
/* line 192, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day, #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content {
  background-color: #7ccff4;
}
/* line 198, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content.available .price, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content.available .price {
  color: white;
}
/* line 201, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content.available .price::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content.available .price::before {
  color: white;
}
/* line 207, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content .restriction-message, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent .day-content .restriction-message {
  color: white;
}
/* line 213, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection::before, #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection::before {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  top: 50%;
  z-index: 1;
}
/* line 224, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection .day, #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection .day-content, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection .day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection .day-content {
  background-color: #1eadec;
}
/* line 228, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.first-selection::before {
  background-color: #1eadec;
  right: 0;
  transform: translate(50%, -50%) rotate(45deg);
}
/* line 236, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection .day, #calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection .day-content, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection .day, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection .day-content {
  background-color: #118ec6;
}
/* line 240, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection::before, #calendar_price_availability #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar tr td.selected-cell-parent.end-selection::before {
  background-color: #118ec6;
  left: 0;
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 254, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .graphs_fields_wrapper, #calendar_price_availability #prices-calendar .calendars-section .graphs_fields_wrapper {
  display: none;
}
/* line 258, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend, #calendar_price_availability #prices-calendar .calendars-section .legend {
  padding: 15px;
}
/* line 262, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li, #calendar_price_availability #prices-calendar .calendars-section .legend ul li {
  position: relative;
  padding-left: 20px;
  font-size: 0.8rem;
}
/* line 267, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li::before, #calendar_price_availability #prices-calendar .calendars-section .legend ul li::before {
  position: absolute;
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
/* line 279, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li:nth-child(1)::before, #calendar_price_availability #prices-calendar .calendars-section .legend ul li:nth-child(1)::before {
  background-color: #00ac6b;
}
/* line 285, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li:nth-child(2)::before, #calendar_price_availability #prices-calendar .calendars-section .legend ul li:nth-child(2)::before {
  background-color: orange;
}
/* line 291, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li:nth-child(3)::before, #calendar_price_availability #prices-calendar .calendars-section .legend ul li:nth-child(3)::before {
  background-color: #E75354;
}
/* line 296, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .legend ul li:nth-child(4), #calendar_price_availability #prices-calendar .calendars-section .legend ul li:nth-child(4) {
  display: none;
}
/* line 304, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper {
  padding: 15px;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  font-size: 0.8rem;
  font-weight: bold;
  margin-bottom: 100px;
  width: 100%;
}
/* line 314, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div {
  width: 33%;
  padding: 5px 15px;
}
/* line 318, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.label_actual_selection, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.label_actual_selection {
  background-color: #efefef;
  color: #333;
}
/* line 323, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.nights_number_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.nights_number_wrapper {
  background-color: #a2a2a2;
  color: white;
}
/* line 327, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.nights_number_wrapper > *:first-child, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.nights_number_wrapper > *:first-child {
  display: block;
}
/* line 332, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.selection_price_wrapper, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.selection_price_wrapper {
  background-color: #6f6f6f;
  color: white;
}
/* line 337, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.selection_price_wrapper .vertical_center > *:first-child, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper > div.selection_price_wrapper .vertical_center > *:first-child {
  display: block;
}
/* line 345, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price #prices-calendar .calendars-section .buttons-section .button, #calendar_price_availability #prices-calendar .calendars-section .buttons-section .button {
  position: fixed;
  bottom: 10px;
  left: 0;
  right: 0;
}
/* line 355, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price .modifyButtonCalendar, #calendar_price_availability .modifyButtonCalendar {
  right: 20px !important;
  left: 100% !important;
  bottom: 60px !important;
  border: 0;
  font-size: 17px;
  text-transform: uppercase;
  padding: 10px 30px;
  color: white;
  background: #f7bb1e;
  transition: left .5s, right .5s;
}
/* line 367, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
#calendar_price .modifyButtonCalendar.disabled-button, #calendar_price_availability .modifyButtonCalendar.disabled-button {
  opacity: 0.8;
  background: lightgray;
}

/* line 375, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
.flexible_dates_wrapper.active #calendar_price_availability .modifyButtonCalendar {
  left: auto !important;
  right: 20px !important;
}

/* line 381, ../../../../../sass/styles_mobile/3/booking_engine/_calendar_price.scss */
.select-last-day {
  display: none !important;
}

/* line 2, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
.room_list_wrapper .booking_title {
  margin-bottom: 0;
}
/* line 6, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
.room_list_wrapper .total_occupancy_wrapper {
  margin: 0 20px;
  display: table;
  width: calc(100% - 40px);
  margin-bottom: 20px;
}
/* line 12, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
.room_list_wrapper .total_occupancy_wrapper .rooms_number, .room_list_wrapper .total_occupancy_wrapper .occupancy_number {
  float: left;
  text-align: center;
  padding-top: 5px;
}
/* line 18, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
.room_list_wrapper .total_occupancy_wrapper .rooms_number {
  width: 35%;
  border-right: 1px solid #cacaca;
}
/* line 22, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
.room_list_wrapper .total_occupancy_wrapper .rooms_number .rooms_number_value {
  display: block;
}
/* line 27, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
.room_list_wrapper .total_occupancy_wrapper .occupancy_number {
  width: 65%;
}
/* line 30, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
.room_list_wrapper .total_occupancy_wrapper .occupancy_number .occupancy_number_value {
  display: block;
}

/* line 37, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup {
  position: fixed;
  top: 100%;
  bottom: -100%;
  left: 0;
  right: 0;
  background: white;
  z-index: 3;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 51, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup.active {
  top: 0;
  bottom: 0;
}
/* line 56, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .header_wrapper {
  padding: 20px;
  border-bottom: 1px solid rgba(81, 81, 81, 0.26);
}
/* line 60, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .header_wrapper .banner_title {
  text-transform: uppercase;
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 0;
  color: #424242;
  font-style: italic;
}
/* line 68, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .header_wrapper .banner_title i {
  margin-right: 10px;
}
/* line 73, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .header_wrapper .close_popup {
  position: absolute;
  right: 25px;
  top: 8px;
  font-size: 29px;
  color: #2BB0A3;
}
/* line 82, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .rooms_wrapper {
  padding: 20px;
  max-height: 90vh;
  overflow: auto;
}
/* line 87, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .rooms_wrapper .rooms_number_wrapper {
  padding: 20px;
}
/* line 90, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .rooms_wrapper .rooms_number_wrapper .rooms_label {
  display: inline-block;
  color: #424242;
}
/* line 97, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .modification_buttons {
  width: 30%;
  float: right;
}
/* line 101, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .modification_buttons i, #occupancy_popup .modification_buttons input {
  display: inline-block;
  width: 30%;
  float: left;
  text-align: center;
  border: 0;
  font-size: 18px;
}
/* line 110, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .modification_buttons i {
  font-size: 21px;
  margin-top: 1px;
}
/* line 114, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .modification_buttons i.disabled {
  opacity: 0.4;
}
/* line 120, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .room_title {
  padding: 20px;
  background: #f4f4f4;
  font-weight: 600;
}
/* line 126, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element {
  padding: 20px;
  border-bottom: 1px solid #e2e2e2;
}
/* line 130, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element .age_selection_wrapper {
  width: 100%;
  overflow: hidden;
}
/* line 134, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element .age_selection_wrapper .age_option {
  display: inline-block;
  text-align: center;
  border: 1px solid black;
  font-size: 10px;
  padding: 3px;
  border-radius: 20px;
  width: 22px;
}
/* line 143, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element .age_selection_wrapper .age_option.active {
  background: black;
  color: white;
}
/* line 149, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element .age_selection_wrapper .block_age_selection {
  border-top: 1px solid #e2e2e2;
  padding: 13px 15px 0;
  margin-top: 15px;
  margin-left: -4%;
  margin-right: -7%;
  overflow: hidden;
}
/* line 157, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element .age_selection_wrapper .block_age_selection.hide {
  padding: 0 15px;
  max-height: 0;
  border-top: 0;
  margin-top: 0;
}
/* line 164, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element .age_selection_wrapper .block_age_selection label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}
/* line 172, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .occupancy_block_element:last-of-type {
  border-bottom: 0;
}
/* line 179, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .room_list_wrapper_close {
  padding: 15px;
  font-weight: bold;
  color: white;
  text-transform: uppercase;
  background: #002578;
  margin-top: 10px;
  text-align: center;
}
/* line 189, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .room {
  max-height: 0;
  -webkit-transition: max-height 0.5s;
  -moz-transition: max-height 0.5s;
  -ms-transition: max-height 0.5s;
  -o-transition: max-height 0.5s;
  transition: max-height 0.5s;
  overflow: hidden;
}
/* line 198, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .room label {
  color: #424242;
}
/* line 202, ../../../../../sass/styles_mobile/3/booking_engine/_occupancy_selector.scss */
#occupancy_popup .room.active {
  max-height: 450px;
  overflow: auto;
}

/* line 2, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper {
  position: relative;
}
/* line 5, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .background_image {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  max-width: 100%;
  z-index: -1;
}
/* line 15, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_title .main_title {
  color: #2BB0A3;
}
/* line 20, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper {
  padding: 0 20px;
  display: inline-block;
  width: 100%;
  position: relative;
}
/* line 26, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .input_email {
  border: 1px solid gray;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  height: 40px;
  padding: 0 10px;
  float: left;
  margin-right: 5px;
  width: calc(100% - 125px);
  font-family: "Open Sans", sans-serif;
}
/* line 38, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .input_email + .error.error_class {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  position: absolute;
  bottom: calc(100% + 5px);
  left: 20px;
  font-size: 10px;
  padding: 5px 10px;
  border-radius: 3px;
}
/* line 51, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .newsletter_button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: #2BB0A3;
  color: white;
  border: 0;
  height: 40px;
  float: left;
  width: 120px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  font-family: "Open Sans", sans-serif;
}
/* line 67, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper {
  display: inline-block;
  width: 100%;
  margin-top: 10px;
}
/* line 72, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper .newsletter_checkbox {
  margin-bottom: 0;
  display: inline-block;
  width: 100%;
  position: relative;
}
/* line 78, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper .newsletter_checkbox input {
  background-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 1px solid gray;
  width: 15px;
  height: 15px;
  vertical-align: top;
  float: left;
  margin-right: 12px;
  position: relative;
}
/* line 91, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper .newsletter_checkbox input:checked:before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background: #2BB0A3;
}
/* line 102, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper .newsletter_checkbox .check {
  display: none;
  background-color: #2BB0A3;
  position: absolute;
  top: 4px;
  left: 5px;
  width: 13px;
  height: 13px;
  z-index: -1;
}
/* line 113, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper .newsletter_checkbox .error {
  display: none !important;
}
/* line 117, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper .newsletter_checkbox label {
  font-size: 11px;
  color: gray;
  vertical-align: top;
  float: left;
  width: calc(100% - 50px);
  margin-top: 3px;
  line-height: 18px;
}
/* line 126, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .newsletter_form_wrapper .checks_wrapper .newsletter_checkbox label.error_class {
  color: #721c24;
}
/* line 134, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .social_newsletter {
  text-align: center;
}
/* line 137, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .newsletter_wrapper .social_newsletter a {
  color: #2BB0A3;
  font-size: 30px;
  display: inline-block;
  margin-right: 20px;
}
/* line 146, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper {
  margin-top: 10px;
}
/* line 149, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element {
  border-bottom: 1px solid white;
}
/* line 152, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_title {
  position: relative;
  background-color: #f4f4f4;
  padding: 10px 20px;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 14px;
  color: #2d2d2d;
}
/* line 162, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_title.active .circle_icon {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
/* line 171, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_title .icon {
  color: #2BB0A3;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 20px;
  font-size: 22px;
}
/* line 177, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_title .icon .circle_icon {
  background: #2BB0A3;
  -webkit-transition: transform 0.4s;
  -moz-transition: transform 0.4s;
  -ms-transition: transform 0.4s;
  -o-transition: transform 0.4s;
  transition: transform 0.4s;
  width: 25px;
  height: 25px;
  position: relative;
  border-radius: 25px;
}
/* line 185, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_title .icon .circle_icon i {
  color: white;
  font-size: 16px;
  position: absolute;
  top: 5px;
  bottom: 0;
  left: 5px;
  right: 0;
  display: block;
}
/* line 199, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_elements_wrapper {
  padding: 10px 40px;
  display: none;
}
/* line 203, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_elements_wrapper .section_element {
  display: block;
  margin-bottom: 18px;
  color: #2d2d2d;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.6px;
}
/* line 211, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .category_footer_element .category_elements_wrapper .section_element:last-of-type {
  margin-bottom: 0;
}
/* line 218, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .legal_links {
  -webkit-column-count: 3;
  -moz-column-count: 3;
  column-count: 3;
  padding: 20px;
}
/* line 224, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .footer_links_wrapper .legal_links .section_element {
  display: block;
  font-size: 10px;
  color: gray;
  margin-bottom: 8px;
  font-weight: 300;
}
/* line 234, ../../../../../sass/styles_mobile/3/_footer.scss */
footer .legal {
  color: gray;
  font-size: 12px;
  text-align: center;
  padding-bottom: 20px;
}

@media screen and (max-width: 321px) {
  /* line 3, ../../../../../sass/styles_mobile/3/_responsive_styles.scss */
  .main_menu .main_ul .submenu_list .submenu_main {
    font-size: 12px !important;
  }
  /* line 6, ../../../../../sass/styles_mobile/3/_responsive_styles.scss */
  .main_menu .main_ul .submenu_list .submenu_element {
    font-size: 11px;
  }
}
@-webkit-keyframes blink {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* Standard syntax */
@keyframes blink {
  0% {
    margin-bottom: 50px;
    opacity: 0;
  }
  100% {
    margin-bottom: 0;
    opacity: 1;
  }
}
/* line 45, ../../../../../sass/styles_mobile/3/_3.scss */
.container_popup_booking img {
  background-color: #777;
}

/* line 1, ../../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_divided_title {
  position: absolute;
  display: block;
  width: 100%;
  height: 300px;
  box-sizing: border-box;
  z-index: 2;
  color: white;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  background: rgba(0, 0, 0, 0.3);
  /* For browsers that do not support gradients */
  background: -webkit-linear-gradient(transparent, transparent, rgba(0, 0, 0, 0.5));
  /* For Safari 5.1 to 6.0 */
  background: -o-linear-gradient(transparent, transparent, rgba(0, 0, 0, 0.5));
  /* For Opera 11.1 to 12.0 */
  background: -moz-linear-gradient(transparent, transparent, rgba(0, 0, 0, 0.5));
  /* For Firefox 3.6 to 15 */
  background: linear-gradient(transparent, transparent, rgba(0, 0, 0, 0.5));
  /* Standard syntax */
  margin: 0;
  padding: 0;
}
/* line 17, ../../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_divided_title span {
  position: absolute;
  bottom: 20px;
  right: 20px;
  left: 20px;
  text-align: right;
}

/* line 25, ../../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_1:last-child {
  margin-bottom: -40px;
}

/* line 28, ../../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_1 {
  list-style-type: none;
  margin: 0;
  position: relative;
  padding: 0;
  width: 100%;
  display: table;
}
/* line 35, ../../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_1 li {
  display: inline-block;
  width: calc(100% / 3);
  height: 150px;
  float: left;
  overflow: hidden;
  position: relative;
}
/* line 42, ../../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_1 li.notshow {
  display: none;
}
/* line 45, ../../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_1 li img, .gallery_1 li iframe, .gallery_1 li .overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-height: 100%;
  min-width: 100%;
  max-width: none;
}
/* line 51, ../../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_1 li .overlay {
  width: 100%;
  height: 100%;
  z-index: 10;
}
/* line 56, ../../../../../sass/styles_mobile/2/_gallery.scss */
.gallery_1 li:first-of-type {
  width: 100%;
  height: 300px;
}

/* line 5, ../../sass/mobile/v2/_header.scss */
.main_menu .top_wrapper .right_controlls .color_buttons i {
  font-size: 23px;
  font-weight: 400;
}
/* line 10, ../../sass/mobile/v2/_header.scss */
.main_menu .top_wrapper .right_controlls .color_buttons.close_button {
  position: relative;
  width: 30px;
  height: 30px;
}
/* line 15, ../../sass/mobile/v2/_header.scss */
.main_menu .top_wrapper .right_controlls .color_buttons.close_button:before, .main_menu .top_wrapper .right_controlls .color_buttons.close_button:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  display: block;
  width: 30px;
  height: 1px;
  background-color: #2BB0A3;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}
/* line 27, ../../sass/mobile/v2/_header.scss */
.main_menu .top_wrapper .right_controlls .color_buttons.close_button:after {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
/* line 31, ../../sass/mobile/v2/_header.scss */
.main_menu .top_wrapper .right_controlls .color_buttons.close_button i {
  display: none;
}
/* line 41, ../../sass/mobile/v2/_header.scss */
.main_menu .main_ul .submenu_main .more, .main_menu .main_ul .submenu_main .less {
  -webkit-transition: none;
  -moz-transition: none;
  -ms-transition: none;
  -o-transition: none;
  transition: none;
}
/* line 53, ../../sass/mobile/v2/_header.scss */
.main_menu .language_wrapper .preview .selected_language {
  color: #2BB0A3 !important;
}
/* line 61, ../../sass/mobile/v2/_header.scss */
.main_menu .submenu_list.level2 .submenu_element a {
  color: #2BB0A3 !important;
}
/* line 65, ../../sass/mobile/v2/_header.scss */
.main_menu .submenu_list.level2 .submenu_element i {
  font-size: 11px;
}
/* line 68, ../../sass/mobile/v2/_header.scss */
.main_menu .submenu_list.level2 .submenu_element i:first-of-type {
  margin-left: 5px;
}

/* line 76, ../../sass/mobile/v2/_header.scss */
.default_content_wrapper.with_extra_header {
  margin-top: 80px;
}

/* line 82, ../../sass/mobile/v2/_header.scss */
header .logo {
  width: 60%;
}
/* line 85, ../../sass/mobile/v2/_header.scss */
header.with_extra_header {
  top: 30px;
}
/* line 88, ../../sass/mobile/v2/_header.scss */
header.with_extra_header .extra_top_header {
  transition: all 0.5s;
  font-size: 12px;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 31px;
  right: 0;
  width: 100%;
  background: #F3D132;
  color: #4b4b4b;
  text-align: center;
}
/* line 104, ../../sass/mobile/v2/_header.scss */
header.with_extra_header .extra_top_header i {
  display: inline-block;
  margin-right: 5px;
}
/* line 109, ../../sass/mobile/v2/_header.scss */
header.with_extra_header .extra_top_header p {
  display: inline-block;
}
/* line 113, ../../sass/mobile/v2/_header.scss */
header.with_extra_header .extra_top_header a {
  position: relative;
  color: #4b4b4b;
  text-decoration: none;
}

/* line 1, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 {
  z-index: 5;
}
/* line 4, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .booking_value {
  color: #23588a;
}
/* line 8, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .label_field {
  color: #2d2d2d;
}
/* line 13, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .booking_title .title_label {
  color: #2d2d2d;
}
/* line 17, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .booking_title i {
  color: #2BB0A3;
}
/* line 23, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .wrapper_booking_button .promocode_input, .mobile_engine_v2 .wrapper_booking_button .submit_button {
  font-family: "Open Sans", sans-serif;
}
/* line 27, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .wrapper_booking_button .submit_button {
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 30, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .wrapper_booking_button .submit_button.floating_button {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 4;
}
/* line 40, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .hotel_element {
  color: #2BB0A3 !important;
}

/* line 48, ../../sass/mobile/v2/_booking_widget.scss */
body.right_move .mobile_engine_v2 .wrapper_booking_button .submit_button.floating_button {
  left: 90%;
}
/* line 54, ../../sass/mobile/v2/_booking_widget.scss */
body.right_move header.with_extra_header .extra_top_header {
  left: 90%;
}

/* line 60, ../../sass/mobile/v2/_booking_widget.scss */
.hotel_selector .selected_hotel {
  color: #2BB0A3;
}

/* line 65, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .booking_value {
  color: #2BB0A3;
}

/* line 70, ../../sass/mobile/v2/_booking_widget.scss */
.mobile_engine_v2 .date_box {
  text-transform: lowercase;
}

/* line 75, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup, #departure_date_popup {
  -webkit-transition: none;
  -moz-transition: none;
  -ms-transition: none;
  -o-transition: none;
  transition: none;
}
/* line 82, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .ui-datepicker-title, #departure_date_popup .ui-datepicker-title {
  color: #012379;
}
/* line 86, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .dates_button.active, #departure_date_popup .dates_button.active {
  color: #012379;
}
/* line 89, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .dates_button.active:after, #departure_date_popup .dates_button.active:after {
  background: #23588a !important;
}
/* line 94, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .step_label, #departure_date_popup .step_label {
  color: #2BB0A3;
}
/* line 98, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .start_datepicker, #entry_date_popup .end_datepicker, #departure_date_popup .start_datepicker, #departure_date_popup .end_datepicker {
  font-family: "Open Sans", sans-serif;
}
/* line 101, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .start_datepicker .ui-datepicker-title, #entry_date_popup .end_datepicker .ui-datepicker-title, #departure_date_popup .start_datepicker .ui-datepicker-title, #departure_date_popup .end_datepicker .ui-datepicker-title {
  font-family: "Open Sans", sans-serif;
}
/* line 107, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection:before {
  background: #8fb8e3;
}
/* line 111, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .start_date_selection span:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span:before {
  background: #2BB0A3;
}
/* line 117, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day:before {
  background: #8fb8e3;
}
/* line 121, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #entry_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #departure_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before, #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before {
  background: #2BB0A3;
}
/* line 128, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .ui-datepicker-title, #entry_date_popup th, #entry_date_popup td, #departure_date_popup .ui-datepicker-title, #departure_date_popup th, #departure_date_popup td {
  font-family: "Open Sans", sans-serif;
}

/* line 136, ../../sass/mobile/v2/_booking_widget.scss */
#entry_date_popup .start_datepicker .ui-datepicker-inline .highlight_day, #entry_date_popup .end_datepicker .ui-datepicker-inline .highlight_day, #departure_date_popup .start_datepicker .ui-datepicker-inline .highlight_day, #departure_date_popup .end_datepicker .ui-datepicker-inline .highlight_day {
  background: #8fb8e3;
}

/* line 143, ../../sass/mobile/v2/_booking_widget.scss */
.modification_buttons {
  color: #2BB0A3;
}
/* line 146, ../../sass/mobile/v2/_booking_widget.scss */
.modification_buttons input {
  color: #2BB0A3;
  font-family: "Open Sans", sans-serif;
  margin-top: -3px;
  font-weight: bold;
}

/* line 155, ../../sass/mobile/v2/_booking_widget.scss */
.room_list_wrapper .rooms_number .rooms_number_value,
.room_list_wrapper .occupancy_number .occupancy_number_value,
.dates_selector_wrapper .date_box {
  color: #5c5c5c;
}

/* line 161, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup {
  z-index: 5;
}
/* line 165, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .header_wrapper .banner_title i {
  color: #2BB0A3;
  font-size: 23px;
  vertical-align: middle;
}
/* line 177, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .rooms_wrapper .room_list .room.active .room_title {
  color: #5c5c5c;
}
/* line 183, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .rooms_wrapper .room_list .room_list_wrapper_close {
  background-color: #F7BB1E;
}
/* line 189, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .room_title {
  color: #2BB0A3;
}
/* line 194, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .block_age_selection .age_label {
  color: #2BB0A3;
}
/* line 198, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .block_age_selection .age_option {
  border: 1px solid #2BB0A3 !important;
  color: #2BB0A3 !important;
}
/* line 202, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .block_age_selection .age_option.active {
  background: #2BB0A3 !important;
  color: white !important;
}
/* line 210, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .modification_buttons input {
  margin-top: 0;
}
/* line 214, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .modification_buttons i.minus, #occupancy_popup .modification_buttons i.plus {
  position: relative;
  display: inline-block;
  width: 24px;
  height: 24px;
  vertical-align: middle;
  border: 1px solid #2BB0A3;
  border-radius: 50%;
}
/* line 223, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .modification_buttons i.minus:before, #occupancy_popup .modification_buttons i.plus:before {
  content: '';
  position: absolute;
  top: 10.5px;
  left: 5.5px;
  display: block;
  width: 11.5px;
  height: 1px;
  background-color: #2BB0A3;
}
/* line 236, ../../sass/mobile/v2/_booking_widget.scss */
#occupancy_popup .modification_buttons i.plus:after {
  content: '';
  position: absolute;
  top: 5.5px;
  left: 10.5px;
  display: block;
  height: 11.5px;
  width: 1px;
  background-color: #2BB0A3;
}

/* line 252, ../../sass/mobile/v2/_booking_widget.scss */
.flexible_dates_wrapper {
  color: #2BB0A3;
}
/* line 255, ../../sass/mobile/v2/_booking_widget.scss */
.flexible_dates_wrapper table.calendar tr:first-of-type th {
  color: #2BB0A3;
  font-style: italic;
}

/* line 3, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .slider_inner_section img, .section_content .slider_inner_section img {
  width: 100%;
  vertical-align: middle;
}
/* line 9, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_hotel_title, .section_content .individual_hotel_title {
  text-align: center;
  margin-top: 18px;
}
/* line 13, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_hotel_title .main_title, .section_content .individual_hotel_title .main_title {
  color: #2BB0A3;
}
/* line 16, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_hotel_title .main_title i, .section_content .individual_hotel_title .main_title i {
  font-size: 9px;
  vertical-align: middle;
  margin-top: -6px;
}
/* line 24, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_hotel_services_wrapper, .section_content .individual_hotel_services_wrapper {
  margin-top: 40px;
}
/* line 28, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .tags_wrapper, .section_content .tags_wrapper {
  margin-top: 10px;
}
/* line 31, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .tags_wrapper .tag_element, .section_content .tags_wrapper .tag_element {
  font-size: 11px;
  display: inline-block;
  color: white;
  background: #ccc;
  padding: 5px 15px;
  font-style: italic;
  margin-bottom: 4px;
}
/* line 40, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .tags_wrapper .tag_element hide, .section_content .tags_wrapper .tag_element hide {
  display: none;
}
/* line 46, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper, .section_content .services_individual_hotel_wrapper {
  background: white;
  margin: 20px 5%;
  padding: 3% 10% 10%;
  display: block;
  width: 90%;
  box-sizing: border-box;
  max-height: 256px;
  overflow: hidden;
  position: relative;
  -webkit-transition: max-height 0.85s;
  -moz-transition: max-height 0.85s;
  -ms-transition: max-height 0.85s;
  -o-transition: max-height 0.85s;
  transition: max-height 0.85s;
  border: 1px solid #d0d0d0;
}
/* line 63, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper.active, .section_content .services_individual_hotel_wrapper.active {
  max-height: 1500px;
}
/* line 67, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper.active .see_all_wrapper i, .section_content .services_individual_hotel_wrapper.active .see_all_wrapper i {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
/* line 77, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .individual_service_item, .section_content .services_individual_hotel_wrapper .individual_service_item {
  display: table;
  clear: both;
  width: 100%;
  position: relative;
  border-bottom: 1px solid #d0d0d0;
  padding: 7px 0;
}
/* line 85, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .individual_service_item:first-of-type, .section_content .services_individual_hotel_wrapper .individual_service_item:first-of-type {
  padding-top: 0;
}
/* line 89, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .individual_service_item:nth-last-of-type(2), .section_content .services_individual_hotel_wrapper .individual_service_item:nth-last-of-type(2) {
  padding-bottom: 0;
  border-bottom: 0;
}
/* line 95, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .individual_service_item.has_hidden_description .see_more_icon, .section_content .services_individual_hotel_wrapper .individual_service_item.has_hidden_description .see_more_icon {
  display: inline-block;
}
/* line 101, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .individual_service_item.is_link_pdf .service_description:after, .section_content .services_individual_hotel_wrapper .individual_service_item.is_link_pdf .service_description:after {
  content: '\f019';
  font-family: "Font Awesome 5 Pro";
  font-weight: 900;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  font-size: 18px;
  margin-left: 5px;
  vertical-align: middle;
  color: #2BB0A3;
}
/* line 120, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_icon_wrapper, .section_content .services_individual_hotel_wrapper .service_icon_wrapper {
  float: left;
  padding-top: 15%;
  width: 15%;
  position: relative;
}
/* line 126, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_icon_wrapper .service_icon, .section_content .services_individual_hotel_wrapper .service_icon_wrapper .service_icon {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}
/* line 135, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_icon_wrapper i, .section_content .services_individual_hotel_wrapper .service_icon_wrapper i {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 21px;
  color: #2BB0A3;
}
/* line 149, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_description, .section_content .services_individual_hotel_wrapper .service_description {
  width: 80%;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  text-transform: uppercase;
  text-decoration: none;
  text-align: left;
  font-size: 12px;
  color: #424141;
  letter-spacing: 0.3px;
}
/* line 166, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_description a, .section_content .services_individual_hotel_wrapper .service_description a {
  color: #424141;
  font-weight: 300;
}
/* line 170, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_description a.pdf, .section_content .services_individual_hotel_wrapper .service_description a.pdf {
  padding-right: 0;
}
/* line 173, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_description a.pdf:after, .section_content .services_individual_hotel_wrapper .service_description a.pdf:after {
  content: '\f019';
  font-family: "Font Awesome 5 Pro";
  font-weight: 900;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  font-size: 18px;
  margin-left: 10px;
  vertical-align: middle;
  color: #2BB0A3;
}
/* line 192, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .service_description hide, .section_content .services_individual_hotel_wrapper .service_description hide {
  display: none;
}
/* line 197, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .see_all_wrapper, .section_content .services_individual_hotel_wrapper .see_all_wrapper {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #bbbbbb;
  padding: 2% 10%;
  text-transform: uppercase;
  color: white;
  font-weight: bold;
}
/* line 208, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .see_all_wrapper i, .section_content .services_individual_hotel_wrapper .see_all_wrapper i {
  float: right;
  margin-top: 3px;
}
/* line 214, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .see_more_icon, .section_content .services_individual_hotel_wrapper .see_more_icon {
  display: none;
  margin-left: 10px;
  background: #2BB0A3;
  width: 20px;
  height: 20px;
  vertical-align: top;
  border-radius: 20px;
  position: relative;
}
/* line 224, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .services_individual_hotel_wrapper .see_more_icon i, .section_content .services_individual_hotel_wrapper .see_more_icon i {
  color: white;
  position: absolute;
  left: 0;
  top: 0;
  font-size: 13px;
  text-align: center;
  margin-top: 4px;
  margin-left: 5px;
  line-height: 13px;
}
/* line 240, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .owl-stage-outer, .section_content .individual_gallery_wrapper .individual_carousel .owl-stage-outer {
  background-color: #f4f4f4;
  border-top: 30px solid #f4f4f4;
  max-height: 200px;
}
/* line 246, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .owl-dots, .section_content .individual_gallery_wrapper .individual_carousel .owl-dots {
  position: relative;
  width: 100%;
  padding: 0 20px;
  display: flex;
  margin-top: 30px;
}
/* line 253, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .owl-dots:before, .section_content .individual_gallery_wrapper .individual_carousel .owl-dots:before {
  content: '';
  width: calc(100% - 40px);
  height: 1px;
  position: absolute;
  top: 0;
  left: 20px;
  right: 20px;
  background-color: #2BB0A3;
}
/* line 264, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .owl-dots .owl-dot, .section_content .individual_gallery_wrapper .individual_carousel .owl-dots .owl-dot {
  flex-flow: row nowrap;
  height: 6px;
  width: 100%;
  margin-top: -3px;
  -webkit-transition: background-color 0.4s;
  -moz-transition: background-color 0.4s;
  -ms-transition: background-color 0.4s;
  -o-transition: background-color 0.4s;
  transition: background-color 0.4s;
}
/* line 271, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .owl-dots .owl-dot.active, .section_content .individual_gallery_wrapper .individual_carousel .owl-dots .owl-dot.active {
  background-color: #2BB0A3;
}
/* line 277, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .gallery_element, .section_content .individual_gallery_wrapper .individual_carousel .gallery_element {
  padding-right: 55px;
  position: relative;
  overflow: hidden;
}
/* line 282, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .gallery_element iframe, .section_content .individual_gallery_wrapper .individual_carousel .gallery_element iframe {
  width: 100%;
  height: 100%;
}
/* line 287, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .gallery_element .vertical, .section_content .individual_gallery_wrapper .individual_carousel .gallery_element .vertical {
  margin-top: -50%;
}
/* line 291, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .gallery_element .preview_video, .section_content .individual_gallery_wrapper .individual_carousel .gallery_element .preview_video {
  position: absolute;
  top: 0;
  right: 14.5%;
  left: 0;
  bottom: 0;
  overflow: hidden;
  -webkit-transition: top 0.5s;
  -moz-transition: top 0.5s;
  -ms-transition: top 0.5s;
  -o-transition: top 0.5s;
  transition: top 0.5s;
}
/* line 304, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .gallery_element .preview_video.hide, .section_content .individual_gallery_wrapper .individual_carousel .gallery_element .preview_video.hide {
  top: -150%;
  bottom: 150%;
}
/* line 308, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .individual_gallery_wrapper .individual_carousel .gallery_element .preview_video .play_icon, .section_content .individual_gallery_wrapper .individual_carousel .gallery_element .preview_video .play_icon {
  position: absolute;
  top: 38px;
  left: 50%;
  color: white;
  font-size: 70px;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
}
/* line 325, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper, .section_content .rooms_hotel_wrapper {
  margin-top: 30px;
}
/* line 329, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .rooms_block_title .main_title, .section_content .rooms_hotel_wrapper .rooms_block_title .main_title {
  color: #2BB0A3;
}
/* line 334, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element, .section_content .rooms_hotel_wrapper .room_element {
  margin-top: 20px;
  position: relative;
}
/* line 338, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element:first-of-type, .section_content .rooms_hotel_wrapper .room_element:first-of-type {
  margin-top: 0;
}
/* line 342, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .occupancy_wrapper, .section_content .rooms_hotel_wrapper .room_element .occupancy_wrapper {
  position: absolute;
  z-index: 2;
  right: 30px;
  top: 30px;
  background: white;
  padding: 4px 11px;
  font-size: 14px;
  color: #424242;
}
/* line 352, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .occupancy_wrapper .label, .section_content .rooms_hotel_wrapper .room_element .occupancy_wrapper .label {
  margin-right: 5px;
}
/* line 356, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .occupancy_wrapper .adult, .section_content .rooms_hotel_wrapper .room_element .occupancy_wrapper .adult {
  font-size: 13px;
}
/* line 360, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .occupancy_wrapper i, .section_content .rooms_hotel_wrapper .room_element .occupancy_wrapper i {
  margin-right: 3px;
}
/* line 365, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery, .section_content .rooms_hotel_wrapper .room_element .room_gallery {
  position: relative;
  padding: 20px 20px 0;
}
/* line 369, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery:before, .section_content .rooms_hotel_wrapper .room_element .room_gallery:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: calc(50% - 10px);
  width: 100%;
  background-color: #f4f4f4;
}
/* line 381, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .room_picture, .section_content .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .room_picture {
  position: relative;
  overflow: hidden;
  height: 200px;
}
/* line 386, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .room_picture img, .section_content .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .room_picture img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
/* line 391, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .room_picture:before, .section_content .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .room_picture:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 2;
}
/* line 400, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .owl-nav .owl-prev, .individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .owl-nav .owl-next, .section_content .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .owl-nav .owl-prev, .section_content .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .owl-nav .owl-next {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  z-index: 10;
  color: white;
  font-size: 24px;
  left: 10px;
}
/* line 408, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .owl-nav .owl-next, .section_content .rooms_hotel_wrapper .room_element .room_gallery .room_carousel .owl-nav .owl-next {
  left: auto;
  right: 10px;
}
/* line 416, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content, .section_content .rooms_hotel_wrapper .room_element .room_content {
  margin: 0 20px;
  padding: 20px;
  border: 1px solid #d0d0d0;
  border-top: 0;
}
/* line 422, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_title, .section_content .rooms_hotel_wrapper .room_element .room_content .room_title {
  font-size: 19px;
  font-style: italic;
  color: #2BB0A3;
  text-align: left;
}
/* line 429, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper, .section_content .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper {
  display: inline-block;
  width: 100%;
  margin-top: 12px;
}
/* line 434, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper .room_service, .section_content .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper .room_service {
  display: inline-block;
  margin-right: 15px;
}
/* line 438, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper .room_service .service_icon, .section_content .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper .room_service .service_icon {
  display: inline-block;
  margin-right: 5px;
  color: #2BB0A3;
}
/* line 443, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper .room_service .service_icon img, .section_content .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper .room_service .service_icon img {
  vertical-align: middle;
}
/* line 448, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper .room_service .service_title, .section_content .rooms_hotel_wrapper .room_element .room_content .room_services_wrapper .room_service .service_title {
  display: inline-block;
  vertical-align: middle;
  text-transform: uppercase;
  font-size: 12px;
  color: gray;
}
/* line 458, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_description, .section_content .rooms_hotel_wrapper .room_element .room_content .room_description {
  text-align: left;
  font-size: 12px;
  margin-top: 10px;
  line-height: 20px;
  color: #424242;
  letter-spacing: 0.2px;
}
/* line 466, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .room_description div, .section_content .rooms_hotel_wrapper .room_element .room_content .room_description div {
  text-align: justify !important;
}
/* line 471, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .rooms_hotel_wrapper .room_element .room_content .button-promotion, .section_content .rooms_hotel_wrapper .room_element .room_content .button-promotion {
  background-color: #F7BB1E;
  color: white;
  width: 100%;
  height: 40px;
  text-align: center;
  font-weight: 600;
  font-size: 18px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  margin-top: 20px;
  text-transform: uppercase;
  font-family: "Open Sans", sans-serif;
}
/* line 491, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .location_hotel_wrapper, .section_content .location_hotel_wrapper {
  padding: 20px 0;
  margin-top: 20px;
}
/* line 495, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .location_hotel_wrapper .maps, .section_content .location_hotel_wrapper .maps {
  margin-bottom: 20px;
}
/* line 498, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .location_hotel_wrapper .maps iframe, .section_content .location_hotel_wrapper .maps iframe {
  width: 100%;
}
/* line 503, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .location_hotel_wrapper .address, .individual_hotel_section_wrapper .location_hotel_wrapper .phone, .individual_hotel_section_wrapper .location_hotel_wrapper .email, .individual_hotel_section_wrapper .location_hotel_wrapper .register, .section_content .location_hotel_wrapper .address, .section_content .location_hotel_wrapper .phone, .section_content .location_hotel_wrapper .email, .section_content .location_hotel_wrapper .register {
  display: inline-block;
  width: 100%;
  padding: 0 20px;
  margin-bottom: 10px;
}
/* line 509, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .location_hotel_wrapper .address .icon, .individual_hotel_section_wrapper .location_hotel_wrapper .phone .icon, .individual_hotel_section_wrapper .location_hotel_wrapper .email .icon, .individual_hotel_section_wrapper .location_hotel_wrapper .register .icon, .section_content .location_hotel_wrapper .address .icon, .section_content .location_hotel_wrapper .phone .icon, .section_content .location_hotel_wrapper .email .icon, .section_content .location_hotel_wrapper .register .icon {
  color: #2BB0A3;
  display: inline-block;
  font-size: 20px;
  vertical-align: top;
  margin-right: 20px;
  float: left;
  width: 20px;
}
/* line 519, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .location_hotel_wrapper .address .title, .individual_hotel_section_wrapper .location_hotel_wrapper .phone .title, .individual_hotel_section_wrapper .location_hotel_wrapper .email .title, .individual_hotel_section_wrapper .location_hotel_wrapper .register .title, .section_content .location_hotel_wrapper .address .title, .section_content .location_hotel_wrapper .phone .title, .section_content .location_hotel_wrapper .email .title, .section_content .location_hotel_wrapper .register .title {
  display: inline-block;
  color: #424242;
  float: left;
  letter-spacing: 0.2px;
  line-height: 24px;
  font-size: 14px;
}
/* line 529, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .location_hotel_wrapper .register, .section_content .location_hotel_wrapper .register {
  margin-bottom: 0;
}
/* line 534, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper, .section_content .trust_informative_wrapper {
  padding: 0;
  border-top: 1px solid #f4f4f4;
  border-bottom: 1px solid #f4f4f4;
  margin: 0 20px 20px;
  position: relative;
}
/* line 541, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block, .section_content .trust_informative_wrapper .left_block {
  width: 67%;
  display: inline-block;
  position: relative;
  vertical-align: top;
  padding: 7px 0;
}
/* line 548, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block .trust_small_element, .section_content .trust_informative_wrapper .left_block .trust_small_element {
  zoom: 0.6;
  position: relative;
  display: inline-block;
  margin: 20px 0;
}
/* line 554, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block .trust_small_element .badges i, .section_content .trust_informative_wrapper .left_block .trust_small_element .badges i {
  margin-right: 0 !important;
}
/* line 558, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block .trust_small_element .counter, .section_content .trust_informative_wrapper .left_block .trust_small_element .counter {
  display: none !important;
}
/* line 563, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block .partners_wrapper, .section_content .trust_informative_wrapper .left_block .partners_wrapper {
  width: 40%;
  display: inline-block;
  vertical-align: middle;
  margin-top: 2px;
  margin-left: 15px;
}
/* line 570, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block .partners_wrapper .partner_element, .section_content .trust_informative_wrapper .left_block .partners_wrapper .partner_element {
  width: 100%;
  display: block;
}
/* line 576, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block.full_width, .section_content .trust_informative_wrapper .left_block.full_width {
  width: 100%;
  text-align: center;
}
/* line 580, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block.full_width .partners_wrapper, .section_content .trust_informative_wrapper .left_block.full_width .partners_wrapper {
  width: auto;
}
/* line 583, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block.full_width .partners_wrapper .partner_element, .section_content .trust_informative_wrapper .left_block.full_width .partners_wrapper .partner_element {
  display: inline-block;
  width: auto;
  max-height: 75px;
  max-width: 100%;
  margin: 0 auto;
  vertical-align: middle;
}
/* line 591, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .left_block.full_width .partners_wrapper .partner_element:last-of-type, .section_content .trust_informative_wrapper .left_block.full_width .partners_wrapper .partner_element:last-of-type {
  margin-bottom: 0;
}
/* line 599, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .right_block, .section_content .trust_informative_wrapper .right_block {
  width: 30%;
  display: inline-block;
  vertical-align: top;
  border-left: 1px solid #f4f4f4;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
}
/* line 609, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .right_block .link_informative_paper, .section_content .trust_informative_wrapper .right_block .link_informative_paper {
  text-align: center;
  font-size: 14px;
  color: gray;
  width: 100%;
  display: block;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 623, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_section_wrapper .trust_informative_wrapper .right_block .link_informative_paper i, .section_content .trust_informative_wrapper .right_block .link_informative_paper i {
  display: block;
  margin-top: 5px;
  font-size: 18px;
  color: #2BB0A3;
}

/* line 636, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_services_wrapper.promotions_wrapper .services_individual_hotel_wrapper {
  padding: 3% 10%;
}
/* line 640, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_services_wrapper.promotions_wrapper .service_description {
  position: relative;
  top: auto;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}
/* line 646, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_services_wrapper.promotions_wrapper .individual_service_item {
  padding: 3% 0;
}
/* line 649, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_services_wrapper.promotions_wrapper .individual_service_item:last-of-type {
  border-bottom: 0;
  padding-bottom: 0;
}
/* line 654, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_services_wrapper.promotions_wrapper .individual_service_item:nth-last-of-type(2) {
  padding-bottom: 3%;
  border-bottom: 1px solid #d0d0d0;
}
/* line 660, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_services_wrapper.promotions_wrapper .promotion_description {
  display: none;
}
/* line 664, ../../sass/mobile/v2/_individual_hotels.scss */
.individual_hotel_services_wrapper.promotions_wrapper .see_more_icon {
  display: block !important;
  position: absolute;
  left: 100%;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

/*=== FAQS ====*/
/* line 674, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper {
  color: gray;
  margin-bottom: 30px;
}
/* line 678, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .general_title_block {
  margin-bottom: 20px;
}
/* line 681, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .general_title_block .main_title {
  color: #2BB0A3;
}
/* line 686, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper {
  margin: 0 20px;
  padding: 7px 0;
}
/* line 690, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .filters_button {
  margin: 0 0 10px;
  border: 1px solid #d0d0d0;
}
/* line 695, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element {
  margin-bottom: 10px;
  display: block;
}
/* line 700, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element.hide {
  max-height: 0;
  overflow: hidden;
  border: 0;
  margin: 0;
}
/* line 707, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element .question {
  padding: 14px 50px 14px 20px;
  position: relative;
  font-weight: bold;
  border: 1px solid #d0d0d0;
  font-size: 14px;
  color: #424242;
}
/* line 715, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element .question:after {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  content: "\f13a";
  color: #2074ca;
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
  transition: all 0.5s;
}
/* line 727, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element .question i {
  position: absolute;
  right: 20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
  color: #2BB0A3;
}
/* line 744, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element .question .less {
  opacity: 0;
}
/* line 750, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element.active .question:after {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
  top: 31%;
}
/* line 756, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element .answer {
  margin: 0 7px;
  padding: 0 18px;
  background: white;
  max-height: 0;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  font-size: 12px;
  letter-spacing: 0.2px;
  color: #424242;
  line-height: 20px;
}
/* line 772, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element .answer .faqs_link {
  display: block;
  text-align: center;
  text-transform: uppercase;
  font-size: 12px;
  margin-top: 15px;
  color: #2BB0A3;
  font-weight: 600;
  letter-spacing: 0.2px;
}
/* line 785, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element.active .answer {
  padding: 10px 18px;
  max-height: 500px;
}
/* line 790, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element.active .less {
  opacity: 1;
}
/* line 794, ../../sass/mobile/v2/_individual_hotels.scss */
.faqs_wrapper .questions_wrapper .question_element.active .more {
  opacity: 0;
}

/* line 1, ../../sass/mobile/v2/_hotels_section.scss */
.filters_button {
  margin: 0 30px;
  border: 1px solid #8e8c8c;
  padding: 15px;
  text-align: center;
  background: white;
  text-transform: uppercase;
  font-weight: bold;
  color: #424242;
  font-size: 12px;
  letter-spacing: 0.6px;
}
/* line 13, ../../sass/mobile/v2/_hotels_section.scss */
.filters_button i {
  color: #2BB0A3;
  margin-right: 10px;
  font-size: 20px;
  font-weight: 300;
  vertical-align: middle;
}

/* line 23, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_buttons_wrapper {
  display: table;
  width: 100%;
  margin: 30px 0;
  text-align: center;
  border-top: 1px solid #cacaca;
  border-bottom: 1px solid #cacaca;
}
/* line 31, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_buttons_wrapper .view_list, .hotels_buttons_wrapper .view_map {
  width: 50%;
  float: left;
  padding: 15px 0;
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 600;
  color: gray;
  letter-spacing: 0.6px;
}
/* line 41, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_buttons_wrapper .view_list i, .hotels_buttons_wrapper .view_map i {
  font-size: 20px;
  vertical-align: middle;
  margin-right: 9px;
}
/* line 47, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_buttons_wrapper .view_list.active, .hotels_buttons_wrapper .view_map.active {
  color: #2BB0A3;
  border-bottom: 3px solid #2BB0A3;
}

/* line 54, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_found {
  margin: 30px 20px 0;
  font-style: italic;
  font-size: 24px;
  line-height: 25px;
  color: #2BB0A3;
}

/* line 62, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper {
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  max-height: fit-content;
}
/* line 70, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper.hide {
  margin-left: -100%;
  margin-right: 100%;
  max-height: 80vh;
}
/* line 76, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element {
  margin: 0 20px 30px;
  border: 1px solid #cacaca;
}
/* line 80, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element.hide {
  display: none;
}
/* line 84, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_image_wrapper {
  height: 160px;
  position: relative;
  overflow: hidden;
}
/* line 89, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_image_wrapper .since_price {
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 2;
  background: white;
  padding: 8px 7px 9px;
  text-transform: uppercase;
  font-size: 19px;
  font-weight: bolder;
  line-height: 16px;
  color: #2BB0A3;
  text-align: center;
}
/* line 103, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_image_wrapper .since_price small {
  display: block;
  font-size: 11px;
  font-weight: 500;
  color: #4e4e4e;
  text-transform: uppercase;
}
/* line 112, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_image_wrapper .hotel_image {
  width: auto;
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  margin: auto;
  max-width: none;
  min-height: 100%;
  min-width: 100%;
}
/* line 126, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_info {
  margin: 20px;
  padding: 0 0 15px;
  border-bottom: 1px solid #d4d4d4;
}
/* line 131, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_info .hotel_destiny {
  margin: 0;
  text-transform: uppercase;
  color: #424242;
  font-size: 11px;
}
/* line 138, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_info .hotel_name {
  margin: 0;
  font-style: italic;
  margin-top: 5px;
  font-size: 21px;
  color: #2BB0A3;
}
/* line 145, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_info .hotel_name i {
  font-size: 13px;
  vertical-align: middle;
}
/* line 152, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls {
  margin: 15px;
  text-align: center;
}
/* line 156, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls .inline_button {
  width: auto;
  display: inline-block;
  text-align: center;
  font-size: 11px;
  text-decoration: none;
  color: #2BB0A3;
  text-transform: uppercase;
  font-weight: 500;
  margin-right: 10px;
}
/* line 167, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls .inline_button:last-of-type {
  margin-right: 0;
}
/* line 171, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls .inline_button i {
  font-size: 20px;
  vertical-align: middle;
  margin-right: 3px;
}
/* line 178, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls .hotel_link {
  text-transform: uppercase;
}
/* line 182, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls .booking_button {
  display: block;
  width: 100%;
  text-align: center;
  color: white;
  margin: 15px 0 10px;
  padding: 13px 0;
  font-weight: bold;
  text-transform: uppercase;
  background: #f6bb33;
}
/* line 194, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls .hotel_link {
  display: block;
  text-align: center;
  color: #2BB0A3;
  font-weight: bold;
  font-size: 12px;
}
/* line 203, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls.without_promotions .inline_button {
  font-size: 13px;
  width: 32%;
  margin-right: 0;
}
/* line 208, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_controls.without_promotions .inline_button i {
  font-size: 22px;
}
/* line 215, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_tags {
  margin-top: 10px;
  display: flex;
}
/* line 219, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_tags .tag_element {
  font-size: 9px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 300;
  color: white;
  background: #ccc;
  padding: 5px 10px;
  font-style: italic;
  width: 100%;
  white-space: nowrap;
}
/* line 232, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_wrapper .hotel_element .hotel_tags .tag_element:not(:first-of-type) {
  margin-left: 2px;
}

/* line 240, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper {
  position: relative;
  overflow: hidden;
}
/* line 244, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper #maps_wrapper {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  min-height: 80vh;
  z-index: 10;
  background: white;
}
/* line 260, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper #maps_wrapper.hide {
  top: 100%;
  bottom: -100%;
}
/* line 265, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper #maps_wrapper .map_carousel_hotels {
  margin: 0;
  margin-top: -20px;
  background: white;
  position: relative;
}
/* line 271, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper #maps_wrapper .map_carousel_hotels .owl-dots {
  margin-top: 10px;
}
/* line 276, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper #maps_wrapper .header_map {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.23) 0%, transparent 100%);
  z-index: 3;
}
/* line 284, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper #maps_wrapper .header_map #close_map {
  float: right;
  margin: 10px 20px;
  font-size: 30px;
  color: white;
}
/* line 292, ../../sass/mobile/v2/_hotels_section.scss */
.hotels_map_wrapper #maps_wrapper #map {
  width: 100%;
  height: calc(100% - 152px);
}

/* line 299, ../../sass/mobile/v2/_hotels_section.scss */
.popup_hotel_info, .popup_hotel_map, .popup_hotel_offers {
  display: none;
}

/* line 303, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner {
  position: fixed;
  top: 100%;
  left: 0;
  right: 0;
  bottom: -100%;
  background: white;
  z-index: 4;
  padding: 20px;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 318, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner.active {
  top: 0;
  bottom: 0;
}
/* line 323, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner #close_filters {
  position: absolute;
  right: 25px;
  top: 8px;
  font-size: 29px;
  color: #2BB0A3;
}
/* line 331, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .banner_title {
  text-transform: uppercase;
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #424242;
}
/* line 338, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .banner_title i {
  margin-right: 10px;
  font-size: 23px;
  font-weight: 100;
  color: #2BB0A3;
  vertical-align: middle;
}
/* line 347, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper {
  border: 1px solid #cacaca;
  max-height: 80vh;
  overflow: auto;
}
/* line 352, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_title {
  border-bottom: 1px solid #cacaca;
  padding: 15px;
  text-transform: uppercase;
  color: #424242;
  font-size: 14px;
  position: relative;
}
/* line 360, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_title .more, #filter_wrapper_banner .availabler_filters_wrapper .filter_title .less {
  position: absolute;
  right: 10px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  color: #2BB0A3;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 377, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_title .less {
  opacity: 0;
}
/* line 383, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .destiny_filters_wrapper .filter_title {
  border-top: 0;
}
/* line 389, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .offers_filters_wrapper .filter_title {
  border-bottom: 0;
}
/* line 394, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list {
  padding: 0 15px;
  max-height: 0;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 404, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list.active {
  padding: 8px 15px;
  max-height: 600px;
}
/* line 409, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list .option_element {
  color: gray;
  font-weight: bold;
  text-transform: uppercase;
  margin-bottom: 5px;
}
/* line 415, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list .option_element input {
  margin: 0;
  vertical-align: middle;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 15px;
  height: 15px;
  position: relative;
  border: 0;
}
/* line 431, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list .option_element input + label:before {
  content: '';
  top: 10px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  position: absolute;
  left: -26px;
  width: 16px;
  height: 16px;
  border-radius: 20px;
  border: 1px solid;
}
/* line 448, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list .option_element input:focus {
  outline: 0;
}
/* line 452, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list .option_element input:checked + label {
  color: #2BB0A3;
}
/* line 454, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list .option_element input:checked + label:after {
  content: '';
  top: 10px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  position: absolute;
  left: -22px;
  width: 10px;
  height: 10px;
  border-radius: 20px;
  background: #2BB0A3;
}
/* line 473, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .options_list .option_element label {
  margin-left: 10px;
  position: relative;
  font-size: 14px;
  line-height: 2;
}
/* line 485, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_block.active .filter_title .less {
  opacity: 1;
}
/* line 489, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_block.active .filter_title .more {
  opacity: 0;
}
/* line 494, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_block.active .options_list {
  padding: 8px 15px;
  max-height: 600px;
  background: #f4f4f4;
  box-shadow: inset 0px -1px 0 #cacaca;
}
/* line 502, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_block.active.offers_filters_wrapper .filter_title {
  border-bottom: 1px solid #cacaca;
}
/* line 506, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .availabler_filters_wrapper .filter_block.active.offers_filters_wrapper .options_list {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
/* line 516, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .filters_buttons_wrapper {
  margin-top: 20px;
}
/* line 519, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .filters_buttons_wrapper #clear_filters_button, #filter_wrapper_banner .filters_buttons_wrapper #apply_filters_button {
  display: inline-block;
  float: left;
  width: 48%;
  font-size: 11px;
  border: 1px solid #9b9b9b;
  text-align: center;
  padding: 10px 0;
  color: #424242;
  font-weight: bold;
  text-transform: uppercase;
}
/* line 532, ../../sass/mobile/v2/_hotels_section.scss */
#filter_wrapper_banner .filters_buttons_wrapper #apply_filters_button {
  float: right;
  color: white;
  border: 1px solid #2BB0A3;
  background: #2BB0A3;
}

/* line 542, ../../sass/mobile/v2/_hotels_section.scss */
.promotions_list_popup ul {
  list-style: circle;
}

/* line 1, ../../sass/mobile/v2/_contact_section.scss */
.general_title_block.contact_content {
  margin-top: 18px;
  text-align: center;
}
/* line 6, ../../sass/mobile/v2/_contact_section.scss */
.general_title_block.contact_content table td {
  display: block;
  width: 100%;
}

/* line 13, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper {
  margin: 0 20px;
  margin-bottom: 30px;
  border: 1px solid #d0d0d0;
  padding: 20px;
}
/* line 19, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contact_form_title {
  font-style: italic;
  font-size: 24px;
  line-height: 25px;
  color: #2BB0A3;
  margin-bottom: 25px;
}
/* line 27, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .separator_title {
  color: #2BB0A3;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 10px;
  padding-top: 10px;
  border-top: 1px solid #d0d0d0;
  margin-top: 10px;
}
/* line 38, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput {
  border: 1px solid #d0d0d0;
  margin-bottom: 10px;
  padding: 10px;
  position: relative;
}
/* line 44, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput input[type='radio'] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 16px;
  margin-left: 20px;
}
/* line 51, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput input[type='radio']:checked + label:after {
  content: '';
  top: 8px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  position: absolute;
  left: -22px;
  width: 10px;
  height: 10px;
  border-radius: 20px;
  background: #2BB0A3;
}
/* line 68, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput input[type='radio'] + label:before {
  content: '';
  top: 8px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  position: absolute;
  left: -26px;
  width: 16px;
  height: 16px;
  border-radius: 20px;
  border: 1px solid;
}
/* line 86, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput label {
  font-weight: bold;
  font-size: 12px;
  color: #424242;
  display: block;
  position: relative;
  text-align: left;
}
/* line 94, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput label.error {
  color: red;
  font-weight: lighter;
  font-size: 11px;
  position: absolute;
  bottom: 0;
  top: 100%;
}
/* line 104, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput input {
  width: 100%;
  padding: 10px 0;
  font-family: "Open Sans", sans-serif;
}
/* line 61, ../../../../../../../../.gem/ruby/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/css3/_user-interface.scss */
.contact_wrapper .contInput input:-moz-placeholder {
  font-style: italic;
  color: #9e9e9e;
}
/* line 64, ../../../../../../../../.gem/ruby/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/css3/_user-interface.scss */
.contact_wrapper .contInput input::-moz-placeholder {
  font-style: italic;
  color: #9e9e9e;
}
/* line 67, ../../../../../../../../.gem/ruby/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/css3/_user-interface.scss */
.contact_wrapper .contInput input:-ms-input-placeholder {
  font-style: italic;
  color: #9e9e9e;
}
/* line 56, ../../../../../../../../.gem/ruby/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/css3/_user-interface.scss */
.contact_wrapper .contInput input::-webkit-input-placeholder {
  font-style: italic;
  color: #9e9e9e;
}
/* line 114, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput input[type='radio'] {
  display: inline-table;
  width: auto;
  margin-right: 5px;
}
/* line 119, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput input[type='radio'] + label {
  display: inline-block;
  margin-right: 40px;
}
/* line 126, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .contInput textarea {
  display: block;
  width: 100%;
}
/* line 132, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .recaptcha_container {
  margin-bottom: 10px;
}
/* line 136, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .legend_label {
  display: block;
  font-weight: lighter;
  font-size: 11px;
  color: #424242;
  font-style: italic;
  margin-top: 10px;
  text-align: right;
}
/* line 146, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper a.myFancyPopup {
  font-weight: bold;
  font-size: 12px;
  color: #424242;
  display: inline-block;
  text-decoration: underline;
}
/* line 154, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper #contact-button {
  background-color: #2BB0A3;
  color: white;
  border: 0;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  font-family: "Open Sans", sans-serif;
  text-align: center;
  padding: 10px 0;
  margin-bottom: 15px;
}
/* line 167, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .check_privacy {
  background-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 1px solid gray;
  width: 15px;
  height: 15px;
  float: left;
  margin-right: 12px;
  position: relative;
  vertical-align: middle;
  margin-top: 6px;
}
/* line 181, ../../sass/mobile/v2/_contact_section.scss */
.contact_wrapper .check_privacy:checked:before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background: #2BB0A3;
}

/* line 196, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .privacy_wrapper {
  display: block;
  width: 100%;
  clear: both;
}
/* line 201, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .privacy_wrapper input[type='checkbox'] {
  vertical-align: middle;
}
/* line 206, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .option_block {
  display: table;
  width: 100%;
  margin-bottom: 20px;
}
/* line 211, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .option_block:last-of-type {
  margin-bottom: 0;
}
/* line 215, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .option_block label {
  width: 55%;
  float: left;
  margin-top: 3px;
}
/* line 221, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .option_block .controlls {
  width: 25%;
  float: right;
}
/* line 225, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .option_block .controlls .less, .groups_contact .option_block .controlls input, .groups_contact .option_block .controlls .plus {
  display: inline-block;
  vertical-align: middle;
  color: #2BB0A3;
}
/* line 230, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .option_block .controlls .less.disabled, .groups_contact .option_block .controlls input.disabled, .groups_contact .option_block .controlls .plus.disabled {
  opacity: 0.4;
}
/* line 235, ../../sass/mobile/v2/_contact_section.scss */
.groups_contact .option_block .controlls input {
  width: 20px;
  text-align: center;
  padding: 0;
  vertical-align: middle;
  font-weight: bold;
  font-size: 15px;
}

/* line 1, ../../sass/mobile/v2/_advance_configs.scss */
.top_ticks_icons_wrapper {
  margin: 0 20px 30px;
  background: #f4f4f4;
  padding: 16px 0;
}
/* line 6, ../../sass/mobile/v2/_advance_configs.scss */
.top_ticks_icons_wrapper .top_tick_element {
  width: 32%;
  display: inline-block;
  text-align: center;
  font-size: 9px;
  text-transform: uppercase;
  padding: 0 20px;
  position: relative;
}
/* line 15, ../../sass/mobile/v2/_advance_configs.scss */
.top_ticks_icons_wrapper .top_tick_element:after {
  content: '';
  width: 1px;
  height: 50px;
  display: block;
  background: gray;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 32, ../../sass/mobile/v2/_advance_configs.scss */
.top_ticks_icons_wrapper .top_tick_element:last-of-type:after {
  display: none;
}
/* line 37, ../../sass/mobile/v2/_advance_configs.scss */
.top_ticks_icons_wrapper .top_tick_element .tick_image_element {
  max-height: 20px;
  margin-bottom: 5px;
}

/* line 44, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info {
  margin-bottom: 30px;
}
/* line 47, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info .deploy_banner_block {
  margin: 0 20px 0;
}
/* line 50, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info .deploy_banner_block .deploy_element {
  border-bottom: 2px solid white;
  margin-bottom: 10px;
}
/* line 54, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info .deploy_banner_block .deploy_element .deploy_title {
  padding: 14px 50px 14px 20px;
  position: relative;
  font-weight: bold;
  border: 1px solid #d0d0d0;
  font-size: 14px;
  color: #424242;
}
/* line 62, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info .deploy_banner_block .deploy_element .deploy_title:after {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  content: "\f13a";
  color: #2074ca;
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
  transition: all 0.5s;
}
/* line 75, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info .deploy_banner_block .deploy_element .deploy_description {
  padding: 0 20px;
  font-size: 13px;
  color: gray;
  max-height: 0;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 89, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info .deploy_banner_block .deploy_element.active .deploy_title:after {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
  top: 31%;
}
/* line 94, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info .deploy_banner_block .deploy_element.active .deploy_description {
  max-height: 1000px;
  padding: 20px;
}
/* line 102, ../../sass/mobile/v2/_advance_configs.scss */
.deploy_blocks_info #popup_best_price_link {
  text-align: center;
  text-transform: uppercase;
  font-size: 12px;
  margin-top: 15px;
  color: #2BB0A3;
  font-weight: 600;
  letter-spacing: 0.2px;
}

/* line 113, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price {
  position: fixed;
  top: 100%;
  left: 0;
  bottom: -100%;
  right: 0;
  background: white;
  z-index: 4;
  padding: 0;
  overflow: scroll;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 129, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .popup_best_price_wrapper {
  padding: 20px;
}
/* line 133, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price.active {
  top: 0;
  bottom: 0;
}
/* line 138, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price #close_best_price {
  position: absolute;
  right: 25px;
  top: 8px;
  font-size: 29px;
  color: #002578;
}
/* line 146, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .hidden {
  display: none;
}
/* line 150, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .popup_title {
  text-transform: uppercase;
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #424242;
}
/* line 157, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .popup_title i {
  margin-right: 10px;
  color: #317abe;
  font-size: 23px;
  vertical-align: middle;
  font-weight: 100;
}
/* line 166, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price #best-price-contact-form {
  max-height: 90vh;
}
/* line 170, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price h4 {
  text-transform: uppercase;
  border-bottom: 1px solid lightgray;
  padding-bottom: 10px;
  margin-bottom: 15px;
  font-size: 14px;
  color: #424242;
  letter-spacing: 0.2px;
}
/* line 180, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block {
  border: 1px solid lightgray;
  padding: 10px;
  margin-bottom: 10px;
  display: table;
  width: 100%;
}
/* line 187, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block label, #popup_best_price .form_block .question_label {
  display: block;
  font-weight: bold;
  color: #424242;
  font-size: 14px;
  margin-left: 5px;
}
/* line 195, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block input {
  width: 100%;
  padding: 6px 0;
  font-style: italic;
}
/* line 16, ../../sass/mobile/styles_mobile_test11.scss */
#popup_best_price .form_block input::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #b6b6b6;
  opacity: 1;
  /* Firefox */
}
/* line 22, ../../sass/mobile/styles_mobile_test11.scss */
#popup_best_price .form_block input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #b6b6b6;
}
/* line 27, ../../sass/mobile/styles_mobile_test11.scss */
#popup_best_price .form_block input::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #b6b6b6;
}
/* line 61, ../../../../../../../../.gem/ruby/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/css3/_user-interface.scss */
#popup_best_price .form_block input:-moz-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 64, ../../../../../../../../.gem/ruby/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/css3/_user-interface.scss */
#popup_best_price .form_block input::-moz-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 67, ../../../../../../../../.gem/ruby/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/css3/_user-interface.scss */
#popup_best_price .form_block input:-ms-input-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 56, ../../../../../../../../.gem/ruby/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/css3/_user-interface.scss */
#popup_best_price .form_block input::-webkit-input-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 206, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block textarea {
  display: block;
  width: 100%;
}
/* line 211, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block + h4 {
  margin-top: 25px;
}
/* line 215, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block select {
  width: 100%;
  padding: 7px 0 0;
  color: #b6b6b6;
  font-style: italic;
}
/* line 222, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block .radio_block_x2 {
  display: inline-block;
  width: 50%;
  float: left;
  margin-top: 10px;
}
/* line 228, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block .radio_block_x2 input, #popup_best_price .form_block .radio_block_x2 label {
  display: inline-block;
  width: auto;
  position: relative;
}
/* line 234, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block .radio_block_x2 input[type='radio'] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 16px;
}
/* line 240, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block .radio_block_x2 input[type='radio'] + label:before {
  content: '';
  top: 11px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  position: absolute;
  left: -26px;
  width: 16px;
  height: 16px;
  border-radius: 20px;
  border: 1px solid;
}
/* line 256, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block .radio_block_x2 input[type='radio']:focus {
  outline: 0;
}
/* line 260, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block .radio_block_x2 input[type='radio']:checked + label {
  color: #2BB0A3;
}
/* line 263, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block .radio_block_x2 input[type='radio']:checked + label:after {
  content: '';
  top: 11px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  position: absolute;
  left: -22px;
  width: 10px;
  height: 10px;
  border-radius: 20px;
  background: #2BB0A3;
}
/* line 282, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .form_block #upload_photo_button {
  display: table;
  margin: 15px auto 10px;
  padding: 13px 35px;
  border: 1px solid gray;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 12px;
  color: #424242;
  letter-spacing: 0.2px;
}
/* line 295, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price #submit_button {
  width: 100%;
  background: #2BB0A3;
  color: white;
  text-align: center;
  padding: 15px 0;
  text-transform: uppercase;
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 10px;
}
/* line 307, ../../sass/mobile/v2/_advance_configs.scss */
#popup_best_price .small_info {
  color: #c7c7c7;
  font-style: italic;
  font-weight: lighter;
  text-align: center;
  display: block;
  font-size: 12px;
  margin-bottom: 20px;
}

/* line 319, ../../sass/mobile/v2/_advance_configs.scss */
.carousel_wrapper .since_wrapper {
  color: #2BB0A3;
}

/* line 325, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper .tick_element.first {
  margin-bottom: 15px;
  padding-bottom: 15px;
}
/* line 330, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper .tick_icon {
  color: #2BB0A3 !important;
}
/* line 334, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_wrapper .external_advantage_link {
  color: #2BB0A3;
}

/* line 342, ../../sass/mobile/v2/_advance_configs.scss */
.carousel_wrapper .general_title_block hide {
  display: none;
}

/* line 348, ../../sass/mobile/v2/_advance_configs.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .content_wrapper .tags_wrapper .tag_element {
  padding: 0 7px;
}

/* line 353, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper {
  margin: 0 20px;
}
/* line 356, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block {
  margin-bottom: 50px;
}
/* line 359, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block .category_title {
  font-weight: bold;
  color: #2BB0A3;
  margin-bottom: 30px;
}
/* line 365, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block .collapse_block {
  margin-bottom: 10px;
}
/* line 368, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block .collapse_block .collapse_title {
  padding: 14px 50px 14px 20px;
  position: relative;
  font-weight: bold;
  border: 1px solid #d0d0d0;
  font-size: 14px;
  color: #424242;
}
/* line 376, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block .collapse_block .collapse_title:after {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  content: "\f13a";
  color: #2BB0A3;
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
  transition: all 0.5s;
}
/* line 389, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block .collapse_block .element_description {
  padding: 0 18px;
  background: white;
  max-height: 0;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  font-size: 12px;
  letter-spacing: 0.2px;
  color: #424242;
  line-height: 20px;
  margin: 0 20px;
}
/* line 407, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block .collapse_block.show .collapse_title:after {
  top: 26%;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
/* line 413, ../../sass/mobile/v2/_advance_configs.scss */
.collapse_wrapper .category_block .collapse_block.show .collapse_description .element_description {
  max-height: 300px;
  margin: 20px;
  color: #646464;
}

/* line 426, ../../sass/mobile/v2/_advance_configs.scss */
.advantages_banner_title .tick_text {
  color: #2d2d2d;
}

/* line 432, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper {
  margin: 0 20px;
  display: block;
}
/* line 436, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element {
  margin-bottom: 30px;
}
/* line 439, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .image_wrapper {
  width: 100%;
  overflow: hidden;
  height: 170px;
  position: relative;
}
/* line 445, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .image_wrapper .image_element {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  margin: auto;
  min-width: 100%;
  min-height: 100%;
  width: 100%;
}
/* line 458, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .cycle_content {
  border: 1px solid #cacaca;
  border-top: 0;
  padding: 20px;
}
/* line 463, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .cycle_content .cycle_title {
  font-style: italic;
  font-size: 21px;
  color: #2BB0A3;
  margin-bottom: 20px;
  border-bottom: 1px solid #cacaca;
  padding-bottom: 10px;
}
/* line 472, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .cycle_content .cycle_description {
  font-size: 12px;
  color: #424242;
  line-height: 20px;
  letter-spacing: 0.2px;
  overflow: hidden;
  transition: max-height 0.5s;
}
/* line 482, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element .see_more_button {
  font-style: italic;
  font-size: 14px;
  color: #2BB0A3;
  margin-top: 20px;
  display: none;
  text-align: right;
}
/* line 492, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element.exceded_height .cycle_description {
  max-height: 440px;
}
/* line 496, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element.exceded_height .see_more_button {
  display: block;
}
/* line 502, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element.deploy .cycle_description {
  max-height: 3000px;
}
/* line 506, ../../sass/mobile/v2/_advance_configs.scss */
.cycle_banners_wrapper .cycle_element.deploy .see_more_button {
  display: none;
}

/* line 516, ../../sass/mobile/v2/_advance_configs.scss */
.carousel_wrapper .promotion_element .main_title {
  height: auto !important;
}
/* line 520, ../../sass/mobile/v2/_advance_configs.scss */
.carousel_wrapper .promotion_element .description {
  color: #424242;
  font-size: 12px;
  letter-spacing: 0.3px;
  max-height: 20px;
  overflow: hidden;
}
/* line 528, ../../sass/mobile/v2/_advance_configs.scss */
.carousel_wrapper .promotion_element .content_wrapper {
  height: 135px;
  position: relative;
}
/* line 532, ../../sass/mobile/v2/_advance_configs.scss */
.carousel_wrapper .promotion_element .content_wrapper .see_hotel_link {
  position: absolute;
  bottom: 15px;
  left: 10px;
  right: 10px;
  width: auto !important;
}

/* line 545, ../../sass/mobile/v2/_advance_configs.scss */
.missing_links {
  margin-bottom: 40px;
}
/* line 548, ../../sass/mobile/v2/_advance_configs.scss */
.missing_links .link_element {
  color: white;
  background: #2BB0A3;
  display: block;
  text-align: center;
  width: 70%;
  padding: 10px 20px;
  margin: 0 auto 20px;
}

/* line 561, ../../sass/mobile/v2/_advance_configs.scss */
.hotel_review_selector {
  width: 90%;
  display: block;
  margin: auto;
  position: relative;
}
/* line 567, ../../sass/mobile/v2/_advance_configs.scss */
.hotel_review_selector:after {
  position: absolute;
  right: 20px;
  top: 35%;
  transform: translateY(-50%);
  content: "\f13a";
  color: #2074ca;
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
  transition: all 0.5s;
  font-size: 21px;
}
/* line 580, ../../sass/mobile/v2/_advance_configs.scss */
.hotel_review_selector select {
  width: 100%;
  margin: auto;
  display: block;
  -webkit-appearance: none;
  background: white;
  border: 0;
  border-bottom: 1px solid;
  text-align: center;
  padding-bottom: 10px;
  color: #2074ca;
  font-size: 16px;
}
/* line 593, ../../sass/mobile/v2/_advance_configs.scss */
.hotel_review_selector select option {
  text-align: center;
}

/* line 599, ../../sass/mobile/v2/_advance_configs.scss */
.trust_wrapper_element {
  padding: 0 5%;
}

/* line 3, ../../sass/mobile/v2/_general_styles.scss */
.big_title_corporate {
  color: #2BB0A3;
  font-style: italic;
  letter-spacing: 1px;
}

/* line 9, ../../sass/mobile/v2/_general_styles.scss */
.waves {
  width: 80px;
  height: 30px;
  margin: 7px 0;
  background: url("https://cdn2.paraty.es/test11/images/5d80d87611dd0dc") no-repeat center;
  background-size: contain;
}

/* line 17, ../../sass/mobile/v2/_general_styles.scss */
.corporate_button1 {
  font-size: 13px;
  color: white;
  background: #2BB0A3;
  display: table;
  padding: 5px 15px;
}

/* line 25, ../../sass/mobile/v2/_general_styles.scss */
#popup_website_v3 .popup_content {
  font-size: 13px;
  max-height: 80vh;
  overflow: auto;
}
/* line 30, ../../sass/mobile/v2/_general_styles.scss */
#popup_website_v3 .popup_content .popup_hotel_info_title {
  font-size: 18px;
  color: #2BB0A3;
  font-weight: bold;
  font-style: italic;
  margin-bottom: 16px;
}

/* line 39, ../../sass/mobile/v2/_general_styles.scss */
.slider_inner_section {
  height: 75px;
  overflow: hidden;
  position: relative;
}
/* line 44, ../../sass/mobile/v2/_general_styles.scss */
.slider_inner_section img {
  position: absolute;
  top: -50%;
  bottom: -50%;
  left: 0;
  right: 0;
  margin: auto;
}

/* line 54, ../../sass/mobile/v2/_general_styles.scss */
a {
  color: #2BB0A3;
  font-weight: bold;
}

/* line 59, ../../sass/mobile/v2/_general_styles.scss */
#popup_website_v3 {
  background: transparent;
}
/* line 64, ../../sass/mobile/v2/_general_styles.scss */
#popup_website_v3 .popup_content .promotions_list_popup .title_promotion {
  font-size: 16px;
  margin-bottom: 8px;
  display: grid;
  grid-template-columns: auto 20px;
  align-items: center;
}
/* line 71, ../../sass/mobile/v2/_general_styles.scss */
#popup_website_v3 .popup_content .promotions_list_popup .title_promotion i {
  color: #2BB0A3;
}
/* line 76, ../../sass/mobile/v2/_general_styles.scss */
#popup_website_v3 .popup_content .promotions_list_popup .description_promotion {
  display: none;
  margin-bottom: 8px;
  margin-left: 8px;
}

/* line 85, ../../sass/mobile/v2/_general_styles.scss */
#popup_v3_overlay {
  background: rgba(255, 255, 255, 0.97);
}

/* line 89, ../../sass/mobile/v2/_general_styles.scss */
.banners_carousel_wrapper .carousel_wrapper .owl-item .banner_element .image_wrapper .image_element {
  max-height: 100%;
}

/* line 93, ../../sass/mobile/v2/_general_styles.scss */
.corporate_icon {
  margin-right: 5px;
  color: #2BB0A3;
}

/* line 100, ../../sass/mobile/v2/_general_styles.scss */
.map_iframe_fancybox .fancybox-stage .fancybox-slide {
  padding-left: 0;
  padding-right: 0;
}

/* line 1, ../../sass/mobile/v2/_my_reservation.scss */
.my_reservation_section {
  margin-top: 18px;
  text-align: center;
}

/* line 7, ../../sass/mobile/v2/_my_reservation.scss */
.general_title_block.my_bookings {
  margin-bottom: 15px;
}
/* line 10, ../../sass/mobile/v2/_my_reservation.scss */
.general_title_block + .my_reservation_form {
  margin-top: 0;
}

/* line 15, ../../sass/mobile/v2/_my_reservation.scss */
.my-reservation-form {
  margin: 20px;
  border: 1px solid #d0d0d0;
  padding: 13px;
}
/* line 20, ../../sass/mobile/v2/_my_reservation.scss */
.my-reservation-form .form_block {
  border: 1px solid lightgray;
  padding: 10px;
  margin-bottom: 10px;
  display: table;
  width: 100%;
}
/* line 27, ../../sass/mobile/v2/_my_reservation.scss */
.my-reservation-form .form_block label {
  display: block;
  font-weight: bold;
  color: #424242;
  font-size: 14px;
  margin-left: 5px;
  text-align: left;
}
/* line 36, ../../sass/mobile/v2/_my_reservation.scss */
.my-reservation-form .form_block input {
  width: 100%;
  padding: 6px 0;
  font-style: italic;
}
/* line 16, ../../sass/mobile/styles_mobile_test11.scss */
.my-reservation-form .form_block input::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #b6b6b6;
  opacity: 1;
  /* Firefox */
}
/* line 22, ../../sass/mobile/styles_mobile_test11.scss */
.my-reservation-form .form_block input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #b6b6b6;
}
/* line 27, ../../sass/mobile/styles_mobile_test11.scss */
.my-reservation-form .form_block input::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #b6b6b6;
}
/* line 61, ../../../../../../../../.gem/ruby/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/css3/_user-interface.scss */
.my-reservation-form .form_block input:-moz-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 64, ../../../../../../../../.gem/ruby/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/css3/_user-interface.scss */
.my-reservation-form .form_block input::-moz-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 67, ../../../../../../../../.gem/ruby/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/css3/_user-interface.scss */
.my-reservation-form .form_block input:-ms-input-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 56, ../../../../../../../../.gem/ruby/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/css3/_user-interface.scss */
.my-reservation-form .form_block input::-webkit-input-placeholder {
  font-size: 12px;
  font-style: italic;
}
/* line 48, ../../sass/mobile/v2/_my_reservation.scss */
.my-reservation-form a[data-role='button'] {
  background: #2BB0A3;
  color: white;
  float: right;
  padding: 10px 30px;
  font-size: 14px;
  text-transform: uppercase;
}

/* line 1, ../../sass/mobile/v2/_work_blocks_section.scss */
.job_offers_found {
  display: none;
  margin: 30px 20px;
  font-style: italic;
  font-size: 22px;
  line-height: 28px;
  color: #2BB0A3;
}

/* line 10, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper {
  display: none;
  margin: 0 20px;
}
/* line 14, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element {
  border: 1px solid #cacaca;
  margin-bottom: 20px;
  padding: 20px;
  display: table;
}
/* line 20, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element.hide {
  display: none;
}
/* line 24, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .destiny_title {
  text-transform: uppercase;
  color: #424242;
  font-size: 12px;
  letter-spacing: 0.3px;
  font-weight: 600;
}
/* line 32, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .job_title {
  margin: 0;
  font-style: italic;
  font-size: 24px;
  line-height: 25px;
  color: #2BB0A3;
  margin-bottom: 5px;
}
/* line 41, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .job_description {
  font-size: 12px;
  color: #424242;
  margin-top: 20px;
  line-height: 20px;
  letter-spacing: 0.2px;
}
/* line 49, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .job_header {
  padding-bottom: 20px;
  border-bottom: 1px solid #cacaca;
}
/* line 54, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .subscribe_job {
  display: block;
  width: 100%;
  text-align: center;
  color: white;
  margin: 15px 0 10px;
  padding: 13px 0;
  font-weight: bold;
  text-transform: uppercase;
  background: #2BB0A3;
  font-family: "Open Sans", sans-serif;
  border: 0;
  font-size: 16px;
}
/* line 69, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .hotels_wrapper {
  font-size: 11px;
  color: #2BB0A3;
}
/* line 73, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .hotels_wrapper .hotel_name {
  margin-right: 10px;
}
/* line 78, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .more_info_job_wrapper {
  display: none;
}
/* line 82, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .job_element .see_more_job_description {
  display: block;
  text-align: center;
  color: #2BB0A3;
  font-weight: bold;
  font-size: 12px;
}
/* line 91, ../../sass/mobile/v2/_work_blocks_section.scss */
.work_wrapper .work_filters_button {
  width: 100%;
  margin: 0;
  margin-bottom: 20px;
  border: 1px solid #cacaca;
}

/* line 100, ../../sass/mobile/v2/_work_blocks_section.scss */
.laboral_offer_type .filter_title {
  border-bottom: 0 !important;
}

/* line 1, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info {
  padding: 0 20px 20px;
}
/* line 3, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block {
  border: 1px solid grey;
}
/* line 5, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block .step_title {
  padding: 10px 20px;
  border-bottom: 1px solid grey;
  font-size: 18px;
  font-weight: 700;
  color: grey;
}
/* line 12, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block .steps_follow {
  padding: 10px;
}
/* line 14, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block .steps_follow .step_number {
  position: relative;
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: top;
  margin-right: 15px;
  border-radius: 50%;
  background-color: #2BB0A3;
  color: white;
  font-size: 14px;
  line-height: 1;
  font-weight: 700;
}
/* line 27, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block .steps_follow .step_number .number {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 31, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block .steps_follow .step_description {
  font-size: 12px;
  color: #424242;
  line-height: 20px;
  letter-spacing: 0.2px;
  display: inline-block;
  vertical-align: top;
  margin-bottom: 15px;
  width: calc(100% - 45px);
}
/* line 41, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block .steps_follow .image_club {
  display: block;
  max-width: 80%;
  margin: 10px auto;
}
/* line 46, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .steps_block .steps_follow .conditions_step {
  font-size: 12px;
  color: #424242;
  line-height: 20px;
  letter-spacing: 0.2px;
  display: block;
}
/* line 56, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .advatages_block {
  margin-top: 20px;
  border: 1px solid grey;
}
/* line 59, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .advatages_block .advantages_title {
  padding: 10px 20px;
  border-bottom: 1px solid grey;
  font-size: 18px;
  font-weight: 700;
  color: grey;
}
/* line 66, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .advatages_block .advantages_description {
  font-size: 12px;
  color: #424242;
  line-height: 20px;
  letter-spacing: 0.2px;
  padding: 10px;
}
/* line 72, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .advatages_block .advantages_description ul {
  list-style: none;
}
/* line 74, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .advatages_block .advantages_description ul li {
  position: relative;
  margin-bottom: 15px;
  display: block;
  padding-left: 40px;
}
/* line 79, ../../sass/mobile/v2/_club_user.scss */
.users_registered_info .advatages_block .advantages_description ul li:before {
  content: '\f00c';
  font-family: "Font Awesome 5 Pro";
  font-weight: 700;
  display: inline-block;
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #2BB0A3;
  color: #2BB0A3;
  font-size: 13px;
  padding: 1px 2px;
  box-sizing: border-box;
}

/* line 104, ../../sass/mobile/v2/_club_user.scss */
.form_suscribe_wrapper .checkbox_wrapper .referal_label {
  font-weight: bold;
  font-size: 12px;
  color: #424242;
  display: block;
  margin-bottom: 5px;
}
/* line 111, ../../sass/mobile/v2/_club_user.scss */
.form_suscribe_wrapper .checkbox_wrapper input {
  width: auto;
  display: inline-block;
  vertical-align: middle;
  margin: 8px 5px;
}
/* line 118, ../../sass/mobile/v2/_club_user.scss */
.form_suscribe_wrapper #contact-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: block;
  width: 100%;
}
/* line 125, ../../sass/mobile/v2/_club_user.scss */
.form_suscribe_wrapper .privacy_wrapper {
  font-weight: bold;
  font-size: 12px;
  color: #424242;
  display: inline-block;
}

/* line 133, ../../sass/mobile/v2/_club_user.scss */
.user_register_conditions {
  padding: 20px;
  font-size: 12px;
  color: #424242;
  line-height: 20px;
  letter-spacing: 0.2px;
}

/* line 48, ../../sass/mobile/styles_mobile_test11.scss */
.general_title_block .main_title {
  color: #2BB0A3;
}

/* line 53, ../../sass/mobile/styles_mobile_test11.scss */
.slider_inner_section {
  width: 100%;
}
/* line 56, ../../sass/mobile/styles_mobile_test11.scss */
.slider_inner_section img {
  width: 100%;
}

/* line 61, ../../sass/mobile/styles_mobile_test11.scss */
body {
  font-family: "Open Sans", sans-serif;
  padding-bottom: 52px;
}
/* line 64, ../../sass/mobile/styles_mobile_test11.scss */
body header {
  padding: 0;
  z-index: 11;
}
/* line 67, ../../sass/mobile/styles_mobile_test11.scss */
body header .menu_bar {
  transform: translateY(50%);
}

/* line 74, ../../sass/mobile/styles_mobile_test11.scss */
.popup_inicio img {
  width: 100%;
}

/* line 80, ../../sass/mobile/styles_mobile_test11.scss */
.default_content_wrapper .section_content {
  text-align: center;
  margin: 40px auto 30px;
}
/* line 85, ../../sass/mobile/styles_mobile_test11.scss */
.default_content_wrapper .section_content h1 .mini_title {
  margin: 0;
  text-transform: uppercase;
  color: #424242;
  font-size: 12px;
  letter-spacing: 0.3px;
  display: block;
}
/* line 94, ../../sass/mobile/styles_mobile_test11.scss */
.default_content_wrapper .section_content h1 .main_title {
  margin: 0;
  display: block;
  font-style: italic;
  font-size: 24px;
  line-height: 25px;
  color: #2BB0A3;
}
/* line 104, ../../sass/mobile/styles_mobile_test11.scss */
.default_content_wrapper .section_content .content {
  font-size: 12px;
  color: #424242;
  margin: 20px;
  line-height: 20px;
  letter-spacing: 0.2px;
}

/* line 116, ../../sass/mobile/styles_mobile_test11.scss */
.banners_block_bottom .general_title_block .mini_title {
  font-style: italic;
  font-size: 24px;
  text-transform: none;
  color: #2BB0A3;
}

/* line 128, ../../sass/mobile/styles_mobile_test11.scss */
.gallery_1 .crop a i {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 10;
  color: white;
  border: 1px solid white;
  border-radius: 50%;
  width: 28px;
  height: 28px;
}
/* line 139, ../../sass/mobile/styles_mobile_test11.scss */
.gallery_1 .crop a i::before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 16px;
}

/* line 156, ../../sass/mobile/styles_mobile_test11.scss */
#entry_date_popup .dates_button.active {
  color: #2BB0A3;
}
/* line 159, ../../sass/mobile/styles_mobile_test11.scss */
#entry_date_popup .dates_button.active::after {
  background-color: #2BB0A3 !important;
}
/* line 164, ../../sass/mobile/styles_mobile_test11.scss */
#entry_date_popup .ui-datepicker-title {
  color: #2BB0A3;
}
/* line 168, ../../sass/mobile/styles_mobile_test11.scss */
#entry_date_popup .step_label {
  color: #2BB0A3;
}
