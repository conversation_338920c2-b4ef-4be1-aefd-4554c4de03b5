<header>
    <div id="wrapper-header"><div class="container12">

        <div class="column3">
            <div class="social_rooms">{{T_web_oficial}}<span> [+]</span></div>
            <div>|</div>
            <div><i class="fa fa-wifi"></i><span>{{T_wifi_gratuito}}</span></div>
        </div>
        <div class="column9">
            <div id="top-sections">
                {{phone_contact}}&nbsp;·&nbsp;
                {% for section in top_sections %}
                    <a href="{{ host|safe }}/{{ section.friendlyUrl }}">
                        <span>{{ section.title|safe }}</span>
                    </a>
                    {% if not forloop.last %}<div class="separator">|</div>{% endif %}
                {% endfor %}
            </div>

            <div class="separator">|</div>

            <div id="social">
                {%if facebook_id %}
                    <a href="http://www.facebook.com/{{facebook_id}}" target="_blank">
                        <i class="fa fa-facebook" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if twitter_id %}
                    <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank">
                        <i class="fa fa-twitter" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if google_plus_id %}
                    <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank" rel="publisher">
                        <i class="fa fa-google-plus" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if youtube_id %}
                    <a href="https://www.youtube.com/{{youtube_id}}" target="_blank">
                        <i class="fa fa-youtube" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if pinterest_id %}
                    <a href="http://es.pinterest.com/{{ pinterest_id }}" target="_blank">
                        <i class="fa fa-pinterest-p" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if instagram_id %}
                    <a href="http://www.instagram.com/{{ instagram_id }}" target="_blank">
                        <i class="fa fa-instagram" aria-hidden="true"></i>
                    </a>
                {% endif %}
            </div>

            <div class="separator">|</div>

            <div id="lang">
                {% for key, value in language_codes.items %}
                    <a href="{% if not key == 'es' %}{{ hostWithoutLanguage }}/{{ key }}/{% else %}/{% endif %}" {% if  key == language %} class="selected" {% endif %}>
                       <img src="/img/{{ base_web }}/lang/{{ key }}.png">
                    </a>
                {% endfor %}
            </div>

            <div class="separator">|</div>


        </div>
    </div></div>

    <div id="wrapper-logo" class="container12">
        <div id="logoDiv" class="column3">
            <a href="{{host|safe}}/">
                <img itemprop="logo" src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
            </a>
        </div>
        <nav id="main_menu" class="column9">
            <div id="mainMenuDiv">
                {% include "main_div.html" %}
            </div>
            <a href="#data" class="button-promotion"><i class="fa fa-th"></i>{{T_reservar}}</a>
        </nav>

    </div>


</header>