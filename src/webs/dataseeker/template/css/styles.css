/* Preload images */
@import url("https://fonts.googleapis.com/css?family=Roboto:100,300,400,500");
@import url("https://fonts.googleapis.com/css?family=Caveat: bold, regular");
/* line 2, ../../../../sass/plugins/_lightbox.scss */
body:after {
  content: url(/static_1/lib/lightbox/images/close.png) url(/static_1/lib/lightbox/images/loading.gif) url(/static_1/lib/lightbox/images/prev.png) url(/static_1/lib/lightbox/images/next.png);
  display: none;
}

/* line 7, ../../../../sass/plugins/_lightbox.scss */
.lightboxOverlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
  background-color: black;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=80);
  opacity: 0.8;
  display: none;
}

/* line 18, ../../../../sass/plugins/_lightbox.scss */
.lightbox {
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 10000;
  text-align: center;
  line-height: 0;
  font-weight: normal;
}

/* line 28, ../../../../sass/plugins/_lightbox.scss */
.lightbox .lb-image {
  display: block;
  height: auto;
  max-width: inherit;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  border-radius: 3px;
}

/* line 39, ../../../../sass/plugins/_lightbox.scss */
.lightbox a img {
  border: none;
}

/* line 43, ../../../../sass/plugins/_lightbox.scss */
.lb-outerContainer {
  position: relative;
  background-color: white;
  *zoom: 1;
  width: 250px;
  height: 250px;
  margin: 0 auto;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
}

/* line 57, ../../../../sass/plugins/_lightbox.scss */
.lb-outerContainer:after {
  content: "";
  display: table;
  clear: both;
}

/* line 63, ../../../../sass/plugins/_lightbox.scss */
.lb-container {
  padding: 4px;
}

/* line 67, ../../../../sass/plugins/_lightbox.scss */
.lb-loader {
  position: absolute;
  top: 43%;
  left: 0;
  height: 25%;
  width: 100%;
  text-align: center;
  line-height: 0;
}

/* line 77, ../../../../sass/plugins/_lightbox.scss */
.lb-cancel {
  display: block;
  width: 32px;
  height: 32px;
  margin: 0 auto;
  background: url(/static_1/lib/lightbox/images/loading.gif) no-repeat;
}

/* line 85, ../../../../sass/plugins/_lightbox.scss */
.lb-nav {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 10;
}

/* line 94, ../../../../sass/plugins/_lightbox.scss */
.lb-container > .nav {
  left: 0;
}

/* line 98, ../../../../sass/plugins/_lightbox.scss */
.lb-nav a {
  outline: none;
  background-image: url("data:image/gif;base64,R0lGODlhAQABAPAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==");
}

/* line 103, ../../../../sass/plugins/_lightbox.scss */
.lb-prev, .lb-next {
  height: 100%;
  cursor: pointer;
  display: block;
}

/* line 109, ../../../../sass/plugins/_lightbox.scss */
.lb-nav a.lb-prev {
  width: 34%;
  left: 0;
  float: left;
  background: url(/static_1/lib/lightbox/images/prev.png) left 48% no-repeat;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
  opacity: 0;
  -webkit-transition: opacity 0.6s;
  -moz-transition: opacity 0.6s;
  -o-transition: opacity 0.6s;
  transition: opacity 0.6s;
}

/* line 122, ../../../../sass/plugins/_lightbox.scss */
.lb-nav a.lb-prev:hover {
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
  opacity: 1;
}

/* line 127, ../../../../sass/plugins/_lightbox.scss */
.lb-nav a.lb-next {
  width: 64%;
  right: 0;
  float: right;
  background: url(/static_1/lib/lightbox/images/next.png) right 48% no-repeat;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
  opacity: 0;
  -webkit-transition: opacity 0.6s;
  -moz-transition: opacity 0.6s;
  -o-transition: opacity 0.6s;
  transition: opacity 0.6s;
}

/* line 140, ../../../../sass/plugins/_lightbox.scss */
.lb-nav a.lb-next:hover {
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
  opacity: 1;
}

/* line 145, ../../../../sass/plugins/_lightbox.scss */
.lb-dataContainer {
  margin: 0 auto;
  padding-top: 5px;
  *zoom: 1;
  width: 100%;
  -moz-border-radius-bottomleft: 4px;
  -webkit-border-bottom-left-radius: 4px;
  border-bottom-left-radius: 4px;
  -moz-border-radius-bottomright: 4px;
  -webkit-border-bottom-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

/* line 158, ../../../../sass/plugins/_lightbox.scss */
.lb-dataContainer:after {
  content: "";
  display: table;
  clear: both;
}

/* line 164, ../../../../sass/plugins/_lightbox.scss */
.lb-data {
  padding: 0 4px;
  color: #ccc;
}

/* line 169, ../../../../sass/plugins/_lightbox.scss */
.lb-data .lb-details {
  width: 85%;
  float: left;
  text-align: left;
  line-height: 1.1em;
}

/* line 176, ../../../../sass/plugins/_lightbox.scss */
.lb-data .lb-caption {
  font-size: 13px;
  font-weight: bold;
  line-height: 1em;
}

/* line 182, ../../../../sass/plugins/_lightbox.scss */
.lb-data .lb-number {
  display: block;
  clear: left;
  padding-bottom: 1em;
  font-size: 12px;
  color: #999999;
}

/* line 190, ../../../../sass/plugins/_lightbox.scss */
.lb-data .lb-close {
  display: block;
  float: right;
  width: 30px;
  height: 30px;
  background: url(/static_1/lib/lightbox/images/close.png) top right no-repeat;
  text-align: right;
  outline: none;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=70);
  opacity: 0.7;
  -webkit-transition: opacity 0.2s;
  -moz-transition: opacity 0.2s;
  -o-transition: opacity 0.2s;
  transition: opacity 0.2s;
}

/* line 206, ../../../../sass/plugins/_lightbox.scss */
.lb-data .lb-close:hover {
  cursor: pointer;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
  opacity: 1;
}

/* line 212, ../../../../sass/plugins/_lightbox.scss */
.lb-number {
  display: none !important;
}

/* line 216, ../../../../sass/plugins/_lightbox.scss */
.fancybox-opened .fancybox-outer {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

/*! fancyBox v2.1.5 fancyapps.com | fancyapps.com/fancybox/#license */
/* line 4, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-wrap,
.fancybox-skin,
.fancybox-outer,
.fancybox-inner,
.fancybox-image,
.fancybox-wrap iframe,
.fancybox-wrap object,
.fancybox-nav,
.fancybox-nav span,
.fancybox-tmp {
  padding: 0;
  margin: 0;
  border: 0;
  outline: none;
  vertical-align: top;
}

/* line 22, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-wrap {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 8020;
}

/* line 29, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-skin {
  position: relative;
  background: #f9f9f9;
  color: #444;
  text-shadow: none;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}

/* line 39, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-opened {
  z-index: 8030;
}

/* line 43, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-opened .fancybox-skin {
  -webkit-box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  -moz-box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

/* line 49, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-outer, .fancybox-inner {
  position: relative;
}

/* line 53, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-inner {
  overflow: hidden;
}

/* line 57, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-type-iframe .fancybox-inner {
  -webkit-overflow-scrolling: touch;
}

/* line 61, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-error {
  color: #444;
  font: 14px/20px "Helvetica Neue",Helvetica,Arial,sans-serif;
  margin: 0;
  padding: 15px;
  white-space: nowrap;
}

/* line 69, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-image, .fancybox-iframe {
  display: block;
  width: 100%;
  height: 100%;
}

/* line 75, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-image {
  max-width: 100%;
  max-height: 100%;
}

/* line 80, ../../../../sass/plugins/_fancybox_2_1_5.scss */
#fancybox-loading, .fancybox-close, .fancybox-prev span, .fancybox-next span {
  background-image: url("/static_1/lib/fancybox/fancybox_sprite.png");
}

/* line 84, ../../../../sass/plugins/_fancybox_2_1_5.scss */
#fancybox-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  margin-top: -22px;
  margin-left: -22px;
  background-position: 0 -108px;
  opacity: 0.8;
  cursor: pointer;
  z-index: 8060;
}

/* line 96, ../../../../sass/plugins/_fancybox_2_1_5.scss */
#fancybox-loading div {
  width: 44px;
  height: 44px;
  background: url("/static_1/lib/fancybox/fancybox_loading.gif") center center no-repeat;
}

/* line 102, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-close {
  position: absolute;
  top: -18px;
  right: -18px;
  width: 36px;
  height: 36px;
  cursor: pointer;
  z-index: 8040;
}

/* line 112, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-nav {
  position: absolute;
  top: 0;
  width: 40%;
  height: 100%;
  cursor: pointer;
  text-decoration: none;
  background: transparent url("../../static_1/lib/fancybox/blank.gif");
  /* helps IE */
  -webkit-tap-highlight-color: transparent;
  z-index: 8040;
}

/* line 124, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-prev {
  left: 0;
}

/* line 128, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-next {
  right: 0;
}

/* line 132, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-nav span {
  position: absolute;
  top: 50%;
  width: 36px;
  height: 34px;
  margin-top: -18px;
  cursor: pointer;
  z-index: 8040;
  visibility: hidden;
}

/* line 143, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-prev span {
  left: 10px;
  background-position: 0 -36px;
}

/* line 148, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-next span {
  right: 10px;
  background-position: 0 -72px;
}

/* line 153, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-nav:hover span {
  visibility: visible;
}

/* line 157, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-tmp {
  position: absolute;
  top: -99999px;
  left: -99999px;
  visibility: hidden;
  max-width: 99999px;
  max-height: 99999px;
  overflow: visible !important;
}

/* Overlay helper */
/* line 169, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-lock {
  overflow: hidden !important;
  width: auto;
}

/* line 174, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-lock body {
  overflow: hidden !important;
}

/* line 178, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-lock-test {
  overflow-y: hidden !important;
}

/* line 182, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-overlay {
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  display: none;
  z-index: 1001;
  background: url("/static_1/lib/fancybox/fancybox_overlay.png");
}

/* line 192, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-overlay-fixed {
  position: fixed;
  bottom: 0;
  right: 0;
}

/* line 198, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-lock .fancybox-overlay {
  overflow: auto;
  overflow-y: scroll;
}

/* Title helper */
/* line 205, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-title {
  visibility: hidden;
  font: normal 13px/20px "Helvetica Neue",Helvetica,Arial,sans-serif;
  position: relative;
  text-shadow: none;
  z-index: 8050;
}

/* line 213, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-opened .fancybox-title {
  visibility: visible;
}

/* line 217, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-title-float-wrap {
  position: absolute;
  bottom: 0;
  right: 50%;
  margin-bottom: -35px;
  z-index: 8050;
  text-align: center;
}

/* line 226, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-title-float-wrap .child {
  display: inline-block;
  margin-right: -100%;
  padding: 2px 20px;
  background: transparent;
  /* Fallback for web browsers that doesn't support RGBa */
  background: rgba(0, 0, 0, 0.8);
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  border-radius: 15px;
  text-shadow: 0 1px 2px #222;
  color: #FFF;
  font-weight: bold;
  line-height: 24px;
  white-space: nowrap;
}

/* line 242, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-title-outside-wrap {
  position: relative;
  margin-top: 10px;
  color: #fff;
}

/* line 248, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-title-inside-wrap {
  padding-top: 10px;
}

/* line 252, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-title-over-wrap {
  position: absolute;
  bottom: 0;
  left: 0;
  color: #fff;
  padding: 10px;
  background: #000;
  background: rgba(0, 0, 0, 0.8);
}

/*Retina graphics!*/
@media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min--moz-device-pixel-ratio: 1.5), only screen and (min-device-pixel-ratio: 1.5) {
  /* line 267, ../../../../sass/plugins/_fancybox_2_1_5.scss */
  #fancybox-loading, .fancybox-close, .fancybox-prev span, .fancybox-next span {
    background-image: url("/static_1/lib/fancybox/<EMAIL>");
    background-size: 44px 152px;
    /*The size of the normal image, half the size of the hi-res image*/
  }

  /* line 272, ../../../../sass/plugins/_fancybox_2_1_5.scss */
  #fancybox-loading div {
    background-image: url("/static_1/lib/fancybox/<EMAIL>");
    background-size: 24px 24px;
    /*The size of the normal image, half the size of the hi-res image*/
  }
}
/* line 3, ../../../../sass/plugins/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 8, ../../../../sass/plugins/_mixins.scss */
.center_xy_before:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 13, ../../../../sass/plugins/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 17, ../../../../sass/plugins/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 21, ../../../../sass/plugins/_mixins.scss */
.center_y_before:before {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 25, ../../../../sass/plugins/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 30, ../../../../sass/plugins/_mixins.scss */
.fs:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
}

/* line 110, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:before, .icon-xcross:after {
  content: '';
  width: 100%;
  height: 2px;
  background: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 124, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -moz-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 61, ../../../../sass/plugins/_mixins.scss */
.display_flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* line 1, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown {
  margin: 40px 20px 20px;
  text-align: center;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  bottom: 180px;
  left: 48% !important;
  color: white;
}
/* line 9, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown div {
  display: inline-block;
  font-size: 16px;
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 0;
  padding-bottom: 0;
  text-transform: uppercase;
}
/* line 20, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .days {
  font-weight: 600;
}
/* line 23, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .date {
  display: block;
  font-size: 4.5rem;
}
/* line 28, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .title_format {
  font-weight: 600;
}

@media screen and (max-width: 800px) {
  /* line 35, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown {
    display: inline-block;
    width: 380px;
    top: 330px;
    left: 45% !important;
  }
  /* line 40, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown div {
    font-size: 10px;
  }
  /* line 43, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown .date {
    font-size: 2.5rem;
  }
}
@font-face {
  font-family: 'FontAwesome';
  src: url("/static_1/fonts/fontawesome/fontawesome/fontawesome-webfont.eot?v=4.7.0");
  src: url("/static_1/fonts/fontawesome/fontawesome-webfont.eot?#iefix&v=4.7.0") format("embedded-opentype"), url("/static_1/fonts/fontawesome/fontawesome-webfont.woff2?v=4.7.0") format("woff2"), url("/static_1/fonts/fontawesome/fontawesome-webfont.woff?v=4.7.0") format("woff"), url("/static_1/fonts/fontawesome/fontawesome-webfont.ttf?v=4.7.0") format("truetype"), url("/static_1/fonts/fontawesome/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular") format("svg");
  font-weight: normal;
  font-style: normal;
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv #newsletter-button:before {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lg {
  font-size: 1.33333333em;
  line-height: .75em;
  vertical-align: -15%;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-3x {
  font-size: 3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-4x {
  font-size: 4em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-5x {
  font-size: 5em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fw {
  width: 1.28571429em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul > li {
  position: relative;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: .14285714em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li.fa-lg {
  left: -1.85714286em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-border {
  padding: .2em .25em .15em;
  border: solid .08em #eee;
  border-radius: .1em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-left, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv .fa-pull-left#newsletter-button:before {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-right, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv .fa-pull-right#newsletter-button:before {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-left, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv .pull-left#newsletter-button:before {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-right, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv .pull-right#newsletter-button:before {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  transform: scale(-1, 1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical {
  filter: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x, .fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x {
  line-height: inherit;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inverse {
  color: #fff;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glass:before {
  content: "\f000";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-music:before {
  content: "\f001";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search:before {
  content: "\f002";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-o:before {
  content: "\f003";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart:before {
  content: "\f004";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star:before {
  content: "\f005";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-o:before {
  content: "\f006";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user:before {
  content: "\f007";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-film:before {
  content: "\f008";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-large:before {
  content: "\f009";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th:before {
  content: "\f00a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-list:before {
  content: "\f00b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check:before {
  content: "\f00c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-remove:before, .fa-close:before, .fa-times:before {
  content: "\f00d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-plus:before {
  content: "\f00e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-minus:before {
  content: "\f010";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-power-off:before {
  content: "\f011";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signal:before {
  content: "\f012";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gear:before, .fa-cog:before {
  content: "\f013";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash-o:before {
  content: "\f014";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-home:before {
  content: "\f015";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-o:before {
  content: "\f016";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clock-o:before {
  content: "\f017";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-road:before {
  content: "\f018";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-download:before {
  content: "\f019";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-down:before {
  content: "\f01a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-up:before {
  content: "\f01b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inbox:before {
  content: "\f01c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle-o:before {
  content: "\f01d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-right:before, .fa-repeat:before {
  content: "\f01e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-refresh:before {
  content: "\f021";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-alt:before {
  content: "\f022";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lock:before {
  content: "\f023";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag:before {
  content: "\f024";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-headphones:before {
  content: "\f025";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-off:before {
  content: "\f026";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-down:before {
  content: "\f027";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-up:before {
  content: "\f028";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qrcode:before {
  content: "\f029";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-barcode:before {
  content: "\f02a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tag:before {
  content: "\f02b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tags:before {
  content: "\f02c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-book:before {
  content: "\f02d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark:before {
  content: "\f02e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-print:before {
  content: "\f02f";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera:before {
  content: "\f030";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-font:before {
  content: "\f031";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bold:before {
  content: "\f032";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-italic:before {
  content: "\f033";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-height:before {
  content: "\f034";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-width:before {
  content: "\f035";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-left:before {
  content: "\f036";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-center:before {
  content: "\f037";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-right:before {
  content: "\f038";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-justify:before {
  content: "\f039";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list:before {
  content: "\f03a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dedent:before, .fa-outdent:before {
  content: "\f03b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-indent:before {
  content: "\f03c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-video-camera:before {
  content: "\f03d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-photo:before, .fa-image:before, .fa-picture-o:before {
  content: "\f03e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil:before {
  content: "\f040";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-marker:before {
  content: "\f041";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adjust:before {
  content: "\f042";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tint:before {
  content: "\f043";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edit:before, .fa-pencil-square-o:before {
  content: "\f044";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square-o:before {
  content: "\f045";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square-o:before {
  content: "\f046";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows:before {
  content: "\f047";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-backward:before {
  content: "\f048";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-backward:before {
  content: "\f049";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-backward:before {
  content: "\f04a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play:before {
  content: "\f04b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause:before {
  content: "\f04c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop:before {
  content: "\f04d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forward:before {
  content: "\f04e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-forward:before {
  content: "\f050";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-forward:before {
  content: "\f051";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eject:before {
  content: "\f052";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-left:before {
  content: "\f053";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-right:before {
  content: "\f054";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-circle:before {
  content: "\f055";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-circle:before {
  content: "\f056";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle:before {
  content: "\f057";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle:before {
  content: "\f058";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle:before {
  content: "\f059";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info-circle:before {
  content: "\f05a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crosshairs:before {
  content: "\f05b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle-o:before {
  content: "\f05c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle-o:before {
  content: "\f05d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ban:before {
  content: "\f05e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-left:before {
  content: "\f060";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-right:before {
  content: "\f061";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-up:before {
  content: "\f062";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-down:before {
  content: "\f063";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-forward:before, .fa-share:before {
  content: "\f064";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expand:before {
  content: "\f065";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compress:before {
  content: "\f066";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus:before {
  content: "\f067";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus:before {
  content: "\f068";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asterisk:before {
  content: "\f069";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation-circle:before {
  content: "\f06a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gift:before {
  content: "\f06b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leaf:before {
  content: "\f06c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire:before {
  content: "\f06d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye:before {
  content: "\f06e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye-slash:before {
  content: "\f070";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-warning:before, .fa-exclamation-triangle:before {
  content: "\f071";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plane:before {
  content: "\f072";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar:before {
  content: "\f073";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-random:before {
  content: "\f074";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment:before {
  content: "\f075";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magnet:before {
  content: "\f076";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-up:before {
  content: "\f077";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-down:before {
  content: "\f078";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-retweet:before {
  content: "\f079";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-cart:before {
  content: "\f07a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder:before {
  content: "\f07b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open:before {
  content: "\f07c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-v:before {
  content: "\f07d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-h:before {
  content: "\f07e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bar-chart-o:before, .fa-bar-chart:before {
  content: "\f080";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter-square:before {
  content: "\f081";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-square:before {
  content: "\f082";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera-retro:before {
  content: "\f083";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-key:before {
  content: "\f084";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gears:before, .fa-cogs:before {
  content: "\f085";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments:before {
  content: "\f086";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-up:before {
  content: "\f087";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-down:before {
  content: "\f088";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half:before {
  content: "\f089";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart-o:before {
  content: "\f08a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-out:before {
  content: "\f08b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin-square:before {
  content: "\f08c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumb-tack:before {
  content: "\f08d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link:before {
  content: "\f08e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-in:before {
  content: "\f090";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trophy:before {
  content: "\f091";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-square:before {
  content: "\f092";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-upload:before {
  content: "\f093";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lemon-o:before {
  content: "\f094";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone:before {
  content: "\f095";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square-o:before {
  content: "\f096";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark-o:before {
  content: "\f097";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone-square:before {
  content: "\f098";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter:before {
  content: "\f099";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-f:before, .fa-facebook:before {
  content: "\f09a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github:before {
  content: "\f09b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock:before {
  content: "\f09c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card:before {
  content: "\f09d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-feed:before, .fa-rss:before {
  content: "\f09e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hdd-o:before {
  content: "\f0a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullhorn:before {
  content: "\f0a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell:before {
  content: "\f0f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-certificate:before {
  content: "\f0a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-right:before {
  content: "\f0a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-left:before {
  content: "\f0a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-up:before {
  content: "\f0a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-down:before {
  content: "\f0a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-left:before {
  content: "\f0a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-right:before {
  content: "\f0a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-up:before {
  content: "\f0aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-down:before {
  content: "\f0ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-globe:before {
  content: "\f0ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wrench:before {
  content: "\f0ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tasks:before {
  content: "\f0ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-filter:before {
  content: "\f0b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-briefcase:before {
  content: "\f0b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-alt:before {
  content: "\f0b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-group:before, .fa-users:before {
  content: "\f0c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chain:before, .fa-link:before {
  content: "\f0c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud:before {
  content: "\f0c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flask:before {
  content: "\f0c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cut:before, .fa-scissors:before {
  content: "\f0c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copy:before, .fa-files-o:before {
  content: "\f0c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paperclip:before {
  content: "\f0c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-save:before, .fa-floppy-o:before {
  content: "\f0c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square:before {
  content: "\f0c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-navicon:before, .fa-reorder:before, .fa-bars:before {
  content: "\f0c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ul:before {
  content: "\f0ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ol:before {
  content: "\f0cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-strikethrough:before {
  content: "\f0cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-underline:before {
  content: "\f0cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-table:before {
  content: "\f0ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magic:before {
  content: "\f0d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-truck:before {
  content: "\f0d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest:before {
  content: "\f0d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-square:before {
  content: "\f0d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-square:before {
  content: "\f0d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus:before {
  content: "\f0d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-money:before {
  content: "\f0d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-down:before {
  content: "\f0d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-up:before {
  content: "\f0d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-left:before {
  content: "\f0d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-right:before {
  content: "\f0da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-columns:before {
  content: "\f0db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unsorted:before, .fa-sort:before {
  content: "\f0dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-down:before, .fa-sort-desc:before {
  content: "\f0dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-up:before, .fa-sort-asc:before {
  content: "\f0de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope:before {
  content: "\f0e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin:before {
  content: "\f0e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-left:before, .fa-undo:before {
  content: "\f0e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-legal:before, .fa-gavel:before {
  content: "\f0e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashboard:before, .fa-tachometer:before {
  content: "\f0e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment-o:before {
  content: "\f0e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments-o:before {
  content: "\f0e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flash:before, .fa-bolt:before {
  content: "\f0e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sitemap:before {
  content: "\f0e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-umbrella:before {
  content: "\f0e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paste:before, .fa-clipboard:before {
  content: "\f0ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lightbulb-o:before {
  content: "\f0eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exchange:before {
  content: "\f0ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-download:before {
  content: "\f0ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-upload:before {
  content: "\f0ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-md:before {
  content: "\f0f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stethoscope:before {
  content: "\f0f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-suitcase:before {
  content: "\f0f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-o:before {
  content: "\f0a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-coffee:before {
  content: "\f0f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cutlery:before {
  content: "\f0f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text-o:before {
  content: "\f0f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building-o:before {
  content: "\f0f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hospital-o:before {
  content: "\f0f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ambulance:before {
  content: "\f0f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medkit:before {
  content: "\f0fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fighter-jet:before {
  content: "\f0fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-beer:before {
  content: "\f0fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-h-square:before {
  content: "\f0fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square:before {
  content: "\f0fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-left:before {
  content: "\f100";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-right:before {
  content: "\f101";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-up:before {
  content: "\f102";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-down:before {
  content: "\f103";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-left:before {
  content: "\f104";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-right:before, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv #newsletter-button:before, .wordpress_block .newsletter_element#newsletter_blog #newsletterButtonExternalDiv #newsletter-button:before {
  content: "\f105";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-up:before {
  content: "\f106";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-down:before {
  content: "\f107";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-desktop:before {
  content: "\f108";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-laptop:before {
  content: "\f109";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tablet:before {
  content: "\f10a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mobile-phone:before, .fa-mobile:before {
  content: "\f10b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o:before {
  content: "\f10c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-left:before {
  content: "\f10d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-right:before {
  content: "\f10e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spinner:before {
  content: "\f110";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle:before {
  content: "\f111";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply:before, .fa-reply:before {
  content: "\f112";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-alt:before {
  content: "\f113";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-o:before {
  content: "\f114";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open-o:before {
  content: "\f115";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-smile-o:before {
  content: "\f118";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-frown-o:before {
  content: "\f119";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meh-o:before {
  content: "\f11a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gamepad:before {
  content: "\f11b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-keyboard-o:before {
  content: "\f11c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-o:before {
  content: "\f11d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-checkered:before {
  content: "\f11e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-terminal:before {
  content: "\f120";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code:before {
  content: "\f121";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply-all:before, .fa-reply-all:before {
  content: "\f122";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before {
  content: "\f123";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-location-arrow:before {
  content: "\f124";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crop:before {
  content: "\f125";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code-fork:before {
  content: "\f126";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlink:before, .fa-chain-broken:before {
  content: "\f127";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question:before {
  content: "\f128";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info:before {
  content: "\f129";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation:before {
  content: "\f12a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superscript:before {
  content: "\f12b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subscript:before {
  content: "\f12c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eraser:before {
  content: "\f12d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-puzzle-piece:before {
  content: "\f12e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone:before {
  content: "\f130";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone-slash:before {
  content: "\f131";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shield:before {
  content: "\f132";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-o:before {
  content: "\f133";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire-extinguisher:before {
  content: "\f134";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rocket:before {
  content: "\f135";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-maxcdn:before {
  content: "\f136";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-left:before {
  content: "\f137";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-right:before {
  content: "\f138";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-up:before {
  content: "\f139";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-down:before {
  content: "\f13a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-html5:before {
  content: "\f13b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-css3:before {
  content: "\f13c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-anchor:before {
  content: "\f13d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock-alt:before {
  content: "\f13e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullseye:before {
  content: "\f140";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-h:before {
  content: "\f141";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-v:before {
  content: "\f142";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rss-square:before {
  content: "\f143";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle:before {
  content: "\f144";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ticket:before {
  content: "\f145";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square:before {
  content: "\f146";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square-o:before {
  content: "\f147";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-up:before {
  content: "\f148";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-down:before {
  content: "\f149";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square:before {
  content: "\f14a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil-square:before {
  content: "\f14b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link-square:before {
  content: "\f14c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square:before {
  content: "\f14d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compass:before {
  content: "\f14e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-down:before, .fa-caret-square-o-down:before {
  content: "\f150";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-up:before, .fa-caret-square-o-up:before {
  content: "\f151";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-right:before, .fa-caret-square-o-right:before {
  content: "\f152";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-euro:before, .fa-eur:before {
  content: "\f153";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gbp:before {
  content: "\f154";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dollar:before, .fa-usd:before {
  content: "\f155";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rupee:before, .fa-inr:before {
  content: "\f156";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before {
  content: "\f157";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ruble:before, .fa-rouble:before, .fa-rub:before {
  content: "\f158";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-won:before, .fa-krw:before {
  content: "\f159";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitcoin:before, .fa-btc:before {
  content: "\f15a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file:before {
  content: "\f15b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text:before {
  content: "\f15c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-asc:before {
  content: "\f15d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-desc:before {
  content: "\f15e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-asc:before {
  content: "\f160";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-desc:before {
  content: "\f161";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-asc:before {
  content: "\f162";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-desc:before {
  content: "\f163";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-up:before {
  content: "\f164";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-down:before {
  content: "\f165";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-square:before {
  content: "\f166";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube:before {
  content: "\f167";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing:before {
  content: "\f168";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing-square:before {
  content: "\f169";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-play:before {
  content: "\f16a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dropbox:before {
  content: "\f16b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-overflow:before {
  content: "\f16c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-instagram:before {
  content: "\f16d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flickr:before {
  content: "\f16e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adn:before {
  content: "\f170";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket:before {
  content: "\f171";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket-square:before {
  content: "\f172";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr:before {
  content: "\f173";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr-square:before {
  content: "\f174";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-down:before {
  content: "\f175";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-up:before {
  content: "\f176";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-left:before {
  content: "\f177";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-right:before {
  content: "\f178";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-apple:before {
  content: "\f179";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-windows:before {
  content: "\f17a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-android:before {
  content: "\f17b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linux:before {
  content: "\f17c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dribbble:before {
  content: "\f17d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skype:before {
  content: "\f17e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-foursquare:before {
  content: "\f180";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trello:before {
  content: "\f181";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-female:before {
  content: "\f182";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-male:before {
  content: "\f183";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gittip:before, .fa-gratipay:before {
  content: "\f184";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sun-o:before {
  content: "\f185";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-moon-o:before {
  content: "\f186";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-archive:before {
  content: "\f187";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bug:before {
  content: "\f188";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vk:before {
  content: "\f189";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-weibo:before {
  content: "\f18a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-renren:before {
  content: "\f18b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pagelines:before {
  content: "\f18c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-exchange:before {
  content: "\f18d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-right:before {
  content: "\f18e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-left:before {
  content: "\f190";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-left:before, .fa-caret-square-o-left:before {
  content: "\f191";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dot-circle-o:before {
  content: "\f192";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair:before {
  content: "\f193";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo-square:before {
  content: "\f194";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-turkish-lira:before, .fa-try:before {
  content: "\f195";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square-o:before {
  content: "\f196";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-space-shuttle:before {
  content: "\f197";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slack:before {
  content: "\f198";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-square:before {
  content: "\f199";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wordpress:before {
  content: "\f19a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-openid:before {
  content: "\f19b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-institution:before, .fa-bank:before, .fa-university:before {
  content: "\f19c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mortar-board:before, .fa-graduation-cap:before {
  content: "\f19d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yahoo:before {
  content: "\f19e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google:before {
  content: "\f1a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit:before {
  content: "\f1a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-square:before {
  content: "\f1a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon-circle:before {
  content: "\f1a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon:before {
  content: "\f1a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-delicious:before {
  content: "\f1a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-digg:before {
  content: "\f1a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-pp:before {
  content: "\f1a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-alt:before {
  content: "\f1a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drupal:before {
  content: "\f1a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-joomla:before {
  content: "\f1aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-language:before {
  content: "\f1ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fax:before {
  content: "\f1ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building:before {
  content: "\f1ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-child:before {
  content: "\f1ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paw:before {
  content: "\f1b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spoon:before {
  content: "\f1b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cube:before {
  content: "\f1b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cubes:before {
  content: "\f1b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance:before {
  content: "\f1b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance-square:before {
  content: "\f1b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam:before {
  content: "\f1b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam-square:before {
  content: "\f1b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-recycle:before {
  content: "\f1b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-automobile:before, .fa-car:before {
  content: "\f1b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cab:before, .fa-taxi:before {
  content: "\f1ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tree:before {
  content: "\f1bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spotify:before {
  content: "\f1bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deviantart:before {
  content: "\f1bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soundcloud:before {
  content: "\f1be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-database:before {
  content: "\f1c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-pdf-o:before {
  content: "\f1c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-word-o:before {
  content: "\f1c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-excel-o:before {
  content: "\f1c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-powerpoint-o:before {
  content: "\f1c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before {
  content: "\f1c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-zip-o:before, .fa-file-archive-o:before {
  content: "\f1c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-sound-o:before, .fa-file-audio-o:before {
  content: "\f1c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-movie-o:before, .fa-file-video-o:before {
  content: "\f1c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-code-o:before {
  content: "\f1c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vine:before {
  content: "\f1ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codepen:before {
  content: "\f1cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-jsfiddle:before {
  content: "\f1cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before {
  content: "\f1cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o-notch:before {
  content: "\f1ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ra:before, .fa-resistance:before, .fa-rebel:before {
  content: "\f1d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ge:before, .fa-empire:before {
  content: "\f1d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git-square:before {
  content: "\f1d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git:before {
  content: "\f1d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before {
  content: "\f1d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tencent-weibo:before {
  content: "\f1d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qq:before {
  content: "\f1d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wechat:before, .fa-weixin:before {
  content: "\f1d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send:before, .fa-paper-plane:before {
  content: "\f1d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send-o:before, .fa-paper-plane-o:before {
  content: "\f1d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-history:before {
  content: "\f1da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-thin:before {
  content: "\f1db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-header:before {
  content: "\f1dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paragraph:before {
  content: "\f1dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sliders:before {
  content: "\f1de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt:before {
  content: "\f1e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt-square:before {
  content: "\f1e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bomb:before {
  content: "\f1e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soccer-ball-o:before, .fa-futbol-o:before {
  content: "\f1e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tty:before {
  content: "\f1e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-binoculars:before {
  content: "\f1e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plug:before {
  content: "\f1e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slideshare:before {
  content: "\f1e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitch:before {
  content: "\f1e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yelp:before {
  content: "\f1e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-newspaper-o:before {
  content: "\f1ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wifi:before {
  content: "\f1eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calculator:before {
  content: "\f1ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paypal:before {
  content: "\f1ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-wallet:before {
  content: "\f1ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-visa:before {
  content: "\f1f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-mastercard:before {
  content: "\f1f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-discover:before {
  content: "\f1f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-amex:before {
  content: "\f1f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-paypal:before {
  content: "\f1f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-stripe:before {
  content: "\f1f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash:before {
  content: "\f1f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash-o:before {
  content: "\f1f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash:before {
  content: "\f1f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copyright:before {
  content: "\f1f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-at:before {
  content: "\f1fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eyedropper:before {
  content: "\f1fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paint-brush:before {
  content: "\f1fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-birthday-cake:before {
  content: "\f1fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-area-chart:before {
  content: "\f1fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pie-chart:before {
  content: "\f200";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-line-chart:before {
  content: "\f201";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm:before {
  content: "\f202";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm-square:before {
  content: "\f203";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-off:before {
  content: "\f204";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-on:before {
  content: "\f205";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bicycle:before {
  content: "\f206";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bus:before {
  content: "\f207";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ioxhost:before {
  content: "\f208";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angellist:before {
  content: "\f209";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc:before {
  content: "\f20a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shekel:before, .fa-sheqel:before, .fa-ils:before {
  content: "\f20b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meanpath:before {
  content: "\f20c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-buysellads:before {
  content: "\f20d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-connectdevelop:before {
  content: "\f20e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashcube:before {
  content: "\f210";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forumbee:before {
  content: "\f211";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leanpub:before {
  content: "\f212";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sellsy:before {
  content: "\f213";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shirtsinbulk:before {
  content: "\f214";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-simplybuilt:before {
  content: "\f215";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skyatlas:before {
  content: "\f216";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-plus:before {
  content: "\f217";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-arrow-down:before {
  content: "\f218";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-diamond:before {
  content: "\f219";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ship:before {
  content: "\f21a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-secret:before {
  content: "\f21b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-motorcycle:before {
  content: "\f21c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-street-view:before {
  content: "\f21d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heartbeat:before {
  content: "\f21e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus:before {
  content: "\f221";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars:before {
  content: "\f222";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mercury:before {
  content: "\f223";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-intersex:before, .fa-transgender:before {
  content: "\f224";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-transgender-alt:before {
  content: "\f225";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-double:before {
  content: "\f226";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-double:before {
  content: "\f227";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-mars:before {
  content: "\f228";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke:before {
  content: "\f229";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-v:before {
  content: "\f22a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-h:before {
  content: "\f22b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-neuter:before {
  content: "\f22c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-genderless:before {
  content: "\f22d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-official:before {
  content: "\f230";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-p:before {
  content: "\f231";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-whatsapp:before {
  content: "\f232";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-server:before {
  content: "\f233";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-plus:before {
  content: "\f234";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-times:before {
  content: "\f235";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hotel:before, .fa-bed:before {
  content: "\f236";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viacoin:before {
  content: "\f237";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-train:before {
  content: "\f238";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subway:before {
  content: "\f239";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medium:before {
  content: "\f23a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yc:before, .fa-y-combinator:before {
  content: "\f23b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-optin-monster:before {
  content: "\f23c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opencart:before {
  content: "\f23d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expeditedssl:before {
  content: "\f23e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before {
  content: "\f240";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-3:before, .fa-battery-three-quarters:before {
  content: "\f241";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-2:before, .fa-battery-half:before {
  content: "\f242";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-1:before, .fa-battery-quarter:before {
  content: "\f243";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-0:before, .fa-battery-empty:before {
  content: "\f244";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mouse-pointer:before {
  content: "\f245";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-i-cursor:before {
  content: "\f246";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-group:before {
  content: "\f247";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-ungroup:before {
  content: "\f248";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note:before {
  content: "\f249";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note-o:before {
  content: "\f24a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-jcb:before {
  content: "\f24b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-diners-club:before {
  content: "\f24c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clone:before {
  content: "\f24d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-balance-scale:before {
  content: "\f24e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-o:before {
  content: "\f250";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-1:before, .fa-hourglass-start:before {
  content: "\f251";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-2:before, .fa-hourglass-half:before {
  content: "\f252";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-3:before, .fa-hourglass-end:before {
  content: "\f253";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass:before {
  content: "\f254";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-grab-o:before, .fa-hand-rock-o:before {
  content: "\f255";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-stop-o:before, .fa-hand-paper-o:before {
  content: "\f256";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-scissors-o:before {
  content: "\f257";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-lizard-o:before {
  content: "\f258";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-spock-o:before {
  content: "\f259";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-pointer-o:before {
  content: "\f25a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-peace-o:before {
  content: "\f25b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trademark:before {
  content: "\f25c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-registered:before {
  content: "\f25d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-creative-commons:before {
  content: "\f25e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg:before {
  content: "\f260";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg-circle:before {
  content: "\f261";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tripadvisor:before {
  content: "\f262";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki:before {
  content: "\f263";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki-square:before {
  content: "\f264";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-get-pocket:before {
  content: "\f265";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wikipedia-w:before {
  content: "\f266";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-safari:before {
  content: "\f267";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chrome:before {
  content: "\f268";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-firefox:before {
  content: "\f269";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opera:before {
  content: "\f26a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-internet-explorer:before {
  content: "\f26b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tv:before, .fa-television:before {
  content: "\f26c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-contao:before {
  content: "\f26d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-500px:before {
  content: "\f26e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-amazon:before {
  content: "\f270";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-plus-o:before {
  content: "\f271";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-minus-o:before {
  content: "\f272";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-times-o:before {
  content: "\f273";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-check-o:before {
  content: "\f274";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-industry:before {
  content: "\f275";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-pin:before {
  content: "\f276";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-signs:before {
  content: "\f277";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-o:before {
  content: "\f278";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map:before {
  content: "\f279";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting:before {
  content: "\f27a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting-o:before {
  content: "\f27b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-houzz:before {
  content: "\f27c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo:before {
  content: "\f27d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-black-tie:before {
  content: "\f27e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fonticons:before {
  content: "\f280";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-alien:before {
  content: "\f281";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edge:before {
  content: "\f282";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card-alt:before {
  content: "\f283";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codiepie:before {
  content: "\f284";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-modx:before {
  content: "\f285";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fort-awesome:before {
  content: "\f286";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-usb:before {
  content: "\f287";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-product-hunt:before {
  content: "\f288";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mixcloud:before {
  content: "\f289";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-scribd:before {
  content: "\f28a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle:before {
  content: "\f28b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle-o:before {
  content: "\f28c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle:before {
  content: "\f28d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle-o:before {
  content: "\f28e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-bag:before {
  content: "\f290";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-basket:before {
  content: "\f291";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hashtag:before {
  content: "\f292";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth:before {
  content: "\f293";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth-b:before {
  content: "\f294";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-percent:before {
  content: "\f295";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gitlab:before {
  content: "\f296";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpbeginner:before {
  content: "\f297";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpforms:before {
  content: "\f298";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envira:before {
  content: "\f299";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-universal-access:before {
  content: "\f29a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair-alt:before {
  content: "\f29b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle-o:before {
  content: "\f29c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-blind:before {
  content: "\f29d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-audio-description:before {
  content: "\f29e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-control-phone:before {
  content: "\f2a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-braille:before {
  content: "\f2a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-assistive-listening-systems:before {
  content: "\f2a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before {
  content: "\f2a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide:before {
  content: "\f2a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide-g:before {
  content: "\f2a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signing:before, .fa-sign-language:before {
  content: "\f2a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-low-vision:before {
  content: "\f2a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo:before {
  content: "\f2a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo-square:before {
  content: "\f2aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat:before {
  content: "\f2ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-ghost:before {
  content: "\f2ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-square:before {
  content: "\f2ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper:before {
  content: "\f2ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-first-order:before {
  content: "\f2b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yoast:before {
  content: "\f2b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-themeisle:before {
  content: "\f2b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-circle:before, .fa-google-plus-official:before {
  content: "\f2b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fa:before, .fa-font-awesome:before {
  content: "\f2b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-handshake-o:before {
  content: "\f2b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open:before {
  content: "\f2b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open-o:before {
  content: "\f2b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linode:before {
  content: "\f2b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book:before {
  content: "\f2b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book-o:before {
  content: "\f2ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard:before, .fa-address-card:before {
  content: "\f2bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard-o:before, .fa-address-card-o:before {
  content: "\f2bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle:before {
  content: "\f2bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle-o:before {
  content: "\f2be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-o:before {
  content: "\f2c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-id-badge:before {
  content: "\f2c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license:before, .fa-id-card:before {
  content: "\f2c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license-o:before, .fa-id-card-o:before {
  content: "\f2c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quora:before {
  content: "\f2c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-free-code-camp:before {
  content: "\f2c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-telegram:before {
  content: "\f2c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before {
  content: "\f2c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-3:before, .fa-thermometer-three-quarters:before {
  content: "\f2c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-2:before, .fa-thermometer-half:before {
  content: "\f2c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-1:before, .fa-thermometer-quarter:before {
  content: "\f2ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-0:before, .fa-thermometer-empty:before {
  content: "\f2cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shower:before {
  content: "\f2cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bathtub:before, .fa-s15:before, .fa-bath:before {
  content: "\f2cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-podcast:before {
  content: "\f2ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-maximize:before {
  content: "\f2d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-minimize:before {
  content: "\f2d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-restore:before {
  content: "\f2d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle:before, .fa-window-close:before {
  content: "\f2d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle-o:before, .fa-window-close-o:before {
  content: "\f2d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bandcamp:before {
  content: "\f2d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-grav:before {
  content: "\f2d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-etsy:before {
  content: "\f2d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-imdb:before {
  content: "\f2d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ravelry:before {
  content: "\f2d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eercast:before {
  content: "\f2da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microchip:before {
  content: "\f2db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snowflake-o:before {
  content: "\f2dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superpowers:before {
  content: "\f2dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpexplorer:before {
  content: "\f2de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meetup:before {
  content: "\f2e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

/* line 1, ../../../../sass/plugins/_1140.scss */
body {
  min-width: 1140px;
}

/* line 5, ../../../../sass/plugins/_1140.scss */
html, body, div, span, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, address, cite, code, del, dfn, em, img, ins, q, small, strong, sub, sup, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td {
  border: 0;
  margin: 0;
  padding: 0;
}

/* line 5, ../../../../sass/plugins/_1140.scss */
article, aside, figure, figure img, figcaption, hgroup, footer, header, nav, section, video, object {
  display: block;
}

/* line 5, ../../../../sass/plugins/_1140.scss */
a img {
  border: 0;
}

/* line 5, ../../../../sass/plugins/_1140.scss */
figure {
  position: relative;
}

/* line 5, ../../../../sass/plugins/_1140.scss */
figure img {
  width: 100%;
}

/* line 6, ../../../../sass/plugins/_1140.scss */
.container12, .container16 {
  margin: 0 auto;
  padding: 0;
  width: 1140px;
}

/* line 11, ../../../../sass/plugins/_1140.scss */
.row {
  margin: 10px 0;
}

/* line 12, ../../../../sass/plugins/_1140.scss */
.rowBottom {
  margin-bottom: 20px;
}

/* line 13, ../../../../sass/plugins/_1140.scss */
.rowTop {
  margin-top: 20px;
}

/* line 14, ../../../../sass/plugins/_1140.scss */
.column1, .column2, .column3, .column4, .column5, .column6, .column7, .column8, .column9, .column10, .column11, .column12, .column13, .column14, .column15, .column16 {
  display: inline;
  float: left;
  margin-left: 10px;
  margin-right: 10px;
}

/* line 20, ../../../../sass/plugins/_1140.scss */
.container12 .alpha, .container16 .alpha {
  margin-left: 0;
}

/* line 21, ../../../../sass/plugins/_1140.scss */
.container12 .omega, .container16 .omega {
  margin-right: 0;
}

/* line 22, ../../../../sass/plugins/_1140.scss */
.container12 .column1 {
  width: 75px;
}

/* line 23, ../../../../sass/plugins/_1140.scss */
.container12 .column2 {
  width: 170px;
}

/* line 24, ../../../../sass/plugins/_1140.scss */
.container12 .column3 {
  width: 265px;
}

/* line 25, ../../../../sass/plugins/_1140.scss */
.container12 .column4 {
  width: 360px;
}

/* line 26, ../../../../sass/plugins/_1140.scss */
.container12 .column5 {
  width: 455px;
}

/* line 27, ../../../../sass/plugins/_1140.scss */
.container12 .column6 {
  width: 550px;
}

/* line 28, ../../../../sass/plugins/_1140.scss */
.container12 .column7 {
  width: 645px;
}

/* line 29, ../../../../sass/plugins/_1140.scss */
.container12 .column8 {
  width: 740px;
}

/* line 30, ../../../../sass/plugins/_1140.scss */
.container12 .column9 {
  width: 835px;
}

/* line 31, ../../../../sass/plugins/_1140.scss */
.container12 .column10 {
  width: 930px;
}

/* line 32, ../../../../sass/plugins/_1140.scss */
.container12 .column11 {
  width: 1025px;
}

/* line 33, ../../../../sass/plugins/_1140.scss */
.container12 .column12 {
  width: 1120px;
}

/* line 34, ../../../../sass/plugins/_1140.scss */
.container12 .prefix1 {
  padding-left: 95px;
}

/* line 35, ../../../../sass/plugins/_1140.scss */
.container12 .prefix2 {
  padding-left: 190px;
}

/* line 36, ../../../../sass/plugins/_1140.scss */
.container12 .prefix3 {
  padding-left: 285px;
}

/* line 37, ../../../../sass/plugins/_1140.scss */
.container12 .prefix4 {
  padding-left: 380px;
}

/* line 38, ../../../../sass/plugins/_1140.scss */
.container12 .prefix5 {
  padding-left: 475px;
}

/* line 39, ../../../../sass/plugins/_1140.scss */
.container12 .prefix6 {
  padding-left: 570px;
}

/* line 40, ../../../../sass/plugins/_1140.scss */
.container12 .prefix7 {
  padding-left: 665px;
}

/* line 41, ../../../../sass/plugins/_1140.scss */
.container12 .prefix8 {
  padding-left: 760px;
}

/* line 42, ../../../../sass/plugins/_1140.scss */
.container12 .prefix9 {
  padding-left: 855px;
}

/* line 43, ../../../../sass/plugins/_1140.scss */
.container12 .prefix10 {
  padding-left: 950px;
}

/* line 44, ../../../../sass/plugins/_1140.scss */
.container12 .prefix11 {
  padding-left: 1045px;
}

/* line 46, ../../../../sass/plugins/_1140.scss */
.container16 .column1 {
  width: 51.25px;
}

/* line 47, ../../../../sass/plugins/_1140.scss */
.container16 .column2 {
  width: 122.5px;
}

/* line 48, ../../../../sass/plugins/_1140.scss */
.container16 .column3 {
  width: 193.75px;
}

/* line 49, ../../../../sass/plugins/_1140.scss */
.container16 .column4 {
  width: 265px;
}

/* line 50, ../../../../sass/plugins/_1140.scss */
.container16 .column5 {
  width: 336.25px;
}

/* line 51, ../../../../sass/plugins/_1140.scss */
.container16 .column6 {
  width: 407.5px;
}

/* line 52, ../../../../sass/plugins/_1140.scss */
.container16 .column7 {
  width: 478.75px;
}

/* line 53, ../../../../sass/plugins/_1140.scss */
.container16 .column8 {
  width: 550px;
}

/* line 54, ../../../../sass/plugins/_1140.scss */
.container16 .column9 {
  width: 621.25px;
}

/* line 55, ../../../../sass/plugins/_1140.scss */
.container16 .column10 {
  width: 692.5px;
}

/* line 56, ../../../../sass/plugins/_1140.scss */
.container16 .column11 {
  width: 763.75px;
}

/* line 57, ../../../../sass/plugins/_1140.scss */
.container16 .column12 {
  width: 835px;
}

/* line 58, ../../../../sass/plugins/_1140.scss */
.container16 .column13 {
  width: 906.25px;
}

/* line 59, ../../../../sass/plugins/_1140.scss */
.container16 .column14 {
  width: 977.5px;
}

/* line 60, ../../../../sass/plugins/_1140.scss */
.container16 .column15 {
  width: 1048.75px;
}

/* line 61, ../../../../sass/plugins/_1140.scss */
.container16 .column16 {
  width: 1120px;
}

/* line 62, ../../../../sass/plugins/_1140.scss */
.container16 .prefix1 {
  padding-left: 71.25px;
}

/* line 63, ../../../../sass/plugins/_1140.scss */
.container16 .prefix2 {
  padding-left: 142.5px;
}

/* line 64, ../../../../sass/plugins/_1140.scss */
.container16 .prefix3 {
  padding-left: 213.75px;
}

/* line 65, ../../../../sass/plugins/_1140.scss */
.container16 .prefix4 {
  padding-left: 285px;
}

/* line 66, ../../../../sass/plugins/_1140.scss */
.container16 .prefix5 {
  padding-left: 356.25px;
}

/* line 67, ../../../../sass/plugins/_1140.scss */
.container16 .prefix6 {
  padding-left: 427.5px;
}

/* line 68, ../../../../sass/plugins/_1140.scss */
.container16 .prefix7 {
  padding-left: 498.75px;
}

/* line 69, ../../../../sass/plugins/_1140.scss */
.container16 .prefix8 {
  padding-left: 570px;
}

/* line 70, ../../../../sass/plugins/_1140.scss */
.container16 .prefix9 {
  padding-left: 641.25px;
}

/* line 71, ../../../../sass/plugins/_1140.scss */
.container16 .prefix10 {
  padding-left: 712.5px;
}

/* line 72, ../../../../sass/plugins/_1140.scss */
.container16 .prefix11 {
  padding-left: 783.75px;
}

/* line 73, ../../../../sass/plugins/_1140.scss */
.container16 .prefix12 {
  padding-left: 855px;
}

/* line 74, ../../../../sass/plugins/_1140.scss */
.container16 .prefix13 {
  padding-left: 926.25px;
}

/* line 75, ../../../../sass/plugins/_1140.scss */
.container16 .prefix14 {
  padding-left: 997.5px;
}

/* line 76, ../../../../sass/plugins/_1140.scss */
.container16 .prefix15 {
  padding-left: 1068.75px;
}

/* line 78, ../../../../sass/plugins/_1140.scss */
.clearfix:before, .clearfix:after,
.row:before, .row:after,
.container12:before, .container12:after, .container16:before, .container16:after {
  content: '.';
  display: block;
  height: 0;
  overflow: hidden;
  visibility: hidden;
  width: 0;
}

/* http://sonspring.com/journal/clearing-floats */
/* line 89, ../../../../sass/plugins/_1140.scss */
.clear {
  clear: both;
  display: block;
  height: 0;
  overflow: hidden;
  visibility: hidden;
  width: 0;
}

/* line 97, ../../../../sass/plugins/_1140.scss */
.row:after, .clearfix:after, .container12:after, .container16:after {
  clear: both;
}

/* For IE7. Move this to separate file when you notice some problems */
/* line 99, ../../../../sass/plugins/_1140.scss */
.row, .rowBottom, .rowTop, .clearfix {
  zoom: 1;
}

/* line 100, ../../../../sass/plugins/_1140.scss */
img, object, embed {
  max-width: 100%;
}

/* line 101, ../../../../sass/plugins/_1140.scss */
img {
  height: auto;
}

/*-------------------------------------------------------------------
Reset stylesheet to reduce browser inconsistencies
http://meyerweb.com/eric/tools/css/reset/ 
v2.0 | 20110126
License: none (public domain)
-------------------------------------------------------------------*/
/* line 7, ../../../../sass/plugins/_templateBaseline.scss */
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
/* line 28, ../../../../sass/plugins/_templateBaseline.scss */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block;
}

/* line 32, ../../../../sass/plugins/_templateBaseline.scss */
ol, ul {
  list-style: none;
}

/* line 38, ../../../../sass/plugins/_templateBaseline.scss */
blockquote, q {
  quotes: none;
}

/* line 41, ../../../../sass/plugins/_templateBaseline.scss */
blockquote:before, blockquote:after,
q:before, q:after {
  content: '';
  content: none;
}

/* line 46, ../../../../sass/plugins/_templateBaseline.scss */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/*-----------------------------------------------------------------*/
/*! fancyBox v2.0.4 fancyapps.com | fancyapps.com/fancybox/#license */
/* line 54, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-tmp iframe, .fancybox-tmp object {
  vertical-align: top;
  padding: 0;
  margin: 0;
}

/* line 60, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-wrap {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1002;
}

/* line 67, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-outer {
  padding: 0;
  margin: 0;
  background: #f9f9f9;
  color: #444;
  text-shadow: none;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}

/* line 78, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-opened {
  z-index: 1003;
}

/* line 82, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-opened .fancybox-outer {
  -webkit-box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  -moz-box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

/* line 88, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-inner {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  position: relative;
  outline: none;
  overflow: hidden;
}

/* line 98, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-error {
  color: #444;
  font: 14px/20px "Helvetica Neue",Helvetica,Arial,sans-serif;
  margin: 0;
  padding: 10px;
}

/* line 105, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-image, .fancybox-iframe {
  display: block;
  width: 100%;
  height: 100%;
  border: 0;
  padding: 0;
  margin: 0;
  vertical-align: top;
}

/* line 115, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-image {
  max-width: 100%;
  max-height: 100%;
}

/* line 120, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  margin-top: -21px;
  margin-left: -21px;
  width: 42px;
  height: 42px;
  background: url("/static_1/lib/fancybox/source/fancybox_loading.gif");
  opacity: 0.8;
  cursor: pointer;
  z-index: 1010;
}

/* line 134, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-close, .fancybox-prev span, .fancybox-next span {
  background-image: url("/static_1/lib/fancybox/source/fancybox_sprite.png");
}

/* line 138, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-close {
  position: absolute;
  top: -18px;
  right: -18px;
  width: 36px;
  height: 36px;
  cursor: pointer;
  z-index: 1004;
}

/* line 148, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-prev, .fancybox-next {
  position: absolute;
  top: 0;
  width: 40%;
  height: 100%;
  cursor: pointer;
  background: transparent url("/static_1/lib/fancybox/source/blank.gif");
  /* helps IE */
  z-index: 1003;
}

/* line 158, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-prev {
  left: 0;
}

/* line 162, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-next {
  right: 0;
}

/* line 166, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-prev span, .fancybox-next span {
  position: absolute;
  top: 50%;
  left: -9999px;
  width: 36px;
  height: 36px;
  margin-top: -18px;
  cursor: pointer;
  z-index: 1003;
}

/* line 177, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-prev span {
  background-position: 0 -36px;
}

/* line 181, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-next span {
  background-position: 0 -72px;
}

/* line 185, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-prev:hover, .fancybox-next:hover {
  visibility: visible;
}

/* line 189, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-prev:hover span {
  left: 20px;
}

/* line 193, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-next:hover span {
  left: auto;
  right: 20px;
}

/* line 198, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-tmp {
  position: absolute;
  top: -9999px;
  left: -9999px;
  padding: 0;
  overflow: visible;
  visibility: hidden;
}

/* Overlay helper */
/* line 209, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-overlay {
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  display: none;
  z-index: 1001;
  background: #000;
}

/* Title helper */
/* line 221, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-title {
  visibility: hidden;
  font: normal 13px/20px "Helvetica Neue",Helvetica,Arial,sans-serif;
  position: relative;
  text-shadow: none;
  z-index: 1005;
}

/* line 229, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-opened .fancybox-title {
  visibility: visible;
}

/* line 233, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-title-float-wrap {
  position: absolute;
  bottom: 0;
  right: 50%;
  margin-bottom: -35px;
  z-index: 1003;
  text-align: center;
}

/* line 242, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-title-float-wrap .child {
  display: inline-block;
  margin-right: -100%;
  padding: 2px 20px;
  background: transparent;
  /* Fallback for web browsers that doesn't support RGBa */
  background: rgba(0, 0, 0, 0.8);
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  border-radius: 15px;
  text-shadow: 0 1px 2px #222;
  color: #FFF;
  font-weight: bold;
  line-height: 24px;
  white-space: nowrap;
}

/* line 258, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-title-outside-wrap {
  position: relative;
  margin-top: 10px;
  color: #fff;
}

/* line 264, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-title-inside-wrap {
  margin-top: 10px;
}

/* line 268, ../../../../sass/plugins/_templateBaseline.scss */
.fancybox-title-over-wrap {
  position: absolute;
  bottom: 0;
  left: 0;
  color: #fff;
  padding: 10px;
  background: #000;
  background: rgba(0, 0, 0, 0.8);
}

/* line 279, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-buttons {
  position: fixed;
  left: 0;
  width: 100%;
  z-index: 1005;
}

/* line 286, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-buttons.top {
  top: 10px;
}

/* line 290, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-buttons.bottom {
  bottom: 10px;
}

/* line 294, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-buttons ul {
  display: block;
  width: 170px;
  height: 30px;
  margin: 0 auto;
  padding: 0;
  list-style: none;
  background: #111;
  -webkit-box-shadow: 0 1px 3px #000, 0 0 0 1px rgba(0, 0, 0, 0.7), inset 0 0 0 1px rgba(255, 255, 255, 0.05);
  -moz-box-shadow: 0 1px 3px #000, 0 0 0 1px rgba(0, 0, 0, 0.7), inset 0 0 0 1px rgba(255, 255, 255, 0.05);
  background: #111 -webkit-gradient(linear, 0% 0%, 0% 100%, from(rgba(255, 255, 255, 0.2)), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.5, rgba(255, 255, 255, 0.1)), to(rgba(255, 255, 255, 0.15)));
  background: #111 -moz-linear-gradient(top, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.15) 100%);
  border-radius: 3px;
}

/* line 309, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-buttons ul li {
  float: left;
  margin: 0;
  padding: 0;
}

/* line 315, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-buttons a {
  display: block;
  width: 30px;
  height: 30px;
  text-indent: -9999px;
  background-repeat: no-repeat;
  outline: none;
}

/* line 325, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-buttons a.btnPrev {
  width: 32px;
  background-position: 6px 0;
}

/* line 330, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-buttons a.btnNext {
  background-position: -33px 0;
  border-right: 1px solid #3e3e3e;
}

/* line 335, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-buttons a.btnPlay {
  background-position: 0 -30px;
}

/* line 339, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-buttons a.btnPlayOn {
  background-position: -30px -30px;
}

/* line 343, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-buttons a.btnToggle {
  background-position: 3px -60px;
  border-left: 1px solid #111;
  border-right: 1px solid #3e3e3e;
  width: 35px;
}

/* line 350, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-buttons a.btnToggleOn {
  background-position: -27px -60px;
}

/* line 354, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-buttons a.btnClose {
  border-left: 1px solid #111;
  width: 38px;
  background-position: -57px 0px;
}

/* line 360, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-buttons a.btnDisabled {
  opacity: 0.5;
  cursor: default;
}

/* line 365, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-thumbs {
  position: fixed;
  left: 0px;
  width: 100%;
  overflow: hidden;
  z-index: 1005;
}

/* line 373, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-thumbs.bottom {
  bottom: 2px;
}

/* line 377, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-thumbs.top {
  top: 2px;
}

/* line 381, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-thumbs ul {
  position: relative;
  list-style: none;
  margin: 0;
  padding: 0;
}

/* line 388, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-thumbs ul li {
  float: left;
  padding: 1px;
  opacity: 0.5;
}

/* line 394, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-thumbs ul li.active {
  opacity: 0.75;
  padding: 0;
  border: 1px solid #fff;
}

/* line 400, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-thumbs ul li:hover {
  opacity: 1;
}

/* line 404, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-thumbs ul li a {
  display: block;
  position: relative;
  overflow: hidden;
  border: 1px solid #222;
  background: #111;
  outline: none;
}

/* line 413, ../../../../sass/plugins/_templateBaseline.scss */
#fancybox-thumbs ul li img {
  display: block;
  position: relative;
  border: 0;
  padding: 0;
}

/* line 420, ../../../../sass/plugins/_templateBaseline.scss */
#pikame {
  display: none;
}

/* line 423, ../../../../sass/plugins/_templateBaseline.scss */
.pika-textnav {
  display: none;
}

/* line 426, ../../../../sass/plugins/_templateBaseline.scss */
.pika-counter {
  display: none;
}

/* line 429, ../../../../sass/plugins/_templateBaseline.scss */
.pika-stage img {
  width: 683px;
  height: 358px;
}

/*social icons*/
/*================================================================================*/
/*========DO NOT EDIT BELOW HERE UNLESS YOU KNOW WHAT YOU ARE DOING===============*/
/*================================================================================*/
/*========================================*/
/*========COMMON STYLING==================*/
/*========================================*/
/* line 443, ../../../../sass/plugins/_templateBaseline.scss */
.social ul, .social_small ul {
  list-style: none;
  margin: 0 auto;
  padding: 0;
}

/* line 444, ../../../../sass/plugins/_templateBaseline.scss */
.social ul.inlined, .social_small ul.inlined {
  float: left;
}

/* line 445, ../../../../sass/plugins/_templateBaseline.scss */
.social ul li {
  width: 32px;
  height: 32px;
  margin: 5px;
  text-indent: -9999px;
}

/* line 446, ../../../../sass/plugins/_templateBaseline.scss */
.social ul li a, .social_small ul li a {
  display: block;
  width: 100%;
  height: 100%;
  opacity: 1;
  -moz-opacity: 1;
  filter: alpha(opacity=100);
}

/* line 454, ../../../../sass/plugins/_templateBaseline.scss */
.social ul li a:hover, .social_small ul li a:hover {
  /* 	opacity: 1;-moz-opacity: 1;filter:alpha(opacity=1); */
}

/* line 458, ../../../../sass/plugins/_templateBaseline.scss */
.social_small ul li {
  width: 16px;
  height: 16px;
  margin: 5px;
  text-indent: -9999px;
}

/*========================================*/
/*========SOCIAL LARGE SIZE===============*/
/*========================================*/
/* line 463, ../../../../sass/plugins/_templateBaseline.scss */
a.twitter, a.facebook, a.flickr, a.friendfeed, a.delicious, a.digg, a.lastfm, a.youtube, a.feed, a.linked-in {
  background: url(/static_1/images/social_icons.png);
}

/* line 467, ../../../../sass/plugins/_templateBaseline.scss */
.social ul a.twitter {
  background-position: 0px 0px;
}

/* line 468, ../../../../sass/plugins/_templateBaseline.scss */
.social ul a.facebook {
  background-position: 0px -42px;
}

/* line 469, ../../../../sass/plugins/_templateBaseline.scss */
.social ul a.flickr {
  background-position: 0px -84px;
}

/* line 470, ../../../../sass/plugins/_templateBaseline.scss */
.social ul a.friendfeed {
  background-position: 0px -126px;
}

/* line 471, ../../../../sass/plugins/_templateBaseline.scss */
.social ul a.delicious {
  background-position: 0px -168px;
}

/* line 472, ../../../../sass/plugins/_templateBaseline.scss */
.social ul a.digg {
  background-position: 0px -210px;
}

/* line 473, ../../../../sass/plugins/_templateBaseline.scss */
.social ul a.lastfm {
  background-position: 0px -252px;
}

/* line 474, ../../../../sass/plugins/_templateBaseline.scss */
.social ul a.linked-in {
  background-position: 0px -294px;
}

/* line 475, ../../../../sass/plugins/_templateBaseline.scss */
.social ul a.youtube {
  background-position: 0px -336px;
}

/* line 476, ../../../../sass/plugins/_templateBaseline.scss */
.social ul a.feed {
  background-position: 0px -378px;
}

/*========================================*/
/*========SOCIAL SMALL SIZE===============*/
/*========================================*/
/* line 481, ../../../../sass/plugins/_templateBaseline.scss */
.social_small ul a.twitter, .social_small ul a.facebook, .social_small ul a.flickr, .social_small ul a.friendfeed, .social_small ul a.delicious, .social_small ul a.digg, .social_small ul a.lastfm, .social_small ul a.youtube, .social_small ul a.feed, .social_small ul a.linked-in {
  background: url(/static_1/images/social_icons_small.png);
}

/* line 485, ../../../../sass/plugins/_templateBaseline.scss */
.social_small ul a.twitter {
  background-position: 0px 0px;
}

/* line 486, ../../../../sass/plugins/_templateBaseline.scss */
.social_small ul a.facebook {
  background-position: 0px -21px;
}

/* line 487, ../../../../sass/plugins/_templateBaseline.scss */
.social_small ul a.flickr {
  background-position: 0px -42px;
}

/* line 488, ../../../../sass/plugins/_templateBaseline.scss */
.social_small ul a.friendfeed {
  background-position: 0px -78px;
}

/* line 489, ../../../../sass/plugins/_templateBaseline.scss */
.social_small ul a.delicious {
  background-position: 0px -104px;
}

/* line 490, ../../../../sass/plugins/_templateBaseline.scss */
.social_small ul a.digg {
  background-position: 0px -130px;
}

/* line 491, ../../../../sass/plugins/_templateBaseline.scss */
.social_small ul a.lastfm {
  background-position: 0px -156px;
}

/* line 492, ../../../../sass/plugins/_templateBaseline.scss */
.social_small ul a.linked-in {
  background-position: 0px -182px;
}

/* line 493, ../../../../sass/plugins/_templateBaseline.scss */
.social_small ul a.youtube {
  background-position: 0px -168px;
}

/* line 494, ../../../../sass/plugins/_templateBaseline.scss */
.social_small ul a.feed {
  background-position: 0px -234px;
}

/**
 * Owl Carousel v2.2.1
 * Copyright 2013-2017 David Deutsch
 * Licensed under  ()
 */
/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel, .owl-carousel .owl-item {
  -webkit-tap-highlight-color: transparent;
  position: relative;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel {
  display: none;
  width: 100%;
  z-index: 1;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage {
  position: relative;
  -ms-touch-action: pan-Y;
  -moz-backface-visibility: hidden;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage:after {
  content: ".";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage-outer {
  position: relative;
  overflow: hidden;
  -webkit-transform: translate3d(0, 0, 0);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item, .owl-carousel .owl-wrapper {
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item {
  min-height: 1px;
  float: left;
  -webkit-backface-visibility: hidden;
  -webkit-touch-callout: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item img {
  display: block;
  width: 100%;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-dots.disabled, .owl-carousel .owl-nav.disabled {
  display: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.no-js .owl-carousel, .owl-carousel.owl-loaded {
  display: block;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-dot, .owl-carousel .owl-nav .owl-next, .owl-carousel .owl-nav .owl-prev {
  cursor: pointer;
  cursor: hand;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-loading {
  opacity: 0;
  display: block;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-hidden {
  opacity: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-refresh .owl-item {
  visibility: hidden;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-drag .owl-item {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-grab {
  cursor: move;
  cursor: grab;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-rtl {
  direction: rtl;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-rtl .owl-item {
  float: right;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-animated-in {
  z-index: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-animated-out {
  z-index: 1;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .fadeOut {
  animation-name: fadeOut;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-height {
  transition: height .5s ease-in-out;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item .owl-lazy {
  opacity: 0;
  transition: opacity .4s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item img.owl-lazy {
  transform-style: preserve-3d;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-wrapper {
  position: relative;
  height: 100%;
  background: #000;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-play-icon {
  position: absolute;
  height: 80px;
  width: 80px;
  left: 50%;
  top: 50%;
  margin-left: -40px;
  margin-top: -40px;
  background: url(owl.video.play.png) no-repeat;
  cursor: pointer;
  z-index: 1;
  -webkit-backface-visibility: hidden;
  transition: transform .1s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-play-icon:hover {
  -ms-transform: scale(1.3, 1.3);
  transform: scale(1.3, 1.3);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-playing .owl-video-play-icon, .owl-carousel .owl-video-playing .owl-video-tn {
  display: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-tn {
  opacity: 0;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: opacity .4s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-frame {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
}

/* line 8, ../../../../sass/plugins/_effects.scss */
.effects_sass {
  visibility: hidden;
}

/*=== Translate None with fadeOut ===*/
/*=== Translate From Bottom with fadeOut ===*/
@-webkit-keyframes slide_up {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-moz-keyframes slide_up {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-o-keyframes slide_up {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@keyframes slide_up {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
/* line 84, ../../../../sass/plugins/_effects.scss */
.slide_up_effect {
  -webkit-animation: slide_up 1s;
  /* Safari 4+ */
  -moz-animation: slide_up 1s;
  /* Fx 5+ */
  -o-animation: slide_up 1s;
  /* Opera 12+ */
  animation: slide_up 1s;
}

@-webkit-keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-moz-keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-o-keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
/* line 125, ../../../../sass/plugins/_effects.scss */
.slide_right_effect {
  -webkit-animation: slide_right 1s;
  /* Safari 4+ */
  -moz-animation: slide_right 1s;
  /* Fx 5+ */
  -o-animation: slide_right 1s;
  /* Opera 12+ */
  animation: slide_right 1s;
}

@-webkit-keyframes slide_left {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0%, 0);
    -moz-transform: translate3d(100%, 0%, 0);
    -ms-transform: translate3d(100%, 0%, 0);
    -o-transform: translate3d(100%, 0%, 0);
    transform: translate3d(100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-moz-keyframes slide_left {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0%, 0);
    -moz-transform: translate3d(100%, 0%, 0);
    -ms-transform: translate3d(100%, 0%, 0);
    -o-transform: translate3d(100%, 0%, 0);
    transform: translate3d(100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-o-keyframes slide_left {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0%, 0);
    -moz-transform: translate3d(100%, 0%, 0);
    -ms-transform: translate3d(100%, 0%, 0);
    -o-transform: translate3d(100%, 0%, 0);
    transform: translate3d(100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@keyframes slide_left {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0%, 0);
    -moz-transform: translate3d(100%, 0%, 0);
    -ms-transform: translate3d(100%, 0%, 0);
    -o-transform: translate3d(100%, 0%, 0);
    transform: translate3d(100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
/* line 165, ../../../../sass/plugins/_effects.scss */
.slide_left_effect {
  -webkit-animation: slide_left 1s;
  /* Safari 4+ */
  -moz-animation: slide_left 1s;
  /* Fx 5+ */
  -o-animation: slide_left 1s;
  /* Opera 12+ */
  animation: slide_left 1s;
}

/*=== Translate From Bottom witout fadeOut ===*/
@-webkit-keyframes slide_up_no_fade_out {
  0% {
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-moz-keyframes slide_up_no_fade_out {
  0% {
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-o-keyframes slide_up_no_fade_out {
  0% {
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@keyframes slide_up_no_fade_out {
  0% {
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
/* line 222, ../../../../sass/plugins/_effects.scss */
.slide_up_no_fade_out_effect {
  -webkit-animation: slide_up_no_fade_out 1s;
  /* Safari 4+ */
  -moz-animation: slide_up_no_fade_out 1s;
  /* Fx 5+ */
  -o-animation: slide_up_no_fade_out 1s;
  /* Opera 12+ */
  animation: slide_up_no_fade_out 1s;
}

/*=== Fade Out Effect ===*/
@-webkit-keyframes fade_out_effect {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-moz-keyframes fade_out_effect {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-o-keyframes fade_out_effect {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fade_out_effect {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* line 271, ../../../../sass/plugins/_effects.scss */
.fade_out_trigger {
  -webkit-animation: fade_out_effect 1.5s;
  /* Safari 4+ */
  -moz-animation: fade_out_effect 1.5s;
  /* Fx 5+ */
  -o-animation: fade_out_effect 1.5s;
  /* Opera 12+ */
  animation: fade_out_effect 1.5s;
}

/*=== Pendule Effect ===*/
/*=== initial transform ===*/
@-webkit-keyframes pendule {
  0% {
    -webkit-transform: rotate(10deg);
    -moz-transform: rotate(10deg);
    -ms-transform: rotate(10deg);
    -o-transform: rotate(10deg);
    transform: rotate(10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
  100% {
    -webkit-transform: rotate(-10deg);
    -moz-transform: rotate(-10deg);
    -ms-transform: rotate(-10deg);
    -o-transform: rotate(-10deg);
    transform: rotate(-10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
}
@-moz-keyframes pendule {
  0% {
    -webkit-transform: rotate(10deg);
    -moz-transform: rotate(10deg);
    -ms-transform: rotate(10deg);
    -o-transform: rotate(10deg);
    transform: rotate(10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
  100% {
    -webkit-transform: rotate(-10deg);
    -moz-transform: rotate(-10deg);
    -ms-transform: rotate(-10deg);
    -o-transform: rotate(-10deg);
    transform: rotate(-10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
}
@-o-keyframes pendule {
  0% {
    -webkit-transform: rotate(10deg);
    -moz-transform: rotate(10deg);
    -ms-transform: rotate(10deg);
    -o-transform: rotate(10deg);
    transform: rotate(10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
  100% {
    -webkit-transform: rotate(-10deg);
    -moz-transform: rotate(-10deg);
    -ms-transform: rotate(-10deg);
    -o-transform: rotate(-10deg);
    transform: rotate(-10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
}
@keyframes pendule {
  0% {
    -webkit-transform: rotate(10deg);
    -moz-transform: rotate(10deg);
    -ms-transform: rotate(10deg);
    -o-transform: rotate(10deg);
    transform: rotate(10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
  100% {
    -webkit-transform: rotate(-10deg);
    -moz-transform: rotate(-10deg);
    -ms-transform: rotate(-10deg);
    -o-transform: rotate(-10deg);
    transform: rotate(-10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
}
/* line 324, ../../../../sass/plugins/_effects.scss */
.pendule {
  -webkit-animation: pendule 2s ease-in-out 0s infinite alternate;
  /* Safari 4+ */
  -moz-animation: pendule 2s ease-in-out 0s infinite alternate;
  /* Fx 5+ */
  -o-animation: pendule 2s ease-in-out 0s infinite alternate;
  /* Opera 12+ */
  animation: pendule 2s ease-in-out 0s infinite alternate;
}

/*=== Text Translate ===*/
/* line 329, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom {
  overflow: hidden;
  position: relative;
}
/* line 333, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom::after {
  content: attr(data-content);
  left: 0;
  position: absolute;
  -webkit-transition: transform 0.6s;
  -moz-transition: transform 0.6s;
  -ms-transition: transform 0.6s;
  -o-transition: transform 0.6s;
  transition: transform 0.6s;
  -webkit-transform: translate(0, 100%);
  -moz-transform: translate(0, 100%);
  -ms-transform: translate(0, 100%);
  -o-transform: translate(0, 100%);
  transform: translate(0, 100%);
}
/* line 341, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom span {
  -webkit-transition: transform 0.6s;
  -moz-transition: transform 0.6s;
  -ms-transition: transform 0.6s;
  -o-transition: transform 0.6s;
  transition: transform 0.6s;
  -webkit-transform: translate(0, 0);
  -moz-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
}
/* line 347, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom:hover::after {
  -webkit-transform: translate(0, 0%);
  -moz-transform: translate(0, 0%);
  -ms-transform: translate(0, 0%);
  -o-transform: translate(0, 0%);
  transform: translate(0, 0%);
}
/* line 351, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom:hover span {
  -webkit-transform: translate(0, -100%);
  -moz-transform: translate(0, -100%);
  -ms-transform: translate(0, -100%);
  -o-transform: translate(0, -100%);
  transform: translate(0, -100%);
}

/*=== Flip Effect ===*/
@-webkit-keyframes flip {
  0% {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
  100% {
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
}
@-moz-keyframes flip {
  0% {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
  100% {
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
}
@-o-keyframes flip {
  0% {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
  100% {
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
}
@keyframes flip {
  0% {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
  100% {
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
}
/* line 406, ../../../../sass/plugins/_effects.scss */
.flip {
  -webkit-animation: flip 3s ease-in-out 0s infinite;
  /* Safari 4+ */
  -moz-animation: flip 3s ease-in-out 0s infinite;
  /* Fx 5+ */
  -o-animation: flip 3s ease-in-out 0s infinite;
  /* Opera 12+ */
  animation: flip 3s ease-in-out 0s infinite;
}

/*=== moveBright Effect ===*/
@-webkit-keyframes moveBrightToRight {
  0% {
    left: -500%;
  }
  100% {
    left: 500%;
  }
}
@-moz-keyframes moveBrightToRight {
  0% {
    left: -500%;
  }
  100% {
    left: 500%;
  }
}
@-o-keyframes moveBrightToRight {
  0% {
    left: -500%;
  }
  100% {
    left: 500%;
  }
}
@keyframes moveBrightToRight {
  0% {
    left: -500%;
  }
  100% {
    left: 500%;
  }
}
/*!
 * animate.css -http://daneden.me/animate
 * Version - 3.6.0
 * Licensed under the MIT license - http://opensource.org/licenses/MIT
 *
 * Copyright (c) 2018 Daniel Eden
 */
/* line 11, ../../../../sass/plugins/_animate.scss */
.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

/* line 18, ../../../../sass/plugins/_animate.scss */
.animated.infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

@-webkit-keyframes bounce {
  from,
  20%,
  53%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0);
  }
}
@keyframes bounce {
  from,
  20%,
  53%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0);
  }
}
/* line 89, ../../../../sass/plugins/_animate.scss */
.bounce {
  -webkit-animation-name: bounce;
  animation-name: bounce;
  -webkit-transform-origin: center bottom;
  transform-origin: center bottom;
}

@-webkit-keyframes flash {
  from,
  50%,
  to {
    opacity: 1;
  }
  25%,
  75% {
    opacity: 0;
  }
}
@keyframes flash {
  from,
  50%,
  to {
    opacity: 1;
  }
  25%,
  75% {
    opacity: 0;
  }
}
/* line 122, ../../../../sass/plugins/_animate.scss */
.flash {
  -webkit-animation-name: flash;
  animation-name: flash;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@-webkit-keyframes pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
/* line 163, ../../../../sass/plugins/_animate.scss */
.pulse {
  -webkit-animation-name: pulse;
  animation-name: pulse;
}

@-webkit-keyframes rubberBand {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes rubberBand {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
/* line 242, ../../../../sass/plugins/_animate.scss */
.rubberBand {
  -webkit-animation-name: rubberBand;
  animation-name: rubberBand;
}

@-webkit-keyframes shake {
  from,
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
@keyframes shake {
  from,
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
/* line 297, ../../../../sass/plugins/_animate.scss */
.shake {
  -webkit-animation-name: shake;
  animation-name: shake;
}

@-webkit-keyframes headShake {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  6.5% {
    -webkit-transform: translateX(-6px) rotateY(-9deg);
    transform: translateX(-6px) rotateY(-9deg);
  }
  18.5% {
    -webkit-transform: translateX(5px) rotateY(7deg);
    transform: translateX(5px) rotateY(7deg);
  }
  31.5% {
    -webkit-transform: translateX(-3px) rotateY(-5deg);
    transform: translateX(-3px) rotateY(-5deg);
  }
  43.5% {
    -webkit-transform: translateX(2px) rotateY(3deg);
    transform: translateX(2px) rotateY(3deg);
  }
  50% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@keyframes headShake {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  6.5% {
    -webkit-transform: translateX(-6px) rotateY(-9deg);
    transform: translateX(-6px) rotateY(-9deg);
  }
  18.5% {
    -webkit-transform: translateX(5px) rotateY(7deg);
    transform: translateX(5px) rotateY(7deg);
  }
  31.5% {
    -webkit-transform: translateX(-3px) rotateY(-5deg);
    transform: translateX(-3px) rotateY(-5deg);
  }
  43.5% {
    -webkit-transform: translateX(2px) rotateY(3deg);
    transform: translateX(2px) rotateY(3deg);
  }
  50% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
/* line 366, ../../../../sass/plugins/_animate.scss */
.headShake {
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-name: headShake;
  animation-name: headShake;
}

@-webkit-keyframes swing {
  20% {
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    -webkit-transform: rotate3d(0, 0, 1, -10deg);
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    -webkit-transform: rotate3d(0, 0, 1, -5deg);
    transform: rotate3d(0, 0, 1, -5deg);
  }
  to {
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg);
  }
}
@keyframes swing {
  20% {
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    -webkit-transform: rotate3d(0, 0, 1, -10deg);
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    -webkit-transform: rotate3d(0, 0, 1, -5deg);
    transform: rotate3d(0, 0, 1, -5deg);
  }
  to {
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg);
  }
}
/* line 427, ../../../../sass/plugins/_animate.scss */
.swing {
  -webkit-transform-origin: top center;
  transform-origin: top center;
  -webkit-animation-name: swing;
  animation-name: swing;
}

@-webkit-keyframes tada {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  10%,
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%,
  60%,
  80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes tada {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  10%,
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%,
  60%,
  80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
/* line 500, ../../../../sass/plugins/_animate.scss */
.tada {
  -webkit-animation-name: tada;
  animation-name: tada;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@-webkit-keyframes wobble {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes wobble {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 581, ../../../../sass/plugins/_animate.scss */
.wobble {
  -webkit-animation-name: wobble;
  animation-name: wobble;
}

@-webkit-keyframes jello {
  from,
  11.1%,
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  22.2% {
    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
    transform: skewX(-12.5deg) skewY(-12.5deg);
  }
  33.3% {
    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
    transform: skewX(6.25deg) skewY(6.25deg);
  }
  44.4% {
    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
    transform: skewX(-3.125deg) skewY(-3.125deg);
  }
  55.5% {
    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
    transform: skewX(1.5625deg) skewY(1.5625deg);
  }
  66.6% {
    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
  }
  77.7% {
    -webkit-transform: skewX(0.39063deg) skewY(0.39063deg);
    transform: skewX(0.39063deg) skewY(0.39063deg);
  }
  88.8% {
    -webkit-transform: skewX(-0.19531deg) skewY(-0.19531deg);
    transform: skewX(-0.19531deg) skewY(-0.19531deg);
  }
}
@keyframes jello {
  from,
  11.1%,
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  22.2% {
    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
    transform: skewX(-12.5deg) skewY(-12.5deg);
  }
  33.3% {
    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
    transform: skewX(6.25deg) skewY(6.25deg);
  }
  44.4% {
    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
    transform: skewX(-3.125deg) skewY(-3.125deg);
  }
  55.5% {
    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
    transform: skewX(1.5625deg) skewY(1.5625deg);
  }
  66.6% {
    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
  }
  77.7% {
    -webkit-transform: skewX(0.39063deg) skewY(0.39063deg);
    transform: skewX(0.39063deg) skewY(0.39063deg);
  }
  88.8% {
    -webkit-transform: skewX(-0.19531deg) skewY(-0.19531deg);
    transform: skewX(-0.19531deg) skewY(-0.19531deg);
  }
}
/* line 674, ../../../../sass/plugins/_animate.scss */
.jello {
  -webkit-animation-name: jello;
  animation-name: jello;
  -webkit-transform-origin: center;
  transform-origin: center;
}

@-webkit-keyframes bounceIn {
  from,
  20%,
  40%,
  60%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97);
  }
  to {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes bounceIn {
  from,
  20%,
  40%,
  60%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97);
  }
  to {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
/* line 771, ../../../../sass/plugins/_animate.scss */
.bounceIn {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-name: bounceIn;
  animation-name: bounceIn;
}

@-webkit-keyframes bounceInDown {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -3000px, 0);
    transform: translate3d(0, -3000px, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, 25px, 0);
    transform: translate3d(0, 25px, 0);
  }
  75% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes bounceInDown {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -3000px, 0);
    transform: translate3d(0, -3000px, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, 25px, 0);
    transform: translate3d(0, 25px, 0);
  }
  75% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 854, ../../../../sass/plugins/_animate.scss */
.bounceInDown {
  -webkit-animation-name: bounceInDown;
  animation-name: bounceInDown;
}

@-webkit-keyframes bounceInLeft {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-3000px, 0, 0);
    transform: translate3d(-3000px, 0, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(25px, 0, 0);
    transform: translate3d(25px, 0, 0);
  }
  75% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  90% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes bounceInLeft {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-3000px, 0, 0);
    transform: translate3d(-3000px, 0, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(25px, 0, 0);
    transform: translate3d(25px, 0, 0);
  }
  75% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  90% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 935, ../../../../sass/plugins/_animate.scss */
.bounceInLeft {
  -webkit-animation-name: bounceInLeft;
  animation-name: bounceInLeft;
}

@-webkit-keyframes bounceInRight {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    -webkit-transform: translate3d(3000px, 0, 0);
    transform: translate3d(3000px, 0, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(-25px, 0, 0);
    transform: translate3d(-25px, 0, 0);
  }
  75% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes bounceInRight {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    -webkit-transform: translate3d(3000px, 0, 0);
    transform: translate3d(3000px, 0, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(-25px, 0, 0);
    transform: translate3d(-25px, 0, 0);
  }
  75% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 1016, ../../../../sass/plugins/_animate.scss */
.bounceInRight {
  -webkit-animation-name: bounceInRight;
  animation-name: bounceInRight;
}

@-webkit-keyframes bounceInUp {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 3000px, 0);
    transform: translate3d(0, 3000px, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }
  75% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes bounceInUp {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 3000px, 0);
    transform: translate3d(0, 3000px, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }
  75% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 1097, ../../../../sass/plugins/_animate.scss */
.bounceInUp {
  -webkit-animation-name: bounceInUp;
  animation-name: bounceInUp;
}

@-webkit-keyframes bounceOut {
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }
  50%,
  55% {
    opacity: 1;
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
}
@keyframes bounceOut {
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }
  50%,
  55% {
    opacity: 1;
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
}
/* line 1142, ../../../../sass/plugins/_animate.scss */
.bounceOut {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-name: bounceOut;
  animation-name: bounceOut;
}

@-webkit-keyframes bounceOutDown {
  20% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
  40%,
  45% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}
@keyframes bounceOutDown {
  20% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
  40%,
  45% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}
/* line 1189, ../../../../sass/plugins/_animate.scss */
.bounceOutDown {
  -webkit-animation-name: bounceOutDown;
  animation-name: bounceOutDown;
}

@-webkit-keyframes bounceOutLeft {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(20px, 0, 0);
    transform: translate3d(20px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}
@keyframes bounceOutLeft {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(20px, 0, 0);
    transform: translate3d(20px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}
/* line 1222, ../../../../sass/plugins/_animate.scss */
.bounceOutLeft {
  -webkit-animation-name: bounceOutLeft;
  animation-name: bounceOutLeft;
}

@-webkit-keyframes bounceOutRight {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(-20px, 0, 0);
    transform: translate3d(-20px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}
@keyframes bounceOutRight {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(-20px, 0, 0);
    transform: translate3d(-20px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}
/* line 1255, ../../../../sass/plugins/_animate.scss */
.bounceOutRight {
  -webkit-animation-name: bounceOutRight;
  animation-name: bounceOutRight;
}

@-webkit-keyframes bounceOutUp {
  20% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }
  40%,
  45% {
    opacity: 1;
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}
@keyframes bounceOutUp {
  20% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }
  40%,
  45% {
    opacity: 1;
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}
/* line 1300, ../../../../sass/plugins/_animate.scss */
.bounceOutUp {
  -webkit-animation-name: bounceOutUp;
  animation-name: bounceOutUp;
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
/* line 1325, ../../../../sass/plugins/_animate.scss */
.fadeIn {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}

@-webkit-keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 1358, ../../../../sass/plugins/_animate.scss */
.fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
}

@-webkit-keyframes fadeInDownBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fadeInDownBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 1391, ../../../../sass/plugins/_animate.scss */
.fadeInDownBig {
  -webkit-animation-name: fadeInDownBig;
  animation-name: fadeInDownBig;
}

@-webkit-keyframes fadeInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fadeInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 1424, ../../../../sass/plugins/_animate.scss */
.fadeInLeft {
  -webkit-animation-name: fadeInLeft;
  animation-name: fadeInLeft;
}

@-webkit-keyframes fadeInLeftBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fadeInLeftBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 1457, ../../../../sass/plugins/_animate.scss */
.fadeInLeftBig {
  -webkit-animation-name: fadeInLeftBig;
  animation-name: fadeInLeftBig;
}

@-webkit-keyframes fadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 1490, ../../../../sass/plugins/_animate.scss */
.fadeInRight {
  -webkit-animation-name: fadeInRight;
  animation-name: fadeInRight;
}

@-webkit-keyframes fadeInRightBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fadeInRightBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 1523, ../../../../sass/plugins/_animate.scss */
.fadeInRightBig {
  -webkit-animation-name: fadeInRightBig;
  animation-name: fadeInRightBig;
}

@-webkit-keyframes fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 1556, ../../../../sass/plugins/_animate.scss */
.fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}

@-webkit-keyframes fadeInUpBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fadeInUpBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 1589, ../../../../sass/plugins/_animate.scss */
.fadeInUpBig {
  -webkit-animation-name: fadeInUpBig;
  animation-name: fadeInUpBig;
}

@-webkit-keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
/* line 1614, ../../../../sass/plugins/_animate.scss */
.fadeOut {
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut;
}

@-webkit-keyframes fadeOutDown {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
@keyframes fadeOutDown {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
/* line 1643, ../../../../sass/plugins/_animate.scss */
.fadeOutDown {
  -webkit-animation-name: fadeOutDown;
  animation-name: fadeOutDown;
}

@-webkit-keyframes fadeOutDownBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}
@keyframes fadeOutDownBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}
/* line 1672, ../../../../sass/plugins/_animate.scss */
.fadeOutDownBig {
  -webkit-animation-name: fadeOutDownBig;
  animation-name: fadeOutDownBig;
}

@-webkit-keyframes fadeOutLeft {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
@keyframes fadeOutLeft {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
/* line 1701, ../../../../sass/plugins/_animate.scss */
.fadeOutLeft {
  -webkit-animation-name: fadeOutLeft;
  animation-name: fadeOutLeft;
}

@-webkit-keyframes fadeOutLeftBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}
@keyframes fadeOutLeftBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}
/* line 1730, ../../../../sass/plugins/_animate.scss */
.fadeOutLeftBig {
  -webkit-animation-name: fadeOutLeftBig;
  animation-name: fadeOutLeftBig;
}

@-webkit-keyframes fadeOutRight {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
@keyframes fadeOutRight {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
/* line 1759, ../../../../sass/plugins/_animate.scss */
.fadeOutRight {
  -webkit-animation-name: fadeOutRight;
  animation-name: fadeOutRight;
}

@-webkit-keyframes fadeOutRightBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}
@keyframes fadeOutRightBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}
/* line 1788, ../../../../sass/plugins/_animate.scss */
.fadeOutRightBig {
  -webkit-animation-name: fadeOutRightBig;
  animation-name: fadeOutRightBig;
}

@-webkit-keyframes fadeOutUp {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
@keyframes fadeOutUp {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
/* line 1817, ../../../../sass/plugins/_animate.scss */
.fadeOutUp {
  -webkit-animation-name: fadeOutUp;
  animation-name: fadeOutUp;
}

@-webkit-keyframes fadeOutUpBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}
@keyframes fadeOutUpBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}
/* line 1846, ../../../../sass/plugins/_animate.scss */
.fadeOutUpBig {
  -webkit-animation-name: fadeOutUpBig;
  animation-name: fadeOutUpBig;
}

@-webkit-keyframes flip {
  from {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  40% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  50% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  80% {
    -webkit-transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
}
@keyframes flip {
  from {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  40% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  50% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  80% {
    -webkit-transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
}
/* line 1925, ../../../../sass/plugins/_animate.scss */
.animated.flip {
  -webkit-backface-visibility: visible;
  backface-visibility: visible;
  -webkit-animation-name: flip;
  animation-name: flip;
}

@-webkit-keyframes flipInX {
  from {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}
@keyframes flipInX {
  from {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}
/* line 1998, ../../../../sass/plugins/_animate.scss */
.flipInX {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipInX;
  animation-name: flipInX;
}

@-webkit-keyframes flipInY {
  from {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}
@keyframes flipInY {
  from {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}
/* line 2071, ../../../../sass/plugins/_animate.scss */
.flipInY {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipInY;
  animation-name: flipInY;
}

@-webkit-keyframes flipOutX {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
  30% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
  }
  to {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
  }
}
@keyframes flipOutX {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
  30% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
  }
  to {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
  }
}
/* line 2116, ../../../../sass/plugins/_animate.scss */
.flipOutX {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-name: flipOutX;
  animation-name: flipOutX;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flipOutY {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
  30% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1;
  }
  to {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0;
  }
}
@keyframes flipOutY {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
  30% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1;
  }
  to {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0;
  }
}
/* line 2163, ../../../../sass/plugins/_animate.scss */
.flipOutY {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipOutY;
  animation-name: flipOutY;
}

@-webkit-keyframes lightSpeedIn {
  from {
    -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
    transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0;
  }
  60% {
    -webkit-transform: skewX(20deg);
    transform: skewX(20deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: skewX(-5deg);
    transform: skewX(-5deg);
    opacity: 1;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes lightSpeedIn {
  from {
    -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
    transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0;
  }
  60% {
    -webkit-transform: skewX(20deg);
    transform: skewX(20deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: skewX(-5deg);
    transform: skewX(-5deg);
    opacity: 1;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
/* line 2224, ../../../../sass/plugins/_animate.scss */
.lightSpeedIn {
  -webkit-animation-name: lightSpeedIn;
  animation-name: lightSpeedIn;
  -webkit-animation-timing-function: ease-out;
  animation-timing-function: ease-out;
}

@-webkit-keyframes lightSpeedOut {
  from {
    opacity: 1;
  }
  to {
    -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
    transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0;
  }
}
@keyframes lightSpeedOut {
  from {
    opacity: 1;
  }
  to {
    -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
    transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0;
  }
}
/* line 2255, ../../../../sass/plugins/_animate.scss */
.lightSpeedOut {
  -webkit-animation-name: lightSpeedOut;
  animation-name: lightSpeedOut;
  -webkit-animation-timing-function: ease-in;
  animation-timing-function: ease-in;
}

@-webkit-keyframes rotateIn {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, -200deg);
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes rotateIn {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, -200deg);
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
/* line 2298, ../../../../sass/plugins/_animate.scss */
.rotateIn {
  -webkit-animation-name: rotateIn;
  animation-name: rotateIn;
}

@-webkit-keyframes rotateInDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes rotateInDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
/* line 2339, ../../../../sass/plugins/_animate.scss */
.rotateInDownLeft {
  -webkit-animation-name: rotateInDownLeft;
  animation-name: rotateInDownLeft;
}

@-webkit-keyframes rotateInDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes rotateInDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
/* line 2380, ../../../../sass/plugins/_animate.scss */
.rotateInDownRight {
  -webkit-animation-name: rotateInDownRight;
  animation-name: rotateInDownRight;
}

@-webkit-keyframes rotateInUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes rotateInUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
/* line 2421, ../../../../sass/plugins/_animate.scss */
.rotateInUpLeft {
  -webkit-animation-name: rotateInUpLeft;
  animation-name: rotateInUpLeft;
}

@-webkit-keyframes rotateInUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -90deg);
    transform: rotate3d(0, 0, 1, -90deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes rotateInUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -90deg);
    transform: rotate3d(0, 0, 1, -90deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
/* line 2462, ../../../../sass/plugins/_animate.scss */
.rotateInUpRight {
  -webkit-animation-name: rotateInUpRight;
  animation-name: rotateInUpRight;
}

@-webkit-keyframes rotateOut {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, 200deg);
    transform: rotate3d(0, 0, 1, 200deg);
    opacity: 0;
  }
}
@keyframes rotateOut {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, 200deg);
    transform: rotate3d(0, 0, 1, 200deg);
    opacity: 0;
  }
}
/* line 2499, ../../../../sass/plugins/_animate.scss */
.rotateOut {
  -webkit-animation-name: rotateOut;
  animation-name: rotateOut;
}

@-webkit-keyframes rotateOutDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
}
@keyframes rotateOutDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
}
/* line 2536, ../../../../sass/plugins/_animate.scss */
.rotateOutDownLeft {
  -webkit-animation-name: rotateOutDownLeft;
  animation-name: rotateOutDownLeft;
}

@-webkit-keyframes rotateOutDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}
@keyframes rotateOutDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}
/* line 2573, ../../../../sass/plugins/_animate.scss */
.rotateOutDownRight {
  -webkit-animation-name: rotateOutDownRight;
  animation-name: rotateOutDownRight;
}

@-webkit-keyframes rotateOutUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}
@keyframes rotateOutUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}
/* line 2610, ../../../../sass/plugins/_animate.scss */
.rotateOutUpLeft {
  -webkit-animation-name: rotateOutUpLeft;
  animation-name: rotateOutUpLeft;
}

@-webkit-keyframes rotateOutUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 90deg);
    transform: rotate3d(0, 0, 1, 90deg);
    opacity: 0;
  }
}
@keyframes rotateOutUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 90deg);
    transform: rotate3d(0, 0, 1, 90deg);
    opacity: 0;
  }
}
/* line 2647, ../../../../sass/plugins/_animate.scss */
.rotateOutUpRight {
  -webkit-animation-name: rotateOutUpRight;
  animation-name: rotateOutUpRight;
}

@-webkit-keyframes hinge {
  0% {
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  20%,
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 80deg);
    transform: rotate3d(0, 0, 1, 80deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  40%,
  80% {
    -webkit-transform: rotate3d(0, 0, 1, 60deg);
    transform: rotate3d(0, 0, 1, 60deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 1;
  }
  to {
    -webkit-transform: translate3d(0, 700px, 0);
    transform: translate3d(0, 700px, 0);
    opacity: 0;
  }
}
@keyframes hinge {
  0% {
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  20%,
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 80deg);
    transform: rotate3d(0, 0, 1, 80deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  40%,
  80% {
    -webkit-transform: rotate3d(0, 0, 1, 60deg);
    transform: rotate3d(0, 0, 1, 60deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 1;
  }
  to {
    -webkit-transform: translate3d(0, 700px, 0);
    transform: translate3d(0, 700px, 0);
    opacity: 0;
  }
}
/* line 2724, ../../../../sass/plugins/_animate.scss */
.hinge {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-name: hinge;
  animation-name: hinge;
}

@-webkit-keyframes jackInTheBox {
  from {
    opacity: 0;
    -webkit-transform: scale(0.1) rotate(30deg);
    transform: scale(0.1) rotate(30deg);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
  }
  50% {
    -webkit-transform: rotate(-10deg);
    transform: rotate(-10deg);
  }
  70% {
    -webkit-transform: rotate(3deg);
    transform: rotate(3deg);
  }
  to {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes jackInTheBox {
  from {
    opacity: 0;
    -webkit-transform: scale(0.1) rotate(30deg);
    transform: scale(0.1) rotate(30deg);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
  }
  50% {
    -webkit-transform: rotate(-10deg);
    transform: rotate(-10deg);
  }
  70% {
    -webkit-transform: rotate(3deg);
    transform: rotate(3deg);
  }
  to {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
/* line 2783, ../../../../sass/plugins/_animate.scss */
.jackInTheBox {
  -webkit-animation-name: jackInTheBox;
  animation-name: jackInTheBox;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@-webkit-keyframes rollIn {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes rollIn {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 2818, ../../../../sass/plugins/_animate.scss */
.rollIn {
  -webkit-animation-name: rollIn;
  animation-name: rollIn;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@-webkit-keyframes rollOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
  }
}
@keyframes rollOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
  }
}
/* line 2849, ../../../../sass/plugins/_animate.scss */
.rollOut {
  -webkit-animation-name: rollOut;
  animation-name: rollOut;
}

@-webkit-keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
}
@keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
}
/* line 2878, ../../../../sass/plugins/_animate.scss */
.zoomIn {
  -webkit-animation-name: zoomIn;
  animation-name: zoomIn;
}

@-webkit-keyframes zoomInDown {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomInDown {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
/* line 2919, ../../../../sass/plugins/_animate.scss */
.zoomInDown {
  -webkit-animation-name: zoomInDown;
  animation-name: zoomInDown;
}

@-webkit-keyframes zoomInLeft {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomInLeft {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
/* line 2960, ../../../../sass/plugins/_animate.scss */
.zoomInLeft {
  -webkit-animation-name: zoomInLeft;
  animation-name: zoomInLeft;
}

@-webkit-keyframes zoomInRight {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomInRight {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
/* line 3001, ../../../../sass/plugins/_animate.scss */
.zoomInRight {
  -webkit-animation-name: zoomInRight;
  animation-name: zoomInRight;
}

@-webkit-keyframes zoomInUp {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomInUp {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
/* line 3042, ../../../../sass/plugins/_animate.scss */
.zoomInUp {
  -webkit-animation-name: zoomInUp;
  animation-name: zoomInUp;
}

@-webkit-keyframes zoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
@keyframes zoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
/* line 3079, ../../../../sass/plugins/_animate.scss */
.zoomOut {
  -webkit-animation-name: zoomOut;
  animation-name: zoomOut;
}

@-webkit-keyframes zoomOutDown {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomOutDown {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
/* line 3124, ../../../../sass/plugins/_animate.scss */
.zoomOutDown {
  -webkit-animation-name: zoomOutDown;
  animation-name: zoomOutDown;
}

@-webkit-keyframes zoomOutLeft {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(-2000px, 0, 0);
    transform: scale(0.1) translate3d(-2000px, 0, 0);
    -webkit-transform-origin: left center;
    transform-origin: left center;
  }
}
@keyframes zoomOutLeft {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(-2000px, 0, 0);
    transform: scale(0.1) translate3d(-2000px, 0, 0);
    -webkit-transform-origin: left center;
    transform-origin: left center;
  }
}
/* line 3161, ../../../../sass/plugins/_animate.scss */
.zoomOutLeft {
  -webkit-animation-name: zoomOutLeft;
  animation-name: zoomOutLeft;
}

@-webkit-keyframes zoomOutRight {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(2000px, 0, 0);
    transform: scale(0.1) translate3d(2000px, 0, 0);
    -webkit-transform-origin: right center;
    transform-origin: right center;
  }
}
@keyframes zoomOutRight {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(2000px, 0, 0);
    transform: scale(0.1) translate3d(2000px, 0, 0);
    -webkit-transform-origin: right center;
    transform-origin: right center;
  }
}
/* line 3198, ../../../../sass/plugins/_animate.scss */
.zoomOutRight {
  -webkit-animation-name: zoomOutRight;
  animation-name: zoomOutRight;
}

@-webkit-keyframes zoomOutUp {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomOutUp {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
/* line 3243, ../../../../sass/plugins/_animate.scss */
.zoomOutUp {
  -webkit-animation-name: zoomOutUp;
  animation-name: zoomOutUp;
}

@-webkit-keyframes slideInDown {
  from {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInDown {
  from {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 3274, ../../../../sass/plugins/_animate.scss */
.slideInDown {
  -webkit-animation-name: slideInDown;
  animation-name: slideInDown;
}

@-webkit-keyframes slideInLeft {
  from {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInLeft {
  from {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 3305, ../../../../sass/plugins/_animate.scss */
.slideInLeft {
  -webkit-animation-name: slideInLeft;
  animation-name: slideInLeft;
}

@-webkit-keyframes slideInRight {
  from {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInRight {
  from {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 3336, ../../../../sass/plugins/_animate.scss */
.slideInRight {
  -webkit-animation-name: slideInRight;
  animation-name: slideInRight;
}

@-webkit-keyframes slideInUp {
  from {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInUp {
  from {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* line 3367, ../../../../sass/plugins/_animate.scss */
.slideInUp {
  -webkit-animation-name: slideInUp;
  animation-name: slideInUp;
}

@-webkit-keyframes slideOutDown {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
@keyframes slideOutDown {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
/* line 3398, ../../../../sass/plugins/_animate.scss */
.slideOutDown {
  -webkit-animation-name: slideOutDown;
  animation-name: slideOutDown;
}

@-webkit-keyframes slideOutLeft {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
@keyframes slideOutLeft {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
/* line 3429, ../../../../sass/plugins/_animate.scss */
.slideOutLeft {
  -webkit-animation-name: slideOutLeft;
  animation-name: slideOutLeft;
}

@-webkit-keyframes slideOutRight {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
@keyframes slideOutRight {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
/* line 3460, ../../../../sass/plugins/_animate.scss */
.slideOutRight {
  -webkit-animation-name: slideOutRight;
  animation-name: slideOutRight;
}

@-webkit-keyframes slideOutUp {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
@keyframes slideOutUp {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
/* line 3491, ../../../../sass/plugins/_animate.scss */
.slideOutUp {
  -webkit-animation-name: slideOutUp;
  animation-name: slideOutUp;
}

/* line 1, ../sass/_our_team.scss */
.our_team {
  margin-top: 80px;
  margin-bottom: 70px;
  padding: 0 5px;
}
/* line 5, ../sass/_our_team.scss */
.our_team h3 {
  color: #3A9BC8;
  font-size: 52px;
  font-weight: 100;
  line-height: 56px;
  margin-bottom: 70px;
  text-align: center;
}
/* line 13, ../sass/_our_team.scss */
.our_team .team_mate {
  background-color: #000;
  position: relative;
  overflow: hidden;
  display: inline-block;
  width: calc((100% - 30px) / 3);
  height: 250px;
  margin: 0 5px 6px;
}
/* line 21, ../sass/_our_team.scss */
.our_team .team_mate .pic {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  -webkit-transition: all 2s;
  -moz-transition: all 2s;
  -ms-transition: all 2s;
  -o-transition: all 2s;
  transition: all 2s;
}
/* line 24, ../sass/_our_team.scss */
.our_team .team_mate .pic img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  max-height: 100%;
  max-width: 100%;
}
/* line 28, ../sass/_our_team.scss */
.our_team .team_mate .pic img.background_blur {
  max-height: none;
  max-width: none;
  min-height: 100%;
  min-width: 100%;
  -webkit-filter: grayscale(30%) blur(5px);
  filter: grayscale(30%) blur(5px);
}
/* line 38, ../sass/_our_team.scss */
.our_team .team_mate .card_front {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8));
  padding: 20px;
  color: white;
  font-weight: lighter;
  transform: rotateY(0deg);
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 49, ../sass/_our_team.scss */
.our_team .team_mate .card_front i.fa, .our_team .team_mate .card_front .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv i#newsletter-button:before, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv .our_team .team_mate .card_front i#newsletter-button:before {
  position: absolute;
  right: 20px;
  bottom: 20px;
  color: #3A9BC8;
}
/* line 56, ../sass/_our_team.scss */
.our_team .team_mate .card_back {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8));
  padding: 20px;
  color: white;
  font-weight: lighter;
  transform: rotateY(180deg);
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 65, ../sass/_our_team.scss */
.our_team .team_mate .card_back .slogan {
  font-size: 30px;
  font-weight: 100;
  line-height: 35px;
  margin-bottom: 30px;
  text-align: center;
}
/* line 71, ../sass/_our_team.scss */
.our_team .team_mate .card_back .slogan i.fa, .our_team .team_mate .card_back .slogan .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv i#newsletter-button:before, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv .our_team .team_mate .card_back .slogan i#newsletter-button:before {
  color: #3A9BC8;
  margin: 0 15px;
}
/* line 76, ../sass/_our_team.scss */
.our_team .team_mate .card_back .title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  color: white;
  font-weight: lighter;
}
/* line 84, ../sass/_our_team.scss */
.our_team .team_mate .card_back .title span {
  color: #446ca9;
}
/* line 87, ../sass/_our_team.scss */
.our_team .team_mate .card_back .title i.fa, .our_team .team_mate .card_back .title .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv i#newsletter-button:before, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv .our_team .team_mate .card_back .title i#newsletter-button:before {
  position: absolute;
  right: 20px;
  bottom: 20px;
  color: #3A9BC8;
}
/* line 96, ../sass/_our_team.scss */
.our_team .team_mate:hover .pic {
  opacity: .3;
  transform: scale(1.2, 1.2);
}
/* line 100, ../sass/_our_team.scss */
.our_team .team_mate:hover .card_front {
  opacity: 0;
  transform: rotateY(180deg);
}
/* line 104, ../sass/_our_team.scss */
.our_team .team_mate:hover .card_back {
  opacity: 1;
  transform: rotateY(0deg);
}

@media (max-width: 1140px) {
  /* line 113, ../sass/_our_team.scss */
  .container12 {
    max-width: 100%;
  }
}
@media (max-width: 1000px) {
  /* line 118, ../sass/_our_team.scss */
  .our_team .team_mate {
    width: calc((100% - 20px) / 2);
  }
}
@media (max-width: 700px) {
  /* line 123, ../sass/_our_team.scss */
  .our_team .team_mate {
    display: block;
    width: 370px;
    margin: 0 auto 10px;
  }
}
/* line 1, ../sass/_template_specific.scss */
body {
  font-family: Roboto;
  background-color: #1D1D1D;
  background-image: linear-gradient(to bottom right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0) calc(50% - 1px), rgba(90, 90, 90, 0.2) 50%, rgba(0, 0, 0, 0) calc(50% + 1px), rgba(0, 0, 0, 0));
  background-size: 20px 20px;
}
/* line 6, ../sass/_template_specific.scss */
body strong {
  font-weight: 700;
}
/* line 9, ../sass/_template_specific.scss */
body .aviso_cookie {
  position: fixed;
  top: 10px;
  right: 10px;
  max-width: 530px;
  width: 100%;
  height: auto;
  padding: 20px 30px;
  background: rgba(0, 0, 0, 0.8);
}
/* line 18, ../sass/_template_specific.scss */
body .aviso_cookie p {
  padding: 0;
  text-align: left;
  line-height: 20px;
}

/* line 26, ../sass/_template_specific.scss */
*, ::after, ::before {
  box-sizing: border-box;
}

/* line 30, ../sass/_template_specific.scss */
.responsive_sections_wrapper {
  display: none;
}

/* line 35, ../sass/_template_specific.scss */
header {
  background-color: #555658 !important;
  height: 120px;
}
/* line 38, ../sass/_template_specific.scss */
header #mainHeaderContainer {
  margin: 12px auto;
}

/* line 43, ../sass/_template_specific.scss */
header, .hide_menu {
  background: transparent;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1001;
}
/* line 49, ../sass/_template_specific.scss */
header .after, .hide_menu .after {
  position: absolute;
  bottom: -3px;
  left: 0;
  right: 0;
  width: 0;
  display: block;
  content: '';
  height: 3px;
  background-color: #446ca9;
}
/* line 61, ../sass/_template_specific.scss */
header #main_sections, .hide_menu #main_sections {
  display: block;
  text-align: right;
}
/* line 66, ../sass/_template_specific.scss */
header #logo, .hide_menu #logo {
  margin-top: 24px;
  float: left;
  width: 170px;
}
/* line 72, ../sass/_template_specific.scss */
header #main_header, .hide_menu #main_header {
  width: 970px;
  float: right;
}
/* line 78, ../sass/_template_specific.scss */
header .section_element, header .section_element top_sections, .hide_menu .section_element, .hide_menu .section_element top_sections {
  display: inline-block;
  font-size: 16px;
  font-weight: 300;
  position: relative;
  cursor: pointer;
}
/* line 85, ../sass/_template_specific.scss */
header .section_element .link, header .section_element top_sections .link, .hide_menu .section_element .link, .hide_menu .section_element top_sections .link {
  color: white;
  text-decoration: none;
  cursor: pointer;
  padding: 5px 10px 5px;
  display: inline-block;
}
/* line 92, ../sass/_template_specific.scss */
header .section_element .link:hover, header .section_element top_sections .link:hover, .hide_menu .section_element .link:hover, .hide_menu .section_element top_sections .link:hover {
  color: #e6e6e6;
}
/* line 98, ../sass/_template_specific.scss */
header .section_element:first-child .link, header .section_element top_sections:first-child .link, .hide_menu .section_element:first-child .link, .hide_menu .section_element top_sections:first-child .link {
  padding-left: 0px;
}
/* line 101, ../sass/_template_specific.scss */
header .section_element:first-child .subsections_wrapper, header .section_element top_sections:first-child .subsections_wrapper, .hide_menu .section_element:first-child .subsections_wrapper, .hide_menu .section_element top_sections:first-child .subsections_wrapper {
  left: 0;
}
/* line 106, ../sass/_template_specific.scss */
header .section_element:last-child .link, header .section_element top_sections:last-child .link, .hide_menu .section_element:last-child .link, .hide_menu .section_element top_sections:last-child .link {
  padding-right: 0;
}
/* line 110, ../sass/_template_specific.scss */
header .section_element .subsections_wrapper, header .section_element top_sections .subsections_wrapper, .hide_menu .section_element .subsections_wrapper, .hide_menu .section_element top_sections .subsections_wrapper {
  background: white;
  position: absolute;
  top: 39px;
  left: 10px;
  opacity: 0;
  margin-top: -25px;
  text-align: left;
  border-radius: 5px;
  overflow: visible !important;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 122, ../sass/_template_specific.scss */
header .section_element .subsections_wrapper .subsection_element, header .section_element top_sections .subsections_wrapper .subsection_element, .hide_menu .section_element .subsections_wrapper .subsection_element, .hide_menu .section_element top_sections .subsections_wrapper .subsection_element {
  display: inline-block;
  padding: 7px 30px 7px 15px;
  width: 100%;
  white-space: nowrap;
  font-weight: 300;
  text-transform: uppercase;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 131, ../sass/_template_specific.scss */
header .section_element .subsections_wrapper .subsection_element .hide_img, header .section_element top_sections .subsections_wrapper .subsection_element .hide_img, .hide_menu .section_element .subsections_wrapper .subsection_element .hide_img, .hide_menu .section_element top_sections .subsections_wrapper .subsection_element .hide_img {
  display: inline-block;
  width: 24px;
  vertical-align: middle;
}
/* line 136, ../sass/_template_specific.scss */
header .section_element .subsections_wrapper .subsection_element a, header .section_element top_sections .subsections_wrapper .subsection_element a, .hide_menu .section_element .subsections_wrapper .subsection_element a, .hide_menu .section_element top_sections .subsections_wrapper .subsection_element a {
  display: inline-block;
  text-decoration: none;
  font-size: 14px;
  color: #333;
  vertical-align: middle;
  padding-left: 15px;
}
/* line 144, ../sass/_template_specific.scss */
header .section_element .subsections_wrapper .subsection_element:hover, header .section_element top_sections .subsections_wrapper .subsection_element:hover, .hide_menu .section_element .subsections_wrapper .subsection_element:hover, .hide_menu .section_element top_sections .subsections_wrapper .subsection_element:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
/* line 148, ../sass/_template_specific.scss */
header .section_element .subsections_wrapper:before, header .section_element top_sections .subsections_wrapper:before, .hide_menu .section_element .subsections_wrapper:before, .hide_menu .section_element top_sections .subsections_wrapper:before {
  content: '';
  position: absolute;
  top: -20px;
  left: 15px;
  display: block;
  border: 10px solid transparent;
  border-bottom-color: white;
}
/* line 157, ../sass/_template_specific.scss */
header .section_element .subsections_wrapper.active, header .section_element top_sections .subsections_wrapper.active, .hide_menu .section_element .subsections_wrapper.active, .hide_menu .section_element top_sections .subsections_wrapper.active {
  opacity: 1;
  margin-top: 0;
}
/* line 164, ../sass/_template_specific.scss */
header #top_header, .hide_menu #top_header {
  text-align: right;
  color: white;
  width: auto;
  font-size: 14px;
  font-weight: 500;
  margin-top: 12px;
  margin-bottom: 12px;
}
/* line 173, ../sass/_template_specific.scss */
header #top_header .social, .hide_menu #top_header .social {
  display: inline-block;
  vertical-align: middle;
  color: white;
  background-color: white;
  border-radius: 5px;
  padding: 10px;
  margin-right: 10px;
}
/* line 183, ../sass/_template_specific.scss */
header #top_header .social a .fa, header #top_header .social a .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv #newsletter-button:before, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv header #top_header .social a #newsletter-button:before, .hide_menu #top_header .social a .fa, .hide_menu #top_header .social a .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv #newsletter-button:before, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv .hide_menu #top_header .social a #newsletter-button:before {
  color: #3A9BC8;
  font-size: 18px;
  margin-right: 10px;
  vertical-align: middle;
}
/* line 188, ../sass/_template_specific.scss */
header #top_header .social a .fa:hover, header #top_header .social a .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv #newsletter-button:hover:before, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv header #top_header .social a #newsletter-button:hover:before, .hide_menu #top_header .social a .fa:hover, .hide_menu #top_header .social a .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv #newsletter-button:hover:before, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv .hide_menu #top_header .social a #newsletter-button:hover:before {
  color: #787878;
}
/* line 195, ../sass/_template_specific.scss */
header #top_header #lang, .hide_menu #top_header #lang {
  display: inline-block;
  color: #3A9BC8 !important;
  margin-right: 10px;
}
/* line 201, ../sass/_template_specific.scss */
header #top_header #selected-language, .hide_menu #top_header #selected-language {
  padding-right: 18px;
  display: inline-block;
  position: relative;
  color: #3A9BC8;
  background-position-y: 7px;
}
/* line 208, ../sass/_template_specific.scss */
header #top_header #selected-language:after, .hide_menu #top_header #selected-language:after {
  content: "\f107";
  font-family: "Fontawesome";
  color: #3A9BC8;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 0;
}
/* line 216, ../sass/_template_specific.scss */
header #top_header #selected-language:hover, .hide_menu #top_header #selected-language:hover {
  color: #787878;
}
/* line 218, ../sass/_template_specific.scss */
header #top_header #selected-language:hover:after, .hide_menu #top_header #selected-language:hover:after {
  content: "\f107";
  font-family: "Fontawesome";
  color: #787878;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 0;
}
/* line 228, ../sass/_template_specific.scss */
header #top_header .language-option-flag, .hide_menu #top_header .language-option-flag {
  display: inline-block;
}
/* line 232, ../sass/_template_specific.scss */
header #top_header .language-option-flag a, .hide_menu #top_header .language-option-flag a {
  color: #3A9BC8;
}
/* line 236, ../sass/_template_specific.scss */
header #top_header a, .hide_menu #top_header a {
  text-decoration: none;
  color: white;
  margin: 0 2px;
}
/* line 241, ../sass/_template_specific.scss */
header #top_header a:hover, .hide_menu #top_header a:hover {
  color: #2d7da2;
}
/* line 244, ../sass/_template_specific.scss */
header #top_header a.top_menu, .hide_menu #top_header a.top_menu {
  color: white;
  background-color: #3A9BC8;
  border-radius: 5px;
  display: inline-block;
  vertical-align: middle;
  padding: 11px 12px;
}
/* line 251, ../sass/_template_specific.scss */
header #top_header a.top_menu:hover, .hide_menu #top_header a.top_menu:hover {
  background-color: #2d7da2;
}
/* line 257, ../sass/_template_specific.scss */
header #top_header a#section-active:not(.top_menu), .hide_menu #top_header a#section-active:not(.top_menu) {
  color: #2d7da2;
}
/* line 262, ../sass/_template_specific.scss */
header #top_header .login, .hide_menu #top_header .login {
  display: inline-block;
  background: #446ca9;
  padding: 5px 10px;
  color: white;
  text-transform: uppercase;
  cursor: pointer;
  margin-left: 10px;
}
/* line 271, ../sass/_template_specific.scss */
header #top_header .login a, .hide_menu #top_header .login a {
  color: white;
  font-size: 13px;
  font-weight: 400;
}
/* line 276, ../sass/_template_specific.scss */
header #top_header .login a:hover, .hide_menu #top_header .login a:hover {
  color: #e6e6e6;
}
/* line 283, ../sass/_template_specific.scss */
header #section-active, .hide_menu #section-active {
  font-weight: 500;
}

/* line 288, ../sass/_template_specific.scss */
.inner_slider {
  width: 100%;
  height: calc(30vh + 120px);
  position: relative;
  overflow: hidden;
}
/* line 293, ../sass/_template_specific.scss */
.inner_slider img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 296, ../sass/_template_specific.scss */
.inner_slider.inner_slider_big {
  height: 400px;
  margin-top: 115px;
}
/* line 299, ../sass/_template_specific.scss */
.inner_slider.inner_slider_big .inner_cartela {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 0;
  right: 0;
  padding: 0 calc((100% - 1140px) / 2) 0;
  color: white;
}
/* line 305, ../sass/_template_specific.scss */
.inner_slider.inner_slider_big .inner_cartela .title {
  font-size: 66px;
  line-height: 70px;
  font-weight: bold;
  padding: 0 20px 10px;
}
/* line 311, ../sass/_template_specific.scss */
.inner_slider.inner_slider_big .inner_cartela .desc {
  font-size: 18px;
  line-height: 20px;
  font-weight: normal;
  padding: 0 20px;
}
/* line 316, ../sass/_template_specific.scss */
.inner_slider.inner_slider_big .inner_cartela .desc strong, .inner_slider.inner_slider_big .inner_cartela .desc b {
  font-weight: bold;
}

/* line 325, ../sass/_template_specific.scss */
.hide_subsections {
  background: #f7f7f7;
}
/* line 328, ../sass/_template_specific.scss */
.hide_subsections .subsections_element {
  text-align: center;
  height: 90px;
}
/* line 332, ../sass/_template_specific.scss */
.hide_subsections .subsections_element .hide_element {
  display: inline-block;
  padding: 19px 0;
}
/* line 336, ../sass/_template_specific.scss */
.hide_subsections .subsections_element .hide_element a {
  color: black;
  text-decoration: none;
  padding: 3px 19px;
  display: inline-block;
  font-size: 12px;
  color: #333;
}
/* line 344, ../sass/_template_specific.scss */
.hide_subsections .subsections_element .hide_element a:hover {
  color: #666666;
}

/* line 352, ../sass/_template_specific.scss */
.hide_menu {
  position: fixed;
  top: 0;
  background: rgba(85, 86, 88, 0.9);
  display: none;
  box-shadow: 0px -2px 10px black;
  height: 120px;
}
/* line 362, ../sass/_template_specific.scss */
.hide_menu #mainHeaderContainer {
  margin: 14px auto;
}
/* line 366, ../sass/_template_specific.scss */
.hide_menu #top_header .social a .fa, .hide_menu #top_header .social a .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv #newsletter-button:before, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv .hide_menu #top_header .social a #newsletter-button:before {
  color: #3A9BC8;
}
/* line 369, ../sass/_template_specific.scss */
.hide_menu #top_header .social a .fa:hover, .hide_menu #top_header .social a .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv #newsletter-button:hover:before, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv .hide_menu #top_header .social a #newsletter-button:hover:before {
  opacity: .8;
}
/* line 374, ../sass/_template_specific.scss */
.hide_menu #logo {
  margin-top: 25px;
}
/* line 379, ../sass/_template_specific.scss */
.hide_menu .section_element a {
  color: white;
}
/* line 382, ../sass/_template_specific.scss */
.hide_menu .section_element a:hover {
  color: #cccccc !important;
}

/*=== Slider ===*/
/* line 390, ../sass/_template_specific.scss */
#slider_container {
  position: relative;
}
/* line 393, ../sass/_template_specific.scss */
#slider_container .tp-rightarrow.default {
  background: none;
}
/* line 395, ../sass/_template_specific.scss */
#slider_container .tp-rightarrow.default:before {
  content: '\f105';
  display: block;
  color: white;
  font-size: 50px;
  font-family: "fontawesome", sans-serif;
}
/* line 404, ../sass/_template_specific.scss */
#slider_container .tp-leftarrow.default {
  background: none;
}
/* line 406, ../sass/_template_specific.scss */
#slider_container .tp-leftarrow.default:before {
  content: '\f104';
  display: block;
  color: white;
  font-size: 50px;
  font-family: "fontawesome", sans-serif;
}
/* line 415, ../sass/_template_specific.scss */
#slider_container .default {
  height: 72px;
}
/* line 419, ../sass/_template_specific.scss */
#slider_container .tp-bullets.simplebullets.round .bullet {
  width: 10px;
  margin-right: 10px;
  margin-left: 0;
  display: inline-block;
  border-radius: 50%;
  background: #ccc;
  color: transparent;
  height: 10px;
}
/* line 431, ../sass/_template_specific.scss */
#slider_container .tp-bullets.simplebullets.round .bullet.last {
  margin-right: 0;
}
/* line 435, ../sass/_template_specific.scss */
#slider_container .tp-bullets.simplebullets.round .bullet.selected {
  background-color: #3A9BC8;
}
/* line 439, ../sass/_template_specific.scss */
#slider_container .icon_down {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  width: 70px;
  padding: 7px 0 3px;
  margin: auto;
  text-align: center;
  z-index: 100;
}
/* line 450, ../sass/_template_specific.scss */
#slider_container .icon_down:before {
  content: '\f107';
  display: block;
  color: #446ca9;
  font-size: 30px;
  font-family: "fontawesome", sans-serif;
}
/* line 459, ../sass/_template_specific.scss */
#slider_container .tp-banner-container {
  height: 80vh !important;
}
/* line 463, ../sass/_template_specific.scss */
#slider_container .tp-caption {
  position: absolute;
  text-align: center;
  top: 200px;
}
/* line 471, ../sass/_template_specific.scss */
#slider_container .tp-caption .revolution_text {
  text-align: center;
  margin-top: 63px;
  line-height: 1;
}
/* line 476, ../sass/_template_specific.scss */
#slider_container .tp-caption .revolution_text .title {
  font-weight: 300;
  font-size: 65px;
  color: white;
  font-family: 'Caveat';
  font-weight: bold;
}
/* line 484, ../sass/_template_specific.scss */
#slider_container .tp-caption .revolution_text .description {
  font-weight: 100;
  font-size: 39px;
  margin-top: 7px;
  color: white;
}
/* line 493, ../sass/_template_specific.scss */
#slider_container .tp-caption .links {
  margin-top: -5px;
  line-height: 1;
}
/* line 497, ../sass/_template_specific.scss */
#slider_container .tp-caption .links .know_more {
  font-size: 24px;
  color: #F9F9F9;
  margin-top: 10px;
  font-weight: 300;
  display: inline-block;
  padding: 10px;
  text-decoration: none;
  line-height: 1;
  background-color: #3A9BC8;
  border-radius: 5px;
}
/* line 511, ../sass/_template_specific.scss */
#slider_container .tp-caption .links .know_more:hover {
  color: #e6e6e6;
  background-color: #225e7a;
}

/* line 520, ../sass/_template_specific.scss */
.left_slider_arrow {
  position: absolute;
  top: 315px;
  z-index: 22;
  left: 20%;
  cursor: pointer;
}

/* line 528, ../sass/_template_specific.scss */
.right_slider_arrow {
  position: absolute;
  top: 315px;
  z-index: 22;
  right: 20%;
  -moz-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
  filter: FlipH;
  -ms-filter: "FlipH";
  cursor: pointer;
}

/* line 542, ../sass/_template_specific.scss */
.tparrows {
  z-index: 30;
}

/* line 546, ../sass/_template_specific.scss */
.tp-bullets {
  bottom: 56px !important;
  opacity: 1 !important;
  -moz-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  -webkit-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  margin-left: 0 !important;
  left: 50%;
}

/* line 557, ../sass/_template_specific.scss */
.slide_inner {
  height: 480px;
  width: 100%;
  overflow: hidden;
  display: inline-block;
  position: relative;
}
/* line 564, ../sass/_template_specific.scss */
.slide_inner img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  max-width: initial;
}

/* line 577, ../sass/_template_specific.scss */
.down_slider_arrow {
  position: absolute;
  bottom: 175px;
  z-index: 22;
  right: 20%;
  cursor: pointer;
}
/* line 584, ../sass/_template_specific.scss */
.down_slider_arrow:hover {
  opacity: 0.8;
}

/* line 591, ../sass/_template_specific.scss */
.bannerx3_wrapper {
  display: flex;
  width: 100%;
  background-color: white;
}
/* line 595, ../sass/_template_specific.scss */
.bannerx3_wrapper .bannerx3 {
  position: relative;
  overflow: hidden;
  display: inline-block;
  width: calc(100% / 3);
  height: 250px;
  border: 5px solid white;
  border-left: none;
}
/* line 603, ../sass/_template_specific.scss */
.bannerx3_wrapper .bannerx3 img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 606, ../sass/_template_specific.scss */
.bannerx3_wrapper .bannerx3 .title {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: 2;
  color: white;
  text-align: center;
  font-size: 56px;
  font-family: 'Caveat';
}
/* line 614, ../sass/_template_specific.scss */
.bannerx3_wrapper .bannerx3:last-of-type {
  border-right: none;
}
/* line 617, ../sass/_template_specific.scss */
.bannerx3_wrapper .bannerx3:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.4);
  -webkit-transition: background-color 0.6s;
  -moz-transition: background-color 0.6s;
  -ms-transition: background-color 0.6s;
  -o-transition: background-color 0.6s;
  transition: background-color 0.6s;
}
/* line 625, ../sass/_template_specific.scss */
.bannerx3_wrapper .bannerx3:hover:before {
  background-color: rgba(58, 155, 200, 0.6);
}

/* line 633, ../sass/_template_specific.scss */
.bannerx5_wrapper {
  padding-top: 40px;
  padding-bottom: 50px;
  width: 1140px;
  margin: 0 auto;
}
/* line 639, ../sass/_template_specific.scss */
.bannerx5_wrapper .col-2 {
  width: 564.5px;
  height: 380px;
  margin-bottom: 10px;
}
/* line 645, ../sass/_template_specific.scss */
.bannerx5_wrapper .col-3 {
  height: 250px;
  width: 372.66px;
}
/* line 650, ../sass/_template_specific.scss */
.bannerx5_wrapper .banner-left {
  margin-right: 10px;
}
/* line 654, ../sass/_template_specific.scss */
.bannerx5_wrapper .banner-mid {
  margin-right: 10px;
}
/* line 658, ../sass/_template_specific.scss */
.bannerx5_wrapper .bannerx5_element {
  position: relative;
  float: left;
  overflow: hidden;
}
/* line 664, ../sass/_template_specific.scss */
.bannerx5_wrapper .bannerx5_element .bannerx5_gallery .flex-control-nav {
  position: absolute;
  bottom: 10px;
  right: 0;
  left: 0;
  text-align: center;
  z-index: 2;
}
/* line 672, ../sass/_template_specific.scss */
.bannerx5_wrapper .bannerx5_element .bannerx5_gallery .flex-control-nav li {
  display: inline-block;
  padding: 5px;
  margin: 5px;
  width: 10px;
}
/* line 678, ../sass/_template_specific.scss */
.bannerx5_wrapper .bannerx5_element .bannerx5_gallery .flex-control-nav li a {
  display: inline-block;
  border-radius: 50%;
  background: #ccc;
  color: transparent;
  height: 10px;
}
/* line 687, ../sass/_template_specific.scss */
.bannerx5_wrapper .bannerx5_element .bannerx5_gallery .flex-control-nav li a.flex-active {
  background: #3A9BC8;
  background-size: contain;
}
/* line 694, ../sass/_template_specific.scss */
.bannerx5_wrapper .bannerx5_element .element_text, .bannerx5_wrapper .bannerx5_element .bannerx5_text {
  position: relative;
  background-color: #555658;
  padding: 15px 25px 25px 25px;
  color: white;
  text-align: center;
  width: 100%;
}
/* line 702, ../sass/_template_specific.scss */
.bannerx5_wrapper .bannerx5_element .element_text .title, .bannerx5_wrapper .bannerx5_element .bannerx5_text .title {
  font-weight: 300;
  font-size: 19px;
  color: white;
}
/* line 707, ../sass/_template_specific.scss */
.bannerx5_wrapper .bannerx5_element .element_text .title b, .bannerx5_wrapper .bannerx5_element .bannerx5_text .title b {
  font-weight: 500;
}
/* line 712, ../sass/_template_specific.scss */
.bannerx5_wrapper .bannerx5_element .element_text .description, .bannerx5_wrapper .bannerx5_element .bannerx5_text .description {
  font-weight: 300;
  font-size: 16px;
  color: white;
}
/* line 719, ../sass/_template_specific.scss */
.bannerx5_wrapper .bannerx5_element .element_img, .bannerx5_wrapper .bannerx5_element .bannerx5_img {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
}
/* line 724, ../sass/_template_specific.scss */
.bannerx5_wrapper .bannerx5_element .element_img img, .bannerx5_wrapper .bannerx5_element .bannerx5_img img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 729, ../sass/_template_specific.scss */
.bannerx5_wrapper .bannerx5_element:nth-of-type(n + 3) .element_img, .bannerx5_wrapper .bannerx5_element:nth-of-type(n + 3) .bannerx5_img {
  height: 170px;
}
/* line 734, ../sass/_template_specific.scss */
.bannerx5_wrapper .links {
  color: white;
  background-color: #3A9BC8;
  border-radius: 5px;
  padding: 10px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 740, ../sass/_template_specific.scss */
.bannerx5_wrapper .links .know_more {
  font-size: 16px;
  color: white;
  font-weight: 300;
  display: inline-block;
  text-decoration: none;
  background-position-y: 6px;
}
/* line 748, ../sass/_template_specific.scss */
.bannerx5_wrapper .links .know_more:hover {
  color: #e6e6e6;
}
/* line 753, ../sass/_template_specific.scss */
.bannerx5_wrapper .links .link_video {
  font-size: 16px;
  color: #3A9BC8;
  margin-top: 10px;
  font-weight: 300;
  background: url("/img/datar/bot-play-banner.png") no-repeat right;
  display: inline-block;
  padding-right: 23px;
  margin-left: 5px;
  text-decoration: none;
  background-position-y: 2px;
}
/* line 765, ../sass/_template_specific.scss */
.bannerx5_wrapper .links .link_video:hover {
  color: #2d7da2;
}
/* line 770, ../sass/_template_specific.scss */
.bannerx5_wrapper .links .highlight {
  font-size: 16px;
  color: #3A9BC8;
  margin-top: 10px;
  font-weight: 300;
  display: inline-block;
  padding-right: 12px;
}

/* line 782, ../sass/_template_specific.scss */
.bannerx4_wrapper {
  padding: 50px 0;
  margin-bottom: 50px;
}
/* line 786, ../sass/_template_specific.scss */
.bannerx4_wrapper .bannerx4_element {
  text-align: center;
  font-weight: 300;
  width: 25%;
  display: inline-block;
}
/* line 793, ../sass/_template_specific.scss */
.bannerx4_wrapper .bannerx4_img {
  margin-bottom: 10px;
}
/* line 795, ../sass/_template_specific.scss */
.bannerx4_wrapper .bannerx4_img img {
  width: 70px;
  height: 70px;
}
/* line 801, ../sass/_template_specific.scss */
.bannerx4_wrapper .bannerx4_title {
  margin-bottom: 0px;
  font-size: 30px;
  color: white;
  font-family: 'Caveat';
}
/* line 808, ../sass/_template_specific.scss */
.bannerx4_wrapper .bannerx4_description {
  line-height: 21px;
  font-size: 16px;
  color: white;
}

/* line 816, ../sass/_template_specific.scss */
.slider_text_wrapper {
  position: relative;
  padding: 50px 0 0;
  background: lightgreen;
  overflow: hidden;
}
/* line 823, ../sass/_template_specific.scss */
.slider_text_wrapper .icon_block {
  text-align: center;
  color: white;
  background-color: #3A9BC8;
  border-radius: 5px;
  font-size: 40px;
  width: 75px;
  padding: 14px 0 10px;
  margin: 0 auto 20px;
}
/* line 835, ../sass/_template_specific.scss */
.slider_text_wrapper .slider_text_title {
  margin-bottom: 30px;
  font-weight: 100;
  font-size: 50px;
  color: #c8c8c8;
}
/* line 841, ../sass/_template_specific.scss */
.slider_text_wrapper .slider_text_title b {
  display: block;
  color: white;
}
/* line 848, ../sass/_template_specific.scss */
.slider_text_wrapper .slider_text_description {
  font-weight: 300;
  line-height: 29px;
  color: white;
  width: 530px;
  margin: 0 auto;
}
/* line 856, ../sass/_template_specific.scss */
.slider_text_wrapper .slides {
  width: 60%;
  margin: 0 auto;
  text-align: center;
}
/* line 863, ../sass/_template_specific.scss */
.slider_text_wrapper .flex-direction-nav .flex-nav-prev {
  position: absolute;
  top: calc(50% - 35.5px);
  left: 30px;
  width: 33px;
  z-index: 2;
  -ms-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}
/* line 873, ../sass/_template_specific.scss */
.slider_text_wrapper .flex-direction-nav .flex-nav-prev a {
  background: url("/img/datar/flecha-slider.png") no-repeat;
  color: transparent;
  height: 71px;
  display: inline-block;
}
/* line 882, ../sass/_template_specific.scss */
.slider_text_wrapper .flex-direction-nav .flex-nav-next {
  position: absolute;
  right: 0;
  top: calc(50% - 35.5px);
  position: absolute;
  right: 30px;
  z-index: 2;
}
/* line 890, ../sass/_template_specific.scss */
.slider_text_wrapper .flex-direction-nav .flex-nav-next a {
  background: url("/img/datar/flecha-slider.png") no-repeat;
  color: transparent;
  height: 71px;
  display: inline-block;
}
/* line 898, ../sass/_template_specific.scss */
.slider_text_wrapper .customers_line_wrappers {
  clear: both;
  margin-top: 20px;
  padding: 20px 0;
  background-color: rgba(0, 0, 0, 0.4);
}
/* line 903, ../sass/_template_specific.scss */
.slider_text_wrapper .customers_line_wrappers .customers_line_title {
  width: 80%;
  max-width: 800px;
  color: lightgrey;
  text-align: center;
  font-size: 30px;
  font-weight: lighter;
  margin: 0 auto 20px;
}
/* line 912, ../sass/_template_specific.scss */
.slider_text_wrapper .customers_line_wrappers .customers_line {
  width: 80%;
  max-width: 800px;
  margin: auto;
}
/* line 916, ../sass/_template_specific.scss */
.slider_text_wrapper .customers_line_wrappers .customers_line .owl-dots {
  text-align: center;
  margin-top: 10px;
}
/* line 920, ../sass/_template_specific.scss */
.slider_text_wrapper .customers_line_wrappers .customers_line .owl-dots .owl-dot {
  display: inline-block;
  vertical-align: middle;
  padding: 1px;
  margin: 9px;
}
/* line 926, ../sass/_template_specific.scss */
.slider_text_wrapper .customers_line_wrappers .customers_line .owl-dots .owl-dot span {
  display: block;
  width: 7px;
  height: 7px;
  background-color: white;
  border-radius: 50%;
}
/* line 934, ../sass/_template_specific.scss */
.slider_text_wrapper .customers_line_wrappers .customers_line .owl-dots .owl-dot.active span {
  background-color: #3A9BC8;
}

/* line 945, ../sass/_template_specific.scss */
.banner_partner_wrapper {
  padding: 50px 0;
  background: #fafafa;
}
/* line 949, ../sass/_template_specific.scss */
.banner_partner_wrapper .icon_block {
  text-align: center;
}
/* line 953, ../sass/_template_specific.scss */
.banner_partner_wrapper .partner_text {
  margin: 0 auto;
  width: 60%;
  text-align: center;
  padding-bottom: 38px;
  margin-top: -16px;
}
/* line 960, ../sass/_template_specific.scss */
.banner_partner_wrapper .partner_text .partner_title {
  margin-bottom: 30px;
  margin-top: 30px;
  font-weight: 100;
  font-size: 50px;
  color: #323232;
}
/* line 967, ../sass/_template_specific.scss */
.banner_partner_wrapper .partner_text .partner_title b {
  display: block;
  color: #8d8d8d;
  margin-top: -5px;
}
/* line 974, ../sass/_template_specific.scss */
.banner_partner_wrapper .partner_text .partner_description {
  width: 80%;
  margin: 0 auto;
  font-weight: 300;
  font-size: 17px;
  line-height: 29px;
  color: #323232;
}
/* line 984, ../sass/_template_specific.scss */
.banner_partner_wrapper .partner_badges {
  text-align: center;
}
/* line 986, ../sass/_template_specific.scss */
.banner_partner_wrapper .partner_badges li.col-md-3 {
  width: calc(100% / 4 - 40px);
  display: inline-block;
  vertical-align: middle;
  margin: 0 20px;
}

/* line 995, ../sass/_template_specific.scss */
.wordpress_block {
  background: url("/img/datar/img-blog.jpg?v=1") no-repeat bottom center;
  background-size: 500px;
  padding: 50px 0;
}
/* line 1000, ../sass/_template_specific.scss */
.wordpress_block .icon_block {
  text-align: center;
  color: #446ca9;
  border: 1px solid #446ca9;
  border-radius: 50%;
  font-size: 40px;
  width: 75px;
  padding: 16px 0 10px;
  margin: 0 auto 20px;
}
/* line 1011, ../sass/_template_specific.scss */
.wordpress_block .wordpress_text .title {
  text-align: center;
  font-weight: 100;
  font-size: 50px;
  color: #323232;
  padding-bottom: 38px;
  margin-top: 15px;
}
/* line 1019, ../sass/_template_specific.scss */
.wordpress_block .wordpress_text .title b {
  display: block;
  color: #8d8d8d;
}
/* line 1025, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections {
  text-align: center;
}
/* line 1027, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element {
  display: inline-block;
  vertical-align: top;
  width: calc(100% / 3 - 40px);
  border: 1px solid #DDD;
  margin: 0 20px;
  text-align: center;
}
/* line 1035, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .title {
  text-align: left;
  padding: 20px;
  color: #446ca9;
  font-weight: bold;
  font-size: 18px;
}
/* line 1042, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .img {
  position: relative;
  width: 100%;
  height: 150px;
  overflow: hidden;
}
/* line 1047, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .img img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 1050, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .img:after {
  content: '';
  border: 10px solid transparent;
  border-bottom-color: white;
  position: absolute;
  bottom: 0px;
  right: 20px;
}
/* line 1059, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .description {
  text-align: left;
  padding: 0 20px;
  font-size: 16px;
  font-weight: 300;
  color: #555;
}
/* line 1066, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .extra {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  text-align: left;
  background: rgba(0, 0, 0, 0.8);
  background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.6));
}
/* line 1075, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .date, .wordpress_block .wordpress_sections .wordpress_element .comments {
  display: inline-block;
  vertical-align: bottom;
  box-sizing: border-box;
  padding: 5px;
  color: white;
  font-size: 16px;
  font-weight: 300;
  margin-left: 20px;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
}
/* line 1086, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .date {
  font-size: 12px;
  padding: 10px 10px 8px;
}
/* line 1091, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .comments i.fa, .wordpress_block .wordpress_sections .wordpress_element .comments .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv i#newsletter-button:before, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv .wordpress_sections .wordpress_element .comments i#newsletter-button:before {
  border: 1px solid #DDD;
  border-radius: 50%;
  font-size: 10px;
  padding: 5px;
}
/* line 1097, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .comments span {
  color: white;
  background-color: #3A9BC8;
  display: inline-block;
  border-radius: 50%;
  margin-left: 5px;
  font-size: 10px;
  padding: 3px 5px;
}
/* line 1109, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .news_text .title {
  font-weight: bold;
  font-size: 17px;
  line-height: 29px;
  color: #323232;
  width: 320px;
  margin: 0 auto;
}
/* line 1118, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .news_text .content {
  font-size: 17px;
  font-weight: 300;
  color: #323232;
  line-height: 29px;
  width: 320px;
  margin: 0 auto;
  max-height: 116px;
  overflow: hidden;
}
/* line 1129, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .links {
  text-align: right;
  margin-top: 15px;
}
/* line 1133, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .links .read_more {
  position: relative;
  display: inline-block;
  font-size: 14px;
  font-weight: 300;
  text-decoration: none;
  color: #3A9BC8;
  padding: 5px 10px;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 1142, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .links .read_more span {
  position: relative;
  z-index: 2;
}
/* line 1146, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .links .read_more:before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  width: 0px;
  bottom: 0;
  background-color: #3A9BC8;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 1157, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .links .read_more:after {
  content: '\f105';
  font-family: "fontawesome", sans-serif;
  margin: 0 5px;
  display: inline-block;
  position: relative;
  z-index: 2;
}
/* line 1165, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .links .read_more:hover {
  color: white;
}
/* line 1167, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element .links .read_more:hover:before {
  width: 100%;
}
/* line 1175, ../sass/_template_specific.scss */
.wordpress_block .wordpress_sections .wordpress_element.middle {
  margin: 0 5px;
}
/* line 1180, ../sass/_template_specific.scss */
.wordpress_block .newsletter_wrapper {
  text-align: center;
  margin-top: 60px;
}
/* line 1184, ../sass/_template_specific.scss */
.wordpress_block .newsletter_wrapper .newsletter_element {
  display: inline-block;
  vertical-align: top;
  margin: auto;
  text-align: center;
  margin-bottom: 20px;
  padding: 0 30px;
}
/* line 1192, ../sass/_template_specific.scss */
.wordpress_block .newsletter_wrapper .newsletter_element label.error {
  display: none !important;
}
/* line 1195, ../sass/_template_specific.scss */
.wordpress_block .newsletter_wrapper .newsletter_element#newsletter_web {
  width: 500px;
}
/* line 1198, ../sass/_template_specific.scss */
.wordpress_block .newsletter_wrapper .newsletter_element#newsletter_web .lopd_button, .wordpress_block .newsletter_wrapper .newsletter_element#newsletter_web .promotions_button {
  display: inline-block;
  vertical-align: middle;
  width: 100%;
  text-align: left;
  margin-top: 10px;
  color: lightgrey;
}
/* line 1206, ../sass/_template_specific.scss */
.wordpress_block .newsletter_wrapper .newsletter_element#newsletter_web .lopd_button a, .wordpress_block .newsletter_wrapper .newsletter_element#newsletter_web .promotions_button a {
  text-decoration: none;
  font-size: 12px;
  color: lightgrey;
}
/* line 1214, ../sass/_template_specific.scss */
.wordpress_block .newsletter_wrapper .newsletter_element .bordeInput {
  display: inline-block;
  vertical-align: middle;
  border: 2px solid #3A9BC8;
  padding: 0 10px;
  width: 380px;
  height: 60px;
}
/* line 1222, ../sass/_template_specific.scss */
.wordpress_block .newsletter_wrapper .newsletter_element .bordeInput::-webkit-input-placeholder {
  color: #8d8d8d;
}
/* line 1226, ../sass/_template_specific.scss */
.wordpress_block .newsletter_wrapper .newsletter_element .bordeInput:-moz-placeholder {
  color: #8d8d8d;
}
/* line 1230, ../sass/_template_specific.scss */
.wordpress_block .newsletter_wrapper .newsletter_element .bordeInput:-ms-input-placeholder {
  color: #8d8d8d;
}
/* line 1234, ../sass/_template_specific.scss */
.wordpress_block .newsletter_wrapper .newsletter_element .bordeInput:focus {
  outline-color: white;
}
/* line 1239, ../sass/_template_specific.scss */
.wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv {
  display: inline-block;
  vertical-align: middle;
}
/* line 1243, ../sass/_template_specific.scss */
.wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv #newsletter-button {
  position: relative;
  background: #3A9BC8;
  padding: 20.5px 0;
  height: 60px;
  width: 60px;
  cursor: pointer;
}
/* line 1251, ../sass/_template_specific.scss */
.wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv #newsletter-button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: white;
  font-size: 30px;
}
/* line 1263, ../sass/_template_specific.scss */
.wordpress_block .newsletter_element#newsletter_blog {
  position: relative;
  max-width: 500px;
  width: 100%;
  background-color: #446ca9;
}
/* line 1268, ../sass/_template_specific.scss */
.wordpress_block .newsletter_element#newsletter_blog .text {
  position: relative;
  z-index: 5;
  display: inline-block;
  float: left;
  padding: 20px;
  color: white;
  font-size: 20px;
  height: auto;
  box-sizing: border-box;
}
/* line 1279, ../sass/_template_specific.scss */
.wordpress_block .newsletter_element#newsletter_blog:before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 0;
  background-color: #3A9BC8;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 1289, ../sass/_template_specific.scss */
.wordpress_block .newsletter_element#newsletter_blog #newsletterButtonExternalDiv {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 0;
}
/* line 1292, ../sass/_template_specific.scss */
.wordpress_block .newsletter_element#newsletter_blog #newsletterButtonExternalDiv #newsletter-button {
  position: relative;
  z-index: 5;
  background: transparent;
}
/* line 1297, ../sass/_template_specific.scss */
.wordpress_block .newsletter_element#newsletter_blog #newsletterButtonExternalDiv #newsletter-button:before {
  font-family: "Fontawesome", sans-serif;
  font-size: 30px;
  color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 1307, ../sass/_template_specific.scss */
.wordpress_block .newsletter_element#newsletter_blog:hover:before {
  height: 100%;
}

/* line 1315, ../sass/_template_specific.scss */
.submenu_flotante_wrapper {
  background-color: white;
  height: 50px;
  padding: 15px 0;
  border-bottom: 1px solid #e3e3e3;
  width: 100%;
  z-index: 100;
}
/* line 1323, ../sass/_template_specific.scss */
.submenu_flotante_wrapper.floating {
  position: fixed;
  top: 0;
}
/* line 1326, ../sass/_template_specific.scss */
.submenu_flotante_wrapper.floating .after {
  position: absolute;
  bottom: -3px;
  left: 0;
  right: 0;
  width: 0;
  display: block;
  content: '';
  height: 3px;
  background-color: #446ca9;
}
/* line 1340, ../sass/_template_specific.scss */
.submenu_flotante_wrapper .logo {
  float: left;
  margin-top: -7px;
}
/* line 1343, ../sass/_template_specific.scss */
.submenu_flotante_wrapper .logo img {
  max-height: 35px;
}
/* line 1348, ../sass/_template_specific.scss */
.submenu_flotante_wrapper .submenu_content {
  float: right;
  text-transform: capitalize;
  padding-top: 4px;
}
/* line 1353, ../sass/_template_specific.scss */
.submenu_flotante_wrapper .submenu_content a {
  text-decoration: none;
  padding: 0 9.5px;
  font-size: 13px;
  color: #333;
}
/* line 1359, ../sass/_template_specific.scss */
.submenu_flotante_wrapper .submenu_content a:hover {
  color: #666666;
}
/* line 1364, ../sass/_template_specific.scss */
.submenu_flotante_wrapper .submenu_content a:last-child {
  padding-right: 0;
}

/* line 1370, ../sass/_template_specific.scss */
.main_content_wrapper {
  background-color: #fafafa;
  text-align: center;
}
/* line 1375, ../sass/_template_specific.scss */
.main_content_wrapper .main_content_title {
  font-weight: 100;
  font-size: 64px;
  opacity: 0;
  color: #3A9BC8;
  margin-top: 128px;
  line-height: 66px;
}
/* line 1384, ../sass/_template_specific.scss */
.main_content_wrapper .empty_block {
  height: 100px;
}
/* line 1388, ../sass/_template_specific.scss */
.main_content_wrapper .main_content_description {
  opacity: 0;
  font-weight: 100;
  font-size: 24px;
  width: 820px;
  margin: 0 auto;
  margin-top: 26px;
  line-height: 33px;
  color: #333;
}
/* line 1398, ../sass/_template_specific.scss */
.main_content_wrapper .main_content_description p {
  padding: 0px 10px;
  padding-bottom: 24px;
}
/* line 1404, ../sass/_template_specific.scss */
.main_content_wrapper .main_content_img {
  padding-top: 80px;
}
/* line 1408, ../sass/_template_specific.scss */
.main_content_wrapper .main_content_img img {
  vertical-align: middle;
}
/* line 1412, ../sass/_template_specific.scss */
.main_content_wrapper .links {
  margin-top: 45px;
}
/* line 1415, ../sass/_template_specific.scss */
.main_content_wrapper .links .know_more {
  font-size: 24px;
  color: #3A9BC8;
  margin-top: 10px;
  font-weight: 300;
  background: url("/img/datar/flecha-enlace-slider.png") no-repeat right;
  display: inline-block;
  padding-right: 15px;
  text-decoration: none;
  background-position-y: 8px;
  margin-right: 20px;
}
/* line 1427, ../sass/_template_specific.scss */
.main_content_wrapper .links .know_more:hover {
  color: #2d7da2;
}
/* line 1432, ../sass/_template_specific.scss */
.main_content_wrapper .links .link_video {
  font-size: 24px;
  color: #3A9BC8;
  margin-top: 10px;
  font-weight: 300;
  background: url("/img/datar/bot-play-banner.png") no-repeat right;
  display: inline-block;
  padding-right: 23px;
  text-decoration: none;
  background-position-y: 7px;
}
/* line 1443, ../sass/_template_specific.scss */
.main_content_wrapper .links .link_video:hover {
  color: #2d7da2;
}

/* line 1452, ../sass/_template_specific.scss */
.black_sections_wrapper.black_sections_fit .black_element {
  padding: 0;
}
/* line 1454, ../sass/_template_specific.scss */
.black_sections_wrapper.black_sections_fit .black_element .element_text {
  padding: 20px 0;
}
/* line 1459, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element {
  color: white;
  padding: 140px 0;
  font-weight: 100;
}
/* line 1463, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .container12 {
  display: flex;
  align-items: center;
}
/* line 1468, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .element_text {
  width: 50%;
  display: inline-block;
  vertical-align: middle;
}
/* line 1473, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .element_text .title {
  font-size: 52px;
  line-height: 56px;
  padding-bottom: 20px;
}
/* line 1479, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .element_text .description {
  font-size: 17px;
  line-height: 29px;
}
/* line 1486, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .work {
  text-align: center !important;
  float: none !important;
  margin: 0 auto;
  width: 100%;
}
/* line 1492, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .work .title {
  width: 760px;
  margin: 0 auto;
  color: #3A9BC8;
}
/* line 1497, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .work .title span {
  display: block;
  color: white;
}
/* line 1503, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .work .description {
  width: 710px;
  margin: 0 auto;
  padding-left: 0 !important;
  padding-right: 0 !important;
  text-align: left;
  margin-top: 4px;
}
/* line 1511, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .work .description .button_mail {
  display: block;
  padding-top: 49px;
  cursor: pointer;
}
/* line 1516, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .work .description .button_mail span {
  text-decoration: none;
  color: white;
  padding: 15px 30px;
  background: #3A9BC8;
  color: white;
  display: inline-block;
}
/* line 1528, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .element_img {
  width: 50%;
  display: inline-block;
  vertical-align: middle;
  position: relative;
  text-align: center;
}
/* line 1535, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .element_img .icon_maxi {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 40px;
}
/* line 1543, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .element {
  width: 395px;
  margin: 0 auto;
  padding: 12px 0;
  border-bottom: 1px solid white;
}
/* line 1549, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .element .title {
  color: #3A9BC8;
  font-size: 52px;
}
/* line 1553, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .element .title .location {
  width: 200px;
  margin: 0 auto;
  text-align: left;
}
/* line 1560, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .element .description {
  color: white;
  font-size: 17px;
}
/* line 1566, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .element_text_additional .element:first-child {
  padding-top: 0;
}
/* line 1570, ../sass/_template_specific.scss */
.black_sections_wrapper .black_element .element_text_additional .element:last-child {
  border-bottom: none;
}
/* line 1575, ../sass/_template_specific.scss */
.black_sections_wrapper .gray {
  background: #333;
}
/* line 1579, ../sass/_template_specific.scss */
.black_sections_wrapper .gray .element_text .title, .black_sections_wrapper .gray .element_text .description {
  padding-right: 80px;
}
/* line 1584, ../sass/_template_specific.scss */
.black_sections_wrapper .gray .element_img {
  text-align: right;
}
/* line 1588, ../sass/_template_specific.scss */
.black_sections_wrapper .gray .element_text_additional {
  float: right;
  width: 50%;
  text-align: center;
}
/* line 1593, ../sass/_template_specific.scss */
.black_sections_wrapper .gray .element_text_additional .element {
  margin-right: 0;
}
/* line 1599, ../sass/_template_specific.scss */
.black_sections_wrapper .black {
  background: #2b2b2b;
}
/* line 1601, ../sass/_template_specific.scss */
.black_sections_wrapper .black .container12 {
  flex-direction: row-reverse;
}
/* line 1607, ../sass/_template_specific.scss */
.black_sections_wrapper .black .element_text .title, .black_sections_wrapper .black .element_text .description {
  padding-left: 80px;
}
/* line 1612, ../sass/_template_specific.scss */
.black_sections_wrapper .black .element_img {
  text-align: left;
}
/* line 1616, ../sass/_template_specific.scss */
.black_sections_wrapper .black .element_text_additional {
  float: left;
  width: 50%;
  text-align: center;
}
/* line 1621, ../sass/_template_specific.scss */
.black_sections_wrapper .black .element_text_additional .element {
  margin-left: 0;
}
/* line 1624, ../sass/_template_specific.scss */
.black_sections_wrapper .black .element_text_additional .element .location {
  background: url("/img/datar/ico-localizacion-nosotros.png") no-repeat left;
  padding-top: 16px;
  padding-left: 56px;
  margin-top: 18px;
  background-position-y: 0;
  width: 258px;
}
/* line 1633, ../sass/_template_specific.scss */
.black_sections_wrapper .black .element_text_additional .element:first-child .location {
  margin-top: 0;
}

/* line 1642, ../sass/_template_specific.scss */
.mini_gallery_wrapper {
  padding: 10px 0;
}
/* line 1644, ../sass/_template_specific.scss */
.mini_gallery_wrapper .mini_gallery_element {
  width: 49.6%;
  display: inline-block;
}
/* line 1648, ../sass/_template_specific.scss */
.mini_gallery_wrapper .mini_gallery_element img {
  width: 100%;
}
/* line 1653, ../sass/_template_specific.scss */
.mini_gallery_wrapper .mini_gallery_element:last-child {
  float: right;
}

/* line 1659, ../sass/_template_specific.scss */
.blue_sections_wrapper {
  padding: 128px 0;
  background: #3A9BC8;
  color: white;
  text-align: center;
  font-weight: 100;
}
/* line 1667, ../sass/_template_specific.scss */
.blue_sections_wrapper .blue_title {
  font-size: 52px;
  line-height: 56px;
}
/* line 1672, ../sass/_template_specific.scss */
.blue_sections_wrapper .blue_description {
  width: 800px;
  margin: 0 auto;
  font-size: 17px;
  line-height: 29px;
  margin-top: 20px;
}
/* line 1680, ../sass/_template_specific.scss */
.blue_sections_wrapper .blue_images, .blue_sections_wrapper .blue_images_hidden {
  width: 890px;
  margin: 0 auto;
}
/* line 1684, ../sass/_template_specific.scss */
.blue_sections_wrapper .blue_images .images, .blue_sections_wrapper .blue_images_hidden .images {
  width: 33%;
  display: inline-block;
  padding: 5px 7px;
  overflow: hidden;
}
/* line 1692, ../sass/_template_specific.scss */
.blue_sections_wrapper .blue_images {
  padding-top: 80px;
}
/* line 1696, ../sass/_template_specific.scss */
.blue_sections_wrapper .more_images {
  position: relative;
  cursor: pointer;
}
/* line 1699, ../sass/_template_specific.scss */
.blue_sections_wrapper .more_images:before, .blue_sections_wrapper .more_images:after {
  content: '';
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  background-color: white;
  left: 131px;
  width: 34%;
  height: 1px;
}
/* line 1707, ../sass/_template_specific.scss */
.blue_sections_wrapper .more_images:after {
  left: auto;
  right: 131px;
}
/* line 1712, ../sass/_template_specific.scss */
.blue_sections_wrapper .more_images .icon_wrapper {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid white;
}
/* line 1719, ../sass/_template_specific.scss */
.blue_sections_wrapper .more_images .icon_wrapper i {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 32px;
  color: white;
}
/* line 1727, ../sass/_template_specific.scss */
.blue_sections_wrapper .icon_text {
  width: 400px;
}

/* line 1734, ../sass/_template_specific.scss */
.suscripcion-google-maps-wrapper {
  padding: 45px 0px 90px;
  background: white;
  color: white;
  text-align: center;
  font-weight: 100;
}
/* line 1742, ../sass/_template_specific.scss */
.suscripcion-google-maps-wrapper .blue_title {
  font-size: 52px;
  line-height: 56px;
  color: #3A9BC8;
  margin-bottom: 40px;
}

/* line 1750, ../sass/_template_specific.scss */
.newsletter_wrapper {
  position: relative;
  background-color: #3A9BC8;
  border-bottom: 1px solid white;
  padding: 30px 0 70px;
}
/* line 1755, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container {
  text-align: center;
}
/* line 1757, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container .newsletter_title {
  display: inline-block;
  vertical-align: middle;
  width: 50%;
  color: white;
  font-size: 18px;
  font-weight: 300;
}
/* line 1765, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container .newsletter_description {
  display: inline-block;
}
/* line 1768, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container .newsletter_form {
  display: inline-block;
  vertical-align: middle;
  width: 40%;
}
/* line 1773, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container .newsletter_form .input_email {
  display: inline-block;
  vertical-align: middle;
  width: 50%;
  height: 45px;
  margin-right: 15px;
  border-radius: 5px;
  padding-left: 20px;
  border: none;
  color: #555658;
  font-size: 16px;
  font-weight: 400;
}
/* line 1786, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container .newsletter_form .input_email::placeholder {
  color: #555658;
  font-size: 16px;
  font-weight: 400;
}
/* line 1792, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container .newsletter_form .button_newsletter {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 45%;
  height: 45px;
  border-radius: 5px;
  cursor: pointer;
  background-color: #555658;
  -webkit-transition: background-color 0.6s;
  -moz-transition: background-color 0.6s;
  -ms-transition: background-color 0.6s;
  -o-transition: background-color 0.6s;
  transition: background-color 0.6s;
}
/* line 1802, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container .newsletter_form .button_newsletter:hover {
  background-color: #446ca9;
}
/* line 1805, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container .newsletter_form .button_newsletter span {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: white;
}
/* line 1810, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container .newsletter_form .check_newsletter {
  position: absolute;
  bottom: 20px;
  display: block;
  left: calc((100% - 950px) / 2);
  margin: 0 auto;
  text-align: center;
}
/* line 1817, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container .newsletter_form .check_newsletter a, .newsletter_wrapper .newsletter_container .newsletter_form .check_newsletter label {
  display: inline-block;
  vertical-align: middle;
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
  -webkit-transition: color 0.4s;
  -moz-transition: color 0.4s;
  -ms-transition: color 0.4s;
  -o-transition: color 0.4s;
  transition: color 0.4s;
}
/* line 1824, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container .newsletter_form .check_newsletter:last-of-type {
  left: auto;
  right: calc((100% - 850px) / 2);
}
/* line 1828, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container .newsletter_form .check_newsletter .check_privacy {
  display: inline-block;
  vertical-align: middle;
  -webkit-appearance: none;
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 5px;
  width: 15px;
  height: 15px;
  margin-right: 5px;
  -webkit-transition: background-color 0.4s;
  -moz-transition: background-color 0.4s;
  -ms-transition: background-color 0.4s;
  -o-transition: background-color 0.4s;
  transition: background-color 0.4s;
}
/* line 1839, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container .newsletter_form .check_newsletter .check_privacy:checked {
  background-color: white;
}
/* line 1841, ../sass/_template_specific.scss */
.newsletter_wrapper .newsletter_container .newsletter_form .check_newsletter .check_privacy:checked + a, .newsletter_wrapper .newsletter_container .newsletter_form .check_newsletter .check_privacy:checked + label {
  color: white;
}

/* line 1852, ../sass/_template_specific.scss */
.form_contact_wrapper .blue_title {
  font-size: 52px;
  line-height: 56px;
  color: white;
  text-align: center;
  margin-bottom: 40px;
  font-weight: 100;
}

/* line 1863, ../sass/_template_specific.scss */
.price_tab {
  height: 80px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e3e3e3;
  color: #333;
  text-align: center;
}
/* line 1870, ../sass/_template_specific.scss */
.price_tab .price_element {
  padding: 24px 25px;
  display: inline-block;
  font-size: 24px;
  font-weight: 300;
  padding-top: 25px;
  cursor: pointer;
}
/* line 1879, ../sass/_template_specific.scss */
.price_tab .selected {
  color: #3A9BC8;
  border-bottom: 2px solid #3A9BC8;
}

/* line 1885, ../sass/_template_specific.scss */
.white_block_wrapper {
  background: white;
  text-align: center;
  padding: 130px 0 138px;
}
/* line 1890, ../sass/_template_specific.scss */
.white_block_wrapper .white_title {
  color: #3A9BC8;
  font-size: 52px;
  font-weight: 100;
  line-height: 56px;
}
/* line 1897, ../sass/_template_specific.scss */
.white_block_wrapper .collage_wrapper {
  margin-top: 80px;
}
/* line 1900, ../sass/_template_specific.scss */
.white_block_wrapper .collage_wrapper img {
  width: 100%;
}
/* line 1906, ../sass/_template_specific.scss */
.white_block_wrapper .white_content .table_text {
  display: none;
}
/* line 1910, ../sass/_template_specific.scss */
.white_block_wrapper .white_content table {
  width: 100%;
  margin-top: 80px;
}
/* line 1914, ../sass/_template_specific.scss */
.white_block_wrapper .white_content table td, .white_block_wrapper .white_content table th {
  border: 2px solid white;
}
/* line 1918, ../sass/_template_specific.scss */
.white_block_wrapper .white_content table td {
  background-color: #f5f5f5;
  vertical-align: middle;
  height: 60px;
  font-size: 17px;
  color: #333;
  font-weight: 300;
}
/* line 1927, ../sass/_template_specific.scss */
.white_block_wrapper .white_content table .text {
  width: 40%;
  padding: 0 10px;
}
/* line 1932, ../sass/_template_specific.scss */
.white_block_wrapper .white_content table .title {
  width: 20%;
  text-align: center;
  background: green;
  font-size: 25px;
  font-weight: 300;
  color: white;
  padding: 19px 0 8px;
}
/* line 1941, ../sass/_template_specific.scss */
.white_block_wrapper .white_content table .title .price {
  font-size: 52px;
  font-weight: 100;
}
/* line 1947, ../sass/_template_specific.scss */
.white_block_wrapper .white_content table .empty {
  background: transparent;
}
/* line 1951, ../sass/_template_specific.scss */
.white_block_wrapper .white_content table .plata {
  background-color: #afaeb4;
}
/* line 1955, ../sass/_template_specific.scss */
.white_block_wrapper .white_content table .oro {
  background-color: #e59721;
}
/* line 1959, ../sass/_template_specific.scss */
.white_block_wrapper .white_content table .platino {
  background-color: #c2c4a6;
}
/* line 1963, ../sass/_template_specific.scss */
.white_block_wrapper .white_content table .tick {
  background: #f5f5f5 url("/img/datar/tick-tabla.png") no-repeat center center;
}
/* line 1970, ../sass/_template_specific.scss */
.white_block_wrapper .white_gallery {
  padding-top: 78px;
}
/* line 1973, ../sass/_template_specific.scss */
.white_block_wrapper .white_gallery .gallery_element {
  width: 33%;
  display: inline-block;
  position: relative;
  overflow: hidden;
  height: 250px;
}
/* line 1980, ../sass/_template_specific.scss */
.white_block_wrapper .white_gallery .gallery_element .element_text {
  position: absolute;
  height: 110px;
  width: 100%;
  padding: 26px 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: none;
}
/* line 1989, ../sass/_template_specific.scss */
.white_block_wrapper .white_gallery .gallery_element .element_text .title {
  color: white;
  font-size: 19px;
}
/* line 1994, ../sass/_template_specific.scss */
.white_block_wrapper .white_gallery .gallery_element .element_text .description {
  color: #8d8d8d;
}
/* line 1999, ../sass/_template_specific.scss */
.white_block_wrapper .white_gallery .gallery_element .element_text .social .social_element {
  color: #3A9BC8;
  display: inline-block;
  font-weight: 300;
}
/* line 2004, ../sass/_template_specific.scss */
.white_block_wrapper .white_gallery .gallery_element .element_text .social .social_element a {
  color: inherit;
  text-decoration: none;
}
/* line 2010, ../sass/_template_specific.scss */
.white_block_wrapper .white_gallery .gallery_element .element_text .social .linkdin a {
  background: url("/img/datar/flecha-enlace-banner.png") no-repeat right;
  padding-right: 10px;
  background-position-y: 6px;
}
/* line 2019, ../sass/_template_specific.scss */
.white_block_wrapper .white_gallery .gallery_element .element_image .gray_img {
  filter: grayscale(100%);
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  vertical-align: middle;
  transition: 1s all;
}
/* line 2030, ../sass/_template_specific.scss */
.white_block_wrapper .white_gallery .gallery_element:hover .gray_img {
  filter: grayscale(0%);
  -webkit-filter: grayscale(0%);
  -moz-filter: grayscale(0%);
  -ms-filter: grayscale(0%);
  -o-filter: grayscale(0%);
  transition: 1s all;
}
/* line 2039, ../sass/_template_specific.scss */
.white_block_wrapper .white_gallery .gallery_element .icon {
  position: absolute;
  width: 100%;
  top: 95px;
  bottom: 0;
  text-align: center;
}

/* line 2051, ../sass/_template_specific.scss */
footer {
  background: rgba(255, 255, 255, 0.2);
  padding: 60px 0;
  border-top: 1px solid #c8c8c8;
  font-size: 14px;
  color: white !important;
}
/* line 2058, ../sass/_template_specific.scss */
footer hr {
  border: 0 solid transparent;
  border-top: 1px solid #c8c8c8;
  margin: 12px 0;
}
/* line 2064, ../sass/_template_specific.scss */
footer .footer_element.arrow {
  padding-right: 10px;
  color: #3A9BC8;
}
/* line 2069, ../sass/_template_specific.scss */
footer .footer_element.arrow a {
  display: block;
  font-size: 14px;
}
/* line 2073, ../sass/_template_specific.scss */
footer .footer_element.arrow a:after {
  content: "\f054";
  font-family: "Fontawesome";
  color: #3A9BC8;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 0;
}
/* line 2081, ../sass/_template_specific.scss */
footer .footer_element.arrow a:hover {
  color: #999999;
}
/* line 2085, ../sass/_template_specific.scss */
footer .footer_element.arrow a#section-active {
  font-weight: bold;
}
/* line 2090, ../sass/_template_specific.scss */
footer .footer_element {
  position: relative;
}
/* line 2094, ../sass/_template_specific.scss */
footer .footer_element .text {
  display: inline-block;
}
/* line 2098, ../sass/_template_specific.scss */
footer .footer_element a {
  color: white;
  text-decoration: none;
}
/* line 2103, ../sass/_template_specific.scss */
footer .lang_footer {
  float: right;
}
/* line 2106, ../sass/_template_specific.scss */
footer .lang_footer a {
  padding: 0 10px;
}
/* line 2109, ../sass/_template_specific.scss */
footer .lang_footer a img {
  width: 20px;
}
/* line 2113, ../sass/_template_specific.scss */
footer .lang_footer a:hover {
  opacity: 0.8;
}
/* line 2119, ../sass/_template_specific.scss */
footer #social {
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 2129, ../sass/_template_specific.scss */
footer #social a {
  padding: 0 10px;
  display: inline-block;
}
/* line 2133, ../sass/_template_specific.scss */
footer #social a .fa, footer #social a .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv #newsletter-button:before, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv footer #social a #newsletter-button:before {
  font-size: 24px;
  color: #3A9BC8;
}
/* line 2137, ../sass/_template_specific.scss */
footer #social a .fa:hover, footer #social a .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv #newsletter-button:hover:before, .wordpress_block .newsletter_wrapper .newsletter_element #newsletterButtonExternalDiv footer #social a #newsletter-button:hover:before {
  opacity: .8;
}
/* line 2142, ../sass/_template_specific.scss */
footer #social a:hover {
  opacity: 0.8;
}
/* line 2148, ../sass/_template_specific.scss */
footer .texto_legal {
  margin-top: 43px;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: white;
  text-align: center;
}
/* line 2156, ../sass/_template_specific.scss */
footer .texto_legal .powered {
  color: #3A9BC8;
}
/* line 2161, ../sass/_template_specific.scss */
footer .logos_footer {
  text-align: center;
  margin-top: 20px;
}
/* line 2165, ../sass/_template_specific.scss */
footer .logos_footer img {
  height: 40px;
}
/* line 2170, ../sass/_template_specific.scss */
footer .popup_legal_wrapper {
  display: inline-block;
  width: 100%;
  text-align: center;
}
/* line 2175, ../sass/_template_specific.scss */
footer .popup_legal_wrapper a {
  color: #3A9BC8;
  display: inline-block;
  margin: 0 5px;
  text-decoration: none;
}
/* line 2181, ../sass/_template_specific.scss */
footer .popup_legal_wrapper a:hover {
  opacity: .8;
}

/* line 2188, ../sass/_template_specific.scss */
.fancybox-opened {
  z-index: 8030;
}

/* line 2192, ../sass/_template_specific.scss */
.fancybox-opened .fancybox-skin {
  padding: 0px !important;
  box-shadow: 0 0 0 transparent;
}

/*/ Popup idioma /*/
/* line 2199, ../sass/_template_specific.scss */
.fancybox-lang {
  width: 400px !important;
}
/* line 2203, ../sass/_template_specific.scss */
.fancybox-lang.fancybox-opened .fancybox-skin {
  padding: 0 0 40px !important;
}
/* line 2206, ../sass/_template_specific.scss */
.fancybox-lang .fancybox-outer {
  height: 100% !important;
  padding: 1px !important;
  border-radius: 0 !important;
  box-shadow: none;
}
/* line 2213, ../sass/_template_specific.scss */
.fancybox-lang .fancybox-inner {
  overflow: visible !important;
  width: 100% !important;
}
/* line 2218, ../sass/_template_specific.scss */
.fancybox-lang .fancybox-close {
  background: black;
  text-align: center;
  top: 0 !important;
  right: 0 !important;
  width: 42px !important;
  height: 42px !important;
}
/* line 2227, ../sass/_template_specific.scss */
.fancybox-lang .fancybox-close:before {
  content: '\f00d';
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  display: block;
  color: white;
  font-size: 23px;
  font-family: "fontawesome", sans-serif;
}
/* line 2235, ../sass/_template_specific.scss */
.fancybox-lang .fancybox-close:hover {
  opacity: 0.8;
}
/* line 2240, ../sass/_template_specific.scss */
.fancybox-lang #popup_lang_content {
  padding: 40px;
  margin: 40px;
  border: 1px solid #ddd;
  text-align: center;
}
/* line 2246, ../sass/_template_specific.scss */
.fancybox-lang #popup_lang_content .title {
  font-size: 16px;
  margin-bottom: 20px;
  text-transform: uppercase;
  color: #666;
}
/* line 2252, ../sass/_template_specific.scss */
.fancybox-lang #popup_lang_content .title b {
  display: block;
  font-size: 28px;
  color: black;
}
/* line 2259, ../sass/_template_specific.scss */
.fancybox-lang #popup_lang_content li {
  display: block;
  text-transform: uppercase;
  margin: 0 auto;
  width: 70%;
}
/* line 2265, ../sass/_template_specific.scss */
.fancybox-lang #popup_lang_content li:first-child a {
  border: none;
}
/* line 2269, ../sass/_template_specific.scss */
.fancybox-lang #popup_lang_content li a {
  line-height: 40px;
  border-top: 1px dotted #ccc;
  display: block;
  color: #666;
  text-decoration: none;
  font-size: 16px;
  letter-spacing: 0.05em;
}
/* line 2278, ../sass/_template_specific.scss */
.fancybox-lang #popup_lang_content li a:hover {
  color: #999999;
}
/* line 2285, ../sass/_template_specific.scss */
.fancybox-lang #popup_login {
  padding: 40px;
  margin: 40px;
  border: 1px solid #ddd;
  text-align: center;
}
/* line 2291, ../sass/_template_specific.scss */
.fancybox-lang #popup_login .title {
  font-size: 16px;
  margin-bottom: 20px;
  text-transform: uppercase;
  color: #666;
}
/* line 2297, ../sass/_template_specific.scss */
.fancybox-lang #popup_login .title strong {
  display: block;
  font-size: 28px;
  color: black;
}
/* line 2304, ../sass/_template_specific.scss */
.fancybox-lang #popup_login .login_button {
  background: #446ca9;
  border: 0;
  color: white;
  padding: 10px 67px;
  margin: 0 auto;
  text-decoration: none;
}
/* line 2313, ../sass/_template_specific.scss */
.fancybox-lang #popup_login .input_login {
  margin-bottom: 10px;
}
/* line 2316, ../sass/_template_specific.scss */
.fancybox-lang #popup_login .input_login input {
  border: 1px solid #ccc;
  color: #666;
  width: 176px;
  padding: 7.5px 10px;
  background: transparent;
  font-size: 12px;
}
/* line 2326, ../sass/_template_specific.scss */
.fancybox-lang #popup_login #select-tool {
  margin-bottom: 10px;
  -webkit-appearance: none;
  border-radius: 0;
  border: 1px solid #ccc;
  width: 176px;
  padding: 7.5px 10px;
  color: #666;
  background: url(/img/datar/flecha-select-idioma.png) no-repeat 90% center;
  font-size: 12px;
}
/* line 2338, ../sass/_template_specific.scss */
.fancybox-lang #popup_login .others_tools {
  margin-top: 10px;
}
/* line 2341, ../sass/_template_specific.scss */
.fancybox-lang #popup_login .others_tools .login_button {
  padding: 10px 52px;
}

/* line 2350, ../sass/_template_specific.scss */
.fancybox-video .fancybox-outer {
  padding: 0 !important;
  background: transparent !important;
}
/* line 2355, ../sass/_template_specific.scss */
.fancybox-video .fancybox-close {
  display: none;
}

/* line 2365, ../sass/_template_specific.scss */
.fancybox-contact .fancybox-outer {
  height: 255px !important;
  padding: 1px !important;
  border-radius: 0 !important;
}
/* line 2371, ../sass/_template_specific.scss */
.fancybox-contact .fancybox-inner {
  overflow: visible !important;
}
/* line 2375, ../sass/_template_specific.scss */
.fancybox-contact .fancybox-close {
  background: url("/img/datar/cerrar-idioma.png") !important;
  top: 0 !important;
  right: 0 !important;
  width: 42px !important;
  height: 42px !important;
}
/* line 2383, ../sass/_template_specific.scss */
.fancybox-contact .fancybox-close:hover {
  opacity: 0.8;
}
/* line 2388, ../sass/_template_specific.scss */
.fancybox-contact .popup_validation {
  padding: 40px;
  margin: 40px;
  border: 1px solid #ddd;
  text-align: center;
}
/* line 2394, ../sass/_template_specific.scss */
.fancybox-contact .popup_validation .title {
  font-size: 16px;
  margin-bottom: 20px;
  text-transform: uppercase;
  color: #666;
}
/* line 2400, ../sass/_template_specific.scss */
.fancybox-contact .popup_validation .title strong {
  display: block;
  font-size: 28px;
  color: black;
}

/* line 2410, ../sass/_template_specific.scss */
.news_sections_wrapper {
  background: #333;
  padding-top: 105px;
  padding-bottom: 20px;
  font-weight: 100;
}
/* line 2416, ../sass/_template_specific.scss */
.news_sections_wrapper .news_element {
  color: white;
  width: 49%;
  display: inline-block;
  text-align: center;
  margin-bottom: 70px;
  vertical-align: top;
}
/* line 2425, ../sass/_template_specific.scss */
.news_sections_wrapper .image_new {
  float: left;
}
/* line 2430, ../sass/_template_specific.scss */
.news_sections_wrapper .new_text .date_text {
  font-size: 52px;
  line-height: 56px;
  padding: 20px 0;
}
/* line 2436, ../sass/_template_specific.scss */
.news_sections_wrapper .new_text .new_description {
  font-size: 17px;
  line-height: 29px;
  width: 420px;
  text-transform: uppercase;
  margin: 0 auto;
}
/* line 2444, ../sass/_template_specific.scss */
.news_sections_wrapper .new_text .link {
  margin-top: 40px;
}
/* line 2447, ../sass/_template_specific.scss */
.news_sections_wrapper .new_text .link a {
  font-size: 24px;
  text-decoration: none;
  background: url("/img/datar/flecha-enlace-wordpress.png") no-repeat right;
  padding: 15px;
  color: #446ca9;
}
/* line 2454, ../sass/_template_specific.scss */
.news_sections_wrapper .new_text .link a:hover {
  color: #355585;
}
/* line 2461, ../sass/_template_specific.scss */
.news_sections_wrapper .new_text.cover {
  text-align: left;
  float: left;
  margin-left: 30px;
}
/* line 2466, ../sass/_template_specific.scss */
.news_sections_wrapper .new_text.cover .new_description {
  width: 270px;
}
/* line 2470, ../sass/_template_specific.scss */
.news_sections_wrapper .new_text.cover .link a {
  text-align: left;
  padding-left: 0;
}

/* line 2, ../sass/_contact.scss */
.blocks_contact_wrapper {
  background: #333;
  padding-top: 72px;
  padding-bottom: 95px;
}
/* line 7, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element {
  width: calc(100% / 4 - 10px);
  float: left;
  color: white;
  font-weight: 100;
  text-align: center;
  margin-right: 10px;
  padding-bottom: 10px;
  background: rgba(255, 255, 255, 0.05);
}
/* line 17, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element:last-child {
  margin-right: 0;
}
/* line 21, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element:first-of-type {
  width: calc(100% - 10px);
  padding-bottom: 20px;
}
/* line 24, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element:first-of-type .title {
  color: #3A9BC8;
  font-size: 42px;
  padding: 20px 45px;
  background-size: 34px;
}
/* line 31, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element:nth-child(2), .blocks_contact_wrapper .block_element:nth-child(3) {
  width: calc((100% - (100% / 4 - 10px)) / 2 - 15px);
  margin-bottom: 10px;
}
/* line 37, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element .title {
  color: #446ca9;
  font-size: 32px;
  padding: 10px 45px;
  width: auto;
  margin: 0 auto;
  background: url(/img/datar/ico-localizacion-nosotros.png) no-repeat left;
  display: inline-block;
  background-size: 25px;
}
/* line 47, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element .title .img_loc {
  display: inline-block;
}
/* line 51, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element .title h3 {
  display: inline-block;
}
/* line 56, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element .content {
  font-size: 17px;
  line-height: 29px;
  width: 100%;
  margin: 0 auto;
}
/* line 62, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element .content strong {
  font-weight: 300;
}
/* line 67, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element .separator {
  width: 220px;
}
/* line 71, ../sass/_contact.scss */
.blocks_contact_wrapper .iframe_maps {
  display: table;
  width: calc(100% - 10px);
  padding-top: 10px;
}
/* line 76, ../sass/_contact.scss */
.blocks_contact_wrapper .iframe_maps iframe {
  width: 100%;
  height: 325px;
}

/* line 83, ../sass/_contact.scss */
.slider_contact_wrapper {
  background: #2b2b2b;
  padding: 100px 0;
  position: relative;
}
/* line 88, ../sass/_contact.scss */
.slider_contact_wrapper .img_slider {
  width: 100%;
  height: 400px;
}
/* line 93, ../sass/_contact.scss */
.slider_contact_wrapper .overlay {
  position: absolute;
  top: 50%;
  left: 20px;
  -moz-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  -webkit-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  width: 350px;
  height: 350px;
}
/* line 106, ../sass/_contact.scss */
.slider_contact_wrapper .overlay .text_block {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 14px;
  font-weight: 100;
  width: 300px;
}
/* line 119, ../sass/_contact.scss */
.slider_contact_wrapper .overlay .text_block .title {
  font-size: 35px;
  padding: 0 20px 0px;
  margin-bottom: 10px;
  background: url(/img/datar/ico-localizacion-nosotros.png) no-repeat left;
  width: auto;
  display: inline-block;
  background-size: contain;
  padding-left: 35px;
  margin-left: -15px;
}
/* line 135, ../sass/_contact.scss */
.slider_contact_wrapper .overlay .text_block .content {
  width: 250px;
  margin: 0 auto;
}
/* line 142, ../sass/_contact.scss */
.slider_contact_wrapper .flex-control-nav {
  position: absolute;
  bottom: 115px;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2;
}
/* line 150, ../sass/_contact.scss */
.slider_contact_wrapper .flex-control-nav li {
  display: inline-block;
}
/* line 153, ../sass/_contact.scss */
.slider_contact_wrapper .flex-control-nav li a.flex-active {
  background: url("/img/datar/sprit1.png") no-repeat top left;
}
/* line 157, ../sass/_contact.scss */
.slider_contact_wrapper .flex-control-nav li a {
  background: url("/img/datar/sprit2.png") no-repeat top left;
  color: transparent;
  width: 10px;
  margin-right: 7px;
  display: inline-block;
}
/* line 165, ../sass/_contact.scss */
.slider_contact_wrapper .flex-control-nav li:last-child a {
  margin-right: 0;
}

/* line 172, ../sass/_contact.scss */
.form_contact_wrapper {
  background: #3A9BC8;
  padding: 100px 0;
}
/* line 177, ../sass/_contact.scss */
.form_contact_wrapper form input {
  padding-left: 39px;
  margin-bottom: 10px;
  color: white;
  height: 39px;
  border-radius: 0px;
  border: 0;
}
/* line 185, ../sass/_contact.scss */
.form_contact_wrapper form input::-webkit-input-placeholder {
  color: white;
}
/* line 189, ../sass/_contact.scss */
.form_contact_wrapper form input:-moz-placeholder {
  color: white;
}
/* line 193, ../sass/_contact.scss */
.form_contact_wrapper form input:-ms-input-placeholder {
  color: white;
}
/* line 198, ../sass/_contact.scss */
.form_contact_wrapper form label.error {
  color: white;
  font-size: 14px;
  margin-bottom: 10px;
}
/* line 204, ../sass/_contact.scss */
.form_contact_wrapper form .name {
  background: rgba(255, 255, 255, 0.2) url(/img/datar/formname.png) no-repeat !important;
}
/* line 208, ../sass/_contact.scss */
.form_contact_wrapper form .mail {
  background: rgba(255, 255, 255, 0.2) url(/img/datar/formmail.png) no-repeat;
}
/* line 212, ../sass/_contact.scss */
.form_contact_wrapper form .business {
  background: rgba(255, 255, 255, 0.2) url(/img/datar/icos/business.png) no-repeat;
}
/* line 216, ../sass/_contact.scss */
.form_contact_wrapper form .phone {
  background: rgba(255, 255, 255, 0.2) url(/img/datar/formphone.png) no-repeat;
}
/* line 220, ../sass/_contact.scss */
.form_contact_wrapper form .hotel {
  background: rgba(255, 255, 255, 0.2) url(/img/datar/ico-hotel.png) no-repeat;
}
/* line 224, ../sass/_contact.scss */
.form_contact_wrapper form .province {
  background: rgba(255, 255, 255, 0.2) url(/img/datar/ico-provincia.png) no-repeat;
}
/* line 228, ../sass/_contact.scss */
.form_contact_wrapper form .comment_wrapper {
  position: relative;
}
/* line 230, ../sass/_contact.scss */
.form_contact_wrapper form .comment_wrapper:before {
  content: "\f075";
  position: absolute;
  top: 0;
  left: 0;
  display: inline-block;
  background-color: white;
  width: 30px;
  height: 40px;
  padding: 10px 7px;
  color: rgba(128, 128, 128, 0.6);
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}
/* line 246, ../sass/_contact.scss */
.form_contact_wrapper form .comment_wrapper textarea {
  padding-left: 39px;
}
/* line 251, ../sass/_contact.scss */
.form_contact_wrapper form textarea {
  height: 88px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0;
  border: 0;
  color: white;
}
/* line 258, ../sass/_contact.scss */
.form_contact_wrapper form textarea::-webkit-input-placeholder {
  color: white;
}
/* line 262, ../sass/_contact.scss */
.form_contact_wrapper form textarea:-moz-placeholder {
  color: white;
}
/* line 266, ../sass/_contact.scss */
.form_contact_wrapper form textarea:-ms-input-placeholder {
  color: white;
}
/* line 270, ../sass/_contact.scss */
.form_contact_wrapper form #contact-button-wrapper {
  display: inline-block;
  vertical-align: top;
  margin-left: 10px;
  width: calc(100% - 324px);
  cursor: pointer;
}
/* line 277, ../sass/_contact.scss */
.form_contact_wrapper form #contact-button {
  color: white;
  background-color: #555658 !important;
  display: inline-block;
  text-align: center;
  width: 100%;
  border-radius: 5px;
  margin-right: 16px;
  text-transform: uppercase !important;
  font-size: 16px;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 10px;
  padding-right: 10px;
  cursor: pointer;
  padding: 28px 10px;
}
/* line 295, ../sass/_contact.scss */
.form_contact_wrapper form #contact-button:hover {
  opacity: 0.8;
}
/* line 299, ../sass/_contact.scss */
.form_contact_wrapper form .g-recaptcha {
  display: inline-block;
}
/* line 303, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit {
  text-align: center;
}
/* line 305, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit a {
  text-decoration: none;
}
/* line 309, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit .lopd_button {
  padding-left: 13px;
  display: inline-block;
}
/* line 313, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit .lopd_button a {
  display: inline-block;
  vertical-align: middle;
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
  -webkit-transition: color 0.4s;
  -moz-transition: color 0.4s;
  -ms-transition: color 0.4s;
  -o-transition: color 0.4s;
  transition: color 0.4s;
}
/* line 321, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit .lopd_button input[type="checkbox"] {
  display: inline-block;
  vertical-align: middle;
  -webkit-appearance: none;
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 5px;
  padding-left: 0;
  width: 15px;
  height: 15px;
  margin: 0 5px 0 0;
  -webkit-transition: background-color 0.4s;
  -moz-transition: background-color 0.4s;
  -ms-transition: background-color 0.4s;
  -o-transition: background-color 0.4s;
  transition: background-color 0.4s;
}
/* line 333, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit .lopd_button input[type="checkbox"]:checked {
  background-color: white;
}
/* line 335, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit .lopd_button input[type="checkbox"]:checked + a, .form_contact_wrapper form .row.submit .lopd_button input[type="checkbox"]:checked + label {
  color: white;
}
/* line 341, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit .lopd_button label[for="privacy"].error {
  position: absolute;
  top: 15px;
  margin-left: 4px;
}

/* line 352, ../sass/_contact.scss */
.fancybox-form-new .form_contact_wrapper {
  padding: 30px;
  box-sizing: border-box;
}
/* line 356, ../sass/_contact.scss */
.fancybox-form-new .form_contact_wrapper #contact_file_download, .fancybox-form-new .form_contact_wrapper .contact_file_download {
  width: auto;
}
/* line 358, ../sass/_contact.scss */
.fancybox-form-new .form_contact_wrapper #contact_file_download input[type=text], .fancybox-form-new .form_contact_wrapper .contact_file_download input[type=text] {
  width: 100%;
  margin-right: 0;
}
/* line 366, ../sass/_contact.scss */
.fancybox-form-new .form_contact_wrapper form .box input {
  display: block;
  width: 100%;
}
/* line 372, ../sass/_contact.scss */
.fancybox-form-new .form_contact_wrapper form .lopd_button {
  float: none;
}

/* line 379, ../sass/_contact.scss */
.file_download_form {
  background-color: #555658;
}
/* line 383, ../sass/_contact.scss */
.file_download_form #contact-button-wrapper {
  background-color: #3A9BC8;
  width: calc(100% - 10px) !important;
  margin-left: 0 !important;
  padding: 10px;
  color: white;
  text-align: center;
  text-transform: uppercase;
}
/* line 393, ../sass/_contact.scss */
.file_download_form #contact_file_download, .file_download_form .contact_file_download {
  width: 500px;
  margin: auto;
}
/* line 396, ../sass/_contact.scss */
.file_download_form #contact_file_download input[type=text], .file_download_form .contact_file_download input[type=text] {
  width: calc(50% - 10px);
  margin-right: 10px;
}
/* line 402, ../sass/_contact.scss */
.file_download_form .lopd_button {
  float: left;
  position: relative;
  margin-top: 8px !important;
  display: flex;
  align-items: center;
}
/* line 409, ../sass/_contact.scss */
.file_download_form .lopd_button input[type="checkbox"] {
  height: auto;
  margin: 0 8px 0 0;
}
/* line 414, ../sass/_contact.scss */
.file_download_form .lopd_button label[for="privacy"].error {
  position: absolute;
  top: 25px;
  margin-left: 4px;
}
/* line 420, ../sass/_contact.scss */
.file_download_form .lopd_button a {
  color: #979797;
  font-size: 12px;
}
/* line 424, ../sass/_contact.scss */
.file_download_form .lopd_button a:hover {
  opacity: 0.8;
}
/* line 430, ../sass/_contact.scss */
.file_download_form .name {
  width: 49%;
  float: left;
}
/* line 435, ../sass/_contact.scss */
.file_download_form .name#surname {
  float: right;
  background: rgba(255, 255, 255, 0.2);
}
/* line 441, ../sass/_contact.scss */
.file_download_form label.error {
  display: none !important;
}
/* line 445, ../sass/_contact.scss */
.file_download_form input.error {
  border: 1px solid red;
}
/* line 449, ../sass/_contact.scss */
.file_download_form input[type="checkbox"].error {
  position: relative;
  z-index: 1;
}
/* line 453, ../sass/_contact.scss */
.file_download_form input[type="checkbox"].error:before {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  bottom: -1px;
  right: -1px;
  border: 1px solid red;
  z-index: 0;
}

@media screen and (max-width: 500px) {
  /* line 468, ../sass/_contact.scss */
  .file_download_form {
    padding: 15px;
  }
  /* line 471, ../sass/_contact.scss */
  .file_download_form #contact_file_download, .file_download_form .contact_file_download {
    width: 100%;
  }
  /* line 473, ../sass/_contact.scss */
  .file_download_form #contact_file_download input[type=text], .file_download_form .contact_file_download input[type=text] {
    width: 100%;
    margin-right: 0;
  }
  /* line 479, ../sass/_contact.scss */
  .file_download_form #contact-button-wrapper {
    display: inline-block;
    vertical-align: top;
  }

  /* line 486, ../sass/_contact.scss */
  body .fancybox-form-new {
    width: calc(100% - 60px) !important;
  }
  /* line 489, ../sass/_contact.scss */
  body .fancybox-form-new .fancybox-inner {
    width: 100% !important;
  }
  /* line 493, ../sass/_contact.scss */
  body .fancybox-form-new form .lopd_button {
    margin-bottom: 10px;
  }
}
@media screen and (max-width: 700px) {
  /* line 501, ../sass/_contact.scss */
  .form_contact_wrapper.file_download_form .lopd_button:last-of-type {
    right: auto !important;
  }
}
/************************************************/
/**************** Bottom Pop-up *****************/
/************************************************/
/* line 2483, ../sass/_template_specific.scss */
.bottom_popup {
  position: fixed;
  width: 100%;
  height: 60px;
  background: #5a5a5a;
  left: 0;
  bottom: 0;
  z-index: 1000;
  display: none !important;
}

/* line 2494, ../sass/_template_specific.scss */
.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

/* line 2500, ../sass/_template_specific.scss */
.bottom_popup .bottom_popup_text {
  width: 590px;
  float: left;
  color: white;
  font-family: Arial, sans-serif;
  font-size: 14px;
}

/* line 2508, ../sass/_template_specific.scss */
.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

/* line 2512, ../sass/_template_specific.scss */
.close_button {
  float: right;
  cursor: pointer;
}

/* line 2517, ../sass/_template_specific.scss */
button.bottom_popup_button {
  background: darkblue;
  width: 150px;
  height: 40px;
  font-size: 18px;
  margin-top: 10px;
}
/* line 2523, ../sass/_template_specific.scss */
button.bottom_popup_button:hover {
  background: #0000be;
}

/* line 2528, ../sass/_template_specific.scss */
#wrapper2 {
  width: 980px;
  margin: 0 auto;
}

/************************************************/
/**************** pop-up inicial ****************/
/************************************************/
/* line 2537, ../sass/_template_specific.scss */
.popup_inicial {
  position: relative;
}

/* line 2542, ../sass/_template_specific.scss */
.popup_img {
  position: relative;
  overflow: hidden;
}
/* line 2548, ../sass/_template_specific.scss */
.popup_img img {
  position: relative;
  margin: auto;
  max-width: 200%;
  vertical-align: middle;
}

/* line 2562, ../sass/_template_specific.scss */
.fancybox-news .fancybox-outer {
  padding: 0 !important;
}

/* line 2567, ../sass/_template_specific.scss */
.popup_text {
  position: absolute;
  top: 85px;
  width: 100%;
  text-align: center;
}

/* line 2574, ../sass/_template_specific.scss */
.popup_title {
  font-size: 24px;
  color: white !important;
  font-weight: 400;
  text-transform: uppercase;
  padding: 0;
}

/* line 2582, ../sass/_template_specific.scss */
.popup_description {
  font-size: 16px;
  line-height: 27px;
  color: white !important;
  margin-top: 0px;
}
/* line 2588, ../sass/_template_specific.scss */
.popup_description hr {
  width: 150px;
  margin: 10px auto;
}
/* line 2593, ../sass/_template_specific.scss */
.popup_description .line1 {
  font-size: 18px;
  font-weight: lighter;
  text-transform: uppercase;
}
/* line 2599, ../sass/_template_specific.scss */
.popup_description .line2 {
  text-transform: uppercase;
  font-size: 14px;
  font-weight: lighter;
}
/* line 2605, ../sass/_template_specific.scss */
.popup_description .line3 {
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 400;
}
/* line 2611, ../sass/_template_specific.scss */
.popup_description a.button_download {
  background: #3A9BC8;
  padding: 5px 25px;
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 400;
  color: white;
  display: inline-block;
  margin-top: 10px;
}

/* line 2623, ../sass/_template_specific.scss */
.popup_lopd {
  font-size: 13px;
}

/* line 2627, ../sass/_template_specific.scss */
.lopd_button {
  margin-top: 2px !important;
}

/* line 2631, ../sass/_template_specific.scss */
.popup_form {
  text-align: center;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 90px;
}

/* line 2639, ../sass/_template_specific.scss */
.popup_inicial form {
  padding: 20px;
  padding-bottom: 25px;
  font-size: 18px;
  display: inline-block;
  position: relative;
}

/* line 2647, ../sass/_template_specific.scss */
.popup_inicial form input {
  width: 250px;
  height: 30px;
  display: inline-block;
  vertical-align: middle;
}

/* line 2654, ../sass/_template_specific.scss */
.popup_inicial form .lopd_button {
  margin-top: 10px;
  text-align: left;
}
/* line 2658, ../sass/_template_specific.scss */
.popup_inicial form .lopd_button input[type="checkbox"] {
  width: auto;
  vertical-align: middle;
}
/* line 2663, ../sass/_template_specific.scss */
.popup_inicial form .lopd_button a {
  display: inline;
  vertical-align: middle;
  text-decoration: none;
  color: white;
}
/* line 2669, ../sass/_template_specific.scss */
.popup_inicial form .lopd_button a:hover {
  opacity: 0.8;
}

/* line 2675, ../sass/_template_specific.scss */
.popup_inicial button {
  background: #3A9BC8;
  width: 150px;
  height: 30px;
  font-size: 18px;
  border: 1px solid #CECCCF;
  color: #FFFFFF;
  margin-left: -6px;
  display: inline-block !important;
  vertical-align: middle;
  border-radius: 0;
  border-left-color: white;
  text-transform: uppercase;
}
/* line 2689, ../sass/_template_specific.scss */
.popup_inicial button:hover {
  background: #62afd3;
}

/* line 2695, ../sass/_template_specific.scss */
.popup_message {
  color: black;
  margin-top: 20px;
}

/* line 2700, ../sass/_template_specific.scss */
#id_email {
  background: white;
  border: 1px solid #CECCCF;
  border-right-color: transparent;
  font-size: 14px;
  border-radius: 0;
  padding-left: 10px;
}
/* line 2708, ../sass/_template_specific.scss */
#id_email.error {
  border-color: red;
  border-right-color: transparent;
}

/* line 2718, ../sass/_template_specific.scss */
.form_popup label {
  color: white;
  display: none !important;
}

/* line 2724, ../sass/_template_specific.scss */
.icons_blue {
  margin-top: 30px;
}
/* line 2727, ../sass/_template_specific.scss */
.icons_blue .icon_element {
  display: inline-block;
  float: left;
  width: 50%;
  border-right: 1px solid white;
  margin-bottom: 20px;
  text-align: right;
}
/* line 2735, ../sass/_template_specific.scss */
.icons_blue .icon_element.even {
  float: right;
  clear: right;
  border: none;
  text-align: left;
}
/* line 2741, ../sass/_template_specific.scss */
.icons_blue .icon_element.even .icon_image {
  margin-right: 0px;
  margin-left: 20px;
}
/* line 2746, ../sass/_template_specific.scss */
.icons_blue .icon_element.even .icon_text {
  margin-right: 0px;
  margin-left: 10px;
}
/* line 2752, ../sass/_template_specific.scss */
.icons_blue .icon_element .icon_image {
  display: inline-block;
  vertical-align: middle;
  margin-right: 20px;
}
/* line 2758, ../sass/_template_specific.scss */
.icons_blue .icon_element .icon_text {
  display: inline-block;
  vertical-align: middle;
  margin-right: 10px;
}
/* line 2765, ../sass/_template_specific.scss */
.icons_blue hr {
  clear: both;
}

/* line 2770, ../sass/_template_specific.scss */
.icons_blue_hidden {
  display: none;
}

/* line 2775, ../sass/_template_specific.scss */
.fancybox-lopd {
  width: 800px !important;
}

/* line 2779, ../sass/_template_specific.scss */
#lopd_content {
  width: 800px;
  padding: 1em;
}

/* line 2784, ../sass/_template_specific.scss */
.col-sm-12 {
  width: 100%;
  box-sizing: border-box;
  padding-right: 10px;
}
/* line 2788, ../sass/_template_specific.scss */
.col-sm-12 textarea {
  width: 100%;
}

/* line 2793, ../sass/_template_specific.scss */
.col-sm-6 {
  display: inline-block;
  box-sizing: border-box;
  padding-right: 10px;
  width: 50%;
}
/* line 2799, ../sass/_template_specific.scss */
.col-sm-6 .box input {
  width: 100%;
}

/* line 2806, ../sass/_template_specific.scss */
.hidden_download_popup .fancybox-skin, .hidden_download_popup .hidden_see_more_info, .hidden_download_popup .fancybox-outer {
  background: transparent;
  box-shadow: none;
}
/* line 2812, ../sass/_template_specific.scss */
.hidden_download_popup .buttons_wrapper .button_element {
  display: table;
  margin: auto;
  padding: 10px 20px;
  margin-top: 15px;
  background: #3A9BC8;
  color: white;
  text-decoration: none;
}

/* line 2824, ../sass/_template_specific.scss */
.newsletter_additional_wrapper {
  margin-top: 65px;
}
/* line 2828, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form .input_wrapper {
  display: inline-block;
  width: calc((100% - 10px) / 2);
  float: left;
  margin-right: 10px;
  margin-bottom: 10px;
  position: relative;
}
/* line 2836, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form .input_wrapper:before {
  font-family: "FontAwesome";
  color: white;
  font-size: 16px;
  z-index: 2;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  left: 19.5px;
}
/* line 2845, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form .input_wrapper:nth-of-type(1):before {
  content: '\f0e0';
}
/* line 2849, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form .input_wrapper:nth-of-type(2):before {
  content: '\f007';
}
/* line 2853, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form .input_wrapper:nth-of-type(3):before {
  content: '\f007';
}
/* line 2857, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form .input_wrapper:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 39px;
  width: 39px;
  background: #446ca9;
  z-index: 1;
}
/* line 2868, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form .input_wrapper:nth-of-type(2n) {
  margin-right: 0;
}
/* line 2873, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form input:not(.check_privacy) {
  display: inline-block;
  width: 100%;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  height: 39px;
  padding: 0 10px 0 49px;
  border: 1px solid lightgrey;
  background: white;
}
/* line 2884, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form input:not(.check_privacy).error {
  border-color: red;
}
/* line 2888, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form input:not(.check_privacy) + label {
  display: none !important;
}
/* line 2893, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form .popup_policy_newsletter {
  display: inline-block;
  float: left;
  margin-top: 5px;
  width: 100%;
}
/* line 2899, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form .popup_policy_newsletter label.error {
  display: none !important;
}
/* line 2903, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form .popup_policy_newsletter a {
  color: #446ca9;
  text-decoration: none;
  font-size: 14px;
}
/* line 2910, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form #send_newsletter_additional {
  display: inline-block;
  float: right;
  width: calc((100% - 10px) / 2);
  height: 39px;
  background: #446ca9;
  color: white;
  cursor: pointer;
  padding: 0px 30px;
  font-size: 13px;
  text-transform: uppercase;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-transition: opacity 0.4s;
  -moz-transition: opacity 0.4s;
  -ms-transition: opacity 0.4s;
  -o-transition: opacity 0.4s;
  transition: opacity 0.4s;
}
/* line 2926, ../sass/_template_specific.scss */
.newsletter_additional_wrapper form #send_newsletter_additional:hover {
  opacity: .8;
}

@media (max-width: 768px) {
  /* line 2934, ../sass/_template_specific.scss */
  .newsletter_additional_wrapper {
    width: 100%;
  }
  /* line 2938, ../sass/_template_specific.scss */
  .newsletter_additional_wrapper form .input_wrapper {
    width: 100%;
  }
  /* line 2948, ../sass/_template_specific.scss */
  .newsletter_additional_wrapper form .popup_policy_newsletter {
    margin-top: 0;
  }
  /* line 2952, ../sass/_template_specific.scss */
  .newsletter_additional_wrapper form #send_newsletter_additional {
    width: 100%;
  }
}
/* line 2974, ../sass/_template_specific.scss */
.full-popup .fancybox-outer {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
/* line 2980, ../sass/_template_specific.scss */
.full-popup div#wrapper_booking_fancybox {
  display: table;
  width: 100%;
  position: absolute;
  bottom: 0;
  top: 0;
  margin: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 2992, ../sass/_template_specific.scss */
.full-popup .fancybox-close {
  position: absolute;
  top: 30px;
  right: 30px;
  background: none;
  text-decoration: none;
  color: white;
}
/* line 3000, ../sass/_template_specific.scss */
.full-popup .fancybox-close:before {
  content: "x";
  color: white;
  font-size: 85px;
  line-height: 36px;
  font-family: 'Montserrat', sans-serif;
}

/* line 3009, ../sass/_template_specific.scss */
.full-popup .fancybox-outer {
  background: #3A9BC8;
}
/* line 3011, ../sass/_template_specific.scss */
.full-popup .fancybox-outer .fancybox-inner {
  color: white;
  padding: 200px 270px;
  width: 100% !important;
}

/* line 3018, ../sass/_template_specific.scss */
#fancybox-loading {
  display: none !important;
}

/* line 2, ../sass/_contact.scss */
.blocks_contact_wrapper {
  background: #333;
  padding-top: 72px;
  padding-bottom: 95px;
}
/* line 7, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element {
  width: calc(100% / 4 - 10px);
  float: left;
  color: white;
  font-weight: 100;
  text-align: center;
  margin-right: 10px;
  padding-bottom: 10px;
  background: rgba(255, 255, 255, 0.05);
}
/* line 17, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element:last-child {
  margin-right: 0;
}
/* line 21, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element:first-of-type {
  width: calc(100% - 10px);
  padding-bottom: 20px;
}
/* line 24, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element:first-of-type .title {
  color: #3A9BC8;
  font-size: 42px;
  padding: 20px 45px;
  background-size: 34px;
}
/* line 31, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element:nth-child(2), .blocks_contact_wrapper .block_element:nth-child(3) {
  width: calc((100% - (100% / 4 - 10px)) / 2 - 15px);
  margin-bottom: 10px;
}
/* line 37, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element .title {
  color: #446ca9;
  font-size: 32px;
  padding: 10px 45px;
  width: auto;
  margin: 0 auto;
  background: url(/img/datar/ico-localizacion-nosotros.png) no-repeat left;
  display: inline-block;
  background-size: 25px;
}
/* line 47, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element .title .img_loc {
  display: inline-block;
}
/* line 51, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element .title h3 {
  display: inline-block;
}
/* line 56, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element .content {
  font-size: 17px;
  line-height: 29px;
  width: 100%;
  margin: 0 auto;
}
/* line 62, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element .content strong {
  font-weight: 300;
}
/* line 67, ../sass/_contact.scss */
.blocks_contact_wrapper .block_element .separator {
  width: 220px;
}
/* line 71, ../sass/_contact.scss */
.blocks_contact_wrapper .iframe_maps {
  display: table;
  width: calc(100% - 10px);
  padding-top: 10px;
}
/* line 76, ../sass/_contact.scss */
.blocks_contact_wrapper .iframe_maps iframe {
  width: 100%;
  height: 325px;
}

/* line 83, ../sass/_contact.scss */
.slider_contact_wrapper {
  background: #2b2b2b;
  padding: 100px 0;
  position: relative;
}
/* line 88, ../sass/_contact.scss */
.slider_contact_wrapper .img_slider {
  width: 100%;
  height: 400px;
}
/* line 93, ../sass/_contact.scss */
.slider_contact_wrapper .overlay {
  position: absolute;
  top: 50%;
  left: 20px;
  -moz-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  -webkit-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  width: 350px;
  height: 350px;
}
/* line 106, ../sass/_contact.scss */
.slider_contact_wrapper .overlay .text_block {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 14px;
  font-weight: 100;
  width: 300px;
}
/* line 119, ../sass/_contact.scss */
.slider_contact_wrapper .overlay .text_block .title {
  font-size: 35px;
  padding: 0 20px 0px;
  margin-bottom: 10px;
  background: url(/img/datar/ico-localizacion-nosotros.png) no-repeat left;
  width: auto;
  display: inline-block;
  background-size: contain;
  padding-left: 35px;
  margin-left: -15px;
}
/* line 135, ../sass/_contact.scss */
.slider_contact_wrapper .overlay .text_block .content {
  width: 250px;
  margin: 0 auto;
}
/* line 142, ../sass/_contact.scss */
.slider_contact_wrapper .flex-control-nav {
  position: absolute;
  bottom: 115px;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2;
}
/* line 150, ../sass/_contact.scss */
.slider_contact_wrapper .flex-control-nav li {
  display: inline-block;
}
/* line 153, ../sass/_contact.scss */
.slider_contact_wrapper .flex-control-nav li a.flex-active {
  background: url("/img/datar/sprit1.png") no-repeat top left;
}
/* line 157, ../sass/_contact.scss */
.slider_contact_wrapper .flex-control-nav li a {
  background: url("/img/datar/sprit2.png") no-repeat top left;
  color: transparent;
  width: 10px;
  margin-right: 7px;
  display: inline-block;
}
/* line 165, ../sass/_contact.scss */
.slider_contact_wrapper .flex-control-nav li:last-child a {
  margin-right: 0;
}

/* line 172, ../sass/_contact.scss */
.form_contact_wrapper {
  background: #3A9BC8;
  padding: 100px 0;
}
/* line 177, ../sass/_contact.scss */
.form_contact_wrapper form input {
  padding-left: 39px;
  margin-bottom: 10px;
  color: white;
  height: 39px;
  border-radius: 0px;
  border: 0;
}
/* line 185, ../sass/_contact.scss */
.form_contact_wrapper form input::-webkit-input-placeholder {
  color: white;
}
/* line 189, ../sass/_contact.scss */
.form_contact_wrapper form input:-moz-placeholder {
  color: white;
}
/* line 193, ../sass/_contact.scss */
.form_contact_wrapper form input:-ms-input-placeholder {
  color: white;
}
/* line 198, ../sass/_contact.scss */
.form_contact_wrapper form label.error {
  color: white;
  font-size: 14px;
  margin-bottom: 10px;
}
/* line 204, ../sass/_contact.scss */
.form_contact_wrapper form .name {
  background: rgba(255, 255, 255, 0.2) url(/img/datar/formname.png) no-repeat !important;
}
/* line 208, ../sass/_contact.scss */
.form_contact_wrapper form .mail {
  background: rgba(255, 255, 255, 0.2) url(/img/datar/formmail.png) no-repeat;
}
/* line 212, ../sass/_contact.scss */
.form_contact_wrapper form .business {
  background: rgba(255, 255, 255, 0.2) url(/img/datar/icos/business.png) no-repeat;
}
/* line 216, ../sass/_contact.scss */
.form_contact_wrapper form .phone {
  background: rgba(255, 255, 255, 0.2) url(/img/datar/formphone.png) no-repeat;
}
/* line 220, ../sass/_contact.scss */
.form_contact_wrapper form .hotel {
  background: rgba(255, 255, 255, 0.2) url(/img/datar/ico-hotel.png) no-repeat;
}
/* line 224, ../sass/_contact.scss */
.form_contact_wrapper form .province {
  background: rgba(255, 255, 255, 0.2) url(/img/datar/ico-provincia.png) no-repeat;
}
/* line 228, ../sass/_contact.scss */
.form_contact_wrapper form .comment_wrapper {
  position: relative;
}
/* line 230, ../sass/_contact.scss */
.form_contact_wrapper form .comment_wrapper:before {
  content: "\f075";
  position: absolute;
  top: 0;
  left: 0;
  display: inline-block;
  background-color: white;
  width: 30px;
  height: 40px;
  padding: 10px 7px;
  color: rgba(128, 128, 128, 0.6);
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}
/* line 246, ../sass/_contact.scss */
.form_contact_wrapper form .comment_wrapper textarea {
  padding-left: 39px;
}
/* line 251, ../sass/_contact.scss */
.form_contact_wrapper form textarea {
  height: 88px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0;
  border: 0;
  color: white;
}
/* line 258, ../sass/_contact.scss */
.form_contact_wrapper form textarea::-webkit-input-placeholder {
  color: white;
}
/* line 262, ../sass/_contact.scss */
.form_contact_wrapper form textarea:-moz-placeholder {
  color: white;
}
/* line 266, ../sass/_contact.scss */
.form_contact_wrapper form textarea:-ms-input-placeholder {
  color: white;
}
/* line 270, ../sass/_contact.scss */
.form_contact_wrapper form #contact-button-wrapper {
  display: inline-block;
  vertical-align: top;
  margin-left: 10px;
  width: calc(100% - 324px);
  cursor: pointer;
}
/* line 277, ../sass/_contact.scss */
.form_contact_wrapper form #contact-button {
  color: white;
  background-color: #555658 !important;
  display: inline-block;
  text-align: center;
  width: 100%;
  border-radius: 5px;
  margin-right: 16px;
  text-transform: uppercase !important;
  font-size: 16px;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 10px;
  padding-right: 10px;
  cursor: pointer;
  padding: 28px 10px;
}
/* line 295, ../sass/_contact.scss */
.form_contact_wrapper form #contact-button:hover {
  opacity: 0.8;
}
/* line 299, ../sass/_contact.scss */
.form_contact_wrapper form .g-recaptcha {
  display: inline-block;
}
/* line 303, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit {
  text-align: center;
}
/* line 305, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit a {
  text-decoration: none;
}
/* line 309, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit .lopd_button {
  padding-left: 13px;
  display: inline-block;
}
/* line 313, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit .lopd_button a {
  display: inline-block;
  vertical-align: middle;
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
  -webkit-transition: color 0.4s;
  -moz-transition: color 0.4s;
  -ms-transition: color 0.4s;
  -o-transition: color 0.4s;
  transition: color 0.4s;
}
/* line 321, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit .lopd_button input[type="checkbox"] {
  display: inline-block;
  vertical-align: middle;
  -webkit-appearance: none;
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 5px;
  padding-left: 0;
  width: 15px;
  height: 15px;
  margin: 0 5px 0 0;
  -webkit-transition: background-color 0.4s;
  -moz-transition: background-color 0.4s;
  -ms-transition: background-color 0.4s;
  -o-transition: background-color 0.4s;
  transition: background-color 0.4s;
}
/* line 333, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit .lopd_button input[type="checkbox"]:checked {
  background-color: white;
}
/* line 335, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit .lopd_button input[type="checkbox"]:checked + a, .form_contact_wrapper form .row.submit .lopd_button input[type="checkbox"]:checked + label {
  color: white;
}
/* line 341, ../sass/_contact.scss */
.form_contact_wrapper form .row.submit .lopd_button label[for="privacy"].error {
  position: absolute;
  top: 15px;
  margin-left: 4px;
}

/* line 352, ../sass/_contact.scss */
.fancybox-form-new .form_contact_wrapper {
  padding: 30px;
  box-sizing: border-box;
}
/* line 356, ../sass/_contact.scss */
.fancybox-form-new .form_contact_wrapper #contact_file_download, .fancybox-form-new .form_contact_wrapper .contact_file_download {
  width: auto;
}
/* line 358, ../sass/_contact.scss */
.fancybox-form-new .form_contact_wrapper #contact_file_download input[type=text], .fancybox-form-new .form_contact_wrapper .contact_file_download input[type=text] {
  width: 100%;
  margin-right: 0;
}
/* line 366, ../sass/_contact.scss */
.fancybox-form-new .form_contact_wrapper form .box input {
  display: block;
  width: 100%;
}
/* line 372, ../sass/_contact.scss */
.fancybox-form-new .form_contact_wrapper form .lopd_button {
  float: none;
}

/* line 379, ../sass/_contact.scss */
.file_download_form {
  background-color: #555658;
}
/* line 383, ../sass/_contact.scss */
.file_download_form #contact-button-wrapper {
  background-color: #3A9BC8;
  width: calc(100% - 10px) !important;
  margin-left: 0 !important;
  padding: 10px;
  color: white;
  text-align: center;
  text-transform: uppercase;
}
/* line 393, ../sass/_contact.scss */
.file_download_form #contact_file_download, .file_download_form .contact_file_download {
  width: 500px;
  margin: auto;
}
/* line 396, ../sass/_contact.scss */
.file_download_form #contact_file_download input[type=text], .file_download_form .contact_file_download input[type=text] {
  width: calc(50% - 10px);
  margin-right: 10px;
}
/* line 402, ../sass/_contact.scss */
.file_download_form .lopd_button {
  float: left;
  position: relative;
  margin-top: 8px !important;
  display: flex;
  align-items: center;
}
/* line 409, ../sass/_contact.scss */
.file_download_form .lopd_button input[type="checkbox"] {
  height: auto;
  margin: 0 8px 0 0;
}
/* line 414, ../sass/_contact.scss */
.file_download_form .lopd_button label[for="privacy"].error {
  position: absolute;
  top: 25px;
  margin-left: 4px;
}
/* line 420, ../sass/_contact.scss */
.file_download_form .lopd_button a {
  color: #979797;
  font-size: 12px;
}
/* line 424, ../sass/_contact.scss */
.file_download_form .lopd_button a:hover {
  opacity: 0.8;
}
/* line 430, ../sass/_contact.scss */
.file_download_form .name {
  width: 49%;
  float: left;
}
/* line 435, ../sass/_contact.scss */
.file_download_form .name#surname {
  float: right;
  background: rgba(255, 255, 255, 0.2);
}
/* line 441, ../sass/_contact.scss */
.file_download_form label.error {
  display: none !important;
}
/* line 445, ../sass/_contact.scss */
.file_download_form input.error {
  border: 1px solid red;
}
/* line 449, ../sass/_contact.scss */
.file_download_form input[type="checkbox"].error {
  position: relative;
  z-index: 1;
}
/* line 453, ../sass/_contact.scss */
.file_download_form input[type="checkbox"].error:before {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  bottom: -1px;
  right: -1px;
  border: 1px solid red;
  z-index: 0;
}

@media screen and (max-width: 500px) {
  /* line 468, ../sass/_contact.scss */
  .file_download_form {
    padding: 15px;
  }
  /* line 471, ../sass/_contact.scss */
  .file_download_form #contact_file_download, .file_download_form .contact_file_download {
    width: 100%;
  }
  /* line 473, ../sass/_contact.scss */
  .file_download_form #contact_file_download input[type=text], .file_download_form .contact_file_download input[type=text] {
    width: 100%;
    margin-right: 0;
  }
  /* line 479, ../sass/_contact.scss */
  .file_download_form #contact-button-wrapper {
    display: inline-block;
    vertical-align: top;
  }

  /* line 486, ../sass/_contact.scss */
  body .fancybox-form-new {
    width: calc(100% - 60px) !important;
  }
  /* line 489, ../sass/_contact.scss */
  body .fancybox-form-new .fancybox-inner {
    width: 100% !important;
  }
  /* line 493, ../sass/_contact.scss */
  body .fancybox-form-new form .lopd_button {
    margin-bottom: 10px;
  }
}
@media screen and (max-width: 700px) {
  /* line 501, ../sass/_contact.scss */
  .form_contact_wrapper.file_download_form .lopd_button:last-of-type {
    right: auto !important;
  }
}
/* line 1, ../sass/_loading.scss */
.loading_site, .white_overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  position: fixed;
  background-position: center;
  background-size: cover;
  background-color: #333333;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
  overflow: hidden !important;
}
/* line 10, ../sass/_loading.scss */
.loading_site:before, .white_overlay:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(51, 51, 51, 0.8);
}
/* line 15, ../sass/_loading.scss */
.loading_site .loading_content, .white_overlay .loading_content {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  text-align: center;
}
/* line 19, ../sass/_loading.scss */
.loading_site .loading_content .popup_loading_logo, .white_overlay .loading_content .popup_loading_logo {
  margin-bottom: 35px;
}
/* line 22, ../sass/_loading.scss */
.loading_site .loading_content .popup_loading_logo img, .white_overlay .loading_content .popup_loading_logo img {
  vertical-align: middle;
}
/* line 27, ../sass/_loading.scss */
.loading_site .loading_content h2, .white_overlay .loading_content h2 {
  font-family: "Oswald", sans-serif;
  font-size: 20px;
  letter-spacing: 3px;
  font-weight: lighter;
  color: white;
}

/* line 36, ../sass/_loading.scss */
.white_overlay {
  background: #F9F9F9 !important;
}
/* line 38, ../sass/_loading.scss */
.white_overlay:before {
  right: 20%;
  background: #3A9BC8;
}

/* line 1, ../../../../sass/plugins/spiners/_dots_loader.scss */
.dots_loader {
  padding: 60px 0 70px;
}
/* line 3, ../../../../sass/plugins/spiners/_dots_loader.scss */
.dots_loader .dot {
  width: 24px;
  height: 24px;
  margin: 0 3px;
  background: #000;
  border-radius: 100%;
  display: inline-block;
  animation: slide 1s infinite;
}
/* line 11, ../../../../sass/plugins/spiners/_dots_loader.scss */
.dots_loader .dot:nth-child(1) {
  animation-delay: 0.1s;
}
/* line 14, ../../../../sass/plugins/spiners/_dots_loader.scss */
.dots_loader .dot:nth-child(2) {
  animation-delay: 0.2s;
}
/* line 17, ../../../../sass/plugins/spiners/_dots_loader.scss */
.dots_loader .dot:nth-child(3) {
  animation-delay: 0.3s;
}
/* line 20, ../../../../sass/plugins/spiners/_dots_loader.scss */
.dots_loader .dot:nth-child(4) {
  animation-delay: 0.4s;
}
/* line 23, ../../../../sass/plugins/spiners/_dots_loader.scss */
.dots_loader .dot:nth-child(5) {
  animation-delay: 0.5s;
}

@-moz-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-webkit-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-o-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
/* line 47, ../sass/_loading.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: bold;
}

/* line 51, ../sass/_loading.scss */
.tp-loader {
  background: none;
}

/* line 2, ../sass/languages.scss */
html[lang="de"] .fancybox-lang #popup_login .others_tools .login_button {
  padding: 10px 18px;
}

@media (max-width: 1139px) {
  /* line 2, ../sass/_responsive.scss */
  header .section_element .link, .hide_menu .section_element .link {
    padding: 5px;
  }

  /* line 6, ../sass/_responsive.scss */
  body {
    min-width: inherit;
  }

  /* line 10, ../sass/_responsive.scss */
  .tp-banner-container {
    min-width: auto !important;
  }

  /* line 15, ../sass/_responsive.scss */
  #slider_container .revolution_text {
    width: 80%;
    margin-left: auto;
    margin-right: auto;
  }
  /* line 21, ../sass/_responsive.scss */
  #slider_container .default {
    top: calc(100% - 36px) !important;
  }

  /* line 27, ../sass/_responsive.scss */
  header #mainHeaderContainer, .hide_menu #mainHeaderContainer {
    width: 95%;
  }
  /* line 31, ../sass/_responsive.scss */
  header #main_header, .hide_menu #main_header {
    width: auto;
    float: right;
  }
  /* line 36, ../sass/_responsive.scss */
  header #main_header #top_header .social, .hide_menu #main_header #top_header .social {
    height: 100%;
  }

  /* line 44, ../sass/_responsive.scss */
  .bannerx5_wrapper {
    width: 95%;
  }
  /* line 47, ../sass/_responsive.scss */
  .bannerx5_wrapper .col-2 {
    width: calc(100% / 2 - 5px);
    height: auto;
  }
  /* line 52, ../sass/_responsive.scss */
  .bannerx5_wrapper .col-3 {
    width: calc(100% / 3 - 7px);
    height: auto;
    margin-bottom: 10px;
  }
  /* line 60, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_text .title {
    font-size: 16px;
  }
  /* line 64, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_text .description {
    font-size: 13px;
  }
  /* line 70, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .links .know_more {
    font-size: 13px;
    background-position-y: 4px;
  }
  /* line 75, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .links .link_video {
    font-size: 13px;
    background-size: contain;
    background-position-y: 0px;
  }
  /* line 84, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_gallery .element_text .title {
    font-size: 16px;
  }
  /* line 88, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_gallery .element_text .description {
    font-size: 13px;
  }
  /* line 94, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_gallery .links .highlight {
    font-size: 13px;
  }
  /* line 98, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_gallery .links .know_more {
    font-size: 13px;
  }

  /* line 106, ../sass/_responsive.scss */
  .bannerx4_wrapper {
    height: auto;
    padding-bottom: 20px;
  }
  /* line 110, ../sass/_responsive.scss */
  .bannerx4_wrapper .container12 {
    width: 95%;
  }
  /* line 114, ../sass/_responsive.scss */
  .bannerx4_wrapper .bannerx4_element {
    width: 50%;
    float: left;
    margin-bottom: 30px;
  }

  /* line 121, ../sass/_responsive.scss */
  .slider_text_wrapper {
    height: auto;
  }
  /* line 124, ../sass/_responsive.scss */
  .slider_text_wrapper .slider_text_description {
    width: 100%;
  }

  /* line 129, ../sass/_responsive.scss */
  .banner_partner_wrapper {
    padding-bottom: 20px;
  }
  /* line 132, ../sass/_responsive.scss */
  .banner_partner_wrapper .container12 {
    width: 95%;
  }
  /* line 136, ../sass/_responsive.scss */
  .banner_partner_wrapper .partner_text {
    width: 100%;
  }
  /* line 141, ../sass/_responsive.scss */
  .banner_partner_wrapper .partner_badges ul li {
    width: 50%;
    float: left;
    margin-bottom: 30px;
    height: 64px;
  }

  /* line 153, ../sass/_responsive.scss */
  .wordpress_block .container12 {
    width: 95%;
  }
  /* line 158, ../sass/_responsive.scss */
  .wordpress_block .wordpress_sections .wordpress_element {
    width: calc(100% / 2 - 40px);
    margin-bottom: 33px;
  }
  /* line 162, ../sass/_responsive.scss */
  .wordpress_block .wordpress_sections .wordpress_element:last-child {
    margin-bottom: 0;
  }

  /* line 170, ../sass/_responsive.scss */
  footer .container12 {
    width: 95%;
  }

  /* line 176, ../sass/_responsive.scss */
  .submenu_flotante_wrapper .container12 {
    width: 900px;
  }

  /* line 182, ../sass/_responsive.scss */
  .main_content_wrapper .container12 {
    width: 95%;
  }
  /* line 186, ../sass/_responsive.scss */
  .main_content_wrapper .main_content_description {
    width: 95%;
  }

  /* line 192, ../sass/_responsive.scss */
  .black_sections_wrapper .container12 {
    width: 95%;
  }

  /* line 198, ../sass/_responsive.scss */
  .white_block_wrapper .container12 {
    width: 95%;
  }

  /* line 204, ../sass/_responsive.scss */
  .blue_sections_wrapper .container12 {
    width: 95%;
  }

  /* line 210, ../sass/_responsive.scss */
  .suscripcion-google-maps-wrapper .container12 {
    width: 95%;
  }

  /* line 216, ../sass/_responsive.scss */
  .form_contact_wrapper .container12 {
    width: 95%;
  }

  /* line 222, ../sass/_responsive.scss */
  .blocks_contact_wrapper .container12 {
    width: 95%;
  }

  /* line 228, ../sass/_responsive.scss */
  .slider_contact_wrapper .container12 {
    width: 95%;
  }

  /* line 234, ../sass/_responsive.scss */
  .slider_contact_wrapper .img_slider {
    height: 405px;
    overflow: hidden;
  }
  /* line 238, ../sass/_responsive.scss */
  .slider_contact_wrapper .img_slider img {
    height: 100%;
    width: auto;
  }

  /* line 255, ../sass/_responsive.scss */
  .news_sections_wrapper .container12 {
    width: 95%;
  }
  /* line 261, ../sass/_responsive.scss */
  .news_sections_wrapper .news_element .new_text .new_description {
    width: 95%;
  }
  /* line 266, ../sass/_responsive.scss */
  .news_sections_wrapper .news_element .image_new {
    float: none;
  }
  /* line 270, ../sass/_responsive.scss */
  .news_sections_wrapper .news_element .new_text.cover {
    float: none;
    margin-left: 0;
    text-align: center;
  }
}
@media (max-width: 1015px) {
  /* line 281, ../sass/_responsive.scss */
  .bannerx5_wrapper .col-2 {
    width: 100%;
    height: 380px;
  }
  /* line 284, ../sass/_responsive.scss */
  .bannerx5_wrapper .col-2.banner-right {
    width: calc(50% - 5px);
    height: auto;
    margin-right: 10px;
  }
  /* line 291, ../sass/_responsive.scss */
  .bannerx5_wrapper .col-3 {
    width: calc(50% - 5px);
    height: auto;
  }
  /* line 294, ../sass/_responsive.scss */
  .bannerx5_wrapper .col-3.banner-left {
    margin-right: 0;
  }
  /* line 301, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_text .title {
    font-size: 19px;
  }
  /* line 305, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_text .description {
    font-size: 16px;
  }
  /* line 309, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .element_img, .bannerx5_wrapper .bannerx5_element .bannerx5_img {
    height: 300px !important;
  }
  /* line 314, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .links .know_more {
    font-size: 16px;
    background-position-y: 6px;
  }
  /* line 319, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .links .link_video {
    font-size: 16px;
    background-size: contain;
    background-position-y: 0px;
  }
  /* line 328, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_gallery .element_text .title {
    font-size: 19px;
  }
  /* line 332, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_gallery .element_text .description {
    font-size: 16px;
  }
  /* line 338, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_gallery .links .highlight {
    font-size: 16px;
  }
  /* line 342, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_gallery .links .know_more {
    font-size: 16px;
  }

  /* line 351, ../sass/_responsive.scss */
  .blue_sections_wrapper .blue_images, .blue_sections_wrapper .blue_images_hidden {
    width: 95%;
  }
  /* line 354, ../sass/_responsive.scss */
  .blue_sections_wrapper .blue_images .images, .blue_sections_wrapper .blue_images_hidden .images {
    width: auto;
  }

  /* line 362, ../sass/_responsive.scss */
  .blocks_contact_wrapper .block_element .title {
    margin: 20px auto;
    font-size: 38px;
    padding: 0 45px;
    background-size: contain;
  }
  /* line 369, ../sass/_responsive.scss */
  .blocks_contact_wrapper .block_element .separator {
    width: 95%;
  }
}
@media (max-width: 1000px) {
  /* line 377, ../sass/_responsive.scss */
  .blocks_contact_wrapper .block_element {
    width: calc(100% / 2 - 10px);
    margin-bottom: 10px;
  }
  /* line 380, ../sass/_responsive.scss */
  .blocks_contact_wrapper .block_element:nth-child(2), .blocks_contact_wrapper .block_element:nth-child(3) {
    width: calc(100% / 2 - 10px);
  }
  /* line 383, ../sass/_responsive.scss */
  .blocks_contact_wrapper .block_element:nth-child(2) .title, .blocks_contact_wrapper .block_element:nth-child(3) .title {
    font-size: 32px;
    padding: 1px 45px;
    background-size: 25px;
  }
  /* line 389, ../sass/_responsive.scss */
  .blocks_contact_wrapper .block_element:nth-child(4), .blocks_contact_wrapper .block_element:nth-child(5), .blocks_contact_wrapper .block_element:nth-child(6) {
    width: calc(100% / 3 - 10px);
  }
  /* line 393, ../sass/_responsive.scss */
  .blocks_contact_wrapper .block_element:nth-child(4) .title, .blocks_contact_wrapper .block_element:nth-child(5) .title, .blocks_contact_wrapper .block_element:nth-child(6) .title {
    font-size: 32px;
    padding: 0 45px;
    background-size: 25px;
  }

  /* line 401, ../sass/_responsive.scss */
  .full-popup .fancybox-outer .fancybox-inner {
    padding: 60px 100px;
  }
}
@media (max-width: 990px) {
  /* line 408, ../sass/_responsive.scss */
  #slider_container .tp-caption {
    top: 0;
  }

  /* line 412, ../sass/_responsive.scss */
  .inner_slider.inner_slider_big {
    margin-top: 60px;
  }

  /* line 416, ../sass/_responsive.scss */
  header {
    background: #555658 !important;
    height: 60px;
    top: 0;
  }
  /* line 421, ../sass/_responsive.scss */
  header #mainHeaderContainer {
    width: 100%;
    position: relative;
    padding: 2.5%;
    height: 36px;
  }
  /* line 427, ../sass/_responsive.scss */
  header #mainHeaderContainer #logo {
    margin-top: 3px;
    width: 100px;
    position: absolute;
    top: 50%;
    -moz-transform: translate(0%, -50%);
    -o-transform: translate(0%, -50%);
    -webkit-transform: translate(0%, -50%);
    transform: translate(0%, -50%);
  }
  /* line 439, ../sass/_responsive.scss */
  header #mainHeaderContainer #main_sections {
    display: none;
  }
  /* line 443, ../sass/_responsive.scss */
  header #top_header {
    display: inline-block;
    position: absolute;
    right: 60px;
    margin: 0;
    top: 50%;
    -moz-transform: translate(0%, -50%);
    -o-transform: translate(0%, -50%);
    -webkit-transform: translate(0%, -50%);
    transform: translate(0%, -50%);
  }
  /* line 458, ../sass/_responsive.scss */
  header #top_header .login {
    padding: 0;
    margin-left: 0;
    background-color: transparent;
  }
  /* line 463, ../sass/_responsive.scss */
  header #top_header .login a {
    background: url("/img/datar/user-icon.png") no-repeat center center;
    color: transparent;
    height: 22px;
    width: 22px;
    background-size: contain;
    display: inline-block;
  }
  /* line 471, ../sass/_responsive.scss */
  header #top_header .login a:hover {
    color: transparent;
  }
  /* line 478, ../sass/_responsive.scss */
  header .responsive_sections_button {
    background: transparent url("/img/datar/menu.png") no-repeat;
    width: 22px;
    height: 22px;
    background-size: contain;
    display: inline-block;
    position: absolute;
    right: 2.5%;
    top: 30px;
    top: 50%;
    -moz-transform: translate(0%, -50%);
    -o-transform: translate(0%, -50%);
    -webkit-transform: translate(0%, -50%);
    transform: translate(0%, -50%);
  }
  /* line 494, ../sass/_responsive.scss */
  header .responsive_sections_wrapper {
    display: block;
    opacity: 0;
    position: fixed;
    right: 0;
    left: 0;
    bottom: 100%;
    text-align: right;
    background: #555658;
    padding: 0 10%;
    overflow: auto;
  }
  /* line 509, ../sass/_responsive.scss */
  header .responsive_sections_wrapper .close_img {
    background: url("/img/datar/cerrar-menu.png");
    background-size: contain;
    height: 22px;
    width: 22px;
    position: absolute;
    top: 20px;
    right: 2.5%;
  }
  /* line 523, ../sass/_responsive.scss */
  header .responsive_sections_wrapper #responsive_sections .section_element {
    display: block;
    font-size: 20px;
    text-align: left;
  }
  /* line 528, ../sass/_responsive.scss */
  header .responsive_sections_wrapper #responsive_sections .section_element .link {
    padding-right: 0;
    padding-left: 0;
    width: 100%;
    font-weight: 100;
    padding: 12px 0;
  }
  /* line 536, ../sass/_responsive.scss */
  header .responsive_sections_wrapper #responsive_sections .section_element ul {
    display: none;
    padding-bottom: 15px;
  }
  /* line 539, ../sass/_responsive.scss */
  header .responsive_sections_wrapper #responsive_sections .section_element ul.active {
    display: block;
  }
  /* line 544, ../sass/_responsive.scss */
  header .responsive_sections_wrapper #responsive_sections .section_element ul .subsection_element .hide_img {
    display: none;
  }
  /* line 548, ../sass/_responsive.scss */
  header .responsive_sections_wrapper #responsive_sections .section_element ul .subsection_element a {
    color: rgba(255, 255, 255, 0.5);
    font-size: 16px;
    text-decoration: none;
    padding-left: 8px;
  }
  /* line 559, ../sass/_responsive.scss */
  header .responsive_sections_wrapper #responsive_sections hr {
    border-top: 2px solid #eee;
    margin: 0;
  }
  /* line 564, ../sass/_responsive.scss */
  header .responsive_sections_wrapper #responsive_sections hr.line_section {
    margin: 0;
    border-top: 1px solid #eee;
  }
  /* line 570, ../sass/_responsive.scss */
  header .responsive_sections_wrapper #responsive_sections .top_sections a {
    color: white;
  }
  /* line 578, ../sass/_responsive.scss */
  header .hiden_responsive_section ul {
    display: block;
  }

  /* line 584, ../sass/_responsive.scss */
  header.home {
    box-shadow: 0px -2px 10px black;
    position: fixed !important;
  }

  /* line 589, ../sass/_responsive.scss */
  .hide_menu {
    display: none !important;
  }

  /* line 593, ../sass/_responsive.scss */
  .fancybox-subsection {
    width: 400px !important;
  }
  /* line 596, ../sass/_responsive.scss */
  .fancybox-subsection .fancybox-outer {
    height: 100% !important;
    padding: 1px !important;
    border-radius: 0 !important;
  }
  /* line 602, ../sass/_responsive.scss */
  .fancybox-subsection .fancybox-inner {
    overflow: visible !important;
  }
  /* line 606, ../sass/_responsive.scss */
  .fancybox-subsection .fancybox-close {
    background: url("/img/datar/cerrar-idioma.png") !important;
    top: 0 !important;
    right: 0 !important;
    width: 42px !important;
    height: 42px !important;
  }
  /* line 614, ../sass/_responsive.scss */
  .fancybox-subsection .fancybox-close:hover {
    opacity: 0.8;
  }
  /* line 619, ../sass/_responsive.scss */
  .fancybox-subsection ul {
    background: rgba(255, 255, 255, 0.9);
    padding: 40px;
  }
  /* line 623, ../sass/_responsive.scss */
  .fancybox-subsection ul .subsection_element {
    display: inline-block;
    height: 40px;
    width: 100%;
    border-bottom: 1px solid #a9a8a9;
    text-align: center;
  }
  /* line 630, ../sass/_responsive.scss */
  .fancybox-subsection ul .subsection_element .hide_img {
    display: inline-block;
    width: 20px;
    vertical-align: middle;
    padding: 9px 0;
  }
  /* line 637, ../sass/_responsive.scss */
  .fancybox-subsection ul .subsection_element a {
    display: inline-block;
    text-decoration: none;
    font-size: 18px;
    color: #333;
    vertical-align: top;
    margin-top: 9px;
    padding-left: 5px;
    text-align: center;
    font-weight: 100;
  }
  /* line 648, ../sass/_responsive.scss */
  .fancybox-subsection ul .subsection_element a:hover {
    color: #666666;
  }
  /* line 654, ../sass/_responsive.scss */
  .fancybox-subsection ul .subsection_element:last-child {
    border-bottom: none;
  }

  /* line 661, ../sass/_responsive.scss */
  .submenu_flotante_wrapper .container12 {
    width: 95%;
  }

  /* line 666, ../sass/_responsive.scss */
  .icons_blue {
    display: none;
  }

  /* line 670, ../sass/_responsive.scss */
  .icons_blue_hidden {
    display: block;
    margin-top: 30px;
  }
  /* line 674, ../sass/_responsive.scss */
  .icons_blue_hidden .icon_element {
    display: block;
  }
  /* line 677, ../sass/_responsive.scss */
  .icons_blue_hidden .icon_element .icon_image {
    display: block;
    vertical-align: middle;
  }
  /* line 682, ../sass/_responsive.scss */
  .icons_blue_hidden .icon_element .icon_text {
    display: inline-block;
    vertical-align: middle;
    margin-top: 10px;
  }

  /* line 690, ../sass/_responsive.scss */
  .newsletter_wrapper {
    padding-bottom: 10px;
  }
  /* line 693, ../sass/_responsive.scss */
  .newsletter_wrapper .newsletter_container .newsletter_title {
    display: block;
    width: 100%;
    text-align: center;
  }
  /* line 698, ../sass/_responsive.scss */
  .newsletter_wrapper .newsletter_container .newsletter_description {
    display: block;
  }
  /* line 701, ../sass/_responsive.scss */
  .newsletter_wrapper .newsletter_container .newsletter_form {
    margin-top: 30px;
    display: block;
    width: 100%;
    text-align: center;
  }
  /* line 706, ../sass/_responsive.scss */
  .newsletter_wrapper .newsletter_container .newsletter_form .check_newsletter {
    position: relative;
    display: block;
    bottom: 0;
    left: 0;
    right: 0 !important;
    text-align: center;
    margin-top: 10px;
  }
}
@media (max-width: 899px) {
  /* line 722, ../sass/_responsive.scss */
  .black_sections_wrapper .black_element {
    text-align: center;
  }
  /* line 725, ../sass/_responsive.scss */
  .black_sections_wrapper .black_element .element_text {
    width: 80%;
  }
  /* line 729, ../sass/_responsive.scss */
  .black_sections_wrapper .black_element .element_img {
    width: 100%;
    text-align: center;
    max-width: 70%;
  }
  /* line 734, ../sass/_responsive.scss */
  .black_sections_wrapper .black_element .element_img > img {
    width: 100%;
  }
  /* line 738, ../sass/_responsive.scss */
  .black_sections_wrapper .black_element .element_img .icon_maxi {
    left: 50%;
    top: 50%;
    width: 100%;
    -webkit-transform: translate(-50%, 50%);
    -moz-transform: translate(-50%, 50%);
    -ms-transform: translate(-50%, 50%);
    -o-transform: translate(-50%, 50%);
    transform: translate(-50%, 50%);
  }
  /* line 750, ../sass/_responsive.scss */
  .black_sections_wrapper .black_element .work {
    width: 100%;
  }
  /* line 756, ../sass/_responsive.scss */
  .black_sections_wrapper .gray .element_text {
    float: none;
    margin: 0 auto;
  }
  /* line 760, ../sass/_responsive.scss */
  .black_sections_wrapper .gray .element_text .description {
    padding-right: 0;
  }
  /* line 765, ../sass/_responsive.scss */
  .black_sections_wrapper .gray .element_img {
    float: none;
    margin: 0 auto;
    margin-top: 40px;
  }
  /* line 772, ../sass/_responsive.scss */
  .black_sections_wrapper .gray .element_text_additional {
    float: none;
    width: auto;
    margin: 40px auto 0;
  }
  /* line 777, ../sass/_responsive.scss */
  .black_sections_wrapper .gray .element_text_additional .element {
    margin-right: auto;
  }
  /* line 785, ../sass/_responsive.scss */
  .black_sections_wrapper .black .element_text {
    float: none;
    margin: 0 auto;
    text-align: center;
  }
  /* line 790, ../sass/_responsive.scss */
  .black_sections_wrapper .black .element_text .description {
    padding-left: 0;
  }
  /* line 795, ../sass/_responsive.scss */
  .black_sections_wrapper .black .element_img {
    float: none;
    margin: 0 auto;
    margin-top: 40px;
  }
  /* line 801, ../sass/_responsive.scss */
  .black_sections_wrapper .black .element_text_additional {
    float: none;
    width: auto;
    margin: 40px auto 0;
  }
  /* line 806, ../sass/_responsive.scss */
  .black_sections_wrapper .black .element_text_additional .element {
    margin-left: auto;
  }
  /* line 813, ../sass/_responsive.scss */
  .black_sections_wrapper .element_text_additional {
    float: none;
    width: auto;
  }

  /* line 820, ../sass/_responsive.scss */
  .banner_partner_wrapper .partner_badges li.col-md-3 {
    width: calc(100% / 2 - 40px);
  }

  /* line 826, ../sass/_responsive.scss */
  .mini_gallery_wrapper .mini_gallery_element {
    width: 100%;
  }
  /* line 829, ../sass/_responsive.scss */
  .mini_gallery_wrapper .mini_gallery_element:first-child {
    margin-bottom: 10px;
  }
  /* line 833, ../sass/_responsive.scss */
  .mini_gallery_wrapper .mini_gallery_element:last-child {
    float: none;
  }

  /* line 840, ../sass/_responsive.scss */
  .blue_sections_wrapper .blue_description {
    width: 95%;
  }

  /* line 846, ../sass/_responsive.scss */
  .blocks_contact_wrapper .block_element {
    width: 65%;
    margin: 0 auto;
    float: none;
    margin-bottom: 20px;
  }
  /* line 852, ../sass/_responsive.scss */
  .blocks_contact_wrapper .block_element:nth-child(2), .blocks_contact_wrapper .block_element:nth-child(3), .blocks_contact_wrapper .block_element:nth-child(4), .blocks_contact_wrapper .block_element:nth-child(5), .blocks_contact_wrapper .block_element:nth-child(6) {
    width: 65%;
  }
  /* line 860, ../sass/_responsive.scss */
  .blocks_contact_wrapper .block_element .title {
    font-size: 52px;
  }
  /* line 864, ../sass/_responsive.scss */
  .blocks_contact_wrapper .block_element:last-child {
    margin-right: auto;
  }
}
@media (max-width: 850px) {
  /* line 873, ../sass/_responsive.scss */
  .slider_contact_wrapper .overlay {
    left: 50%;
    -moz-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
  }
  /* line 881, ../sass/_responsive.scss */
  .slider_contact_wrapper .flex-control-nav {
    bottom: 98px;
  }

  /* line 889, ../sass/_responsive.scss */
  .black_sections_wrapper .black_element .work .title {
    width: 95%;
  }
  /* line 893, ../sass/_responsive.scss */
  .black_sections_wrapper .black_element .work .description {
    width: 90%;
    text-align: justify;
  }

  /* line 901, ../sass/_responsive.scss */
  .fancybox-lopd {
    width: 95% !important;
    left: 2.5% !important;
  }
}
@media (max-width: 800px) {
  /* line 909, ../sass/_responsive.scss */
  .news_sections_wrapper .news_element {
    width: 100%;
  }
  /* line 912, ../sass/_responsive.scss */
  .news_sections_wrapper .news_element .new_text .new_description {
    width: 75%;
  }

  /* line 920, ../sass/_responsive.scss */
  .black_sections_wrapper .black_element .container12 {
    display: block;
  }
}
@media (max-width: 700px) {
  /* line 929, ../sass/_responsive.scss */
  .bannerx5_wrapper .col-2 {
    width: 100%;
    height: auto;
  }
  /* line 932, ../sass/_responsive.scss */
  .bannerx5_wrapper .col-2.banner-right {
    width: 100%;
    margin-right: 0;
  }
  /* line 938, ../sass/_responsive.scss */
  .bannerx5_wrapper .col-3 {
    width: 100%;
    margin-right: 0;
  }

  /* line 943, ../sass/_responsive.scss */
  .white_block_wrapper .white_content table {
    display: none;
  }

  /* line 948, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text {
    display: block;
    margin-top: 80px;
  }
  /* line 952, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element {
    padding-bottom: 10px;
    margin: 20px auto;
    color: white;
    width: 450px;
  }
  /* line 958, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element .title {
    font-size: 40px;
    font-weight: 300;
    padding-top: 10px;
  }
  /* line 963, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element .title .type, .white_block_wrapper .white_content .table_text .table_element .title .price {
    display: inline-block;
  }
  /* line 968, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element .accordion {
    display: none;
  }
  /* line 972, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element .accordion .text {
    padding: 15px 0;
  }
  /* line 976, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element .accordion hr {
    width: 90%;
    margin: 0 auto;
  }
  /* line 982, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element .see_more {
    padding-top: 10px;
  }
  /* line 987, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.silver {
    background-color: #afaeb4;
  }
  /* line 990, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.silver .accordion {
    background-color: #7b7983;
  }
  /* line 993, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.silver .accordion hr {
    border-color: #afaeb4;
  }
  /* line 999, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.gold {
    background-color: #e59721;
  }
  /* line 1002, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.gold .accordion {
    background-color: #8f5d11;
  }
  /* line 1005, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.gold .accordion hr {
    border-color: #e59721;
  }
  /* line 1011, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.platinum {
    background-color: #c2c4a6;
  }
  /* line 1014, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.platinum .accordion {
    background-color: #989b69;
  }
  /* line 1017, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.platinum .accordion hr {
    border-color: #c2c4a6;
  }

  /* line 1026, ../sass/_responsive.scss */
  .wordpress_block .wordpress_sections .wordpress_element {
    width: calc(100% - 40px);
  }

  /* line 1033, ../sass/_responsive.scss */
  .newsletter_wrapper .newsletter_container {
    text-align: center;
  }
  /* line 1035, ../sass/_responsive.scss */
  .newsletter_wrapper .newsletter_container .newsletter_title {
    display: block;
    width: 100%;
    padding: 0 10px;
  }
  /* line 1040, ../sass/_responsive.scss */
  .newsletter_wrapper .newsletter_container .newsletter_description {
    display: block;
  }
  /* line 1043, ../sass/_responsive.scss */
  .newsletter_wrapper .newsletter_container .newsletter_form {
    display: block;
    padding: 0 10px;
    width: 100%;
  }
  /* line 1048, ../sass/_responsive.scss */
  .newsletter_wrapper .newsletter_container .newsletter_form .input_email {
    display: block;
    width: 100%;
    margin-bottom: 20px;
  }
  /* line 1053, ../sass/_responsive.scss */
  .newsletter_wrapper .newsletter_container .newsletter_form .button_newsletter {
    display: block;
    width: 100%;
  }
  /* line 1057, ../sass/_responsive.scss */
  .newsletter_wrapper .newsletter_container .newsletter_form .check_newsletter {
    text-align: left;
  }
  /* line 1059, ../sass/_responsive.scss */
  .newsletter_wrapper .newsletter_container .newsletter_form .check_newsletter:last-of-type {
    left: auto;
    right: calc((100% - 850px) / 2);
  }
  /* line 1062, ../sass/_responsive.scss */
  .newsletter_wrapper .newsletter_container .newsletter_form .check_newsletter:last-of-type .check_privacy {
    margin-top: 0;
    vertical-align: top;
  }
  /* line 1066, ../sass/_responsive.scss */
  .newsletter_wrapper .newsletter_container .newsletter_form .check_newsletter:last-of-type label {
    width: calc(100% - 25px);
    vertical-align: top;
  }

  /* line 1076, ../sass/_responsive.scss */
  .form_contact_wrapper .g-recaptcha {
    display: block !important;
    text-align: center;
  }
  /* line 1079, ../sass/_responsive.scss */
  .form_contact_wrapper .g-recaptcha > div {
    display: inline-block;
  }
  /* line 1083, ../sass/_responsive.scss */
  .form_contact_wrapper #contact-button-wrapper {
    display: block !important;
    margin-left: 0 !important;
    width: 99% !important;
  }
  /* line 1088, ../sass/_responsive.scss */
  .form_contact_wrapper .lopd_button {
    display: block !important;
    text-align: left;
  }
  /* line 1091, ../sass/_responsive.scss */
  .form_contact_wrapper .lopd_button:last-of-type {
    left: auto;
    right: calc((100% - 850px) / 2);
  }
  /* line 1094, ../sass/_responsive.scss */
  .form_contact_wrapper .lopd_button:last-of-type .check_privacy {
    margin-top: 0 !important;
    vertical-align: top !important;
  }
  /* line 1098, ../sass/_responsive.scss */
  .form_contact_wrapper .lopd_button:last-of-type a {
    width: calc(100% - 25px) !important;
    vertical-align: top !important;
  }
}
@media (max-width: 600px) {
  /* line 1108, ../sass/_responsive.scss */
  .bannerx5_wrapper .col-2, .bannerx5_wrapper .col-3 {
    height: auto;
  }

  /* line 1112, ../sass/_responsive.scss */
  .col-sm-6 {
    width: 100%;
  }

  /* line 1119, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_text .title {
    font-size: 16px;
  }
  /* line 1123, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_text .description {
    font-size: 13px;
  }
  /* line 1130, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_gallery .element_text .title {
    font-size: 16px;
  }
  /* line 1134, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_gallery .element_text .description {
    font-size: 13px;
  }
  /* line 1140, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_gallery .links .highlight {
    font-size: 13px;
  }
  /* line 1144, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .bannerx5_gallery .links .know_more {
    font-size: 13px;
    background-position-y: 4px;
  }
  /* line 1152, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .links .know_more {
    font-size: 13px;
    background-position-y: 4px;
  }
  /* line 1157, ../sass/_responsive.scss */
  .bannerx5_wrapper .bannerx5_element .links .link_video {
    font-size: 13px;
    background-size: contain;
    background-position-y: 0px;
  }

  /* line 1166, ../sass/_responsive.scss */
  #slider_container .tp-caption {
    top: 0;
    height: 100% !important;
    width: 100% !important;
    left: 0 !important;
  }
  /* line 1173, ../sass/_responsive.scss */
  #slider_container .revolution_description {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    width: 90%;
  }
  /* line 1178, ../sass/_responsive.scss */
  #slider_container .revolution_description .revolution_text .title {
    font-size: 50px;
  }
  /* line 1182, ../sass/_responsive.scss */
  #slider_container .revolution_description .revolution_text .description {
    font-size: 24px;
  }

  /* line 1190, ../sass/_responsive.scss */
  .slider_text_wrapper .slider_text_title {
    font-size: 35px;
  }
  /* line 1194, ../sass/_responsive.scss */
  .slider_text_wrapper .slider_text_description {
    font-size: 14px;
  }

  /* line 1200, ../sass/_responsive.scss */
  .main_content_wrapper .main_content_title {
    font-size: 50px;
  }

  /* line 1205, ../sass/_responsive.scss */
  .black_sections_wrapper .black_element .element_text .title {
    font-size: 40px;
  }

  /* line 1209, ../sass/_responsive.scss */
  .blue_sections_wrapper .blue_title {
    font-size: 40px;
  }

  /* line 1213, ../sass/_responsive.scss */
  .suscripcion-google-maps-wrapper .blue_title {
    font-size: 40px;
  }

  /* line 1216, ../sass/_responsive.scss */
  .form_contact_wrapper .blue_title {
    font-size: 40px;
  }

  /* line 1220, ../sass/_responsive.scss */
  .white_block_wrapper .white_title {
    font-size: 40px;
  }

  /* line 1225, ../sass/_responsive.scss */
  .blocks_contact_wrapper .block_element {
    width: 95%;
  }
  /* line 1227, ../sass/_responsive.scss */
  .blocks_contact_wrapper .block_element:nth-child(2), .blocks_contact_wrapper .block_element:nth-child(3), .blocks_contact_wrapper .block_element:nth-child(4), .blocks_contact_wrapper .block_element:nth-child(5), .blocks_contact_wrapper .block_element:nth-child(6) {
    width: 95%;
  }

  /* line 1242, ../sass/_responsive.scss */
  .hidden_download_popup iframe {
    max-width: 100%;
    height: 220px !important;
  }
  /* line 1247, ../sass/_responsive.scss */
  .hidden_download_popup video {
    max-width: 100%;
  }
}
@media (max-width: 540px) {
  /* line 1255, ../sass/_responsive.scss */
  .inner_slider.inner_slider_big {
    height: 75vh;
  }

  /* line 1258, ../sass/_responsive.scss */
  .submenu_flotante_wrapper {
    height: initial;
  }

  /* line 1264, ../sass/_responsive.scss */
  .bannerx3_wrapper .bannerx3 .title {
    font-size: 24px;
  }

  /* line 1271, ../sass/_responsive.scss */
  .fancybox-news .popup_inicial .form_popup {
    width: 100%;
    box-sizing: border-box;
    padding: 0 50px;
  }
  /* line 1277, ../sass/_responsive.scss */
  .fancybox-news .popup_inicial .form_popup .lopd_button a {
    font-size: 14px;
  }
  /* line 1285, ../sass/_responsive.scss */
  .fancybox-news .popup_img .popup_text {
    top: 65px;
  }
  /* line 1289, ../sass/_responsive.scss */
  .fancybox-news .popup_img img {
    width: 400px;
  }
  /* line 1293, ../sass/_responsive.scss */
  .fancybox-news .popup_img .popup_title {
    font-size: 18px;
  }
  /* line 1298, ../sass/_responsive.scss */
  .fancybox-news .popup_img .popup_description .line1 {
    font-size: 12px;
  }
  /* line 1302, ../sass/_responsive.scss */
  .fancybox-news .popup_img .popup_description .line2, .fancybox-news .popup_img .popup_description .line3 {
    font-size: 8px;
  }
  /* line 1306, ../sass/_responsive.scss */
  .fancybox-news .popup_img .popup_description .button_download {
    font-size: 8px;
    padding: 0 20px;
    margin-top: 0px;
  }
}
@media (max-width: 555px) {
  /* line 1318, ../sass/_responsive.scss */
  header .responsive_sections_wrapper {
    left: 0;
    text-align: center;
  }

  /* line 1324, ../sass/_responsive.scss */
  .bannerx4_wrapper .bannerx4_element {
    width: 100%;
  }

  /* line 1328, ../sass/_responsive.scss */
  .banner_partner_wrapper .partner_badges ul li {
    width: 100%;
  }

  /* line 1333, ../sass/_responsive.scss */
  .slider_text_wrapper .flex-direction-nav .flex-nav-prev {
    left: 15px;
  }
  /* line 1337, ../sass/_responsive.scss */
  .slider_text_wrapper .flex-direction-nav .flex-nav-next {
    right: 15px;
  }

  /* line 1344, ../sass/_responsive.scss */
  .banner_partner_wrapper .partner_text .partner_title {
    font-size: 35px;
  }

  /* line 1350, ../sass/_responsive.scss */
  .wordpress_block .wordpress_text .title {
    font-size: 35px;
  }

  /* line 1354, ../sass/_responsive.scss */
  .white_block_wrapper .white_content table {
    display: none;
  }

  /* line 1359, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text {
    display: block;
  }
  /* line 1362, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element {
    padding-bottom: 10px;
    margin: 20px 0;
    color: white;
    width: 100%;
  }
  /* line 1368, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element .title {
    font-size: 40px;
    font-weight: 300;
    padding-top: 10px;
  }
  /* line 1373, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element .title .type, .white_block_wrapper .white_content .table_text .table_element .title .price {
    display: inline-block;
  }
  /* line 1378, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element .accordion {
    display: none;
  }
  /* line 1382, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element .accordion .text {
    padding: 15px 0;
  }
  /* line 1386, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element .accordion hr {
    width: 90%;
    margin: 0 auto;
  }
  /* line 1392, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element .see_more {
    padding-top: 10px;
  }
  /* line 1397, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.plata {
    background-color: #afaeb4;
  }
  /* line 1400, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.plata .accordion {
    background-color: #7b7983;
  }
  /* line 1405, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.oro {
    background-color: #e59721;
  }
  /* line 1408, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.oro .accordion {
    background-color: #8f5d11;
  }
  /* line 1413, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.platino {
    background-color: #c2c4a6;
  }
  /* line 1416, ../sass/_responsive.scss */
  .white_block_wrapper .white_content .table_text .table_element.platino .accordion {
    background-color: #989b69;
  }

  /* line 1425, ../sass/_responsive.scss */
  .fancybox-news .popup_form input {
    width: 100%;
    border-right-color: #cecccf !important;
    border-radius: 0 !important;
  }
  /* line 1431, ../sass/_responsive.scss */
  .fancybox-news .popup_form button {
    width: 100%;
    margin-left: 0;
    margin-top: 5px;
  }

  /* line 1439, ../sass/_responsive.scss */
  .popup_title {
    font-size: 25px;
  }

  /* line 1443, ../sass/_responsive.scss */
  .popup_description {
    font-size: 12px;
  }
}
@media (max-width: 500px) {
  /* line 1449, ../sass/_responsive.scss */
  .submenu_flotante_wrapper .logo {
    width: 32px;
    margin-top: -7px;
    overflow: hidden;
  }
  /* line 1454, ../sass/_responsive.scss */
  .submenu_flotante_wrapper .logo img {
    max-width: none;
  }
}
@media (max-width: 465px) {
  /* line 1463, ../sass/_responsive.scss */
  .wordpress_block .newsletter_wrapper .newsletter_element {
    width: 95% !important;
  }
  /* line 1465, ../sass/_responsive.scss */
  .wordpress_block .newsletter_wrapper .newsletter_element .bordeInput {
    width: calc(100% - 60px);
  }

  /* line 1472, ../sass/_responsive.scss */
  .wordpress_block #newsletter_blog .text {
    width: calc(100% - 60px);
    overflow: hidden;
    height: 60px;
  }
}
@media (max-width: 445px) {
  /* line 1480, ../sass/_responsive.scss */
  header #logo {
    width: 20%;
    margin-top: 40px;
  }

  /* line 1485, ../sass/_responsive.scss */
  .submenu_flotante_wrapper .submenu_content {
    padding-top: 0;
  }

  /* line 1489, ../sass/_responsive.scss */
  .fancybox-lang {
    width: 90% !important;
  }

  /* line 1493, ../sass/_responsive.scss */
  .fancybox-contact {
    width: 90% !important;
  }

  /* line 1497, ../sass/_responsive.scss */
  .fancybox-subsection {
    width: 90% !important;
  }

  /* line 1501, ../sass/_responsive.scss */
  .black_sections_wrapper .gray .element_text_additional .element {
    width: auto;
  }

  /* line 1505, ../sass/_responsive.scss */
  .black_sections_wrapper .black .element_text_additional .element {
    width: auto;
  }

  /* line 1509, ../sass/_responsive.scss */
  .black_sections_wrapper .black_element .element .title {
    font-size: 45px;
  }

  /* line 1513, ../sass/_responsive.scss */
  .slider_contact_wrapper .overlay {
    width: 90%;
  }
  /* line 1516, ../sass/_responsive.scss */
  .slider_contact_wrapper .overlay .separator {
    width: 95%;
  }
}
@media (max-width: 400px) {
  /* line 1523, ../sass/_responsive.scss */
  footer #social a {
    padding: 0 5px;
  }

  /* line 1527, ../sass/_responsive.scss */
  footer .lang_footer a {
    padding: 0 5px;
  }

  /* line 1531, ../sass/_responsive.scss */
  .submenu_flotante_wrapper .submenu_content a {
    padding: 0 5px;
    font-size: 13px;
  }

  /* line 1536, ../sass/_responsive.scss */
  .slider_contact_wrapper .overlay .text_block {
    width: 95%;
  }
}
@media (max-width: 380px) {
  /* line 1543, ../sass/_responsive.scss */
  header #top_header {
    font-size: 10px;
  }
  /* line 1547, ../sass/_responsive.scss */
  header #top_header .login a {
    font-size: 10px;
  }
  /* line 1551, ../sass/_responsive.scss */
  header #top_header #selected-language {
    background-position-y: 3px;
  }
  /* line 1556, ../sass/_responsive.scss */
  header .responsive_sections_button {
    width: 20px;
    height: 20px;
  }

  /* line 1562, ../sass/_responsive.scss */
  .wordpress_block #newsletter_blog .text {
    padding: 10.5px 0;
  }
}
@media (max-height: 650px) and (max-width: 899px) {
  /* line 1572, ../sass/_responsive.scss */
  #slider_container .revolution_description .revolution_text .title {
    font-size: 50px;
  }
  /* line 1576, ../sass/_responsive.scss */
  #slider_container .revolution_description .revolution_text .description {
    font-size: 24px;
  }
}
@media (max-height: 420px) and (max-width: 899px) {
  /* line 1588, ../sass/_responsive.scss */
  #slider_container .revolution_description .revolution_text .title {
    font-size: 30px;
  }
  /* line 1592, ../sass/_responsive.scss */
  #slider_container .revolution_description .revolution_text .description {
    font-size: 12px;
  }

  /* line 1598, ../sass/_responsive.scss */
  #slider_container .links .know_more {
    font-size: 12px;
    background-position-y: 2px;
    background-size: 5px;
    margin-top: 0px;
  }
}
@media (max-height: 480px) and (max-width: 320px) {
  /* line 1607, ../sass/_responsive.scss */
  #slider_container .revolution_description .revolution_text .title {
    font-size: 40px;
  }
}
@media (max-width: 440px) {
  /* line 1614, ../sass/_responsive.scss */
  header #top_header #selected-language {
    display: none;
  }

  /* line 1619, ../sass/_responsive.scss */
  .fancybox-news .popup_inicial .popup_form {
    bottom: 60px;
  }
  /* line 1623, ../sass/_responsive.scss */
  .fancybox-news .popup_inicial .form_popup {
    width: 100%;
    box-sizing: border-box;
    padding: 0 50px;
  }
  /* line 1628, ../sass/_responsive.scss */
  .fancybox-news .popup_inicial .form_popup .popup_button {
    font-size: 12px;
    height: auto;
    padding: 5px 0;
  }
  /* line 1634, ../sass/_responsive.scss */
  .fancybox-news .popup_inicial .form_popup #id_email {
    font-size: 12px;
    height: auto;
    padding: 5px 10px;
  }
  /* line 1641, ../sass/_responsive.scss */
  .fancybox-news .popup_inicial .form_popup .lopd_button a {
    font-size: 8px;
  }
  /* line 1649, ../sass/_responsive.scss */
  .fancybox-news .popup_img .popup_text {
    top: 50px;
  }
  /* line 1653, ../sass/_responsive.scss */
  .fancybox-news .popup_img img {
    width: 300px;
  }
  /* line 1657, ../sass/_responsive.scss */
  .fancybox-news .popup_img .popup_title {
    font-size: 14px;
  }
  /* line 1661, ../sass/_responsive.scss */
  .fancybox-news .popup_img .popup_description {
    line-height: 18px;
  }
  /* line 1664, ../sass/_responsive.scss */
  .fancybox-news .popup_img .popup_description .line1 {
    font-size: 10px;
  }
  /* line 1668, ../sass/_responsive.scss */
  .fancybox-news .popup_img .popup_description hr {
    margin: 5px auto;
    width: 100px;
  }
  /* line 1673, ../sass/_responsive.scss */
  .fancybox-news .popup_img .popup_description .line2, .fancybox-news .popup_img .popup_description .line3 {
    font-size: 6px;
  }
  /* line 1677, ../sass/_responsive.scss */
  .fancybox-news .popup_img .popup_description .button_download {
    font-size: 6px;
    padding: 0 20px;
    margin-top: 0px;
  }
}
