{% if bannersx3 %}
</div><section class="carousel_top_wrapper">{% for x in bannersx3 %}<a class="carousel_element" {% if x.linkUrl %}href="{{ x.linkUrl|safe }}"{% endif %}>
    <div class="carousel_image"><img data-src="{{ x.servingUrl|safe }}=s600" lazy="true" alt="{{ x.title|safe }}"/></div>
    <div class="carousel_title">{{ x.title|safe }}</div>
</a>{% endfor %}</section><div class="container12 wrapper_content">
{% endif %}
{% if bannerx4 %}
</div>
    <section class="bannerx4_wrapper">
        {% if bannerx4_title %}<h2>{{ bannerx4_title.subtitle|safe }}</h2>{% endif %}
        <div class="container12">
        {% for x in bannerx4 %}<a class="banner" {% if x.linkUrl %}href="{{ x.linkUrl|safe }}"{% endif %}>
            <div class="banner_image"><img data-src="{{ x.servingUrl|safe }}" lazy="true" alt="{{ x.altText|safe }}"/></div>
            <div class="banner_title">{{ x.title|safe }}</div>
            <div class="banner_desc">{{ x.description|safe }}</div>
        </a>{% endfor %}</div>
    </section>
<div class="container12 wrapper_content">
{% endif %}
{% if content_subtitle %}
    <section class="content_subtitle_wrapper {% if not inner_section %} effects_sass" sass_effect="slide_up_effect"
    {% else %} " {% endif %}>
    <h1 class="content_subtitle_title">{{ content_subtitle.subtitle|safe }}</h1>

    <div class="content_subtitle_description">
        {{ content_subtitle.content|safe }}
    </div>
    </section>
{% endif %}
{% if rooms %}{% include "rooms.html" %}{% endif %}
{% if content_location %}{% include "location_section.html" %}{% endif %}
{% if automatic_content %}<section class="automatic_content_wrapper">{{ content }}</section>{% endif %}
{% if room_individual %}{{ room_individual|safe }}{% endif %}
{% if banner_iframe %}{% include "banners/_banner_iframe.html" %}{% endif %}
{% if iframe_map %}<div class="iframe_map" style="{% if not minigallery %}margin-bottom: 35px;{% endif %}">{{ iframe_map|safe }}</div>{% endif %}
{% if banner_apartamentos %}
</div>
    <section class="banner_apartamentos_wrapper">
        <div class="container12">
        {% if banner_apartamentos_title %}<h2>{{ banner_apartamentos_title|safe }}</h2>{% endif %}
        <a href="{{host|safe}}/{{seoLinkString}}{{ banner_apartamentos_link|safe }}" class="top_link">{{ T_todos_los_alojamiento }} <i class="fa fa-angle-double-right"></i></a>
        <div class="banner_apartamentos_content"><ul class="slides">
        {% for x in banner_apartamentos %}<li class="banner">
            <div class="banner_image"><img data-src="{{ x.servingUrl|safe }}" lazy="true" alt="{{ x.altText|safe }}"/></div>
            <div class="banner_title">{{ x.title|safe }}</div>
            <div class="banner_links">
                <a class="button_promotion" href="javaScript:booking_namespace('{{ x.namespace|safe }}')">{{ T_book_now }}</a>
                {% if x.linkUrl %}<a href="{{ x.linkUrl|safe }}" class="read_more"><i class="fa fa-plus"></i></a>{% endif %}
            </div>
        </li>{% endfor %}</ul></div></div>
    </section>
    <script>
        $(function () {
            var params_slider_2 = {
                controlNav: true,
                directionNav: false,
                animation: "slide",
                itemWidth: (1140/3),
                minItems: 3,
                maxItems: 3,
                itemMargin: 10
            };
            $(".banner_apartamentos_content").flexslider(params_slider_2);
        })
    </script>
<div class="container12 wrapper_content">
{% endif %}
{% if form_contact %}</div>{% include "_form_contact.html" %}<div class="container12 wrapper_content">{% endif %}
{% if iframe_google_maps %}<div class="iframe_maps_wrapper">{{ iframe_google_maps.content|safe }}</div>{% endif %}
{% if images_section %}{% include "_gallery.html" %}{% endif %}
{% if offers_home or cycle_banners %}</div>{% include "cycle_banners.html" %}<div class="container12 wrapper_content">{% endif %}
{% if minigallery %}
    </div>
    <section class="carousel_bottom_wrapper">
        {% if minigallery_title.subtitle %}
            <div class="minigallery_title">{{ minigallery_title.subtitle|safe }}</div>
        {% endif %}
        <ul class="slides">
            {% for x in minigallery %}
                <li class="carousel_element">
                    <a href="{{ x.servingUrl|safe }}=s600" rel="lightbox[gallery_footer]">
                        <div class="carousel_image"><img data-src="{{ x.servingUrl|safe }}" lazy="true"/></div>
                    </a>
                </li>
            {% endfor %}
        </ul>
    </section>
    <script async>
        $(function () {
            params_slider = {
                controlNav: false,
                directionNav: true,
                animation: "slide",
                prevText: '<i class="fa fa-angle-left" aria-hidden="true"></i>',
                nextText: '<i class="fa fa-angle-right" aria-hidden="true"></i>',
                itemWidth: ($(window).width() / 5),
                minItems: 5,
                maxItems: 5,
                move: 1,
                itemMargin: 5
            }
            $(".carousel_bottom_wrapper").flexslider(params_slider);
        })
    </script>
    <div class="container12 wrapper_content">
{% endif %}