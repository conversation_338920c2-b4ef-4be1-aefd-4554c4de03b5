.content_access.section_restring {
  padding: 0;
  display: block !important;
  #contentInPage {
    padding: 30px calc((100% - 1040px) / 2);
    text-align: center;
    h1 {
      color: $corporate_1 !important;
    }
    .block-rest {
      ul {
        li {
          padding: 5px !important;
          background: none !important;
          margin-bottom: 0 !important;
          display: inline-block;
          vertical-align: top;
          width: calc((100% / 3) - 10px);
          img {
            vertical-align:middle;
          }
        }
      }
    }
  }
  #userAndPassword {
    background: #FAFAFA;
    h1, .proceted_section_content_wrapper {
      display: none;
    }
    form {
      text-align:center;
      .contInput {
        display: inline-block;
        vertical-align: middle;
        float: none !important;
        .user_label, .password_label {
          padding: 15px 10px;
          font-size: $font_body_size;
          margin-bottom: 20px;
          display: none;
        }
        input {
          background: white;
          border: none;
          border-bottom: 1px solid $black;
          font-size: $font_body_size;
          font-weight: 300;
          padding: 15px 10px;
          width: 200px;
          margin-left: 10px;
          margin-bottom: 20px;
          color: $black;
          letter-spacing: 1px!important;
          &::placeholder {
              color: rgba($black, .6);
              font-weight: lighter;
          }
        }
      }
      #access_button {
        float: none !important;
        padding-bottom: 20px;
        a div {
          position: relative;
          padding: 15px 0 !important;
          text-align: center;
          width: 200px !important;
          float: none !important;
          border-radius: 0 !important;
          background: $corporate_1 !important;
          color: white !important;
          margin: auto;
          &:hover {
            &:after {
              right: 20px;
            }
          }
          &:after {
            font-family: "Font Awesome 5 Pro";
            content: '\f061';
            color: white;
            font-size: 20px;
            @include center_y;
            right: 10px;
            @include transition(all, .6s);
          }
        }
      }
    }
  }
}
.register-form {
  padding: 30px calc((100% - 1040px) / 2);
  text-align: center;
  .register-form-wrapper {
    text-align: left;
    li {
      display: inline-block;
      vertical-align: bottom;
      padding: 10px 5px;
      width: calc(50% - 5px);
      text-align: left;
      &.select {
        position: relative;
        select {
          position:relative;
          z-index: 2;
        }
        &:after {
            font-family: "Font Awesome 5 Pro";
            content: '\f107';
            color: $black;
            font-size: 20px;
            position: absolute;
            bottom: 20px;
            right: 15px;
            @include transition(all, .6s);
          }
      }
      label {
        display: block;
        width: 100%;
        padding-top: 5px;
        color: $corporate_1;
      }
      input[type=text], select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: #FAFAFA;
        border: none;
        border-bottom: 1px solid $black;
        font-size: $font_body_size;
        font-weight: 300;
        padding: 15px 10px;
        width: 100%;
        color: $black;
        letter-spacing: 1px!important;
        &::placeholder {
            color: rgba($black, .6);
            font-weight: lighter;
        }
      }
    }
    #contact-button {
      display: inline-block;
      vertical-align: bottom;
      position: relative;
      padding: 12px 0;
      margin-bottom: 10px;
      text-align: center;
      width: calc(50% - 5px);
      background: $corporate_1;
      color: white;
      margin: auto;
      cursor: pointer;
      &:hover {
        &:after {
          right: 20px;
        }
      }
      &:after {
        font-family: "Font Awesome 5 Pro";
        content: '\f061';
        color: white;
        font-size: 20px;
        @include center_y;
        right: 10px;
        @include transition(all, .6s);
      }
    }
  }
}