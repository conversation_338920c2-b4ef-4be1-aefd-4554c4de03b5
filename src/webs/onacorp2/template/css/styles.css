@import url("https://fonts.googleapis.com/css2?family=Satisfy&display=swap");
@import url(//fonts.googleapis.com/css?family=Montserrat:300,400,600|Source+Sans+Pro:400,300,700,600&display=swap);
/* line 50, ../sass/_defaults.scss */
h1, .h1 {
  font-family: "Cera Pro", sans-serif;
  font-size: 56px;
  font-weight: bold;
  line-height: 55px;
}

/* line 59, ../sass/_defaults.scss */
h2, .h2 {
  font-family: "Cera Pro", sans-serif;
  font-size: 40px;
  font-weight: normal;
  line-height: 40px;
}

/* line 68, ../sass/_defaults.scss */
h3, #main_menu .main_menu .main-section-div-wrapper a, .h3 {
  font-family: "Cera Pro", sans-serif;
  font-size: 34px;
  font-weight: bold;
  line-height: 34px;
}

/* line 78, ../sass/_defaults.scss */
h4, #main_menu .main_menu .social a, .h4 {
  font-family: "Cera Pro", sans-serif;
  font-size: 25px;
  font-weight: normal;
  line-height: 25px;
}

/* line 88, ../sass/_defaults.scss */
h5, .h5 {
  font-family: "Cera Pro", sans-serif;
  font-size: 20px;
  font-weight: bold;
  line-height: 20px;
}

/* line 98, ../sass/_defaults.scss */
h6, .h6 {
  font-family: "Cera Pro", sans-serif;
  font-size: 18px;
  font-weight: normal;
  line-height: 18px;
}

/* line 108, ../sass/_defaults.scss */
.body1 {
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-size: 16px;
  color: #002D42;
}

/* line 115, ../sass/_defaults.scss */
.body2 {
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-size: 16px;
  color: #002D42;
  font-size: 14px;
}

/* line 121, ../sass/_defaults.scss */
.subtitle1 {
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-size: 16px;
  color: #002D42;
}

/* line 128, ../sass/_defaults.scss */
.subtitle2 {
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-size: 16px;
  color: #002D42;
  font-size: 14px;
  font-weight: 500;
}

/* line 137, ../sass/_defaults.scss */
.overline {
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-size: 16px;
  color: #002D42;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 12px;
}

/* line 146, ../sass/_defaults.scss */
.caption {
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-size: 16px;
  color: #002D42;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 300;
  font-size: 12px;
}

/* line 211, ../sass/_defaults.scss */
.btn {
  display: inline-block;
  padding: 10px;
  min-width: 145px;
  max-width: 100%;
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-size: 16px;
  color: #002D42;
  font-size: 14px;
  background: #002D42;
  cursor: pointer;
  color: white;
  font-weight: bold;
  font-size: 14pt;
  border-width: 0;
  text-transform: uppercase;
  text-align: center;
  -moz-transition: all, 0.6s;
  -ms-transition: all, 0.6s;
  -o-transition: all, 0.6s;
  -webkit-transition: all, 0.6s;
  transition: all, 0.6s;
}
/* line 169, ../sass/_defaults.scss */
.btn:hover {
  background: #335768;
}
/* line 172, ../sass/_defaults.scss */
.btn.btn-small {
  min-width: 100px;
  padding: 5px;
  font-size: 12px;
}
/* line 177, ../sass/_defaults.scss */
.btn.btn-rounded {
  border-radius: 20px;
  padding: 10px 20px;
  min-width: 175px;
  font-weight: normal;
  font-size: 16pt;
}
/* line 183, ../sass/_defaults.scss */
.btn.btn-rounded.btn-small {
  min-width: 100px;
  padding: 5px;
  font-size: 12px;
}
/* line 188, ../sass/_defaults.scss */
.btn.btn-rounded:hover {
  background: #002D42;
  color: #11CCC7;
}
/* line 193, ../sass/_defaults.scss */
.btn.btn-rounded-aqua {
  border-radius: 20px;
  padding: 10px 20px;
  min-width: 220px;
  font-size: 14pt;
  background: #11CCC7;
  color: #002D42;
}
/* line 200, ../sass/_defaults.scss */
.btn.btn-rounded-aqua.btn-small {
  min-width: 100px;
  padding: 5px;
  font-size: 12px;
}
/* line 205, ../sass/_defaults.scss */
.btn.btn-rounded-aqua:hover {
  background: #11CCC7;
  color: white;
}

/* line 258, ../sass/_defaults.scss */
.link {
  position: relative;
  color: #002D42;
  font-size: 20px;
  font-weight: bold;
  padding-right: 50px;
  -moz-transition: padding, 0.6s;
  -ms-transition: padding, 0.6s;
  -o-transition: padding, 0.6s;
  -webkit-transition: padding, 0.6s;
  transition: padding, 0.6s;
}
/* line 222, ../sass/_defaults.scss */
.link:hover {
  padding-right: 60px;
}
/* line 225, ../sass/_defaults.scss */
.link:before {
  content: '';
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%) rotate(45deg);
  -moz-transform: translateY(-50%) rotate(45deg);
  -ms-transform: translateY(-50%) rotate(45deg);
  -o-transform: translateY(-50%) rotate(45deg);
  transform: translateY(-50%) rotate(45deg);
  right: 0;
  width: 7px;
  height: 7px;
  border: 1px solid #002D42;
  border-bottom-width: 0;
  border-left-width: 0;
}
/* line 241, ../sass/_defaults.scss */
.link:after {
  content: '';
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 0;
  display: inline-block;
  vertical-align: middle;
  height: 1px;
  background: #002D42;
  width: 40px;
}

/* line 274, ../sass/_defaults.scss */
.swipe-up {
  display: block;
  padding: 5px;
  color: white;
}
/* line 265, ../sass/_defaults.scss */
.swipe-up:before {
  content: '\f106';
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 300;
  display: block !important;
  text-align: center;
  margin: 0 auto -3px;
}

@keyframes preloading {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}
/* line 312, ../sass/_defaults.scss */
.tabs {
  display: table;
  width: 100%;
}
/* line 286, ../sass/_defaults.scss */
.tabs .tab {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  padding: 5px 20px;
  min-width: 145px;
  -moz-transition: padding, 0.6s;
  -ms-transition: padding, 0.6s;
  -o-transition: padding, 0.6s;
  -webkit-transition: padding, 0.6s;
  transition: padding, 0.6s;
}
/* line 293, ../sass/_defaults.scss */
.tabs .tab:before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  width: 0;
  height: 2px;
  -moz-transition: width, 0.6s;
  -ms-transition: width, 0.6s;
  -o-transition: width, 0.6s;
  -webkit-transition: width, 0.6s;
  transition: width, 0.6s;
}
/* line 304, ../sass/_defaults.scss */
.tabs .tab:hover, .tabs .tab.selected {
  padding: 2px 20px 8px;
}
/* line 306, ../sass/_defaults.scss */
.tabs .tab:hover:before, .tabs .tab.selected:before {
  width: 100%;
}

/* line 356, ../sass/_defaults.scss */
.filters {
  display: table;
  width: 100%;
  padding: 20px 0 40px;
}
/* line 319, ../sass/_defaults.scss */
.filters .filter {
  position: relative;
  display: inline-block;
  padding: 5px 10px;
  min-width: 145px;
  text-align: center;
  text-transform: uppercase;
  color: #002D42;
  letter-spacing: 0.4px;
  -moz-transition: color, 0.6s;
  -ms-transition: color, 0.6s;
  -o-transition: color, 0.6s;
  -webkit-transition: color, 0.6s;
  transition: color, 0.6s;
}
/* line 329, ../sass/_defaults.scss */
.filters .filter:before {
  content: '';
  position: absolute;
  top: -1px;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  height: 100%;
  width: 0;
  border: 1px solid transparent;
  -moz-transition: width, 0.6s;
  -ms-transition: width, 0.6s;
  -o-transition: width, 0.6s;
  -webkit-transition: width, 0.6s;
  transition: width, 0.6s;
}
/* line 343, ../sass/_defaults.scss */
.filters .filter:hover:before, .filters .filter.selected:before {
  width: 100%;
  border: 1px solid #002D42;
}
/* line 348, ../sass/_defaults.scss */
.filters .filter.selected {
  color: #11CCC7;
}
/* line 350, ../sass/_defaults.scss */
.filters .filter.selected:before {
  border: 1px solid #11CCC7;
}

/* line 359, ../sass/_defaults.scss */
.toolkit {
  position: relative;
}
/* line 361, ../sass/_defaults.scss */
.toolkit .tooltip {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
  top: 100%;
  z-index: -1;
  height: 0;
  overflow: hidden;
  margin-top: -10px;
  opacity: 0;
  display: inline-block;
  padding: 5px 10px;
  background: white;
  color: #002D42;
  font-size: 12px;
  white-space: nowrap;
  -webkit-transition: margin-top .6s, opacity .6s;
  -moz-transition: margin-top .6s, opacity .6s;
  -ms-transition: margin-top .6s, opacity .6s;
  -o-transition: margin-top .6s, opacity .6s;
  transition: margin-top .6s, opacity .6s;
}
/* line 386, ../sass/_defaults.scss */
.toolkit .tooltip:before {
  content: '';
  border: 5px solid transparent;
  border-color: transparent transparent white transparent;
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
  bottom: 100%;
}
/* line 400, ../sass/_defaults.scss */
.toolkit:hover .tooltip {
  opacity: 1;
  margin-top: 0;
  height: auto;
  overflow: visible;
  z-index: 1;
}

/* line 409, ../sass/_defaults.scss */
.float_left {
  display: inline-block;
  float: left;
}

/* line 413, ../sass/_defaults.scss */
.float_right {
  display: inline-block;
  float: right;
}

/* line 418, ../sass/_defaults.scss */
#content {
  position: relative;
}

/* line 422, ../sass/_defaults.scss */
.page {
  background: white;
}
/* line 424, ../sass/_defaults.scss */
.page .pagetabs {
  position: relative;
  z-index: 30;
  padding: 20px calc((100% - 1040px) / 2);
}
/* line 429, ../sass/_defaults.scss */
.page .pagetabs.dark .tab {
  color: white;
}
/* line 433, ../sass/_defaults.scss */
.page .pagetabs .tab {
  text-transform: uppercase;
  font-weight: bold;
  font-size: 12px;
  color: #002D42;
  text-align: center;
  min-width: 50px;
  padding: 5px 0;
  margin-right: 25px;
}
/* line 442, ../sass/_defaults.scss */
.page .pagetabs .tab:hover, .page .pagetabs .tab.selected {
  padding: 0 0 20px;
}
/* line 445, ../sass/_defaults.scss */
.page .pagetabs .tab:before {
  background: #11CCC7;
}

/* line 451, ../sass/_defaults.scss */
.scroll_nav {
  position: fixed;
  top: 50%;
  z-index: 1001;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 15px;
}
/* line 463, ../sass/_defaults.scss */
.scroll_nav.fixed a:before, .scroll_nav.fixed a span {
  background: #11CCC7;
}
/* line 468, ../sass/_defaults.scss */
.scroll_nav a {
  display: block;
}
/* line 471, ../sass/_defaults.scss */
.scroll_nav a:first-of-type:before {
  display: none;
}
/* line 476, ../sass/_defaults.scss */
.scroll_nav a.active:before {
  opacity: 1;
}
/* line 479, ../sass/_defaults.scss */
.scroll_nav a.active span {
  width: 10px;
  height: 10px;
}
/* line 484, ../sass/_defaults.scss */
.scroll_nav a.active ~ a:before {
  opacity: 0.3;
}
/* line 487, ../sass/_defaults.scss */
.scroll_nav a.active ~ a span {
  width: 4px;
  height: 4px;
}
/* line 493, ../sass/_defaults.scss */
.scroll_nav a:before {
  content: '';
  display: block;
  margin: auto;
  width: 2px;
  height: 30px;
  opacity: 1;
  background: white;
  -moz-transition: all, 0.6s;
  -ms-transition: all, 0.6s;
  -o-transition: all, 0.6s;
  -webkit-transition: all, 0.6s;
  transition: all, 0.6s;
}
/* line 503, ../sass/_defaults.scss */
.scroll_nav a span {
  display: block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: white;
  margin: 1px auto;
}

/* line 513, ../sass/_defaults.scss */
.wave-aqua, .wave-navy, .wave-white {
  display: block;
  height: 20px;
  background: url("/img/onac2/wave-aqua.png");
}

/* line 518, ../sass/_defaults.scss */
.wave-navy {
  background: url("/img/onac2/wave-navy.png");
}

/* line 521, ../sass/_defaults.scss */
.wave-white {
  background: url("/img/onac2/wave-white.png");
}

/* line 524, ../sass/_defaults.scss */
.wave-aqua-small, .wave-navy-small {
  display: block;
  height: 10px;
  background: url("/img/onac2/wave-aqua-small.png");
}

/* line 529, ../sass/_defaults.scss */
.wave-navy-small {
  background: url("/img/onac2/wave-navy-small.png");
}

/* line 533, ../sass/_defaults.scss */
.input_checkbox {
  display: inline-block;
  vertical-align: middle;
  position: relative;
}

/* line 538, ../sass/_defaults.scss */
input[type=checkbox], input[type=radio] {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  margin: 0;
  z-index: 2;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  outline: none;
  border: 1px solid #11CCC7;
}
/* line 551, ../sass/_defaults.scss */
input[type=checkbox] + label + .checkmark, input[type=checkbox] + .checkmark, input[type=radio] + label + .checkmark, input[type=radio] + .checkmark {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  margin-top: 1px;
}
/* line 565, ../sass/_defaults.scss */
input[type=checkbox] + label + .checkmark.checked, input[type=checkbox] + .checkmark.checked, input[type=radio] + label + .checkmark.checked, input[type=radio] + .checkmark.checked {
  background: #11CCC7;
}

/* line 571, ../sass/_defaults.scss */
input[type=radio] {
  border-radius: 50%;
}
/* line 573, ../sass/_defaults.scss */
input[type=radio] + .checkmark {
  border-radius: 50%;
}

/* line 3, ../../../../sass/plugins/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 8, ../../../../sass/plugins/_mixins.scss */
.center_xy_before:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 13, ../../../../sass/plugins/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 17, ../../../../sass/plugins/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 21, ../../../../sass/plugins/_mixins.scss */
.center_y_before:before {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 25, ../../../../sass/plugins/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 30, ../../../../sass/plugins/_mixins.scss */
.fs:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
}

/* line 110, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:before, .icon-xcross:after {
  content: '';
  width: 100%;
  height: 2px;
  background: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 124, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -moz-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 61, ../../../../sass/plugins/_mixins.scss */
.display_flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* line 1, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown {
  margin: 40px 20px 20px;
  text-align: center;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  bottom: 180px;
  left: 48% !important;
  color: white;
}
/* line 9, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown div {
  display: inline-block;
  font-size: 16px;
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 0;
  padding-bottom: 0;
  text-transform: uppercase;
}
/* line 20, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .days {
  font-weight: 600;
}
/* line 23, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .date {
  display: block;
  font-size: 4.5rem;
}
/* line 28, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .title_format {
  font-weight: 600;
}

@media screen and (max-width: 800px) {
  /* line 35, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown {
    display: inline-block;
    width: 380px;
    top: 330px;
    left: 45% !important;
  }
  /* line 40, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown div {
    font-size: 10px;
  }
  /* line 43, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown .date {
    font-size: 2.5rem;
  }
}
/**
 * Owl Carousel v2.2.1
 * Copyright 2013-2017 David Deutsch
 * Licensed under  ()
 */
/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel, .owl-carousel .owl-item {
  -webkit-tap-highlight-color: transparent;
  position: relative;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel {
  display: none;
  width: 100%;
  z-index: 1;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage {
  position: relative;
  -ms-touch-action: pan-Y;
  -moz-backface-visibility: hidden;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage:after {
  content: ".";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage-outer {
  position: relative;
  overflow: hidden;
  -webkit-transform: translate3d(0, 0, 0);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item, .owl-carousel .owl-wrapper {
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item {
  min-height: 1px;
  float: left;
  -webkit-backface-visibility: hidden;
  -webkit-touch-callout: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item img {
  display: block;
  width: 100%;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-dots.disabled, .owl-carousel .owl-nav.disabled {
  display: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.no-js .owl-carousel, .owl-carousel.owl-loaded {
  display: block;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-dot, .owl-carousel .owl-nav .owl-next, .owl-carousel .owl-nav .owl-prev {
  cursor: pointer;
  cursor: hand;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-loading {
  opacity: 0;
  display: block;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-hidden {
  opacity: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-refresh .owl-item {
  visibility: hidden;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-drag .owl-item {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-grab {
  cursor: move;
  cursor: grab;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-rtl {
  direction: rtl;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-rtl .owl-item {
  float: right;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-animated-in {
  z-index: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-animated-out {
  z-index: 1;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .fadeOut {
  animation-name: fadeOut;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-height {
  transition: height .5s ease-in-out;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item .owl-lazy {
  opacity: 0;
  transition: opacity .4s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item img.owl-lazy {
  transform-style: preserve-3d;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-wrapper {
  position: relative;
  height: 100%;
  background: #000;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-play-icon {
  position: absolute;
  height: 80px;
  width: 80px;
  left: 50%;
  top: 50%;
  margin-left: -40px;
  margin-top: -40px;
  background: url(owl.video.play.png) no-repeat;
  cursor: pointer;
  z-index: 1;
  -webkit-backface-visibility: hidden;
  transition: transform .1s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-play-icon:hover {
  -ms-transform: scale(1.3, 1.3);
  transform: scale(1.3, 1.3);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-playing .owl-video-play-icon, .owl-carousel .owl-video-playing .owl-video-tn {
  display: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-tn {
  opacity: 0;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: opacity .4s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-frame {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
}

@font-face {
  font-family: 'Cera Pro';
  src: url("/static_1/fonts/cera_pro/CeraPro-Italic.eot");
  src: local("Cera Pro Italic"), local("CeraPro-Italic"), url("/static_1/fonts/cera_pro/CeraPro-Italic.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/cera_pro/CeraPro-Italic.woff") format("woff"), url("/static_1/fonts/cera_pro/CeraPro-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'Cera Pro';
  src: url("/static_1/fonts/cera_pro/CeraPro-Regular.eot");
  src: local("Cera Pro Regular"), local("CeraPro-Regular"), url("/static_1/fonts/cera_pro/CeraPro-Regular.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/cera_pro/CeraPro-Regular.woff") format("woff"), url("/static_1/fonts/cera_pro/CeraPro-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Cera Pro';
  src: url("/static_1/fonts/cera_pro/CeraPro-BlackItalic.eot");
  src: local("Cera Pro Black Italic"), local("CeraPro-BlackItalic"), url("/static_1/fonts/cera_pro/CeraPro-BlackItalic.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/cera_pro/CeraPro-BlackItalic.woff") format("woff"), url("/static_1/fonts/cera_pro/CeraPro-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'Cera Pro';
  src: url("/static_1/fonts/cera_pro/CeraPro-Light.eot");
  src: local("Cera Pro Light"), local("CeraPro-Light"), url("/static_1/fonts/cera_pro/CeraPro-Light.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/cera_pro/CeraPro-Light.woff") format("woff"), url("/static_1/fonts/cera_pro/CeraPro-Light.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Cera Pro';
  src: url("/static_1/fonts/cera_pro/CeraPro-BoldItalic.eot");
  src: local("Cera Pro Bold Italic"), local("CeraPro-BoldItalic"), url("/static_1/fonts/cera_pro/CeraPro-BoldItalic.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/cera_pro/CeraPro-BoldItalic.woff") format("woff"), url("/static_1/fonts/cera_pro/CeraPro-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'Cera Pro';
  src: url("/static_1/fonts/cera_pro/CeraPro-Black.eot");
  src: local("Cera Pro Black"), local("CeraPro-Black"), url("/static_1/fonts/cera_pro/CeraPro-Black.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/cera_pro/CeraPro-Black.woff") format("woff"), url("/static_1/fonts/cera_pro/CeraPro-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Cera Pro';
  src: url("/static_1/fonts/cera_pro/CeraPro-Medium.eot");
  src: local("Cera Pro Medium"), local("CeraPro-Medium"), url("/static_1/fonts/cera_pro/CeraPro-Medium.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/cera_pro/CeraPro-Medium.woff") format("woff"), url("/static_1/fonts/cera_pro/CeraPro-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Cera Pro';
  src: url("/static_1/fonts/cera_pro/CeraPro-Bold.eot");
  src: local("Cera Pro Bold"), local("CeraPro-Bold"), url("/static_1/fonts/cera_pro/CeraPro-Bold.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/cera_pro/CeraPro-Bold.woff") format("woff"), url("/static_1/fonts/cera_pro/CeraPro-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Cera Pro';
  src: url("/static_1/fonts/cera_pro/CeraPro-Thin.eot");
  src: local("Cera Pro Thin"), local("CeraPro-Thin"), url("/static_1/fonts/cera_pro/CeraPro-Thin.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/cera_pro/CeraPro-Thin.woff") format("woff"), url("/static_1/fonts/cera_pro/CeraPro-Thin.ttf") format("truetype");
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Cera Pro';
  src: url("/static_1/fonts/cera_pro/CeraPro-MediumItalic.eot");
  src: local("Cera Pro Medium Italic"), local("CeraPro-MediumItalic"), url("/static_1/fonts/cera_pro/CeraPro-MediumItalic.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/cera_pro/CeraPro-MediumItalic.woff") format("woff"), url("/static_1/fonts/cera_pro/CeraPro-MediumItalic.ttf") format("truetype");
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'Cera Pro';
  src: url("/static_1/fonts/cera_pro/CeraPro-LightItalic.eot");
  src: local("Cera Pro Light Italic"), local("CeraPro-LightItalic"), url("/static_1/fonts/cera_pro/CeraPro-LightItalic.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/cera_pro/CeraPro-LightItalic.woff") format("woff"), url("/static_1/fonts/cera_pro/CeraPro-LightItalic.ttf") format("truetype");
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'Cera Pro';
  src: url("/static_1/fonts/cera_pro/CeraPro-ThinItalic.eot");
  src: local("Cera Pro Thin Italic"), local("CeraPro-ThinItalic"), url("/static_1/fonts/cera_pro/CeraPro-ThinItalic.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/cera_pro/CeraPro-ThinItalic.woff") format("woff"), url("/static_1/fonts/cera_pro/CeraPro-ThinItalic.ttf") format("truetype");
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'Lovely Home';
  src: url("/static_1/fonts/Lovely_Home/LovelyHome.ttf");
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}
/* line 3, ../../../../sass/booking/_booking_engine_7.scss */
.booking_widget {
  position: relative;
}

/* line 7, ../../../../sass/booking/_booking_engine_7.scss */
#full-booking-engine-html-7 {
  width: 275px;
  display: block;
}
/* line 11, ../../../../sass/booking/_booking_engine_7.scss */
#full-booking-engine-html-7 form.booking_form {
  padding: 0 !important;
}
/* line 15, ../../../../sass/booking/_booking_engine_7.scss */
#full-booking-engine-html-7 .booking_form_title {
  background: #383838;
}

/* line 21, ../../../../sass/booking/_booking_engine_7.scss */
.destination_wrapper {
  border-bottom: 1px solid lightgray;
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat 92% 70% !important;
}
/* line 25, ../../../../sass/booking/_booking_engine_7.scss */
.destination_wrapper label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
  display: inline-block;
  margin: 10px 0 0 10px;
}

/* line 34, ../../../../sass/booking/_booking_engine_7.scss */
.destination_wrapper input {
  border: none;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 25px;
  font-weight: 600;
  background: white;
}
/* line 41, ../../../../sass/booking/_booking_engine_7.scss */
.destination_wrapper input::-webkit-input-placeholder {
  color: #585d63;
}
/* line 45, ../../../../sass/booking/_booking_engine_7.scss */
.destination_wrapper input:-moz-placeholder {
  /* Firefox 18- */
  color: #585d63;
}
/* line 50, ../../../../sass/booking/_booking_engine_7.scss */
.destination_wrapper input::-moz-placeholder {
  /* Firefox 19+ */
  color: #585d63;
}
/* line 55, ../../../../sass/booking/_booking_engine_7.scss */
.destination_wrapper input:-ms-input-placeholder {
  color: #585d63;
}

/* line 60, ../../../../sass/booking/_booking_engine_7.scss */
.entry_date_wrapper {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 66.6%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}

/* line 75, ../../../../sass/booking/_booking_engine_7.scss */
.departure_date_wrapper, .half_size {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 50%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}
/* line 89, ../../../../sass/booking/_booking_engine_7.scss */
.departure_date_wrapper .date_day, .half_size .date_day {
  font-size: 21px !important;
}

/* line 94, ../../../../sass/booking/_booking_engine_7.scss */
.nights_number_wrapper {
  float: right;
  height: 70px;
  border-bottom: 1px solid lightgrey;
  box-sizing: border-box;
  width: 33.3%;
  padding: 5px 10px;
}

/* line 103, ../../../../sass/booking/_booking_engine_7.scss */
.num_nights_label, .entry_date_label, .children_label, .rooms_label, .adults_label, .promocode_label, .departure_date_label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/* line 109, ../../../../sass/booking/_booking_engine_7.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 30%;
}

/*===== Entry date =====*/
/* line 116, ../../../../sass/booking/_booking_engine_7.scss */
.date_box.entry_date, .date_box.departure_date {
  font-size: 20px;
  background: url(/static_1/images/booking_5/calendar.png) no-repeat center right;
  margin-top: 9px;
  background-size: 29px;
}
/* line 122, ../../../../sass/booking/_booking_engine_7.scss */
.date_box.entry_date .date_day, .date_box.departure_date .date_day {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 25px;
  font-weight: 600;
}

/*====== Booking button ====*/
/* line 131, ../../../../sass/booking/_booking_engine_7.scss */
button.submit_button {
  width: 100%;
  border: 0;
  border-radius: 0;
  background: #ffd600;
  height: 55px;
  text-transform: uppercase;
  font-weight: bolder;
  font-size: 21px;
  color: #565656;
  cursor: pointer;
}

/*====== Rooms section ======*/
/* line 145, ../../../../sass/booking/_booking_engine_7.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 33.3%;
  padding: 7px 10px 5px;
  box-sizing: border-box;
}
/* line 152, ../../../../sass/booking/_booking_engine_7.scss */
.rooms_number_wrapper .room_list_wrapper li.room {
  float: right;
  width: 69%;
}
/* line 158, ../../../../sass/booking/_booking_engine_7.scss */
.rooms_number_wrapper .adults_selector {
  height: 70px;
  float: left;
  border-right: 1px solid gray;
  width: 46.5%;
}

/* line 166, ../../../../sass/booking/_booking_engine_7.scss */
.room_list_wrapper {
  display: table;
  float: right;
  width: 66.6%;
}
/* line 171, ../../../../sass/booking/_booking_engine_7.scss */
.room_list_wrapper .room_title {
  display: none;
}
/* line 175, ../../../../sass/booking/_booking_engine_7.scss */
.room_list_wrapper .adults_selector {
  border-right: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
}
/* line 180, ../../../../sass/booking/_booking_engine_7.scss */
.room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
  width: 50%;
  display: inline-block;
  height: 70px;
  box-sizing: border-box;
  padding: 7px 10px 5px;
}
/* line 188, ../../../../sass/booking/_booking_engine_7.scss */
.room_list_wrapper .children_selector {
  width: 50% !important;
  padding: 7px 10px 5px;
  float: right;
}

/* line 195, ../../../../sass/booking/_booking_engine_7.scss */
.room_list .room {
  height: 70px;
}

/* line 199, ../../../../sass/booking/_booking_engine_7.scss */
.room_list .room.room3, .room_list .room.room2 {
  border-top: 1px solid lightgrey;
}

/*==== Promocode section  ====*/
/* line 204, ../../../../sass/booking/_booking_engine_7.scss */
.promocode_wrapper {
  display: table;
  width: 100%;
  padding: 10px 10px 5px;
  border-top: 1px solid lightgrey;
  box-sizing: border-box;
}

/* line 212, ../../../../sass/booking/_booking_engine_7.scss */
label.promocode_label {
  display: block;
}

/* line 216, ../../../../sass/booking/_booking_engine_7.scss */
input.promocode_input {
  border: 0;
  width: 100%;
  display: block;
  height: 30px;
  font-size: 13px;
  font-weight: lighter;
  margin-top: 2px;
  font-family: sans-serif;
}

/*====== Selectric ======*/
/* line 228, ../../../../sass/booking/_booking_engine_7.scss */
.selectricItems {
  overflow: auto !important;
  width: 92px !important;
  margin-left: -11px;
  margin-top: 2px;
}
/* line 234, ../../../../sass/booking/_booking_engine_7.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 240, ../../../../sass/booking/_booking_engine_7.scss */
.selectricItems li {
  font-weight: 500;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAADAQMAAABs5if8AAAABlBMVEXOzs7y8vKNS741AAAADklEQVQI12M4wPSAWQAABoQBtgsaY5kAAAAASUVORK5CYII=);
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  line-height: 29px !important;
  font-size: 18px !important;
  font-family: 'Source Sans Pro', sans-serif;
}
/* line 250, ../../../../sass/booking/_booking_engine_7.scss */
.selectricItems li:hover {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 257, ../../../../sass/booking/_booking_engine_7.scss */
.selectricItems ul {
  z-index: 40;
}
/* line 261, ../../../../sass/booking/_booking_engine_7.scss */
.selectricItems .room {
  padding-top: 17px;
}

/* line 267, ../../../../sass/booking/_booking_engine_7.scss */
#full-booking-engine-html-7 .selectric .button {
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 23px;
  margin-left: 0;
  margin-top: 8px;
  text-indent: 999px;
  height: 22px;
  font-weight: 600;
}

/* line 280, ../../../../sass/booking/_booking_engine_7.scss */
.selectricWrapper .selectric .label {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 28px;
  margin-left: 0;
  font-weight: 600;
}

/* line 288, ../../../../sass/booking/_booking_engine_7.scss */
.selectricWrapper .selectric {
  margin-top: 3px;
}

/* line 292, ../../../../sass/booking/_booking_engine_7.scss */
.selectricWrapper {
  width: 78px !important;
}

/*======= Phone web support =======*/
/* line 297, ../../../../sass/booking/_booking_engine_7.scss */
.wrapper-new-web-support.booking_form_title {
  border-radius: 0;
  padding: 5px 10px;
  font-family: 'Montserrat', sans-serif;
  font-size: 11px !important;
  margin-top: 11px;
}

/* line 305, ../../../../sass/booking/_booking_engine_7.scss */
.wrapper-new-web-support .web_support_number, .web_support_label_1 {
  font-size: 11px !important;
  margin-left: 5px;
}

/* line 310, ../../../../sass/booking/_booking_engine_7.scss */
img.phone_support_image {
  float: left;
  width: 28px;
  margin-top: 1px;
  margin-right: 12px;
}

/*======= Header title ======*/
/* line 318, ../../../../sass/booking/_booking_engine_7.scss */
.booking_title_1, .booking_title_2, .best_price {
  display: none;
}

/* line 322, ../../../../sass/booking/_booking_engine_7.scss */
.promocode_header {
  font-family: 'Montserrat', sans-serif;
  padding: 10px;
  display: table;
  width: 100%;
  box-sizing: border-box;
  cursor: pointer;
}
/* line 330, ../../../../sass/booking/_booking_engine_7.scss */
.promocode_header:hover {
  opacity: 0.8;
}
/* line 334, ../../../../sass/booking/_booking_engine_7.scss */
.promocode_header img.booking_header_discount {
  float: left;
  margin-right: 15px;
}
/* line 339, ../../../../sass/booking/_booking_engine_7.scss */
.promocode_header p.first_offer_name {
  font-size: 12px;
  color: #8096c7;
  margin-top: 9px;
}
/* line 345, ../../../../sass/booking/_booking_engine_7.scss */
.promocode_header p.second_offer_name {
  font-size: 10px;
  margin-top: 3px;
  color: gray;
}

/* line 352, ../../../../sass/booking/_booking_engine_7.scss */
.ui-datepicker {
  width: 283px;
}
/* line 355, ../../../../sass/booking/_booking_engine_7.scss */
.ui-datepicker .ui-widget-header {
  background: none !important;
  border: 0;
}
/* line 359, ../../../../sass/booking/_booking_engine_7.scss */
.ui-datepicker .ui-widget-header .ui-datepicker-title {
  color: #646464 !important;
  font-family: Verdana, Arial, sans-serif;
  font-weight: 300;
}

/* line 368, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-datepicker th {
  font-weight: 300;
  font-size: 14px;
}
/* line 373, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-state-default, body .ui-widget-content .ui-state-default, body .ui-widget-header .ui-state-default {
  background: transparent !important;
  border: 0 !important;
  color: #646464 !important;
  font-weight: 400;
  font-family: Circular, "Helvetica Neue", Helvetica, Arial, sans-serif;
  text-align: center;
  font-size: 13px;
}
/* line 383, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-widget-content .ui-state-hover {
  border: 0;
  background: #002D42 !important;
  color: white !important;
}
/* line 389, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-widget-content {
  border: 0;
  border-radius: 0;
  padding-bottom: 40px;
}
/* line 395, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-state-default {
  padding: 8px;
}
/* line 399, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-datepicker-start_date {
  opacity: 1 !important;
}
/* line 402, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-datepicker-start_date .ui-state-default {
  border: 0;
  background: #002D42 !important;
  color: white !important;
}
/* line 409, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-datepicker-highlighted .ui-state-default {
  border: 0;
  background: rgba(40, 96, 144, 0.25) !important;
}
/* line 414, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-widget-content .ui-state-active {
  border: 0;
  background: #002D42 !important;
  color: white !important;
}
/* line 420, ../../../../sass/booking/_booking_engine_7.scss */
body span.ui-icon.ui-icon-circle-triangle-e, body span.ui-icon.ui-icon-circle-triangle-w {
  /* background: url(/static_1/images/mobile_img/renovation/flecha_der.png) no-repeat center;*/
  background: none;
  text-indent: 0;
  color: transparent;
  font-size: 0;
}
/* line 427, ../../../../sass/booking/_booking_engine_7.scss */
body span.ui-icon.ui-icon-circle-triangle-e:before, body span.ui-icon.ui-icon-circle-triangle-w:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  content: '\f105';
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-size: 20px;
  color: black;
}
/* line 438, ../../../../sass/booking/_booking_engine_7.scss */
body span.ui-icon.ui-icon-circle-triangle-w:before {
  content: '\f104';
}
/* line 443, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-datepicker .ui-datepicker-next, body .ui-datepicker .ui-datepicker-prev {
  background: none;
}
/* line 447, ../../../../sass/booking/_booking_engine_7.scss */
body .ui-datepicker-next.ui-state-hover, body .ui-datepicker-prev.ui-datepicker-prev-hover {
  border: 1px solid #636363;
}

/* line 454, ../../../../sass/booking/_booking_engine_7.scss */
.datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-e:before, .datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-w:before {
  display: none;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

/* line 465, ../../../../sass/booking/_booking_engine_7.scss */
.start_end_date_wrapper {
  width: 290px;
  height: 47px;
  display: inline-block;
  background: white url(/img/onac2/calendar_ico.png?v=1) no-repeat 4px center;
  font-weight: 300;
  font-size: 14px;
  padding: 15px 33px 12px;
  box-sizing: border-box;
  cursor: pointer;
  position: relative;
}

/* line 478, ../../../../sass/booking/_booking_engine_7.scss */
.dates_selector_personalized {
  display: inline-block;
  float: left;
  margin-right: 5px;
  position: relative;
}

/* line 485, ../../../../sass/booking/_booking_engine_7.scss */
.nights_number_wrapper_personalized {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 55px;
  background: #F3D132;
  line-height: 16px;
}
/* line 494, ../../../../sass/booking/_booking_engine_7.scss */
.nights_number_wrapper_personalized span.days_number_datepicker {
  display: block;
  text-align: center;
  color: white;
  font-size: 14px;
  font-weight: 500;
  margin-top: 8px;
}
/* line 503, ../../../../sass/booking/_booking_engine_7.scss */
.nights_number_wrapper_personalized span.night_label {
  color: white;
  font-size: 12px;
  text-align: center;
  display: block;
}
/* line 510, ../../../../sass/booking/_booking_engine_7.scss */
.nights_number_wrapper_personalized .ui-datepicker td {
  border: 0;
  padding: 1px 0;
}

/* line 516, ../../../../sass/booking/_booking_engine_7.scss */
.stay_selection {
  display: none !important;
}

/* line 520, ../../../../sass/booking/_booking_engine_7.scss */
label.dates_selector_label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/* line 526, ../../../../sass/booking/_booking_engine_7.scss */
.start_date_datepicker, .departure_datepicker {
  position: absolute;
  top: 100%;
}

/* line 531, ../../../../sass/booking/_booking_engine_7.scss */
.close_calendar {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40px;
  height: 40px;
  cursor: pointer;
  background: #535454;
}
/* line 540, ../../../../sass/booking/_booking_engine_7.scss */
.close_calendar:before {
  content: "\f00d";
  font-family: FontAwesome;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: white;
}

/* line 548, ../../../../sass/booking/_booking_engine_7.scss */
.entry_label_calendar, .departure_label_calendar {
  position: absolute;
  bottom: 0;
  font-family: 'Roboto', sans-serif;
  left: 0;
  font-weight: 300;
  color: white;
  font-size: 13px;
  background: #315390;
  width: 250px;
  line-height: 40px;
  padding-left: 18px;
  box-sizing: border-box;
}

/* line 2, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 {
  border-radius: 0;
}
/* line 4, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-skin {
  border-radius: 0;
}
/* line 7, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide > * {
  padding: 0 !important;
  background-color: transparent;
}
/* line 11, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide .calendar_popup_wrapper {
  background-color: transparent;
}
/* line 15, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .full_screen_engine .container_popup_booking {
  background-color: transparent !important;
  padding: 24px !important;
}
/* line 20, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  color: gray;
}
/* line 23, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading {
  width: 50px;
  height: 50px;
  margin: auto;
  position: absolute;
  left: 0;
  right: 0;
  top: 100px;
  bottom: 0;
}
/* line 10, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:before {
  content: '';
  width: 50px;
  height: 5px;
  background: #000;
  opacity: 0.3;
  position: absolute;
  top: 59px;
  left: 0;
  border-radius: 50%;
  animation: box_shadow .5s linear infinite;
}
/* line 22, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:after {
  content: '';
  width: 50px;
  height: 50px;
  background: white;
  animation: box_animate .5s linear infinite;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 3px;
}
@keyframes box_animate {
  17% {
    border-bottom-right-radius: 3px;
  }
  25% {
    transform: translateY(9px) rotate(22.5deg);
  }
  50% {
    transform: translateY(18px) scale(1, 0.9) rotate(45deg);
    border-bottom-right-radius: 40px;
  }
  75% {
    transform: translateY(9px) rotate(67.5deg);
  }
  100% {
    transform: translateY(0) rotate(90deg);
  }
}
@keyframes box_shadow {
  0%, 100% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.2, 1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader {
  padding: 60px 0 70px;
}
/* line 3, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot {
  width: 24px;
  height: 24px;
  margin: 0 3px;
  background: #000;
  border-radius: 100%;
  display: inline-block;
  animation: slide 1s infinite;
}
/* line 11, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(1) {
  animation-delay: 0.1s;
}
/* line 14, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(2) {
  animation-delay: 0.2s;
}
/* line 17, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(3) {
  animation-delay: 0.3s;
}
/* line 20, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(4) {
  animation-delay: 0.4s;
}
/* line 23, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(5) {
  animation-delay: 0.5s;
}
@-moz-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-webkit-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-o-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube {
  margin: 20px auto;
  width: 40px;
  height: 40px;
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}
/* line 7, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube {
  float: left;
  width: 50%;
  height: 50%;
  position: relative;
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}
/* line 15, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  -webkit-animation: sk-foldCubeAngle 2.4s infinite linear both;
  animation: sk-foldCubeAngle 2.4s infinite linear both;
  -webkit-transform-origin: 100% 100%;
  -ms-transform-origin: 100% 100%;
  transform-origin: 100% 100%;
}
/* line 30, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2 {
  -webkit-transform: scale(1.1) rotateZ(90deg);
  transform: scale(1.1) rotateZ(90deg);
}
/* line 34, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3 {
  -webkit-transform: scale(1.1) rotateZ(270deg);
  transform: scale(1.1) rotateZ(270deg);
}
/* line 38, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4 {
  -webkit-transform: scale(1.1) rotateZ(180deg);
  transform: scale(1.1) rotateZ(180deg);
}
/* line 43, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2:before {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
/* line 49, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3:before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
}
/* line 55, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4:before {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
}
@-webkit-keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
@keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
/* line 1, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots {
  padding: 40px 0;
  text-align: center;
  animation: dots-rotate 2s linear infinite;
}
/* line 5, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  content: '';
  animation: dots-margin 2s linear infinite;
}
/* line 9, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots span, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  display: inline-block;
  vertical-align: middle;
  width: 30px;
  height: 30px;
  background-color: white;
  border-radius: 50%;
  margin: 5px;
}
@keyframes dots-margin {
  0% {
    margin: 5px;
  }
  12% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  25% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  50% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  62% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  75% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  100% {
    margin: 5px;
  }
}
@keyframes dots-rotate {
  0% {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  25% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  50% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  75% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/* line 33, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_top_popup_booking {
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: lighter;
  color: gray;
}
/* line 41, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  font-weight: bold;
  text-transform: uppercase;
}
/* line 46, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2 .container_popup_booking {
  width: 555px;
  display: block;
  padding: 30px 0;
  box-sizing: border-box;
  margin: 7px;
  border: 1px solid #3483b2;
}

/* line 57, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine {
  width: auto !important;
  height: auto !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}
/* line 65, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-outer {
  background: transparent;
}
/* line 70, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine.custom_color_overlay .fancybox-skin {
  background: transparent;
}
/* line 75, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-skin {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}
/* line 84, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-inner {
  overflow: visible;
}
/* line 88, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 101, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  border: 0 !important;
}
/* line 105, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: lighter;
}
/* line 108, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking strong {
  font-weight: bold;
  text-decoration: underline;
  font-size: 14px;
}

/* line 116, ../../../../sass/booking/_booking_popupv2.scss */
.fancy-booking-search_v2 {
  border-radius: 0;
}
/* line 119, ../../../../sass/booking/_booking_popupv2.scss */
.fancy-booking-search_v2 .fancybox-skin {
  border-radius: 0;
}
/* line 123, ../../../../sass/booking/_booking_popupv2.scss */
.fancy-booking-search_v2 .gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}
/* line 129, ../../../../sass/booking/_booking_popupv2.scss */
.fancy-booking-search_v2 .gif_wrapper .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
/* line 142, ../../../../sass/booking/_booking_popupv2.scss */
.fancy-booking-search_v2 .description_top_popup_booking {
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: lighter;
  color: gray;
}
/* line 150, ../../../../sass/booking/_booking_popupv2.scss */
.fancy-booking-search_v2 .description_bottom_popup_booking {
  font-weight: bold;
  text-transform: uppercase;
  color: gray;
}
/* line 156, ../../../../sass/booking/_booking_popupv2.scss */
.fancy-booking-search_v2 .container_popup_booking {
  width: 555px;
  display: block;
  padding: 30px 0;
  box-sizing: border-box;
  margin: 7px;
  border: 1px solid #3483b2;
}

/* line 166, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine {
  width: auto !important;
  height: auto !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}
/* line 174, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-outer {
  background: transparent;
}
/* line 178, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-skin {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}
/* line 187, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-inner {
  overflow: visible;
}
/* line 191, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 204, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  border: 0 !important;
}
/* line 208, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: lighter;
}
/* line 211, ../../../../sass/booking/_booking_popupv2.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking strong {
  font-weight: bold;
  text-decoration: underline;
  font-size: 14px;
}

/* line 9, ../sass/_calendar_disco_styles.scss */
body .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-datepicker-start_date .ui-state-default,
body .datepicker_wrapper_element .datepicker_ext_inf_ed .ui-datepicker-start_date .ui-state-default, body .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-datepicker-start_date .ui-state-default,
body .datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-datepicker-start_date .ui-state-default, body .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-datepicker-start_date .ui-state-default,
body .datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-datepicker-start_date .ui-state-default {
  background: #11CCC7 !important;
}
/* line 11, ../sass/_calendar_disco_styles.scss */
body .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-datepicker-start_date .ui-state-default:before,
body .datepicker_wrapper_element .datepicker_ext_inf_ed .ui-datepicker-start_date .ui-state-default:before, body .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-datepicker-start_date .ui-state-default:before,
body .datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-datepicker-start_date .ui-state-default:before, body .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-datepicker-start_date .ui-state-default:before,
body .datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-datepicker-start_date .ui-state-default:before {
  border-left-color: #11CCC7 !important;
}
/* line 16, ../sass/_calendar_disco_styles.scss */
body .datepicker_wrapper_element .datepicker_ext_inf_sd .highlight,
body .datepicker_wrapper_element .datepicker_ext_inf_ed .highlight, body .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .highlight,
body .datepicker_wrapper_element_2 .datepicker_ext_inf_ed .highlight, body .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .highlight,
body .datepicker_wrapper_element_3 .datepicker_ext_inf_ed .highlight {
  background: rgba(0, 45, 66, 0.5) !important;
}
/* line 18, ../sass/_calendar_disco_styles.scss */
body .datepicker_wrapper_element .datepicker_ext_inf_sd .highlight a.ui-state-default,
body .datepicker_wrapper_element .datepicker_ext_inf_ed .highlight a.ui-state-default, body .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .highlight a.ui-state-default,
body .datepicker_wrapper_element_2 .datepicker_ext_inf_ed .highlight a.ui-state-default, body .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .highlight a.ui-state-default,
body .datepicker_wrapper_element_3 .datepicker_ext_inf_ed .highlight a.ui-state-default {
  color: white !important;
  background: transparent !important;
}
/* line 22, ../sass/_calendar_disco_styles.scss */
body .datepicker_wrapper_element .datepicker_ext_inf_sd .highlight.last-highlight-selection,
body .datepicker_wrapper_element .datepicker_ext_inf_ed .highlight.last-highlight-selection, body .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .highlight.last-highlight-selection,
body .datepicker_wrapper_element_2 .datepicker_ext_inf_ed .highlight.last-highlight-selection, body .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .highlight.last-highlight-selection,
body .datepicker_wrapper_element_3 .datepicker_ext_inf_ed .highlight.last-highlight-selection {
  background: rgba(0, 45, 66, 0.8) !important;
}
/* line 26, ../sass/_calendar_disco_styles.scss */
body .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-state-active,
body .datepicker_wrapper_element .datepicker_ext_inf_ed .ui-state-active, body .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-state-active,
body .datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-state-active, body .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-state-active,
body .datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-state-active {
  background: #002D42 !important;
}
/* line 29, ../sass/_calendar_disco_styles.scss */
body .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-state-hover,
body .datepicker_wrapper_element .datepicker_ext_inf_ed .ui-state-hover, body .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-state-hover,
body .datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-state-hover, body .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-state-hover,
body .datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-state-hover {
  background: #11CCC7 !important;
}
/* line 33, ../sass/_calendar_disco_styles.scss */
body .datepicker_wrapper_element .specific_month_selector, body .datepicker_wrapper_element .go_back_button, body .datepicker_wrapper_element_2 .specific_month_selector, body .datepicker_wrapper_element_2 .go_back_button, body .datepicker_wrapper_element_3 .specific_month_selector, body .datepicker_wrapper_element_3 .go_back_button {
  background: #11CCC7 !important;
}
/* line 38, ../sass/_calendar_disco_styles.scss */
body .booking_engine_modifications_wrapper.calendar_showed {
  overflow: visible;
  max-height: 780px;
}
/* line 44, ../sass/_calendar_disco_styles.scss */
body .header_wrapper_calendar_availability {
  width: 1040px;
}
/* line 47, ../sass/_calendar_disco_styles.scss */
body .header_wrapper_calendar_availability .graph_calendar_selector .calendar_button,
body .header_wrapper_calendar_availability .graph_calendar_selector .graph_button {
  background-color: #D7D7D7;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: black;
}
/* line 53, ../sass/_calendar_disco_styles.scss */
body .header_wrapper_calendar_availability .graph_calendar_selector .calendar_button .ico,
body .header_wrapper_calendar_availability .graph_calendar_selector .graph_button .ico {
  position: relative;
  width: 30px;
  height: 30px;
  background-size: 30px;
  background: transparent;
}
/* line 59, ../sass/_calendar_disco_styles.scss */
body .header_wrapper_calendar_availability .graph_calendar_selector .calendar_button .ico:before,
body .header_wrapper_calendar_availability .graph_calendar_selector .graph_button .ico:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-family: "icomoon", sans-serif;
  font-size: 30px;
  speak: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* line 75, ../sass/_calendar_disco_styles.scss */
body .header_wrapper_calendar_availability .graph_calendar_selector .calendar_button.active,
body .header_wrapper_calendar_availability .graph_calendar_selector .graph_button.active {
  background: white;
  color: black;
  position: relative;
  z-index: 1;
}
/* line 84, ../sass/_calendar_disco_styles.scss */
body .header_wrapper_calendar_availability .graph_calendar_selector .calendar_button .ico:before {
  content: "\e9a1";
}
/* line 91, ../sass/_calendar_disco_styles.scss */
body .header_wrapper_calendar_availability .graph_calendar_selector .graph_button .ico:before {
  content: "\e905";
}
/* line 97, ../sass/_calendar_disco_styles.scss */
body .header_wrapper_calendar_availability .popup_helper_wrapper {
  background-color: #002D42;
}
/* line 102, ../sass/_calendar_disco_styles.scss */
body #prices-calendar {
  width: 1040px;
  padding: 50px 0 0;
  background-color: white;
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.5);
  border-radius: 0 10px 10px 10px;
}
/* line 108, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .overlay_loading {
  background-color: white;
}
/* line 111, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .popup_month_selector {
  position: absolute;
  top: 65px;
  left: 0;
  right: 0;
  width: 100%;
  padding: 0;
}
/* line 118, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .popup_month_selector .month_select_element {
  background-color: #D7D7D7;
  background-image: url("/static_1/images/booking/angle-down.png");
  background-size: 12px;
  background-position: calc(100% - 10px) center;
  border-width: 0;
  text-transform: uppercase;
  font-size: 14px;
  margin-top: -83px;
  margin-left: 7px;
}
/* line 129, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .popup_month_selector .previous_month_selector,
body #prices-calendar .popup_month_selector .next_month_selector {
  position: relative;
  background: transparent;
}
/* line 133, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .popup_month_selector .previous_month_selector:before,
body #prices-calendar .popup_month_selector .next_month_selector:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 40px;
  color: #11CCC7;
}
/* line 146, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .popup_month_selector .previous_month_selector {
  float: left;
  margin-left: 10px;
}
/* line 150, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .popup_month_selector .next_month_selector {
  float: right;
  margin-right: 10px;
}
/* line 155, ../sass/_calendar_disco_styles.scss */
body #prices-calendar #selectorRooms {
  margin: 0 0 8px 620px;
}
/* line 157, ../sass/_calendar_disco_styles.scss */
body #prices-calendar #selectorRooms .text {
  color: black;
}
/* line 160, ../sass/_calendar_disco_styles.scss */
body #prices-calendar #selectorRooms #roomFilterInCalendar {
  background-color: #D7D7D7;
  background-image: url("/static_1/images/booking/angle-down.png");
  background-size: 12px;
  background-position: calc(100% - 10px) center;
  border-width: 0;
  text-transform: uppercase;
  font-size: 14px;
  width: 220px;
}
/* line 171, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section {
  padding: 0;
}
/* line 173, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars {
  max-height: 500px;
  padding: 0 15px;
}
/* line 176, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars.hide {
  display: none !important;
}
/* line 180, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar {
  text-align: center;
}
/* line 182, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar {
  width: 470px;
}
/* line 184, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar th {
  border-color: transparent;
  font-size: 30px;
  letter-spacing: 3px;
  padding: 10px 0;
}
/* line 189, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar th.day_label_element {
  border-color: white;
  font-size: 14px;
  padding: 5px 0;
  background-color: #4B4B4B;
  border-top: 4px solid white;
  border-bottom: 4px solid white;
}
/* line 198, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td {
  border-color: transparent;
}
/* line 202, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td.selected-cell-parent .day.available-day, body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td.selected-cell-parent .day.restricted-day {
  background-color: #D88A6F !important;
}
/* line 206, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td.selected-cell-parent .day-content.selected-cell {
  background-color: #D88A6F !important;
}
/* line 209, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td.selected-cell-parent .day-content.selected-cell .price:before {
  color: white;
}
/* line 213, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td.selected-cell-parent .day-content.selected-cell .restriction-message {
  color: white;
}
/* line 219, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td.selected-cell-parent.first-selection .day.available-day, body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td.selected-cell-parent.first-selection .day.restricted-day {
  background-color: #11CCC7 !important;
}
/* line 223, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td.selected-cell-parent.first-selection .selected-cell {
  background-color: #11CCC7 !important;
}
/* line 225, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td.selected-cell-parent.first-selection .selected-cell:before {
  border-color: transparent transparent transparent #11CCC7 !important;
  color: white;
}
/* line 233, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td.selected-cell-parent.end-selection .day.available-day, body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td.selected-cell-parent.end-selection .day.restricted-day {
  background-color: #11CCC7 !important;
}
/* line 237, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td.selected-cell-parent.end-selection .selected-cell {
  background-color: #11CCC7 !important;
}
/* line 239, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td.selected-cell-parent.end-selection .selected-cell:before {
  border-color: transparent #11CCC7 transparent transparent !important;
  color: white;
}
/* line 247, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td .day.available-day {
  background-color: #34A853;
}
/* line 250, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td .day.not-available-day {
  background-color: #EA4335;
}
/* line 253, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td .day.restricted-day {
  background-color: #FBBC05;
}
/* line 257, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td .day.restricted-day + .day-content .price:before {
  margin-top: 5px;
  margin-bottom: -5px;
}
/* line 265, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td .day-content {
  background-color: #fafafa;
}
/* line 268, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td .day-content.no-available .not-available-message {
  padding: 6px 10px;
}
/* line 273, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td .day-content {
  height: 40px;
}
/* line 276, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td .day-content.available .price {
  margin-top: -7px;
}
/* line 278, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .calendars .calendars_wrapper .calendar table.calendar td .day-content.available .price:before {
  display: block;
  color: #34A853;
  font-size: 16px;
}
/* line 291, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .graphs_fields_wrapper {
  background: transparent;
  margin-top: 10px;
}
/* line 295, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .legend {
  padding: 10px 0;
}
/* line 298, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .legend ul li {
  display: inline-block;
}
/* line 300, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .legend ul li div {
  display: inline-block;
  vertical-align: middle;
  width: 15px;
  height: 15px;
}
/* line 305, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .legend ul li div.available-day-box {
  background-color: #34A853;
  border-width: 0;
}
/* line 309, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .legend ul li div.restricted-day-box {
  background-color: #FBBC05;
  border-width: 0;
}
/* line 313, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .legend ul li div.not-available-day-box {
  background-color: #EA4335;
  border-width: 0;
}
/* line 318, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .legend ul li p {
  display: inline-block;
  vertical-align: middle;
  color: #666;
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 0;
}
/* line 331, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper .label_actual_selection,
body #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper .nights_number_wrapper,
body #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper .selection_price_wrapper {
  height: 75px;
}
/* line 335, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper .label_actual_selection .vertical_center,
body #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper .nights_number_wrapper .vertical_center,
body #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper .selection_price_wrapper .vertical_center {
  top: 47%;
}
/* line 339, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper .label_actual_selection {
  padding-top: 15px;
  padding-bottom: 15px;
  font-size: 18px;
  font-weight: lighter;
  font-family: "Cera Pro", sans-serif;
}
/* line 346, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper .nights_number_wrapper {
  position: relative;
  background-color: #999;
}
/* line 349, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper .nights_number_wrapper .nights_number, body #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper .nights_number_wrapper label {
  display: block;
  text-align: center;
  width: 100%;
}
/* line 354, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper .nights_number_wrapper .nights_number {
  margin-top: 25px;
  font-size: 30px;
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-weight: lighter;
}
/* line 361, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .buttons-section .actual_selection_info_wrapper .nights_number_wrapper label {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: 0;
  text-transform: uppercase;
  font-size: 14px;
  font-family: "Cera Pro", sans-serif;
  font-weight: normal;
}
/* line 374, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .buttons-section button.button {
  height: 75px;
  font-family: "Cera Pro", sans-serif;
  background-color: #11CCC7;
  color: white;
  font-size: 25px;
  position: relative;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
  display: inline-block;
  vertical-align: middle;
  font-weight: bold;
  text-transform: uppercase;
  border-radius: 0;
  z-index: 1;
}
/* line 392, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .buttons-section button.button:before {
  font-family: 'icomoon', sans-serif;
  position: absolute;
  top: 50%;
  right: 15px;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  color: white;
  font-size: 35px;
  z-index: 1;
}
/* line 406, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .buttons-section button.button:after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: auto;
  background: #11CCC7;
  width: 0;
  -webkit-transition: width 0.6s;
  -moz-transition: width 0.6s;
  -ms-transition: width 0.6s;
  -o-transition: width 0.6s;
  transition: width 0.6s;
  z-index: -1;
}
/* line 422, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .buttons-section button.button:hover {
  color: white;
}
/* line 424, ../sass/_calendar_disco_styles.scss */
body #prices-calendar .calendars-section .buttons-section button.button:hover:after {
  left: 0;
  width: 100%;
}
/* line 434, ../sass/_calendar_disco_styles.scss */
body .room_popup_individual_element .popup_carousel {
  overflow: hidden;
}

/* line 442, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .header_popup .calendar_button_head, div.calendar_popup_wrapper .header_popup .graph_button_head {
  background-color: #D7D7D7;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: black;
}
/* line 447, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .header_popup .calendar_button_head span, div.calendar_popup_wrapper .header_popup .graph_button_head span {
  position: relative;
  width: 30px;
  height: 30px;
  background-size: 30px;
  background: transparent;
}
/* line 453, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .header_popup .calendar_button_head span:before, div.calendar_popup_wrapper .header_popup .graph_button_head span:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-family: "icomoon", sans-serif;
  font-size: 30px;
  speak: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* line 469, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .header_popup .calendar_button_head.active, div.calendar_popup_wrapper .header_popup .graph_button_head.active {
  background: white;
  color: black;
  position: relative;
  z-index: 1;
}
/* line 478, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .header_popup .calendar_button_head span:before {
  content: "\e9a1";
}
/* line 486, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .header_popup .graph_button_head span:before {
  content: "\e905";
}
/* line 492, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .popup_month_selector {
  position: absolute;
  top: 90px;
  left: 0;
  right: 0;
  width: 100%;
  padding: 5px 10px !important;
  background: transparent !important;
}
/* line 500, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .popup_month_selector .month_select_element {
  display: none;
}
/* line 503, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .popup_month_selector .previous_month_selector,
div.calendar_popup_wrapper .popup_month_selector .next_month_selector {
  position: relative;
  background: transparent;
}
/* line 507, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .popup_month_selector .previous_month_selector:before,
div.calendar_popup_wrapper .popup_month_selector .next_month_selector:before {
  position: absolute;
  top: 50%;
  content: "\f0d9";
  font-family: "Font Awesome 5 Pro";
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 40px;
  color: white;
}
/* line 522, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .popup_month_selector .previous_month_selector {
  float: left;
  margin-left: 10px;
}
/* line 526, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .popup_month_selector .next_month_selector {
  float: right;
  margin-right: 10px;
}
/* line 531, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field {
  padding-top: 20px !important;
  background: white !important;
}
/* line 535, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar th {
  border-color: transparent white transparent;
  font-size: 14px;
  padding: 5px 0;
}
/* line 539, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar th.day_label_element {
  border-color: white;
  font-size: 12px;
  padding: 5px 0;
  background-color: #4B4B4B;
  border-top: 4px solid white;
  border-bottom: 4px solid white;
}
/* line 548, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td {
  border-color: transparent;
}
/* line 552, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td.selected-cell-parent:not(.first-selection):not(.end-selection) .day.available-day, div.calendar_popup_wrapper .calendar_field table.calendar td.selected-cell-parent:not(.first-selection):not(.end-selection) .day.restricted-day {
  background-color: #11CCC7 !important;
}
/* line 556, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td.selected-cell-parent:not(.first-selection):not(.end-selection) .day-content.selected-cell {
  background-color: #11CCC7 !important;
}
/* line 559, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td.selected-cell-parent .day-content.selected-cell {
  background-color: #11CCC7 !important;
}
/* line 562, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td.selected-cell-parent .day-content.selected-cell .price:before {
  color: white;
}
/* line 566, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td.selected-cell-parent .day-content.selected-cell .restriction-message {
  color: white;
}
/* line 572, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td.selected-cell-parent.first-selection .day.available-day, div.calendar_popup_wrapper .calendar_field table.calendar td.selected-cell-parent.first-selection .restricted-day.available-day {
  background-color: #11CCC7 !important;
}
/* line 577, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td.selected-cell-parent.first-selection .selected-cell {
  background-color: #002D42 !important;
}
/* line 579, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td.selected-cell-parent.first-selection .selected-cell:before {
  border-color: transparent transparent transparent #002D42 !important;
  color: white;
}
/* line 587, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td.selected-cell-parent.end-selection .day.available-day, div.calendar_popup_wrapper .calendar_field table.calendar td.selected-cell-parent.end-selection .day .restricted-day {
  background-color: #11CCC7 !important;
}
/* line 591, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td.selected-cell-parent.end-selection .selected-cell {
  background-color: #002D42 !important;
}
/* line 593, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td.selected-cell-parent.end-selection .selected-cell:before {
  border-color: transparent #002D42 transparent transparent !important;
  color: white;
}
/* line 601, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td .day.available-day {
  background-color: #34A853 !important;
}
/* line 604, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td .day.not-available-day {
  background-color: #EA4335 !important;
}
/* line 607, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td .day.restricted-day {
  background-color: #FBBC05 !important;
}
/* line 610, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td .day.restricted-day + .day-content .price {
  padding-top: 3px;
}
/* line 612, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td .day.restricted-day + .day-content .price:before {
  display: none;
}
/* line 619, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td .day-content {
  background-color: #fafafa;
}
/* line 622, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td .day-content.no-available .not-available-message {
  padding: 6px 10px;
}
/* line 627, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td .day-content {
  height: 40px;
}
/* line 630, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td .day-content.available .price {
  margin-top: -10px;
}
/* line 632, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .calendar_field table.calendar td .day-content.available .price:before {
  display: block;
  color: #34A853;
  font-size: 16px;
}
/* line 643, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper div.loading_popup_spinner {
  background: white;
}
/* line 645, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper div.loading_popup_spinner span {
  background-color: #DCDCDC;
  font-family: "Cera Pro", sans-serif;
  font-weight: 400;
  color: white;
  border-radius: 20px;
}
/* line 651, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper div.loading_popup_spinner span strong {
  font-weight: 700;
}
/* line 656, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .graphs_field_wrapper {
  background: white !important;
  padding-top: 10px !important;
}
/* line 660, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .legend_wrapper {
  background: white !important;
}
/* line 662, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .legend_wrapper div {
  color: #333 !important;
}
/* line 664, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .legend_wrapper div .legend_square {
  display: inline-block;
  vertical-align: middle;
  width: 15px;
  height: 15px;
}
/* line 670, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .legend_wrapper div.available_stay .legend_square {
  background-color: #34A853;
}
/* line 673, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .legend_wrapper div.min_stay .legend_square {
  background-color: #FBBC05;
}
/* line 676, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .legend_wrapper div.no_dispo .legend_square {
  background-color: #EA4335;
}
/* line 681, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .total_price_wrapper {
  background: white !important;
}
/* line 684, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .total_price_wrapper .total_price_label .nights_number_wrapper {
  background: #11CCC7 !important;
}
/* line 688, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .total_price_wrapper .booking_button_element {
  font-family: "Cera Pro", sans-serif;
  background-color: #11CCC7 !important;
  color: white;
  font-size: 25px;
  position: relative;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
  display: inline-block;
  vertical-align: middle;
  font-weight: bold;
  text-transform: uppercase;
  border-radius: 0 !important;
  z-index: 1;
}
/* line 705, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .total_price_wrapper .booking_button_element:before {
  font-family: 'icomoon', sans-serif;
  position: absolute;
  top: 50%;
  right: 15px;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  color: white;
  font-size: 35px;
  z-index: 1;
}
/* line 719, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .total_price_wrapper .booking_button_element:after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: auto;
  background: #11CCC7;
  width: 0;
  -webkit-transition: width 0.6s;
  -moz-transition: width 0.6s;
  -ms-transition: width 0.6s;
  -o-transition: width 0.6s;
  transition: width 0.6s;
  z-index: -1;
}
/* line 735, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .total_price_wrapper .booking_button_element:hover {
  color: white;
}
/* line 737, ../sass/_calendar_disco_styles.scss */
div.calendar_popup_wrapper .total_price_wrapper .booking_button_element:hover:after {
  left: 0;
  width: 100%;
}

/* line 746, ../sass/_calendar_disco_styles.scss */
.calendar_selection_fancybox .fancybox-close,
.calendar_selection_fancybox .fancybox-close-small {
  top: -20px !important;
}

/***** booking 0 ****/
/* line 753, ../sass/_calendar_disco_styles.scss */
.popup_helper_wrapper {
  background-color: #11CCC7 !important;
}

/* line 761, ../sass/_calendar_disco_styles.scss */
.calendar_popup_wrapper table.calendar td td .day.restricted-day + .day-content .price {
  top: 28px;
}
/* line 764, ../sass/_calendar_disco_styles.scss */
.calendar_popup_wrapper table.calendar td .day.restricted-day + .day-content .restriction-message {
  padding-top: 1px !important;
}
/* line 768, ../sass/_calendar_disco_styles.scss */
.calendar_popup_wrapper table.calendar td .day.restricted-day + .day-content .price {
  top: 31px !important;
}
/* line 776, ../sass/_calendar_disco_styles.scss */
.calendar_popup_wrapper .calendar_field table.calendar tr:nth-child(1) th {
  background-color: #11CCC7;
  padding: 10px 0;
  font-family: "Cera Pro", sans-serif;
  font-size: 25px;
}
/* line 782, ../sass/_calendar_disco_styles.scss */
.calendar_popup_wrapper .calendar_field table.calendar td {
  font-family: "Cera Pro", sans-serif;
}
/* line 786, ../sass/_calendar_disco_styles.scss */
.calendar_popup_wrapper .calendar_field table.calendar td .day-content.available .price {
  top: 26px;
}
/* line 791, ../sass/_calendar_disco_styles.scss */
.calendar_popup_wrapper .calendar_field table.calendar td .day-content.no-available .not-available-message {
  padding: 10px 0;
}
/* line 798, ../sass/_calendar_disco_styles.scss */
.calendar_popup_wrapper .calendar_field table.calendar th {
  border-color: white;
  font-size: 12px;
  padding: 5px 0;
  background-color: #4B4B4B;
  border-top: 4px solid white;
  border-bottom: 4px solid white;
  font-family: "Cera Pro", sans-serif;
  letter-spacing: 3px;
}

/* line 818, ../sass/_calendar_disco_styles.scss */
#prices-calendar .calendars-section .graphs_fields_wrapper .graphs_field .day_element_option:not(.disabled_day) .graph_block .graph_element {
  background-color: #11CCC7 !important;
}

/* line 4, ../sass/_booking_engine.scss */
.inner_section_hotel:not(.hacienda-alamo, .ona-mar-menor-aptos) #full_wrapper_booking .booking_form .destination_wrapper {
  display: none !important;
}
/* line 9, ../sass/_booking_engine.scss */
.inner_section_hotel:not(.hacienda-alamo, .ona-mar-menor-aptos) #full_wrapper_booking .booking_form .wrapper_booking_button .promocode_wrapper input.promocode_input:lang(de), .inner_section_hotel:not(.hacienda-alamo, .ona-mar-menor-aptos) #full_wrapper_booking .booking_form .wrapper_booking_button .promocode_wrapper input.promocode_input:lang(en), .inner_section_hotel:not(.hacienda-alamo, .ona-mar-menor-aptos) #full_wrapper_booking .booking_form .wrapper_booking_button .promocode_wrapper input.promocode_input:lang(fr) {
  width: 190px;
}

/* line 19, ../sass/_booking_engine.scss */
#full_wrapper_booking {
  position: absolute;
  bottom: 80px;
  left: 0;
  right: 0;
  margin: auto;
  background: transparent;
  z-index: 1000;
  min-width: 1140px;
}
/* line 30, ../sass/_booking_engine.scss */
.black_friday #full_wrapper_booking .wrapper_booking_button .submit_button {
  background: #000000 !important;
  color: white !important;
}
/* line 36, ../sass/_booking_engine.scss */
#full_wrapper_booking.with_search {
  background: white;
  bottom: 0;
}
/* line 40, ../sass/_booking_engine.scss */
#full_wrapper_booking.with_search .boking_widget_inline {
  display: none;
}
/* line 46, ../sass/_booking_engine.scss */
#full_wrapper_booking:not(.with_search) .widget_search_wrapper {
  display: none;
}
/* line 51, ../sass/_booking_engine.scss */
#full_wrapper_booking.fixed_old {
  position: fixed;
  bottom: 0;
  background: white;
  z-index: 1050;
}
/* line 58, ../sass/_booking_engine.scss */
#full_wrapper_booking.fixed_old.with_search .widget_search_wrapper {
  display: none;
}
/* line 62, ../sass/_booking_engine.scss */
#full_wrapper_booking.fixed_old.with_search .boking_widget_inline {
  display: block;
}
/* line 68, ../sass/_booking_engine.scss */
#full_wrapper_booking.fixed {
  z-index: 1050;
}
/* line 71, ../sass/_booking_engine.scss */
#full_wrapper_booking.fixed .booking_steps, #full_wrapper_booking.fixed .banner_ticks_widget {
  display: block;
}
/* line 75, ../sass/_booking_engine.scss */
#full_wrapper_booking.fixed #full-booking-engine-html-7 {
  position: fixed;
  top: 110vh;
}
/* line 81, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps, #full_wrapper_booking .banner_ticks_widget {
  display: none;
}
/* line 85, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps {
  position: fixed;
  left: 0;
  right: 0;
  width: 1100px;
  margin: auto;
  z-index: 1002;
  top: 0;
  border-top: 30px solid white;
  text-align: center;
}
/* line 96, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps.hidden {
  display: none !important;
}
/* line 100, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps .step {
  display: inline-block;
  padding: 10px 20px;
  width: 330px;
  text-transform: uppercase;
  font-size: 12px;
  font-weight: bold;
  letter-spacing: 0.4px;
  background: #ECFBFB;
  color: rgba(0, 45, 66, 0.4);
}
/* line 111, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps .step:lang(fr) {
  width: 265px;
  white-space: nowrap;
}
/* line 116, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps .step.current_step {
  background: #002D42;
  color: white;
}
/* line 121, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps .step.current_step.step_1:after, #full_wrapper_booking .booking_steps .step.current_step.step_2:after {
  border-color: transparent transparent transparent #002D42;
}
/* line 127, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps .step.step_1 {
  position: relative;
  z-index: 2;
}
/* line 132, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps .step.step_2 {
  position: relative;
  z-index: 1;
}
/* line 137, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps .step.step_2, #full_wrapper_booking .booking_steps .step.step_3 {
  padding-left: 30px;
}
/* line 141, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps .step.step_1, #full_wrapper_booking .booking_steps .step.step_2 {
  padding-right: 0;
}
/* line 144, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps .step.step_1 span, #full_wrapper_booking .booking_steps .step.step_2 span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* line 150, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps .step.step_1:before, #full_wrapper_booking .booking_steps .step.step_1:after, #full_wrapper_booking .booking_steps .step.step_2:before, #full_wrapper_booking .booking_steps .step.step_2:after {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: -33px;
  content: '';
  border-width: 17px;
  border-style: solid;
  border-color: transparent transparent transparent #ECFBFB;
}
/* line 159, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps .step.step_1:before, #full_wrapper_booking .booking_steps .step.step_2:before {
  border-width: 19px;
  right: -37px;
  border-color: transparent transparent transparent white;
}
/* line 168, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_steps .step:lang(de):first-of-type {
  white-space: nowrap;
}
/* line 174, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget {
  background: #002D42;
  color: white;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1002;
  text-align: center;
}
/* line 184, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #002D42;
  z-index: 1;
}
/* line 191, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .tick {
  position: relative;
  z-index: 2;
  display: inline-block;
  vertical-align: middle;
  margin-right: 70px;
  padding: 20px 0;
}
/* line 199, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .tick img {
  width: 30px;
  margin-right: 20px;
  display: inline-block;
  vertical-align: middle;
}
/* line 206, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .tick span {
  display: inline-block;
  vertical-align: middle;
  text-transform: uppercase;
  font-size: 20px;
}
/* line 214, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info {
  position: absolute;
  z-index: -1;
  padding: 0 30px;
  bottom: 70px;
  width: 320px;
  border-radius: 15px 15px 0 0;
  max-height: 0;
  text-align: center;
  background: #002D42;
  right: 40px;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 227, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info.showed {
  max-height: 700px;
  padding: 30px;
}
/* line 231, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info.showed .show {
  top: 5px;
}
/* line 234, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info.showed .show i:before {
  display: block;
  -webkit-transform: rotateX(180deg);
  -moz-transform: rotateX(180deg);
  -ms-transform: rotateX(180deg);
  -o-transform: rotateX(180deg);
  transform: rotateX(180deg);
}
/* line 245, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info .show {
  position: absolute;
  top: -38px;
  right: 0;
  width: 40px;
  height: 40px;
  cursor: pointer;
  border-radius: 10px 10px 0 0;
  background: #002D42;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 256, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info .show i {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 34px;
}
/* line 260, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info .show i:before {
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 266, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info .tick_header {
  text-align: left;
  font-size: 20px;
  padding: 0 0 30px;
}
/* line 271, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info .tick_header b {
  display: block;
}
/* line 276, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info .tick_extra {
  text-align: left;
  padding: 0 0 30px;
}
/* line 280, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info .tick_extra img {
  display: inline-block;
  vertical-align: middle;
  width: 40px;
}
/* line 286, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info .tick_extra .title {
  display: inline-block;
  vertical-align: middle;
  width: calc(100% - 45px);
}
/* line 292, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info .tick_extra .desc {
  display: block;
  font-size: 12px;
  padding: 10px 0;
}
/* line 299, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info .btn {
  background: white;
  color: #002D42;
  font-size: 14px;
  font-weight: bold;
  padding: 5px 15px;
}
/* line 306, ../sass/_booking_engine.scss */
#full_wrapper_booking .banner_ticks_widget .extra_info .btn i {
  margin-right: 5px;
}
/* line 313, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-7 {
  width: auto;
  margin: auto;
  text-align: center;
}
/* line 319, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form {
  text-align: left;
  display: inline-block;
  background: white;
  border-radius: 10px;
}
/* line 325, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .destination_wrapper {
  position: relative;
  z-index: 10;
  display: inline-block;
  vertical-align: middle;
  background: transparent !important;
  border-bottom-width: 0;
  padding: 9px 0;
  float: left;
  width: 270px;
}
/* line 338, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .destination_wrapper.hotel_is_selected .destination {
  font-size: 15px !important;
}
/* line 343, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .destination_wrapper:after {
  content: '';
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  height: 80%;
  width: 1px;
  right: 0;
  background: #002D42;
}
/* line 352, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .destination_wrapper.current_step {
  background: #CFF5F4 !important;
}
/* line 357, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .destination_wrapper .destination_fieldo input.destination {
  font-family: "Cera Pro", sans-serif;
  font-size: 25px;
  font-weight: normal;
  line-height: 25px;
  font-size: 21px;
  padding: 5px 10px;
  outline: none;
  cursor: pointer;
  text-align: center;
  width: 100%;
  background: transparent;
  box-sizing: border-box;
}
/* line 368, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .destination_wrapper .destination_fieldo input.destination::-webkit-input-placeholder {
  color: #002D42;
}
/* line 372, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .destination_wrapper .destination_fieldo input.destination::-moz-placeholder {
  color: #002D42;
}
/* line 376, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .destination_wrapper .destination_fieldo input.destination:-ms-input-placeholder {
  color: #002D42;
}
/* line 380, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .destination_wrapper .destination_fieldo input.destination:-moz-placeholder {
  color: #002D42;
}
/* line 387, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector {
  display: none;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 500;
  background: white;
  padding: 40px calc((100% - 800px) / 2);
}
/* line 398, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .close {
  position: absolute;
  top: 30px;
  left: 100px;
  width: 40px;
  height: 40px;
  cursor: pointer;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 407, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .close.active {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
/* line 415, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .close:before, #full_wrapper_booking .booking_form .hotel_selector .close:after {
  background: #002D42;
}
/* line 420, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .close:hover:before, #full_wrapper_booking .booking_form .hotel_selector .close:hover:after {
  background: #11CCC7;
}
/* line 426, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .tabs {
  margin-bottom: 20px;
}
/* line 429, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .tabs .tab {
  font-size: 12px;
  font-weight: bold;
  color: #002D42;
  text-align: center;
  text-transform: uppercase;
  min-width: 100px;
  padding: 10px 20px;
  margin-right: 5px;
}
/* line 439, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .tabs .tab:before {
  background: #11CCC7;
}
/* line 443, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .tabs .tab:hover, #full_wrapper_booking .booking_form .hotel_selector .tabs .tab.selected {
  padding: 5px 20px 15px;
}
/* line 449, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .hotel_selector_search {
  position: relative;
  border: 1px solid #11CCC7;
  border-radius: 30px;
  margin-bottom: 30px;
}
/* line 455, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .hotel_selector_search i {
  position: absolute;
  top: 3px;
  left: 5px;
  padding: 5px;
  color: #11CCC7;
  font-size: 14px;
}
/* line 464, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .hotel_selector_search input {
  width: 100%;
  padding: 5px 5px 5px 45px;
  background: transparent;
  border-width: 0;
  color: #11CCC7;
  font-size: 14px;
  font-weight: 500;
}
/* line 473, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .hotel_selector_search input::-webkit-input-placeholder {
  color: #11CCC7;
}
/* line 477, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .hotel_selector_search input::-moz-placeholder {
  color: #11CCC7;
}
/* line 481, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .hotel_selector_search input:-ms-input-placeholder {
  color: #11CCC7;
}
/* line 485, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .hotel_selector_search input:-moz-placeholder {
  color: #11CCC7;
}
/* line 491, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner, #full_wrapper_booking .booking_form .hotel_selector .hotel_selector_inner, #full_wrapper_booking .booking_form .hotel_selector .map_selector_inner {
  max-height: calc(100vh - 350px);
  width: 600px;
  overflow: auto;
  display: none;
  opacity: 0;
  -webkit-transition: opacity 0.6s;
  -moz-transition: opacity 0.6s;
  -ms-transition: opacity 0.6s;
  -o-transition: opacity 0.6s;
  transition: opacity 0.6s;
}
/* line 499, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner.active, #full_wrapper_booking .booking_form .hotel_selector .hotel_selector_inner.active, #full_wrapper_booking .booking_form .hotel_selector .map_selector_inner.active {
  display: block;
  opacity: 1;
}
/* line 506, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner .destiny {
  border-bottom: 1px solid #002D42;
}
/* line 511, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner .destiny.active i.fa-plus:before {
  content: '\f068';
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
/* line 521, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner .destiny.active .countries {
  height: auto;
  max-height: 900px;
  margin-bottom: 10px;
  -webkit-transition: all 0.9s ease-in;
  -moz-transition: all 0.9s ease-in;
  -ms-transition: all 0.9s ease-in;
  -o-transition: all 0.9s ease-in;
  transition: all 0.9s ease-in;
}
/* line 529, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner .destiny li {
  padding: 5px 0 5px 20px;
  cursor: pointer;
}
/* line 533, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner .destiny li.hide {
  display: none !important;
}
/* line 537, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner .destiny li:hover {
  color: #11CCC7;
}
/* line 542, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner .destiny .booking_0_hotel_selection {
  cursor: pointer;
  font-weight: bold;
}
/* line 547, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner .destiny a {
  position: relative;
  display: block;
  font-weight: bold;
  font-family: "Cera Pro", sans-serif;
  font-size: 20px;
  font-weight: bold;
  line-height: 20px;
  padding: 5px 0;
  color: #002D42;
}
/* line 555, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner .destiny a span {
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-size: 16px;
  color: #002D42;
  font-size: 14px;
  font-weight: 500;
  color: #11CCC7;
}
/* line 560, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner .destiny a i {
  position: absolute;
  top: 50%;
  right: 5px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 570, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner .destiny a i:before {
  display: block;
  -webkit-transition: all 0.6s ease-in;
  -moz-transition: all 0.6s ease-in;
  -ms-transition: all 0.6s ease-in;
  -o-transition: all 0.6s ease-in;
  transition: all 0.6s ease-in;
}
/* line 577, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner .destiny .countries {
  max-height: 0;
  overflow: hidden;
  -webkit-transition: all 0.6s ease-out;
  -moz-transition: all 0.6s ease-out;
  -ms-transition: all 0.6s ease-out;
  -o-transition: all 0.6s ease-out;
  transition: all 0.6s ease-out;
}
/* line 582, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .destiny_selector_inner .destiny .countries a {
  padding: 0;
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-size: 16px;
  color: #002D42;
  font-size: 14px;
  font-weight: bold;
}
/* line 592, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .hotel_selector_inner li {
  cursor: pointer;
}
/* line 595, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .hotel_selector_inner li.hide {
  display: none !important;
}
/* line 599, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .hotel_selector_inner li:hover {
  color: #11CCC7;
}
/* line 605, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .map_selector_inner {
  position: relative;
  height: 415px;
}
/* line 609, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .map_selector_inner .map_selector_wrapper {
  height: 415px;
}
/* line 612, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .map_selector_inner .map_selector_wrapper .ol-attribution.ol-uncollapsible {
  display: none !important;
}
/* line 617, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .map_selector_inner .hotel_card {
  position: absolute;
  top: 20px;
  right: 20px;
  background: white;
  padding: 20px;
  width: 250px;
  box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.2);
}
/* line 626, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .map_selector_inner .hotel_card .bottom {
  display: table;
  width: 100%;
  margin-top: 10px;
}
/* line 631, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .map_selector_inner .hotel_card .bottom .price {
  display: inline-block;
  text-transform: uppercase;
}
/* line 634, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .map_selector_inner .hotel_card .bottom .price span {
  font-weight: lighter;
  font-size: 12px;
}
/* line 642, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .map_selector_inner .hotel_card .bottom a {
  float: right;
  color: white;
}
/* line 646, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .hotel_selector .map_selector_inner .hotel_card .bottom a:hover {
  background: #002D42;
}
/* line 655, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .dates_selector_personalized {
  position: relative;
  z-index: 10;
  vertical-align: middle;
  width: 320px;
  height: auto;
  float: left;
  padding: 7px 0;
  margin: 0;
}
/* line 665, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .dates_selector_personalized.current_step {
  background: #CFF5F4 !important;
}
/* line 669, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .dates_selector_personalized label {
  display: none;
}
/* line 673, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper {
  display: table;
  font-size: 0;
  padding: 7px 21px 7px 0;
  height: auto;
  width: 100%;
  position: relative;
  background: transparent !important;
}
/* line 682, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper:before, #full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper:after {
  content: '';
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
}
/* line 691, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper:before {
  width: 40px;
  height: 1px;
  margin-left: 5px;
  background: #002D42;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 703, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper:after {
  width: 5px;
  height: 5px;
  margin-left: 21px;
  border: 1px solid transparent;
  border-color: #002D42 #002D42 transparent transparent;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 716, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper .start_date_personalized {
  float: left;
}
/* line 720, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper .end_date_personalized {
  float: right;
}
/* line 724, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper .start_date_personalized, #full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper .end_date_personalized {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  padding: 0 21px;
  font-family: "Cera Pro", sans-serif;
  font-size: 25px;
  font-weight: normal;
  line-height: 25px;
}
/* line 731, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper .start_date_personalized:before, #full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper .end_date_personalized:before {
  content: '\f106';
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
  position: absolute;
  top: 50%;
  right: -5px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  font-size: 80%;
  color: #11CCC7;
}
/* line 747, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper .start_date_personalized span, #full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper .end_date_personalized span {
  display: inline-block;
  padding: 0 5px;
  text-transform: lowercase;
}
/* line 754, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper .nights_number_wrapper_personalized {
  display: none;
}
/* line 760, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .rooms_number_wrapper, #full_wrapper_booking .booking_form .room_list_wrapper {
  display: none;
}
/* line 764, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .guest_selector {
  position: relative;
  z-index: 50;
  display: inline-block;
  vertical-align: middle;
  padding: 6px 0 7px;
  cursor: pointer;
  font-weight: 500;
}
/* line 773, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .guest_selector:before, #full_wrapper_booking .booking_form .guest_selector:after {
  content: '';
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  height: 80%;
  width: 1px;
  background: #002D42;
}
/* line 781, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .guest_selector:before {
  left: 0;
}
/* line 785, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .guest_selector:after {
  right: 0;
}
/* line 789, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .guest_selector.current_step {
  background: #CFF5F4 !important;
}
/* line 793, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .guest_selector label {
  display: inline-block;
  vertical-align: middle;
  font-weight: normal;
  padding: 10px 21px;
  font-size: 11px;
  cursor: pointer;
}
/* line 802, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .guest_selector .placeholder_text {
  display: inline-block;
  vertical-align: middle;
  font-size: 20px;
  line-height: 37px;
  padding-right: 21px;
  font-weight: normal;
  cursor: pointer;
}
/* line 811, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .guest_selector .placeholder_text .guest_adults {
  font-size: 25px;
  cursor: pointer;
}
/* line 817, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .guest_selector .button {
  display: none;
}
/* line 822, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: -1;
  width: 100%;
  background: white;
  float: none;
  display: none;
  vertical-align: middle;
}
/* line 835, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .close_guest_selector {
  position: absolute;
  top: 30px;
  left: 100px;
  width: 40px;
  height: 40px;
  border-width: 0;
  border-radius: 0;
  left: 50px;
  top: 20px;
  cursor: pointer;
}
/* line 847, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .close_guest_selector:before, #full_wrapper_booking .booking_form .room_list_wrapper .close_guest_selector:after {
  background: #002D42;
}
/* line 852, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: auto;
  text-align: center;
  white-space: nowrap;
  display: flex;
  flex-flow: column;
  align-items: center;
  max-height: 75%;
  overflow-y: scroll;
  min-width: 650px;
}
/* line 864, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}
/* line 869, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list::-webkit-scrollbar-thumb {
  background: #70E0DD;
  border-radius: 15px;
}
/* line 874, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list::-webkit-scrollbar-thumb:hover {
  background: #002D42;
}
/* line 878, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}
/* line 883, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .add_room {
  display: inline-block;
  vertical-align: middle;
  position: absolute;
  right: 5px;
  width: 30px;
  cursor: pointer;
  height: 30px;
  margin: 5px -50px 0 10px;
  border: 1px solid #12CCC7;
  border-radius: 50%;
}
/* line 895, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .add_room i {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 16px;
  font-weight: 300;
  color: #12CCC7;
}
/* line 903, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room_info_wrapper {
  display: table;
  width: 100%;
  height: auto;
  font-weight: 600;
  position: relative;
  color: #002D42;
  text-transform: uppercase;
}
/* line 912, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room_info_wrapper .hotel_name_rooms.with_name {
  background: rgba(17, 204, 199, 0.1);
  font-size: 18px;
  padding: 10px;
  line-height: 25px;
  margin-bottom: 5px;
}
/* line 920, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room_info_wrapper .dates_wrapper {
  width: 75%;
  display: inline-block;
  background: rgba(17, 204, 199, 0.1);
  line-height: 25px;
  font-size: 18px;
  padding: 10px 40px;
  margin-bottom: 50px;
}
/* line 931, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .wrapper_booking_button_guest {
  display: block;
  width: 280px;
  margin: 50px auto 0;
}
/* line 936, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .wrapper_booking_button_guest .promocode_wrapper, #full_wrapper_booking .booking_form .room_list_wrapper .room_list .wrapper_booking_button_guest .submit_button {
  display: block;
  margin: auto;
  width: 100%;
  padding: 0;
  margin: 5px 0;
}
/* line 944, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .wrapper_booking_button_guest .promocode_wrapper {
  padding-bottom: 15px;
}
/* line 947, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .wrapper_booking_button_guest .promocode_wrapper .promocode_input {
  width: 65%;
  margin: auto;
}
/* line 951, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .wrapper_booking_button_guest .promocode_wrapper .promocode_input::-webkit-input-placeholder {
  color: #11CCC7;
}
/* line 955, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .wrapper_booking_button_guest .promocode_wrapper .promocode_input::-moz-placeholder {
  color: #11CCC7;
}
/* line 959, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .wrapper_booking_button_guest .promocode_wrapper .promocode_input:-ms-input-placeholder {
  color: #11CCC7;
}
/* line 963, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .wrapper_booking_button_guest .promocode_wrapper .promocode_input:-moz-placeholder {
  color: #11CCC7;
}
/* line 969, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .wrapper_booking_button_guest .submit_button {
  width: 70%;
  height: 35px;
  line-height: 35px;
  font-size: 18px;
  border-radius: 30px;
  margin: auto;
  padding: 0;
}
/* line 978, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .wrapper_booking_button_guest .submit_button:lang(fr) {
  width: 80%;
}
/* line 985, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_2 .room1 {
  border-color: #11CCC7;
}
/* line 988, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_2 .room1 .add_room {
  display: none !important;
}
/* line 992, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_2 .room1 .room_title,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_2 .room1 .remove_room,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_2 .room1 label,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_2 .room1 .room_selector .selectric .label,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_2 .room1 .room_selector .selectric .label:before,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_2 .room1 .room_selector .selectric .button:before {
  color: #11CCC7;
}
/* line 1001, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_2 .room1 .children_selector {
  border-left-color: #11CCC7;
}
/* line 1008, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room1, #full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room2 {
  border-color: #11CCC7;
}
/* line 1011, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room1 .room_title,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room1 .remove_room,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room1 label,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room1 .room_selector .selectric .label,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room1 .room_selector .selectric .label:before,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room1 .room_selector .selectric .button:before, #full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room2 .room_title,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room2 .remove_room,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room2 label,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room2 .room_selector .selectric .label,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room2 .room_selector .selectric .label:before,
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room2 .room_selector .selectric .button:before {
  color: #11CCC7;
}
/* line 1020, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room1 .children_selector, #full_wrapper_booking .booking_form .room_list_wrapper .room_list.size_3 .room2 .children_selector {
  border-left-color: #11CCC7;
}
/* line 1026, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room {
  position: relative;
  display: block;
  vertical-align: middle;
  height: auto;
  width: 280px;
  padding: 8px 0;
  text-align: center;
  border: 1px solid #12CCC7;
  border-radius: 10px;
  margin: 30px 0 20px;
  overflow: initial !important;
}
/* line 1039, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room:lang(de) {
  width: 310px;
}
/* line 1043, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room .room_title {
  position: absolute;
  top: -20px;
  margin: 0 15px;
  display: block;
  font-size: 12px;
  font-weight: 500;
  text-align: left;
  text-transform: uppercase;
}
/* line 1054, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room .remove_room {
  position: absolute;
  top: -30px;
  cursor: pointer;
  right: 15px;
}
/* line 1061, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room label {
  display: inline-block;
  vertical-align: middle;
  text-transform: capitalize;
  font-size: 11px;
  text-align: left;
}
/* line 1068, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room label .range-age {
  display: block;
  font-size: 10px;
}
/* line 1074, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room .room_selector {
  display: inline-block;
  vertical-align: middle;
}
/* line 1078, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room .room_selector .selectricItems {
  display: none !important;
}
/* line 1082, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room .room_selector .selectric {
  height: 30px;
  margin: 0 0 0 5px;
}
/* line 1086, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room .room_selector .selectric .label {
  text-align: center;
  margin-left: -2px;
  color: #002D42;
  font-family: "Cera Pro", sans-serif;
  font-size: 25px;
  font-weight: normal;
  line-height: 25px;
  font-size: 28px;
  line-height: 30px;
  font-weight: 500;
}
/* line 1095, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room .room_selector .selectric .label:before {
  content: '\f067';
  position: absolute;
  top: 50%;
  right: 5px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  font-family: "Font Awesome 5 Pro";
  font-weight: bold;
  font-size: 14px;
  color: #002D42;
}
/* line 1112, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room .room_selector .selectric .button {
  position: absolute;
  top: 0;
  text-indent: 0;
  height: auto;
  color: white;
  margin: 0;
  font-size: 0;
  left: -2px;
  line-height: 24px;
  background: transparent !important;
  text-shadow: 0 0 0 transparent !important;
}
/* line 1125, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room .room_selector .selectric .button:before {
  content: '\f068';
  position: absolute;
  top: 50%;
  right: 15px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  display: inline-block;
  vertical-align: middle;
  font-family: "Font Awesome 5 Pro";
  font-weight: bold;
  font-size: 14px;
  line-height: 24px;
  color: #002D42;
}
/* line 1147, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room .adults_selector, #full_wrapper_booking .booking_form .room_list_wrapper .room_list .room .children_selector {
  height: auto;
  padding: 5px 0;
  border-left-width: 0;
  border-right: 1px solid #12CCC7;
}
/* line 1154, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room .children_selector {
  border-left: 1px solid #12CCC7;
  border-left-width: 0;
}
/* line 1164, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper {
  display: none;
  margin-bottom: 10px;
}
/* line 1168, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .kids_ages_label {
  display: none;
}
/* line 1172, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .kids_age_selection {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
/* line 1177, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .kids_age_selection .kid_age_element_wrapper, #full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .kids_age_selection .kid_age_element_wrapper.hide {
  display: none;
}
/* line 1182, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .kids_age_selection .kid_age_element_wrapper select {
  padding: 5px;
  border-radius: 0;
}
/* line 1190, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide {
  display: inline-flex;
  flex-flow: column;
  width: 168px;
}
/* line 1195, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide .slide_content {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 14px;
}
/* line 1201, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide .slide_content .max-value {
  font-weight: bold;
}
/* line 1206, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide input[type="range"].slider_age {
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  display: block;
  background: lightgray;
  width: 168px;
  height: 4px;
  background-image: -webkit-gradient(linear, 0% 0%, 100% 0%, from(#002d42), from(lightgray));
  background-image: -moz-linear-gradient(left center, #12CCC7 0%, #12CCC7 0%, lightgray 100%, lightgray 100%);
}
/* line 1219, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide input[type="range"].slider_age::-webkit-slider-thumb {
  appearance: none;
  -webkit-appearance: none;
  border: 1px solid #12CCC7;
  height: 19px;
  width: 19px;
  border-radius: 50%;
  background: #12CCC7;
  cursor: pointer;
  margin-top: 0;
}
/* line 1231, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide input[type="range"].slider_age::-moz-range-thumb, #full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide input[type="range"].slider_age::-ms-thumb {
  appearance: none;
  -webkit-appearance: none;
  border: 1px solid #12CCC7;
  height: 19px;
  width: 19px;
  border-radius: 50%;
  background: #12CCC7;
  cursor: pointer;
}
/* line 1242, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide input[type="range"].slider_age::-webkit-slider-runnable-track, #full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide input[type="range"].slider_age::-moz-range-track, #full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide input[type="range"].slider_age::-ms-track {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 210px;
  height: 4px;
  cursor: pointer;
  border: none;
  background: lightgray;
}
/* line 1253, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide input[type="range"].slider_age:focus {
  outline: none;
}
/* line 1256, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide input[type="range"].slider_age:focus::-webkit-slider-thumb {
  margin-top: -7px;
}
/* line 1260, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide input[type="range"].slider_age:focus::-webkit-slider-runnable-track {
  height: 4px;
}
/* line 1265, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide input[type="range"].slider_age::-ms-track {
  width: 100%;
  cursor: pointer;
  background: transparent;
  border-color: transparent;
  color: transparent;
}
/* line 1274, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide.hide {
  display: none;
}
/* line 1279, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper .slides_wrapper .input_slide.show:not(:first-of-type) {
  margin-left: 40px;
}
/* line 1286, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper.show {
  display: block;
}
/* line 1289, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper.show .kids_ages_label, #full_wrapper_booking .booking_form .room_list_wrapper .room_list .full_ages_wrapper.show .kid_age_element_wrapper.show {
  display: block;
}
/* line 1295, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room.room_with_babies {
  width: 500px;
}
/* line 1298, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room.room_with_babies .adults_selector, #full_wrapper_booking .booking_form .room_list_wrapper .room_list .room.room_with_babies .children_selector, #full_wrapper_booking .booking_form .room_list_wrapper .room_list .room.room_with_babies .babies_selector {
  width: calc(100% / 3) !important;
  display: inline-block;
  float: none;
}
/* line 1307, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .wrapper_booking_button, #full_wrapper_booking .booking_form .wrapper_booking_button_guest {
  position: relative;
  z-index: 10;
  display: inline-block;
  vertical-align: middle;
  float: none;
  text-align: right;
}
/* line 1315, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .wrapper_booking_button .promocode_wrapper, #full_wrapper_booking .booking_form .wrapper_booking_button_guest .promocode_wrapper {
  display: inline-block;
  vertical-align: middle;
  border-width: 0;
  width: auto;
  padding: 0 18px;
}
/* line 1322, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .wrapper_booking_button .promocode_wrapper label.promocode_label, #full_wrapper_booking .booking_form .wrapper_booking_button_guest .promocode_wrapper label.promocode_label {
  display: none;
}
/* line 1326, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .wrapper_booking_button .promocode_wrapper input.promocode_input, #full_wrapper_booking .booking_form .wrapper_booking_button_guest .promocode_wrapper input.promocode_input {
  border: 1px solid #11CCC7;
  border-radius: 20px;
  text-align: center;
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-size: 16px;
  color: #002D42;
  text-transform: uppercase;
  width: 146px;
  margin-top: 0;
  font-size: 11px;
}
/* line 1336, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .wrapper_booking_button .promocode_wrapper input.promocode_input::-webkit-input-placeholder, #full_wrapper_booking .booking_form .wrapper_booking_button_guest .promocode_wrapper input.promocode_input::-webkit-input-placeholder {
  color: #002D42;
  text-transform: initial;
}
/* line 1341, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .wrapper_booking_button .promocode_wrapper input.promocode_input::-moz-placeholder, #full_wrapper_booking .booking_form .wrapper_booking_button_guest .promocode_wrapper input.promocode_input::-moz-placeholder {
  color: #002D42;
  text-transform: initial;
}
/* line 1346, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .wrapper_booking_button .promocode_wrapper input.promocode_input:-ms-input-placeholder, #full_wrapper_booking .booking_form .wrapper_booking_button_guest .promocode_wrapper input.promocode_input:-ms-input-placeholder {
  color: #002D42;
  text-transform: initial;
}
/* line 1351, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .wrapper_booking_button .promocode_wrapper input.promocode_input:-moz-placeholder, #full_wrapper_booking .booking_form .wrapper_booking_button_guest .promocode_wrapper input.promocode_input:-moz-placeholder {
  color: #002D42;
  text-transform: initial;
}
/* line 1358, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .wrapper_booking_button .submit_button, #full_wrapper_booking .booking_form .wrapper_booking_button_guest .submit_button {
  display: inline-block;
  vertical-align: middle;
  width: 210px;
  height: 53px;
  font-family: "Cera Pro", sans-serif;
  font-size: 20px;
  font-weight: bold;
  line-height: 20px;
  color: #002D42;
  background: #11CCC7;
  border-radius: 0 10px 10px 0;
}
/* line 1368, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form .wrapper_booking_button .submit_button:hover, #full_wrapper_booking .booking_form .wrapper_booking_button_guest .submit_button:hover {
  color: white;
}

/* line 1376, ../sass/_booking_engine.scss */
.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
  width: 100vw;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  z-index: 1006 !important;
  max-width: 100%;
  min-width: 1140px;
  border-radius: 0;
}
/* line 1389, ../sass/_booking_engine.scss */
.datepicker_wrapper_element:before, .datepicker_wrapper_element_2:before, .datepicker_wrapper_element_3:before {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  content: '';
  display: none;
  background: url("/img/onac2/wave-patern-small.png");
  height: 120px;
}
/* line 1400, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .header_datepicker, .datepicker_wrapper_element_2 .header_datepicker, .datepicker_wrapper_element_3 .header_datepicker {
  background: transparent !important;
}
/* line 1403, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .header_datepicker:before, .datepicker_wrapper_element_2 .header_datepicker:before, .datepicker_wrapper_element_3 .header_datepicker:before {
  content: '';
  display: block;
  height: 20px;
}
/* line 1409, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .header_datepicker:after, .datepicker_wrapper_element_2 .header_datepicker:after, .datepicker_wrapper_element_3 .header_datepicker:after {
  content: '';
  display: block;
  margin: 10px auto 20px;
  width: 150px;
  height: 10px;
  background: url("/img/onac2/wave-aqua-small.png");
}
/* line 1418, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .header_datepicker .close_button_datepicker, .datepicker_wrapper_element_2 .header_datepicker .close_button_datepicker, .datepicker_wrapper_element_3 .header_datepicker .close_button_datepicker {
  width: 40px;
  height: 40px;
  border-width: 0;
  border-radius: 0;
  left: 50px;
  top: 20px;
}
/* line 110, ../../../../sass/plugins/_only_mixins.scss */
.datepicker_wrapper_element .header_datepicker .close_button_datepicker:before, .datepicker_wrapper_element .header_datepicker .close_button_datepicker:after, .datepicker_wrapper_element_2 .header_datepicker .close_button_datepicker:before, .datepicker_wrapper_element_2 .header_datepicker .close_button_datepicker:after, .datepicker_wrapper_element_3 .header_datepicker .close_button_datepicker:before, .datepicker_wrapper_element_3 .header_datepicker .close_button_datepicker:after {
  content: '';
  width: 100%;
  height: 2px;
  background: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 124, ../../../../sass/plugins/_only_mixins.scss */
.datepicker_wrapper_element .header_datepicker .close_button_datepicker:after, .datepicker_wrapper_element_2 .header_datepicker .close_button_datepicker:after, .datepicker_wrapper_element_3 .header_datepicker .close_button_datepicker:after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -moz-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}
/* line 1427, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .header_datepicker .close_button_datepicker:before, .datepicker_wrapper_element .header_datepicker .close_button_datepicker:after, .datepicker_wrapper_element_2 .header_datepicker .close_button_datepicker:before, .datepicker_wrapper_element_2 .header_datepicker .close_button_datepicker:after, .datepicker_wrapper_element_3 .header_datepicker .close_button_datepicker:before, .datepicker_wrapper_element_3 .header_datepicker .close_button_datepicker:after {
  background: #002D42;
}
/* line 1432, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .header_datepicker .specific_date_selector, .datepicker_wrapper_element_2 .header_datepicker .specific_date_selector, .datepicker_wrapper_element_3 .header_datepicker .specific_date_selector {
  display: none;
}
/* line 1440, ../sass/_booking_engine.scss */
.datepicker_wrapper_element[datepicker=endDate] .header_datepicker .specific_date_selector:before, .datepicker_wrapper_element_2[datepicker=endDate] .header_datepicker .specific_date_selector:before, .datepicker_wrapper_element_3[datepicker=endDate] .header_datepicker .specific_date_selector:before {
  content: '<';
}
/* line 1447, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd,
.datepicker_wrapper_element .datepicker_ext_inf_ed, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 0;
  right: 0;
  margin: auto;
}
/* line 1454, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content {
  margin: auto;
  width: 650px !important;
}
/* line 1459, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-group-last .ui-widget-header,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-group-last .ui-widget-header, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-group-last .ui-widget-header,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-group-last .ui-widget-header, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-group-last .ui-widget-header,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-group-last .ui-widget-header {
  margin-left: 10px;
}
/* line 1464, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header {
  background: #FAFAFA !important;
  padding: 5px;
  margin-right: 10px;
}
/* line 1469, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev, .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next {
  background: transparent;
  color: #002D42;
  -webkit-transform: rotate(0) !important;
  -moz-transform: rotate(0) !important;
  -ms-transform: rotate(0) !important;
  -o-transform: rotate(0) !important;
  transform: rotate(0) !important;
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
  top: 0;
}
/* line 1481, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev:hover, .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev.ui-state-hover, .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next:hover, .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next.ui-state-hover,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev:hover,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev.ui-state-hover,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next:hover,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next.ui-state-hover, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev:hover, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev.ui-state-hover, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next:hover, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next.ui-state-hover,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev:hover,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev.ui-state-hover,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next:hover,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next.ui-state-hover, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev:hover, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev.ui-state-hover, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next:hover, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next.ui-state-hover,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev:hover,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev.ui-state-hover,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next:hover,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next.ui-state-hover {
  background: transparent !important;
  color: #11CCC7 !important;
}
/* line 1486, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev .ui-icon, .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next .ui-icon,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev .ui-icon,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next .ui-icon, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev .ui-icon, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next .ui-icon,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev .ui-icon,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next .ui-icon, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev .ui-icon, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next .ui-icon,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev .ui-icon,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next .ui-icon {
  background: transparent !important;
}
/* line 1490, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev:before, .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next:before,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev:before,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next:before, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev:before, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next:before,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev:before,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next:before, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev:before, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next:before,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev:before,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 24px;
}
/* line 1497, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev:before,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev:before, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev:before,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev:before, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-prev:before,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-prev:before {
  content: '\f104';
}
/* line 1503, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next:before,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next:before, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next:before,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next:before, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-next:before,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-next:before {
  content: '\f105';
}
/* line 1508, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-title,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-title, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-title,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-title, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-widget-header .ui-datepicker-title,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-widget-header .ui-datepicker-title {
  font-family: "Cera Pro", sans-serif;
  font-size: 18px;
  font-weight: normal;
  line-height: 18px;
  font-weight: 500;
  color: #002D42 !important;
}
/* line 1516, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar th,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar th, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar th,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar th, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar th,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar th {
  font-size: 18px;
  font-weight: bold;
  color: #11CCC7;
}
/* line 1522, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td {
  border-width: 0;
  height: 35px;
}
/* line 1526, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td span, .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td a,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td span,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td a, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td span, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td a,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td span,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td a, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td span, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td a,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td span,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td a {
  font-size: 16px;
  line-height: 35px;
  font-weight: 500;
  font-family: "Cera Pro", sans-serif;
  color: #002D42 !important;
}
/* line 1533, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td span.ui-state-active, .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td a.ui-state-active,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td span.ui-state-active,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td a.ui-state-active, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td span.ui-state-active, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td a.ui-state-active,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td span.ui-state-active,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td a.ui-state-active, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td span.ui-state-active, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td a.ui-state-active,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td span.ui-state-active,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td a.ui-state-active {
  background: transparent !important;
}
/* line 1537, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td span.ui-state-hover, .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td a.ui-state-hover,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td span.ui-state-hover,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td a.ui-state-hover, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td span.ui-state-hover, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td a.ui-state-hover,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td span.ui-state-hover,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td a.ui-state-hover, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td span.ui-state-hover, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td a.ui-state-hover,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td span.ui-state-hover,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td a.ui-state-hover {
  background: #CFF5F4 !important;
  border-radius: 30px;
}
/* line 1543, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-week-end,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-week-end, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-week-end,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-week-end, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-week-end,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-week-end {
  background: transparent;
}
/* line 1546, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-week-end:before,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-week-end:before, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-week-end:before,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-week-end:before, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-week-end:before,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-week-end:before {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  z-index: -1;
  background: #FAFAFA;
}
/* line 1558, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled {
  background: transparent !important;
}
/* line 1561, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled span, .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled a,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled span,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled a, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled span, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled a,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled span,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled a, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled span, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled a,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled span,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled a {
  font-weight: normal;
}
/* line 1565, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled.ui-datepicker-week-end,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled.ui-datepicker-week-end, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled.ui-datepicker-week-end,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled.ui-datepicker-week-end, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled.ui-datepicker-week-end,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled.ui-datepicker-week-end {
  background: transparent;
}
/* line 1568, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled.ui-datepicker-week-end:before,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled.ui-datepicker-week-end:before, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled.ui-datepicker-week-end:before,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled.ui-datepicker-week-end:before, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled.ui-datepicker-week-end:before,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-state-disabled.ui-datepicker-week-end:before {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  z-index: -1;
  background: #F2F2F2;
}
/* line 1582, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date span, .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date a,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date span,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date a, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date span, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date a,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date span,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date a, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date span, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date a,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date span,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date a {
  background: #CFF5F4 !important;
  border-radius: 30px 0 0 30px;
}
/* line 1586, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date span:before, .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date a:before,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date span:before,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date a:before, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date span:before, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date a:before,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date span:before,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date a:before, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date span:before, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date a:before,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date span:before,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.ui-datepicker-start_date a:before {
  display: none;
}
/* line 1592, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.highlight,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.highlight, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.highlight,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.highlight, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.highlight,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.highlight {
  background: transparent !important;
}
/* line 1595, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.highlight span, .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.highlight a,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.highlight span,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.highlight a, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.highlight span, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.highlight a,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.highlight span,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.highlight a, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.highlight span, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.highlight a,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.highlight span,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.highlight a {
  background: #CFF5F4 !important;
}
/* line 1600, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection {
  background: transparent !important;
}
/* line 1603, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection span, .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection a,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection span,
.datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection a, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection span, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection a,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection span,
.datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection a, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection span, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection a,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection span,
.datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-content .ui-datepicker-calendar td.last-highlight-selection a {
  background: #CFF5F4 !important;
  border-radius: 0 30px 30px 0;
}
/* line 1613, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .current_nights_selector, .datepicker_wrapper_element_2 .current_nights_selector, .datepicker_wrapper_element_3 .current_nights_selector {
  display: none;
}
/* line 1618, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .months_selector_container .months_selector_title, .datepicker_wrapper_element_2 .months_selector_container .months_selector_title, .datepicker_wrapper_element_3 .months_selector_container .months_selector_title {
  text-transform: uppercase;
  color: #002D42;
}
/* line 1623, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .months_selector_container .months_selector_wrapper, .datepicker_wrapper_element_2 .months_selector_container .months_selector_wrapper, .datepicker_wrapper_element_3 .months_selector_container .months_selector_wrapper {
  margin: auto;
  width: 300px;
}
/* line 1629, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .specific_month_selector, .datepicker_wrapper_element .go_back_button, .datepicker_wrapper_element .cheapest_month_selector, .datepicker_wrapper_element_2 .specific_month_selector, .datepicker_wrapper_element_2 .go_back_button, .datepicker_wrapper_element_2 .cheapest_month_selector, .datepicker_wrapper_element_3 .specific_month_selector, .datepicker_wrapper_element_3 .go_back_button, .datepicker_wrapper_element_3 .cheapest_month_selector {
  display: block;
  width: 300px;
  margin: 0 auto;
  padding: 5px 10px;
  text-align: center;
  background: #ECFBFB;
  margin-top: 15px;
  color: #002D42;
  text-decoration: none;
}
/* line 1640, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .specific_month_selector strong, .datepicker_wrapper_element .go_back_button strong, .datepicker_wrapper_element .cheapest_month_selector strong, .datepicker_wrapper_element_2 .specific_month_selector strong, .datepicker_wrapper_element_2 .go_back_button strong, .datepicker_wrapper_element_2 .cheapest_month_selector strong, .datepicker_wrapper_element_3 .specific_month_selector strong, .datepicker_wrapper_element_3 .go_back_button strong, .datepicker_wrapper_element_3 .cheapest_month_selector strong {
  font-weight: bold;
  color: #002D42;
}
/* line 1646, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .specific_month_selector, .datepicker_wrapper_element_2 .specific_month_selector, .datepicker_wrapper_element_3 .specific_month_selector {
  border-radius: 10px !important;
  -webkit-transform: translateY(15px);
  -moz-transform: translateY(15px);
  -ms-transform: translateY(15px);
  -o-transform: translateY(15px);
  transform: translateY(15px);
}
/* line 1655, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .go_back_button, .datepicker_wrapper_element_2 .go_back_button, .datepicker_wrapper_element_3 .go_back_button {
  background: #002D42;
  color: white;
}
/* line 1659, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .go_back_button strong, .datepicker_wrapper_element_2 .go_back_button strong, .datepicker_wrapper_element_3 .go_back_button strong {
  color: white;
}

/* line 1665, ../sass/_booking_engine.scss */
.fancybox-overlay {
  z-index: 10001;
}

@media (max-width: 1180px) {
  /* line 1670, ../sass/_booking_engine.scss */
  #full_wrapper_booking .booking_form .dates_selector_personalized {
    width: 301px;
  }
  /* line 1674, ../sass/_booking_engine.scss */
  #full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper:before {
    display: none;
  }
  /* line 1678, ../sass/_booking_engine.scss */
  #full_wrapper_booking .booking_form .dates_selector_personalized .start_end_date_wrapper:after {
    margin-left: 5px;
  }
}
@media (max-height: 720px) {
  /* line 1687, ../sass/_booking_engine.scss */
  div.datepicker_wrapper_element div.ticks_wrapper,
  div.datepicker_wrapper_element div.dates_shortcut {
    display: none;
  }
}
/* line 1698, ../sass/_booking_engine.scss */
body.halloween #full_wrapper_booking .booking_form .wrapper_booking_button::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  background-image: url("/img/onac2/spider-insect.svg");
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  width: 60px;
  height: 60px;
  -webkit-transform: translate(50%, -50%) rotateY(180deg);
  -moz-transform: translate(50%, -50%) rotateY(180deg);
  -ms-transform: translate(50%, -50%) rotateY(180deg);
  -o-transform: translate(50%, -50%) rotateY(180deg);
  transform: translate(50%, -50%) rotateY(180deg);
}

/* line 1716, ../sass/_booking_engine.scss */
.hidden {
  display: none !important;
}

/* line 1720, ../sass/_booking_engine.scss */
.calendar_app.popup.fancybox-content {
  width: 1140px !important;
  height: auto !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  opacity: 1 !important;
  overflow: visible !important;
  padding: 0 !important;
}
/* line 1731, ../sass/_booking_engine.scss */
.calendar_app.popup.fancybox-content .fancybox-button {
  top: 0 !important;
}

/* line 1738, ../sass/_booking_engine.scss */
html[lang="fr"] #full_wrapper_booking .booking_form .dates_selector_personalized .start_date_personalized, html[lang="fr"] #full_wrapper_booking .booking_form .dates_selector_personalized .end_date_personalized {
  padding: 0 12px;
}

/* line 1, ../sass/_modal.scss */
.modal_wrapper {
  display: none;
  opacity: 0;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1005;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 45, 66, 0.8);
  -webkit-transition: opacity 1s;
  -moz-transition: opacity 1s;
  -ms-transition: opacity 1s;
  -o-transition: opacity 1s;
  transition: opacity 1s;
}
/* line 12, ../sass/_modal.scss */
.modal_wrapper.active {
  display: block;
  opacity: 1;
}
/* line 16, ../sass/_modal.scss */
.modal_wrapper .modal {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 10px;
  padding: 40px;
  max-height: calc(100vh - 220px);
  max-width: calc(100vw - 60px);
  box-sizing: border-box;
}
/* line 24, ../sass/_modal.scss */
.modal_wrapper .modal .close_modal {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 20px;
  padding: 10px 10px 10px 15px;
  color: white;
  background: #11CCC7;
  border-radius: 0 10px 0 50%;
}
/* line 33, ../sass/_modal.scss */
.modal_wrapper .modal .close_modal:hover {
  background: #002D42;
}
/* line 37, ../sass/_modal.scss */
.modal_wrapper .modal:before {
  content: '';
  display: block;
  background: url(/img/onac2/wave-patern.png) center center;
  height: 30px;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-radius: 0 0 10px 10px;
  overflow: hidden;
}
/* line 49, ../sass/_modal.scss */
.modal_wrapper .modal .content {
  max-height: calc(100vh - 330px);
  overflow: auto;
}

/* line 55, ../sass/_modal.scss */
.open_modal, .close_modal {
  cursor: pointer;
}

/* line 59, ../sass/_modal.scss */
ul.list-dots li {
  display: block;
  padding: 5px 0 5px 20px;
  position: relative;
}
/* line 63, ../sass/_modal.scss */
ul.list-dots li:before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #11CCC7;
  position: absolute;
  left: 0;
  top: 11px;
  opacity: .5;
}

/* line 1, ../sass/_header.scss */
header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1002;
  padding: 50px 60px;
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0));
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 12, ../sass/_header.scss */
.black_friday header #top-sections .booking_button a {
  background-color: #000000;
  color: white;
}
/* line 18, ../sass/_header.scss */
header.fixed {
  background: white;
  padding: 0 0 0 60px;
}
/* line 22, ../sass/_header.scss */
header.fixed .float_left {
  width: 20%;
}
/* line 26, ../sass/_header.scss */
header.fixed .float_right {
  width: 80%;
}
/* line 30, ../sass/_header.scss */
header.fixed .menu_toggle {
  padding-top: 5px;
}
/* line 33, ../sass/_header.scss */
header.fixed .menu_toggle span {
  opacity: 0;
  display: none;
}
/* line 38, ../sass/_header.scss */
header.fixed .menu_toggle:before, header.fixed .menu_toggle:after {
  background: #11CCC7;
}
/* line 43, ../sass/_header.scss */
header.fixed #logoDiv {
  width: 70px;
  padding-right: 0;
}
/* line 47, ../sass/_header.scss */
header.fixed #logoDiv img {
  width: 0;
  opacity: 0;
}
/* line 52, ../sass/_header.scss */
header.fixed #logoDiv span {
  width: 50px;
  height: 50px;
  opacity: 1;
}
/* line 59, ../sass/_header.scss */
header.fixed .hotel_info_header {
  max-width: 400px;
}
/* line 63, ../sass/_header.scss */
header.fixed #lang {
  opacity: 1;
  padding-bottom: 0;
  margin-top: -20px;
}
/* line 70, ../sass/_header.scss */
header.fixed #top-sections i.fa-info-circle {
  color: #11CCC7;
}
/* line 74, ../sass/_header.scss */
header.fixed #top-sections .phone, header.fixed #top-sections .search, header.fixed #top-sections .user, header.fixed #top-sections .language_icon {
  color: #11CCC7;
}
/* line 79, ../sass/_header.scss */
header.fixed #top-sections .user.new_form_login_club i {
  font-size: 0;
  background: #11CCC7;
}
/* line 85, ../sass/_header.scss */
header.fixed #top-sections a {
  color: #11CCC7;
}
/* line 89, ../sass/_header.scss */
header.fixed #top-sections .booking_button {
  width: 200px;
  margin-left: 20px;
}
/* line 96, ../sass/_header.scss */
header .popup_phone {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: fixed;
  z-index: 100;
  background: rgba(0, 45, 66, 0.8);
}
/* line 102, ../sass/_header.scss */
header .popup_phone .close_popup_phone {
  width: 30px;
  height: 30px;
  position: absolute;
  top: 20px;
  left: 20px;
  cursor: pointer;
}
/* line 110, ../sass/_header.scss */
header .popup_phone .close_popup_phone:before, header .popup_phone .close_popup_phone:after {
  background: white;
}
/* line 115, ../sass/_header.scss */
header .popup_phone .content {
  background: white;
  border-radius: 10px;
  color: #002D42;
  padding: 20px;
}
/* line 121, ../sass/_header.scss */
header .popup_phone .content .subtitle {
  padding-bottom: 30px;
}
/* line 125, ../sass/_header.scss */
header .popup_phone .content .icon_list {
  text-align: left;
}
/* line 128, ../sass/_header.scss */
header .popup_phone .content .icon_list li {
  display: block;
}
/* line 131, ../sass/_header.scss */
header .popup_phone .content .icon_list li a {
  color: #002D42 !important;
}
/* line 139, ../sass/_header.scss */
header .menu_toggle {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  color: white;
  padding-top: 20px;
  width: 45px;
  height: 40px;
  text-align: right;
  cursor: pointer;
  -webkit-transition: padding 0.6s;
  -moz-transition: padding 0.6s;
  -ms-transition: padding 0.6s;
  -o-transition: padding 0.6s;
  transition: padding 0.6s;
}
/* line 151, ../sass/_header.scss */
header .menu_toggle span {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 3px;
  opacity: 1;
  -webkit-transition: opacity 0.6s;
  -moz-transition: opacity 0.6s;
  -ms-transition: opacity 0.6s;
  -o-transition: opacity 0.6s;
  transition: opacity 0.6s;
}
/* line 161, ../sass/_header.scss */
header .menu_toggle:before, header .menu_toggle:after {
  content: '';
  display: block;
  width: 100%;
  height: 3px;
  background: white;
  border-radius: 20%;
  margin: 8px 0 0 auto;
  -webkit-transition: width 0.6s;
  -moz-transition: width 0.6s;
  -ms-transition: width 0.6s;
  -o-transition: width 0.6s;
  transition: width 0.6s;
}
/* line 172, ../sass/_header.scss */
header .menu_toggle:after {
  width: 50%;
  top: 35px;
}
/* line 178, ../sass/_header.scss */
header .menu_toggle:hover:before {
  width: 50%;
}
/* line 182, ../sass/_header.scss */
header .menu_toggle:hover:after {
  width: 100%;
}
/* line 188, ../sass/_header.scss */
header #logoDiv {
  display: inline-block;
  vertical-align: middle;
  width: 250px;
  white-space: nowrap;
  padding: 0 0 0 30px;
}
/* line 195, ../sass/_header.scss */
header #logoDiv img {
  vertical-align: middle;
  opacity: 1;
  width: 100%;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 202, ../sass/_header.scss */
header #logoDiv span {
  display: inline-block;
  width: 0;
  height: 0;
  opacity: 0;
  color: #11CCC7;
  line-height: 45px;
  font-size: 48px;
  font-weight: bold;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 215, ../sass/_header.scss */
header .hotel_info_header {
  display: inline-block;
  vertical-align: middle;
  white-space: nowrap;
  max-width: 0;
  overflow: hidden;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 223, ../sass/_header.scss */
header .hotel_info_header .stars i {
  display: inline-block;
  vertical-align: top;
  font-size: 10px;
  padding-top: 3px;
}
/* line 231, ../sass/_header.scss */
header #lang {
  text-align: right;
  color: white;
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-size: 16px;
  color: #002D42;
  font-size: 14px;
  padding-bottom: 7px;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 238, ../sass/_header.scss */
header #lang .currency, header #lang .lang_selector {
  display: inline-block;
  vertical-align: middle;
}
/* line 242, ../sass/_header.scss */
header #lang .currency a, header #lang .lang_selector a {
  color: white;
}
/* line 248, ../sass/_header.scss */
header #top-sections {
  text-align: right;
}
/* line 251, ../sass/_header.scss */
header #top-sections .extra_section {
  margin-right: 10px;
  display: inline-block;
}
/* line 255, ../sass/_header.scss */
header #top-sections .extra_section i {
  margin-right: 5px;
}
/* line 260, ../sass/_header.scss */
header #top-sections .separator {
  display: inline-block;
  vertical-align: middle;
  height: 20px;
  width: 1px;
  background: white;
  margin: 0 15px;
}
/* line 269, ../sass/_header.scss */
header #top-sections .phone, header #top-sections .search, header #top-sections .user, header #top-sections .language_icon {
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  color: white;
  cursor: pointer;
  padding-left: 7px;
}
/* line 277, ../sass/_header.scss */
header #top-sections .phone i, header #top-sections .search i, header #top-sections .user i, header #top-sections .language_icon i {
  font-size: 20px;
}
/* line 282, ../sass/_header.scss */
header #top-sections .phone, header #top-sections .search, header #top-sections .language_icon {
  padding-right: 7px;
}
/* line 288, ../sass/_header.scss */
header #top-sections .phone > i:before {
  display: block;
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  -ms-transform: rotateY(180deg);
  -o-transform: rotateY(180deg);
  transform: rotateY(180deg);
}
/* line 300, ../sass/_header.scss */
header #top-sections .language_icon span {
  padding: 3px;
}
/* line 303, ../sass/_header.scss */
header #top-sections .language_icon span a {
  display: block;
  padding: 3px 7px;
  font-weight: bold;
  color: #002D42;
}
/* line 309, ../sass/_header.scss */
header #top-sections .language_icon span a:hover {
  background: #CFF5F4;
}
/* line 316, ../sass/_header.scss */
header #top-sections .user {
  position: relative;
  display: none;
}
/* line 320, ../sass/_header.scss */
header #top-sections .user.new_form_login_club {
  line-height: 1;
  vertical-align: top;
}
/* line 326, ../sass/_header.scss */
header #top-sections .user.logged i {
  -webkit-mask-image: url(https://storage.googleapis.com/cdn.paraty.es/ona-corporativa/files/user-login.svg);
}
/* line 331, ../sass/_header.scss */
header #top-sections .user i {
  cursor: pointer;
  font-size: 0;
  -webkit-mask-image: url(https://storage.googleapis.com/cdn.paraty.es/ona-corporativa/files/user-logout.svg);
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  background: white;
  width: 26px;
  height: 26px;
}
/* line 342, ../sass/_header.scss */
header #top-sections .user label {
  display: none;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  position: absolute;
  top: -7px;
  right: -7px;
  background: red;
  color: white;
  border: 1px solid white;
}
/* line 354, ../sass/_header.scss */
header #top-sections .user label span {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 12px;
}
/* line 360, ../sass/_header.scss */
header #top-sections .user .popup {
  width: 350px;
  position: absolute;
  right: 0;
  top: calc(100% + 30px);
  color: #002D42;
}
/* line 367, ../sass/_header.scss */
header #top-sections .user .popup .close {
  display: block;
  position: absolute;
  top: 50px;
  right: 40px;
  width: 15px;
  height: 15px;
  cursor: pointer;
}
/* line 376, ../sass/_header.scss */
header #top-sections .user .popup .close:before, header #top-sections .user .popup .close:after {
  background: #002D42;
}
/* line 382, ../sass/_header.scss */
header #top-sections .user .popup svg * {
  fill: rgba(236, 251, 251, 0.6);
}
/* line 387, ../sass/_header.scss */
header #top-sections .user .popup .content {
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-size: 16px;
  color: #002D42;
  position: absolute;
  top: 75px;
  left: 50px;
  right: 75px;
}
/* line 394, ../sass/_header.scss */
header #top-sections .user .popup .content:lang(de) {
  top: 65px;
  left: 28px;
  right: 45px;
}
/* line 400, ../sass/_header.scss */
header #top-sections .user .popup .content img {
  width: 50px;
}
/* line 407, ../sass/_header.scss */
header #top-sections a {
  color: white;
}
/* line 410, ../sass/_header.scss */
header #top-sections a i.fa-info-circle {
  color: #11CCC7;
}
/* line 415, ../sass/_header.scss */
header #top-sections > a:first-of-type {
  padding: 5px 10px;
  border-radius: 10px;
  color: #11CCC7;
  background-color: white;
}
/* line 422, ../sass/_header.scss */
header #top-sections .booking_button {
  display: inline-block;
  vertical-align: middle;
  overflow: hidden;
  width: 0;
  text-align: center;
  -webkit-transition: width 0.6s;
  -moz-transition: width 0.6s;
  -ms-transition: width 0.6s;
  -o-transition: width 0.6s;
  transition: width 0.6s;
}
/* line 430, ../sass/_header.scss */
header #top-sections .booking_button a {
  display: block;
  height: 50px;
  line-height: 50px;
  font-size: 24px;
  font-weight: 500;
  color: #002D42;
  text-transform: uppercase;
  background: #11CCC7;
}
/* line 443, ../sass/_header.scss */
header .float_left {
  width: 45%;
}
/* line 447, ../sass/_header.scss */
header .float_right {
  width: 52%;
}

/* line 452, ../sass/_header.scss */
#main_menu {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: fixed;
  height: 100vw;
  width: 0;
  z-index: 1002;
  overflow: hidden;
  background: white;
  -webkit-transition: width 1s;
  -moz-transition: width 1s;
  -ms-transition: width 1s;
  -o-transition: width 1s;
  transition: width 1s;
}
/* line 462, ../sass/_header.scss */
#main_menu.opened {
  width: 100vw;
  z-index: 1003;
}
/* line 466, ../sass/_header.scss */
#main_menu.opened:before {
  width: 100%;
  border-radius: 0;
  -webkit-transition: all 1.5s;
  -moz-transition: all 1.5s;
  -ms-transition: all 1.5s;
  -o-transition: all 1.5s;
  transition: all 1.5s;
}
/* line 473, ../sass/_header.scss */
#main_menu.opened .close:before, #main_menu.opened .close:after {
  left: 50%;
  top: 50%;
}
/* line 478, ../sass/_header.scss */
#main_menu.opened .close:before {
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 486, ../sass/_header.scss */
#main_menu.opened .close:after {
  width: 100%;
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -moz-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}
/* line 496, ../sass/_header.scss */
#main_menu.opened .banner_extra_menu {
  opacity: 1;
}
/* line 501, ../sass/_header.scss */
#main_menu:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 0;
  z-index: -1;
  background: #CFF5F4;
  border-radius: 0 50% 50% 0;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 511, ../sass/_header.scss */
#main_menu .close {
  position: absolute;
  top: 50px;
  left: 50px;
  width: 50px;
  height: 50px;
  cursor: pointer;
}
/* line 519, ../sass/_header.scss */
#main_menu .close:before, #main_menu .close:after {
  background: #11CCC7;
  height: 3px;
  border-radius: 20%;
  left: 0;
  top: 20px;
  -webkit-transform: rotate(0) translate(0, 0);
  -moz-transform: rotate(0) translate(0, 0);
  -ms-transform: rotate(0) translate(0, 0);
  -o-transform: rotate(0) translate(0, 0);
  transform: rotate(0) translate(0, 0);
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}
/* line 533, ../sass/_header.scss */
#main_menu .close:after {
  top: 40px;
  left: 50%;
  width: 50%;
}
/* line 540, ../sass/_header.scss */
#main_menu .main_menu {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  top: 50vh;
  left: 0;
  margin-left: 100px;
}
/* line 547, ../sass/_header.scss */
#main_menu .main_menu .main-section-div-wrapper a {
  display: block;
  color: #002D42;
  margin-bottom: 15px;
  position: relative;
}
/* line 555, ../sass/_header.scss */
#main_menu .main_menu .main-section-div-wrapper a:hover:before {
  width: 100%;
}
/* line 560, ../sass/_header.scss */
#main_menu .main_menu .main-section-div-wrapper a:before {
  content: '';
  height: 3px;
  background: #70E0DD;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 0;
  -webkit-transition: width 0.6s;
  -moz-transition: width 0.6s;
  -ms-transition: width 0.6s;
  -o-transition: width 0.6s;
  transition: width 0.6s;
}
/* line 574, ../sass/_header.scss */
#main_menu .main_menu .social {
  margin-top: 15px;
  display: inline-block;
}
/* line 578, ../sass/_header.scss */
#main_menu .main_menu .social .wave-aqua {
  margin-bottom: 20px;
}
/* line 582, ../sass/_header.scss */
#main_menu .main_menu .social a {
  position: relative;
  display: inline-block;
  color: #002D42;
  width: 50px;
  height: 50px;
  border: 2px solid #002D42;
  border-radius: 50%;
  margin-right: 10px;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 594, ../sass/_header.scss */
#main_menu .main_menu .social a:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  background: #11CCC7;
  width: 0;
  height: 0;
  border-radius: 50%;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 604, ../sass/_header.scss */
#main_menu .main_menu .social a i {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 608, ../sass/_header.scss */
#main_menu .main_menu .social a:hover {
  color: white;
  border-color: #70E0DD;
}
/* line 612, ../sass/_header.scss */
#main_menu .main_menu .social a:hover:before {
  width: 100%;
  height: 100%;
}
/* line 621, ../sass/_header.scss */
#main_menu .banner_extra_menu {
  position: absolute;
  top: 50px;
  bottom: 100px;
  right: 0;
  width: calc(50vw - 150px);
  color: #002D42;
  opacity: 0;
  -webkit-transition: opacity 1s;
  -moz-transition: opacity 1s;
  -ms-transition: opacity 1s;
  -o-transition: opacity 1s;
  transition: opacity 1s;
}
/* line 631, ../sass/_header.scss */
#main_menu .banner_extra_menu .content {
  position: absolute;
  top: 100px;
  right: 0;
  left: 0;
  padding: 100px 50px;
}
/* line 638, ../sass/_header.scss */
#main_menu .banner_extra_menu .content h3, #main_menu .banner_extra_menu .content .main_menu .main-section-div-wrapper a, #main_menu .main_menu .main-section-div-wrapper .banner_extra_menu .content a {
  margin-bottom: 30px;
}
/* line 642, ../sass/_header.scss */
#main_menu .banner_extra_menu .content .link {
  display: inline-block;
  margin-top: 30px;
}
/* line 648, ../sass/_header.scss */
#main_menu .banner_extra_menu svg {
  position: relative;
  width: 50vw;
  right: 0;
}
/* line 653, ../sass/_header.scss */
#main_menu .banner_extra_menu svg * {
  fill: url(#bubble2-gradient) #11CCC7;
}

/* line 661, ../sass/_header.scss */
body.home.header_countdown header.header_countdown {
  top: 50px;
}
/* line 664, ../sass/_header.scss */
body.home.header_countdown header.header_countdown.fixed {
  top: 0;
}

/* line 1, ../sass/_slider.scss */
#slider_container {
  padding: 0;
  height: auto;
  position: relative;
  background: #002D42;
}
/* line 6, ../sass/_slider.scss */
#slider_container.full_slider_container {
  height: 100vh;
}
/* line 9, ../sass/_slider.scss */
#slider_container.page {
  height: 100vh;
}
/* line 11, ../sass/_slider.scss */
#slider_container.page .tp-banner-container {
  height: 100vh !important;
}
/* line 15, ../sass/_slider.scss */
#slider_container .caption {
  width: 100% !important;
  top: 0 !important;
  left: 0 !important;
  bottom: 0;
  padding: 50px 140px !important;
  z-index: 3;
}
/* line 23, ../sass/_slider.scss */
#slider_container .caption .cartela1 {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: 100%;
  text-transform: none;
  color: white;
}
/* line 29, ../sass/_slider.scss */
#slider_container .caption .cartela1.v2 {
  font-family: "Cera Pro", sans-serif;
  font-weight: 700;
  letter-spacing: 0;
  line-height: 1;
  color: #002D42;
}
/* line 36, ../sass/_slider.scss */
#slider_container .caption .cartela1.v2 h1 {
  font-size: 40px;
  line-height: 45px;
}
/* line 41, ../sass/_slider.scss */
#slider_container .caption .cartela1.v2 span {
  color: #11CCC7;
}
/* line 45, ../sass/_slider.scss */
#slider_container .caption .cartela1.v2 a.btn_link {
  display: inline-block;
  padding: 10px 30px;
  margin-top: 30px;
  border-radius: 10px;
  background-color: #11CCC7;
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.5);
  font-size: 24px;
  color: #002D42;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}
/* line 56, ../sass/_slider.scss */
#slider_container .caption .cartela1.v2 a.btn_link:hover {
  color: white;
}
/* line 63, ../sass/_slider.scss */
#slider_container .caption .cartela1.v2.black_friday {
  font-weight: 900;
}
/* line 66, ../sass/_slider.scss */
#slider_container .caption .cartela1.v2.black_friday h1 {
  font-weight: 900;
}
/* line 70, ../sass/_slider.scss */
#slider_container .caption .cartela1.v2.black_friday a.btn_link {
  background-color: #6427B7;
  border-radius: 22px;
  box-shadow: none;
  margin-top: 20px;
  font-size: 20px;
  color: white;
  text-transform: uppercase;
}
/* line 80, ../sass/_slider.scss */
#slider_container .caption .cartela_homey {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: 100%;
  text-transform: none;
  color: white;
}
/* line 85, ../sass/_slider.scss */
#slider_container .caption .cartela_homey h1 {
  color: #002D42;
  margin-left: -35px;
  margin-bottom: -17px;
  margin-top: 15px;
}
/* line 90, ../sass/_slider.scss */
#slider_container .caption .cartela_homey h1 i {
  margin-top: -15px;
  margin-right: -20px;
  color: white;
  background: #11CCC7;
  border-radius: 50%;
  width: 140px;
  vertical-align: middle;
  height: 140px;
  position: relative;
  z-index: -1;
}
/* line 101, ../sass/_slider.scss */
#slider_container .caption .cartela_homey h1 i:before {
  font-size: 70px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 106, ../sass/_slider.scss */
#slider_container .caption .cartela_homey h1 .image_circle {
  background: #11CCC7;
  border-radius: 50%;
  width: 135px;
  margin-right: -22px;
  height: 130px;
  vertical-align: sub;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
/* line 118, ../sass/_slider.scss */
#slider_container .caption .cartela_homey h2 {
  font-weight: bold;
}
/* line 120, ../sass/_slider.scss */
#slider_container .caption .cartela_homey h2 span {
  position: relative;
  font-weight: normal;
}
/* line 123, ../sass/_slider.scss */
#slider_container .caption .cartela_homey h2 span:after {
  content: "";
  width: 100%;
  background: url("/img/onac2/wave-line-aqua.png");
  height: 10px;
  background-repeat: no-repeat;
  bottom: -15px;
  right: 0;
  position: absolute;
}
/* line 135, ../sass/_slider.scss */
#slider_container .caption .cartela_homey a {
  background: #002D42;
  color: white;
  font-size: 22px;
  border-radius: 30px;
  padding: 14px 34px;
  display: inline-block;
  width: auto;
  margin-top: 20px;
}
/* line 144, ../sass/_slider.scss */
#slider_container .caption .cartela_homey a:hover {
  background: #11CCC7;
}
/* line 150, ../sass/_slider.scss */
#slider_container .tp-bullets {
  bottom: 25vh !important;
  left: 140px !important;
  margin: 0 !important;
  opacity: 1 !important;
  z-index: 40;
  display: none;
}
/* line 157, ../sass/_slider.scss */
#slider_container .tp-bullets .bullet {
  position: relative;
  background: white;
  width: 40px;
  height: 3px;
  margin-right: 15px;
  opacity: .5;
}
/* line 164, ../sass/_slider.scss */
#slider_container .tp-bullets .bullet.selected {
  opacity: 1;
}
/* line 167, ../sass/_slider.scss */
#slider_container .tp-bullets .bullet:after {
  content: '';
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 0;
  right: 0;
  height: 20px;
}
/* line 179, ../sass/_slider.scss */
#slider_container .inner_slider {
  position: relative;
  height: 400px;
  overflow: hidden;
  border-bottom: 1px solid #002D42;
}
/* line 184, ../sass/_slider.scss */
#slider_container .inner_slider.full_slider {
  height: 100vh;
}
/* line 187, ../sass/_slider.scss */
#slider_container .inner_slider.full_slider .innner_slider_carousel .pic {
  height: 100vh;
}
/* line 192, ../sass/_slider.scss */
#slider_container .inner_slider .pic_slider {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 196, ../sass/_slider.scss */
#slider_container .inner_slider .innner_slider_carousel .pic {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
}
/* line 201, ../sass/_slider.scss */
#slider_container .inner_slider .innner_slider_carousel .pic img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  width: auto;
}
/* line 206, ../sass/_slider.scss */
#slider_container .inner_slider .innner_slider_carousel .caption_slider {
  position: absolute;
  width: 100% !important;
  top: 0 !important;
  left: 0 !important;
  bottom: 0;
  padding: 50px 140px !important;
  background: rgba(51, 51, 51, 0.3);
}
/* line 214, ../sass/_slider.scss */
#slider_container .inner_slider .innner_slider_carousel .caption_slider .cartela1 {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: 100%;
  text-transform: none;
  color: white;
}
/* line 219, ../sass/_slider.scss */
#slider_container .inner_slider .innner_slider_carousel .caption_slider .cartela1 h1, #slider_container .inner_slider .innner_slider_carousel .caption_slider .cartela1 .title {
  font-size: 72px;
  font-weight: bold;
  line-height: 55px;
}
/* line 224, ../sass/_slider.scss */
#slider_container .inner_slider .innner_slider_carousel .caption_slider .cartela1 h2 {
  font-size: 45px;
  padding: 20px 0;
}
/* line 229, ../sass/_slider.scss */
#slider_container .inner_slider .innner_slider_carousel .caption_slider video {
  object-fit: cover;
}
/* line 233, ../sass/_slider.scss */
#slider_container .inner_slider .innner_slider_carousel .owl-dots {
  position: absolute;
  bottom: 25vh !important;
  left: 140px !important;
  margin: 0 !important;
  opacity: 1 !important;
  z-index: 40;
}
/* line 240, ../sass/_slider.scss */
#slider_container .inner_slider .innner_slider_carousel .owl-dots .owl-dot {
  position: relative;
  display: inline-block;
  background: white;
  width: 40px;
  height: 3px;
  margin-right: 15px;
  opacity: .5;
}
/* line 248, ../sass/_slider.scss */
#slider_container .inner_slider .innner_slider_carousel .owl-dots .owl-dot.active {
  opacity: 1;
}
/* line 251, ../sass/_slider.scss */
#slider_container .inner_slider .innner_slider_carousel .owl-dots .owl-dot:after {
  content: '';
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 0;
  right: 0;
  height: 20px;
}

/* line 2, ../sass/_agencies_login.scss */
.inner_slider.agencies_login {
  background: black;
}
/* line 4, ../sass/_agencies_login.scss */
.inner_slider.agencies_login .innner_slider_carousel {
  opacity: .5;
}
/* line 8, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: 22;
  margin-top: 50px;
  background: transparent;
  overflow-x: hidden;
}
/* line 14, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login img {
  display: none;
}
/* line 17, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login .login-agency-message.m_ok {
  display: none !important;
}
/* line 20, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login.logged {
  width: 100%;
  padding-left: 150px;
  text-align: left;
}
/* line 24, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login.logged .login-agency-message.m_ok {
  width: 300px;
  display: block !important;
  background: transparent;
  color: white;
  margin: 0;
  text-align: left;
}
/* line 32, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login.logged .login-agency-message.m_ok .title {
  margin: 0 10px 0 0;
  text-align: left;
}
/* line 35, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login.logged .login-agency-message.m_ok .title svg {
  left: 0;
  right: -10px;
  width: 100%;
}
/* line 41, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login.logged .login-agency-message.m_ok h3, .inner_slider .popup-agency-login.logged .login-agency-message.m_ok #main_menu .main_menu .main-section-div-wrapper a, #main_menu .main_menu .main-section-div-wrapper .inner_slider .popup-agency-login.logged .login-agency-message.m_ok a {
  font-size: 22px;
  padding: 10px 0;
}
/* line 45, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login.logged .login-agency-message.m_ok h3 + .title, .inner_slider .popup-agency-login.logged .login-agency-message.m_ok #main_menu .main_menu .main-section-div-wrapper a + .title, #main_menu .main_menu .main-section-div-wrapper .inner_slider .popup-agency-login.logged .login-agency-message.m_ok a + .title {
  font-weight: normal;
  font-size: 22px;
  margin: 0;
}
/* line 50, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login.logged .login-agency-message.m_ok .wellcome {
  font-size: 12px;
  line-height: 20px;
  padding: 10px 0;
}
/* line 55, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login.logged .login-agency-message.m_ok .wellcome + a {
  display: inline-block;
  color: white;
  font-size: 12px;
  text-transform: uppercase;
}
/* line 60, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login.logged .login-agency-message.m_ok .wellcome + a:hover {
  color: #11CCC7;
}
/* line 63, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login.logged .login-agency-message.m_ok .wellcome + a:before {
  content: '';
  background-image: url("/img/onac2/logout.png");
  background-repeat: no-repeat;
  background-position: center 3px;
  background-size: 18px;
  display: inline-block;
  vertical-align: middle;
  width: 25px;
  height: 25px;
  margin-right: 5px;
}
/* line 78, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login .title {
  position: relative;
  font-size: 33px;
  color: white;
  font-weight: bold;
  text-transform: unset;
  padding-bottom: 20px;
  font-family: "Cera Pro", sans-serif;
}
/* line 86, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login .title svg {
  width: calc(100% + 40px);
  position: absolute;
  bottom: 0;
  left: -10px;
  right: -20px;
}
/* line 94, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login .input_wrapper label {
  position: relative;
  z-index: 2;
  margin-bottom: -29px;
  font-size: 12px;
  padding-left: 20px;
  color: white;
  font-family: "Cera Pro", sans-serif;
}
/* line 104, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login div.input_wrapper .input.input_ko #agency_user, .inner_slider .popup-agency-login div.input_wrapper .input.input_ko #agency_password {
  background-color: rgba(255, 255, 255, 0.6);
}
/* line 107, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login div.input_wrapper .input.input_ko::after {
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
}
/* line 113, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login #agency_user, .inner_slider .popup-agency-login #agency_password {
  border-bottom-width: 0;
  border-radius: 50px;
  color: white;
  font-family: "Cera Pro", sans-serif;
}
/* line 119, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login #agency_password {
  letter-spacing: 2px;
}
/* line 122, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login .links {
  padding: 0 5px;
}
/* line 124, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login .links .link {
  color: white;
  border-bottom-width: 0;
  text-decoration: underline;
  font-weight: 300;
}
/* line 129, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login .links .link:after, .inner_slider .popup-agency-login .links .link:before {
  display: none;
}
/* line 132, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login .links .link:hover {
  background: transparent;
  color: #11CCC7;
}
/* line 139, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login .btn_wrapper .btn {
  border-radius: 20px;
  background: #11CCC7;
  font-family: "Cera Pro", sans-serif;
}
/* line 143, ../sass/_agencies_login.scss */
.inner_slider .popup-agency-login .btn_wrapper .btn.btn_link {
  background: white;
  color: #11CCC7;
  width: 80%;
  margin-top: 20px;
}

@supports (-webkit-backdrop-filter: none) or (backdrop-filter: none) {
  /* line 155, ../sass/_agencies_login.scss */
  .popup-agency-login #agency_user, .popup-agency-login #agency_password {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.3);
  }
}
/* line 163, ../sass/_agencies_login.scss */
.modal_signup {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: fixed;
  overflow: hidden;
  bottom: 100vw;
  opacity: 0;
  z-index: -2;
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}
/* line 172, ../sass/_agencies_login.scss */
.modal_signup.active {
  opacity: 1;
  bottom: 0;
  z-index: 2001;
}
/* line 177, ../sass/_agencies_login.scss */
.modal_signup .close_modal_signup {
  position: absolute;
  top: 20px;
  right: 20px;
  color: black;
  font-size: 50px;
  z-index: 10;
}
/* line 186, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_pic {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 500px;
  background-size: cover;
  background-position: center;
  text-align: center;
}
/* line 195, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_pic::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 45, 66, 0.8);
}
/* line 200, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_pic .center_xy {
  margin-top: -100px;
}
/* line 203, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_pic .title_singup {
  display: inline-block;
  position: relative;
  font-size: 33px;
  color: white;
  font-weight: bold;
  text-transform: unset;
  padding-bottom: 20px;
  font-family: "Cera Pro", sans-serif;
}
/* line 212, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_pic .title_singup svg {
  width: calc(100% + 40px);
  position: absolute;
  bottom: 0;
  left: -10px;
  right: -20px;
}
/* line 220, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_pic .desc_signup {
  font-size: 12px;
  line-height: 20px;
  color: white;
  margin-top: 30px;
}
/* line 227, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_form {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: calc(100% - 500px);
  background: white;
}
/* line 234, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_form .popup-agency-signup {
  max-height: 100%;
  width: 100%;
  padding: 100px 100px 20px 20px;
}
/* line 238, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_form .popup-agency-signup img {
  display: none;
}
/* line 241, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_form .popup-agency-signup * {
  font-family: "Cera Pro", sans-serif;
}
/* line 244, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_form .popup-agency-signup input[type=text], .modal_signup .modal_content .modal_form .popup-agency-signup input[type=password] {
  border-radius: 0;
  background: transparent;
  border-bottom: 1px solid #11CCC7;
}
/* line 249, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_form .popup-agency-signup input[type=checkbox] {
  background: transparent;
  border: 1px solid #11CCC7;
}
/* line 255, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_form .popup-agency-signup .input_wrapper .input:after {
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
}
/* line 260, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_form .popup-agency-signup .input_wrapper .btn {
  border-radius: 20px;
  background: #11CCC7;
  color: #002D42;
  padding: 10px 50px;
  font-family: "Cera Pro", sans-serif;
  overflow: hidden;
}
/* line 267, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_form .popup-agency-signup .input_wrapper .btn.btn_link {
  background: white;
  color: #11CCC7;
  width: 80%;
  margin-top: 20px;
}
/* line 274, ../sass/_agencies_login.scss */
.modal_signup .modal_content .modal_form .popup-agency-signup .input_wrapper .btn.loading::before {
  font-family: "Font Awesome 5 Pro";
  line-height: 22px;
}

/* line 1, ../sass/banners/_banner_iconsx3.scss */
.banner_iconsx3_wrapper {
  padding: 70px calc((100% - 1140px) / 2);
  text-align: center;
}
/* line 4, ../sass/banners/_banner_iconsx3.scss */
.banner_iconsx3_wrapper .subtitle {
  margin-bottom: 50px;
}
/* line 7, ../sass/banners/_banner_iconsx3.scss */
.banner_iconsx3_wrapper .content {
  font-size: 18px;
  font-weight: 500;
  padding-bottom: 50px;
}
/* line 13, ../sass/banners/_banner_iconsx3.scss */
.banner_iconsx3_wrapper .banner_iconsx3 .icon {
  display: inline-block;
  vertical-align: top;
  width: calc(100% / 3);
  padding: 0 50px;
}
/* line 18, ../sass/banners/_banner_iconsx3.scss */
.banner_iconsx3_wrapper .banner_iconsx3 .icon i, .banner_iconsx3_wrapper .banner_iconsx3 .icon img {
  display: block;
  padding: 20px;
  font-size: 40px;
  color: #11CCC7;
  text-align: center;
  margin: auto;
}
/* line 26, ../sass/banners/_banner_iconsx3.scss */
.banner_iconsx3_wrapper .banner_iconsx3 .icon .title {
  text-transform: uppercase;
  font-weight: bold;
  color: #002D42;
  padding-bottom: 10px;
}
/* line 32, ../sass/banners/_banner_iconsx3.scss */
.banner_iconsx3_wrapper .banner_iconsx3 .icon .desc {
  color: #99ABB3;
  font-size: 14px;
}
/* line 35, ../sass/banners/_banner_iconsx3.scss */
.banner_iconsx3_wrapper .banner_iconsx3 .icon .desc a {
  padding: 10px 0;
  display: block;
  color: #66818E;
  font-weight: bold;
}
/* line 40, ../sass/banners/_banner_iconsx3.scss */
.banner_iconsx3_wrapper .banner_iconsx3 .icon .desc a:hover {
  color: #11CCC7;
}

/* line 1, ../sass/banners/_banner_see_more_content.scss */
.see_more_banner {
  padding: 40px calc((100% - 1140px) / 2);
}
/* line 3, ../sass/banners/_banner_see_more_content.scss */
.see_more_banner .desc {
  line-height: 22px;
}
/* line 5, ../sass/banners/_banner_see_more_content.scss */
.see_more_banner .desc h2 {
  margin: 20px 0;
  font-size: 20px;
  font-weight: bold;
  line-height: 20px;
}
/* line 11, ../sass/banners/_banner_see_more_content.scss */
.see_more_banner .desc hide {
  display: none;
}
/* line 14, ../sass/banners/_banner_see_more_content.scss */
.see_more_banner .desc a {
  color: #002d42;
  font-weight: bold;
}
/* line 19, ../sass/banners/_banner_see_more_content.scss */
.see_more_banner .see_more {
  margin-top: 28px;
  font-weight: 600;
  cursor: pointer;
  display: block;
}
/* line 24, ../sass/banners/_banner_see_more_content.scss */
.see_more_banner .see_more:after {
  display: inline-block;
  vertical-align: middle;
  font-family: "Font Awesome 5 Pro";
  content: '\f105';
  color: #11ccc7;
  margin-left: 5px;
  transform: rotate(0);
  transition: all .4s;
}
/* line 34, ../sass/banners/_banner_see_more_content.scss */
.see_more_banner .see_more.active:after {
  transform: rotate(-90deg);
}

/* line 3, ../../../../sass/plugins/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 8, ../../../../sass/plugins/_mixins.scss */
.center_xy_before:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 13, ../../../../sass/plugins/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 17, ../../../../sass/plugins/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 21, ../../../../sass/plugins/_mixins.scss */
.center_y_before:before {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 25, ../../../../sass/plugins/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 30, ../../../../sass/plugins/_mixins.scss */
.fs:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
}

/* line 110, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:before, .icon-xcross:after {
  content: '';
  width: 100%;
  height: 2px;
  background: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 124, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -moz-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 61, ../../../../sass/plugins/_mixins.scss */
.display_flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* line 1, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown {
  margin: 40px 20px 20px;
  text-align: center;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  bottom: 180px;
  left: 48% !important;
  color: white;
}
/* line 9, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown div {
  display: inline-block;
  font-size: 16px;
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 0;
  padding-bottom: 0;
  text-transform: uppercase;
}
/* line 20, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .days {
  font-weight: 600;
}
/* line 23, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .date {
  display: block;
  font-size: 4.5rem;
}
/* line 28, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .title_format {
  font-weight: 600;
}

@media screen and (max-width: 800px) {
  /* line 35, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown {
    display: inline-block;
    width: 380px;
    top: 330px;
    left: 45% !important;
  }
  /* line 40, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown div {
    font-size: 10px;
  }
  /* line 43, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown .date {
    font-size: 2.5rem;
  }
}
/* line 3, ../sass/_bono_customized.scss */
.wrapper_gift_bono {
  max-width: 1140px;
  margin: auto;
  text-align: left;
  padding: 70px 0;
}
/* line 9, ../sass/_bono_customized.scss */
.wrapper_gift_bono .bono_title {
  margin-bottom: 30px;
}
/* line 12, ../sass/_bono_customized.scss */
.wrapper_gift_bono .bono_title .title {
  display: block;
  font-weight: 700;
  font-size: 40px;
  color: #002D42;
}
/* line 19, ../sass/_bono_customized.scss */
.wrapper_gift_bono .bono_title .subtitle {
  display: block;
  font-weight: 400;
  font-size: 30px;
  color: #11CCC7;
}

/* line 28, ../sass/_bono_customized.scss */
.gift_bono_content {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
}
/* line 34, ../sass/_bono_customized.scss */
.gift_bono_content .default_text {
  font-size: 16px;
  padding-bottom: 40px;
  line-height: 25px;
}
/* line 40, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper {
  width: calc((100% - 100px) / 2);
}
/* line 43, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper:nth-of-type(2) {
  padding-top: 70px;
}
/* line 47, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .prices_top_text {
  font-weight: 700;
  font-size: 26px;
  margin-bottom: 20px;
}
/* line 53, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .currency_selector_wrapper {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin: 10px 0 20px;
}
/* line 60, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .currency_selector_wrapper .select2-container--default {
  width: auto !important;
}
/* line 63, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .currency_selector_wrapper .select2-container--default .select2-selection--single {
  border-radius: 0;
  outline: none;
}
/* line 67, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .currency_selector_wrapper .select2-container--default .select2-selection--single .select2-selection__rendered {
  padding-left: 20px;
  padding-right: 35px;
  font-weight: 600;
}
/* line 73, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .currency_selector_wrapper .select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 2px;
  right: 10px;
}
/* line 77, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .currency_selector_wrapper .select2-container--default .select2-selection--single .select2-selection__arrow:before {
  content: "\f078";
  font-family: "Font Awesome 5 Pro";
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-weight: 300;
  color: #333;
  font-size: 17px;
}
/* line 86, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .currency_selector_wrapper .select2-container--default .select2-selection--single .select2-selection__arrow b {
  display: none;
}
/* line 92, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .currency_selector_wrapper .select2-container--default .select2-search--dropdown .select2-search__field {
  border: none;
  border-bottom: 2px solid #333;
}
/* line 97, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .currency_selector_wrapper .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow {
  transform: rotateX(180deg);
}
/* line 103, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .table_prices_wrapper {
  margin-bottom: 30px;
}
/* line 106, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .table_prices_wrapper .table_prices {
  display: grid;
  grid-template-columns: calc(100% / 3) calc(100% / 3) calc(100% / 3);
}
/* line 110, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .table_prices_wrapper .table_prices .price_cell {
  cursor: pointer;
  padding: 20px;
  text-align: center;
  font-size: 22px;
  font-weight: bold;
}
@media (min-width: 320px) and (max-width: 410px) {
  /* line 110, ../sass/_bono_customized.scss */
  .gift_bono_content .half_content_wrapper .table_prices_wrapper .table_prices .price_cell {
    padding: 20px 15px;
  }
}
/* line 121, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .table_prices_wrapper .table_prices .price_cell.other {
  background-color: #11CCC7;
  font-size: 21px !important;
}
@media (min-width: 320px) and (max-width: 410px) {
  /* line 121, ../sass/_bono_customized.scss */
  .gift_bono_content .half_content_wrapper .table_prices_wrapper .table_prices .price_cell.other {
    padding: 20px 0;
  }
}
/* line 133, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .discount_text {
  margin-bottom: 30px;
  text-align: center;
  font-style: italic;
  font-size: 18px;
}
/* line 140, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .button_bono {
  display: block;
  text-align: center;
}
/* line 144, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .button_bono .buy_bono {
  cursor: pointer;
  background: #11CCC7;
  border-radius: 50px;
  box-shadow: 0px 3px 6px #00000029;
  color: #002D42;
  font-size: 16px;
  appearance: none;
  border: none;
  padding: 10px 45px;
  text-transform: uppercase;
  font-weight: 600;
}
/* line 157, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .button_bono .buy_bono:hover {
  color: white;
}
/* line 161, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .button_bono .buy_bono.disabled {
  background: #11CCC7;
}
/* line 168, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .custom_price_wrapper {
  margin-bottom: 30px;
}
/* line 171, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .custom_price_wrapper .message {
  display: inline-block;
  vertical-align: middle;
  margin-right: 10px;
  font-weight: 700;
  font-size: 18px;
  letter-spacing: 0.5px;
}
/* line 180, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .custom_price_wrapper .custom_input_wrapper {
  display: inline-block;
  vertical-align: middle;
  position: relative;
}
/* line 185, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .custom_price_wrapper .custom_input_wrapper .input_price_custom {
  box-sizing: border-box;
  appearance: none;
  border: none;
  background-color: #F2F2F2;
  width: 85px;
  padding: 10px 20px 10px 15px;
  font-family: "Cera Pro", sans-serif;
  font-weight: 700;
  font-size: 16px;
  letter-spacing: 0.5px;
  color: #002D42;
  text-align: right;
}
/* line 200, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .custom_price_wrapper .custom_input_wrapper .input_currency {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 5px;
  font-weight: 700;
  font-size: 16px;
  letter-spacing: 0.5px;
}
/* line 210, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .gift_wrapper {
  background: #0A5689;
  background-position: center !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  height: 370px;
  border-radius: 25px;
  padding: 40px 60px;
  position: relative;
  margin: 20px 0;
  color: white;
}
/* line 222, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .gift_wrapper span {
  display: block;
  margin-top: 5px;
  margin-bottom: 50px;
  font-weight: 700;
  font-size: 20px;
  text-align: left;
  text-transform: uppercase;
}
@media (min-width: 320px) and (max-width: 410px) {
  /* line 222, ../sass/_bono_customized.scss */
  .gift_bono_content .half_content_wrapper .gift_wrapper span {
    margin-top: 15px;
  }
}
@media (min-width: 411px) and (max-width: 768px) {
  /* line 222, ../sass/_bono_customized.scss */
  .gift_bono_content .half_content_wrapper .gift_wrapper span {
    margin-left: 20px;
  }
}
/* line 240, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .gift_wrapper .logo_bono {
  max-height: 40px;
  margin-bottom: 40px;
}
/* line 245, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper {
  display: inline-block;
  position: relative;
}
@media (min-width: 411px) and (max-width: 768px) {
  /* line 245, ../sass/_bono_customized.scss */
  .gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper {
    margin-left: 20px;
  }
}
/* line 253, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper .hidden {
  visibility: hidden;
  position: absolute;
  pointer-events: none;
}
/* line 259, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper .message {
  display: none;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  top: -15px;
  width: 60%;
  text-align: left;
  font-size: 12px;
  white-space: nowrap;
}
/* line 269, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper .input_price, .gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper .input_price_custom {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  -o-appearance: none;
  appearance: none;
  border: 0;
  background: transparent;
  font-size: 42px;
  color: white;
  font-weight: bold;
  width: 130px;
  height: 60px;
  outline: none;
  text-align: right;
}
/* line 285, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper .input_price::placeholder, .gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper .input_price_custom::placeholder {
  color: white;
  white-space: pre-line;
  font-weight: lighter;
  position: relative;
  top: -12px;
  font-size: 18px;
  text-align: left;
}
/* line 296, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper .custom_price {
  display: inline-block;
  vertical-align: middle;
  max-width: 350px;
  overflow: hidden;
  font-weight: 700;
  font-size: 42px;
  color: white;
  text-overflow: ellipsis;
}
/* line 307, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper .input_price_custom {
  visibility: hidden !important;
  position: absolute !important;
  pointer-events: none !important;
}
/* line 313, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper .currency {
  display: inline-block;
  vertical-align: middle;
  font-weight: 700;
  font-size: 42px;
}
/* line 320, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper label.error {
  display: block;
  white-space: nowrap;
}
/* line 327, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .conditions_wrapper {
  margin-top: 40px;
}
/* line 330, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .conditions_wrapper .conditions_title {
  margin-bottom: 20px;
}
/* line 334, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .conditions_wrapper .condition {
  display: grid;
  grid-template-columns: 30px auto;
  align-items: center;
  font-size: 15px;
  margin: 10px 0;
}
/* line 341, ../sass/_bono_customized.scss */
.gift_bono_content .half_content_wrapper .conditions_wrapper .condition i {
  vertical-align: middle;
  color: #11CCC7;
}

/* line 350, ../sass/_bono_customized.scss */
.wrapper_gift_bono.mobile_v {
  padding: 70px 20px 20px;
}
/* line 353, ../sass/_bono_customized.scss */
.wrapper_gift_bono.mobile_v .title {
  margin: auto;
}
/* line 357, ../sass/_bono_customized.scss */
.wrapper_gift_bono.mobile_v .gift_bono_content {
  flex-flow: column;
}
/* line 360, ../sass/_bono_customized.scss */
.wrapper_gift_bono.mobile_v .gift_bono_content .half_content_wrapper {
  padding-top: 0 !important;
  width: auto;
}
/* line 364, ../sass/_bono_customized.scss */
.wrapper_gift_bono.mobile_v .gift_bono_content .half_content_wrapper .discount_text {
  font-size: 15px;
}
/* line 368, ../sass/_bono_customized.scss */
.wrapper_gift_bono.mobile_v .gift_bono_content .half_content_wrapper .gift_wrapper {
  height: 200px;
  padding: 20px 35px;
}
/* line 373, ../sass/_bono_customized.scss */
.wrapper_gift_bono.mobile_v .gift_bono_content .half_content_wrapper .gift_wrapper .title_wrapper .title_info {
  font-size: 16px;
  margin-bottom: 20px;
}
/* line 379, ../sass/_bono_customized.scss */
.wrapper_gift_bono.mobile_v .gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper {
  vertical-align: middle;
}
/* line 382, ../sass/_bono_customized.scss */
.wrapper_gift_bono.mobile_v .gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper .custom_price {
  max-width: 250px;
}
/* line 386, ../sass/_bono_customized.scss */
.wrapper_gift_bono.mobile_v .gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper .input_price {
  width: 90px;
  font-size: 32px;
  padding: 0;
}
/* line 392, ../sass/_bono_customized.scss */
.wrapper_gift_bono.mobile_v .gift_bono_content .half_content_wrapper .gift_wrapper .input_wrapper label.error {
  left: 20%;
  font-size: 12px;
}
/* line 398, ../sass/_bono_customized.scss */
.wrapper_gift_bono.mobile_v .gift_bono_content .half_content_wrapper .gift_wrapper .button_bono {
  float: none;
}
/* line 401, ../sass/_bono_customized.scss */
.wrapper_gift_bono.mobile_v .gift_bono_content .half_content_wrapper .gift_wrapper .button_bono .buy_bono {
  font-size: 15px;
  padding: 10px 25px;
}
/* line 408, ../sass/_bono_customized.scss */
.wrapper_gift_bono.mobile_v .gift_bono_content .half_content_wrapper .conditions_wrapper {
  text-align: left;
}

/* line 416, ../sass/_bono_customized.scss */
.select2-container.select2-container--default .select2-search--dropdown .select2-search__field {
  border: none;
  border-bottom: 2px solid #333;
}
/* line 420, ../sass/_bono_customized.scss */
.select2-container.select2-container--default .select2-search--dropdown .select2-search__field:focus {
  outline: none;
}
/* line 425, ../sass/_bono_customized.scss */
.select2-container.select2-container--default .select2-results__option--disabled {
  display: none;
}

/* line 431, ../sass/_bono_customized.scss */
.select2-results strong {
  font-weight: 600 !important;
}

/* line 1, ../sass/_template_specific.scss */
.hide_element {
  display: none;
}

/* line 5, ../sass/_template_specific.scss */
#full_wrapper_booking.d-none {
  display: none;
}

/* line 9, ../sass/_template_specific.scss */
.pdf_ico_download {
  padding-left: 40px;
  position: relative;
}
/* line 13, ../sass/_template_specific.scss */
.pdf_ico_download:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  background: #11CCC7;
  width: 30px;
  height: 30px;
  border-radius: 30px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 25, ../sass/_template_specific.scss */
.pdf_ico_download:after {
  content: '\f1c1';
  font-family: "Font Awesome 5 Pro";
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  position: absolute;
  color: #002D42;
  font-size: 17px;
  left: 10px;
  top: 0;
}

/* line 43, ../sass/_template_specific.scss */
.circle_wrapper_corporate {
  content: '';
  background: #11CCC7;
  width: 30px;
  height: 30px;
  border-radius: 30px;
  position: relative;
  vertical-align: middle;
}
/* line 52, ../sass/_template_specific.scss */
.circle_wrapper_corporate:before {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

/* line 62, ../sass/_template_specific.scss */
body {
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-size: 16px;
  color: #002D42;
  overflow-x: hidden;
  /* Mis reservas */
  /* Iframe Map */
}
/* line 67, ../sass/_template_specific.scss */
body.ona_traveler header {
  padding-bottom: 0;
}
/* line 71, ../sass/_template_specific.scss */
body.ona_traveler .hotel_info_wrapper,
body.ona_traveler footer:not(.footer_goup_link) {
  display: none;
}
/* line 76, ../sass/_template_specific.scss */
body.ona_traveler #slider_container {
  position: absolute;
  bottom: 300%;
}
/* line 81, ../sass/_template_specific.scss */
body.ona_traveler input[type="checkbox"] {
  -webkit-appearance: checkbox;
}
/* line 88, ../sass/_template_specific.scss */
body.webseeker_section.inner_section.inner_section_hotel header.fixed {
  display: flex;
  align-items: center;
}
/* line 92, ../sass/_template_specific.scss */
body.webseeker_section.inner_section.inner_section_hotel header.fixed .float_left,
body.webseeker_section.inner_section.inner_section_hotel header.fixed .float_right {
  width: 50%;
}
/* line 100, ../sass/_template_specific.scss */
body.webseeker_section.inner_section.inner_section_hotel #slider_container .inner_slider .innner_slider_carousel .caption_slider .cartela1 {
  width: 860px;
  left: 50%;
  transform: translate(-50%, -50%);
  top: calc(100% - 230px);
}
/* line 107, ../sass/_template_specific.scss */
body.webseeker_section.inner_section.inner_section_hotel #slider_container .inner_slider .innner_slider_carousel .caption_slider .cartela1 .title .reduce_title {
  font-size: 65px;
}
/* line 113, ../sass/_template_specific.scss */
body.webseeker_section.inner_section.inner_section_hotel #slider_container .inner_slider .innner_slider_carousel .owl-dots {
  display: none !important;
}
@media (max-height: 625px) {
  /* line 123, ../sass/_template_specific.scss */
  body:not(.webseeker_section) #slider_container .cartela1.v2 h2 span {
    font-size: 20px !important;
    text-shadow: #171a1e 3px 0px 0px, #171a1e 2.83487px 0.98158px 0px, #171a1e 2.35766px 1.85511px 0px, #171a1e 1.62091px 2.52441px 0px, #171a1e 0.70571px 2.91581px 0px, #171a1e -0.28717px 2.98622px 0px, #171a1e -1.24844px 2.72789px 0px, #171a1e -2.07227px 2.16926px 0px, #171a1e -2.66798px 1.37182px 0px, #171a1e -2.96998px 0.42336px 0px, #171a1e -2.94502px -0.5717px 0px, #171a1e -2.59586px -1.50383px 0px, #171a1e -1.96093px -2.27041px 0px, #171a1e -1.11013px -2.78704px 0px, #171a1e -0.13712px -2.99686px 0px, #171a1e 0.85099px -2.87677px 0px, #171a1e 1.74541px -2.43999px 0px, #171a1e 2.44769px -1.73459px 0px, #171a1e 2.88051px -0.83825px 0px;
  }
  /* line 127, ../sass/_template_specific.scss */
  body:not(.webseeker_section) #slider_container .cartela1.v2 h2 span .big {
    font-size: 34px;
  }
  /* line 132, ../sass/_template_specific.scss */
  body:not(.webseeker_section) #slider_container .cartela1.v2 .cartela_button {
    transform: translateY(-20px);
  }
}
/* line 140, ../sass/_template_specific.scss */
body * {
  box-sizing: border-box;
  font-family: "Cera Pro", sans-serif;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}
/* line 147, ../sass/_template_specific.scss */
body a {
  text-decoration: none;
}
/* line 151, ../sass/_template_specific.scss */
body strong, body b {
  font-weight: bold;
}
/* line 155, ../sass/_template_specific.scss */
body > img[src*="pixel"], body > img[src*="analytics"] {
  display: none;
}
/* line 160, ../sass/_template_specific.scss */
body .aviso_cookie {
  position: fixed;
  top: auto;
  bottom: 10px;
  left: 10px;
  width: 530px;
  height: auto;
  padding: 20px 30px;
  background: rgba(0, 0, 0, 0.8);
}
/* line 170, ../sass/_template_specific.scss */
body .aviso_cookie p {
  padding: 0;
  text-align: left;
  line-height: 20px;
}
/* line 177, ../sass/_template_specific.scss */
body .preloading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: fixed;
  z-index: 1100;
  background: white;
}
/* line 183, ../sass/_template_specific.scss */
body .preloading .waves {
  position: absolute;
  top: 10%;
  left: 20%;
  right: 50%;
  display: block;
  height: 200px;
  background: url("/img/onac2/wave-patern-big.png");
  background-attachment: fixed;
}
/* line 194, ../sass/_template_specific.scss */
body .preloading .waves2 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: block;
  height: 100px;
  background: url("/img/onac2/wave-patern.png");
  background-attachment: fixed;
}
/* line 205, ../sass/_template_specific.scss */
body .preloading .ona-o {
  font-weight: bold;
  font-size: 700px;
  color: #11CCC7;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 30px;
}
/* line 213, ../sass/_template_specific.scss */
body .preloading .loading {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 60%;
  right: 10%;
  height: 1px;
  background: #CFF5F4;
}
/* line 220, ../sass/_template_specific.scss */
body .preloading .loading:before {
  content: '';
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  display: block;
  height: 3px;
  width: 0;
  background: #11CCC7;
  animation: preloading 5s infinite;
}
/* line 233, ../sass/_template_specific.scss */
body .content_access > div:not(.wrapper_gift_bono) {
  display: none;
}
/* line 239, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .modify_reservation_widget {
  margin: auto;
  margin-top: 40px;
  margin-bottom: 0;
  padding: 20px;
}
/* line 245, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .modify_reservation_widget #info_ninos {
  display: none !important;
}
/* line 249, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .modify_reservation_widget #contenedor_opciones {
  margin: 0 auto 10px;
}
/* line 252, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .modify_reservation_widget #contenedor_opciones #hab1, body #my-bookings-form #reservation .modify_reservation_widget #contenedor_opciones #hab2, body #my-bookings-form #reservation .modify_reservation_widget #contenedor_opciones #hab3 {
  width: 350px;
}
/* line 256, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .modify_reservation_widget #contenedor_opciones .ninos-con-babies {
  margin-right: 15px;
}
/* line 261, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .modify_reservation_widget #contenedor_fechas {
  text-align: center;
}
/* line 264, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .modify_reservation_widget #contenedor_fechas #fecha_entrada, body #my-bookings-form #reservation .modify_reservation_widget #contenedor_fechas #fecha_salida {
  display: inline-block;
  float: none;
}
/* line 270, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .modify_reservation_widget #contenedor_habitaciones {
  text-align: center;
}
/* line 273, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .modify_reservation_widget #contenedor_habitaciones label {
  display: inline-block;
  float: none;
}
/* line 278, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .modify_reservation_widget #contenedor_habitaciones select {
  display: inline-block;
  float: none;
}
/* line 284, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .modify_reservation_widget #envio {
  text-align: center;
}
/* line 287, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .modify_reservation_widget #envio input {
  margin: auto auto 20px auto;
  width: 100%;
}
/* line 292, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .modify_reservation_widget #envio button {
  border-radius: 5px;
}
/* line 298, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .my-bookings-booking-info {
  margin: 40px auto 0;
}
/* line 301, ../sass/_template_specific.scss */
body #my-bookings-form #reservation .my-bookings-booking-info .fResumenReserva {
  margin: auto;
}
/* line 307, ../sass/_template_specific.scss */
body #my-bookings-form #modify-button-container {
  display: none;
}
/* line 312, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields label {
  display: block;
  text-align: center;
  text-transform: uppercase;
  color: #002D42;
  font-weight: 500;
}
/* line 320, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields input, body #my-bookings-form #my-bookings-form-fields select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: block;
  width: 300px;
  margin: 10px auto;
  height: 40px;
  border-radius: 0;
  text-align: center;
  font-size: 14px;
  border: 1px solid #70E0DD;
}
/* line 334, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields select {
  padding: 0 0 0 15px;
  background: #ECFBFB;
}
/* line 339, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields ul {
  text-align: center;
  margin-top: 30px;
}
/* line 343, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields ul li {
  display: inline-block;
  width: 200px;
  vertical-align: middle;
}
/* line 348, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields ul li button {
  height: 40px;
  border-radius: 20px;
  text-transform: uppercase;
  font-size: 16px;
  color: white;
  border: 0;
  cursor: pointer;
  width: 100%;
  font-weight: 500;
  background: #002D42;
  -webkit-transition: background 0.4s;
  -moz-transition: background 0.4s;
  -ms-transition: background 0.4s;
  -o-transition: background 0.4s;
  transition: background 0.4s;
}
/* line 361, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields ul li button.modify-reservation {
  background: #002D42;
}
/* line 364, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields ul li button.modify-reservation:hover {
  color: #11CCC7;
}
/* line 369, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields ul li button.searchForReservation {
  background: #11CCC7;
}
/* line 372, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields ul li button.searchForReservation:hover {
  color: #002D42;
}
/* line 380, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields .custom_checks {
  width: 300px;
  margin: 0 auto;
}
/* line 384, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields .custom_checks .checkbox_wrapper {
  width: 100%;
  margin: 10px 0;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
}
/* line 391, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields .custom_checks .checkbox_wrapper input[type="checkbox"] {
  position: relative;
  width: 12px;
  height: 12px;
  margin: 5px 10px 0 0;
}
/* line 398, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields .custom_checks .checkbox_wrapper input[type="checkbox"]:checked:before {
  content: '';
  width: 6px;
  height: 6px;
  background: #002D42;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 408, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields .custom_checks .checkbox_wrapper label {
  flex: 1;
  font-weight: 400;
  font-size: 12px;
  color: black;
  text-transform: none;
  text-align: left;
}
/* line 418, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields .custom_checks .privacy_link {
  width: 100%;
  margin: 10px 0;
  font-weight: 400;
  font-size: 12px;
  color: black;
}
/* line 425, ../sass/_template_specific.scss */
body #my-bookings-form #my-bookings-form-fields .custom_checks .privacy_link a {
  color: #11CCC7;
}
/* line 432, ../sass/_template_specific.scss */
body #my-bookings-form #cancelButton {
  display: none;
  background: #11CCC7;
  height: 40px;
  text-transform: uppercase;
  font-size: 16px;
  color: white;
  border: 0;
  cursor: pointer;
  width: 200px;
  font-weight: 100;
  margin: 40px auto 0;
  -webkit-transition: background 0.4s;
  -moz-transition: background 0.4s;
  -ms-transition: background 0.4s;
  -o-transition: background 0.4s;
  transition: background 0.4s;
}
/* line 446, ../sass/_template_specific.scss */
body #my-bookings-form #cancelButton:hover {
  background: #0d9d99;
}
/* line 455, ../sass/_template_specific.scss */
body .iframe_map_wrapper iframe {
  width: 100%;
}
/* line 460, ../sass/_template_specific.scss */
body .extra_banner {
  width: 300px;
  text-align: center;
  padding: 30px 20px;
}
/* line 465, ../sass/_template_specific.scss */
body .extra_banner.extra_banner_class1 {
  position: relative;
}
/* line 468, ../sass/_template_specific.scss */
body .extra_banner.extra_banner_class1 .main_icon, body .extra_banner.extra_banner_class1 .main_img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 471, ../sass/_template_specific.scss */
body .extra_banner.extra_banner_class1 .main_icon img, body .extra_banner.extra_banner_class1 .main_img img {
  max-width: 100%;
}
/* line 476, ../sass/_template_specific.scss */
body .extra_banner.extra_banner_class1 .label {
  position: absolute;
  top: 0;
  left: 0;
  display: inline-block;
  background: white;
  border: 1px solid #002D42;
  color: #002D42;
  padding: 5px 20px;
  font-weight: normal;
  font-family: "Cera Pro", sans-serif;
  font-size: 16px;
  color: #002D42;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 12px;
  font-size: 10px;
}
/* line 489, ../sass/_template_specific.scss */
body .extra_banner.extra_banner_class1 .title, body .extra_banner.extra_banner_class1 .desc {
  position: relative;
  z-index: 2;
}
/* line 494, ../sass/_template_specific.scss */
body .extra_banner.extra_banner_class1 .banner_link {
  position: absolute;
  bottom: 15px;
  right: 15px;
  font-size: 12px;
  color: white;
  padding-right: 30px;
}
/* line 502, ../sass/_template_specific.scss */
body .extra_banner.extra_banner_class1 .banner_link:hover {
  padding-right: 50px;
}
/* line 506, ../sass/_template_specific.scss */
body .extra_banner.extra_banner_class1 .banner_link:before {
  border-color: white;
}
/* line 510, ../sass/_template_specific.scss */
body .extra_banner.extra_banner_class1 .banner_link:after {
  background: white;
  width: 20px;
}
/* line 519, ../sass/_template_specific.scss */
body .extra_banner.extra_banner_promocode .promocode span {
  margin: 10px auto 20px;
  display: block;
  width: 160px;
  text-align: center;
  border-width: 0 0 1px 0;
  border-style: solid;
  border-color: transparent;
  border-image: 16 repeating-linear-gradient(90deg, #002D42 0, #002D42 0.6em, transparent 0, transparent 1.2em);
  padding: 5px 10px;
}
/* line 532, ../sass/_template_specific.scss */
body .extra_banner.extra_banner_promocode button {
  position: relative;
  color: #002D42;
  font-size: 20px;
  font-weight: bold;
  padding-right: 50px;
  -webkit-transition: padding 0.6s;
  -moz-transition: padding 0.6s;
  -ms-transition: padding 0.6s;
  -o-transition: padding 0.6s;
  transition: padding 0.6s;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: transparent;
  border-width: 0;
}
/* line 222, ../sass/_defaults.scss */
body .extra_banner.extra_banner_promocode button:hover {
  padding-right: 60px;
}
/* line 225, ../sass/_defaults.scss */
body .extra_banner.extra_banner_promocode button:before {
  content: '';
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%) rotate(45deg);
  -moz-transform: translateY(-50%) rotate(45deg);
  -ms-transform: translateY(-50%) rotate(45deg);
  -o-transform: translateY(-50%) rotate(45deg);
  transform: translateY(-50%) rotate(45deg);
  right: 0;
  width: 7px;
  height: 7px;
  border: 1px solid #002D42;
  border-bottom-width: 0;
  border-left-width: 0;
}
/* line 241, ../sass/_defaults.scss */
body .extra_banner.extra_banner_promocode button:after {
  content: '';
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 0;
  display: inline-block;
  vertical-align: middle;
  height: 1px;
  background: #002D42;
  width: 40px;
}
/* line 541, ../sass/_template_specific.scss */
body .extra_banner.extra_banner_promocode h3, body .extra_banner.extra_banner_promocode #main_menu .main_menu .main-section-div-wrapper a, #main_menu .main_menu .main-section-div-wrapper body .extra_banner.extra_banner_promocode a {
  color: #002D42;
  margin-bottom: 10px;
}
/* line 551, ../sass/_template_specific.scss */
body.christmas .start_date_personalized:before, body.christmas .end_date_personalized:before {
  color: #af8400 !important;
}
/* line 556, ../sass/_template_specific.scss */
body.christmas .promocode_input {
  border: 1px solid #af8400 !important;
}
/* line 561, ../sass/_template_specific.scss */
body.christmas .wrapper_booking_button .submit_button {
  background: #af8400 !important;
  color: white !important;
}
/* line 568, ../sass/_template_specific.scss */
body.christmas #content .hotel_info_wrapper {
  padding-top: 65px;
}
/* line 573, ../sass/_template_specific.scss */
body.christmas #content .hotel_info_wrapper .hotel_info .hotel_info_title h1 {
  font-family: 'Satisfy', cursive;
  color: #af8400;
  font-weight: normal;
  text-align: center;
  font-size: 65px;
}
/* line 580, ../sass/_template_specific.scss */
body.christmas #content .hotel_info_wrapper .hotel_info .hotel_info_title h1:before {
  content: '';
  display: inline-block;
  width: 160px;
  margin-right: 30px;
  height: 1px;
  background-color: #af8400;
  margin-bottom: 10px;
}
/* line 590, ../sass/_template_specific.scss */
body.christmas #content .hotel_info_wrapper .hotel_info .hotel_info_title h1:after {
  content: '';
  display: inline-block;
  width: 160px;
  margin-left: 40px;
  height: 1px;
  background-color: #af8400;
  margin-bottom: 10px;
}
/* line 603, ../sass/_template_specific.scss */
body.christmas #content .hotel_info_wrapper .hotel_info .description .content {
  text-align: center;
  letter-spacing: 1px;
  line-height: 38px;
}
/* line 608, ../sass/_template_specific.scss */
body.christmas #content .hotel_info_wrapper .hotel_info .description .content span {
  font-style: italic;
  font-weight: 300;
}
/* line 613, ../sass/_template_specific.scss */
body.christmas #content .hotel_info_wrapper .hotel_info .description .content strong {
  font-family: 'Satisfy', cursive;
  color: #af8400;
  font-size: 25px;
  font-weight: bold;
}
/* line 622, ../sass/_template_specific.scss */
body.christmas #content .hotel_info_wrapper .hotel_info .wave-navy-small {
  display: none;
}
/* line 628, ../sass/_template_specific.scss */
body.christmas #content .filters {
  display: flex;
  justify-content: space-between;
  padding: 0;
  margin-bottom: 30px;
}
/* line 635, ../sass/_template_specific.scss */
body.christmas #content .filters .filter.selected {
  color: #af8400;
  font-weight: bold;
}
/* line 639, ../sass/_template_specific.scss */
body.christmas #content .filters .filter.selected:before {
  border: 1px solid #af8400;
}
/* line 644, ../sass/_template_specific.scss */
body.christmas #content .filters .filter:hover::before {
  border: 1px solid #af8400;
}
/* line 651, ../sass/_template_specific.scss */
body.christmas .banner_x2_full_wrapper .banner_x2_wrapper .banner .banner_content .title {
  font-family: 'Satisfy', cursive;
}
/* line 659, ../sass/_template_specific.scss */
body.semana_santa .banner_pic_wrapper, body.landing_verano .banner_pic_wrapper {
  padding: 90px calc((100% - 1140px) / 2) 70px;
  justify-content: space-between;
}
/* line 663, ../sass/_template_specific.scss */
body.semana_santa .banner_pic_wrapper .banner_pic, body.landing_verano .banner_pic_wrapper .banner_pic {
  margin-top: 0;
  width: 43%;
}
/* line 667, ../sass/_template_specific.scss */
body.semana_santa .banner_pic_wrapper .banner_pic img, body.landing_verano .banner_pic_wrapper .banner_pic img {
  height: 100%;
}
/* line 672, ../sass/_template_specific.scss */
body.semana_santa .banner_pic_wrapper .banner_pic_content, body.landing_verano .banner_pic_wrapper .banner_pic_content {
  width: 48%;
}
/* line 678, ../sass/_template_specific.scss */
body.semana_santa .banner_carousel_wrapper .owl-stage-outer, body.landing_verano .banner_carousel_wrapper .owl-stage-outer {
  height: 325px !important;
}
/* line 683, ../sass/_template_specific.scss */
body.semana_santa .banner_destinos_wrapper, body.landing_verano .banner_destinos_wrapper {
  margin-bottom: 50px !important;
}
/* line 687, ../sass/_template_specific.scss */
body.semana_santa .baner_hotels, body.landing_verano .baner_hotels {
  padding: 0 calc((100% - 1140px) / 2) !important;
  margin: 50px 0 !important;
}
/* line 697, ../sass/_template_specific.scss */
body.landing_verano .banner_pic_wrapper .banner_pic_content .title h1 {
  font-size: 43px;
}
/* line 706, ../sass/_template_specific.scss */
body.reservas_ona header, body.reservas_ona #slider_container, body.reservas_ona #footer {
  display: none;
}
/* line 712, ../sass/_template_specific.scss */
body.hide_loading_popup .fancy-booking-search_v2 {
  display: none !important;
}

/* line 718, ../sass/_template_specific.scss */
.fa-linkedin:before {
  content: "\f0e1" !important;
}

/* line 722, ../sass/_template_specific.scss */
.fa-instagram {
  font-size: 0;
  width: 40px;
  height: 40px;
  -webkit-mask-image: url(https://storage.googleapis.com/cdn.paraty.es/ona-corporativa/files/instagram.svg);
  -webkit-mask-repeat: no-repeat;
  background-color: #002D42;
  -webkit-mask-size: contain;
}

/* line 734, ../sass/_template_specific.scss */
#main_menu .main_menu .social a:hover .fa-instagram,
footer#footer .footer_menu .social a:hover .fa-instagram {
  background-color: #fff;
}

/* line 741, ../sass/_template_specific.scss */
#main_menu .banner_extra_menu .st0 {
  transform: scale(0.7);
}

/* line 747, ../sass/_template_specific.scss */
.automatic_floating_picture {
  left: auto !important;
  right: 32px !important;
  transition: all 0.5s;
}
/* line 752, ../sass/_template_specific.scss */
.automatic_floating_picture a {
  display: block;
}
/* line 756, ../sass/_template_specific.scss */
.automatic_floating_picture .open_popup_club {
  cursor: pointer;
}
/* line 760, ../sass/_template_specific.scss */
.automatic_floating_picture .close_automatic_floating {
  position: absolute;
  top: 0;
  right: 0;
  opacity: 1;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2006 !important;
  color: #002D42;
  cursor: pointer;
}
/* line 775, ../sass/_template_specific.scss */
.automatic_floating_picture.hide {
  opacity: 0;
  transform: translateX(100%);
}

/* line 782, ../sass/_template_specific.scss */
body.inner_section.black_friday .banner_pic_wrapper {
  margin-top: -50px;
  padding: 0 calc((100% - 1040px) / 2);
}
/* line 785, ../sass/_template_specific.scss */
body.inner_section.black_friday .banner_pic_wrapper .banner_pic {
  justify-content: stretch;
}
/* line 788, ../sass/_template_specific.scss */
body.inner_section.black_friday .banner_pic_wrapper .banner_pic .picture_content img {
  width: 60%;
}

/* line 798, ../sass/_template_specific.scss */
.popup-container .popup-restart-password-form .input-form[name="repeat-password"] {
  letter-spacing: 3px !important;
  font-size: 20px !important;
}
/* line 801, ../sass/_template_specific.scss */
.popup-container .popup-restart-password-form .input-form[name="repeat-password"]::-webkit-input-placeholder {
  letter-spacing: initial !important;
  font-size: 15px !important;
}
/* line 806, ../sass/_template_specific.scss */
.popup-container .popup-restart-password-form .btn-send-form {
  border-radius: 50px !important;
  background: #11CCC7 !important;
  font-size: 14px !important;
  padding: 5px 5px !important;
  letter-spacing: 0.6px !important;
  text-transform: uppercase !important;
  font-weight: 500 !important;
  color: #003865 !important;
  border: 0 !important;
}
/* line 816, ../sass/_template_specific.scss */
.popup-container .popup-restart-password-form .btn-send-form:hover {
  opacity: .7;
}

/* line 9, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_pic_wrapper .banner_pic {
  width: 500px;
  margin-top: 0;
}
/* line 13, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_pic_wrapper .banner_pic img {
  object-fit: contain;
}
/* line 18, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_pic_wrapper .banner_pic_content {
  width: calc(100% - 500px - 75px);
}
/* line 21, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_pic_wrapper .banner_pic_content .title {
  text-align: center;
}
/* line 24, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_pic_wrapper .banner_pic_content .title h1 {
  margin-bottom: 30px;
  font-family: "Lovely Home";
  font-weight: 400;
  color: #FF6666;
}
/* line 31, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_pic_wrapper .banner_pic_content .title h4, body.san_valentin .banner_pic_wrapper .banner_pic_content .title #main_menu .main_menu .social a, #main_menu .main_menu .social body.san_valentin .banner_pic_wrapper .banner_pic_content .title a {
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 700;
}
/* line 37, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_pic_wrapper .banner_pic_content .title h4::before, body.san_valentin .banner_pic_wrapper .banner_pic_content .title #main_menu .main_menu .social a::before, #main_menu .main_menu .social body.san_valentin .banner_pic_wrapper .banner_pic_content .title a::before, body.san_valentin .banner_pic_wrapper .banner_pic_content .title h4::after, body.san_valentin .banner_pic_wrapper .banner_pic_content .title #main_menu .main_menu .social a::after, #main_menu .main_menu .social body.san_valentin .banner_pic_wrapper .banner_pic_content .title a::after {
  content: '';
  display: block;
  flex: 1;
  height: 3px;
  margin: 0 20px;
  border-radius: 50%;
  background-color: #FF6666;
}
/* line 50, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_pic_wrapper .banner_pic_content .desc {
  line-height: 45px;
  text-align: justify;
}
/* line 57, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_x2_full_wrapper {
  position: relative;
  padding: 70px calc((100% - 1140px) / 2);
  margin-top: 50px;
}
/* line 62, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_x2_full_wrapper::before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  height: 3px;
  width: 1000px;
  border-radius: 50%;
  background-color: #FF6666;
}
/* line 76, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_x2_full_wrapper .banner .image_wrapper {
  height: 300px;
}
/* line 79, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_x2_full_wrapper .banner .image_wrapper .picture_text {
  left: 0;
  right: 0;
  padding: 30px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 60%, rgba(0, 0, 0, 0) 100%) !important;
}
/* line 86, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_x2_full_wrapper .banner .image_wrapper .btn_promotion_wrapper {
  position: absolute;
  top: auto;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.25) 60%, rgba(0, 0, 0, 0) 100%) !important;
}
/* line 98, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_x2_full_wrapper .banner .image_wrapper .btn_promotion_wrapper .button_promotion {
  position: static;
  transform: none;
  width: auto;
  height: auto;
  background: none;
  padding: 5px 20px;
  background-color: #11CCC7;
  border-radius: 10px;
  opacity: 1;
  transition: background-color 0.3s;
}
/* line 110, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_x2_full_wrapper .banner .image_wrapper .btn_promotion_wrapper .button_promotion:hover {
  background-color: #002D42;
}
/* line 114, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_x2_full_wrapper .banner .image_wrapper .btn_promotion_wrapper .button_promotion span {
  position: static;
  transform: none;
  font-size: 18px;
}
/* line 121, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_x2_full_wrapper .banner .image_wrapper .btn_promotion_wrapper .since_price {
  font-weight: 700;
  line-height: 1;
  color: white;
}
/* line 126, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_x2_full_wrapper .banner .image_wrapper .btn_promotion_wrapper .since_price .since_price_label {
  font-size: 14px;
  text-transform: capitalize;
}
/* line 131, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_x2_full_wrapper .banner .image_wrapper .btn_promotion_wrapper .since_price .since_price_value {
  font-size: 30px;
}
/* line 138, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_x2_full_wrapper .banner .banner_content {
  background-color: #FAFAFA;
  padding: 20px;
}
/* line 142, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_x2_full_wrapper .banner .banner_content .title {
  font-family: "Cera Pro", sans-serif;
  font-weight: 700;
  font-size: 20px;
  color: #11CCC7;
}
/* line 150, ../sass/_seasonal_customizations.scss */
body.san_valentin .banner_x2_full_wrapper .banner .banner_content .btn_personalized_1::after {
  background-color: rgba(255, 102, 102, 0.7);
}

/* line 163, ../sass/_seasonal_customizations.scss */
body.vacacionas .hotel_info .wave-navy-small {
  display: none;
}
/* line 168, ../sass/_seasonal_customizations.scss */
body.vacacionas .banner_ticks_wrapper.v2 {
  padding-top: 100px;
  border-top: 100px solid #F29365;
}

/* line 177, ../sass/_seasonal_customizations.scss */
body.family_hotels .hotel_info_wrapper .hotel_info .wave-navy-small {
  display: none;
}
/* line 183, ../sass/_seasonal_customizations.scss */
body.family_hotels .banner_destinos_wrapper {
  margin-bottom: 20px;
}
/* line 186, ../sass/_seasonal_customizations.scss */
body.family_hotels .banner_destinos_wrapper .banner_title {
  margin-bottom: 20px;
}
/* line 192, ../sass/_seasonal_customizations.scss */
body.family_hotels .page.active section.baner_hotels {
  margin: 20px 0 80px 0;
}
/* line 197, ../sass/_seasonal_customizations.scss */
body.family_hotels .banner_especial_promotion_wrapper_inline {
  padding: 0 0 10px;
}

/* line 3, ../sass/sections/escapadas_otono.scss */
body.escapadas_otono .banner_pic_wrapper.landing_v2 {
  min-height: 370px;
  padding: 30px calc((100% - 1190px) / 2) 20px;
}
/* line 7, ../sass/sections/escapadas_otono.scss */
body.escapadas_otono .banner_pic_wrapper.landing_v2 .banner_pic {
  margin-top: 20px;
}
/* line 11, ../sass/sections/escapadas_otono.scss */
body.escapadas_otono .banner_pic_wrapper.landing_v2.otono_section {
  background-image: url("https://cdn2.paraty.es/ona-corporativa/images/15df2aca38e52d1");
  padding: 85px calc((100% - 1190px) / 2) 95px;
}
/* line 15, ../sass/sections/escapadas_otono.scss */
body.escapadas_otono .banner_pic_wrapper.landing_v2.otono_section .banner_pic_content .desc {
  padding-top: 20px;
}
/* line 21, ../sass/sections/escapadas_otono.scss */
body.escapadas_otono .banner_pic_wrapper.landing_v2.free_kid .banner_pic {
  margin-top: -20px;
}
/* line 24, ../sass/sections/escapadas_otono.scss */
body.escapadas_otono .banner_pic_wrapper.landing_v2.free_kid .banner_pic img {
  position: inherit;
}
/* line 31, ../sass/sections/escapadas_otono.scss */
body.escapadas_otono .wave-navy {
  width: 200px;
  transform: scale(0.5);
  position: relative;
  right: 47px;
}
/* line 38, ../sass/sections/escapadas_otono.scss */
body.escapadas_otono .banner_destinos_wrapper {
  max-width: none;
  padding: 40px 104px;
  margin: 30px auto;
}
/* line 43, ../sass/sections/escapadas_otono.scss */
body.escapadas_otono .banner_destinos_wrapper .banner_tabs {
  max-width: 1140px;
  margin: 0 auto 55px;
}
/* line 48, ../sass/sections/escapadas_otono.scss */
body.escapadas_otono .banner_destinos_wrapper .destinations_wrapper .destiny_wrapper .desc {
  background-color: #F6F7F7;
}
/* line 53, ../sass/sections/escapadas_otono.scss */
body.escapadas_otono i:before {
  font-family: "Font Awesome 5 Pro";
}
