<div class="rooms_background"></div>
{% if section_content %}
        <div class="location_content">
            {% if section_content.subtitle %}
                <h2 class="section-subtitle">{{ section_content.subtitle|safe }}</h2>
            {% else %}
                <h1 class="section-title">{{ section_content.title|safe }}</h1>
            {% endif %}

            {% if section_content.content %}
                <div class="contact_content_element">
                    {{ section_content.content|safe }}
                </div>
            {% endif %}
        </div>
    {% endif %}
<div class="rooms_wrapper">
{% for room in rooms_information %}
    <div class="room_block">
        <div class="room_picture">
                <img data-src="{{room.servingUrl|safe}}">
        </div>
        <div class="room_info">
            {% if use_h2 %}
                <h2>{{room.title|safe}}</h2>
            {% else %}
                <h1>{{room.title|safe}}</h1>
            {% endif %}
            <div class="room_description" style="display:none;">{{room.description|safe}}</div>
        </div>
        <div class="buttons">
            <a {% if room.linkUrl and not 'http_' in room.linkUrl %} href="{{room.linkUrl}}" {% else %} onclick="return" {% endif %} id="room_link" class="room_link">
                <i class="fa fa-plus"></i>
            </a><!--
            --><a class="button-promotion" onclick="return" {% if room.room_filter %}room_filter="{{ room.room_filter }}"{% endif %}><i class="fa fa-calendar"></i> {{T_reservar}}</a>
        </div>
    </div>
{% endfor %}
</div>

<script>
    $(".room_picture img").each(function(){
        var picture = $(this),
            src = picture.data("src"),
            parent_wrapper = picture.parent(),
            parent_width = parent_wrapper.width(),
            parent_height = parent_wrapper.height();

        if (parent_width == 0) {
            parent_width = $('.section_content').width();
            picture.attr("src", src + "=s" + parent_width + "-c");
        }
        else if(parent_width <= parent_height) {
            picture.attr("src", src + "=s" + parent_width + "-c");
        } else {
            picture.attr("src", src + "=s" + (parent_width + 50));
        }
    });
</script>