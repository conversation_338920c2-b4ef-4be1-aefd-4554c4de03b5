{% if faqs_config %}
    <div class="faqs_section">
        <div class="faqs_wrapper">
            <div class="questions_wrapper">

                <div class="filters_button">
                    <i class="fas fa-sliders-h"></i>
                    {{ T_filtrar }}
                </div>


                {% for faq in faqs_config %}
                    {% if not faq.hide_in_section %}
                        <div class="question_element" tag="{{ faq.category|safe }}">
                            <h3 class="question">
                                {{ faq.title|safe }}
                            </h3>
                            <div class="answer">
                                {{ faq.description|safe }}
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
        </div>

        <div id="filter_wrapper_banner">
            <div id="close_filters">
                <i class="fal fa-times"></i>
            </div>

            <div class="banner_title">
                <i class="fas fa-sliders-h"></i>
                {{ T_filtrar }}
            </div>

            <div class="available_filters_wrapper">
                <div class="faqs_filters_wrapper filter_block">
                    <div class="options_list active"></div>
                </div>
            </div>

            <div class="filters_buttons_wrapper">
                <div id="clear_filters_button">{{ T_borrar_filtros }}</div>
                <div id="apply_filters_button">{{ T_aplicar_filtros }}</div>
            </div>
        </div>
    </div>

    <!--<script src="/js/{{ base_web }}/mobile_functions.js?v=1.00"></script>-->
    <script>
        $(function () {
            $(".question_element .question").click(function () {
                $(this).parent().toggleClass('active')
            })

            FaqsController.init()
        });
    </script>
{% endif %}
<script defer src="/js/{{ base_web }}/banners/mobile/faqs.js?v=1.01"></script>