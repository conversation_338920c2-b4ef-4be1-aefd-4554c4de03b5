.faqs_section {
  @include display_flex;
  align-items: flex-start;
  box-sizing: border-box;
  padding-bottom: 40px;
  justify-content: center;

  * {
    box-sizing: border-box;
  }

  .content_title {
    width: 100%;
    text-align: center;

    .subtitle_faqs {
      font-size: 14px;
      display: block;
      font-family: $text_family;
      line-height: 38px;
      color: $color_text;
      font-weight: 400;
      letter-spacing: 0.05em;
      text-transform: uppercase;
    }

    .title_faqs {
      @include title_styles;
      margin-bottom: 40px;
    }
  }

  .faqs_wrapper {
    display: inline-block;
    width: 90%;

    .question_element {
      &.hide {
        max-height: 0;
        overflow: hidden;
        border: 0;
        margin: 0;
      }
    }

    &.faqs_hide {
      opacity: 0;
      display: block;
      max-height: 0;
      overflow: hidden;
      @include transition(all, .8s);

      &.active {
        opacity: 1;
        max-height: 5000px;
      }
    }
  }

  .btn_more_faqs {
    display: block;
    width: 100%;
    margin-top: 20px;
    text-align: center;


    .read {
      i {
        margin-left: 10px;
      }
    }

    .read:not(.active) {
      display: none;
    }
  }

  #filter_wrapper_banner {
    display: inline-block;
    width: calc(40% - 45px);
  }
}

.availabler_filters_wrapper {
  .options_list {
    padding: 0 15px;
    max-height: 0;
    overflow: hidden;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -ms-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s;

    &.active {
      padding: 8px 15px;
      max-height: 600px;
    }

    .option_element {
      color: grey;
      font-weight: bold;
      text-transform: uppercase;
      display: flex;
      margin-bottom: 5px;

      input {
        margin: 0;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        width: 15px;
        height: 15px;
        position: relative;
        display: inline-block;
        vertical-align: middle;

        & + label {
          &:before {
            content: '';
            @include center_y;
            left: -17px;
            width: 22px;
            height: 22px;
            border-radius: 20px;
            border: 1px solid;
            background: white;
          }
        }

        &:focus {
          outline: 0;
        }

        &:checked + label {
          color: $corporate_1;

          &:after {
            content: '';
            @include center_y;
            left: -14px;
            width: 16px;
            height: 16px;
            border-radius: 20px;
            background: $corporate_1;
          }
        }
      }

      label {
        margin-left: 10px;
        position: relative;
        display: inline-block;
        vertical-align: middle;
        line-height: 2;
        cursor: pointer;
        font-size: 15px;
        letter-spacing: 0.4px;
        padding: 0 15px 4px;
      }
    }
  }
}

.faqs_wrapper {
  position: relative;

  .titles_wrapper {
    position: relative;
    padding-bottom: 30px;
    text-align: center;

    .mini_title {
      font-size: 18px;
      font-weight: 300;
      margin-bottom: 12px;
    }

    .main_title {
      font-size: 36px;
      margin-bottom: 7px;
    }
  }

  .questions_wrapper {
    .question_element {
      margin-bottom: 20px;

      .question {
        position: relative;
        font-weight: bold;
        border: 1px solid #d0d0d0;
        background: rgba(78, 91, 48, 0.2);
        border-radius: 10px;
        color: $corporate_1;
        cursor: pointer;
        font-size: 15px;
        letter-spacing: 0.6px;
        padding: 15px 50px 15px 15px;

        &:after {
          position: absolute;
          right: 20px;
          top: 50%;
          transform: translateY(-50%);
          content: "\f13a";
          color: $corporate_2;
          font-family: "Font Awesome 5 Pro";
          font-weight: 300;
          transition: all 0.5s;
          font-size: 21px;
        }
      }

      .answer {
        box-sizing: border-box;
        font-weight: 400;
        position: relative;
        width: 100%;
        margin: auto;
        padding: 0 50px 0 30px;
        max-height: 0;
        overflow: hidden;
        -webkit-transition: all 0.3s;
        -moz-transition: all 0.3s;
        -ms-transition: all 0.3s;
        -o-transition: all 0.3s;
        transition: all 0.3s;
        font-size: 15px;
        color: $corporate_1;
        line-height: 26px;

        a {
          color: $corporate_2;
          font-weight: 600;
        }
      }

      &.active {
        .question:after {
          -webkit-transform: rotate(180deg);
          -moz-transform: rotate(180deg);
          -ms-transform: rotate(180deg);
          -o-transform: rotate(180deg);
          transform: rotate(180deg);
          top: 31%;
        }

        .answer {
          padding: 14px 50px 14px 30px;
          max-height: 750px;
        }
      }
    }
  }

  .faqs_link {
    text-transform: uppercase;
    text-decoration: none;
    position: absolute;
    right: 0;
    bottom: -40px;
    color: $corporate_2;
    @include transition(color, .6s);
    font-size: 16px;
    letter-spacing: 0.4px;
    font-weight: bold;
    z-index: 1;

    &:hover {
      color: $corporate_1;
    }
  }
}