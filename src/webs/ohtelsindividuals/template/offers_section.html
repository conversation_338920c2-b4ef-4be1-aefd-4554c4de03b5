{% for x in offers_hotels %}
        <div class="vive_element" {% if forloop.counter|divisibleby:3 %}style="margin-right: 0"{% endif %}>
            <div class="exceded">
                <img class="vive_image" src="{{ x.picture|safe }}">
                <img class="plus_image" src="/img/{{ base_web }}/plus.png">
            </div>
            <div class="vive_description_wrapper">
                <h3 class="vive_title">{{ x.name|safe }}</h3>

                <div class="vive_description">{{ x.description|safe }}</div>
            </div>
            <div class="buttons_wrapper">
                <a href="#data" class="button-promotion" {% if x.promocode %}data-promocode="{{ x.promocode }}"{% endif %} {% if x.smartDatasAttributes %}{{ x.smartDatasAttributes }}{% endif %}>{{ T_reservar }}</a>
                <a href="#hide_vive_{{ forloop.counter }}" class="vive_button">{{ T_ver_mas }}</a>
            </div>
        </div>

        <div class="hide_vive_description" id="hide_vive_{{ forloop.counter }}" style="display: none">
            <h3 class="vive_title">{{ x.name|safe }}</h3>

            <div class="vive_description">
                {{ x.description|safe }}
            </div>
        </div>
{% endfor %}