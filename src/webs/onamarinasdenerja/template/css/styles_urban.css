@import url("https://fonts.googleapis.com/css?family=Open+Sans:300,400,400i,700,700i");
@import url(//fonts.googleapis.com/css?family=Montserrat|Source+Sans+Pro:400,300,700,600);
@font-face {
  font-family: 'FontAwesome';
  src: url("/static_1/fonts/fontawesome/fontawesome/fontawesome-webfont.eot?v=4.7.0");
  src: url("/static_1/fonts/fontawesome/fontawesome-webfont.eot?#iefix&v=4.7.0") format("embedded-opentype"), url("/static_1/fonts/fontawesome/fontawesome-webfont.woff2?v=4.7.0") format("woff2"), url("/static_1/fonts/fontawesome/fontawesome-webfont.woff?v=4.7.0") format("woff"), url("/static_1/fonts/fontawesome/fontawesome-webfont.ttf?v=4.7.0") format("truetype"), url("/static_1/fonts/fontawesome/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular") format("svg");
  font-weight: normal;
  font-style: normal;
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lg {
  font-size: 1.33333333em;
  line-height: .75em;
  vertical-align: -15%;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-3x {
  font-size: 3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-4x {
  font-size: 4em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-5x {
  font-size: 5em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fw {
  width: 1.28571429em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul > li {
  position: relative;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: .14285714em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li.fa-lg {
  left: -1.85714286em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-border {
  padding: .2em .25em .15em;
  border: solid .08em #eee;
  border-radius: .1em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-left {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-right {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-left {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-right {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  transform: scale(-1, 1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical {
  filter: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x, .fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x {
  line-height: inherit;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inverse {
  color: #fff;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glass:before {
  content: "\f000";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-music:before {
  content: "\f001";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search:before {
  content: "\f002";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-o:before {
  content: "\f003";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart:before {
  content: "\f004";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star:before {
  content: "\f005";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-o:before {
  content: "\f006";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user:before {
  content: "\f007";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-film:before {
  content: "\f008";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-large:before {
  content: "\f009";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th:before {
  content: "\f00a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-list:before {
  content: "\f00b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check:before {
  content: "\f00c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-remove:before, .fa-close:before, .fa-times:before, .newsletter_floating_wrapper .newsletter_message_wrapper.active .newsletter_message_arrow i.fa:before {
  content: "\f00d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-plus:before {
  content: "\f00e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-minus:before {
  content: "\f010";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-power-off:before {
  content: "\f011";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signal:before {
  content: "\f012";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gear:before, .fa-cog:before {
  content: "\f013";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash-o:before {
  content: "\f014";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-home:before {
  content: "\f015";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-o:before {
  content: "\f016";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clock-o:before {
  content: "\f017";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-road:before {
  content: "\f018";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-download:before {
  content: "\f019";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-down:before {
  content: "\f01a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-up:before {
  content: "\f01b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inbox:before {
  content: "\f01c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle-o:before {
  content: "\f01d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-right:before, .fa-repeat:before {
  content: "\f01e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-refresh:before {
  content: "\f021";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-alt:before {
  content: "\f022";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lock:before {
  content: "\f023";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag:before {
  content: "\f024";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-headphones:before {
  content: "\f025";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-off:before {
  content: "\f026";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-down:before {
  content: "\f027";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-up:before {
  content: "\f028";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qrcode:before {
  content: "\f029";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-barcode:before {
  content: "\f02a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tag:before {
  content: "\f02b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tags:before {
  content: "\f02c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-book:before {
  content: "\f02d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark:before {
  content: "\f02e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-print:before {
  content: "\f02f";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera:before {
  content: "\f030";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-font:before {
  content: "\f031";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bold:before {
  content: "\f032";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-italic:before {
  content: "\f033";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-height:before {
  content: "\f034";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-width:before {
  content: "\f035";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-left:before {
  content: "\f036";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-center:before {
  content: "\f037";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-right:before {
  content: "\f038";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-justify:before {
  content: "\f039";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list:before {
  content: "\f03a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dedent:before, .fa-outdent:before {
  content: "\f03b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-indent:before {
  content: "\f03c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-video-camera:before {
  content: "\f03d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-photo:before, .fa-image:before, .fa-picture-o:before {
  content: "\f03e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil:before {
  content: "\f040";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-marker:before {
  content: "\f041";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adjust:before {
  content: "\f042";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tint:before {
  content: "\f043";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edit:before, .fa-pencil-square-o:before {
  content: "\f044";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square-o:before {
  content: "\f045";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square-o:before {
  content: "\f046";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows:before {
  content: "\f047";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-backward:before {
  content: "\f048";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-backward:before {
  content: "\f049";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-backward:before {
  content: "\f04a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play:before {
  content: "\f04b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause:before {
  content: "\f04c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop:before {
  content: "\f04d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forward:before {
  content: "\f04e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-forward:before {
  content: "\f050";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-forward:before {
  content: "\f051";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eject:before {
  content: "\f052";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-left:before {
  content: "\f053";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-right:before {
  content: "\f054";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-circle:before {
  content: "\f055";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-circle:before {
  content: "\f056";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle:before {
  content: "\f057";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle:before {
  content: "\f058";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle:before {
  content: "\f059";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info-circle:before {
  content: "\f05a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crosshairs:before {
  content: "\f05b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle-o:before {
  content: "\f05c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle-o:before {
  content: "\f05d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ban:before {
  content: "\f05e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-left:before {
  content: "\f060";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-right:before {
  content: "\f061";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-up:before {
  content: "\f062";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-down:before {
  content: "\f063";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-forward:before, .fa-share:before {
  content: "\f064";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expand:before {
  content: "\f065";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compress:before {
  content: "\f066";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus:before {
  content: "\f067";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus:before {
  content: "\f068";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asterisk:before {
  content: "\f069";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation-circle:before {
  content: "\f06a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gift:before {
  content: "\f06b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leaf:before {
  content: "\f06c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire:before {
  content: "\f06d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye:before {
  content: "\f06e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye-slash:before {
  content: "\f070";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-warning:before, .fa-exclamation-triangle:before {
  content: "\f071";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plane:before {
  content: "\f072";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar:before {
  content: "\f073";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-random:before {
  content: "\f074";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment:before {
  content: "\f075";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magnet:before {
  content: "\f076";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-up:before {
  content: "\f077";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-down:before {
  content: "\f078";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-retweet:before {
  content: "\f079";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-cart:before {
  content: "\f07a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder:before {
  content: "\f07b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open:before {
  content: "\f07c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-v:before {
  content: "\f07d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-h:before {
  content: "\f07e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bar-chart-o:before, .fa-bar-chart:before {
  content: "\f080";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter-square:before {
  content: "\f081";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-square:before {
  content: "\f082";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera-retro:before {
  content: "\f083";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-key:before {
  content: "\f084";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gears:before, .fa-cogs:before {
  content: "\f085";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments:before {
  content: "\f086";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-up:before {
  content: "\f087";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-down:before {
  content: "\f088";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half:before {
  content: "\f089";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart-o:before {
  content: "\f08a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-out:before {
  content: "\f08b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin-square:before {
  content: "\f08c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumb-tack:before {
  content: "\f08d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link:before {
  content: "\f08e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-in:before {
  content: "\f090";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trophy:before {
  content: "\f091";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-square:before {
  content: "\f092";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-upload:before {
  content: "\f093";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lemon-o:before {
  content: "\f094";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone:before {
  content: "\f095";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square-o:before {
  content: "\f096";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark-o:before {
  content: "\f097";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone-square:before {
  content: "\f098";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter:before {
  content: "\f099";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-f:before, .fa-facebook:before {
  content: "\f09a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github:before {
  content: "\f09b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock:before {
  content: "\f09c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card:before {
  content: "\f09d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-feed:before, .fa-rss:before {
  content: "\f09e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hdd-o:before {
  content: "\f0a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullhorn:before {
  content: "\f0a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell:before {
  content: "\f0f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-certificate:before {
  content: "\f0a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-right:before {
  content: "\f0a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-left:before {
  content: "\f0a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-up:before {
  content: "\f0a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-down:before {
  content: "\f0a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-left:before {
  content: "\f0a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-right:before {
  content: "\f0a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-up:before {
  content: "\f0aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-down:before {
  content: "\f0ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-globe:before {
  content: "\f0ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wrench:before {
  content: "\f0ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tasks:before {
  content: "\f0ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-filter:before {
  content: "\f0b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-briefcase:before {
  content: "\f0b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-alt:before {
  content: "\f0b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-group:before, .fa-users:before {
  content: "\f0c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chain:before, .fa-link:before {
  content: "\f0c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud:before {
  content: "\f0c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flask:before {
  content: "\f0c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cut:before, .fa-scissors:before {
  content: "\f0c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copy:before, .fa-files-o:before {
  content: "\f0c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paperclip:before {
  content: "\f0c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-save:before, .fa-floppy-o:before {
  content: "\f0c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square:before {
  content: "\f0c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-navicon:before, .fa-reorder:before, .fa-bars:before {
  content: "\f0c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ul:before {
  content: "\f0ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ol:before {
  content: "\f0cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-strikethrough:before {
  content: "\f0cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-underline:before {
  content: "\f0cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-table:before {
  content: "\f0ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magic:before {
  content: "\f0d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-truck:before {
  content: "\f0d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest:before {
  content: "\f0d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-square:before {
  content: "\f0d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-square:before {
  content: "\f0d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus:before {
  content: "\f0d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-money:before {
  content: "\f0d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-down:before {
  content: "\f0d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-up:before {
  content: "\f0d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-left:before {
  content: "\f0d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-right:before {
  content: "\f0da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-columns:before {
  content: "\f0db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unsorted:before, .fa-sort:before {
  content: "\f0dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-down:before, .fa-sort-desc:before {
  content: "\f0dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-up:before, .fa-sort-asc:before {
  content: "\f0de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope:before {
  content: "\f0e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin:before {
  content: "\f0e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-left:before, .fa-undo:before {
  content: "\f0e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-legal:before, .fa-gavel:before {
  content: "\f0e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashboard:before, .fa-tachometer:before {
  content: "\f0e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment-o:before {
  content: "\f0e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments-o:before {
  content: "\f0e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flash:before, .fa-bolt:before {
  content: "\f0e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sitemap:before {
  content: "\f0e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-umbrella:before {
  content: "\f0e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paste:before, .fa-clipboard:before {
  content: "\f0ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lightbulb-o:before {
  content: "\f0eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exchange:before {
  content: "\f0ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-download:before {
  content: "\f0ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-upload:before {
  content: "\f0ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-md:before {
  content: "\f0f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stethoscope:before {
  content: "\f0f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-suitcase:before {
  content: "\f0f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-o:before {
  content: "\f0a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-coffee:before {
  content: "\f0f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cutlery:before {
  content: "\f0f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text-o:before {
  content: "\f0f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building-o:before {
  content: "\f0f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hospital-o:before {
  content: "\f0f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ambulance:before {
  content: "\f0f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medkit:before {
  content: "\f0fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fighter-jet:before {
  content: "\f0fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-beer:before {
  content: "\f0fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-h-square:before {
  content: "\f0fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square:before {
  content: "\f0fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-left:before {
  content: "\f100";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-right:before {
  content: "\f101";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-up:before {
  content: "\f102";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-down:before {
  content: "\f103";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-left:before {
  content: "\f104";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-right:before {
  content: "\f105";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-up:before {
  content: "\f106";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-down:before {
  content: "\f107";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-desktop:before {
  content: "\f108";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-laptop:before {
  content: "\f109";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tablet:before {
  content: "\f10a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mobile-phone:before, .fa-mobile:before {
  content: "\f10b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o:before {
  content: "\f10c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-left:before {
  content: "\f10d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-right:before {
  content: "\f10e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spinner:before {
  content: "\f110";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle:before {
  content: "\f111";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply:before, .fa-reply:before {
  content: "\f112";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-alt:before {
  content: "\f113";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-o:before {
  content: "\f114";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open-o:before {
  content: "\f115";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-smile-o:before {
  content: "\f118";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-frown-o:before {
  content: "\f119";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meh-o:before {
  content: "\f11a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gamepad:before {
  content: "\f11b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-keyboard-o:before {
  content: "\f11c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-o:before {
  content: "\f11d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-checkered:before {
  content: "\f11e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-terminal:before {
  content: "\f120";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code:before {
  content: "\f121";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply-all:before, .fa-reply-all:before {
  content: "\f122";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before {
  content: "\f123";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-location-arrow:before {
  content: "\f124";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crop:before {
  content: "\f125";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code-fork:before {
  content: "\f126";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlink:before, .fa-chain-broken:before {
  content: "\f127";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question:before {
  content: "\f128";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info:before {
  content: "\f129";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation:before {
  content: "\f12a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superscript:before {
  content: "\f12b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subscript:before {
  content: "\f12c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eraser:before {
  content: "\f12d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-puzzle-piece:before {
  content: "\f12e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone:before {
  content: "\f130";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone-slash:before {
  content: "\f131";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shield:before {
  content: "\f132";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-o:before {
  content: "\f133";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire-extinguisher:before {
  content: "\f134";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rocket:before {
  content: "\f135";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-maxcdn:before {
  content: "\f136";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-left:before {
  content: "\f137";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-right:before {
  content: "\f138";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-up:before {
  content: "\f139";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-down:before {
  content: "\f13a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-html5:before {
  content: "\f13b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-css3:before {
  content: "\f13c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-anchor:before {
  content: "\f13d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock-alt:before {
  content: "\f13e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullseye:before {
  content: "\f140";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-h:before {
  content: "\f141";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-v:before {
  content: "\f142";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rss-square:before {
  content: "\f143";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle:before {
  content: "\f144";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ticket:before {
  content: "\f145";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square:before {
  content: "\f146";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square-o:before {
  content: "\f147";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-up:before {
  content: "\f148";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-down:before {
  content: "\f149";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square:before {
  content: "\f14a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil-square:before {
  content: "\f14b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link-square:before {
  content: "\f14c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square:before {
  content: "\f14d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compass:before {
  content: "\f14e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-down:before, .fa-caret-square-o-down:before {
  content: "\f150";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-up:before, .fa-caret-square-o-up:before {
  content: "\f151";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-right:before, .fa-caret-square-o-right:before {
  content: "\f152";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-euro:before, .fa-eur:before {
  content: "\f153";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gbp:before {
  content: "\f154";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dollar:before, .fa-usd:before {
  content: "\f155";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rupee:before, .fa-inr:before {
  content: "\f156";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before {
  content: "\f157";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ruble:before, .fa-rouble:before, .fa-rub:before {
  content: "\f158";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-won:before, .fa-krw:before {
  content: "\f159";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitcoin:before, .fa-btc:before {
  content: "\f15a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file:before {
  content: "\f15b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text:before {
  content: "\f15c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-asc:before {
  content: "\f15d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-desc:before {
  content: "\f15e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-asc:before {
  content: "\f160";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-desc:before {
  content: "\f161";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-asc:before {
  content: "\f162";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-desc:before {
  content: "\f163";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-up:before {
  content: "\f164";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-down:before {
  content: "\f165";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-square:before {
  content: "\f166";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube:before {
  content: "\f167";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing:before {
  content: "\f168";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing-square:before {
  content: "\f169";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-play:before {
  content: "\f16a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dropbox:before {
  content: "\f16b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-overflow:before {
  content: "\f16c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-instagram:before {
  content: "\f16d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flickr:before {
  content: "\f16e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adn:before {
  content: "\f170";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket:before {
  content: "\f171";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket-square:before {
  content: "\f172";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr:before {
  content: "\f173";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr-square:before {
  content: "\f174";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-down:before {
  content: "\f175";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-up:before {
  content: "\f176";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-left:before {
  content: "\f177";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-right:before {
  content: "\f178";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-apple:before {
  content: "\f179";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-windows:before {
  content: "\f17a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-android:before {
  content: "\f17b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linux:before {
  content: "\f17c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dribbble:before {
  content: "\f17d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skype:before {
  content: "\f17e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-foursquare:before {
  content: "\f180";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trello:before {
  content: "\f181";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-female:before {
  content: "\f182";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-male:before {
  content: "\f183";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gittip:before, .fa-gratipay:before {
  content: "\f184";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sun-o:before {
  content: "\f185";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-moon-o:before {
  content: "\f186";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-archive:before {
  content: "\f187";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bug:before {
  content: "\f188";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vk:before {
  content: "\f189";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-weibo:before {
  content: "\f18a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-renren:before {
  content: "\f18b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pagelines:before {
  content: "\f18c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-exchange:before {
  content: "\f18d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-right:before {
  content: "\f18e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-left:before {
  content: "\f190";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-left:before, .fa-caret-square-o-left:before {
  content: "\f191";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dot-circle-o:before {
  content: "\f192";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair:before {
  content: "\f193";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo-square:before {
  content: "\f194";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-turkish-lira:before, .fa-try:before {
  content: "\f195";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square-o:before {
  content: "\f196";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-space-shuttle:before {
  content: "\f197";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slack:before {
  content: "\f198";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-square:before {
  content: "\f199";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wordpress:before {
  content: "\f19a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-openid:before {
  content: "\f19b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-institution:before, .fa-bank:before, .fa-university:before {
  content: "\f19c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mortar-board:before, .fa-graduation-cap:before {
  content: "\f19d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yahoo:before {
  content: "\f19e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google:before {
  content: "\f1a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit:before {
  content: "\f1a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-square:before {
  content: "\f1a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon-circle:before {
  content: "\f1a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon:before {
  content: "\f1a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-delicious:before {
  content: "\f1a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-digg:before {
  content: "\f1a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-pp:before {
  content: "\f1a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-alt:before {
  content: "\f1a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drupal:before {
  content: "\f1a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-joomla:before {
  content: "\f1aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-language:before {
  content: "\f1ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fax:before {
  content: "\f1ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building:before {
  content: "\f1ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-child:before {
  content: "\f1ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paw:before {
  content: "\f1b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spoon:before {
  content: "\f1b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cube:before {
  content: "\f1b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cubes:before {
  content: "\f1b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance:before {
  content: "\f1b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance-square:before {
  content: "\f1b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam:before {
  content: "\f1b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam-square:before {
  content: "\f1b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-recycle:before {
  content: "\f1b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-automobile:before, .fa-car:before {
  content: "\f1b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cab:before, .fa-taxi:before {
  content: "\f1ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tree:before {
  content: "\f1bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spotify:before {
  content: "\f1bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deviantart:before {
  content: "\f1bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soundcloud:before {
  content: "\f1be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-database:before {
  content: "\f1c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-pdf-o:before {
  content: "\f1c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-word-o:before {
  content: "\f1c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-excel-o:before {
  content: "\f1c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-powerpoint-o:before {
  content: "\f1c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before {
  content: "\f1c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-zip-o:before, .fa-file-archive-o:before {
  content: "\f1c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-sound-o:before, .fa-file-audio-o:before {
  content: "\f1c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-movie-o:before, .fa-file-video-o:before {
  content: "\f1c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-code-o:before {
  content: "\f1c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vine:before {
  content: "\f1ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codepen:before {
  content: "\f1cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-jsfiddle:before {
  content: "\f1cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before {
  content: "\f1cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o-notch:before {
  content: "\f1ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ra:before, .fa-resistance:before, .fa-rebel:before {
  content: "\f1d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ge:before, .fa-empire:before {
  content: "\f1d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git-square:before {
  content: "\f1d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git:before {
  content: "\f1d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before {
  content: "\f1d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tencent-weibo:before {
  content: "\f1d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qq:before {
  content: "\f1d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wechat:before, .fa-weixin:before {
  content: "\f1d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send:before, .fa-paper-plane:before {
  content: "\f1d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send-o:before, .fa-paper-plane-o:before {
  content: "\f1d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-history:before {
  content: "\f1da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-thin:before {
  content: "\f1db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-header:before {
  content: "\f1dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paragraph:before {
  content: "\f1dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sliders:before {
  content: "\f1de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt:before {
  content: "\f1e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt-square:before {
  content: "\f1e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bomb:before {
  content: "\f1e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soccer-ball-o:before, .fa-futbol-o:before {
  content: "\f1e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tty:before {
  content: "\f1e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-binoculars:before {
  content: "\f1e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plug:before {
  content: "\f1e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slideshare:before {
  content: "\f1e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitch:before {
  content: "\f1e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yelp:before {
  content: "\f1e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-newspaper-o:before {
  content: "\f1ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wifi:before {
  content: "\f1eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calculator:before {
  content: "\f1ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paypal:before {
  content: "\f1ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-wallet:before {
  content: "\f1ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-visa:before {
  content: "\f1f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-mastercard:before {
  content: "\f1f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-discover:before {
  content: "\f1f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-amex:before {
  content: "\f1f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-paypal:before {
  content: "\f1f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-stripe:before {
  content: "\f1f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash:before {
  content: "\f1f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash-o:before {
  content: "\f1f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash:before {
  content: "\f1f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copyright:before {
  content: "\f1f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-at:before {
  content: "\f1fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eyedropper:before {
  content: "\f1fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paint-brush:before {
  content: "\f1fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-birthday-cake:before {
  content: "\f1fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-area-chart:before {
  content: "\f1fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pie-chart:before {
  content: "\f200";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-line-chart:before {
  content: "\f201";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm:before {
  content: "\f202";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm-square:before {
  content: "\f203";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-off:before {
  content: "\f204";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-on:before {
  content: "\f205";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bicycle:before {
  content: "\f206";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bus:before {
  content: "\f207";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ioxhost:before {
  content: "\f208";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angellist:before {
  content: "\f209";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc:before {
  content: "\f20a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shekel:before, .fa-sheqel:before, .fa-ils:before {
  content: "\f20b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meanpath:before {
  content: "\f20c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-buysellads:before {
  content: "\f20d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-connectdevelop:before {
  content: "\f20e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashcube:before {
  content: "\f210";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forumbee:before {
  content: "\f211";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leanpub:before {
  content: "\f212";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sellsy:before {
  content: "\f213";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shirtsinbulk:before {
  content: "\f214";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-simplybuilt:before {
  content: "\f215";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skyatlas:before {
  content: "\f216";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-plus:before {
  content: "\f217";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-arrow-down:before {
  content: "\f218";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-diamond:before {
  content: "\f219";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ship:before {
  content: "\f21a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-secret:before {
  content: "\f21b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-motorcycle:before {
  content: "\f21c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-street-view:before {
  content: "\f21d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heartbeat:before {
  content: "\f21e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus:before {
  content: "\f221";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars:before {
  content: "\f222";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mercury:before {
  content: "\f223";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-intersex:before, .fa-transgender:before {
  content: "\f224";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-transgender-alt:before {
  content: "\f225";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-double:before {
  content: "\f226";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-double:before {
  content: "\f227";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-mars:before {
  content: "\f228";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke:before {
  content: "\f229";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-v:before {
  content: "\f22a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-h:before {
  content: "\f22b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-neuter:before {
  content: "\f22c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-genderless:before {
  content: "\f22d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-official:before {
  content: "\f230";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-p:before {
  content: "\f231";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-whatsapp:before {
  content: "\f232";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-server:before {
  content: "\f233";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-plus:before {
  content: "\f234";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-times:before {
  content: "\f235";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hotel:before, .fa-bed:before {
  content: "\f236";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viacoin:before {
  content: "\f237";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-train:before {
  content: "\f238";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subway:before {
  content: "\f239";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medium:before {
  content: "\f23a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yc:before, .fa-y-combinator:before {
  content: "\f23b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-optin-monster:before {
  content: "\f23c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opencart:before {
  content: "\f23d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expeditedssl:before {
  content: "\f23e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before {
  content: "\f240";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-3:before, .fa-battery-three-quarters:before {
  content: "\f241";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-2:before, .fa-battery-half:before {
  content: "\f242";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-1:before, .fa-battery-quarter:before {
  content: "\f243";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-0:before, .fa-battery-empty:before {
  content: "\f244";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mouse-pointer:before {
  content: "\f245";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-i-cursor:before {
  content: "\f246";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-group:before {
  content: "\f247";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-ungroup:before {
  content: "\f248";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note:before {
  content: "\f249";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note-o:before {
  content: "\f24a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-jcb:before {
  content: "\f24b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-diners-club:before {
  content: "\f24c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clone:before {
  content: "\f24d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-balance-scale:before {
  content: "\f24e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-o:before {
  content: "\f250";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-1:before, .fa-hourglass-start:before {
  content: "\f251";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-2:before, .fa-hourglass-half:before {
  content: "\f252";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-3:before, .fa-hourglass-end:before {
  content: "\f253";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass:before {
  content: "\f254";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-grab-o:before, .fa-hand-rock-o:before {
  content: "\f255";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-stop-o:before, .fa-hand-paper-o:before {
  content: "\f256";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-scissors-o:before {
  content: "\f257";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-lizard-o:before {
  content: "\f258";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-spock-o:before {
  content: "\f259";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-pointer-o:before {
  content: "\f25a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-peace-o:before {
  content: "\f25b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trademark:before {
  content: "\f25c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-registered:before {
  content: "\f25d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-creative-commons:before {
  content: "\f25e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg:before {
  content: "\f260";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg-circle:before {
  content: "\f261";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tripadvisor:before {
  content: "\f262";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki:before {
  content: "\f263";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki-square:before {
  content: "\f264";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-get-pocket:before {
  content: "\f265";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wikipedia-w:before {
  content: "\f266";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-safari:before {
  content: "\f267";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chrome:before {
  content: "\f268";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-firefox:before {
  content: "\f269";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opera:before {
  content: "\f26a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-internet-explorer:before {
  content: "\f26b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tv:before, .fa-television:before {
  content: "\f26c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-contao:before {
  content: "\f26d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-500px:before {
  content: "\f26e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-amazon:before {
  content: "\f270";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-plus-o:before {
  content: "\f271";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-minus-o:before {
  content: "\f272";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-times-o:before {
  content: "\f273";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-check-o:before {
  content: "\f274";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-industry:before {
  content: "\f275";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-pin:before {
  content: "\f276";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-signs:before {
  content: "\f277";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-o:before {
  content: "\f278";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map:before {
  content: "\f279";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting:before {
  content: "\f27a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting-o:before {
  content: "\f27b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-houzz:before {
  content: "\f27c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo:before {
  content: "\f27d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-black-tie:before {
  content: "\f27e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fonticons:before {
  content: "\f280";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-alien:before {
  content: "\f281";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edge:before {
  content: "\f282";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card-alt:before {
  content: "\f283";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codiepie:before {
  content: "\f284";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-modx:before {
  content: "\f285";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fort-awesome:before {
  content: "\f286";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-usb:before {
  content: "\f287";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-product-hunt:before {
  content: "\f288";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mixcloud:before {
  content: "\f289";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-scribd:before {
  content: "\f28a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle:before {
  content: "\f28b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle-o:before {
  content: "\f28c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle:before {
  content: "\f28d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle-o:before {
  content: "\f28e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-bag:before {
  content: "\f290";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-basket:before {
  content: "\f291";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hashtag:before {
  content: "\f292";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth:before {
  content: "\f293";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth-b:before {
  content: "\f294";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-percent:before {
  content: "\f295";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gitlab:before {
  content: "\f296";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpbeginner:before {
  content: "\f297";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpforms:before {
  content: "\f298";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envira:before {
  content: "\f299";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-universal-access:before {
  content: "\f29a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair-alt:before {
  content: "\f29b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle-o:before {
  content: "\f29c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-blind:before {
  content: "\f29d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-audio-description:before {
  content: "\f29e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-control-phone:before {
  content: "\f2a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-braille:before {
  content: "\f2a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-assistive-listening-systems:before {
  content: "\f2a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before {
  content: "\f2a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide:before {
  content: "\f2a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide-g:before {
  content: "\f2a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signing:before, .fa-sign-language:before {
  content: "\f2a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-low-vision:before {
  content: "\f2a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo:before {
  content: "\f2a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo-square:before {
  content: "\f2aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat:before {
  content: "\f2ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-ghost:before {
  content: "\f2ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-square:before {
  content: "\f2ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper:before {
  content: "\f2ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-first-order:before {
  content: "\f2b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yoast:before {
  content: "\f2b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-themeisle:before {
  content: "\f2b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-circle:before, .fa-google-plus-official:before {
  content: "\f2b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fa:before, .fa-font-awesome:before {
  content: "\f2b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-handshake-o:before {
  content: "\f2b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open:before {
  content: "\f2b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open-o:before {
  content: "\f2b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linode:before {
  content: "\f2b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book:before {
  content: "\f2b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book-o:before {
  content: "\f2ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard:before, .fa-address-card:before {
  content: "\f2bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard-o:before, .fa-address-card-o:before {
  content: "\f2bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle:before {
  content: "\f2bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle-o:before {
  content: "\f2be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-o:before {
  content: "\f2c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-id-badge:before {
  content: "\f2c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license:before, .fa-id-card:before {
  content: "\f2c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license-o:before, .fa-id-card-o:before {
  content: "\f2c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quora:before {
  content: "\f2c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-free-code-camp:before {
  content: "\f2c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-telegram:before {
  content: "\f2c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before {
  content: "\f2c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-3:before, .fa-thermometer-three-quarters:before {
  content: "\f2c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-2:before, .fa-thermometer-half:before {
  content: "\f2c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-1:before, .fa-thermometer-quarter:before {
  content: "\f2ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-0:before, .fa-thermometer-empty:before {
  content: "\f2cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shower:before {
  content: "\f2cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bathtub:before, .fa-s15:before, .fa-bath:before {
  content: "\f2cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-podcast:before {
  content: "\f2ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-maximize:before {
  content: "\f2d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-minimize:before {
  content: "\f2d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-restore:before {
  content: "\f2d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle:before, .fa-window-close:before {
  content: "\f2d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle-o:before, .fa-window-close-o:before {
  content: "\f2d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bandcamp:before {
  content: "\f2d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-grav:before {
  content: "\f2d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-etsy:before {
  content: "\f2d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-imdb:before {
  content: "\f2d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ravelry:before {
  content: "\f2d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eercast:before {
  content: "\f2da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microchip:before {
  content: "\f2db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snowflake-o:before {
  content: "\f2dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superpowers:before {
  content: "\f2dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpexplorer:before {
  content: "\f2de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meetup:before {
  content: "\f2e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

@font-face {
  font-family: 'icomoon';
  src: url("/static_1/fonts/iconmoon/icomoon.eot?ytm7g4");
  src: url("/static_1/fonts/iconmoon/icomoon.eot?ytm7g4#iefix") format("embedded-opentype"), url("/static_1/fonts/iconmoon/icomoon.ttf?ytm7g4") format("truetype"), url("/static_1/fonts/iconmoon/icomoon.woff?ytm7g4") format("woff"), url("/static_1/fonts/iconmoon/icomoon.svg?ytm7g4#icomoon") format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
/* line 13, ../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* line 28, ../../../../sass/plugins/_iconmoon.scss */
.icon-terrace:before {
  content: "\ea52";
}

/* line 31, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-hotelmanager:before {
  content: "\ea4c";
}

/* line 34, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-paritymaker:before {
  content: "\ea4d";
}

/* line 37, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-priceseeker:before {
  content: "\ea4e";
}

/* line 40, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-ratecheck:before {
  content: "\ea4f";
}

/* line 43, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-rescueseeker:before {
  content: "\ea50";
}

/* line 46, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-reviewseeker:before {
  content: "\ea51";
}

/* line 49, ../../../../sass/plugins/_iconmoon.scss */
.icon-couponlong:before {
  content: "\ea4a";
}

/* line 52, ../../../../sass/plugins/_iconmoon.scss */
.icon-coupon:before {
  content: "\ea4b";
}

/* line 55, ../../../../sass/plugins/_iconmoon.scss */
.icon-cocktel:before {
  content: "\ea46";
}

/* line 58, ../../../../sass/plugins/_iconmoon.scss */
.icon-gafas:before {
  content: "\ea47";
}

/* line 61, ../../../../sass/plugins/_iconmoon.scss */
.icon-pelota:before {
  content: "\ea48";
}

/* line 64, ../../../../sass/plugins/_iconmoon.scss */
.icon-piscina:before {
  content: "\ea49";
}

/* line 67, ../../../../sass/plugins/_iconmoon.scss */
.icon-email2:before {
  content: "\ea43";
}

/* line 70, ../../../../sass/plugins/_iconmoon.scss */
.icon-luggage2:before {
  content: "\ea44";
}

/* line 73, ../../../../sass/plugins/_iconmoon.scss */
.icon-nodisturb:before {
  content: "\ea45";
}

/* line 76, ../../../../sass/plugins/_iconmoon.scss */
.icon-percent:before {
  content: "\61";
}

/* line 79, ../../../../sass/plugins/_iconmoon.scss */
.icon-bed:before {
  content: "\62";
}

/* line 82, ../../../../sass/plugins/_iconmoon.scss */
.icon-dots:before {
  content: "\63";
}

/* line 85, ../../../../sass/plugins/_iconmoon.scss */
.icon-buffet:before {
  content: "\e900";
}

/* line 88, ../../../../sass/plugins/_iconmoon.scss */
.icon-zen:before {
  content: "\e901";
}

/* line 91, ../../../../sass/plugins/_iconmoon.scss */
.icon-drink:before {
  content: "\e902";
}

/* line 94, ../../../../sass/plugins/_iconmoon.scss */
.icon-bike:before {
  content: "\e903";
}

/* line 97, ../../../../sass/plugins/_iconmoon.scss */
.icon-celiac:before {
  content: "\e904";
}

/* line 100, ../../../../sass/plugins/_iconmoon.scss */
.icon-chart:before {
  content: "\e905";
}

/* line 103, ../../../../sass/plugins/_iconmoon.scss */
.icon-chips:before {
  content: "\e906";
}

/* line 106, ../../../../sass/plugins/_iconmoon.scss */
.icon-clock:before {
  content: "\e907";
}

/* line 109, ../../../../sass/plugins/_iconmoon.scss */
.icon-download:before {
  content: "\e908";
}

/* line 112, ../../../../sass/plugins/_iconmoon.scss */
.icon-friends:before {
  content: "\e909";
}

/* line 115, ../../../../sass/plugins/_iconmoon.scss */
.icon-group:before {
  content: "\e90a";
}

/* line 118, ../../../../sass/plugins/_iconmoon.scss */
.icon-headset:before {
  content: "\e90b";
}

/* line 121, ../../../../sass/plugins/_iconmoon.scss */
.icon-hipster:before {
  content: "\e90c";
}

/* line 124, ../../../../sass/plugins/_iconmoon.scss */
.icon-lamp:before {
  content: "\e90d";
}

/* line 127, ../../../../sass/plugins/_iconmoon.scss */
.icon-like:before {
  content: "\e90e";
}

/* line 130, ../../../../sass/plugins/_iconmoon.scss */
.icon-map:before {
  content: "\e90f";
}

/* line 133, ../../../../sass/plugins/_iconmoon.scss */
.icon-men:before {
  content: "\e910";
}

/* line 136, ../../../../sass/plugins/_iconmoon.scss */
.icon-monument:before {
  content: "\e911";
}

/* line 139, ../../../../sass/plugins/_iconmoon.scss */
.icon-new:before {
  content: "\e912";
}

/* line 142, ../../../../sass/plugins/_iconmoon.scss */
.icon-pig:before {
  content: "\e913";
}

/* line 145, ../../../../sass/plugins/_iconmoon.scss */
.icon-pdf:before {
  content: "\e914";
}

/* line 148, ../../../../sass/plugins/_iconmoon.scss */
.icon-play:before {
  content: "\e915";
}

/* line 151, ../../../../sass/plugins/_iconmoon.scss */
.icon-row:before {
  content: "\e916";
}

/* line 154, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE917:before {
  content: "\e917";
}

/* line 157, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE918:before {
  content: "\e918";
}

/* line 160, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE919:before {
  content: "\e919";
}

/* line 163, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE91A:before {
  content: "\e91a";
}

/* line 166, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE91B:before {
  content: "\e91b";
}

/* line 169, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE91C:before {
  content: "\e91c";
}

/* line 172, ../../../../sass/plugins/_iconmoon.scss */
.icon-sea:before {
  content: "\e91d";
}

/* line 175, ../../../../sass/plugins/_iconmoon.scss */
.icon-slide:before {
  content: "\e91e";
}

/* line 178, ../../../../sass/plugins/_iconmoon.scss */
.icon-smile:before {
  content: "\e91f";
}

/* line 181, ../../../../sass/plugins/_iconmoon.scss */
.icon-tick:before {
  content: "\e920";
}

/* line 184, ../../../../sass/plugins/_iconmoon.scss */
.icon-ticket:before {
  content: "\e921";
}

/* line 187, ../../../../sass/plugins/_iconmoon.scss */
.icon-trees:before {
  content: "\e922";
}

/* line 190, ../../../../sass/plugins/_iconmoon.scss */
.icon-upgrade:before {
  content: "\e923";
}

/* line 193, ../../../../sass/plugins/_iconmoon.scss */
.icon-watergame:before {
  content: "\e924";
}

/* line 196, ../../../../sass/plugins/_iconmoon.scss */
.icon-wedding:before {
  content: "\e925";
}

/* line 199, ../../../../sass/plugins/_iconmoon.scss */
.icon-basketball:before {
  content: "\e926";
}

/* line 202, ../../../../sass/plugins/_iconmoon.scss */
.icon-books:before {
  content: "\e927";
}

/* line 205, ../../../../sass/plugins/_iconmoon.scss */
.icon-calendar:before {
  content: "\e928";
}

/* line 208, ../../../../sass/plugins/_iconmoon.scss */
.icon-candles:before {
  content: "\e929";
}

/* line 211, ../../../../sass/plugins/_iconmoon.scss */
.icon-coins:before {
  content: "\e92a";
}

/* line 214, ../../../../sass/plugins/_iconmoon.scss */
.icon-cup:before {
  content: "\e92b";
}

/* line 217, ../../../../sass/plugins/_iconmoon.scss */
.icon-cutlery:before {
  content: "\e92c";
}

/* line 220, ../../../../sass/plugins/_iconmoon.scss */
.icon-dice:before {
  content: "\e92d";
}

/* line 223, ../../../../sass/plugins/_iconmoon.scss */
.icon-doc:before {
  content: "\e92e";
}

/* line 226, ../../../../sass/plugins/_iconmoon.scss */
.icon-email:before {
  content: "\e92f";
}

/* line 229, ../../../../sass/plugins/_iconmoon.scss */
.icon-euro:before {
  content: "\e930";
}

/* line 232, ../../../../sass/plugins/_iconmoon.scss */
.icon-info:before {
  content: "\e931";
}

/* line 235, ../../../../sass/plugins/_iconmoon.scss */
.icon-light:before {
  content: "\e932";
}

/* line 238, ../../../../sass/plugins/_iconmoon.scss */
.icon-night:before {
  content: "\e933";
}

/* line 241, ../../../../sass/plugins/_iconmoon.scss */
.icon-pet:before {
  content: "\e934";
}

/* line 244, ../../../../sass/plugins/_iconmoon.scss */
.icon-shell:before {
  content: "\e935";
}

/* line 247, ../../../../sass/plugins/_iconmoon.scss */
.icon-sofa:before {
  content: "\e936";
}

/* line 250, ../../../../sass/plugins/_iconmoon.scss */
.icon-star:before {
  content: "\e937";
}

/* line 253, ../../../../sass/plugins/_iconmoon.scss */
.icon-user:before {
  content: "\e938";
}

/* line 256, ../../../../sass/plugins/_iconmoon.scss */
.icon-wii:before {
  content: "\e939";
}

/* line 259, ../../../../sass/plugins/_iconmoon.scss */
.icon-ball:before {
  content: "\e93a";
}

/* line 262, ../../../../sass/plugins/_iconmoon.scss */
.icon-booking:before {
  content: "\e93b";
}

/* line 265, ../../../../sass/plugins/_iconmoon.scss */
.icon-cleanset:before {
  content: "\e93c";
}

/* line 268, ../../../../sass/plugins/_iconmoon.scss */
.icon-comment:before {
  content: "\e93d";
}

/* line 271, ../../../../sass/plugins/_iconmoon.scss */
.icon-ethernet:before {
  content: "\e93e";
}

/* line 274, ../../../../sass/plugins/_iconmoon.scss */
.icon-eye:before {
  content: "\e93f";
}

/* line 277, ../../../../sass/plugins/_iconmoon.scss */
.icon-feet:before {
  content: "\e940";
}

/* line 280, ../../../../sass/plugins/_iconmoon.scss */
.icon-fridge:before {
  content: "\e941";
}

/* line 283, ../../../../sass/plugins/_iconmoon.scss */
.icon-hairdrier:before {
  content: "\e942";
}

/* line 286, ../../../../sass/plugins/_iconmoon.scss */
.icon-handicap:before {
  content: "\e943";
}

/* line 289, ../../../../sass/plugins/_iconmoon.scss */
.icon-iron:before {
  content: "\e944";
}

/* line 292, ../../../../sass/plugins/_iconmoon.scss */
.icon-key:before {
  content: "\e945";
}

/* line 295, ../../../../sass/plugins/_iconmoon.scss */
.icon-lift:before {
  content: "\e946";
}

/* line 298, ../../../../sass/plugins/_iconmoon.scss */
.icon-mapmarker:before {
  content: "\e947";
}

/* line 301, ../../../../sass/plugins/_iconmoon.scss */
.icon-mask:before {
  content: "\e948";
}

/* line 304, ../../../../sass/plugins/_iconmoon.scss */
.icon-mouse:before {
  content: "\e949";
}

/* line 307, ../../../../sass/plugins/_iconmoon.scss */
.icon-movie:before {
  content: "\e94a";
}

/* line 310, ../../../../sass/plugins/_iconmoon.scss */
.icon-mug:before {
  content: "\e94b";
}

/* line 313, ../../../../sass/plugins/_iconmoon.scss */
.icon-plug:before {
  content: "\e94c";
}

/* line 316, ../../../../sass/plugins/_iconmoon.scss */
.icon-plus:before {
  content: "\e94d";
}

/* line 319, ../../../../sass/plugins/_iconmoon.scss */
.icon-printer:before {
  content: "\e94e";
}

/* line 322, ../../../../sass/plugins/_iconmoon.scss */
.icon-sack:before {
  content: "\e94f";
}

/* line 325, ../../../../sass/plugins/_iconmoon.scss */
.icon-shower:before {
  content: "\e950";
}

/* line 328, ../../../../sass/plugins/_iconmoon.scss */
.icon-solarium:before {
  content: "\e951";
}

/* line 331, ../../../../sass/plugins/_iconmoon.scss */
.icon-tenis:before {
  content: "\e952";
}

/* line 334, ../../../../sass/plugins/_iconmoon.scss */
.icon-tv:before {
  content: "\e953";
}

/* line 337, ../../../../sass/plugins/_iconmoon.scss */
.icon-window:before {
  content: "\e954";
}

/* line 340, ../../../../sass/plugins/_iconmoon.scss */
.icon-apple:before {
  content: "\e955";
}

/* line 343, ../../../../sass/plugins/_iconmoon.scss */
.icon-bathrobe:before {
  content: "\e956";
}

/* line 346, ../../../../sass/plugins/_iconmoon.scss */
.icon-bell:before {
  content: "\e957";
}

/* line 349, ../../../../sass/plugins/_iconmoon.scss */
.icon-building:before {
  content: "\e958";
}

/* line 352, ../../../../sass/plugins/_iconmoon.scss */
.icon-car:before {
  content: "\e959";
}

/* line 355, ../../../../sass/plugins/_iconmoon.scss */
.icon-cigar:before {
  content: "\e95a";
}

/* line 358, ../../../../sass/plugins/_iconmoon.scss */
.icon-comments:before {
  content: "\e95b";
}

/* line 361, ../../../../sass/plugins/_iconmoon.scss */
.icon-coolheart:before {
  content: "\e95c";
}

/* line 364, ../../../../sass/plugins/_iconmoon.scss */
.icon-cupboard:before {
  content: "\e95d";
}

/* line 367, ../../../../sass/plugins/_iconmoon.scss */
.icon-dimensions:before {
  content: "\e95e";
}

/* line 370, ../../../../sass/plugins/_iconmoon.scss */
.icon-family:before {
  content: "\e95f";
}

/* line 373, ../../../../sass/plugins/_iconmoon.scss */
.icon-flattv:before {
  content: "\e960";
}

/* line 376, ../../../../sass/plugins/_iconmoon.scss */
.icon-formaluser:before {
  content: "\e961";
}

/* line 379, ../../../../sass/plugins/_iconmoon.scss */
.icon-guarantee:before {
  content: "\e962";
}

/* line 382, ../../../../sass/plugins/_iconmoon.scss */
.icon-gift:before {
  content: "\e963";
}

/* line 385, ../../../../sass/plugins/_iconmoon.scss */
.icon-lock:before {
  content: "\e964";
}

/* line 388, ../../../../sass/plugins/_iconmoon.scss */
.icon-movie2:before {
  content: "\e965";
}

/* line 391, ../../../../sass/plugins/_iconmoon.scss */
.icon-picasa:before {
  content: "\e966";
}

/* line 394, ../../../../sass/plugins/_iconmoon.scss */
.icon-roulette:before {
  content: "\e967";
}

/* line 397, ../../../../sass/plugins/_iconmoon.scss */
.icon-sauna:before {
  content: "\e968";
}

/* line 400, ../../../../sass/plugins/_iconmoon.scss */
.icon-shower2:before {
  content: "\e969";
}

/* line 403, ../../../../sass/plugins/_iconmoon.scss */
.icon-singlebed:before {
  content: "\e96a";
}

/* line 406, ../../../../sass/plugins/_iconmoon.scss */
.icon-ski:before {
  content: "\e96b";
}

/* line 409, ../../../../sass/plugins/_iconmoon.scss */
.icon-smartphone:before {
  content: "\e96c";
}

/* line 412, ../../../../sass/plugins/_iconmoon.scss */
.icon-student:before {
  content: "\e96d";
}

/* line 415, ../../../../sass/plugins/_iconmoon.scss */
.icon-thermometer:before {
  content: "\e96e";
}

/* line 418, ../../../../sass/plugins/_iconmoon.scss */
.icon-washer:before {
  content: "\e96f";
}

/* line 421, ../../../../sass/plugins/_iconmoon.scss */
.icon-drinks:before {
  content: "\e970";
}

/* line 424, ../../../../sass/plugins/_iconmoon.scss */
.icon-drinks2:before {
  content: "\e971";
}

/* line 427, ../../../../sass/plugins/_iconmoon.scss */
.icon-airconditioner:before {
  content: "\e972";
}

/* line 430, ../../../../sass/plugins/_iconmoon.scss */
.icon-arrowdown:before {
  content: "\e973";
}

/* line 433, ../../../../sass/plugins/_iconmoon.scss */
.icon-arrowleft:before {
  content: "\e974";
}

/* line 436, ../../../../sass/plugins/_iconmoon.scss */
.icon-arrowright:before {
  content: "\e975";
}

/* line 439, ../../../../sass/plugins/_iconmoon.scss */
.icon-arrowup:before {
  content: "\e976";
}

/* line 442, ../../../../sass/plugins/_iconmoon.scss */
.icon-bag:before {
  content: "\e977";
}

/* line 445, ../../../../sass/plugins/_iconmoon.scss */
.icon-bike2:before {
  content: "\e978";
}

/* line 448, ../../../../sass/plugins/_iconmoon.scss */
.icon-biker:before {
  content: "\e979";
}

/* line 451, ../../../../sass/plugins/_iconmoon.scss */
.icon-briefcase:before {
  content: "\e97a";
}

/* line 454, ../../../../sass/plugins/_iconmoon.scss */
.icon-card:before {
  content: "\e97b";
}

/* line 457, ../../../../sass/plugins/_iconmoon.scss */
.icon-cocktail:before {
  content: "\e97c";
}

/* line 460, ../../../../sass/plugins/_iconmoon.scss */
.icon-cooker:before {
  content: "\e97d";
}

/* line 463, ../../../../sass/plugins/_iconmoon.scss */
.icon-drop:before {
  content: "\e97e";
}

/* line 466, ../../../../sass/plugins/_iconmoon.scss */
.icon-gym:before {
  content: "\e97f";
}

/* line 469, ../../../../sass/plugins/_iconmoon.scss */
.icon-info2:before {
  content: "\e980";
}

/* line 472, ../../../../sass/plugins/_iconmoon.scss */
.icon-massage:before {
  content: "\e981";
}

/* line 475, ../../../../sass/plugins/_iconmoon.scss */
.icon-moon:before {
  content: "\e982";
}

/* line 478, ../../../../sass/plugins/_iconmoon.scss */
.icon-music:before {
  content: "\e983";
}

/* line 481, ../../../../sass/plugins/_iconmoon.scss */
.icon-news:before {
  content: "\e984";
}

/* line 484, ../../../../sass/plugins/_iconmoon.scss */
.icon-nosmoke:before {
  content: "\e985";
}

/* line 487, ../../../../sass/plugins/_iconmoon.scss */
.icon-parking:before {
  content: "\e986";
}

/* line 490, ../../../../sass/plugins/_iconmoon.scss */
.icon-phone1:before {
  content: "\e987";
}

/* line 493, ../../../../sass/plugins/_iconmoon.scss */
.icon-phone2:before {
  content: "\e988";
}

/* line 496, ../../../../sass/plugins/_iconmoon.scss */
.icon-pictures:before {
  content: "\e989";
}

/* line 499, ../../../../sass/plugins/_iconmoon.scss */
.icon-plane:before {
  content: "\e98a";
}

/* line 502, ../../../../sass/plugins/_iconmoon.scss */
.icon-shield:before {
  content: "\e98b";
}

/* line 505, ../../../../sass/plugins/_iconmoon.scss */
.icon-spa:before {
  content: "\e98c";
}

/* line 508, ../../../../sass/plugins/_iconmoon.scss */
.icon-sun:before {
  content: "\e98d";
}

/* line 511, ../../../../sass/plugins/_iconmoon.scss */
.icon-ball2:before {
  content: "\e98e";
}

/* line 514, ../../../../sass/plugins/_iconmoon.scss */
.icon-bubbles:before {
  content: "\e98f";
}

/* line 517, ../../../../sass/plugins/_iconmoon.scss */
.icon-cot:before {
  content: "\e990";
}

/* line 520, ../../../../sass/plugins/_iconmoon.scss */
.icon-cutlery2:before {
  content: "\e991";
}

/* line 523, ../../../../sass/plugins/_iconmoon.scss */
.icon-golfplayer:before {
  content: "\e992";
}

/* line 526, ../../../../sass/plugins/_iconmoon.scss */
.icon-heart:before {
  content: "\e993";
}

/* line 529, ../../../../sass/plugins/_iconmoon.scss */
.icon-towels:before {
  content: "\e994";
}

/* line 532, ../../../../sass/plugins/_iconmoon.scss */
.icon-tree:before {
  content: "\e995";
}

/* line 535, ../../../../sass/plugins/_iconmoon.scss */
.icon-wifi:before {
  content: "\e996";
}

/* line 538, ../../../../sass/plugins/_iconmoon.scss */
.icon-alarmclock:before {
  content: "\e997";
}

/* line 541, ../../../../sass/plugins/_iconmoon.scss */
.icon-amenities:before {
  content: "\e998";
}

/* line 544, ../../../../sass/plugins/_iconmoon.scss */
.icon-astronomy:before {
  content: "\e999";
}

/* line 547, ../../../../sass/plugins/_iconmoon.scss */
.icon-barbecue:before {
  content: "\e99a";
}

/* line 550, ../../../../sass/plugins/_iconmoon.scss */
.icon-bells:before {
  content: "\e99b";
}

/* line 553, ../../../../sass/plugins/_iconmoon.scss */
.icon-bottle:before {
  content: "\e99c";
}

/* line 556, ../../../../sass/plugins/_iconmoon.scss */
.icon-bottle2:before {
  content: "\e99d";
}

/* line 559, ../../../../sass/plugins/_iconmoon.scss */
.icon-breakfast:before {
  content: "\e99e";
}

/* line 562, ../../../../sass/plugins/_iconmoon.scss */
.icon-broom:before {
  content: "\e99f";
}

/* line 565, ../../../../sass/plugins/_iconmoon.scss */
.icon-buffet2:before {
  content: "\e9a0";
}

/* line 568, ../../../../sass/plugins/_iconmoon.scss */
.icon-calendar2:before {
  content: "\e9a1";
}

/* line 571, ../../../../sass/plugins/_iconmoon.scss */
.icon-camera:before {
  content: "\e9a2";
}

/* line 574, ../../../../sass/plugins/_iconmoon.scss */
.icon-camera2:before {
  content: "\e9a3";
}

/* line 577, ../../../../sass/plugins/_iconmoon.scss */
.icon-caravan:before {
  content: "\e9a4";
}

/* line 580, ../../../../sass/plugins/_iconmoon.scss */
.icon-champagne:before {
  content: "\e9a5";
}

/* line 583, ../../../../sass/plugins/_iconmoon.scss */
.icon-chocolate:before {
  content: "\e9a6";
}

/* line 586, ../../../../sass/plugins/_iconmoon.scss */
.icon-christmasbauble:before {
  content: "\e9a7";
}

/* line 589, ../../../../sass/plugins/_iconmoon.scss */
.icon-christmasbauble2:before {
  content: "\e9a8";
}

/* line 592, ../../../../sass/plugins/_iconmoon.scss */
.icon-christmastree:before {
  content: "\e9a9";
}

/* line 595, ../../../../sass/plugins/_iconmoon.scss */
.icon-christmastree2:before {
  content: "\e9aa";
}

/* line 598, ../../../../sass/plugins/_iconmoon.scss */
.icon-chronometer:before {
  content: "\e9ab";
}

/* line 601, ../../../../sass/plugins/_iconmoon.scss */
.icon-clic:before {
  content: "\e9ac";
}

/* line 604, ../../../../sass/plugins/_iconmoon.scss */
.icon-cocktail2:before {
  content: "\e9ad";
}

/* line 607, ../../../../sass/plugins/_iconmoon.scss */
.icon-coffee:before {
  content: "\e9ae";
}

/* line 610, ../../../../sass/plugins/_iconmoon.scss */
.icon-coffee2:before {
  content: "\e9af";
}

/* line 613, ../../../../sass/plugins/_iconmoon.scss */
.icon-comment2:before {
  content: "\e9b0";
}

/* line 616, ../../../../sass/plugins/_iconmoon.scss */
.icon-comment3:before {
  content: "\e9b1";
}

/* line 619, ../../../../sass/plugins/_iconmoon.scss */
.icon-comments2:before {
  content: "\e9b2";
}

/* line 622, ../../../../sass/plugins/_iconmoon.scss */
.icon-cushions:before {
  content: "\e9b3";
}

/* line 625, ../../../../sass/plugins/_iconmoon.scss */
.icon-cushions2:before {
  content: "\e9b4";
}

/* line 628, ../../../../sass/plugins/_iconmoon.scss */
.icon-deaf:before {
  content: "\e9b5";
}

/* line 631, ../../../../sass/plugins/_iconmoon.scss */
.icon-design:before {
  content: "\e9b6";
}

/* line 634, ../../../../sass/plugins/_iconmoon.scss */
.icon-desktop:before {
  content: "\e9b7";
}

/* line 637, ../../../../sass/plugins/_iconmoon.scss */
.icon-dishes:before {
  content: "\e9b8";
}

/* line 640, ../../../../sass/plugins/_iconmoon.scss */
.icon-dollar:before {
  content: "\e9b9";
}

/* line 643, ../../../../sass/plugins/_iconmoon.scss */
.icon-download2:before {
  content: "\e9ba";
}

/* line 646, ../../../../sass/plugins/_iconmoon.scss */
.icon-family2:before {
  content: "\e9bb";
}

/* line 649, ../../../../sass/plugins/_iconmoon.scss */
.icon-fireworks:before {
  content: "\e9bc";
}

/* line 652, ../../../../sass/plugins/_iconmoon.scss */
.icon-flipflops:before {
  content: "\e9bd";
}

/* line 655, ../../../../sass/plugins/_iconmoon.scss */
.icon-friends2:before {
  content: "\e9be";
}

/* line 658, ../../../../sass/plugins/_iconmoon.scss */
.icon-fruit:before {
  content: "\e9bf";
}

/* line 661, ../../../../sass/plugins/_iconmoon.scss */
.icon-gender:before {
  content: "\e9c0";
}

/* line 664, ../../../../sass/plugins/_iconmoon.scss */
.icon-gift2:before {
  content: "\e9c1";
}

/* line 667, ../../../../sass/plugins/_iconmoon.scss */
.icon-gifts:before {
  content: "\e9c2";
}

/* line 670, ../../../../sass/plugins/_iconmoon.scss */
.icon-gold:before {
  content: "\e9c3";
}

/* line 673, ../../../../sass/plugins/_iconmoon.scss */
.icon-hairdrier2:before {
  content: "\e9c4";
}

/* line 676, ../../../../sass/plugins/_iconmoon.scss */
.icon-hammock:before {
  content: "\e9c5";
}

/* line 679, ../../../../sass/plugins/_iconmoon.scss */
.icon-hammock2:before {
  content: "\e9c6";
}

/* line 682, ../../../../sass/plugins/_iconmoon.scss */
.icon-heart2:before {
  content: "\e9c7";
}

/* line 685, ../../../../sass/plugins/_iconmoon.scss */
.icon-homepage:before {
  content: "\e9c8";
}

/* line 688, ../../../../sass/plugins/_iconmoon.scss */
.icon-hotel:before {
  content: "\e9c9";
}

/* line 691, ../../../../sass/plugins/_iconmoon.scss */
.icon-ice:before {
  content: "\e9ca";
}

/* line 694, ../../../../sass/plugins/_iconmoon.scss */
.icon-kettle:before {
  content: "\e9cb";
}

/* line 697, ../../../../sass/plugins/_iconmoon.scss */
.icon-kitchen:before {
  content: "\e9cc";
}

/* line 700, ../../../../sass/plugins/_iconmoon.scss */
.icon-latecheckout:before {
  content: "\e9cd";
}

/* line 703, ../../../../sass/plugins/_iconmoon.scss */
.icon-luggage:before {
  content: "\e9ce";
}

/* line 706, ../../../../sass/plugins/_iconmoon.scss */
.icon-meeting:before {
  content: "\e9cf";
}

/* line 709, ../../../../sass/plugins/_iconmoon.scss */
.icon-microwave:before {
  content: "\e9d0";
}

/* line 712, ../../../../sass/plugins/_iconmoon.scss */
.icon-monument2:before {
  content: "\e9d1";
}

/* line 715, ../../../../sass/plugins/_iconmoon.scss */
.icon-mouse1:before {
  content: "\e9d2";
}

/* line 718, ../../../../sass/plugins/_iconmoon.scss */
.icon-nani:before {
  content: "\e9d3";
}

/* line 721, ../../../../sass/plugins/_iconmoon.scss */
.icon-offer2:before {
  content: "\e9d4";
}

/* line 724, ../../../../sass/plugins/_iconmoon.scss */
.icon-offers:before {
  content: "\e9d5";
}

/* line 727, ../../../../sass/plugins/_iconmoon.scss */
.icon-percent2:before {
  content: "\e9d6";
}

/* line 730, ../../../../sass/plugins/_iconmoon.scss */
.icon-player:before {
  content: "\e9d7";
}

/* line 733, ../../../../sass/plugins/_iconmoon.scss */
.icon-romantic:before {
  content: "\e9d8";
}

/* line 736, ../../../../sass/plugins/_iconmoon.scss */
.icon-roomservice:before {
  content: "\e9d9";
}

/* line 739, ../../../../sass/plugins/_iconmoon.scss */
.icon-santa:before {
  content: "\e9da";
}

/* line 742, ../../../../sass/plugins/_iconmoon.scss */
.icon-satellite:before {
  content: "\e9db";
}

/* line 745, ../../../../sass/plugins/_iconmoon.scss */
.icon-sea2:before {
  content: "\e9dc";
}

/* line 748, ../../../../sass/plugins/_iconmoon.scss */
.icon-settings:before {
  content: "\e9dd";
}

/* line 751, ../../../../sass/plugins/_iconmoon.scss */
.icon-shield2:before {
  content: "\e9de";
}

/* line 754, ../../../../sass/plugins/_iconmoon.scss */
.icon-slippers:before {
  content: "\e9df";
}

/* line 757, ../../../../sass/plugins/_iconmoon.scss */
.icon-snowflakeeps:before {
  content: "\e9e0";
}

/* line 760, ../../../../sass/plugins/_iconmoon.scss */
.icon-soap:before {
  content: "\e9e1";
}

/* line 763, ../../../../sass/plugins/_iconmoon.scss */
.icon-sofa2:before {
  content: "\e9e2";
}

/* line 766, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialbreakfast:before {
  content: "\e9e3";
}

/* line 769, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialfamily:before {
  content: "\e9e4";
}

/* line 772, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialreception:before {
  content: "\e9e5";
}

/* line 775, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialshower:before {
  content: "\e9e6";
}

/* line 778, ../../../../sass/plugins/_iconmoon.scss */
.icon-suit:before {
  content: "\e9e7";
}

/* line 781, ../../../../sass/plugins/_iconmoon.scss */
.icon-sun2:before {
  content: "\e9e8";
}

/* line 784, ../../../../sass/plugins/_iconmoon.scss */
.icon-sun3:before {
  content: "\e9e9";
}

/* line 787, ../../../../sass/plugins/_iconmoon.scss */
.icon-tie:before {
  content: "\e9ea";
}

/* line 790, ../../../../sass/plugins/_iconmoon.scss */
.icon-toaster:before {
  content: "\e9eb";
}

/* line 793, ../../../../sass/plugins/_iconmoon.scss */
.icon-toilet:before {
  content: "\e9ec";
}

/* line 796, ../../../../sass/plugins/_iconmoon.scss */
.icon-washer2:before {
  content: "\e9ed";
}

/* line 799, ../../../../sass/plugins/_iconmoon.scss */
.icon-waterpark:before {
  content: "\e9ee";
}

/* line 802, ../../../../sass/plugins/_iconmoon.scss */
.icon-wine:before {
  content: "\e9ef";
}

/* line 805, ../../../../sass/plugins/_iconmoon.scss */
.icon-world:before {
  content: "\e9f0";
}

/* line 808, ../../../../sass/plugins/_iconmoon.scss */
.icon-www:before {
  content: "\e9f1";
}

/* line 811, ../../../../sass/plugins/_iconmoon.scss */
.icon-adults:before {
  content: "\e9f2";
}

/* line 814, ../../../../sass/plugins/_iconmoon.scss */
.icon-percentpig:before {
  content: "\e9f3";
}

/* line 817, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialwifi:before {
  content: "\e9f4";
}

/* line 820, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialbuilding:before {
  content: "\e9f5";
}

/* line 823, ../../../../sass/plugins/_iconmoon.scss */
.icon-speciallunch:before {
  content: "\e9f6";
}

/* line 826, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialweb:before {
  content: "\e9f7";
}

/* line 829, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialbed:before {
  content: "\e9f8";
}

/* line 832, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialevents:before {
  content: "\e9f9";
}

/* line 835, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialpool:before {
  content: "\e9fa";
}

/* line 838, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialbeds:before {
  content: "\e9fb";
}

/* line 841, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialcalendar:before {
  content: "\e9fc";
}

/* line 844, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialfitness:before {
  content: "\e9fd";
}

/* line 847, ../../../../sass/plugins/_iconmoon.scss */
.icon-speciallocation:before {
  content: "\e9fe";
}

/* line 850, ../../../../sass/plugins/_iconmoon.scss */
.icon-settings2:before {
  content: "\e9ff";
}

/* line 853, ../../../../sass/plugins/_iconmoon.scss */
.icon-nopets:before {
  content: "\ea00";
}

/* line 856, ../../../../sass/plugins/_iconmoon.scss */
.icon-videocamera:before {
  content: "\ea01";
}

/* line 859, ../../../../sass/plugins/_iconmoon.scss */
.icon-window1:before {
  content: "\ea02";
}

/* line 862, ../../../../sass/plugins/_iconmoon.scss */
.icon-offer:before {
  content: "\ea03";
}

/* line 865, ../../../../sass/plugins/_iconmoon.scss */
.icon-save:before {
  content: "\ea04";
}

/* line 868, ../../../../sass/plugins/_iconmoon.scss */
.icon-plane2:before {
  content: "\ea05";
}

/* line 871, ../../../../sass/plugins/_iconmoon.scss */
.icon-longarrow:before {
  content: "\ea06";
}

/* line 874, ../../../../sass/plugins/_iconmoon.scss */
.icon-paraty:before {
  content: "\ea07";
}

/* line 877, ../../../../sass/plugins/_iconmoon.scss */
.icon-horseshoe:before {
  content: "\ea08";
}

/* line 880, ../../../../sass/plugins/_iconmoon.scss */
.icon-balloons:before {
  content: "\ea09";
}

/* line 883, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-tiger:before {
  content: "\ea0a";
}

/* line 886, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-2drinks:before {
  content: "\ea0b";
}

/* line 889, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-bbq:before {
  content: "\ea0c";
}

/* line 892, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-disco:before {
  content: "\ea0d";
}

/* line 895, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-pasta:before {
  content: "\ea0e";
}

/* line 898, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-snack:before {
  content: "\ea0f";
}

/* line 901, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-archery:before {
  content: "\ea10";
}

/* line 904, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-football:before {
  content: "\ea11";
}

/* line 907, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-gameboard:before {
  content: "\ea12";
}

/* line 910, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-golf:before {
  content: "\ea13";
}

/* line 913, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-hotbath:before {
  content: "\ea14";
}

/* line 916, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-hotpool:before {
  content: "\ea15";
}

/* line 919, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-iron:before {
  content: "\ea16";
}

/* line 922, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-jetshower:before {
  content: "\ea17";
}

/* line 925, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-massage:before {
  content: "\ea18";
}

/* line 928, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-petanque:before {
  content: "\ea19";
}

/* line 931, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-phone:before {
  content: "\ea1a";
}

/* line 934, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-shooting:before {
  content: "\ea1b";
}

/* line 937, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-volleyball:before {
  content: "\ea1c";
}

/* line 940, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-balloons:before {
  content: "\ea1d";
}

/* line 943, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-bike:before {
  content: "\ea1e";
}

/* line 946, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-gift:before {
  content: "\ea1f";
}

/* line 949, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-jacuzzi:before {
  content: "\ea20";
}

/* line 952, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-mouse:before {
  content: "\ea21";
}

/* line 955, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-movie:before {
  content: "\ea22";
}

/* line 958, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-playground:before {
  content: "\ea23";
}

/* line 961, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-playingcards:before {
  content: "\ea24";
}

/* line 964, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-shopping:before {
  content: "\ea25";
}

/* line 967, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-shower:before {
  content: "\ea26";
}

/* line 970, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-sofa:before {
  content: "\ea27";
}

/* line 973, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-washing:before {
  content: "\ea28";
}

/* line 976, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-bills:before {
  content: "\ea29";
}

/* line 979, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-car:before {
  content: "\ea2a";
}

/* line 982, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-creditcard:before {
  content: "\ea2b";
}

/* line 985, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-drink:before {
  content: "\ea2c";
}

/* line 988, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-fruit:before {
  content: "\ea2d";
}

/* line 991, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-lock:before {
  content: "\ea2e";
}

/* line 994, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-offer:before {
  content: "\ea2f";
}

/* line 997, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-spa:before {
  content: "\ea30";
}

/* line 1000, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-tennis:before {
  content: "\ea31";
}

/* line 1003, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-aircon:before {
  content: "\ea32";
}

/* line 1006, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-hairdryer:before {
  content: "\ea33";
}

/* line 1009, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-kitchen:before {
  content: "\ea34";
}

/* line 1012, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-tv:before {
  content: "\ea35";
}

/* line 1015, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-wifi:before {
  content: "\ea36";
}

/* line 1018, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-clock:before {
  content: "\ea37";
}

/* line 1021, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-lightning:before {
  content: "\ea38";
}

/* line 1024, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-piggybank:before {
  content: "\ea39";
}

/* line 1027, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-pricetag:before {
  content: "\ea3a";
}

/* line 1030, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-creditcard2:before {
  content: "\ea3b";
}

/* line 1033, ../../../../sass/plugins/_iconmoon.scss */
.icon-360:before {
  content: "\ea3c";
}

/* line 1036, ../../../../sass/plugins/_iconmoon.scss */
.icon-contactless:before {
  content: "\ea3d";
}

/* line 1039, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-umbrella:before {
  content: "\ea3e";
}

/* line 1042, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-sun:before {
  content: "\ea3f";
}

/* line 1045, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-slippers:before {
  content: "\ea40";
}

/* line 1048, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-mail:before {
  content: "\ea41";
}

/* line 1051, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-comment:before {
  content: "\ea42";
}

/* line 3, ../../../../sass/plugins/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 8, ../../../../sass/plugins/_mixins.scss */
.center_xy_before:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 13, ../../../../sass/plugins/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 17, ../../../../sass/plugins/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 21, ../../../../sass/plugins/_mixins.scss */
.center_y_before:before {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 25, ../../../../sass/plugins/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 30, ../../../../sass/plugins/_mixins.scss */
.fs:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
}

/* line 110, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:before, .icon-xcross:after {
  content: '';
  width: 100%;
  height: 2px;
  background: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 124, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -moz-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 61, ../../../../sass/plugins/_mixins.scss */
.display_flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* line 1, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown {
  margin: 40px 20px 20px;
  text-align: center;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  bottom: 180px;
  left: 48% !important;
  color: white;
}
/* line 9, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown div {
  display: inline-block;
  font-size: 16px;
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 0;
  padding-bottom: 0;
  text-transform: uppercase;
}
/* line 20, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .days {
  font-weight: 600;
}
/* line 23, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .date {
  display: block;
  font-size: 4.5rem;
}
/* line 28, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .title_format {
  font-weight: 600;
}

@media screen and (max-width: 800px) {
  /* line 35, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown {
    display: inline-block;
    width: 380px;
    top: 330px;
    left: 45% !important;
  }
  /* line 40, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown div {
    font-size: 10px;
  }
  /* line 43, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown .date {
    font-size: 2.5rem;
  }
}
/**
 * Owl Carousel v2.2.1
 * Copyright 2013-2017 David Deutsch
 * Licensed under  ()
 */
/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel, .owl-carousel .owl-item {
  -webkit-tap-highlight-color: transparent;
  position: relative;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel {
  display: none;
  width: 100%;
  z-index: 1;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage {
  position: relative;
  -ms-touch-action: pan-Y;
  -moz-backface-visibility: hidden;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage:after {
  content: ".";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage-outer {
  position: relative;
  overflow: hidden;
  -webkit-transform: translate3d(0, 0, 0);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item, .owl-carousel .owl-wrapper {
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item {
  min-height: 1px;
  float: left;
  -webkit-backface-visibility: hidden;
  -webkit-touch-callout: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item img {
  display: block;
  width: 100%;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-dots.disabled, .owl-carousel .owl-nav.disabled {
  display: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.no-js .owl-carousel, .owl-carousel.owl-loaded {
  display: block;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-dot, .owl-carousel .owl-nav .owl-next, .owl-carousel .owl-nav .owl-prev {
  cursor: pointer;
  cursor: hand;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-loading {
  opacity: 0;
  display: block;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-hidden {
  opacity: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-refresh .owl-item {
  visibility: hidden;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-drag .owl-item {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-grab {
  cursor: move;
  cursor: grab;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-rtl {
  direction: rtl;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-rtl .owl-item {
  float: right;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-animated-in {
  z-index: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-animated-out {
  z-index: 1;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .fadeOut {
  animation-name: fadeOut;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-height {
  transition: height .5s ease-in-out;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item .owl-lazy {
  opacity: 0;
  transition: opacity .4s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item img.owl-lazy {
  transform-style: preserve-3d;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-wrapper {
  position: relative;
  height: 100%;
  background: #000;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-play-icon {
  position: absolute;
  height: 80px;
  width: 80px;
  left: 50%;
  top: 50%;
  margin-left: -40px;
  margin-top: -40px;
  background: url(owl.video.play.png) no-repeat;
  cursor: pointer;
  z-index: 1;
  -webkit-backface-visibility: hidden;
  transition: transform .1s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-play-icon:hover {
  -ms-transform: scale(1.3, 1.3);
  transform: scale(1.3, 1.3);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-playing .owl-video-play-icon, .owl-carousel .owl-video-playing .owl-video-tn {
  display: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-tn {
  opacity: 0;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: opacity .4s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-frame {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
}

/* line 3, ../../../../sass/booking/_booking_engine_5.scss */
.booking_widget {
  position: relative;
}

/* line 7, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 {
  width: 275px;
  display: block;
}
/* line 11, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 form.booking_form {
  padding: 0 !important;
}
/* line 15, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 .booking_form_title {
  background: #383838;
}

/* line 20, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper {
  border-bottom: 1px solid lightgray;
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat 92% 70% !important;
}
/* line 24, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
  display: inline-block;
  margin: 10px 0 0 10px;
}

/* line 32, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input {
  border: none;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 25px;
  font-weight: 600;
  background: white;
}
/* line 40, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input::-webkit-input-placeholder {
  color: #585d63;
}
/* line 44, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input:-moz-placeholder {
  /* Firefox 18- */
  color: #585d63;
}
/* line 48, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input::-moz-placeholder {
  /* Firefox 19+ */
  color: #585d63;
}
/* line 52, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input:-ms-input-placeholder {
  color: #585d63;
}

/* line 57, ../../../../sass/booking/_booking_engine_5.scss */
.entry_date_wrapper {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 66.6%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}

/* line 72, ../../../../sass/booking/_booking_engine_5.scss */
.departure_date_wrapper, .half_size {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 50%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}
/* line 86, ../../../../sass/booking/_booking_engine_5.scss */
.departure_date_wrapper .date_day, .half_size .date_day {
  font-size: 21px !important;
}

/* line 91, ../../../../sass/booking/_booking_engine_5.scss */
.nights_number_wrapper {
  float: right;
  height: 70px;
  border-bottom: 1px solid lightgrey;
  box-sizing: border-box;
  width: 33.3%;
  padding: 5px 10px;
}

/* line 100, ../../../../sass/booking/_booking_engine_5.scss */
.num_nights_label, .entry_date_label, .children_label, .rooms_label, .adults_label, .promocode_label, .departure_date_label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/* line 106, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 30%;
}

/*===== Entry date =====*/
/* line 113, ../../../../sass/booking/_booking_engine_5.scss */
.date_box.entry_date, .date_box.departure_date {
  font-size: 20px;
  background: url(/static_1/images/booking_5/calendar.png) no-repeat center right;
  margin-top: 9px;
  background-size: 29px;
}
/* line 119, ../../../../sass/booking/_booking_engine_5.scss */
.date_box.entry_date .date_day, .date_box.departure_date .date_day {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 25px;
  font-weight: 600;
}

/*====== Booking button ====*/
/* line 128, ../../../../sass/booking/_booking_engine_5.scss */
button.submit_button {
  width: 100%;
  border: 0;
  border-radius: 0;
  background: #ffd600;
  height: 55px;
  text-transform: uppercase;
  font-weight: bolder;
  font-size: 21px;
  color: #565656;
  cursor: pointer;
}

/*====== Rooms section ======*/
/* line 142, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 33.3%;
  padding: 7px 10px 5px;
  box-sizing: border-box;
}
/* line 149, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper .room_list_wrapper li.room {
  float: right;
  width: 69%;
}
/* line 155, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper .adults_selector {
  height: 70px;
  float: left;
  border-right: 1px solid gray;
  width: 46.5%;
}

/* line 163, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper {
  display: table;
  float: right;
  width: 66.6%;
}
/* line 168, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .room_title {
  display: none;
}
/* line 172, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .adults_selector {
  border-right: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
}
/* line 177, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
  width: 50%;
  display: inline-block;
  height: 70px;
  box-sizing: border-box;
  padding: 7px 10px 5px;
}
/* line 185, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .children_selector {
  width: 50% !important;
  padding: 7px 10px 5px;
  float: right;
}

/* line 192, ../../../../sass/booking/_booking_engine_5.scss */
.room_list .room {
  height: 70px;
}

/* line 196, ../../../../sass/booking/_booking_engine_5.scss */
.room_list .room.room3, .room_list .room.room2 {
  border-top: 1px solid lightgrey;
}

/*==== Promocode section  ====*/
/* line 201, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_wrapper {
  display: table;
  width: 100%;
  padding: 10px 10px 5px;
  border-top: 1px solid lightgrey;
  box-sizing: border-box;
}

/* line 209, ../../../../sass/booking/_booking_engine_5.scss */
label.promocode_label {
  display: block;
}

/* line 213, ../../../../sass/booking/_booking_engine_5.scss */
input.promocode_input {
  border: 0;
  width: 100%;
  display: block;
  height: 30px;
  font-size: 13px;
  font-weight: lighter;
  margin-top: 2px;
  font-family: sans-serif;
}

/*====== Selectric ======*/
/* line 225, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems {
  overflow: scroll !important;
  width: 92px !important;
  margin-left: -11px;
  margin-top: 2px;
}
/* line 231, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 237, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li {
  font-weight: 500;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAADAQMAAABs5if8AAAABlBMVEXOzs7y8vKNS741AAAADklEQVQI12M4wPSAWQAABoQBtgsaY5kAAAAASUVORK5CYII=);
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  line-height: 29px !important;
  font-size: 18px !important;
  font-family: 'Source Sans Pro', sans-serif;
}
/* line 247, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li:hover {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 254, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems ul {
  z-index: 40;
}
/* line 258, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems .room {
  padding-top: 17px;
}

/* line 264, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 .selectric .button {
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 23px;
  margin-left: 0;
  margin-top: 8px;
  text-indent: 999px;
  height: 22px;
  font-weight: 600;
}

/* line 277, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper .selectric .label {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 28px;
  margin-left: 0;
  font-weight: 600;
}

/* line 285, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper .selectric {
  margin-top: 3px;
}

/* line 289, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper {
  width: 78px !important;
}

/*======= Phone web support =======*/
/* line 294, ../../../../sass/booking/_booking_engine_5.scss */
.wrapper-new-web-support.booking_form_title {
  border-radius: 0;
  padding: 5px 10px;
  font-family: 'Montserrat', sans-serif;
  font-size: 11px !important;
  margin-top: 11px;
}

/* line 302, ../../../../sass/booking/_booking_engine_5.scss */
.wrapper-new-web-support .web_support_number, .web_support_label_1 {
  font-size: 11px !important;
  margin-left: 5px;
}

/* line 307, ../../../../sass/booking/_booking_engine_5.scss */
img.phone_support_image {
  float: left;
  width: 28px;
  margin-top: 1px;
  margin-right: 12px;
}

/*======= Header title ======*/
/* line 315, ../../../../sass/booking/_booking_engine_5.scss */
.booking_title_1, .booking_title_2, .best_price {
  display: none;
}

/* line 319, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header {
  font-family: 'Montserrat', sans-serif;
  padding: 10px;
  display: table;
  width: 100%;
  box-sizing: border-box;
  cursor: pointer;
}
/* line 327, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header:hover {
  opacity: 0.8;
}
/* line 331, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header img.booking_header_discount {
  float: left;
  margin-right: 15px;
}
/* line 336, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header p.first_offer_name {
  font-size: 12px;
  color: #8096c7;
  margin-top: 9px;
}
/* line 342, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header p.second_offer_name {
  font-size: 10px;
  margin-top: 3px;
  color: gray;
}

/* line 349, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker {
  width: 283px;
}
/* line 352, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker .ui-widget-header {
  background: none !important;
  border: 0;
}
/* line 356, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker .ui-widget-header .ui-datepicker-title {
  color: #646464 !important;
  font-family: Verdana, Arial, sans-serif;
  font-weight: 300;
}

/* line 365, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker th {
  font-weight: 300;
  font-size: 14px;
}
/* line 371, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker .ui-state-default, body .ui-datepicker .ui-widget-content .ui-state-default, body .ui-datepicker .ui-widget-header .ui-state-default {
  background: transparent !important;
  border: 0 !important;
  color: #646464 !important;
  font-weight: 400;
  font-family: Circular, "Helvetica Neue", Helvetica, Arial, sans-serif;
  text-align: center;
  font-size: 13px;
}
/* line 382, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content .ui-state-hover {
  border: 0;
  background: #4B4B4B !important;
  color: white !important;
}
/* line 388, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content {
  border: 0;
  border-radius: 0;
  padding-bottom: 40px;
}
/* line 394, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-state-default {
  padding: 8px;
}
/* line 398, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-start_date {
  opacity: 1 !important;
}
/* line 401, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-start_date .ui-state-default {
  border: 0;
  background: #00B0B9 !important;
  color: white !important;
}
/* line 408, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-highlighted .ui-state-default {
  border: 0;
  background: rgba(40, 96, 144, 0.25) !important;
}
/* line 413, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content .ui-state-active {
  border: 0;
  background: #00B0B9 !important;
  color: white !important;
}
/* line 419, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-e, body span.ui-icon.ui-icon-circle-triangle-w {
  /* background: url(/static_1/images/mobile_img/renovation/flecha_der.png) no-repeat center;*/
  background: none;
  text-indent: 0;
  color: transparent;
  font-size: 0;
}
/* line 425, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-e:before, body span.ui-icon.ui-icon-circle-triangle-w:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  content: '\f105';
  font-family: "FontAwesome", "Font Awesome 5 Pro", sans-serif;
  font-size: 20px;
  color: black;
}
/* line 442, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-w:before {
  content: '\f104';
}
/* line 447, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker .ui-datepicker-next, body .ui-datepicker .ui-datepicker-prev {
  background: none;
}
/* line 451, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-next.ui-state-hover, body .ui-datepicker-prev.ui-datepicker-prev-hover {
  border: 1px solid #636363;
}

/* line 457, ../../../../sass/booking/_booking_engine_5.scss */
.datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-e:before, .datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-w:before {
  display: none;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

/* line 469, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 {
  border-radius: 0;
}
/* line 471, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-skin {
  border-radius: 0;
}
/* line 474, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide > * {
  padding: 0 !important;
  background-color: transparent;
}
/* line 478, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide .calendar_popup_wrapper {
  background-color: transparent;
}
/* line 482, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .full_screen_engine .container_popup_booking {
  background-color: transparent !important;
  padding: 24px !important;
}
/* line 487, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  color: gray;
}
/* line 490, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading {
  width: 50px;
  height: 50px;
  margin: auto;
  position: absolute;
  left: 0;
  right: 0;
  top: 100px;
  bottom: 0;
}
/* line 10, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:before {
  content: '';
  width: 50px;
  height: 5px;
  background: #000;
  opacity: 0.3;
  position: absolute;
  top: 59px;
  left: 0;
  border-radius: 50%;
  animation: box_shadow .5s linear infinite;
}
/* line 22, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:after {
  content: '';
  width: 50px;
  height: 50px;
  background: white;
  animation: box_animate .5s linear infinite;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 3px;
}
@keyframes box_animate {
  17% {
    border-bottom-right-radius: 3px;
  }
  25% {
    transform: translateY(9px) rotate(22.5deg);
  }
  50% {
    transform: translateY(18px) scale(1, 0.9) rotate(45deg);
    border-bottom-right-radius: 40px;
  }
  75% {
    transform: translateY(9px) rotate(67.5deg);
  }
  100% {
    transform: translateY(0) rotate(90deg);
  }
}
@keyframes box_shadow {
  0%, 100% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.2, 1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader {
  padding: 60px 0 70px;
}
/* line 3, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot {
  width: 24px;
  height: 24px;
  margin: 0 3px;
  background: #000;
  border-radius: 100%;
  display: inline-block;
  animation: slide 1s infinite;
}
/* line 11, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(1) {
  animation-delay: 0.1s;
}
/* line 14, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(2) {
  animation-delay: 0.2s;
}
/* line 17, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(3) {
  animation-delay: 0.3s;
}
/* line 20, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(4) {
  animation-delay: 0.4s;
}
/* line 23, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(5) {
  animation-delay: 0.5s;
}
@-moz-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-webkit-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-o-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube {
  margin: 20px auto;
  width: 40px;
  height: 40px;
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}
/* line 7, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube {
  float: left;
  width: 50%;
  height: 50%;
  position: relative;
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}
/* line 15, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  -webkit-animation: sk-foldCubeAngle 2.4s infinite linear both;
  animation: sk-foldCubeAngle 2.4s infinite linear both;
  -webkit-transform-origin: 100% 100%;
  -ms-transform-origin: 100% 100%;
  transform-origin: 100% 100%;
}
/* line 30, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2 {
  -webkit-transform: scale(1.1) rotateZ(90deg);
  transform: scale(1.1) rotateZ(90deg);
}
/* line 34, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3 {
  -webkit-transform: scale(1.1) rotateZ(270deg);
  transform: scale(1.1) rotateZ(270deg);
}
/* line 38, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4 {
  -webkit-transform: scale(1.1) rotateZ(180deg);
  transform: scale(1.1) rotateZ(180deg);
}
/* line 43, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2:before {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
/* line 49, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3:before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
}
/* line 55, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4:before {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
}
@-webkit-keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
@keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
/* line 1, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots {
  padding: 40px 0;
  text-align: center;
  animation: dots-rotate 2s linear infinite;
}
/* line 5, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  content: '';
  animation: dots-margin 2s linear infinite;
}
/* line 9, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots span, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  display: inline-block;
  vertical-align: middle;
  width: 30px;
  height: 30px;
  background-color: white;
  border-radius: 50%;
  margin: 5px;
}
@keyframes dots-margin {
  0% {
    margin: 5px;
  }
  12% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  25% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  50% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  62% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  75% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  100% {
    margin: 5px;
  }
}
@keyframes dots-rotate {
  0% {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  25% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  50% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  75% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/* line 500, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_top_popup_booking {
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: lighter;
  color: gray;
}
/* line 508, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  font-weight: bold;
  text-transform: uppercase;
}
/* line 513, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .container_popup_booking {
  width: 555px;
  display: block;
  padding: 30px 0;
  box-sizing: border-box;
  margin: 7px;
  border: 1px solid #3483b2;
}

/* line 524, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine {
  width: auto !important;
  height: auto !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}
/* line 532, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-outer {
  background: transparent;
}
/* line 537, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine.custom_color_overlay .fancybox-skin {
  background: transparent;
}
/* line 542, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-skin {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}
/* line 551, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-inner {
  overflow: visible;
}
/* line 555, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 568, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  border: 0 !important;
}
/* line 572, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: lighter;
}
/* line 575, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking strong {
  font-weight: bold;
  text-decoration: underline;
  font-size: 14px;
}

/* line 1, ../sass/_booking_engine.scss */
body.inner_section #inner_slider_container #full_wrapper_booking.deployed,
#full_wrapper_booking.deployed {
  position: fixed;
  bottom: auto !important;
  top: 0;
  background: #5F319C;
  height: auto;
}
/* line 8, ../sass/_booking_engine.scss */
body.inner_section #inner_slider_container #full_wrapper_booking.deployed div#wrapper_booking,
#full_wrapper_booking.deployed div#wrapper_booking {
  padding: 8px 0;
}
/* line 11, ../sass/_booking_engine.scss */
body.inner_section #inner_slider_container #full_wrapper_booking.deployed .room_list_wrapper,
#full_wrapper_booking.deployed .room_list_wrapper {
  top: 75px;
}

/* line 17, ../sass/_booking_engine.scss */
#full_wrapper_booking {
  position: absolute;
  bottom: 25%;
  left: 0;
  right: 0;
  z-index: 35;
  /*======== Booking Widget =======*/
}
/* line 25, ../sass/_booking_engine.scss */
#full_wrapper_booking div#wrapper_booking {
  width: 1121px;
  max-width: 100%;
  box-sizing: border-box;
  padding: 20px 0;
  background: transparent;
  position: relative;
}
/* line 34, ../sass/_booking_engine.scss */
#full_wrapper_booking div#wrapper_booking.home {
  bottom: 70px;
}
/* line 38, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget {
  position: absolute;
  left: 0px;
}
/* line 43, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 {
  width: 1121px;
}
/* line 46, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 .promocode_header {
  display: none;
}
/* line 51, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 form.booking_form {
  background: white;
}
/* line 55, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form_title .best_price {
  display: none;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}
/* line 63, ../sass/_booking_engine.scss */
#full_wrapper_booking .promocode_header p.first_offer_name {
  color: white;
}
/* line 66, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .date_box, #full_wrapper_booking .booking_widget .selectricWrapper, #full_wrapper_booking #booking_widget_popup .date_box, #full_wrapper_booking #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 70, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .date_box .date_day, #full_wrapper_booking #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 74, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectric {
  height: 38px;
  background: transparent;
}
/* line 79, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .adults_selector, #full_wrapper_booking .room_list_wrapper .children_selector {
  width: 33.3% !important;
  height: 68px;
  float: left;
  box-sizing: border-box;
}
/* line 86, ../sass/_booking_engine.scss */
#full_wrapper_booking button.submit_button {
  background: #00B0B9;
  color: white;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 90, ../sass/_booking_engine.scss */
#full_wrapper_booking button.submit_button:hover {
  background: #231b22;
}
/* line 95, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .web_support_label_1, #full_wrapper_booking .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 100, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support .web_support_number, #full_wrapper_booking .web_support_label_1 {
  line-height: 15px !important;
  font-size: 14px !important;
}
/* line 105, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title {
  margin-top: 0;
  text-align: center;
}
/* line 110, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title .web_support_label_2 {
  display: inline-block;
  margin: 0 10px;
}
/* line 115, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title .phone_support_image {
  display: none;
}
/* line 124, ../sass/_booking_engine.scss */
#full_wrapper_booking #data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}
/* line 128, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date, #full_wrapper_booking .date_box.departure_date {
  margin-top: 6px;
}
/* line 131, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date .date_year, #full_wrapper_booking .date_box.departure_date .date_year {
  display: none;
}
/* line 135, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date .date_day, #full_wrapper_booking .date_box.departure_date .date_day {
  border-bottom: 0 !important;
}
/* line 140, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectricWrapper {
  width: 100% !important;
}
/* line 143, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectricWrapper .selectric {
  margin-top: 0;
}
/* line 148, ../sass/_booking_engine.scss */
#full_wrapper_booking #slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}
/* line 152, ../sass/_booking_engine.scss */
#full_wrapper_booking .promocode_text {
  display: none;
}
/* line 156, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection {
  display: inline-block;
  vertical-align: top;
  float: left;
}
/* line 161, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection .entry_date_wrapper, #full_wrapper_booking .stay_selection .departure_date_wrapper {
  display: inline-block;
  vertical-align: top;
  width: 175px;
  float: left;
  border-top: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
}
/* line 170, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection .departure_date_wrapper {
  border-left: 0;
  border-right: 0;
}
/* line 175, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection .nights_number_wrapper {
  display: inline-block;
  width: 95px;
  float: left;
  vertical-align: top;
  border-top: 1px solid lightgrey;
}
/* line 184, ../sass/_booking_engine.scss */
#full_wrapper_booking .rooms_number_wrapper {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 169px;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
}
/* line 193, ../sass/_booking_engine.scss */
#full_wrapper_booking .rooms_number_wrapper .rooms_number {
  padding-left: 45px;
  box-sizing: border-box;
  background: url(/static_1/images/booking_5/rooms_number.png) no-repeat center left;
}
/* line 200, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper {
  display: none;
  vertical-align: top;
  float: left;
  border-bottom: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
  width: 275px;
  height: 68px;
}
/* line 209, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room {
  background: white;
  height: 68px;
}
/* line 213, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room.room2 .children_selector, #full_wrapper_booking .room_list_wrapper .room.room3 .children_selector {
  border-right: 1px solid lightgray;
}
/* line 217, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room.room3, #full_wrapper_booking .room_list_wrapper .room.room2 {
  border-bottom: 1px solid lightgray;
}
/* line 221, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room.room3 {
  border-top: 0;
}
/* line 227, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button {
  display: inline-block;
  width: auto;
  float: left;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  height: 69px;
}
/* line 235, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .promocode_wrapper {
  display: inline-block;
  vertical-align: top;
  float: left;
  height: 70px;
  width: 165px;
  padding: 10px 5px 5px;
}
/* line 243, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .promocode_input {
  font-size: 10px;
  padding-left: 25px;
  margin-left: -3px;
}
/* line 249, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .submit_button {
  width: 263px;
  display: inline-block;
  vertical-align: top;
  float: left;
  height: 70px;
  border: 1px solid lightgrey;
  display: block !important;
}
/* line 258, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .spinner_wrapper .spinner {
  display: none !important;
}

/* line 264, ../sass/_booking_engine.scss */
.babies_selector {
  width: 33.3%;
  display: inline-block;
  padding: 7px 10px 5px;
  box-sizing: border-box;
}
/* line 270, ../sass/_booking_engine.scss */
.babies_selector label {
  font-family: 'Open Sans', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/*=== Ocupancy selector ====*/
/* line 278, ../sass/_booking_engine.scss */
.guest_selector {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 173px;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
  padding: 7px 10px 5px;
  box-sizing: border-box;
  height: 64px;
  cursor: pointer;
}
/* line 291, ../sass/_booking_engine.scss */
.guest_selector span.placeholder_text {
  color: darkgray;
  font-size: 13px;
  font-weight: lighter;
  font-family: 'Open Sans', sans-serif;
  padding-top: 12px;
  float: left;
  display: block;
  text-transform: uppercase;
  padding-left: 33px;
  box-sizing: border-box;
  background: url(/static_1/images/booking_5/ocupancy.png) no-repeat bottom left;
  padding-bottom: 3px;
}
/* line 305, ../sass/_booking_engine.scss */
.guest_selector span.placeholder_text.selected_value {
  font-family: 'Open Sans', sans-serif;
  color: #585d63;
  font-size: 21px;
  padding-top: 3px;
  background-position-y: 8px;
  font-weight: 600;
}
/* line 315, ../sass/_booking_engine.scss */
.guest_selector > label {
  font-family: 'Open Sans', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
  cursor: pointer;
}
/* line 322, ../sass/_booking_engine.scss */
.guest_selector b.button {
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
  font-family: 'Open Sans', sans-serif;
  color: #585d63;
  font-size: 23px;
  margin-left: 0;
  text-indent: 999px;
  height: 22px;
  font-weight: 600;
  float: right;
}

/* line 335, ../sass/_booking_engine.scss */
#booking label {
  cursor: pointer;
}

/*===== Slider container ====*/
/* line 340, ../sass/_booking_engine.scss */
#slider_container {
  position: relative;
}

/* line 343, ../sass/_booking_engine.scss */
.booking-data-popup {
  background: rgba(0, 0, 0, 0.8);
  min-width: 1140px !important;
  min-height: 100%;
  position: fixed !important;
}

/* line 1, ../sass/_booking_widget_modal.scss */
.fancybox-wrap {
  /*======== Booking Widget =======*/
}
/* line 4, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .fancybox-outer {
  padding: 0 !important;
}
/* line 8, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .fancybox-inner {
  width: auto !important;
}
/* line 13, ../sass/_booking_widget_modal.scss */
.fancybox-wrap div#wrapper_booking {
  position: absolute;
  height: 420px;
  top: 145px;
  left: 0px;
  right: 0px;
  z-index: 35;
}
/* line 21, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget {
  position: absolute;
  left: 0px;
}
/* line 26, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header {
  text-align: center;
}
/* line 30, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.first_offer_name {
  font-size: 16px;
  text-transform: uppercase;
}
/* line 35, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header img.booking_header_discount {
  display: none;
}
/* line 39, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .date_box.entry_date, .fancybox-wrap .date_box.departure_date {
  background: none;
}
/* line 42, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .date_box.entry_date .date_year, .fancybox-wrap .date_box.departure_date .date_year {
  display: none;
}
/* line 47, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 {
  width: 305px;
}
/* line 51, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 form.booking_form {
  background: white;
}
/* line 55, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_form_title .best_price {
  display: none;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}
/* line 63, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.first_offer_name {
  color: white;
}
/* line 66, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .date_box, .fancybox-wrap .booking_widget .selectricWrapper, .fancybox-wrap #booking_widget_popup .date_box, .fancybox-wrap #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 70, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .date_box .date_day, .fancybox-wrap #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 74, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .selectric {
  height: 38px;
  background: transparent;
}
/* line 79, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .room_list_wrapper .adults_selector, .fancybox-wrap .room_list_wrapper .children_selector {
  width: 49.8%;
}
/* line 83, ../sass/_booking_widget_modal.scss */
.fancybox-wrap button.submit_button {
  background: #00B0B9 !important;
  color: white !important;
}
/* line 88, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .web_support_label_1, .fancybox-wrap .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 93, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .wrapper-new-web-support .web_support_number, .fancybox-wrap .web_support_label_1 {
  line-height: 15px !important;
}
/* line 97, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .wrapper-new-web-support.booking_form_title {
  background: gray !important;
}
/* line 101, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 {
  margin-top: 20px !important;
}
/* line 105, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}
/* line 109, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .date_box.entry_date {
  margin-top: 6px;
}
/* line 112, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .selectricWrapper .selectric {
  margin-top: 0px;
}
/* line 116, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}
/* line 120, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_text {
  display: none;
}

/*=====================================================================
  Selectric
======================================================================*/
/* line 5, ../../../../sass/booking/_selectric.scss */
.selectricWrapper {
  position: relative;
  margin: 0 0 0px;
  width: 80px;
  cursor: pointer;
}

/* line 12, ../../../../sass/booking/_selectric.scss */
.selectricDisabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 23, ../../../../sass/booking/_selectric.scss */
.selectricOpen {
  z-index: 9999;
}

/* line 27, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 31, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

/* line 38, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect select {
  position: absolute;
  left: -100%;
  display: none;
}

/* line 44, ../../../../sass/booking/_selectric.scss */
.selectricInput {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  _font: 0/0 a;
  background: none !important;
}

/* line 60, ../../../../sass/booking/_selectric.scss */
.selectricTempShow {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* line 67, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectric {
  z-index: 9999;
}

/* line 72, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 76, ../../../../sass/booking/_selectric.scss */
.selectric {
  background: white;
  position: relative;
  border-radius: 6px;
  height: 40px;
}

/* line 83, ../../../../sass/booking/_selectric.scss */
.selectric .label {
  display: block;
  overflow: hidden;
  font-size: 16px;
  line-height: 40px;
  color: #00B0B9;
  text-align: left;
  margin-left: 20px;
}

/* line 93, ../../../../sass/booking/_selectric.scss */
.selectric .button {
  zoom: 1;
  position: absolute;
  font: 0/0 a;
  overflow: hidden;
  margin: auto;
  top: 0;
  right: 2px;
  bottom: 0;
  width: 35px;
  height: 35px;
  border: 0;
  background: #00B0B9 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  padding: 0;
}

/* line 109, ../../../../sass/booking/_selectric.scss */
.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */
/* line 115, ../../../../sass/booking/_selectric.scss */
.selectricItems {
  display: none;
  position: absolute;
  overflow: hidden;
  top: 100%;
  left: 0;
  background: #F9F9F9;
  border: 1px solid #CCC;
  z-index: 9998;
  -webkit-box-shadow: 0 0 10px -6px;
  box-shadow: 0 0 10px -6px;
}

/* line 128, ../../../../sass/booking/_selectric.scss */
.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* line 138, ../../../../sass/booking/_selectric.scss */
.selectricItems li {
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 146, ../../../../sass/booking/_selectric.scss */
.selectricItems li:hover {
  background: #F0F0F0;
  color: #444;
}
/* line 151, ../../../../sass/booking/_selectric.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}

/* line 158, ../../../../sass/booking/_selectric.scss */
.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;
  cursor: default;
}

/* line 3, ../sass/_booking_popup.scss */
html[lang="en"] div#data .selectricWrapper.selector_adultos p.label, html[lang="en"] div#data .selectricWrapper.selector_bebes p.label {
  font-size: 16px !important;
}
/* line 6, ../sass/_booking_popup.scss */
html[lang="en"] div#data .selectricWrapper.selector_adultos p.label {
  text-align: center;
}
/* line 9, ../sass/_booking_popup.scss */
html[lang="en"] div#data .selectricWrapper.selector_ninos p.label {
  font-size: 14px !important;
}

/* line 16, ../sass/_booking_popup.scss */
.booking-data-popup .fancybox-outer {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  background: transparent;
}
/* line 22, ../sass/_booking_popup.scss */
.booking-data-popup + #fancybox-overlay {
  opacity: 0 !important;
}
/* line 26, ../sass/_booking_popup.scss */
.booking-data-popup div#wrapper_booking_fancybox {
  display: table;
  width: 100%;
  margin: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 35, ../sass/_booking_popup.scss */
.booking-data-popup .adultos.numero_personas > label, .booking-data-popup .ninos.numero_personas > label, .booking-data-popup .bebes.numero_personas > label {
  display: none !important;
}

/* line 43, ../sass/_booking_popup.scss */
div#data .booking_title1, div#data .best_price {
  display: none;
}
/* line 47, ../sass/_booking_popup.scss */
div#data div#booking_engine_title {
  display: block;
  float: none;
  text-align: center;
}
/* line 53, ../sass/_booking_popup.scss */
div#data #motor_reserva {
  width: 595px;
  margin: auto;
  display: table;
  background: #F1EFE9;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}
/* line 61, ../sass/_booking_popup.scss */
div#data div#fecha_entrada, div#data div#fecha_salida {
  width: 290px;
  float: left;
  height: 125px;
  background: white;
}
/* line 68, ../sass/_booking_popup.scss */
div#data div#fecha_salida {
  float: right;
  margin-left: 0 !important;
}
/* line 73, ../sass/_booking_popup.scss */
div#data label#titulo_fecha_entrada, div#data label#titulo_fecha_salida {
  display: block;
  color: #00B0B9;
  width: 100% !important;
  text-align: center;
  margin-top: 20px;
  text-transform: uppercase;
  font-size: 17px;
  font-weight: bolder;
}
/* line 84, ../sass/_booking_popup.scss */
div#data #contenedor_fechas {
  width: 100%;
  margin-bottom: 10px;
}
/* line 89, ../sass/_booking_popup.scss */
div#data .wrapper-old-web-support {
  display: none !important;
}
/* line 93, ../sass/_booking_popup.scss */
div#data #fecha_entrada input, div#data #fecha_salida input {
  border: 0 !important;
  height: 84px !important;
  width: 100% !important;
  text-align: center !important;
  box-sizing: border-box !important;
  font-size: 31px !important;
  color: #4b4b4b !important;
  padding-right: 40px;
  background: transparent url(/static_1/images/booking_5/entry_date.png) no-repeat 85% center !important;
}
/* line 104, ../sass/_booking_popup.scss */
div#data #fecha_entrada input::-webkit-input-placeholder, div#data #fecha_salida input::-webkit-input-placeholder {
  color: #4b4b4b !important;
}
/* line 109, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones {
  margin: auto;
  width: 290px;
  float: left;
  height: 125px;
  background: white;
}
/* line 116, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones label {
  display: block;
  color: #00B0B9;
  width: 100% !important;
  text-align: center;
  margin-top: 20px;
  text-transform: uppercase;
  font-size: 17px;
  float: none;
  font-weight: bolder;
}
/* line 128, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones select#selector_habitaciones {
  -webkit-border-radius: 0 !important;
  -moz-border-radius: 0 !important;
  border-radius: 0 !important;
  border: 0;
  width: 260px;
  float: left;
  height: 125px;
  background: white;
  -webkit-appearance: none;
}
/* line 139, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones select#selector_habitaciones option {
  text-align: center;
}
/* line 144, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones .selectric {
  height: 83px;
  border-radius: 0;
}
/* line 148, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones .selectric p.label {
  color: #4b4b4b;
  text-align: center;
  box-sizing: border-box !important;
  font-size: 31px !important;
  padding-top: 22px;
}
/* line 156, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones .selectric .button {
  background: transparent url(/img/onama/ico_down.png) no-repeat center !important;
  right: 27px;
}
/* line 162, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones .selectricItems li {
  color: #4b4b4b;
  text-align: center;
  box-sizing: border-box !important;
  font-size: 21px !important;
  padding: 12px 12px 10px;
}
/* line 171, ../sass/_booking_popup.scss */
div#data .selectricWrapper {
  width: 100% !important;
}
/* line 175, ../sass/_booking_popup.scss */
div#data #contenedor_opciones {
  float: right;
  margin-top: -125px;
}
/* line 179, ../sass/_booking_popup.scss */
div#data #contenedor_opciones #hab1, div#data #contenedor_opciones #hab2, div#data #contenedor_opciones #hab3 {
  margin: auto;
  width: 290px;
  float: left;
  background: white;
  height: 125px;
}
/* line 187, ../sass/_booking_popup.scss */
div#data #contenedor_opciones #hab1 {
  margin-left: 305px;
}
/* line 191, ../sass/_booking_popup.scss */
div#data #contenedor_opciones #hab2, div#data #contenedor_opciones #hab3 {
  margin-top: 20px;
  display: block !important;
}
/* line 196, ../sass/_booking_popup.scss */
div#data #contenedor_opciones #hab3 {
  float: right;
}
/* line 200, ../sass/_booking_popup.scss */
div#data #contenedor_opciones label.numero_habitacion {
  color: #00B0B9;
  font-weight: bolder;
  width: 100% !important;
  text-align: center;
  margin-top: 20px;
  display: block !important;
  text-transform: uppercase;
  font-size: 17px;
  float: none;
}
/* line 213, ../sass/_booking_popup.scss */
div#data .adultos.numero_personas, div#data .ninos.numero_personas, div#data .bebes.numero_personas {
  margin: 0;
  position: relative;
  display: inline-block;
}
/* line 218, ../sass/_booking_popup.scss */
div#data .adultos.numero_personas option, div#data .ninos.numero_personas option, div#data .bebes.numero_personas option {
  display: none;
}
/* line 223, ../sass/_booking_popup.scss */
div#data .adultos.numero_personas {
  width: 36%;
}
/* line 227, ../sass/_booking_popup.scss */
div#data .ninos.numero_personas {
  width: 32%;
}
/* line 230, ../sass/_booking_popup.scss */
div#data .ninos.numero_personas .selectricItems {
  left: -93px !important;
}
/* line 235, ../sass/_booking_popup.scss */
div#data .bebes.numero_personas {
  width: 32%;
}
/* line 238, ../sass/_booking_popup.scss */
div#data .bebes.numero_personas .selectricItems {
  left: -186px !important;
}
/* line 243, ../sass/_booking_popup.scss */
div#data .ninos {
  float: left;
}
/* line 246, ../sass/_booking_popup.scss */
div#data .ninos label#info_ninos {
  position: absolute;
  top: 20px;
  color: black;
  right: 0px;
  font-size: 9px !important;
  display: inline-block;
}
/* line 257, ../sass/_booking_popup.scss */
div#data .selectricWrapper.selector_adultos .selectric, div#data .selectricWrapper.selector_ninos .selectric, div#data .selectricWrapper.selector_bebes .selectric {
  height: 83px;
  border-radius: 0;
}
/* line 262, ../sass/_booking_popup.scss */
div#data .selectricWrapper.selector_adultos p.label, div#data .selectricWrapper.selector_ninos p.label, div#data .selectricWrapper.selector_bebes p.label {
  color: #4b4b4b;
  text-align: left;
  padding-right: 0 !important;
  box-sizing: border-box !important;
  padding-top: 23px;
  font-size: 18px !important;
  padding-left: 9px;
}
/* line 272, ../sass/_booking_popup.scss */
div#data .selectricWrapper.selector_adultos .button, div#data .selectricWrapper.selector_ninos .button, div#data .selectricWrapper.selector_bebes .button {
  background: transparent url(/img/onama/ico_down.png) no-repeat center !important;
  width: 16px;
  height: 20px;
}
/* line 278, ../sass/_booking_popup.scss */
div#data .selectricWrapper.selector_adultos .selectricItems li, div#data .selectricWrapper.selector_ninos .selectricItems li, div#data .selectricWrapper.selector_bebes .selectricItems li {
  color: #4b4b4b;
  text-align: center;
  box-sizing: border-box !important;
  font-size: 16px !important;
  padding: 6px 12px 4px;
}
/* line 287, ../sass/_booking_popup.scss */
div#data fieldset#envio {
  width: 100%;
  margin-left: 0;
}
/* line 291, ../sass/_booking_popup.scss */
div#data fieldset#envio input#promocode {
  float: left;
  width: 290px;
  border-radius: 0;
  border: 0;
  box-sizing: border-box;
  margin-top: 10px;
  height: 90px;
  text-align: center;
  background: #5F319C;
  font-size: 31px !important;
  font-weight: lighter;
  color: white;
}
/* line 305, ../sass/_booking_popup.scss */
div#data fieldset#envio input#promocode::-webkit-input-placeholder {
  color: white;
  font-size: 18px;
  font-weight: lighter;
  text-transform: uppercase;
}
/* line 313, ../sass/_booking_popup.scss */
div#data fieldset#envio button#search-button {
  display: block;
  float: right;
  height: 90px;
  width: 290px;
  border-radius: 0;
  border: 0;
  box-sizing: border-box;
  margin-top: 10px;
  background: #00B0B9;
  color: white;
  text-transform: uppercase;
  font-size: 27px !important;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 327, ../sass/_booking_popup.scss */
div#data fieldset#envio button#search-button:hover {
  background: #231b22;
}
/* line 334, ../sass/_booking_popup.scss */
div#data div#hab2 .disabled_overlay, div#data div#hab3 .disabled_overlay {
  display: none;
}
/* line 338, ../sass/_booking_popup.scss */
div#data div#hab2.disabled, div#data div#hab3.disabled {
  opacity: 0.4;
  position: relative;
}
/* line 342, ../sass/_booking_popup.scss */
div#data div#hab2.disabled .disabled_overlay, div#data div#hab3.disabled .disabled_overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: block;
}
/* line 354, ../sass/_booking_popup.scss */
div#data #booking_engine_title #booking_title1, div#data #booking_engine_title #best_price {
  display: none;
}
/* line 358, ../sass/_booking_popup.scss */
div#data #booking_engine_title h4#booking_title2 {
  color: #231b22;
  margin-bottom: 25px;
  text-transform: uppercase;
  font-size: 22px;
  margin-top: 0;
}
/* line 368, ../sass/_booking_popup.scss */
div#data #booking_engine_title #booking_title2:after {
  content: '';
  display: block;
  width: 70px;
  height: 1px;
  background: #231b22;
  margin: 10px auto;
}
/* line 377, ../sass/_booking_popup.scss */
div#data #booking_engine_title #booking_title2 span {
  font-weight: lighter;
}
/* line 383, ../sass/_booking_popup.scss */
div#data #contenedor_opciones div#hab2.disabled {
  display: none !important;
}
/* line 386, ../sass/_booking_popup.scss */
div#data #contenedor_opciones div#hab2.disabled + #hab3 {
  display: none !important;
}
/* line 392, ../sass/_booking_popup.scss */
div#data .selectricItems {
  width: 288px !important;
  top: 84% !important;
  left: 11px !important;
}

/* line 399, ../sass/_booking_popup.scss */
.booking-data-popup .fancybox-close {
  position: absolute;
  top: 25%;
  left: calc(50% + 308px);
  -webkit-transform: translate(0, -50%);
  -moz-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  -o-transform: translate(0, -50%);
  transform: translate(0, -50%);
  background: #231b22;
  width: 45px;
  height: 45px;
  text-align: center;
  border-radius: 0 5px 5px 0;
}
/* line 414, ../sass/_booking_popup.scss */
.booking-data-popup .fancybox-close:before {
  content: "x";
  color: white;
  font-size: 50px;
  line-height: 36px;
}

/* line 422, ../sass/_booking_popup.scss */
.contact_bottom_popup {
  display: block;
  width: 535px;
  margin: 20px auto;
  text-align: center;
  color: white;
}
/* line 429, ../sass/_booking_popup.scss */
.contact_bottom_popup .phone_hotel_booking_popup, .contact_bottom_popup .email_hotel {
  display: inline-block;
  padding-left: 30px;
  line-height: 25px;
}
/* line 435, ../sass/_booking_popup.scss */
.contact_bottom_popup .phone_hotel_booking_popup {
  margin-right: 10px;
  background: url(/img/onama/booking_icos/phone_ico.png) no-repeat left center;
}
/* line 440, ../sass/_booking_popup.scss */
.contact_bottom_popup .email_hotel {
  background: url(/img/onama/booking_icos/mail_ico.png) no-repeat left center;
}

/* line 1, ../sass/_header.scss */
header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 30;
  padding: 20px calc((100% - 1140px) / 2);
  background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0));
}
/* line 10, ../sass/_header.scss */
header.deployed {
  top: 80px;
  background: #5f319c;
  padding: 0 calc((100% - 1140px) / 2);
}
/* line 15, ../sass/_header.scss */
header.deployed #logoHeader img {
  display: none;
}
/* line 17, ../sass/_header.scss */
header.deployed #logoHeader img.floating_logo {
  display: block;
}
/* line 22, ../sass/_header.scss */
header.deployed .right_header {
  padding: 15px 0;
}
/* line 24, ../sass/_header.scss */
header.deployed .right_header .top_sections {
  display: none;
}
/* line 30, ../sass/_header.scss */
header.no_widget {
  top: 0 !important;
}
/* line 34, ../sass/_header.scss */
header #logoHeader {
  display: inline-block;
  vertical-align: top;
  width: 200px;
}
/* line 38, ../sass/_header.scss */
header #logoHeader .floating_logo {
  display: none;
}
/* line 42, ../sass/_header.scss */
header .right_header {
  display: inline-block;
  vertical-align: top;
  text-align: right;
  width: calc(100% - 200px);
}
/* line 47, ../sass/_header.scss */
header .right_header .top_sections {
  padding: 10px;
}
/* line 49, ../sass/_header.scss */
header .right_header .top_sections .top_section, header .right_header .top_sections .oficial_site {
  padding: 0 5px;
  display: inline-block;
  vertical-align: middle;
}
/* line 53, ../sass/_header.scss */
header .right_header .top_sections .top_section:hover, header .right_header .top_sections .oficial_site:hover {
  opacity: .8;
}
/* line 56, ../sass/_header.scss */
header .right_header .top_sections .top_section.bg_white, header .right_header .top_sections .oficial_site.bg_white {
  background-color: white;
  color: #00B0B9;
  border-radius: 10px;
}
/* line 62, ../sass/_header.scss */
header .right_header .top_sections .extra_info_link {
  display: inline-block;
  vertical-align: middle;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 12px;
  text-decoration: underline;
}
/* line 69, ../sass/_header.scss */
header .right_header .top_sections .extra_info_link i {
  margin-right: 5px;
  font-size: 18px;
}
/* line 74, ../sass/_header.scss */
header .right_header .top_sections a {
  color: white;
  display: inline-block;
  text-decoration: none;
  letter-spacing: 1px;
}
/* line 79, ../sass/_header.scss */
header .right_header .top_sections a span {
  display: inline-block;
  vertical-align: middle;
  font-weight: 300;
  text-transform: uppercase;
  font-size: 12px;
}
/* line 86, ../sass/_header.scss */
header .right_header .top_sections a .icon {
  position: relative;
  width: 30px;
  height: 30px;
  border-radius: 50%;
}
/* line 91, ../sass/_header.scss */
header .right_header .top_sections a .icon img, header .right_header .top_sections a .icon i.fa {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 16px;
}
/* line 94, ../sass/_header.scss */
header .right_header .top_sections a .icon img[class*=" icon-"], header .right_header .top_sections a .icon i.fa[class*=" icon-"] {
  font-size: 20px;
}
/* line 98, ../sass/_header.scss */
header .right_header .top_sections a .icon img {
  width: 50px;
  max-width: none;
}
/* line 104, ../sass/_header.scss */
header .right_header .top_sections #lang {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  padding: 0 5px;
}
/* line 109, ../sass/_header.scss */
header .right_header .top_sections #lang img {
  border-radius: 50%;
  width: 15px;
  height: 15px;
}
/* line 114, ../sass/_header.scss */
header .right_header .top_sections #lang .language_selected {
  display: block;
  position: relative;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
}
/* line 121, ../sass/_header.scss */
header .right_header .top_sections #lang .language_selected img, header .right_header .top_sections #lang .language_selected i.fa {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 5px;
}
/* line 125, ../sass/_header.scss */
header .right_header .top_sections #lang .language_selected i.fa {
  color: white;
  left: auto;
  right: 1px;
  font-size: 12px;
}
/* line 132, ../sass/_header.scss */
header .right_header .top_sections #lang .language_selector {
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  padding: 5px;
}
/* line 139, ../sass/_header.scss */
header .right_header .top_sections #lang .language_selector a {
  display: block;
  width: 100px;
  white-space: nowrap;
  padding: 3px 10px;
  text-align: left;
  color: #333;
}
/* line 146, ../sass/_header.scss */
header .right_header .top_sections #lang .language_selector a:hover {
  background: #5F319C;
  color: white;
}
/* line 150, ../sass/_header.scss */
header .right_header .top_sections #lang .language_selector a img, header .right_header .top_sections #lang .language_selector a span {
  display: inline-block;
  vertical-align: middle;
  margin: 0 3px;
}
/* line 159, ../sass/_header.scss */
header .right_header #main_menu {
  display: inline-block;
}
/* line 161, ../sass/_header.scss */
header .right_header #main_menu .main-section-div-wrapper {
  display: inline-block;
}
/* line 163, ../sass/_header.scss */
header .right_header #main_menu .main-section-div-wrapper a {
  display: block;
  text-transform: uppercase;
  color: white;
  text-decoration: none;
  font-weight: 300;
  font-size: 14px;
  padding: 5px 10px;
  letter-spacing: 1px;
}
/* line 172, ../sass/_header.scss */
header .right_header #main_menu .main-section-div-wrapper a:hover {
  background: #5F319C;
}
/* line 178, ../sass/_header.scss */
header .right_header .button-promotion {
  display: inline-block;
  text-transform: uppercase;
  text-decoration: none;
  font-weight: 300;
  font-size: 14px;
  padding: 5px 20px;
  letter-spacing: 1px;
  background: white;
  color: #5F319C;
}
/* line 188, ../sass/_header.scss */
header .right_header .button-promotion:hover {
  background: #5F319C;
  color: white;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/
/* line 2, ../sass/_template_specific.scss */
.lightboxOverlay, .lightbox {
  min-width: 1140px;
}

/* line 6, ../sass/_template_specific.scss */
#full_wrapper_booking.d-none {
  display: none;
}

/* line 11, ../sass/_template_specific.scss */
body .datepicker_wrapper_element .header_datepicker {
  background: #00B0B9 !important;
}
/* line 16, ../sass/_template_specific.scss */
body .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-state-active, body .datepicker_wrapper_element .datepicker_ext_inf_ed .ui-state-active {
  background: #5F319C !important;
  color: white !important;
}
/* line 22, ../sass/_template_specific.scss */
body .datepicker_wrapper_element .months_selector_container .cheapest_month_selector {
  background: #00B0B9 !important;
  padding: 7px 0 7px;
}

/* line 29, ../sass/_template_specific.scss */
body.web-fancybox-open .ui-state-default {
  border: 1px solid white !important;
}
/* line 33, ../sass/_template_specific.scss */
body.web-fancybox-open .ui-datepicker-title {
  color: white !important;
}
/* line 37, ../sass/_template_specific.scss */
body.web-fancybox-open .ui-widget-header {
  background: #00B0B9 !important;
}
/* line 41, ../sass/_template_specific.scss */
body.web-fancybox-open .ui-datepicker .ui-widget-header .ui-datepicker-title {
  color: white !important;
}
/* line 45, ../sass/_template_specific.scss */
body.web-fancybox-open .ui-state-default, body.web-fancybox-open .ui-widget-content .ui-state-default, body.web-fancybox-open .ui-widget-header .ui-state-default {
  background: #00B0B9 !important;
  color: white;
}
/* line 50, ../sass/_template_specific.scss */
body.web-fancybox-open .ui-datepicker .ui-state-default {
  color: white !important;
}
/* line 53, ../sass/_template_specific.scss */
body.web-fancybox-open .ui-datepicker .ui-state-default:hover {
  opacity: 0.8;
}
/* line 58, ../sass/_template_specific.scss */
body.web-fancybox-open .ui-datepicker .ui-state-active {
  background: #231b22 !important;
}

/* line 63, ../sass/_template_specific.scss */
body {
  font-family: 'Open Sans', sans-serif;
}

/* line 79, ../sass/_template_specific.scss */
.skype_button {
  display: inline-block;
}
/* line 90, ../sass/_template_specific.scss */
.skype_button img {
  margin: 0 10px !important;
  vertical-align: 0 !important;
}

/*=== Specific languages ====*/
/* line 98, ../sass/_template_specific.scss */
body.de #slider_container .revolution_slider_description .right_rev_description .ticks_rev_wrapper, body.nl #slider_container .revolution_slider_description .right_rev_description .ticks_rev_wrapper {
  margin-top: 28px;
}
/* line 101, ../sass/_template_specific.scss */
body.de #slider_container .revolution_slider_description .right_rev_description .ticks_rev_wrapper .ticks, body.nl #slider_container .revolution_slider_description .right_rev_description .ticks_rev_wrapper .ticks {
  font-size: 10px;
}

/* line 108, ../sass/_template_specific.scss */
body.nl .ticks.tick_2 {
  margin-top: 7px;
  vertical-align: top;
}

/* line 115, ../sass/_template_specific.scss */
body:not(.es) .rooms_wrapper .room_element .room_buttons_wrapper .room_book {
  width: auto !important;
}

/* line 120, ../sass/_template_specific.scss */
#bannersBottom {
  margin-top: 20px;
  margin-bottom: 20px;
  background: white;
}
/* line 125, ../sass/_template_specific.scss */
#bannersBottom .banner1 {
  height: 175px;
  background-color: rgba(144, 144, 144, 0.73);
  position: relative;
  width: 270px;
}
/* line 131, ../sass/_template_specific.scss */
#bannersBottom .banner1 img {
  width: 270px;
  height: 175px;
}
/* line 136, ../sass/_template_specific.scss */
#bannersBottom .banner1 .banner_description {
  position: absolute;
  bottom: 0;
  background-color: #00B0B9;
  width: 100%;
  height: 35px;
  text-align: center;
}
/* line 144, ../sass/_template_specific.scss */
#bannersBottom .banner1 .banner_description div {
  margin-top: 5px;
  font-weight: 100;
  color: white;
  font-size: 18px;
  font-style: italic;
}
/* line 152, ../sass/_template_specific.scss */
#bannersBottom .banner1 .banner_description span {
  font-weight: 400;
  font-style: italic;
}
/* line 159, ../sass/_template_specific.scss */
#bannersBottom .banner-block-1 {
  margin-left: 0;
}
/* line 163, ../sass/_template_specific.scss */
#bannersBottom .banner-block-4 {
  margin-right: 0;
}

/* line 168, ../sass/_template_specific.scss */
h2, h3 {
  text-transform: uppercase;
}

/* line 172, ../sass/_template_specific.scss */
.best-online {
  text-align: center;
  padding: 5px 0;
  background-color: #00B0B9;
  font-size: 20px;
  color: white;
}

/* line 180, ../sass/_template_specific.scss */
#title_line {
  display: none;
}

/* line 184, ../sass/_template_specific.scss */
.fullWidth {
  width: 100%;
}

/* line 188, ../sass/_template_specific.scss */
.fullHeight {
  height: 100%;
}
/* line 191, ../sass/_template_specific.scss */
.fullHeight #logo img {
  margin-top: 19px;
}

/* line 196, ../sass/_template_specific.scss */
#mainMenuDiv {
  height: 28px;
}

/* line 200, ../sass/_template_specific.scss */
#slider_map_container {
  padding-top: 0;
}

/* line 204, ../sass/_template_specific.scss */
#sliderDiv {
  height: 400px;
}

/* line 208, ../sass/_template_specific.scss */
#bestPriceDiv {
  height: 70px;
}

/* line 212, ../sass/_template_specific.scss */
.info_main {
  float: left;
  background-color: #f7f5f3;
  overflow: hidden;
  margin-left: 0;
  margin-right: 0;
  width: 560px !important;
}
/* line 220, ../sass/_template_specific.scss */
.info_main img {
  min-width: 100%;
  max-width: none;
}
/* line 225, ../sass/_template_specific.scss */
.info_main .titular {
  position: relative;
}
/* line 228, ../sass/_template_specific.scss */
.info_main .titular h3 {
  display: none;
}
/* line 232, ../sass/_template_specific.scss */
.info_main .titular img {
  position: absolute;
  top: 5px;
  right: 5px;
}
/* line 239, ../sass/_template_specific.scss */
.info_main h3 {
  padding: 10px 30px;
  background-color: #5F319C;
  color: #00B0B9;
  font-size: 18px;
  text-transform: uppercase;
  font-weight: 100;
  border-bottom: 2px solid white;
}
/* line 249, ../sass/_template_specific.scss */
.info_main .content_main {
  padding: 30px;
  color: #5a5a5a;
  font-size: 16px;
  line-height: 20px;
  font-weight: 200;
}
/* line 256, ../sass/_template_specific.scss */
.info_main .content_main p:last-child {
  margin-bottom: 2px;
}

/* line 263, ../sass/_template_specific.scss */
.spa_content {
  min-height: 517px;
}

/* line 267, ../sass/_template_specific.scss */
.aditional-info {
  float: left;
  margin-left: 0;
  margin-right: 0;
  width: 560px !important;
}
/* line 273, ../sass/_template_specific.scss */
.aditional-info .titular {
  position: relative;
}
/* line 276, ../sass/_template_specific.scss */
.aditional-info .titular img {
  position: absolute;
  top: 6px;
  right: 5px;
  background-color: red;
  padding: 8px;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
}
/* line 284, ../sass/_template_specific.scss */
.aditional-info .titular img:hover {
  background-color: #007f86;
}
/* line 290, ../sass/_template_specific.scss */
.aditional-info .gallery {
  margin-bottom: 20px;
}
/* line 293, ../sass/_template_specific.scss */
.aditional-info .gallery ul {
  overflow: hidden;
}
/* line 297, ../sass/_template_specific.scss */
.aditional-info .gallery li {
  float: left;
}
/* line 300, ../sass/_template_specific.scss */
.aditional-info .gallery li img {
  height: 122px;
  width: 186px;
  vertical-align: bottom;
  margin-bottom: 2px;
  -webkit-backface-visibility: hidden;
  -webkit-transform: translateZ(0) scale(1, 1);
  transform: translateZ(0);
}
/* line 311, ../sass/_template_specific.scss */
.aditional-info .gallery .pic-1 img {
  width: 185px;
  margin-right: 1px;
}
/* line 316, ../sass/_template_specific.scss */
.aditional-info .gallery .pic-2 img {
  width: 184px;
  margin-left: 1px;
  margin-right: 1px;
}
/* line 322, ../sass/_template_specific.scss */
.aditional-info .gallery .pic-3 img {
  width: 185px;
  margin-left: 1px;
}
/* line 328, ../sass/_template_specific.scss */
.aditional-info .todo {
  height: 180px;
}
/* line 331, ../sass/_template_specific.scss */
.aditional-info .todo li {
  float: left;
}
/* line 334, ../sass/_template_specific.scss */
.aditional-info .todo li img {
  width: 279px;
  height: 140px;
}
/* line 341, ../sass/_template_specific.scss */
.aditional-info .gallery h3, .aditional-info .todo h3 {
  padding: 10px 30px;
  background-color: #5F319C;
  color: #00B0B9;
  font-size: 18px;
  text-transform: uppercase;
  font-weight: 100;
  border-bottom: 2px solid white;
}

/* line 352, ../sass/_template_specific.scss */
.top_right {
  padding-top: 10px;
}

/* line 356, ../sass/_template_specific.scss */
#languageDiv a {
  display: inline-block;
  text-decoration: none;
  color: #4b4b4b;
  border-radius: 50%;
  text-transform: uppercase;
  font-size: 13px;
}

/* line 365, ../sass/_template_specific.scss */
#languageDiv a .selected, #languageDiv a.selected, #languageDiv a:hover {
  color: red;
  font-weight: bolder;
}

/* line 370, ../sass/_template_specific.scss */
#topMenuDiv {
  text-align: right;
  font-size: 12px;
  color: white;
  font-weight: lighter;
  float: right;
}
/* line 377, ../sass/_template_specific.scss */
#topMenuDiv span.separator {
  color: #4b4b4b;
  font-weight: lighter;
  font-size: 14px;
}

/* line 384, ../sass/_template_specific.scss */
b {
  font-weight: bold;
}

/* line 388, ../sass/_template_specific.scss */
#topMenuDiv a {
  text-decoration: none;
  color: #4b4b4b;
  font-size: 14px;
}

/* line 394, ../sass/_template_specific.scss */
#topMenuDiv a:hover {
  color: #007f86;
}

/* line 398, ../sass/_template_specific.scss */
#topRightDiv {
  height: 80px;
  text-align: right;
  padding-top: 3px;
}

/* line 404, ../sass/_template_specific.scss */
#secondaryLogosDiv, #languageAndTelephoneDiv {
  height: 100%;
}

/* line 408, ../sass/_template_specific.scss */
#languageAndWeather {
  margin-left: 85px;
}

/* line 412, ../sass/_template_specific.scss */
#languageDiv {
  float: right;
  width: auto;
  margin: 0 2px;
  margin-right: 24px;
}
/* line 418, ../sass/_template_specific.scss */
#languageDiv span.language_separator {
  font-weight: lighter;
  font-size: 13px;
}

/* line 424, ../sass/_template_specific.scss */
.floatRight {
  float: right;
}

/* line 428, ../sass/_template_specific.scss */
.weather {
  width: 127px !important;
  margin-left: 57px;
}
/* line 432, ../sass/_template_specific.scss */
.weather p {
  float: left;
  margin-bottom: 0;
}
/* line 437, ../sass/_template_specific.scss */
.weather .number img {
  width: 60px;
}
/* line 441, ../sass/_template_specific.scss */
.weather span {
  position: relative;
  top: -17px;
  color: #00B0B9;
  font-size: 13px;
}

/* line 449, ../sass/_template_specific.scss */
.web-oficial {
  margin-top: -13px;
  float: right;
}

/* line 454, ../sass/_template_specific.scss */
#languageText {
  color: #00B0B9;
  text-transform: uppercase;
  font-size: 12px;
  padding-top: 12px;
  float: left;
  margin-right: 5px;
}

/* line 463, ../sass/_template_specific.scss */
#languageSelectors {
  float: left;
  line-height: 18px;
}

/* line 468, ../sass/_template_specific.scss */
#telephoneDiv {
  background: url(/img/onama/phone_header.png) no-repeat left center;
  font-size: 15px;
  color: #4b4b4b;
  font-weight: bold;
  float: left;
  padding-left: 28px;
  margin-left: 14px;
}
/* line 477, ../sass/_template_specific.scss */
#telephoneDiv span {
  font-weight: 200;
}

/* line 482, ../sass/_template_specific.scss */
#secondaryLogosDiv img {
  max-height: 65px;
  max-width: 150px;
  margin-left: 5px;
}

/* line 488, ../sass/_template_specific.scss */
.selectricOpen .selectricItems {
  overflow: auto !important;
}

/*===== Slider container =====*/
/* line 494, ../sass/_template_specific.scss */
body.inner_section .revolution_slider_description {
  display: none !important;
}
/* line 498, ../sass/_template_specific.scss */
body.inner_section #inner_slider_container {
  height: 470px;
  position: relative;
}
/* line 502, ../sass/_template_specific.scss */
body.inner_section #inner_slider_container .exceded_image_slider {
  height: 100%;
  overflow: hidden;
}
/* line 506, ../sass/_template_specific.scss */
body.inner_section #inner_slider_container .exceded_image_slider .pic {
  height: 470px;
  width: 100%;
  overflow: hidden;
  position: relative;
  background: #EFEFEF;
}
/* line 513, ../sass/_template_specific.scss */
body.inner_section #inner_slider_container .exceded_image_slider .pic img {
  width: auto;
}
/* line 518, ../sass/_template_specific.scss */
body.inner_section #inner_slider_container .exceded_image_slider .owl-nav {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 0;
  right: 0;
  height: 0;
}
/* line 524, ../sass/_template_specific.scss */
body.inner_section #inner_slider_container .exceded_image_slider .owl-nav .owl-next, body.inner_section #inner_slider_container .exceded_image_slider .owl-nav .owl-prev {
  display: inline-block;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  padding: 10px;
  font-size: 50px;
  z-index: 50;
  color: white;
}
/* line 533, ../sass/_template_specific.scss */
body.inner_section #inner_slider_container .exceded_image_slider .owl-nav .owl-next {
  right: 0;
}
/* line 537, ../sass/_template_specific.scss */
body.inner_section #inner_slider_container .exceded_image_slider .owl-nav .owl-prev {
  left: 0;
}
/* line 543, ../sass/_template_specific.scss */
body.inner_section #inner_slider_container #full_wrapper_booking {
  position: absolute;
  bottom: 0;
  left: 0;
  top: auto;
  right: 0;
  margin: 0;
  z-index: 35;
  height: 105px;
}

/* line 556, ../sass/_template_specific.scss */
#slider_container {
  overflow: hidden;
  position: relative;
}
/* line 560, ../sass/_template_specific.scss */
#slider_container .overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.25);
}
/* line 569, ../sass/_template_specific.scss */
#slider_container .revolution_slider_description {
  position: absolute;
  z-index: 22;
  bottom: 20%;
  height: 80px;
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
}
/* line 577, ../sass/_template_specific.scss */
#slider_container .revolution_slider_description .revolution_slider_center_content {
  display: block;
  margin: auto;
  color: white;
}
/* line 583, ../sass/_template_specific.scss */
#slider_container .revolution_slider_description .left_rev_description {
  width: 38%;
  float: left;
  font-weight: lighter;
  margin-top: 18px;
}
/* line 589, ../sass/_template_specific.scss */
#slider_container .revolution_slider_description .left_rev_description strong {
  display: block;
  font-weight: bolder;
}
/* line 595, ../sass/_template_specific.scss */
#slider_container .revolution_slider_description .right_rev_description {
  width: 56%;
  float: right;
}
/* line 599, ../sass/_template_specific.scss */
#slider_container .revolution_slider_description .right_rev_description .button-promotion {
  background: #231b22;
  color: white;
  width: 157px;
  height: 40px;
  vertical-align: top;
  text-align: center;
  text-transform: uppercase;
  box-sizing: border-box;
  padding: 10px 0;
  margin-top: 20px;
  margin-right: 0;
}
/* line 613, ../sass/_template_specific.scss */
#slider_container .revolution_slider_description .right_rev_description .button-promotion, #slider_container .revolution_slider_description .right_rev_description .ticks_rev_wrapper {
  display: inline-block;
}
/* line 617, ../sass/_template_specific.scss */
#slider_container .revolution_slider_description .right_rev_description .ticks_rev_wrapper {
  width: 70%;
  float: right;
  margin-top: 20px;
}
/* line 622, ../sass/_template_specific.scss */
#slider_container .revolution_slider_description .right_rev_description .ticks_rev_wrapper .ticks {
  display: inline-block;
  vertical-align: middle;
  font-weight: lighter;
  text-transform: uppercase;
  width: 32.5%;
  box-sizing: border-box;
  font-size: 15px;
  padding-left: 40px;
  background-position-x: 5% !important;
}
/* line 633, ../sass/_template_specific.scss */
#slider_container .revolution_slider_description .right_rev_description .ticks_rev_wrapper .ticks.tick_1 {
  background: url("/img/onama/ticks/eur.png") no-repeat center left;
}
/* line 637, ../sass/_template_specific.scss */
#slider_container .revolution_slider_description .right_rev_description .ticks_rev_wrapper .ticks.tick_2 {
  background: url("/img/onama/ticks/pig.png") no-repeat center left;
}
/* line 641, ../sass/_template_specific.scss */
#slider_container .revolution_slider_description .right_rev_description .ticks_rev_wrapper .ticks.tick_3 {
  background: url("/img/onama/ticks/shield.png") no-repeat center left;
}

/*====== Booking engine ======*/
/* line 651, ../sass/_template_specific.scss */
.date_box.entry_date, .date_box.departure_date {
  background: url(/static_1/images/booking_5/entry_date.png) no-repeat center left;
  padding-left: 34px;
}

/* line 656, ../sass/_template_specific.scss */
.date_box.departure_date {
  background: url(/static_1/images/booking_5/departure_date.png) no-repeat center left;
  margin-top: 6px;
}

/* line 661, ../sass/_template_specific.scss */
.selectricWrapper .selectric .label {
  font-size: 21px;
  line-height: 35px;
}

/* line 666, ../sass/_template_specific.scss */
.departure_date_wrapper, .half_size, .departure_date_wrapper, .half_size, #full_wrapper_booking .room_list_wrapper .room {
  height: 64px;
}

/* line 670, ../sass/_template_specific.scss */
#full_wrapper_booking .room_list_wrapper .adults_selector, #full_wrapper_booking .room_list_wrapper .children_selector {
  height: 63px !important;
  border-right: 1px solid lightgrey;
}

/* line 675, ../sass/_template_specific.scss */
#full_wrapper_booking .room_list_wrapper {
  height: 62px;
  position: absolute;
  top: 85px;
  left: 540px;
}

/* line 682, ../sass/_template_specific.scss */
#full_wrapper_booking .room_list_wrapper .room {
  height: 62px;
}

/* line 686, ../sass/_template_specific.scss */
#full_wrapper_booking .wrapper_booking_button {
  height: 63px;
}

/* line 690, ../sass/_template_specific.scss */
#full_wrapper_booking .wrapper_booking_button .submit_button, .rooms_number_wrapper {
  height: 64px;
}

/* line 694, ../sass/_template_specific.scss */
input.promocode_input {
  background: white url("/static_1/images/booking_5/promocode.png") no-repeat center left;
  padding-left: 35px;
  box-sizing: border-box;
}

/*==== Included blocks =====*/
/* line 701, ../sass/_template_specific.scss */
.included_blocks_wrapper {
  background: #f5f5f5;
  padding: 17px 0;
  height: 80px;
  box-sizing: border-box;
  margin: 5px 0;
}
/* line 708, ../sass/_template_specific.scss */
.included_blocks_wrapper .included_blocks_center {
  text-align: justify;
}
/* line 711, ../sass/_template_specific.scss */
.included_blocks_wrapper .included_blocks_center:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}
/* line 719, ../sass/_template_specific.scss */
.included_blocks_wrapper .included_block_title, .included_blocks_wrapper .included_element_wrapper {
  display: inline-block;
}
/* line 723, ../sass/_template_specific.scss */
.included_blocks_wrapper .included_block_title {
  border: 1px solid #00B0B9;
  color: #00B0B9;
  padding: 10px;
  cursor: pointer;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 734, ../sass/_template_specific.scss */
.included_blocks_wrapper .included_block_title:hover {
  background: #00B0B9;
  color: white;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5;
}
/* line 745, ../sass/_template_specific.scss */
.included_blocks_wrapper img.included_element_image {
  vertical-align: middle;
}
/* line 749, ../sass/_template_specific.scss */
.included_blocks_wrapper span.included_element_title {
  color: #4b4b4b;
  font-size: 13px;
  max-width: 85px;
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}

/*====== Full blocks top ======*/
/* line 760, ../sass/_template_specific.scss */
.full_blocks_top_wrapper {
  width: 100%;
  display: table;
}
/* line 764, ../sass/_template_specific.scss */
.full_blocks_top_wrapper .full_block_element {
  width: calc(20% - 4px);
  float: left;
  overflow: hidden;
  padding: 0 3px;
  box-sizing: border-box;
  position: relative;
  cursor: pointer;
  margin: 0 2.5px;
}
/* line 774, ../sass/_template_specific.scss */
.full_blocks_top_wrapper .full_block_element:first-of-type {
  padding-left: 0;
  margin-left: 0;
}
/* line 779, ../sass/_template_specific.scss */
.full_blocks_top_wrapper .full_block_element:last-of-type {
  padding-right: 0;
  margin-right: 0;
}
/* line 784, ../sass/_template_specific.scss */
.full_blocks_top_wrapper .full_block_element .full_block_title {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  text-align: center;
  width: 100%;
  font-size: 20px;
  color: white;
  font-weight: lighter;
  height: 50px;
  z-index: 2;
}
/* line 797, ../sass/_template_specific.scss */
.full_blocks_top_wrapper .full_block_element .full_block_title strong {
  display: block;
  font-weight: bolder;
}
/* line 803, ../sass/_template_specific.scss */
.full_blocks_top_wrapper .full_block_element .exceded {
  width: 100%;
  height: 195px;
  overflow: hidden;
  position: relative;
}
/* line 809, ../sass/_template_specific.scss */
.full_blocks_top_wrapper .full_block_element .exceded img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  max-width: initial;
}
/* line 814, ../sass/_template_specific.scss */
.full_blocks_top_wrapper .full_block_element .black_overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.4);
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 829, ../sass/_template_specific.scss */
.full_blocks_top_wrapper .full_block_element:hover .black_overlay {
  opacity: 0;
}

/*===== Content by subtitle ======*/
/* line 837, ../sass/_template_specific.scss */
.content_subtitle_wrapper {
  margin: 10px auto;
  display: table;
  width: 970px;
  padding: 5px 0;
}
/* line 843, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_title {
  text-align: center;
  font-size: 24px;
  font-weight: lighter;
  color: #00B0B9;
  margin-top: 16px;
}
/* line 850, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_title strong {
  font-weight: bolder;
}
/* line 855, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_description {
  color: gray;
  font-weight: 300;
  margin-top: 27px;
}
/* line 860, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_description p {
  margin-bottom: 21px;
}
/* line 864, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_description strong {
  font-weight: 600;
}

/*===== Bottom pictures list =====*/
/* line 871, ../sass/_template_specific.scss */
.bottom_pictures_wrapper {
  position: relative;
}
/* line 874, ../sass/_template_specific.scss */
.bottom_pictures_wrapper .bottom_picture_element {
  float: left;
  width: 100%;
  height: 250px;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}
/* line 882, ../sass/_template_specific.scss */
.bottom_pictures_wrapper .bottom_picture_element .picture_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  max-width: none;
  min-height: 250px;
  overflow: hidden;
}
/* line 889, ../sass/_template_specific.scss */
.bottom_pictures_wrapper .bottom_picture_element .picture_image img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  max-width: initial;
  width: auto;
}
/* line 897, ../sass/_template_specific.scss */
.bottom_pictures_wrapper .flex-nav-prev {
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  margin: auto;
  height: 70px;
  -ms-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}
/* line 909, ../sass/_template_specific.scss */
.bottom_pictures_wrapper .flex-nav-next {
  position: absolute;
  right: 20px;
  top: 0;
  bottom: 0;
  margin: auto;
  height: 70px;
}
/* line 919, ../sass/_template_specific.scss */
.bottom_pictures_wrapper .flexslider_elements img {
  min-height: 195px;
  min-width: 100%;
}

/*===== Newsletter bottom =====*/
/* line 927, ../sass/_template_specific.scss */
.center_newsletter_social {
  text-align: center;
}

/* line 931, ../sass/_template_specific.scss */
.newsletter_social_wrapper {
  background: #afafaf;
  margin-bottom: 5px;
  height: 180px;
}
/* line 936, ../sass/_template_specific.scss */
.newsletter_social_wrapper #newsletter {
  display: inline-block;
}
/* line 939, ../sass/_template_specific.scss */
.newsletter_social_wrapper #newsletter #title_newsletter {
  display: none !important;
}
/* line 943, ../sass/_template_specific.scss */
.newsletter_social_wrapper #newsletter input#suscEmail, .newsletter_social_wrapper #newsletter div#newsletterButtonExternalDiv {
  display: inline-block;
}
/* line 947, ../sass/_template_specific.scss */
.newsletter_social_wrapper #newsletter label#suscEmailLabel {
  background: white url("/img/onama/mail.png") no-repeat center;
  width: 47px;
  height: 47px;
  overflow: hidden;
  color: transparent;
  position: absolute;
}
/* line 956, ../sass/_template_specific.scss */
.newsletter_social_wrapper #newsletter input#suscEmail {
  height: 49px;
  width: 250px;
  background: transparent;
  padding-left: 59px;
  border: 2px solid white;
  box-sizing: border-box;
  color: white;
  font-size: 13px;
}
/* line 966, ../sass/_template_specific.scss */
.newsletter_social_wrapper #newsletter input#suscEmail::-webkit-input-placeholder {
  color: white;
}
/* line 970, ../sass/_template_specific.scss */
.newsletter_social_wrapper #newsletter input#suscEmail::-moz-placeholder {
  color: white;
}
/* line 974, ../sass/_template_specific.scss */
.newsletter_social_wrapper #newsletter input#suscEmail:-ms-input-placeholder {
  color: white;
}
/* line 978, ../sass/_template_specific.scss */
.newsletter_social_wrapper #newsletter input#suscEmail:-moz-placeholder {
  color: white;
}
/* line 984, ../sass/_template_specific.scss */
.newsletter_social_wrapper p.newsletter_title {
  width: auto;
  float: left;
  text-align: right;
  color: white;
  font-weight: lighter;
  font-size: 18px;
  margin-right: 20px;
}
/* line 994, ../sass/_template_specific.scss */
.newsletter_social_wrapper button#newsletter-button {
  height: 49px;
  border: 0;
  vertical-align: middle;
  background: white;
  color: #00B0B9;
  text-transform: uppercase;
  font-size: 16px;
  padding: 0 15px;
  font-weight: 500;
  margin-top: -2px;
  cursor: pointer;
}
/* line 1007, ../sass/_template_specific.scss */
.newsletter_social_wrapper button#newsletter-button:hover {
  opacity: 0.8;
}
/* line 1012, ../sass/_template_specific.scss */
.newsletter_social_wrapper .newsletter_wrapper {
  margin-top: 15px;
  display: inline-block;
}
/* line 1017, ../sass/_template_specific.scss */
.newsletter_social_wrapper .newsletter_checkbox {
  font-size: 12px;
  color: white;
  width: 465px;
  padding-left: 115px;
  text-align: left;
}
/* line 1024, ../sass/_template_specific.scss */
.newsletter_social_wrapper .newsletter_checkbox a {
  text-decoration: underline !important;
  color: white;
}
/* line 1030, ../sass/_template_specific.scss */
.newsletter_social_wrapper input#promotions {
  float: left;
}
/* line 1034, ../sass/_template_specific.scss */
.newsletter_social_wrapper .social_wrapper {
  display: inline-block;
  margin-left: 40px;
  vertical-align: middle;
}
/* line 1039, ../sass/_template_specific.scss */
.newsletter_social_wrapper .social_wrapper p.social_title {
  display: inline-block;
  color: #231b22;
  font-weight: 400;
  font-size: 18px;
  text-transform: uppercase;
  vertical-align: middle;
  margin-right: 15px;
}
/* line 1049, ../sass/_template_specific.scss */
.newsletter_social_wrapper .social_wrapper div#social {
  display: inline-block;
  vertical-align: middle;
}
/* line 1053, ../sass/_template_specific.scss */
.newsletter_social_wrapper .social_wrapper div#social a {
  display: inline-block;
  vertical-align: middle;
  margin: 0 4px;
}
/* line 1058, ../sass/_template_specific.scss */
.newsletter_social_wrapper .social_wrapper div#social a i.fa {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid #231b22;
  color: #231b22;
}
/* line 1066, ../sass/_template_specific.scss */
.newsletter_social_wrapper .social_wrapper div#social a i.fa:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 1072, ../sass/_template_specific.scss */
.newsletter_social_wrapper .social_wrapper div#social a:hover i.fa.fa-facebook {
  border-color: #3b5998;
  color: #3b5998;
}
/* line 1077, ../sass/_template_specific.scss */
.newsletter_social_wrapper .social_wrapper div#social a:hover i.fa.fa-twitter {
  border-color: #55acee;
  color: #55acee;
}
/* line 1082, ../sass/_template_specific.scss */
.newsletter_social_wrapper .social_wrapper div#social a:hover i.fa.fa-google-plus {
  border-color: #dd4b39;
  color: #dd4b39;
}
/* line 1087, ../sass/_template_specific.scss */
.newsletter_social_wrapper .social_wrapper div#social a:hover i.fa.fa-youtube {
  border-color: #d62424;
  color: #d62424;
}

/*====== Footer =====*/
/* line 1098, ../sass/_template_specific.scss */
footer {
  display: inline-block;
  width: 100%;
  margin-top: 55px;
  background: #231b22;
  text-align: center;
  padding: 25px 0;
}
/* line 1106, ../sass/_template_specific.scss */
footer a.footer_menu_element, footer a.footer_prominent_element {
  color: white;
  font-weight: lighter;
  font-size: 14px;
}
/* line 1112, ../sass/_template_specific.scss */
footer span.separator {
  color: white;
  font-size: 9px;
  vertical-align: top;
  display: inline-block;
  margin: 6px 2px 0;
}
/* line 1121, ../sass/_template_specific.scss */
footer .footer_logos_wrapper img.logo_footer_image {
  width: 70px;
  display: inline-block;
  vertical-align: middle;
  margin: 0 5px;
}
/* line 1128, ../sass/_template_specific.scss */
footer .footer_logos_wrapper .footer_logo_content {
  display: inline-block;
  vertical-align: middle;
  padding: 20px;
  text-align: left;
  color: white;
  font-weight: lighter;
  font-size: 12px;
}
/* line 1137, ../sass/_template_specific.scss */
footer .footer_logos_wrapper .footer_logo_content a {
  color: white;
}
/* line 1140, ../sass/_template_specific.scss */
footer .footer_logos_wrapper .footer_logo_content a:hover {
  text-decoration: underline;
}
/* line 1145, ../sass/_template_specific.scss */
footer .footer_logos_wrapper .footer_logo_content strong {
  font-weight: bold;
}

/* line 1152, ../sass/_template_specific.scss */
.newsletter_conditions_policy {
  width: 65%;
  margin: auto;
  color: white;
  font-weight: lighter;
  font-size: 12px;
  border: 1px solid white;
  padding: 14px;
  margin-top: 20px;
}

/* line 1163, ../sass/_template_specific.scss */
.second_footer {
  background: #231b22;
  text-align: center;
  padding: 20px 0;
}
/* line 1168, ../sass/_template_specific.scss */
.second_footer div#div-txt-copyright p {
  text-align: center;
  margin-bottom: 5px;
}
/* line 1174, ../sass/_template_specific.scss */
.second_footer .social_buttons_likes div#facebook_like {
  float: left;
  width: 49%;
  text-align: right;
  padding-top: 2px;
}
/* line 1181, ../sass/_template_specific.scss */
.second_footer .social_buttons_likes div#google_plus_one {
  width: 49%;
  float: right;
  text-align: left;
}
/* line 1188, ../sass/_template_specific.scss */
.second_footer .full-copyright {
  color: white;
  font-weight: lighter;
  font-size: 14px;
}
/* line 1193, ../sass/_template_specific.scss */
.second_footer .full-copyright a {
  color: white;
  font-weight: lighter;
  font-size: 14px;
}

/* line 1201, ../sass/_template_specific.scss */
.tripadvisor_wrapper, .iso_image_wrapper {
  display: inline-block;
}
/* line 1204, ../sass/_template_specific.scss */
.tripadvisor_wrapper img.tripadvisor_image, .iso_image_wrapper img.tripadvisor_image {
  width: 50px;
}
/* line 1208, ../sass/_template_specific.scss */
.tripadvisor_wrapper img.iso_image, .iso_image_wrapper img.iso_image {
  width: 70px;
}

/* line 1213, ../sass/_template_specific.scss */
.iso_image_wrapper {
  vertical-align: top;
  padding-top: 9px;
  margin: 0 10px;
}

/* line 1219, ../sass/_template_specific.scss */
.footer_menu_prominent_wrapper {
  margin-bottom: 21px;
}

/* line 1223, ../sass/_template_specific.scss */
.up_arrow_wrapper {
  position: relative;
}
/* line 1226, ../sass/_template_specific.scss */
.up_arrow_wrapper .up_arrow_button {
  position: absolute;
  right: 0;
  top: -58px;
  cursor: pointer;
}
/* line 1232, ../sass/_template_specific.scss */
.up_arrow_wrapper .up_arrow_button i.fa {
  background: #00B0B9;
  border: 4px solid white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  position: relative;
  color: white;
}
/* line 1241, ../sass/_template_specific.scss */
.up_arrow_wrapper .up_arrow_button i.fa:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 30px;
  margin-top: -5px;
}

/*====== Habitaciones ====*/
/* line 1251, ../sass/_template_specific.scss */
.rooms_wrapper {
  margin-bottom: 60px;
  margin-top: 30px;
}
/* line 1255, ../sass/_template_specific.scss */
.rooms_wrapper .room_element {
  height: auto;
  margin-bottom: 20px;
  width: 100%;
  display: table;
}
/* line 1261, ../sass/_template_specific.scss */
.rooms_wrapper .room_element h3.room_title {
  font-size: 24px;
  color: #5F319C;
  margin-bottom: 12px;
}
/* line 1266, ../sass/_template_specific.scss */
.rooms_wrapper .room_element h3.room_title span.capacity {
  font-weight: lighter;
  font-size: 16px;
  text-transform: capitalize;
  vertical-align: top;
  margin-top: 6px;
  color: #231b22;
  display: inline-block;
}
/* line 1275, ../sass/_template_specific.scss */
.rooms_wrapper .room_element h3.room_title span.capacity strong {
  font-weight: 400;
}
/* line 1279, ../sass/_template_specific.scss */
.rooms_wrapper .room_element h3.room_title span.capacity i.fa {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #231b22;
  margin-right: 5px;
  position: relative;
  display: inline-block;
  vertical-align: middle;
  font-size: 12px;
}
/* line 1290, ../sass/_template_specific.scss */
.rooms_wrapper .room_element h3.room_title span.capacity i.fa:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 1297, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_description {
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
  color: gray;
  padding-bottom: 11px;
  border-bottom: 1px solid #CECECE;
  margin-bottom: 33px;
}
/* line 1306, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_description .hide_me {
  display: none;
}
/* line 1311, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .service_elements li {
  display: inline-block;
  padding-right: 20px;
  font-size: 13px;
  color: #00B0B9;
  font-weight: lighter;
}
/* line 1318, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .service_elements li img {
  vertical-align: middle;
  margin-right: 5px;
}
/* line 1324, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .exceded {
  width: 32%;
  min-height: 278px;
  float: left;
  position: relative;
  overflow: hidden;
}
/* line 1331, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .exceded li {
  position: relative;
  overflow: hidden;
}
/* line 1336, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .exceded .slides li {
  height: 278px;
}
/* line 1340, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .exceded img.room_image {
  min-height: 100%;
  max-width: none;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translateY(-50%) translateX(-50%);
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  object-fit: cover;
}
/* line 1353, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .exceded i.fa {
  width: 30px;
  height: 30px;
  border-radius: 5px;
  background: #00B0B9;
  color: white;
  font-size: 20px;
}
/* line 1361, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .exceded i.fa:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 1367, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .exceded:hover i.fa {
  background: #5F319C;
}
/* line 1372, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .exceded img.plus_image, .rooms_wrapper .room_element .exceded i.fa {
  position: absolute;
  left: 20px;
  top: 20px;
  z-index: 1;
}
/* line 1380, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .flex-nav-prev {
  position: absolute !important;
  left: 0;
  top: 0;
  bottom: 0;
  height: 45px;
  margin: auto;
}
/* line 1389, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .flex-nav-next {
  position: absolute !important;
  right: 0;
  top: 0;
  bottom: 0;
  height: 45px;
  margin: auto;
}
/* line 1398, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_description_wrapper {
  background: #F8F8F8;
  float: right;
  width: 68%;
  padding: 25px 40px;
  min-height: 278px;
  box-sizing: border-box;
  position: relative;
}
/* line 1409, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .service_elements .divide_0, .rooms_wrapper .room_element .service_elements .divide_1, .rooms_wrapper .room_element .service_elements .divide_2 {
  width: 32%;
  display: inline-table;
}
/* line 1413, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .service_elements .divide_0 li, .rooms_wrapper .room_element .service_elements .divide_1 li, .rooms_wrapper .room_element .service_elements .divide_2 li {
  font-size: 13px;
  color: #5F319C;
  font-weight: lighter;
}
/* line 1418, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .service_elements .divide_0 li img, .rooms_wrapper .room_element .service_elements .divide_1 li img, .rooms_wrapper .room_element .service_elements .divide_2 li img {
  vertical-align: middle;
  margin-right: 5px;
}
/* line 1426, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_buttons_wrapper {
  position: absolute;
  top: 17px;
  right: 40px;
}
/* line 1431, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_buttons_wrapper img {
  vertical-align: middle;
  width: auto;
  display: none;
  height: 37px;
}
/* line 1438, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_buttons_wrapper .room_book {
  color: white;
  padding: 8px;
  background: #00B0B9;
  width: 117px;
  box-sizing: border-box;
  height: 37px;
  display: inline-block;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  float: right;
}
/* line 1453, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_types {
  position: absolute;
  top: 16px;
  right: 27%;
}
/* line 1458, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_types a {
  text-decoration: none;
}

/* line 1465, ../sass/_template_specific.scss */
.room_type_wrapper, .room_complete_description {
  display: none;
}
/* line 1468, ../sass/_template_specific.scss */
.room_type_wrapper h3, .room_complete_description h3 {
  font-size: 24px;
  color: #5F319C;
  margin-bottom: 12px;
}
/* line 1474, ../sass/_template_specific.scss */
.room_type_wrapper > div, .room_complete_description > div {
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
  color: gray;
}

/* Rooms icons tooltips */
/* line 1483, ../sass/_template_specific.scss */
.tooltip {
  display: inline;
  position: relative;
  cursor: pointer;
}

/* line 1489, ../sass/_template_specific.scss */
.tooltip:hover:after {
  background: #00B0B9;
  border-radius: 5px;
  bottom: 26px;
  color: white;
  content: attr(title);
  left: 20%;
  top: -54px;
  height: 22px;
  padding: 5px 15px;
  position: absolute;
  z-index: 98;
  text-align: center;
  text-transform: uppercase;
}

/*======= Top Carousel Images =====*/
/* line 1506, ../sass/_template_specific.scss */
.top_carousel_wrapper {
  margin-top: 5px;
  margin-bottom: 2px;
  position: relative;
  height: 195px;
  overflow: hidden;
}
/* line 1513, ../sass/_template_specific.scss */
.top_carousel_wrapper .flex-nav-prev {
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  margin: auto;
  height: 70px;
  -ms-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}
/* line 1525, ../sass/_template_specific.scss */
.top_carousel_wrapper .flex-nav-next {
  position: absolute;
  right: 20px;
  top: 0;
  bottom: 0;
  margin: auto;
  height: 70px;
}
/* line 1534, ../sass/_template_specific.scss */
.top_carousel_wrapper .flexslider_elements {
  overflow: hidden;
}
/* line 1537, ../sass/_template_specific.scss */
.top_carousel_wrapper .flexslider_elements img {
  min-height: 195px;
  min-width: 100%;
}
/* line 1543, ../sass/_template_specific.scss */
.top_carousel_wrapper .exceded {
  height: 195px;
  position: relative;
}
/* line 1547, ../sass/_template_specific.scss */
.top_carousel_wrapper .exceded img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  max-width: initial;
}

/*====== Celebration blocks ========*/
/* line 1554, ../sass/_template_specific.scss */
.celebration {
  background: white;
  padding: 0 0 20px;
}
/* line 1558, ../sass/_template_specific.scss */
.celebration .block {
  margin-bottom: 20px;
  width: 366px;
  height: 240px;
  position: relative;
  overflow: hidden;
  margin-left: 0;
  margin-right: 20px;
}
/* line 1567, ../sass/_template_specific.scss */
.celebration .block.double {
  width: 560px;
}
/* line 1571, ../sass/_template_specific.scss */
.celebration .block .block .img-celebration {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  max-width: initial;
  transition: 1s -webkit-filter linear;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
/* line 1579, ../sass/_template_specific.scss */
.celebration .block .overlay {
  width: 100%;
  height: 100%;
  position: absolute;
  top: -100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 2;
  transition: all 0.7s ease;
}
/* line 1589, ../sass/_template_specific.scss */
.celebration .block .block:hover .overlay {
  opacity: 0;
}
/* line 1594, ../sass/_template_specific.scss */
.celebration .block .block .overlay {
  top: 0;
  opacity: 1;
}
/* line 1603, ../sass/_template_specific.scss */
.celebration .block .celebration-banner-content {
  position: absolute;
  top: 125%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 366px;
  text-align: center;
  z-index: 2;
  color: white;
  text-transform: uppercase;
  transition: all 0.7s ease;
  -webkit-transition-delay: 100ms;
  -moz-transition-delay: 100ms;
  transition-delay: 100ms;
}
/* line 1620, ../sass/_template_specific.scss */
.celebration .block .celebration-banner-content hide {
  display: none;
}
/* line 1630, ../sass/_template_specific.scss */
.celebration .block .celebration-banner-content {
  top: 50%;
}
/* line 1635, ../sass/_template_specific.scss */
.celebration .celebration-banner-content h3 {
  font-weight: 300;
  margin-bottom: 10px;
  font-family: 'Open Sans', sans-serif;
  font-size: 21px;
}
/* line 1642, ../sass/_template_specific.scss */
.celebration .celebration-banner-content p {
  font-size: 12px;
  line-height: 18px;
  padding: 0 25px;
  margin-bottom: 10px;
}
/* line 1649, ../sass/_template_specific.scss */
.celebration .celebration-banner-content a {
  display: inline-block;
  width: auto;
  height: auto;
  padding: 0 20px;
  line-height: 37px;
  color: white;
  background: transparent;
  border: 2px solid white;
  font-family: 'Open Sans', sans-serif;
  font-weight: 300;
  font-size: 16px;
  text-decoration: none;
  margin: auto;
}
/* line 1664, ../sass/_template_specific.scss */
.celebration .celebration-banner-content a.button-promotion {
  background: #00B0B9;
  border-color: #00B0B9;
  -webkit-transition: opacity .6s;
  -moz-transition: opacity .6s;
  -ms-transition: opacity .6s;
  -o-transition: opacity .6s;
  transition: opacity .6s;
}
/* line 1673, ../sass/_template_specific.scss */
.celebration .celebration-banner-content a.button-promotion:hover {
  opacity: .8;
}

/* line 1680, ../sass/_template_specific.scss */
.celebration_hidden_popup hide {
  display: block;
}

/* line 1684, ../sass/_template_specific.scss */
.event-popup {
  font-size: 14px;
  line-height: 22px;
}
/* line 1688, ../sass/_template_specific.scss */
.event-popup p, .event-popup div {
  margin-bottom: 20px;
}
/* line 1692, ../sass/_template_specific.scss */
.event-popup h3 {
  color: #00B0B9;
}

/* line 1698, ../sass/_template_specific.scss */
#contact-promo,
#contact-promo2 {
  padding: 30px;
}
/* line 1702, ../sass/_template_specific.scss */
#contact-promo label,
#contact-promo2 label {
  padding-bottom: 3px;
  margin: auto;
  text-align: center;
  text-transform: capitalize;
  display: block;
  margin-bottom: 3px;
  font-weight: 300;
  font-size: 14px;
  color: #4b4b4b;
}
/* line 1714, ../sass/_template_specific.scss */
#contact-promo .contInput,
#contact-promo2 .contInput {
  margin-bottom: 20px;
  text-align: center;
}
/* line 1719, ../sass/_template_specific.scss */
#contact-promo input.bordeInput,
#contact-promo2 input.bordeInput {
  display: block;
  margin: 0 auto;
  margin-bottom: 10px;
  width: 250px;
  height: 15px;
  padding: 5px;
  text-align: center;
  background-color: #e6e6e6;
  color: black;
  border: none;
}
/* line 1732, ../sass/_template_specific.scss */
#contact-promo textarea.bordeInput,
#contact-promo2 textarea.bordeInput {
  display: block;
  margin: 0 auto;
  margin-bottom: 10px;
  width: 250px;
  height: 15px;
  padding: 5px;
  text-align: center;
  background-color: #e6e6e6;
  color: black;
  border: none;
}
/* line 1745, ../sass/_template_specific.scss */
#contact-promo select.bordeInput,
#contact-promo2 select.bordeInput {
  border: 3px solid #dddddd !important;
  height: 30px;
  width: 308px;
  color: #00B0B9;
}
/* line 1752, ../sass/_template_specific.scss */
#contact-promo .error,
#contact-promo2 .error {
  font-size: 12px;
  color: red;
  display: inline;
  margin: -1px 0 23px 163px;
}
/* line 1759, ../sass/_template_specific.scss */
#contact-promo .btn-corporate,
#contact-promo2 .btn-corporate {
  background: #00B0B9;
  padding: 6px 9px;
  display: block;
  width: 140px;
  text-transform: uppercase;
  color: white;
  margin: auto;
  margin-top: 25px;
  cursor: pointer;
  font-size: 18px;
  text-align: center;
}
/* line 1773, ../sass/_template_specific.scss */
#contact-promo #fecha_entrada,
#contact-promo2 #fecha_entrada {
  margin: 0 !important;
}
/* line 1777, ../sass/_template_specific.scss */
#contact-promo a.myFancyPopup,
#contact-promo2 a.myFancyPopup {
  margin-bottom: 10px;
  font-weight: 300;
  font-size: 14px;
  color: #4b4b4b;
  text-decoration: underline;
  display: inline-block;
  vertical-align: top;
}
/* line 1787, ../sass/_template_specific.scss */
#contact-promo input#privacy,
#contact-promo2 input#privacy {
  display: inline-block;
  width: auto !important;
}

/* line 1793, ../sass/_template_specific.scss */
.celebration_hidden_popup {
  padding: 30px;
  color: gray;
  font-weight: 300;
}
/* line 1798, ../sass/_template_specific.scss */
.celebration_hidden_popup h2.celebration_title_popup {
  text-align: left;
  font-size: 24px;
  font-weight: lighter;
  color: #00B0B9;
  margin-top: 0;
}
/* line 1806, ../sass/_template_specific.scss */
.celebration_hidden_popup a {
  border: 1px solid #00B0B9;
  color: #00B0B9;
  padding: 7px;
  display: table;
  margin-top: 15px;
}

/*===== Wedding ====*/
/* line 1816, ../sass/_template_specific.scss */
ul.contact-event {
  color: white;
  font-weight: bold;
}

/* line 1821, ../sass/_template_specific.scss */
.wedding .titular h3 {
  background-color: #00B0B9;
  color: white;
  font-size: 18px;
  text-transform: uppercase;
  font-weight: 100;
  padding: 10px 30px;
  border-bottom: 2px solid white;
}

/* line 1831, ../sass/_template_specific.scss */
.aditional-info .image-weding {
  background: red;
  padding: 30px;
}

/* line 1836, ../sass/_template_specific.scss */
.weding .download-map {
  position: absolute;
  top: 10px;
  right: 10px;
  padding-left: 30px;
}
/* line 1842, ../sass/_template_specific.scss */
.weding .download-map a {
  color: white;
  text-decoration: none;
  padding: 5px 10px 5px 30px;
  font-weight: 100;
  font-size: 13px;
  background: #464445 url("/img/onama/dowload_icon.png") 5px no-repeat;
  border-radius: 4px;
}
/* line 1852, ../sass/_template_specific.scss */
.weding .download-map a:hover {
  background-color: #787878;
}

/* line 1857, ../sass/_template_specific.scss */
.wedding .info_main h3 {
  background-color: #f1ece4;
  color: #8d7d6c;
}

/* line 1862, ../sass/_template_specific.scss */
.wedding #my-bookings-form-fields label {
  text-transform: capitalize;
  display: block;
  margin-bottom: 10px;
  font-weight: 300;
  font-size: 14px;
  color: #4b4b4b;
}
/* line 1871, ../sass/_template_specific.scss */
.wedding #my-bookings-form-fields label.error {
  color: red;
  font-size: 13px;
  position: absolute;
  right: 0;
  top: 20px;
}

/* line 1880, ../sass/_template_specific.scss */
.privacy_wrapper {
  position: relative;
  display: inline-block;
  float: left;
}

/* line 1886, ../sass/_template_specific.scss */
.wedding #contact-button {
  background: #00B0B9;
  padding: 6px 9px;
  display: block;
  width: 140px;
  text-transform: uppercase;
  color: white;
  margin: auto;
  margin-top: 0;
  cursor: pointer;
  font-size: 18px;
  clear: both;
  float: right;
  text-align: center;
  margin-top: -35px;
}
/* line 1902, ../sass/_template_specific.scss */
.wedding #contact-button:hover {
  opacity: 0.8;
}

/* line 1907, ../sass/_template_specific.scss */
.wedding .aditional-info .titular img {
  background-color: #d2baa0;
}
/* line 1910, ../sass/_template_specific.scss */
.wedding .aditional-info .titular img:hover {
  background-color: #e3cbb1;
}

/* line 1915, ../sass/_template_specific.scss */
.form_booking.wedding {
  width: 100%;
  margin: auto;
  margin-bottom: 40px;
}
/* line 1920, ../sass/_template_specific.scss */
.form_booking.wedding .form {
  background: #efefef;
  padding: 30px;
}
/* line 1924, ../sass/_template_specific.scss */
.form_booking.wedding .form li {
  width: 501px;
  display: inline-block;
  margin-bottom: 10px;
  position: relative;
}
/* line 1930, ../sass/_template_specific.scss */
.form_booking.wedding .form li:nth-child(even) {
  float: right;
}
/* line 1934, ../sass/_template_specific.scss */
.form_booking.wedding .form li:nth-child(odd) {
  float: left;
}
/* line 1938, ../sass/_template_specific.scss */
.form_booking.wedding .form li:last-of-type {
  width: 100%;
}
/* line 1944, ../sass/_template_specific.scss */
.form_booking.wedding #my-bookings-form-fields {
  text-align: left;
  display: table;
}
/* line 1948, ../sass/_template_specific.scss */
.form_booking.wedding #my-bookings-form-fields input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0 !important;
  height: 30px;
  background-color: white;
  color: black;
  text-align: left;
  box-sizing: border-box;
}
/* line 1960, ../sass/_template_specific.scss */
.form_booking.wedding #my-bookings-form-fields input#privacy {
  display: inline-block;
  width: auto;
  float: left;
}
/* line 1965, ../sass/_template_specific.scss */
.form_booking.wedding #my-bookings-form-fields input#privacy + label {
  font-weight: 300;
  font-size: 14px;
  color: #4b4b4b;
  display: inline-block;
  float: left;
  margin-top: 5px;
  margin-left: 10px;
  text-decoration: underline;
}
/* line 1975, ../sass/_template_specific.scss */
.form_booking.wedding #my-bookings-form-fields input#privacy + label.error {
  color: red;
}
/* line 1978, ../sass/_template_specific.scss */
.form_booking.wedding #my-bookings-form-fields input#privacy + label.error + label {
  font-weight: 300;
  font-size: 14px;
  color: #4b4b4b;
  display: inline-block;
  float: left;
  margin-top: 5px;
  margin-left: 10px;
  text-decoration: underline;
}
/* line 1990, ../sass/_template_specific.scss */
.form_booking.wedding #my-bookings-form-fields input#privacy + label a {
  color: #4b4b4b;
}
/* line 1996, ../sass/_template_specific.scss */
.form_booking.wedding #my-bookings-form-fields textarea#comments {
  width: 100%;
  display: block;
  margin: 0 auto;
  margin-bottom: 10px;
  height: 90px;
  padding: 5px;
  background-color: white;
  color: black;
  text-align: left;
  border: none;
  box-sizing: border-box;
}

/*==== My booking ======*/
/* line 2013, ../sass/_template_specific.scss */
.booking-content {
  text-align: center;
  margin-bottom: 20px;
}

/* line 2018, ../sass/_template_specific.scss */
#my-bookings-form-fields {
  text-align: center;
}
/* line 2021, ../sass/_template_specific.scss */
#my-bookings-form-fields label {
  display: block;
  color: #00B0B9;
  text-transform: uppercase;
}
/* line 2027, ../sass/_template_specific.scss */
#my-bookings-form-fields input {
  display: block;
  margin: 0 auto;
  margin-bottom: 10px;
  width: 250px;
  height: 15px;
  padding: 5px;
  text-align: center;
  background-color: #e6e6e6;
  color: black;
  border: none;
}
/* line 2040, ../sass/_template_specific.scss */
#my-bookings-form-fields #localizadorInput + ul li {
  display: inline-block;
}
/* line 2043, ../sass/_template_specific.scss */
#my-bookings-form-fields #localizadorInput + ul li button {
  width: 125px;
  padding: 10px 0;
  border: none;
  font-size: 14px;
  cursor: pointer;
  background: gray;
  color: white;
}
/* line 2052, ../sass/_template_specific.scss */
#my-bookings-form-fields #localizadorInput + ul li button.cancelButton {
  background: #231b22;
}
/* line 2056, ../sass/_template_specific.scss */
#my-bookings-form-fields #localizadorInput + ul li button.modify-reservation {
  background: #00B0B9;
}
/* line 2062, ../sass/_template_specific.scss */
#my-bookings-form-fields #my-bookings-form-search-button {
  margin-top: 20px;
  width: 260px;
  color: white;
  background-color: #00B0B9;
  padding: 10px 0;
  border: none;
  font-size: 14px;
  cursor: pointer;
}
/* line 2072, ../sass/_template_specific.scss */
#my-bookings-form-fields #my-bookings-form-search-button:hover {
  background-color: #5F319C;
}

/* line 2078, ../sass/_template_specific.scss */
.modify_reservation_widget {
  margin: auto !important;
  margin-bottom: 50px !important;
}

/* line 2083, ../sass/_template_specific.scss */
#cancelButton {
  width: 260px;
  color: white;
  background-color: #00B0B9;
  padding: 10px 0;
  border: none;
  font-size: 14px;
  cursor: pointer;
  margin-left: 150px;
  margin-top: 20px;
  display: none;
}
/* line 2095, ../sass/_template_specific.scss */
#cancelButton:hover {
  background-color: #5F319C;
}

/* line 2100, ../sass/_template_specific.scss */
#reservation {
  background-color: whitesmoke;
}
/* line 2103, ../sass/_template_specific.scss */
#reservation .fResumenReserva {
  border: none !important;
  width: 518px !important;
}
/* line 2107, ../sass/_template_specific.scss */
#reservation .fResumenReserva .txtCosteTotal {
  color: #00B0B9 !important;
}
/* line 2112, ../sass/_template_specific.scss */
#reservation #my-bookings-booking-inf {
  width: 560px !important;
}

/*======== Ofertas =======*/
/* line 2118, ../sass/_template_specific.scss */
.offers_wrapper {
  background: white;
  margin-top: 30px;
}
/* line 2123, ../sass/_template_specific.scss */
.offers_wrapper .cycle_element .cycle_text_wrapper {
  background: #F5F6F8;
}
/* line 2127, ../sass/_template_specific.scss */
.offers_wrapper .cycle_element .cycle_description hide {
  display: none;
}

/* line 2133, ../sass/_template_specific.scss */
.cycle_banners_wrapper {
  padding: 0 0 20px;
}
/* line 2136, ../sass/_template_specific.scss */
.cycle_banners_wrapper .slides > li {
  display: table;
  width: 100%;
  position: relative;
}
/* line 2142, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element {
  display: table;
  position: relative;
  width: 100%;
  margin-bottom: 20px;
}
/* line 2148, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .exceded {
  width: 50%;
  float: left;
  height: 275px;
  overflow: hidden;
  position: relative;
}
/* line 2155, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .exceded img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  max-width: initial;
}
/* line 2160, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper {
  width: 50%;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  background: #F8F8F8;
}
/* line 2168, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_title {
  font-family: 'Open Sans', sans-serif;
  font-size: 15px;
  color: #00B0B9;
  margin-bottom: 35px;
  font-weight: bolder;
  position: relative;
}
/* line 2176, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_title .plus_image {
  position: absolute;
  right: 0;
  top: -10px;
}
/* line 2182, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_title strong {
  font-weight: bolder;
}
/* line 2187, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description {
  font-size: 15px;
  color: #231b22;
  line-height: 28px;
}
/* line 2192, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description strong {
  font-weight: bolder;
}
/* line 2196, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description a {
  display: inline-table;
  text-decoration: none;
  color: white;
  font-size: 15px;
  background: #5F319C;
  padding: 4px 21px;
  margin-top: 26px;
  border-top: 1px solid;
  font-weight: 300;
  border-bottom: 1px solid;
  text-transform: uppercase;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 2210, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description a.button-promotion {
  background: #00B0B9;
  font-weight: 500;
}
/* line 2214, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description a.button-promotion:hover {
  background: #231b22;
}
/* line 2219, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description a:hover {
  background: #231b22;
}
/* line 2225, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .center_div {
  position: absolute;
  top: 25px;
  bottom: 0;
  margin: auto;
  display: table;
  text-align: left;
  padding: 0 75px;
}
/* line 2236, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle, .cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle-offers {
  position: absolute;
  bottom: 80px;
  right: 25%;
  width: 25%;
  text-align: left;
  z-index: 2;
  padding: 0 75px;
  padding-right: 0;
  box-sizing: border-box;
}
/* line 2247, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle li, .cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle-offers li {
  display: inline-table;
  margin: 0 3px;
}
/* line 2252, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle span.bottom_lane, .cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle-offers span.bottom_lane {
  height: 4px;
  width: 60px;
  background: #00B0B9;
  display: block;
  opacity: 0.6;
  cursor: pointer;
}
/* line 2260, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle span.bottom_lane.flex-active, .cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle-offers span.bottom_lane.flex-active {
  opacity: 1;
}
/* line 2267, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element.align_right .exceded {
  float: right;
}
/* line 2271, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element.align_right .cycle_text_wrapper {
  right: auto;
  left: 0;
}
/* line 2276, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element.align_right ol.flex-controlador-cycle, .cycle_banners_wrapper .cycle_element.align_right .flex-controlador-cycle-offers {
  right: auto;
  left: 0;
}
/* line 2283, ../sass/_template_specific.scss */
.cycle_banners_wrapper a.link_cycle {
  position: absolute;
  bottom: 66px;
  right: 53px;
  color: #ADADAD;
  font-style: italic;
  text-decoration: none;
  font-size: 18px;
  letter-spacing: 1px;
  padding: 9px 0;
  border-top: 1px solid #DAD3D2;
  border-bottom: 1px solid #DAD3D2;
}

/* line 2298, ../sass/_template_specific.scss */
.offer_element_popup {
  font-size: 15px;
  color: #757881;
  line-height: 28px;
  padding: 40px;
}
/* line 2304, ../sass/_template_specific.scss */
.offer_element_popup .offer_popup_title {
  font-family: 'Open Sans', sans-serif;
  font-size: 15px;
  color: #00B0B9;
  margin-bottom: 35px;
  font-weight: bolder;
  position: relative;
}
/* line 2313, ../sass/_template_specific.scss */
.offer_element_popup hide {
  display: block;
}

/*========== FAQ =========*/
/* line 2319, ../sass/_template_specific.scss */
.frecuency {
  margin-top: 20px;
}
/* line 2322, ../sass/_template_specific.scss */
.frecuency .accordion h5 {
  color: white;
  font-size: 18px;
  margin-bottom: 20px;
  background: #00B0B9;
  padding: 10px 40px;
}
/* line 2329, ../sass/_template_specific.scss */
.frecuency .accordion h5:hover, .frecuency .accordion h5.open {
  color: white;
  background: #5F319C;
  cursor: pointer;
}
/* line 2337, ../sass/_template_specific.scss */
.frecuency .accordion p {
  margin-bottom: 15px;
  display: none;
  padding: 0 20px;
}
/* line 2343, ../sass/_template_specific.scss */
.frecuency .accordion div {
  margin-bottom: 30px;
  display: none;
}

/*===== Popup Inicial ======*/
/* line 2350, ../sass/_template_specific.scss */
.popup_overlay {
  position: fixed;
  background: black;
  cursor: pointer;
  opacity: 0.8;
  top: 0px;
  width: 100%;
  height: 100%;
  z-index: 50000;
}

/* line 2361, ../sass/_template_specific.scss */
.popup_container {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  position: fixed;
  z-index: 50000;
  background: white;
}
/* line 2367, ../sass/_template_specific.scss */
.popup_container .popup_inicial {
  position: relative;
  padding: 10px 10px 6px;
  width: 512px;
}
/* line 2372, ../sass/_template_specific.scss */
.popup_container .popup_inicial .close_automatic_floating {
  position: absolute;
  cursor: pointer;
  left: 3%;
  color: white;
}

/* line 2382, ../sass/_template_specific.scss */
.popup_img img {
  width: 100%;
}

/* line 2387, ../sass/_template_specific.scss */
.popup_text {
  position: absolute;
  width: 90%;
  box-sizing: border-box;
  text-align: center;
  margin: auto;
  left: 0;
  right: 0;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 2404, ../sass/_template_specific.scss */
.popup_title {
  font-size: 30px;
  padding: 10px;
  color: white !important;
}

/* line 2410, ../sass/_template_specific.scss */
.popup_description {
  font-size: 16px;
  line-height: 27px;
  color: white !important;
  width: 79%;
  box-sizing: border-box;
  margin: auto;
}

/* line 2420, ../sass/_template_specific.scss */
.popup_form {
  text-align: center;
}

/* line 2424, ../sass/_template_specific.scss */
.popup_inicial form {
  padding: 20px;
  padding-bottom: 25px;
  font-size: 18px;
  display: inline-block;
}

/* line 2431, ../sass/_template_specific.scss */
.popup_inicial form input {
  width: 250px;
  height: 30px;
  display: inline-block;
  vertical-align: middle;
}

/* line 2438, ../sass/_template_specific.scss */
.popup_inicial button {
  background: #63BE7B;
  width: 150px;
  height: 34px;
  font-size: 18px;
  border: 1px solid #CECCCF;
  color: #FFFFFF;
  margin-left: -6px;
  display: inline-block !important;
  vertical-align: middle;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left-color: white;
  text-transform: uppercase;
}
/* line 2453, ../sass/_template_specific.scss */
.popup_inicial button:hover {
  background: #87cd99;
}

/* line 2458, ../sass/_template_specific.scss */
.popup_message {
  color: black;
  margin-top: 20px;
}

/*======== Image Gallery ========*/
/* line 2464, ../sass/_template_specific.scss */
.gallery-image {
  background: white;
  padding: 0 0 35px;
  margin-top: 30px;
}

/* line 2470, ../sass/_template_specific.scss */
.filter-gallery {
  background: #00B0B9;
  height: 75px;
  line-height: 75px;
  color: white;
  text-transform: uppercase;
  font-size: 24px;
  font-weight: 200;
  margin-bottom: 50px;
  cursor: pointer;
  position: relative;
}
/* line 2482, ../sass/_template_specific.scss */
.filter-gallery .element_hide {
  display: none;
}
/* line 2486, ../sass/_template_specific.scss */
.filter-gallery h3 {
  padding-left: 30px;
}
/* line 2490, ../sass/_template_specific.scss */
.filter-gallery span {
  display: inline-block;
  position: absolute;
  height: 75px;
  width: 75px;
  background: #5F319C url(/img/holi2/arrow-newsletter.png) no-repeat center center;
  right: 0px;
  top: 0px;
  border-left: 2px solid white;
}
/* line 2502, ../sass/_template_specific.scss */
.filter-gallery ul {
  background: #62f7ff;
  font-size: 18px;
  line-height: 1;
  display: none;
}
/* line 2511, ../sass/_template_specific.scss */
.filter-gallery li {
  padding: 10px 30px;
  cursor: pointer;
  color: #00B0B9;
}
/* line 2517, ../sass/_template_specific.scss */
.filter-gallery li:hover {
  background: #3af5ff;
}

/* line 2522, ../sass/_template_specific.scss */
.big-img {
  text-align: center;
  max-height: 760px;
  overflow: hidden;
  -webkit-transition: all 2s;
  -moz-transition: all 2s;
  -ms-transition: all 2s;
  -o-transition: all 2s;
  transition: all 2s;
  position: relative;
}
/* line 2533, ../sass/_template_specific.scss */
.big-img .gallery_image_title {
  position: absolute;
  top: 40px;
  left: 40px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px 40px;
  text-transform: uppercase;
}
/* line 2543, ../sass/_template_specific.scss */
.big-img img.main_image {
  width: 100%;
}
/* line 2547, ../sass/_template_specific.scss */
.big-img img.gallery_previous_image, .big-img img.gallery_next_image {
  position: absolute;
  height: 70px;
  top: 0;
  bottom: 0;
  right: 30px;
  margin: auto;
  cursor: pointer;
}
/* line 2556, ../sass/_template_specific.scss */
.big-img img.gallery_previous_image:hover, .big-img img.gallery_next_image:hover {
  opacity: 0.8;
}
/* line 2561, ../sass/_template_specific.scss */
.big-img img.gallery_previous_image {
  -ms-transform: rotate(180deg);
  /* IE 9 */
  -webkit-transform: rotate(180deg);
  /* Chrome, Safari, Opera */
  transform: rotate(180deg);
  right: auto;
  left: 30px;
}

/* line 2570, ../sass/_template_specific.scss */
.image-grid {
  margin-top: 20px;
}
/* line 2574, ../sass/_template_specific.scss */
.image-grid ul {
  overflow: hidden;
  text-align: center;
}
/* line 2579, ../sass/_template_specific.scss */
.image-grid li {
  display: inline-block;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  width: 50px;
  height: 50px;
  overflow: hidden;
  border: 1px solid white;
  position: relative;
}
/* line 2591, ../sass/_template_specific.scss */
.image-grid li img {
  position: absolute;
  top: 0;
  left: -50%;
  bottom: 0;
  right: -50%;
  margin: 0 auto;
  min-width: 120%;
  min-height: 50px;
  height: auto;
  vertical-align: bottom;
  cursor: pointer;
}
/* line 2606, ../sass/_template_specific.scss */
.image-grid li:hover {
  border: 2px solid #5F319C;
}

/* line 2611, ../sass/_template_specific.scss */
.gallery-wrapper .big-img {
  width: 100%;
  height: 650px;
  overflow: hidden;
}

/*======= Location and Contact =========*/
/* line 2618, ../sass/_template_specific.scss */
.location_block_separator {
  display: block;
  width: 100%;
  margin: 60px 0;
}

/* line 2625, ../sass/_template_specific.scss */
.page-localizacion #wrapper_content, .page-localizacion .how-to {
  background: rgba(252, 241, 235, 0.86);
  padding: 20px;
  margin-top: 200px;
  width: 1100px;
}
/* line 2632, ../sass/_template_specific.scss */
.page-localizacion .container12 .column6 {
  width: 530px;
}

/* line 2638, ../sass/_template_specific.scss */
.iframe-google-maps-wrapper iframe {
  width: 100%;
}

/* line 2643, ../sass/_template_specific.scss */
.location-info-and-form-wrapper {
  display: table;
  padding-top: 20px;
  margin-bottom: 30px;
  padding-bottom: 12px;
}

/* line 2650, ../sass/_template_specific.scss */
.location-info-and-form-wrapper h1 {
  font-family: 'Open Sans', sans-serif;
  font-size: 33px;
  color: #00B0B9;
  font-weight: 100;
  margin-bottom: 35px;
  position: relative;
  padding: 0 20px;
}

/* line 2660, ../sass/_template_specific.scss */
.location-info strong {
  font-weight: bold;
}

/* line 2664, ../sass/_template_specific.scss */
#location-description-intro {
  margin-bottom: 30px;
}

/* line 2668, ../sass/_template_specific.scss */
.iframe-google-maps-wrapper {
  margin-top: 30px;
  width: 100%;
  margin-bottom: 40px;
}

/* line 2674, ../sass/_template_specific.scss */
li.how-to-go {
  cursor: pointer;
  color: #00B0B9;
  background: url("/img/amera/icons_maps/walk.png") left center no-repeat;
  padding-left: 30px;
  margin-left: -10px;
}
/* line 2681, ../sass/_template_specific.scss */
li.how-to-go .car {
  background: url("/img/amera/icons_maps/car.png") left center no-repeat;
}

/* line 2686, ../sass/_template_specific.scss */
.form-contact #title {
  display: none !important;
}

/* line 2690, ../sass/_template_specific.scss */
.form-contact #google-plus {
  display: none !important;
}

/* line 2694, ../sass/_template_specific.scss */
.form-contact .fb-like {
  display: none !important;
}

/* line 2702, ../sass/_template_specific.scss */
.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

/* line 2707, ../sass/_template_specific.scss */
.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  font-weight: 300;
  font-size: 14px;
  color: #4b4b4b;
}
/* line 2714, ../sass/_template_specific.scss */
.form-contact #contact .contInput label:first-of-type {
  margin-top: 0;
}

/* line 2719, ../sass/_template_specific.scss */
#contactContent .info {
  margin-top: 5px !important;
  padding-left: 32px !important;
  padding-top: 25px;
  background: #efefef;
  box-sizing: border-box;
  width: 100%;
  display: block;
  padding-bottom: 55px;
}

/* line 2730, ../sass/_template_specific.scss */
.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px !important;
  height: 30px;
  background-color: white;
  color: black;
}

/* line 2740, ../sass/_template_specific.scss */
.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  border: 0;
  background-color: white;
  color: black;
  width: 1070px;
  margin-right: 0;
}

/* line 2750, ../sass/_template_specific.scss */
.form-contact #contact-button-wrapper {
  padding-right: 0 !important;
  margin-right: 0;
}

/* line 2755, ../sass/_template_specific.scss */
.form-contact #contact-button {
  border-radius: 0 !important;
  height: 30px !important;
  width: 130px !important;
  background: #00B0B9 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0 !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0 !important;
  line-height: 32px;
}

/* line 2770, ../sass/_template_specific.scss */
.form-contact #contact-button:hover {
  background-color: #5F319C !important;
}

/* line 2774, ../sass/_template_specific.scss */
.location-info {
  font-weight: 300;
  box-sizing: border-box;
  font-size: 14px;
  color: #4b4b4b;
  line-height: 25px;
  padding: 50px 0;
}

/* line 2787, ../sass/_template_specific.scss */
.how-to {
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;
}
/* line 2793, ../sass/_template_specific.scss */
.how-to h3 {
  font-size: 30px;
  margin-bottom: 20px;
  color: #00B0B9;
}

/* line 2800, ../sass/_template_specific.scss */
ul.location_destiny {
  margin-top: 40px;
}
/* line 2803, ../sass/_template_specific.scss */
ul.location_destiny li {
  padding: 10px 0 10px 35px;
  background: url("/img/onama/destiny.png") left center no-repeat;
  cursor: pointer;
}
/* line 2808, ../sass/_template_specific.scss */
ul.location_destiny li a {
  text-decoration: none;
  color: #00B0B9;
}
/* line 2813, ../sass/_template_specific.scss */
ul.location_destiny li a.active {
  color: white;
}
/* line 2818, ../sass/_template_specific.scss */
ul.location_destiny .car {
  background: url("/img/onama/car.png") left center no-repeat;
}
/* line 2822, ../sass/_template_specific.scss */
ul.location_destiny .walk {
  background: url("/img/onama/walk.png") left center no-repeat;
}

/* line 2828, ../sass/_template_specific.scss */
.form-contact.location_section h1 {
  width: 100%;
  padding: 10px 30px;
  background-color: #e2ebf4;
  color: #00B0B9;
  font-size: 18px;
  text-transform: uppercase;
  font-weight: 100;
  border-bottom: 2px solid white;
  box-sizing: border-box;
}

/* line 2841, ../sass/_template_specific.scss */
.location_description_wrapper {
  padding: 0 35px;
}

/* line 2845, ../sass/_template_specific.scss */
#contactContent .info {
  padding-right: 32px;
  margin-bottom: 42px;
}
/* line 2849, ../sass/_template_specific.scss */
#contactContent .info a.myFancyPopup {
  margin-bottom: 10px;
  font-weight: 300;
  font-size: 14px;
  color: #4b4b4b;
  text-decoration: underline;
}
/* line 2857, ../sass/_template_specific.scss */
#contactContent .info .contInput {
  margin-bottom: 10px;
  display: inline-block;
  width: auto;
  clear: both;
}
/* line 2863, ../sass/_template_specific.scss */
#contactContent .info .contInput:nth-of-type(2), #contactContent .info .contInput:nth-of-type(4) {
  float: right;
}
/* line 2868, ../sass/_template_specific.scss */
#contactContent .info input#privacy {
  width: auto !important;
  margin-right: 10px;
  position: relative;
}
/* line 2873, ../sass/_template_specific.scss */
#contactContent .info input#privacy + .error {
  display: inline-block;
  font-weight: 300;
  font-size: 14px;
  margin-right: 10px;
}

/*==== Automatic content =====*/
/* line 2883, ../sass/_template_specific.scss */
.automatic_content_wrapper {
  color: gray;
  font-weight: 300;
  margin-top: 20px;
}
/* line 2888, ../sass/_template_specific.scss */
.automatic_content_wrapper > div:first-of-type {
  display: none;
}
/* line 2892, ../sass/_template_specific.scss */
.automatic_content_wrapper #reservation {
  background: none;
  display: table;
  margin: auto;
}
/* line 2897, ../sass/_template_specific.scss */
.automatic_content_wrapper #reservation .fResumenReserva {
  background: #eaeaea;
}
/* line 2902, ../sass/_template_specific.scss */
.automatic_content_wrapper #cancelButton {
  margin: 20px auto;
}

/* line 2907, ../sass/_template_specific.scss */
button#cancellation-confirmation-button {
  width: auto;
  color: white;
  background-color: #00B0B9;
  padding: 8px 20px;
  border: none;
  font-size: 13px;
  cursor: pointer;
  margin-top: 11px;
}

/* line 2918, ../sass/_template_specific.scss */
div#reservation-cancellation-popup {
  padding: 20px;
  color: gray;
  font-weight: 300;
}
/* line 2923, ../sass/_template_specific.scss */
div#reservation-cancellation-popup textarea {
  width: 430px;
  margin-top: 6px;
}

/* line 2930, ../sass/_template_specific.scss */
.fancy_landing .fancybox-inner {
  left: 424px !important;
}
/* line 2934, ../sass/_template_specific.scss */
.fancy_landing #data {
  width: auto !important;
  height: auto !important;
  zoom: .6;
}

/* line 2941, ../sass/_template_specific.scss */
.datepicker_landing {
  top: 43% !important;
  left: 54% !important;
}

/* line 2947, ../sass/_template_specific.scss */
.automatic_content_wrapper h3.section-title {
  text-align: center;
  font-size: 24px;
  font-weight: lighter;
  color: #00B0B9;
  margin-top: 16px;
}
/* line 2955, ../sass/_template_specific.scss */
.automatic_content_wrapper .newsletter_landing_content {
  color: gray;
  font-weight: 300;
  margin-top: 27px;
  display: block !important;
  text-align: center;
}
/* line 2963, ../sass/_template_specific.scss */
.automatic_content_wrapper .newsletter_additional_wrapper {
  margin: 20px 0;
}

/* line 2970, ../sass/_template_specific.scss */
.info #promotions {
  float: left !important;
  width: 15px !important;
  margin-top: 5px !important;
}
/* line 2977, ../sass/_template_specific.scss */
.info label[for=promotions] {
  font-size: 14px;
  color: #4b4b4b;
  margin-left: 4px;
}

/* line 2984, ../sass/_template_specific.scss */
.bottom_pictures_wrapper .owl-nav {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 0;
  right: 0;
  height: 0;
}
/* line 2990, ../sass/_template_specific.scss */
.bottom_pictures_wrapper .owl-nav .owl-next, .bottom_pictures_wrapper .owl-nav .owl-prev {
  display: inline-block;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  padding: 10px;
  font-size: 50px;
  z-index: 50;
  color: white;
}
/* line 2999, ../sass/_template_specific.scss */
.bottom_pictures_wrapper .owl-nav .owl-next {
  right: 0;
}
/* line 3003, ../sass/_template_specific.scss */
.bottom_pictures_wrapper .owl-nav .owl-prev {
  left: 0;
}

/* line 3008, ../sass/_template_specific.scss */
.table_book {
  margin: 10px 0 30px;
  border-radius: 20px;
  border: 2px solid #00B0B9;
  display: table;
  line-height: 28px;
  color: #00B0B9;
  text-decoration: none;
  text-transform: uppercase;
  padding: 0 20px;
}
/* line 3019, ../sass/_template_specific.scss */
.table_book span {
  display: inline-block;
  padding: 5px 15px;
}

/* line 3025, ../sass/_template_specific.scss */
.book_table_wrapper {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: fixed;
  z-index: 1005;
  background: rgba(0, 176, 185, 0.9);
}
/* line 3032, ../sass/_template_specific.scss */
.book_table_wrapper .close_map, .book_table_wrapper .close_book_table {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 40px;
  color: white;
  cursor: pointer;
}
/* line 3041, ../sass/_template_specific.scss */
.book_table_wrapper .book_table {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  background: white;
  width: 820px;
  max-width: 80vw;
  max-height: 80vh;
  border-radius: 10px;
  padding: 30px;
}
/* line 3049, ../sass/_template_specific.scss */
.book_table_wrapper .book_table h3 {
  font-size: 22px;
  padding: 10px 20px;
}
/* line 3054, ../sass/_template_specific.scss */
.book_table_wrapper .book_table iframe {
  width: 100%;
  vertical-align: middle;
  border-radius: 10px;
}
/* line 3062, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper {
  overflow: hidden;
}
/* line 3065, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper .title {
  text-align: center;
  margin-bottom: 20px;
}
/* line 3070, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper .subtitle {
  text-align: center;
  padding-bottom: 40px;
}
/* line 3075, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper .desc {
  padding: 0 200px;
  margin-bottom: 50px;
  text-align: center;
}
/* line 3081, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form {
  padding: 0 calc((100% - 1040px) / 2);
  display: table;
  margin: auto;
}
/* line 3086, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact .info, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .info {
  display: table;
  position: relative;
}
/* line 3091, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact .contInput, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput {
  display: inline-block;
  float: left;
  padding: 10px 0 10px 20px;
  width: calc((100% / 3) - 5px);
  position: relative;
}
/* line 3098, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact .contInput.area, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput.area {
  width: 100%;
}
/* line 3102, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact .contInput label, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput label {
  font-size: 12px;
  color: #5F319C;
}
/* line 3106, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact .contInput label.error, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput label.error {
  color: red;
}
/* line 3110, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact .contInput label.hotel_selector:after, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput label.hotel_selector:after {
  content: '\f078';
  font-family: "font awesome 5";
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  font-weight: 300;
  display: inline-block;
  font-size: 18px;
  margin-left: 70px;
  vertical-align: middle;
  color: rgba(45, 45, 45, 0.5);
  position: relative;
  top: 20px;
  pointer-events: none;
}
/* line 3132, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact .contInput input[type="date"], .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput input[type="date"] {
  padding-bottom: 13px !important;
}
/* line 3136, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact .contInput input:not([type="checkbox"]), .book_table_wrapper .book_table .contact_form_wrapper #contact .contInput select, .book_table_wrapper .book_table .contact_form_wrapper #contact .contInput textarea, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput input:not([type="checkbox"]), .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput select, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: white;
  padding: 0 0 15px;
  font-size: 14px;
  border-radius: 0;
  border-width: 0;
  width: 100%;
  margin-bottom: 20px;
  border-bottom: 2px solid #5F319C;
  outline: none;
}
/* line 3151, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact .contInput textarea, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput textarea {
  height: 150px;
}
/* line 3156, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact .contInput input.error, .book_table_wrapper .book_table .contact_form_wrapper #contact .contInput select.error, .book_table_wrapper .book_table .contact_form_wrapper #contact .contInput textarea.error, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput input.error, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput select.error, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput textarea.error {
  outline: 1px solid red;
}
/* line 3161, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact .contInput #accept-term, .book_table_wrapper .book_table .contact_form_wrapper #contact .contInput#privacity, .book_table_wrapper .book_table .contact_form_wrapper #contact .contInput #promotions, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput #accept-term, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput#privacity, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput #promotions {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: transparent;
  border: 1px solid #5F319C;
  border-radius: 0;
  width: 12px;
  height: 12px;
  vertical-align: middle;
  position: relative;
  outline: none;
}
/* line 3175, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact .contInput #accept-term:checked:before, .book_table_wrapper .book_table .contact_form_wrapper #contact .contInput#privacity:checked:before, .book_table_wrapper .book_table .contact_form_wrapper #contact .contInput #promotions:checked:before, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput #accept-term:checked:before, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput#privacity:checked:before, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .contInput #promotions:checked:before {
  content: '';
  width: 6px;
  height: 6px;
  background: #00B0B9;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 3186, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact .policy-terms, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form .policy-terms {
  display: block;
  clear: left;
  width: auto;
  color: black;
  font-size: 12px;
  margin: 0;
}
/* line 3195, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact a.myFancyPopup, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form a.myFancyPopup {
  display: inline-block;
  vertical-align: middle;
  color: black;
}
/* line 3201, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper #contact #contact-button, .book_table_wrapper .book_table .contact_form_wrapper #traveller_form #contact-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  float: right;
  font-size: 18px;
  padding: 5px 25px;
  font-weight: bold;
}
/* line 3213, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper.without_form {
  padding-bottom: 0;
}
/* line 3216, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper.without_form .banner_contact_wrapper {
  margin-bottom: 0;
}
/* line 3219, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper.without_form .banner_contact_wrapper .banner_img, .book_table_wrapper .book_table .contact_form_wrapper.without_form .banner_contact_wrapper .banner_text {
  margin-bottom: 0;
}
/* line 3223, ../sass/_template_specific.scss */
.book_table_wrapper .book_table .contact_form_wrapper.without_form .banner_contact_wrapper .banner_text {
  background: #ECFBFB;
  margin-left: 0;
  padding-left: 55px;
}

/* line 3238, ../sass/_template_specific.scss */
body.fr .rooms_wrapper .room_element h3.room_title span.capacity, body.de .rooms_wrapper .room_element h3.room_title span.capacity {
  display: block;
}

/* line 3250, ../sass/_template_specific.scss */
body.fr #my-bookings-form-fields #localizadorInput + ul li button {
  width: 150px;
}

/* line 3265, ../sass/_template_specific.scss */
body.de div#data #motor_reserva .paraty-booking-form #contenedor_opciones .selectricWrapper.selector_adultos p.label, body.de div#data #motor_reserva .paraty-booking-form #contenedor_opciones .selectricWrapper.selector_ninos p.label, body.de div#data #motor_reserva .paraty-booking-form #contenedor_opciones .selectricWrapper.selector_bebes p.label {
  font-size: 15px !important;
}

/* line 1, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper {
  padding: 30px 0;
  height: auto;
  background-image: url("/img/onama/bg-bottom.png");
  background-size: 1400px 120px;
  background-position: center bottom;
  background-color: white;
}
/* line 9, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper * {
  box-sizing: border-box;
}
/* line 13, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .center_newsletter_social {
  margin: 15px auto 0;
  padding: 30px 0;
  background: white;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}
/* line 18, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .center_newsletter_social a {
  text-decoration: none;
}
/* line 23, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper {
  display: none;
  padding: 30px 0 0;
  margin: 0;
  width: 100%;
  background-image: url("/img/onama/bg-ones.png");
  background-size: 1400px 120px;
  background-position: center;
  background-color: #EDEDED;
}
/* line 33, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .container12 {
  background: white;
  padding: 20px 40px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);
}
/* line 39, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_content {
  display: inline-block;
  vertical-align: middle;
  width: 370px;
  padding-right: 20px;
}
/* line 45, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_content .newsletter_title {
  text-transform: uppercase;
  color: #231b22;
  font-weight: 600;
  font-size: 25px;
}
/* line 52, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_content .newsletter_description {
  text-transform: uppercase;
  color: #231b22;
  font-size: 14px;
  margin-top: 20px;
}
/* line 60, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_icos_link_wrapper {
  display: inline-block;
  vertical-align: middle;
  width: 690px;
  background-color: white;
  padding: 20px 0;
}
/* line 67, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_icos_link_wrapper .newsletter_icos_wrapper {
  display: inline-block;
  vertical-align: middle;
  width: 500px;
}
/* line 72, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_icos_link_wrapper .newsletter_icos_wrapper .icon_element {
  display: inline-block;
  vertical-align: top;
  text-align: center;
  width: calc((100% - 15px) / 4);
}
/* line 78, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_icos_link_wrapper .newsletter_icos_wrapper .icon_element .icon_picture {
  border-radius: 50%;
  display: inline-block;
  position: relative;
  overflow: hidden;
  width: 60px;
  height: 60px;
}
/* line 86, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_icos_link_wrapper .newsletter_icos_wrapper .icon_element .icon_picture i.fa {
  color: #00B0B9;
  font-size: 46px;
}
/* line 92, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_icos_link_wrapper .newsletter_icos_wrapper .icon_element .icon_title {
  color: #231b22;
  text-transform: uppercase;
  font-size: 16px;
  margin-top: 10px;
}
/* line 101, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_icos_link_wrapper .newsletter_link {
  display: inline-block;
  vertical-align: middle;
  margin-left: 10px;
}
/* line 106, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_icos_link_wrapper .newsletter_link a {
  text-decoration: none;
  display: inline-block;
  height: 50px;
  width: 170px;
  background-color: #00B0B9;
  color: white;
  position: relative;
  overflow: hidden;
}
/* line 117, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_icos_link_wrapper .newsletter_link a:hover .arrow {
  background-color: #151014;
}
/* line 122, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_icos_link_wrapper .newsletter_link a .newsletter_link_text {
  text-transform: uppercase;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 0;
  width: calc(100% - 50px);
  text-align: center;
}
/* line 130, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_icos_link_wrapper .newsletter_link a .arrow {
  font-family: 'fontawesome', sans-serif;
  display: inline-block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: #007f86;
  width: 50px;
  -webkit-transition: background-color 0.4s;
  -moz-transition: background-color 0.4s;
  -ms-transition: background-color 0.4s;
  -o-transition: background-color 0.4s;
  transition: background-color 0.4s;
}
/* line 141, ../sass/banners/_banner_newsletter.scss */
.newsletter_social_wrapper .newsletter_wrapper .newsletter_icos_link_wrapper .newsletter_link a .arrow:before {
  content: '\f054';
  color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 25px;
}

/* line 1, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper {
  display: inline-block;
  width: 100%;
  padding: 50px 0;
}
/* line 6, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper * {
  box-sizing: border-box;
}
/* line 10, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_icons_wrapper {
  display: inline-block;
  width: 540px;
  float: left;
}
/* line 15, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_icons_wrapper .icon_element {
  background-color: #F5F5F5;
  display: inline-block;
  width: calc((100% - 80px) / 2);
  margin-right: 80px;
  vertical-align: top;
  position: relative;
  padding: 20px 20px 20px 70px;
  margin-top: 30px;
}
/* line 25, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_icons_wrapper .icon_element:nth-child(-n+2) {
  margin-top: 0;
}
/* line 29, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_icons_wrapper .icon_element:nth-child(2n), .newsletter_landing_wrapper .newsletter_icons_wrapper .icon_element:last-child {
  margin-right: 0;
}
/* line 33, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_icons_wrapper .icon_element .icon_picture {
  background-color: #00B0B9;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  position: relative;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  left: 0;
}
/* line 42, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_icons_wrapper .icon_element .icon_picture i.fa {
  color: white;
  font-size: 48px;
}
/* line 48, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_icons_wrapper .icon_element .icon_title {
  text-transform: uppercase;
  color: #00B0B9;
  font-weight: 600;
}
/* line 54, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_icons_wrapper .icon_element .icon_description {
  font-size: 14px;
  margin-top: 10px;
}
/* line 61, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper {
  display: inline-block;
  width: 540px;
  float: right;
  background-color: #f5f5f5;
  padding: 26px 30px;
}
/* line 69, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .input_wrapper {
  margin-bottom: 15px;
  position: relative;
}
/* line 73, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .input_wrapper label {
  font-size: 14px;
}
/* line 76, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .input_wrapper label input {
  display: block;
  width: 100%;
  height: 38px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  margin-top: 10px;
  padding-left: 15px;
}
/* line 88, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .input_wrapper label select {
  display: none;
}
/* line 92, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .input_wrapper label .select2 {
  display: block;
  width: 100% !important;
  margin-top: 10px;
}
/* line 97, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .input_wrapper label .select2 .select2-selection {
  border-radius: 0;
  border: 0;
  height: 38px;
}
/* line 102, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .input_wrapper label .select2 .select2-selection .select2-selection__rendered {
  line-height: 38px;
}
/* line 106, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .input_wrapper label .select2 .select2-selection .select2-selection__arrow {
  height: 38px;
}
/* line 112, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .input_wrapper label label.error {
  position: absolute;
  top: 100%;
  left: 0;
  border: 1px solid red;
  border-radius: 3px;
  background-color: #ffa7a7;
  padding: 3px 10px;
  font-size: 12px;
  z-index: 2;
}
/* line 126, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .check_newsletter {
  display: inline-block;
  width: 290px;
}
/* line 130, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .check_newsletter .newsletter_checkbox {
  margin-top: 5px;
  display: inline-block;
  width: 100%;
  position: relative;
}
/* line 136, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .check_newsletter .newsletter_checkbox:first-child {
  margin-top: 0;
}
/* line 140, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .check_newsletter .newsletter_checkbox input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 10px;
  height: 10px;
  border: 0;
  background-color: #fff;
  margin: 2px 5px 0 0;
  display: inline-block;
  vertical-align: top;
  -webkit-transition: background-color 0.4s;
  -moz-transition: background-color 0.4s;
  -ms-transition: background-color 0.4s;
  -o-transition: background-color 0.4s;
  transition: background-color 0.4s;
}
/* line 153, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .check_newsletter .newsletter_checkbox input:checked {
  background-color: #00B0B9;
}
/* line 158, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .check_newsletter .newsletter_checkbox label, .newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .check_newsletter .newsletter_checkbox a {
  display: inline-block;
  vertical-align: top;
  font-size: 11px;
  width: calc(100% - 20px);
  color: black;
  text-decoration: none;
}
/* line 167, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .check_newsletter .newsletter_checkbox label.error {
  position: absolute;
  top: 100%;
  left: 0;
  border: 1px solid red;
  border-radius: 3px;
  background-color: #ffa7a7;
  padding: 3px 10px;
  font-size: 12px;
  z-index: 2;
}
/* line 181, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .newsletter_button {
  float: right;
  display: inline-block;
  background-color: #00B0B9;
  color: white;
  text-transform: uppercase;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  font-size: 16px;
  height: 45px;
  width: 160px;
  cursor: pointer;
  -webkit-transition: opacity 0.4s;
  -moz-transition: opacity 0.4s;
  -ms-transition: opacity 0.4s;
  -o-transition: opacity 0.4s;
  transition: opacity 0.4s;
}
/* line 197, ../sass/banners/_form_newsletter.scss */
.newsletter_landing_wrapper .newsletter_form_wrapper .newsletter_form .newsletter_button:hover {
  opacity: .8;
}

/* line 206, ../sass/banners/_form_newsletter.scss */
.newsletter_thanks_popup_wrapper .fancybox-outer {
  background-color: #fff;
  padding: 30px !important;
}
/* line 211, ../sass/banners/_form_newsletter.scss */
.newsletter_thanks_popup_wrapper .newsletter_thanks_popup {
  text-align: center;
}
/* line 214, ../sass/banners/_form_newsletter.scss */
.newsletter_thanks_popup_wrapper .newsletter_thanks_popup .popup_description {
  width: 100%;
  color: #00B0B9 !important;
  padding: 10px;
}

/* line 1, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 100;
}
/* line 7, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper * {
  box-sizing: border-box;
}
/* line 11, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_message_wrapper {
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
  text-align: right;
}
/* line 15, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_message_wrapper.active .subscribe_message_wrapper {
  opacity: 0;
}
/* line 19, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_message_wrapper.active .newsletter_message_arrow {
  border-radius: 5px 5px 0 0;
}
/* line 22, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_message_wrapper.active .newsletter_message_arrow:hover {
  background-color: #231b22;
}
/* line 32, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_message_wrapper .subscribe_message_wrapper {
  display: inline-block;
  position: relative;
  background-color: #5F319C;
  opacity: 1;
  border-radius: 5px 0 0 5px;
  -webkit-transition: opacity 0.4s;
  -moz-transition: opacity 0.4s;
  -ms-transition: opacity 0.4s;
  -o-transition: opacity 0.4s;
  transition: opacity 0.4s;
}
/* line 40, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_message_wrapper .subscribe_message_wrapper .icon_wrapper {
  display: inline-block;
  padding: 15px 15px 16px;
  vertical-align: middle;
}
/* line 45, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_message_wrapper .subscribe_message_wrapper .icon_wrapper i.fa {
  color: white;
  font-size: 18px;
}
/* line 51, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_message_wrapper .subscribe_message_wrapper .subscribe_message {
  vertical-align: middle;
  color: white;
  /*display: inline-block;*/
  text-transform: uppercase;
  font-size: 24px;
  padding: 10px;
  display: none;
}
/* line 62, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_message_wrapper .newsletter_message_arrow {
  display: inline-block;
  vertical-align: top;
  height: 53px;
  width: 51px;
  background-color: #5F319C;
  border-radius: 0 5px 5px 0;
  position: relative;
  cursor: pointer;
  -webkit-transition: background-color 0.4s;
  -moz-transition: background-color 0.4s;
  -ms-transition: background-color 0.4s;
  -o-transition: background-color 0.4s;
  transition: background-color 0.4s;
}
/* line 73, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_message_wrapper .newsletter_message_arrow:hover i.fa {
  color: #DDD;
}
/* line 77, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_message_wrapper .newsletter_message_arrow i.fa {
  -webkit-transition: transform 0.4s;
  -moz-transition: transform 0.4s;
  -ms-transition: transform 0.4s;
  -o-transition: transform 0.4s;
  transition: transform 0.4s;
  color: white;
  font-size: 24px;
  width: 100%;
  height: 100%;
}
/* line 84, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_message_wrapper .newsletter_message_arrow i.fa:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 91, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_floating_content {
  width: 272px;
  background-position: center;
  background-color: #EDEDED;
  padding: 30px;
  border-radius: 5px 0 5px 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  text-align: center;
  display: none;
  position: relative;
}
/* line 102, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_floating_content .floating_title {
  text-transform: uppercase;
  color: #00B0B9;
  font-weight: 600;
  font-size: 24px;
}
/* line 109, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_floating_content .floating_description {
  text-transform: uppercase;
  font-size: 18px;
  color: #231b22;
  margin-top: 10px;
}
/* line 116, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_floating_content .newsletter_link {
  display: block;
  margin-top: 30px;
}
/* line 120, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_floating_content .newsletter_link a {
  display: block;
  height: 50px;
  width: 100%;
  background-color: #00B0B9;
  color: white;
  position: relative;
  overflow: hidden;
}
/* line 130, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_floating_content .newsletter_link a:hover .arrow {
  background-color: #231b22;
}
/* line 135, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_floating_content .newsletter_link a .newsletter_link_text {
  text-transform: uppercase;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 0;
  width: calc(100% - 50px);
  text-align: center;
}
/* line 143, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_floating_content .newsletter_link a .arrow {
  font-family: 'Fontawesome', sans-serif;
  display: inline-block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: #007f86;
  width: 50px;
  -webkit-transition: background-color 0.4s;
  -moz-transition: background-color 0.4s;
  -ms-transition: background-color 0.4s;
  -o-transition: background-color 0.4s;
  transition: background-color 0.4s;
}
/* line 154, ../sass/banners/_floating_newsletter.scss */
.newsletter_floating_wrapper .newsletter_floating_content .newsletter_link a .arrow:before {
  content: '\f054';
  color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 24px;
}

/* line 1, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper {
  position: relative;
  padding: 85px calc((100% - 1040px) / 2);
  text-align: center;
}
/* line 8, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.v2 .banner_ticks_content .title_logo_wrapper {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
}
/* line 13, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.v2 .banner_ticks_content .title_logo_wrapper .logo {
  margin-right: 35px;
}
/* line 18, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.v2 .banner_ticks_content .content_desc {
  font-size: 20px;
  margin-top: 45px;
  display: flex;
  flex-wrap: wrap;
  width: 420px;
}
/* line 25, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.v2 .banner_ticks_content .content_desc .button_traveler {
  margin-top: 40px;
  float: none;
  display: inline-block;
  padding: 15px 45px;
  color: white;
  background-color: #00B0B9;
  text-transform: none;
  border-radius: 50px;
}
/* line 36, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.v2 .banner_ticks_content .content_desc .wave_pic {
  text-align: center;
}
/* line 44, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.v2 .banner_ticks .tick div .desc {
  font-size: 16px;
}
/* line 52, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .buble_floating {
  width: 40vw;
  height: 60vh;
  margin-left: 70px;
}
/* line 57, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .buble_floating svg * {
  fill: #5F319C;
}
/* line 62, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .banner_ticks_content, .banner_ticks_wrapper .banner_ticks {
  position: relative;
  z-index: 2;
  display: inline-block;
  vertical-align: middle;
}
/* line 68, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .banner_ticks_content {
  width: calc(100% - 405px);
  padding-left: 30px;
  text-align: left;
}
/* line 72, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .banner_ticks_content .content_title {
  margin-bottom: 30px;
}
/* line 74, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .banner_ticks_content .content_title h1 {
  font-size: 50px;
}
/* line 78, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .banner_ticks_content .content_desc {
  padding-top: 10px;
}
/* line 80, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .banner_ticks_content .content_desc .btn {
  float: left;
  clear: both;
  padding: 5px 25px;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 40px;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.3);
}
/* line 88, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .banner_ticks_content .content_desc .btn i {
  margin-right: 10px;
}
/* line 92, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .banner_ticks_content .content_desc h1, .banner_ticks_wrapper .banner_ticks_content .content_desc h2, .banner_ticks_wrapper .banner_ticks_content .content_desc h3, .banner_ticks_wrapper .banner_ticks_content .content_desc h4, .banner_ticks_wrapper .banner_ticks_content .content_desc h5, .banner_ticks_wrapper .banner_ticks_content .content_desc h6 {
  float: left;
  clear: both;
  padding-bottom: 25px;
  background: url("/img/onama/wave-navy.png") bottom left repeat-x;
}
/* line 101, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .banner_ticks {
  width: 320px;
  text-align: left;
}
/* line 104, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .banner_ticks .tick {
  margin: 50px 0;
}
/* line 106, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .banner_ticks .tick img {
  display: inline-block;
  vertical-align: middle;
  width: 67px;
  margin-right: 20px;
}
/* line 112, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .banner_ticks .tick img + div {
  display: inline-block;
  vertical-align: middle;
  width: calc(100% - 125px);
}
/* line 116, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper .banner_ticks .tick img + div .desc {
  font-size: 12px;
}
/* line 122, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile {
  position: relative;
  padding: 30px 0;
  text-align: center;
  background: url("/img/onama/background_ona_1.png") no-repeat center right;
  background-size: cover;
}
/* line 132, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile.v2 .banner_ticks_content .title_logo_wrapper {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
}
/* line 137, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile.v2 .banner_ticks_content .title_logo_wrapper .logo {
  width: 72px;
  margin-right: 24px;
}
/* line 141, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile.v2 .banner_ticks_content .title_logo_wrapper .logo img {
  width: 100%;
}
/* line 146, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile.v2 .banner_ticks_content .title_logo_wrapper .content_title {
  margin-bottom: 0;
}
/* line 149, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile.v2 .banner_ticks_content .title_logo_wrapper .content_title h2 {
  font-size: 19pt;
}
/* line 153, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile.v2 .banner_ticks_content .title_logo_wrapper .content_title h1 {
  font-size: 24pt;
  margin-bottom: 0;
}
/* line 160, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile.v2 .banner_ticks_content .content_desc {
  margin-top: 47px;
  font-size: 13pt;
  text-align: center;
}
/* line 165, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile.v2 .banner_ticks_content .content_desc > div {
  text-align: left;
}
/* line 171, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile.v2 .banner_ticks {
  max-width: 325px;
  margin: 20px auto 0;
  width: 100%;
}
/* line 176, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile.v2 .banner_ticks .tick {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  margin-bottom: 40px;
}
/* line 182, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile.v2 .banner_ticks .tick img {
  margin: 0 30px 0 0;
}
/* line 186, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile.v2 .banner_ticks .tick div .desc {
  font-size: 12pt;
}
/* line 192, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile.v2 .button_traveler {
  margin-top: 30px;
  margin-bottom: 0;
  background-color: #5F319C;
  color: #00B0B9;
  font-size: 16px;
  padding: 12px 40px;
  text-transform: none;
  font-weight: 600;
}
/* line 203, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile.v2 .wave_pic {
  margin-top: 15px;
  text-align: center;
}
/* line 209, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile .buble_floating {
  display: none;
}
/* line 212, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile .banner_ticks_content, .banner_ticks_wrapper.mobile .banner_ticks {
  display: block;
}
/* line 215, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile .banner_ticks_content {
  padding: 20px;
  text-align: left;
}
/* line 218, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile .banner_ticks_content .content_title {
  margin-bottom: 30px;
}
/* line 220, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile .banner_ticks_content .content_title .h1, .banner_ticks_wrapper.mobile .banner_ticks_content .content_title .h2, .banner_ticks_wrapper.mobile .banner_ticks_content .content_title .h3, .banner_ticks_wrapper.mobile .banner_ticks_content .content_title .h4, .banner_ticks_wrapper.mobile .banner_ticks_content .content_title .h5, .banner_ticks_wrapper.mobile .banner_ticks_content .content_title .h6,
.banner_ticks_wrapper.mobile .banner_ticks_content .content_title h1, .banner_ticks_wrapper.mobile .banner_ticks_content .content_title h2, .banner_ticks_wrapper.mobile .banner_ticks_content .content_title h3, .banner_ticks_wrapper.mobile .banner_ticks_content .content_title h4, .banner_ticks_wrapper.mobile .banner_ticks_content .content_title h5, .banner_ticks_wrapper.mobile .banner_ticks_content .content_title h6 {
  text-align: left;
}
/* line 224, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile .banner_ticks_content .content_title h1 {
  font-weight: bold;
}
/* line 229, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile .banner_ticks_content .content_desc .btn {
  padding: 5px 25px;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.3);
}
/* line 235, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile .banner_ticks_content .content_desc .btn i {
  margin-right: 10px;
}
/* line 241, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile .banner_ticks {
  text-align: left;
}
/* line 243, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile .banner_ticks .tick {
  margin: 0;
  padding: 0 20px;
}
/* line 246, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile .banner_ticks .tick img {
  display: inline-block;
  vertical-align: middle;
  width: 50px;
  margin: 20px 10px 20px 0;
}
/* line 252, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile .banner_ticks .tick img + div {
  display: inline-block;
  vertical-align: middle;
  font-size: 18px;
  width: calc(100% - 75px);
}
/* line 257, ../sass/banners/_banner_ticks.scss */
.banner_ticks_wrapper.mobile .banner_ticks .tick img + div .desc {
  font-size: 12px;
}
