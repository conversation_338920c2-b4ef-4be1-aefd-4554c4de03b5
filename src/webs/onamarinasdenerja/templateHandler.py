# -*- coding: utf-8 -*-
import copy
import os
from collections import OrderedDict

from booking_process.constants.advance_configs_names import PUBLIC_CAPTCHA_KEY, STYLE_NAME, EMAIL_SENDER, CONTACT_PHONES, \
	TOP_SECTIONS
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import getPicturesForKey, get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_code
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.templates.template_utils import buildTemplate
from webs.BaseTemplateHandler2 import BaseTemplateHandler2

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

TEMPLATE_NAME = "onamarinasdenerja"
#Change this value too in default.scss and in config.rb!!
base_web = "onama"


class TemplateHandler(BaseTemplateHandler2):

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		#Advance properties of section
		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

		result_params_dict = {'base_web': base_web,
							  'footer_columns': get_pictures_from_section_name("footer columns", language),
		                      'newsletter_text': get_section_from_section_spanish_name('newsletter_text', language),
		                      'footer_menu': get_pictures_from_section_name('footer menu', language),
		                      'footer_prominent': get_pictures_from_section_name('footer destacados', language),
		                      'weding_form_title': get_section_from_section_spanish_name("bodas form titulo", language),
		                      'bottom_popup': get_section_from_section_spanish_name("popup inicio footer", language),
		                      'popup_inicio_automatico': self.getPicturesProperties(language, "popup inicio", ['booking']),
		                      'booking_engine_2': self.buildSearchEngine2(language),
		                      'footer_logos': get_pictures_from_section_name('footer_logos', language),
							  'aviso_legal': get_section_from_section_spanish_name("aviso legal", language),
							  'newsletter_banner': self.getNewsletterData(language),
							  'actual_language': language,
							  'styles_name': get_config_property_value(STYLE_NAME),
							  'pictures_silder': get_pictures_from_section_name(section_name, language),
							  'extra_top_sections': self.getPicturesProperties(language, "_extra_top_sections")
		                      }

		header_link = get_pictures_from_section_name("_header_link", language)
		if user_agent_is_mobile():
			result_params_dict['fontawesome5'] = True
			result_params_dict['header_link'] = list(filter(lambda x: x.get('title') == 'link', header_link))
			result_params_dict['header_link_icon'] = list(filter(lambda x: x.get('title') == 'icon', header_link))

			if advance_properties.get("banner_advantages"):
				myParams = {
					"is_mobile": True,
					'banner_advantages_pics': get_pictures_from_section_name(
						advance_properties.get("banner_advantages"), language),
					'banner_advantages': get_section_from_section_spanish_name(
						advance_properties.get("banner_advantages"), language)
				}

				fullPath = os.path.join(os.path.dirname(__file__), 'template/banners/_banner_traveller.html')
				result = dict(list(myParams.items()) + list(get_web_dictionary(language).items()))
				result_params_dict['html_after_booking'] = buildTemplate(fullPath, result)

			if advance_properties.get('hide_booking_widget'):
				script_args = {
					"section_type": sectionToUse.get("sectionType")
				}
				params_mobile = {
					"extra_bottom_script": self.buildTemplate_2("mobile/_script_mobile.html", script_args, False, TEMPLATE_NAME),
				}
				result_params_dict.update(params_mobile)

		all_sections = self.getSections(language)
		top_sections = self.getSectionsFor(language, all_sections, TOP_SECTIONS)
		for top_element in top_sections:
			section_pictures = getPicturesForKey(language, top_element.get('key'), [])
			top_element['icon'] = list(filter(lambda x: x.get('title') == 'icon', section_pictures))
		self.internationalizeUrls(top_sections)
		result_params_dict['top_sections'] = top_sections


		if header_link:
			result_params_dict['header_link'] = filter(lambda x: x.get('title') == 'link', header_link)
			result_params_dict['header_link_icon'] = filter(lambda x: x.get('title') == 'icon', header_link)

		#Contact booking popup
		context_contact = dict(list(get_web_dictionary(language).items()) + list({'hotel_phone': get_config_property_value(CONTACT_PHONES).split(";")}.items()))
		result_params_dict['contact_booking_popup'] = buildTemplate(os.path.join(os.path.dirname(__file__), 'template/booking_contact_bottom.html'), context_contact)

		#Automatic content
		automatic_content_sections = {
			'Mis Reservas': True
		}
		if automatic_content_sections.get(section_type, False):
			result_params_dict['automatic_content'] = True

		if self.picturesInSlider(language):
			slider_pictures = get_pictures_from_section_name(sectionToUse.get('sectionName'), language)
			slider_pictures_filtered = list(filter(lambda x: x.get('title') == 'slider', slider_pictures))
			result_params_dict['pictures'] = slider_pictures_filtered if slider_pictures_filtered else slider_pictures

		if section_type == 'Inicio':
			result_params_dict['ini_section'] = True

		actual_section = get_section_from_section_spanish_name(sectionToUse['sectionName'], language)
		if actual_section.get('subtitle'):
			result_params_dict['content_subtitle'] = actual_section

		if advance_properties.get('top_carousel'):
			result_params_dict['top_carousel_images'] = get_pictures_from_section_name(advance_properties['top_carousel'], language)

		if advance_properties.get("table_book_text"):
			result_params_dict['table_book_text'] = advance_properties.get("table_book_text")
			result_params_dict['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)
			result_params_dict['language'] = language
			if advance_properties.get('custom_hours_range'):
				hours_group = advance_properties.get('custom_hours_range').split("@@")
				result_params_dict['book_table_options'] = []
				for group in hours_group:
					group_list = group.split(';')
					if len(group_list) > 1:
						group_name = group_list.pop(0)
						mini_dict = {
							'group_name': group_name,
							'options': group_list
						}
						result_params_dict['book_table_options'].append(mini_dict)

		if advance_properties.get('celebration_blocks') or advance_properties.get('banners_x2'):
			picures_section = advance_properties['celebration_blocks'] if advance_properties.get('celebration_blocks') else advance_properties.get('banners_x2')
			result_params_dict['bannerx2_blocks'] = advance_properties.get('banners_x2')
			result_params_dict["celebration_blocks"] = get_pictures_from_section_name(picures_section, language)
			for celebration_element in result_params_dict['celebration_blocks']:
				if celebration_element.get('linkUrl'):
					celebration_element['gallery_pictures'] = get_pictures_from_section_name(celebration_element['linkUrl'], language)
				celebration_properties = self.getSectionAdvanceProperties(celebration_element, language)
				if celebration_properties.get('disabled_info'):
					celebration_element['disabled_info'] = True
				if celebration_properties.get("reservar"):
					celebration_element['booking_button'] = True
				if celebration_properties.get("use_link"):
					celebration_element['use_link'] = True
				if celebration_properties.get("promocode"):
					celebration_element['promocode'] = celebration_properties.get("promocode")
				if celebration_properties.get("external_link"):
					celebration_element['external_link'] = celebration_properties.get("external_link")
				if celebration_properties.get("form_hamacas"):
					celebration_element['form_hamacas'] = True

		if advance_properties.get('included_blocks'):
			result_params_dict['included_blocks'] = {
				'title': get_section_from_section_spanish_name(advance_properties['included_blocks'], language).get('subtitle'),
				'pictures': get_pictures_from_section_name(advance_properties['included_blocks'], language)
			}

		if advance_properties.get('full_blocks'):
			result_params_dict['full_blocks_top'] = get_pictures_from_section_name(advance_properties['full_blocks'], language)

		if advance_properties.get("banner_advantages"):
			result_params_dict['banner_advantages_pics'] = get_pictures_from_section_name(advance_properties['banner_advantages'], language)
			result_params_dict['banner_advantages'] = get_section_from_section_spanish_name(advance_properties['banner_advantages'], language)

		if advance_properties.get('bottom_pictures'):
			result_params_dict['bottom_pictures_list'] = get_pictures_from_section_name(advance_properties['bottom_pictures'], language)[:10]

		if advance_properties.get('destination_email'):
			result_params_dict['destination_email'] = advance_properties.get('destination_email')

		if advance_properties.get("custom_menu"):
			custom_menu = self.filterSections(allSections, advance_properties['custom_menu'].split(";"))
			result_params_dict['main_sections'] = custom_menu

		if section_type == 'Habitaciones':
			all_rooms = get_pictures_from_section_name('habitaciones_blocks', language)
			for x in all_rooms:
				room_advance = self.getSectionAdvanceProperties(x, language)
				if room_advance.get('capacidad', False):
					x['capacity'] = room_advance['capacidad']
					# x['capacity'] = [None] * int(room_advance['capacidad'])

				if room_advance.get('tipo', False):
					x['tipo'] = room_advance['tipo'].split(";")


				x['pictures'] = get_pictures_from_section_name(x.get('linkUrl', ''), language)
				x['services'] = get_section_from_section_spanish_name(x.get('linkUrl', ''), language).get('content', '')

			result_params_dict['rooms'] = all_rooms
			room_types = ['honeymoon', 'romantica', 'relax', 'business']
			result_params_dict['type_room'] = {}
			for x in room_types:
				result_params_dict['type_room'][x] = get_section_from_section_spanish_name(x + '_type', language)

		if section_type == 'Extra 1':
			result_params_dict['events_form'] = True

		if section_type == 'Extra 2':
			result_params_dict['newsletter_form'] = True

		if section_type == 'Ofertas':
			result_params_dict['offers'] = self.buildPromotionsInfo(language)

		if section_type == u"Galeria de Imagenes":
			result_params_dict['images'] = OrderedDict()
			result_params_dict['images_section'] = True
			result_params_dict["content_page"] = get_section_from_section_spanish_name(section_name, language)
			gallery_pictures = get_pictures_from_section_name(sectionToUse['sectionName'], language)
			for picture_element in gallery_pictures:
				if picture_element.get('linkUrl'):
					picture_element['video'] = picture_element['linkUrl']
			gallery_pictures_filtered = list(filter(lambda x: x.get('title') != 'slider', gallery_pictures))
			result_params_dict['images'][get_config_property_value(EMAIL_SENDER)] = {'images_blocks': gallery_pictures_filtered}

		#Atencion al cliente
		if section_type == u'Atención al cliente':
			additionalParams4Contact={}
			additionalParams4Contact['language'] = get_language_code(language)
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = True
			additionalParams4Contact['privacy_checkbox'] = True
			additionalParams4Contact['nofollow'] = True
			sectionTemplate = 'secciones/contact.html'
			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			mySectionParams['content'] = ''
			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)
			result_params_dict['contact_html'] = contact_html
			result_params_dict['disabled_maps'] = True

		#Localizacion y contacto
		if section_type == u'Localización':
			additionalParams4Contact={}
			additionalParams4Contact['language'] = language
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = True
			additionalParams4Contact['privacy_checkbox'] = True

			sectionTemplate = 'secciones/contact.html'

			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			mySectionParams['content'] = ''

			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)
			if result_params_dict.get('content_subtitle'):
				result_params_dict['content_subtitle'] = copy.deepcopy(result_params_dict['content_subtitle'])
				result_params_dict['content_subtitle']['content'] = ''
			result_params_dict['banners1'] = self.getPromotionBanners('banners1', language)
			result_params_dict['banners2'] = self.getPromotionBanners('banners2', language)
			result_params_dict['main_content'] = get_section_from_section_spanish_name(u'localización', language)
			result_params_dict['left_content'] = get_section_from_section_spanish_name(u'localización left', language)
			result_params_dict['contact_html'] = contact_html
			result_params_dict['location_html'] = get_section_from_section_spanish_name(sectionToUse['sectionName'], language)
			result_params_dict['subtitle_form'] = sectionToUse['subtitle']
			iframe_google_map = get_section_from_section_spanish_name("iframe google maps", language)
			result_params_dict['iframe_google_map'] = iframe_google_map

		if advance_properties.get('hide_booking_widget'):
			result_params_dict['hide_booking_widget'] = advance_properties['hide_booking_widget']

		if advance_properties.get('form_hamacas'):
			form_hamacas = {}
			form_hamacas.update(get_web_dictionary(language))
			form_hamacas_html = self.buildTemplate_2("/_form_hamacas.html", form_hamacas, False, TEMPLATE_NAME)
			result_params_dict['custom_elements'] += form_hamacas_html

		return result_params_dict

	def getNewsletterData(self, language):
		params = {
			"section": get_section_from_section_spanish_name("_newsletter_banner", language),
			"icons": [],
			"popup": get_section_from_section_spanish_name("_newsletter_popup", language)
		}

		newsletter_pictures = self.getPicturesProperties(language, "_newsletter_banner", ['icon'])

		for picture in newsletter_pictures:
			if picture.get("linkUrl"):
				params['link'] = picture['linkUrl']

			if picture.get("title", "") == "background":
				params['background'] = picture.get("servingUrl")

			else:
				params['icons'].append(picture)

		newsletter_advance_properties = self.getSectionAdvanceProperties(params['section'], language)
		if newsletter_advance_properties.get("button_text"):
			params['link_text'] = newsletter_advance_properties['button_text']

		if newsletter_advance_properties.get("floating_text"):
			params['floating_text'] = newsletter_advance_properties['floating_text']

		return params

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		params['booking_no_hide'] = True

		return self.buildTemplate('booking/booking_engine_5/_booking_widget.html', params, allowMobile=False)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		context = {
			'booking_header': get_section_from_section_spanish_name('booking_header', language)

		}
		context['booking_header_promocode'] = self.getSectionAdvanceProperties(context['booking_header'], language).get('promocode')
		options['custom_title_html'] = buildTemplate('%s/template/header_booking.html' % '/'.join(os.path.abspath(__file__).split("/")[:-1]), context)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		options['booking_no_hide'] = True
		return options

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "550"

	def get_revolution_full_screen(self):
		return "on"

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['horizontal_nolabel'] = True
		options['custom_new_title'] = get_section_from_section_spanish_name('saber mas', language).get('content', '')
		options['caption_submit_book'] = True
		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):

		sectionToUse = self.getSectionParams(sectionFriendlyUrl, language)

		if sectionToUse:
			advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)
			name_current_section = sectionToUse['sectionName'].lower().strip()
			type_current_section = sectionToUse['sectionType']

			if user_agent_is_mobile():

				additionalParams['custom_elements'] = ""

				if name_current_section == 'bodas y eventos' or advance_properties.get('event_form'):
					base_path = os.path.dirname(__file__)

					template_values = {}
					if advance_properties.get('destination_email'):
						template_values['destination_email'] = advance_properties.get('destination_email')
					template_values['event'] = get_section_from_section_spanish_name('bodas y eventos', language)
					if not template_values['event']:
						template_values['event'] = sectionToUse

					template_values['event_images'] = get_pictures_from_section_name('bodas y eventos top', language)
					template_values['event_images_title'] = get_section_from_section_spanish_name('bodas y eventos top', language)['subtitle']
					template_values['weding_form_title'] = get_section_from_section_spanish_name('bodas form titulo', language)['subtitle']
					template_values['sectionName'] = sectionToUse['title']
					template_values['pictures'] = get_pictures_from_section_name('bodas y eventos', language)
					template_values['formulario_mobile'] = True

					fullPath = os.path.join(base_path, 'template/mobile.html')
					result = dict(list(template_values.items()) + list(get_web_dictionary(language).items()))

					return buildTemplate(fullPath, result)




				elif name_current_section == u'Gastronomía':
					base_path = os.path.dirname(__file__)

					template_values = {}
					template_values['hotel'] = get_pictures_from_section_name('gastronomia', language)
					for gastronomy_element in template_values['hotel']:
						gastronomy_element['pictures'] = get_pictures_from_section_name(gastronomy_element['linkUrl'], language)
					template_values['title'] = self.getSectionParams(sectionFriendlyUrl, language)
					template_values['more_pictures'] = True
					template_values['default'] = True


					fullPath = os.path.join(base_path, 'template/mobile_gastronomia.html')
					result = dict(list(template_values.items()) + list(get_web_dictionary(language).items()))

					return buildTemplate(fullPath, result)

				elif type_current_section == u'Extra 1':
					base_path = os.path.dirname(__file__)
					template_values = {}
					template_values['actual_content'] = get_section_from_section_spanish_name(sectionToUse['sectionName'], language)
					template_values['sorteo_video'] = get_section_from_section_spanish_name("sorteo_video", language)
					template_values['sorteo_base'] = get_section_from_section_spanish_name("sorteo_bases", language)
					template_values['sorteo_base_2'] = get_section_from_section_spanish_name("sorteo_bases_2", language)
					template_values['template_extend'] = 'empty_index_4_mobile.html'
					template_values['i_am_mobile'] = True
					template_values['main_content'] = None



					fullPath = os.path.join(base_path, 'template/mobile/sorteo.html')
					result = dict(list(template_values.items()) + list(get_web_dictionary(language).items()))

					return buildTemplate(fullPath, result)



				if advance_properties.get('celebration_blocks') or advance_properties.get('banners_x2'):
					context = {}
					picures_section = advance_properties['celebration_blocks'] if advance_properties.get('celebration_blocks') else advance_properties.get('banners_x2')
					context['bannerx2_blocks'] = advance_properties.get('banners_x2')
					context["celebration_blocks"] = get_pictures_from_section_name(picures_section, language)
					if advance_properties.get('destination_email'):
						context['destination_email'] = advance_properties.get('destination_email')
					for celebration_element in context['celebration_blocks']:
						if celebration_element.get('linkUrl'):
							celebration_element['gallery_pictures'] = get_pictures_from_section_name(celebration_element['linkUrl'], language)
						celebration_properties = self.getSectionAdvanceProperties(celebration_element, language)
						if celebration_properties.get('disabled_info'):
							celebration_element['disabled_info'] = True
						if celebration_properties.get("form_hamacas"):
							celebration_element['form_hamacas'] = True
							context['language'] = get_language_code(language)

					fullPath = os.path.join(os.path.dirname(__file__), 'template/mobile/general.html')
					result = dict(list(context.items()) + list(get_web_dictionary(language).items()))
					additionalParams['custom_elements'] = buildTemplate(fullPath, result)

				if advance_properties.get('form_hamacas'):
					form_hamacas = {}
					form_hamacas.update(get_web_dictionary(language))
					form_hamacas_html = self.buildTemplate_2("/_form_hamacas.html", form_hamacas, False, TEMPLATE_NAME)
					additionalParams['custom_elements'] += form_hamacas_html

				if advance_properties.get('bottom_pictures'):
					context = {
						"is_mobile": True,
						"bottom_pictures_list": get_pictures_from_section_name(
					advance_properties['bottom_pictures'], language)[:10],
					}

					fullPath = os.path.join(os.path.dirname(__file__), 'template/bottom_pictures.html')
					result = dict(list(context.items()) + list(get_web_dictionary(language).items()))
					additionalParams['custom_elements'] = buildTemplate(fullPath, result)

				if advance_properties.get('table_book_text'):
					context = {
						"is_mobile": True,
						"table_book_text": advance_properties.get("table_book_text"),
						"captcha_box": get_config_property_value(PUBLIC_CAPTCHA_KEY),
						"language": language
					}
					if advance_properties.get('custom_hours_range'):
						hours_group = advance_properties.get('custom_hours_range').split("@@")
						context['book_table_options'] = []
						for group in hours_group:
							group_list = group.split(';')
							if len(group_list) > 1:
								group_name = group_list.pop(0)
								mini_dict = {
									'group_name': group_name,
									'options': group_list
								}
								context['book_table_options'].append(mini_dict)

					fullPath = os.path.join(os.path.dirname(__file__), 'template/banners/_book_table.html')
					result = dict(list(context.items()) + list(get_web_dictionary(language).items()))
					additionalParams['custom_elements'] = buildTemplate(fullPath, result)

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)
