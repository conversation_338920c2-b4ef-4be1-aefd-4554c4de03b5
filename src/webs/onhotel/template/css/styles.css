/* line 8, ../../../../sass/plugins/_effects.scss */
.effects_sass {
  visibility: hidden;
}

/*=== Translate None with fadeOut ===*/
/*=== Translate From Bottom with fadeOut ===*/
@-webkit-keyframes slide_up {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-moz-keyframes slide_up {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-o-keyframes slide_up {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@keyframes slide_up {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
/* line 76, ../../../../sass/plugins/_effects.scss */
.slide_up_effect {
  -webkit-animation: slide_up 1s;
  /* Safari 4+ */
  -moz-animation: slide_up 1s;
  /* Fx 5+ */
  -o-animation: slide_up 1s;
  /* Opera 12+ */
  animation: slide_up 1s;
}

@-webkit-keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-moz-keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-o-keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
/* line 117, ../../../../sass/plugins/_effects.scss */
.slide_right_effect {
  -webkit-animation: slide_right 1s;
  /* Safari 4+ */
  -moz-animation: slide_right 1s;
  /* Fx 5+ */
  -o-animation: slide_right 1s;
  /* Opera 12+ */
  animation: slide_right 1s;
}

/*=== Translate From Bottom witout fadeOut ===*/
@-webkit-keyframes slide_up_no_fade_out {
  0% {
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-moz-keyframes slide_up_no_fade_out {
  0% {
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-o-keyframes slide_up_no_fade_out {
  0% {
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@keyframes slide_up_no_fade_out {
  0% {
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
/* line 174, ../../../../sass/plugins/_effects.scss */
.slide_up_no_fade_out_effect {
  -webkit-animation: slide_up_no_fade_out 1s;
  /* Safari 4+ */
  -moz-animation: slide_up_no_fade_out 1s;
  /* Fx 5+ */
  -o-animation: slide_up_no_fade_out 1s;
  /* Opera 12+ */
  animation: slide_up_no_fade_out 1s;
}

/*=== Fade Out Effect ===*/
@-webkit-keyframes fade_out_effect {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-moz-keyframes fade_out_effect {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-o-keyframes fade_out_effect {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fade_out_effect {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* line 223, ../../../../sass/plugins/_effects.scss */
.fade_out_trigger {
  -webkit-animation: fade_out_effect 1.5s;
  /* Safari 4+ */
  -moz-animation: fade_out_effect 1.5s;
  /* Fx 5+ */
  -o-animation: fade_out_effect 1.5s;
  /* Opera 12+ */
  animation: fade_out_effect 1.5s;
}

/*=== Pendule Effect ===*/
/*=== initial transform ===*/
@-webkit-keyframes pendule {
  0% {
    -webkit-transform: rotate(10deg);
    -moz-transform: rotate(10deg);
    -ms-transform: rotate(10deg);
    -o-transform: rotate(10deg);
    transform: rotate(10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
  100% {
    -webkit-transform: rotate(-10deg);
    -moz-transform: rotate(-10deg);
    -ms-transform: rotate(-10deg);
    -o-transform: rotate(-10deg);
    transform: rotate(-10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
}
@-moz-keyframes pendule {
  0% {
    -webkit-transform: rotate(10deg);
    -moz-transform: rotate(10deg);
    -ms-transform: rotate(10deg);
    -o-transform: rotate(10deg);
    transform: rotate(10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
  100% {
    -webkit-transform: rotate(-10deg);
    -moz-transform: rotate(-10deg);
    -ms-transform: rotate(-10deg);
    -o-transform: rotate(-10deg);
    transform: rotate(-10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
}
@-o-keyframes pendule {
  0% {
    -webkit-transform: rotate(10deg);
    -moz-transform: rotate(10deg);
    -ms-transform: rotate(10deg);
    -o-transform: rotate(10deg);
    transform: rotate(10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
  100% {
    -webkit-transform: rotate(-10deg);
    -moz-transform: rotate(-10deg);
    -ms-transform: rotate(-10deg);
    -o-transform: rotate(-10deg);
    transform: rotate(-10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
}
@keyframes pendule {
  0% {
    -webkit-transform: rotate(10deg);
    -moz-transform: rotate(10deg);
    -ms-transform: rotate(10deg);
    -o-transform: rotate(10deg);
    transform: rotate(10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
  100% {
    -webkit-transform: rotate(-10deg);
    -moz-transform: rotate(-10deg);
    -ms-transform: rotate(-10deg);
    -o-transform: rotate(-10deg);
    transform: rotate(-10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
}
/* line 276, ../../../../sass/plugins/_effects.scss */
.pendule {
  -webkit-animation: pendule 2s ease-in-out 0s infinite alternate;
  /* Safari 4+ */
  -moz-animation: pendule 2s ease-in-out 0s infinite alternate;
  /* Fx 5+ */
  -o-animation: pendule 2s ease-in-out 0s infinite alternate;
  /* Opera 12+ */
  animation: pendule 2s ease-in-out 0s infinite alternate;
}

/*=== Text Translate ===*/
/* line 281, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom {
  overflow: hidden;
  position: relative;
}
/* line 285, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom::after {
  content: attr(data-content);
  left: 0;
  position: absolute;
  -moz-transition: -moz-transform, 0.6s;
  -o-transition: -o-transform, 0.6s;
  -webkit-transition: -webkit-transform, 0.6s;
  transition: transform, 0.6s;
  -moz-transform: translate(0, 100%);
  -ms-transform: translate(0, 100%);
  -webkit-transform: translate(0, 100%);
  transform: translate(0, 100%);
}
/* line 293, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom span {
  -moz-transition: -moz-transform, 0.6s;
  -o-transition: -o-transform, 0.6s;
  -webkit-transition: -webkit-transform, 0.6s;
  transition: transform, 0.6s;
  -moz-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}
/* line 299, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom:hover::after {
  -moz-transform: translate(0, 0%);
  -ms-transform: translate(0, 0%);
  -webkit-transform: translate(0, 0%);
  transform: translate(0, 0%);
}
/* line 303, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom:hover span {
  -moz-transform: translate(0, -100%);
  -ms-transform: translate(0, -100%);
  -webkit-transform: translate(0, -100%);
  transform: translate(0, -100%);
}

/*=== Flip Effect ===*/
@-webkit-keyframes flip {
  0% {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
  100% {
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
}
@-moz-keyframes flip {
  0% {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
  100% {
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
}
@-o-keyframes flip {
  0% {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
  100% {
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
}
@keyframes flip {
  0% {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
  100% {
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
}
/* line 358, ../../../../sass/plugins/_effects.scss */
.flip, #image-fiestas img {
  -webkit-animation: flip 3s ease-in-out 0s infinite;
  /* Safari 4+ */
  -moz-animation: flip 3s ease-in-out 0s infinite;
  /* Fx 5+ */
  -o-animation: flip 3s ease-in-out 0s infinite;
  /* Opera 12+ */
  animation: flip 3s ease-in-out 0s infinite;
}

/*=== moveBright Effect ===*/
@-webkit-keyframes moveBrightToRight {
  0% {
    left: -500%;
  }
  100% {
    left: 500%;
  }
}
@-moz-keyframes moveBrightToRight {
  0% {
    left: -500%;
  }
  100% {
    left: 500%;
  }
}
@-o-keyframes moveBrightToRight {
  0% {
    left: -500%;
  }
  100% {
    left: 500%;
  }
}
@keyframes moveBrightToRight {
  0% {
    left: -500%;
  }
  100% {
    left: 500%;
  }
}
/* line 1, ../sass/_sprites.scss */
.sprite, .mute_video, .mute_video.muted_icon {
  background-image: url(/img/onhol/spritesheet.png?v=1);
  background-repeat: no-repeat;
  display: block;
}

/* line 7, ../sass/_sprites.scss */
.sprite-arrow_link {
  width: 82px;
  height: 25px;
  background-position: -5px -5px;
}

/* line 13, ../sass/_sprites.scss */
.sprite-boton_search {
  width: 150px;
  height: 150px;
  background-position: -97px -5px;
}

/* line 19, ../sass/_sprites.scss */
.sprite-boton_search_2 {
  width: 150px;
  height: 150px;
  background-position: -257px -5px;
}

/* line 25, ../sass/_sprites.scss */
.sprite-boton_search_green {
  width: 150px;
  height: 150px;
  background-position: -417px -5px;
}

/* line 31, ../sass/_sprites.scss */
.sprite-calendar_ico {
  width: 35px;
  height: 38px;
  background-position: -577px -5px;
}

/* line 37, ../sass/_sprites.scss */
.sprite-calendario {
  width: 25px;
  height: 25px;
  background-position: -622px -5px;
}

/* line 43, ../sass/_sprites.scss */
.sprite-close_button {
  width: 72px;
  height: 62px;
  background-position: -657px -5px;
}

/* line 49, ../sass/_sprites.scss */
.sprite-flechas-der {
  width: 45px;
  height: 45px;
  background-position: -739px -5px;
}

/* line 55, ../sass/_sprites.scss */
.sprite-flechas-izq {
  width: 45px;
  height: 45px;
  background-position: -794px -5px;
}

/* line 61, ../sass/_sprites.scss */
.sprite-ico-fotos-habitaciones {
  width: 50px;
  height: 50px;
  background-position: -849px -5px;
}

/* line 67, ../sass/_sprites.scss */
.sprite-ico-misreservas {
  width: 20px;
  height: 20px;
  background-position: -909px -5px;
}

/* line 73, ../sass/_sprites.scss */
.sprite-ico_down {
  width: 27px;
  height: 20px;
  background-position: -909px -35px;
}

/* line 79, ../sass/_sprites.scss */
.sprite-ico_play {
  width: 70px;
  height: 70px;
  background-position: -5px -65px;
}

/* line 85, ../sass/_sprites.scss */
.sprite-img-fidelion-footer {
  width: 195px;
  height: 100px;
  background-position: -739px -65px;
}

/* line 91, ../sass/_sprites.scss */
.sprite-img-fidelion-footer-2 {
  width: 159px;
  height: 105px;
  background-position: -5px -175px;
}

/* line 97, ../sass/_sprites.scss */
.sprite-img-fidelion-header {
  width: 110px;
  height: 56px;
  background-position: -174px -175px;
}

/* line 103, ../sass/_sprites.scss */
.sprite-img-fidelion-header-en {
  width: 175px;
  height: 175px;
  background-position: -294px -175px;
}

/* line 109, ../sass/_sprites.scss */
.sprite-img-fidelion-header-es {
  width: 175px;
  height: 175px;
  background-position: -479px -175px;
}

/* line 115, ../sass/_sprites.scss */
.sprite-logo-presentsON-header-new {
  width: 125px;
  height: 125px;
  background-position: -664px -175px;
}

/* line 121, ../sass/_sprites.scss */
.sprite-logo_footer_1 {
  width: 100px;
  height: 98px;
  background-position: -799px -175px;
}

/* line 127, ../sass/_sprites.scss */
.sprite-logo_footer_2 {
  width: 100px;
  height: 98px;
  background-position: -174px -283px;
}

/* line 133, ../sass/_sprites.scss */
.sprite-marca-agua {
  width: 936px;
  height: 483px;
  background-position: -5px -391px;
}

/* line 139, ../sass/_sprites.scss */
.sprite-mute_icon, .mute_video.muted_icon {
  width: 30px;
  height: 30px;
  background-position: -577px -65px;
}

/* line 145, ../sass/_sprites.scss */
.sprite-opiniones {
  width: 28px;
  height: 25px;
  background-position: -617px -65px;
}

/* line 151, ../sass/_sprites.scss */
.sprite-plus {
  width: 29px;
  height: 28px;
  background-position: -909px -175px;
}

/* line 157, ../sass/_sprites.scss */
.sprite-select_down {
  width: 13px;
  height: 8px;
  background-position: -909px -213px;
}

/* line 163, ../sass/_sprites.scss */
.sprite-sound_ico, .mute_video {
  width: 30px;
  height: 30px;
  background-position: -909px -231px;
}

/* line 169, ../sass/_sprites.scss */
.sprite-whatsappFooter {
  width: 230px;
  height: 230px;
  background-position: -949px -5px;
}

/* line 175, ../sass/_sprites.scss */
.sprite-whatsappHeader {
  width: 108px;
  height: 25px;
  background-position: -174px -245px;
}

/* line 181, ../sass/_sprites.scss */
.sprite-wifi {
  width: 33px;
  height: 25px;
  background-position: -949px -245px;
}

/* line 1, ../sass/_sprites_2.scss */
.sprite_2 {
  background-image: url(/img/onhol/spritesheet_2.png?v=1);
  background-repeat: no-repeat;
  display: block;
}

/* line 8, ../sass/_sprites_2.scss */
.sprite-de {
  width: 20px;
  height: 20px;
  background-position: -5px -5px;
}

/* line 14, ../sass/_sprites_2.scss */
.sprite-en {
  width: 20px;
  height: 20px;
  background-position: -35px -5px;
}

/* line 20, ../sass/_sprites_2.scss */
.sprite-es {
  width: 20px;
  height: 20px;
  background-position: -65px -5px;
}

/* line 26, ../sass/_sprites_2.scss */
.sprite-facebook {
  width: 9px;
  height: 20px;
  background-position: -5px -35px;
}

/* line 32, ../sass/_sprites_2.scss */
.sprite-fr {
  width: 20px;
  height: 20px;
  background-position: -24px -35px;
}

/* line 38, ../sass/_sprites_2.scss */
.sprite-googleplus {
  width: 31px;
  height: 20px;
  background-position: -54px -35px;
}

/* line 44, ../sass/_sprites_2.scss */
.sprite-instagram {
  width: 19px;
  height: 20px;
  background-position: -5px -65px;
}

/* line 50, ../sass/_sprites_2.scss */
.sprite-pinterest {
  width: 15px;
  height: 20px;
  background-position: -34px -65px;
}

/* line 56, ../sass/_sprites_2.scss */
.sprite-pt {
  width: 20px;
  height: 20px;
  background-position: -59px -65px;
}

/* line 62, ../sass/_sprites_2.scss */
.sprite-twitter {
  width: 24px;
  height: 20px;
  background-position: -95px -5px;
}

/* line 68, ../sass/_sprites_2.scss */
.sprite-youtube {
  width: 16px;
  height: 20px;
  background-position: -95px -35px;
}

/* line 1, ../../../../sass/booking/_booking_engine.scss */
.booking_widget {
  position: absolute;
  z-index: 400;
  top: 185px;
}

/* line 7, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title {
  background: rgba(192, 207, 166, 0.8);
  text-align: center;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 20px;
  padding-top: 5px;
  padding-bottom: 5px;
}
/* line 15, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title h4 {
  margin-top: 0;
  color: white;
}
/* line 20, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title .booking_title_1,
.booking_form_title .booking_title_2 {
  display: none;
}

/* line 26, ../../../../sass/booking/_booking_engine.scss */
.booking_form {
  font-family: 'Source Sans Pro', sans-serif;
  padding: 20px;
  width: 260px;
  background: white;
}

/* line 33, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper {
  position: relative;
}
/* line 35, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper label {
  color: gray;
  font-size: 12px;
}
/* line 40, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper input {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  height: 40px;
  border: 0;
  width: 250px;
  padding-left: 10px;
}
/* line 48, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper .right_arrow {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  position: absolute;
  background: rgba(192, 207, 166, 0.8) url(/static_1/images/booking/flecha_motor_der.png) no-repeat center center;
  right: 2px;
  top: 30px;
  height: 35px;
  width: 35px;
}

/* line 62, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper {
  margin-right: 10px;
}
/* line 67, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper,
.stay_selection .rooms_number_wrapper {
  float: left;
  width: 80px;
}
/* line 74, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper label,
.stay_selection .departure_date_wrapper label,
.stay_selection .rooms_number_wrapper label {
  color: gray;
  font-size: 12px;
}

/* line 83, ../../../../sass/booking/_booking_engine.scss */
.date_box {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  background: white;
  height: 40px;
}
/* line 90, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_day,
.date_box .date_year {
  display: block;
  text-align: center;
}
/* line 96, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_day {
  text-transform: uppercase;
  color: rgba(192, 207, 166, 0.8);
  margin-left: 5px;
  margin-right: 5px;
  font-size: 16px;
  font-weight: bolder;
}
/* line 105, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_year {
  color: white;
  font-size: 12px;
  height: 14px;
  line-height: 14px;
}

/* line 113, ../../../../sass/booking/_booking_engine.scss */
.room {
  clear: both;
  margin-bottom: 5px;
}
/* line 116, ../../../../sass/booking/_booking_engine.scss */
.room .room_title {
  margin-top: 25px;
}
/* line 120, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector {
  margin-right: 10px;
}
/* line 125, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector,
.room .children_selector,
.room .babies_selector {
  float: left;
  width: 80px;
}
/* line 131, ../../../../sass/booking/_booking_engine.scss */
.room .room_title label,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  display: block;
}
/* line 136, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  color: gray;
  font-size: 12px;
}

/* line 145, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button {
  font-family: 'Source Sans Pro', sans-serif;
  position: relative;
  margin-top: 50px;
  text-align: left;
}
/* line 152, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button .promocode_input {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  border: 0;
  padding: 0;
  margin-top: 10px;
  text-align: center;
  color: rgba(192, 207, 166, 0.8);
  width: 170px;
  height: 40px;
  font-size: 16px;
}
/* line 164, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button button {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  color: white;
  border: 0;
  float: right;
  background: rgba(192, 207, 166, 0.8);
  font-size: 16px;
  text-transform: uppercase;
  height: 40px;
  width: 80px;
  margin-top: 10px;
}
/* line 177, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button .spinner_wrapper {
  position: absolute;
  right: 30px;
  bottom: 20px;
}

/* line 184, ../../../../sass/booking/_booking_engine.scss */
.horizontal_engine {
  height: 379px;
  background: white !important;
}

/* line 189, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal {
  width: 600px;
  margin: 0 auto;
  padding-top: 172px;
}
/* line 194, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 199, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form {
  border-left: 1px solid white;
  border-right: 1px solid white;
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
}
/* line 207, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form_title {
  display: none;
}
/* line 211, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form {
  width: 560px;
}
/* line 215, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .destination_wrapper {
  float: left;
}
/* line 219, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .stay_selection {
  float: right;
}
/* line 223, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .room {
  float: right;
}
/* line 227, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .wrapper_booking_button {
  clear: both;
}
/* line 231, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .promocode_input {
  width: 260px !important;
}
/* line 235, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .wrapper_booking_button button {
  width: 170px !important;
}

/* line 241, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline {
  width: 1140px;
  height: 75px;
  margin: 0 auto;
  padding-top: 20px;
  background-color: gray;
}
/* line 248, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 253, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form {
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
  background-color: gray;
}
/* line 260, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form_title {
  display: none;
}
/* line 264, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form {
  width: 1140px;
  padding: 0;
}
/* line 269, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .destination_wrapper {
  float: left;
}
/* line 273, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .stay_selection {
  float: left;
  margin-left: 90px;
}
/* line 277, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .stay_selection .entry_date_wrapper label,
.boking_widget_inline .stay_selection .departure_date_wrapper label,
.boking_widget_inline .stay_selection .rooms_number_wrapper label {
  color: #787878;
  font-size: 12px;
}
/* line 285, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room_list_wrapper {
  float: left;
  margin-left: 20px;
  margin-right: 20px;
  margin-top: 5px;
}
/* line 291, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room {
  float: right;
}
/* line 294, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room .room_title,
.boking_widget_inline .room .adults_selector label,
.boking_widget_inline .room .children_selector label,
.boking_widget_inline .room .babies_selector label {
  color: gray;
  font-size: 12px;
}
/* line 301, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room .room_title {
  text-align: right;
}
/* line 307, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button {
  display: inline;
  margin-top: 10px;
}
/* line 311, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  float: none;
}
/* line 316, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .promocode_input {
  width: 200px !important;
  margin-right: 20px;
  margin-top: 19px;
  background-color: #5a5a5a;
}
/* line 323, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  width: 170px !important;
}
/* line 327, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .selectric, .boking_widget_inline .date_box {
  background: #5a5a5a;
}

/* line 333, ../../../../sass/booking/_booking_engine.scss */
.booking_footer_message {
  margin-top: 65px;
}

/* line 338, ../../../../sass/booking/_booking_engine.scss */
.booking-form-center-text {
  text-align: center;
}

/* line 342, ../../../../sass/booking/_booking_engine.scss */
.babies_selector {
  margin-left: 10px;
}

/* line 346, ../../../../sass/booking/_booking_engine.scss */
.room_title.room_title_with_babies {
  float: none;
  margin-bottom: 5px;
  padding-top: 10px;
  color: rgba(192, 207, 166, 0.8);
  text-align: center;
  width: 100%;
  text-transform: uppercase;
}

/* line 359, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text {
  margin-top: 7px;
}
/* line 362, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .stay_selection {
  margin-left: 45px !important;
}
/* line 366, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .room_title.room_title_with_babies {
  margin-top: -30px;
  text-align: center;
}
/* line 371, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .room_list_wrapper {
  margin-left: 60px;
}

/* line 379, ../../../../sass/booking/_booking_engine.scss */
.range-age {
  width: 55px;
  display: inline-block;
  float: right;
  white-space: nowrap;
}

/* line 387, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 {
  border-radius: 0;
}
/* line 389, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-skin {
  border-radius: 0;
}
/* line 392, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide > * {
  padding: 0 !important;
  background-color: transparent;
}
/* line 396, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide .calendar_popup_wrapper {
  background-color: transparent;
}
/* line 400, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .full_screen_engine .container_popup_booking {
  background-color: transparent !important;
  padding: 24px !important;
}
/* line 405, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  color: gray;
}
/* line 408, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading {
  width: 50px;
  height: 50px;
  margin: auto;
  position: absolute;
  left: 0;
  right: 0;
  top: 100px;
  bottom: 0;
}
/* line 10, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:before {
  content: '';
  width: 50px;
  height: 5px;
  background: #000;
  opacity: 0.3;
  position: absolute;
  top: 59px;
  left: 0;
  border-radius: 50%;
  animation: box_shadow .5s linear infinite;
}
/* line 22, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:after {
  content: '';
  width: 50px;
  height: 50px;
  background: white;
  animation: box_animate .5s linear infinite;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 3px;
}
@keyframes box_animate {
  17% {
    border-bottom-right-radius: 3px;
  }
  25% {
    transform: translateY(9px) rotate(22.5deg);
  }
  50% {
    transform: translateY(18px) scale(1, 0.9) rotate(45deg);
    border-bottom-right-radius: 40px;
  }
  75% {
    transform: translateY(9px) rotate(67.5deg);
  }
  100% {
    transform: translateY(0) rotate(90deg);
  }
}
@keyframes box_shadow {
  0%, 100% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.2, 1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader {
  padding: 60px 0 70px;
}
/* line 3, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot {
  width: 24px;
  height: 24px;
  margin: 0 3px;
  background: #000;
  border-radius: 100%;
  display: inline-block;
  animation: slide 1s infinite;
}
/* line 11, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(1) {
  animation-delay: 0.1s;
}
/* line 14, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(2) {
  animation-delay: 0.2s;
}
/* line 17, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(3) {
  animation-delay: 0.3s;
}
/* line 20, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(4) {
  animation-delay: 0.4s;
}
/* line 23, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(5) {
  animation-delay: 0.5s;
}
@-moz-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-webkit-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-o-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube {
  margin: 20px auto;
  width: 40px;
  height: 40px;
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}
/* line 7, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube {
  float: left;
  width: 50%;
  height: 50%;
  position: relative;
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}
/* line 15, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  -webkit-animation: sk-foldCubeAngle 2.4s infinite linear both;
  animation: sk-foldCubeAngle 2.4s infinite linear both;
  -webkit-transform-origin: 100% 100%;
  -ms-transform-origin: 100% 100%;
  transform-origin: 100% 100%;
}
/* line 30, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2 {
  -webkit-transform: scale(1.1) rotateZ(90deg);
  transform: scale(1.1) rotateZ(90deg);
}
/* line 34, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3 {
  -webkit-transform: scale(1.1) rotateZ(270deg);
  transform: scale(1.1) rotateZ(270deg);
}
/* line 38, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4 {
  -webkit-transform: scale(1.1) rotateZ(180deg);
  transform: scale(1.1) rotateZ(180deg);
}
/* line 43, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2:before {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
/* line 49, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3:before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
}
/* line 55, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4:before {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
}
@-webkit-keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
@keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
/* line 1, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots {
  padding: 40px 0;
  text-align: center;
  animation: dots-rotate 2s linear infinite;
}
/* line 5, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  content: '';
  animation: dots-margin 2s linear infinite;
}
/* line 9, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots span, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  display: inline-block;
  vertical-align: middle;
  width: 30px;
  height: 30px;
  background-color: white;
  border-radius: 50%;
  margin: 5px;
}
@keyframes dots-margin {
  0% {
    margin: 5px;
  }
  12% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  25% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  50% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  62% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  75% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  100% {
    margin: 5px;
  }
}
@keyframes dots-rotate {
  0% {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  25% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  50% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  75% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/* line 417, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_top_popup_booking {
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: lighter;
  color: gray;
}
/* line 425, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  font-weight: bold;
  text-transform: uppercase;
}
/* line 430, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .container_popup_booking {
  width: 555px;
  display: block;
  padding: 30px 0;
  box-sizing: border-box;
  margin: 7px;
  border: 1px solid #3483b2;
}

/* line 441, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine {
  width: auto !important;
  height: auto !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}
/* line 449, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-outer {
  background: transparent;
}
/* line 454, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine.custom_color_overlay .fancybox-skin {
  background: transparent;
}
/* line 459, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-skin {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}
/* line 468, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-inner {
  overflow: visible;
}
/* line 472, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 485, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  border: 0 !important;
}
/* line 489, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: lighter;
}
/* line 492, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking strong {
  font-weight: bold;
  text-decoration: underline;
  font-size: 14px;
}

/* line 1, ../sass/_booking_engine.scss */
.full_width {
  display: table;
  width: 100%;
  position: absolute;
  bottom: 0;
  background: rgba(192, 207, 166, 0.8);
  z-index: 22;
}

/* line 10, ../sass/_booking_engine.scss */
#full_wrapper_booking {
  width: 1140px;
  position: relative;
  z-index: 22;
  left: 0;
  bottom: 0px;
  right: 0;
  padding: 11px 40px 0;
  margin: auto;
  box-sizing: border-box;
}

/* line 22, ../sass/_booking_engine.scss */
#full_wrapper_booking #wrapper_booking {
  position: relative;
  padding: 0;
  width: 970px !important;
}

/* line 28, ../sass/_booking_engine.scss */
#full_wrapper_booking #motor_reserva {
  position: relative !important;
  top: 0px !important;
  color: #5a5a5a;
  font-size: 12px;
  padding: 0px !important;
  margin: 0px;
  width: 100%;
}

/* line 38, ../sass/_booking_engine.scss */
#full_wrapper_booking #fecha_entrada input, #fecha_salida input {
  background: white url(/img/onhol/ico-misreservas.png) no-repeat 114px !important;
  height: 30px !important;
  border: 0 !important;
  border-radius: 25px !important;
  padding-left: 15px;
  font-size: 15px;
  font-weight: 300;
  width: 125px !important;
}
/* line 48, ../sass/_booking_engine.scss */
#full_wrapper_booking #fecha_entrada input::-webkit-input-placeholder, #fecha_salida input::-webkit-input-placeholder {
  color: #7D7D7D;
}
/* line 52, ../sass/_booking_engine.scss */
#full_wrapper_booking #fecha_entrada input:-moz-placeholder, #fecha_salida input:-moz-placeholder {
  /* Firefox 18- */
  color: #7D7D7D;
}
/* line 57, ../sass/_booking_engine.scss */
#full_wrapper_booking #fecha_entrada input::-moz-placeholder, #fecha_salida input::-moz-placeholder {
  /* Firefox 19+ */
  color: #7D7D7D;
}
/* line 62, ../sass/_booking_engine.scss */
#full_wrapper_booking #fecha_entrada input:-ms-input-placeholder, #fecha_salida input:-ms-input-placeholder {
  color: #7D7D7D;
}

/* line 67, ../sass/_booking_engine.scss */
.colocar_fechas {
  margin: 0 0 5px 30px !important;
}

/* line 71, ../sass/_booking_engine.scss */
#fecha_entrada {
  margin-left: 0 !important;
}

/* line 75, ../sass/_booking_engine.scss */
#full_wrapper_booking #contenedor_habitaciones,
#full_wrapper_booking #contenedor_opciones {
  width: auto !important;
  margin: 0 0 0 5px !important;
}

/* line 81, ../sass/_booking_engine.scss */
#full_wrapper_booking #contenedor_fechas {
  width: auto;
}

/* line 85, ../sass/_booking_engine.scss */
#full_wrapper_booking #contenedor_habitaciones {
  width: 130px !important;
  height: auto;
  margin-top: 5px !important;
  margin-left: 30px !important;
}

/* line 92, ../sass/_booking_engine.scss */
#full_wrapper_booking #contenedor_opciones {
  margin-top: 5px !important;
  margin-left: 30px !important;
}

/* line 97, ../sass/_booking_engine.scss */
#full_wrapper_booking #titulo_fecha_entrada, #titulo_fecha_salida {
  float: left;
  margin-right: 10px;
  width: 120px !important;
  margin-top: 10px;
}

/* line 104, ../sass/_booking_engine.scss */
#full_wrapper_booking #booking_engine_title {
  padding: 3px 10px 10px 0;
  font-weight: 500;
  display: block;
  width: 100%;
  box-sizing: border-box;
  text-align: center;
  font-size: 18px;
  text-transform: uppercase;
  color: #D47605;
  font-family: 'Open Sans', sans-serif;
}

/* line 117, ../sass/_booking_engine.scss */
#full_wrapper_booking #booking_title1 {
  display: none;
  text-align: center;
  font-size: 18px;
  color: rgba(192, 207, 166, 0.8);
  text-transform: uppercase;
}

/* line 125, ../sass/_booking_engine.scss */
#full_wrapper_booking #booking_title2 {
  text-align: left;
  font-size: 23px;
  line-height: 30px;
  color: white;
  text-transform: uppercase;
  display: block;
  font-family: "4";
}

/* line 135, ../sass/_booking_engine.scss */
#full_wrapper_booking #best_price {
  text-align: center;
  font-size: 11px;
  color: rgba(192, 207, 166, 0.8);
  text-transform: uppercase;
  display: none;
}

/* line 143, ../sass/_booking_engine.scss */
#full_wrapper_booking #info_ninos {
  font-size: 9px !important;
  top: 3px;
  left: 170px;
  width: 100px;
  display: none;
}

/* line 151, ../sass/_booking_engine.scss */
#full_wrapper_booking #search-button {
  border-radius: 0px !important;
  height: 93px !important;
  width: 93px !important;
  background: url(/img/onhol/boton_search.png);
  color: white;
  margin: auto !important;
  margin-top: -29px !important;
  text-transform: uppercase;
  background-size: contain;
}
/* line 162, ../sass/_booking_engine.scss */
#full_wrapper_booking #search-button:hover {
  opacity: 0.8;
}

/* line 167, ../sass/_booking_engine.scss */
#full_wrapper_booking .spinner {
  text-align: center;
  height: 30px;
  left: 196px !important;
}

/* line 173, ../sass/_booking_engine.scss */
#full_wrapper_booking #envio input {
  width: 130px !important;
  height: 30px !important;
  border: 0px !important;
  border-radius: 25px !important;
  margin: 0px auto 15px !important;
  margin-right: 30px !important;
  text-align: center;
  font-size: 14px;
  font-weight: 300;
  float: left;
}
/* line 185, ../sass/_booking_engine.scss */
#full_wrapper_booking #envio input::-webkit-input-placeholder {
  color: #7D7D7D;
  font-size: 10px;
  padding-top: 2px;
}
/* line 191, ../sass/_booking_engine.scss */
#full_wrapper_booking #envio input:-moz-placeholder {
  /* Firefox 18- */
  color: #7D7D7D;
  font-size: 10px;
  padding-top: 2px;
}
/* line 198, ../sass/_booking_engine.scss */
#full_wrapper_booking #envio input::-moz-placeholder {
  /* Firefox 19+ */
  color: #7D7D7D;
  font-size: 10px;
  padding-top: 2px;
}
/* line 205, ../sass/_booking_engine.scss */
#full_wrapper_booking #envio input:-ms-input-placeholder {
  color: #7D7D7D;
  font-size: 10px;
  padding-top: 2px;
}

/* line 212, ../sass/_booking_engine.scss */
#full_wrapper_booking #envio {
  text-align: center;
  padding-bottom: 10px;
  margin-left: 25px;
  height: auto;
  width: auto;
}

/* line 220, ../sass/_booking_engine.scss */
#full_wrapper_booking #ui-datepicker div {
  z-index: 9999 !important;
}

/* line 224, ../sass/_booking_engine.scss */
#full_wrapper_booking #selector_habitaciones {
  width: 75px !important;
}

/* line 228, ../sass/_booking_engine.scss */
#full_wrapper_booking .adultos {
  margin: 0px 5px 0 0 !important;
}

/* line 232, ../sass/_booking_engine.scss */
#full_wrapper_booking .ninos {
  float: left;
  display: none !important;
  margin: 0px 0 0 12px;
}

/* line 238, ../sass/_booking_engine.scss */
#full_wrapper_booking #ui-datepicker-div {
  z-index: 99999 !important;
}

/* line 242, ../sass/_booking_engine.scss */
#full_wrapper_booking #motor_reserva select {
  width: 137px !important;
  height: 32px !important;
  padding: 5px;
  padding-left: 15px;
  font-size: 15px;
  line-height: 100%;
  border: 0px;
  border-radius: 25px;
  -webkit-appearance: none;
  color: #7D7D7D;
  font-weight: 300;
  margin-bottom: 15px;
  background: white url(/img/onhol/select_down.png) no-repeat 115px !important;
}

/* line 259, ../sass/_booking_engine.scss */
#ui-datepicker-div {
  z-index: 999999 !important;
  background: white;
}

/* line 264, ../sass/_booking_engine.scss */
#motor_reserva label, #motor_reserva p {
  color: white;
}

/* line 268, ../sass/_booking_engine.scss */
#contenedor_fechas {
  padding-top: 0;
}

/* line 272, ../sass/_booking_engine.scss */
#hotel_destino {
  margin-top: 5px;
}

/* line 276, ../sass/_booking_engine.scss */
#contenedor_hotel {
  display: inline-block;
  float: left;
}
/* line 280, ../sass/_booking_engine.scss */
#contenedor_hotel label {
  display: none;
}

/* line 285, ../sass/_booking_engine.scss */
#full_wrapper_booking #motor_reserva select#hotel_destino {
  width: 150px !important;
  background-position: 130px center !important;
}

/* line 290, ../sass/_booking_engine.scss */
#full_wrapper_booking #booking_engine_title.wrapper-old-web-support {
  font-size: 18px;
  color: white;
  text-transform: initial;
  opacity: 1;
  margin-bottom: 6px;
  font-family: "4";
  letter-spacing: 0px;
  margin-top: -26px;
}
/* line 300, ../sass/_booking_engine.scss */
#full_wrapper_booking #booking_engine_title.wrapper-old-web-support .web_support_number {
  color: white;
  font-size: 17px !important;
}

/*======= Slider Container =======*/
/* line 307, ../sass/_booking_engine.scss */
section#slider_container {
  position: relative;
}

/* line 1, ../sass/_booking_widget_modal.scss */
#motor_reserva.modal-booking-widget {
  width: 256px !important;
}

/* line 5, ../sass/_booking_widget_modal.scss */
.modal-booking-widget {
  width: 50px;
}

/* line 9, ../sass/_booking_widget_modal.scss */
.modal-booking-widget {
  width: 50px;
}

/* line 13, ../sass/_booking_widget_modal.scss */
.modal-booking-widget fieldset {
  float: none;
}

/* line 17, ../sass/_booking_widget_modal.scss */
.modal-booking-widget .adultos {
  margin: 0 30px 0 0 !important;
}

/* line 21, ../sass/_booking_widget_modal.scss */
.modal-booking-widget .contenedor_opciones {
  width: 230px;
}

/* line 25, ../sass/_booking_widget_modal.scss */
.modal-booking-widget .adultos select.selector_adultos,
.modal-booking-widget .contenedor_opciones .ninos select,
.modal-booking-widget .contenedor_opciones .numero_personas select {
  width: 100px;
}

/* line 31, ../sass/_booking_widget_modal.scss */
.modal-booking-widget .contenedor_opciones .numero_personas select {
  float: none;
}

/* line 39, ../sass/_booking_widget_modal.scss */
.modal-booking-widget .ninos .info_ninos {
  left: 169px !important;
  width: 80px;
}

/* line 44, ../sass/_booking_widget_modal.scss */
#contenedor_opciones {
  width: 10px !important;
  height: auto;
}

/* line 49, ../sass/_booking_widget_modal.scss */
.fancybox-wrap {
  width: auto !important;
}

/* line 57, ../sass/_booking_widget_modal.scss */
.fancybox-inner {
  overflow: hidden !important;
}

/* line 61, ../sass/_booking_widget_modal.scss */
.booking-widget {
  background-color: white;
}

/* line 65, ../sass/_booking_widget_modal.scss */
.booking-widget fieldset {
  margin: 8px 0 0;
}

/* line 69, ../sass/_booking_widget_modal.scss */
.booking-widget label {
  color: #5a5a5a;
  display: block;
  font-size: 12px;
}

/* line 75, ../sass/_booking_widget_modal.scss */
.booking-widget .numero_habitacion {
  display: none;
}

/* line 79, ../sass/_booking_widget_modal.scss */
.modal-form {
  padding: 12px;
}

/* line 83, ../sass/_booking_widget_modal.scss */
.modal-form .booking_title1 {
  display: none;
}

/* line 87, ../sass/_booking_widget_modal.scss */
.modal-form .booking_title2 {
  text-align: center;
  background-color: rgba(192, 207, 166, 0.8);
  padding-top: 10px;
  font-size: 30px;
  color: white;
}

/* line 95, ../sass/_booking_widget_modal.scss */
.modal-form .best_price {
  text-align: center;
  background-color: rgba(192, 207, 166, 0.8);
  padding-bottom: 10px;
  color: white;
}

/* line 102, ../sass/_booking_widget_modal.scss */
.modal-form .best_price {
  margin-bottom: 20px;
  font-size: 18px;
}

/* line 107, ../sass/_booking_widget_modal.scss */
.modal-form #selector_hotel {
  width: 100%;
}

/* line 111, ../sass/_booking_widget_modal.scss */
.modal-form #hotel_destino {
  width: 100%;
}

/* line 115, ../sass/_booking_widget_modal.scss */
.modal-form .colocar_fechas {
  float: left;
}

/* line 119, ../sass/_booking_widget_modal.scss */
.modal-form .fecha_salida {
  padding-top: 10px;
  clear: both;
}

/* line 124, ../sass/_booking_widget_modal.scss */
.modal-form .fecha_entrada input, .modal-form .fecha_salida input {
  height: 18px;
  width: 91px;
  border: 1px solid #5a5a5a;
  border-radius: 4px;
  cursor: pointer;
  background: #f0f0f0 url(/img/maria/date_icon.jpg) no-repeat 72px;
  background-size: 18px;
  float: right;
}

/* line 135, ../sass/_booking_widget_modal.scss */
.modal-form .contador_noches {
  display: none;
}

/* line 139, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_habitaciones {
  margin-top: 10px;
  margin-bottom: 10px;
}

/* line 144, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_habitaciones select {
  width: 95px !important;
  float: right;
}

/* line 149, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_habitaciones label {
  float: left;
  width: 108px;
}

/* line 154, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_opciones {
  margin-bottom: 10px;
}

/* line 158, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_opciones select {
  float: right;
  width: 92px;
}

/* line 163, ../sass/_booking_widget_modal.scss */
.modal-form .hab1 {
  position: relative;
}

/* line 167, ../sass/_booking_widget_modal.scss */
.modal-form .hab2, .modal-form .hab3 {
  display: none;
}

/* line 171, ../sass/_booking_widget_modal.scss */
.modal-form .numero_habitacion {
  display: none;
}

/* line 175, ../sass/_booking_widget_modal.scss */
.modal-form .adultos {
  float: left;
  margin: 0 15px 0 0;
}

/* line 180, ../sass/_booking_widget_modal.scss */
.modal-form .ninos {
  float: right;
}

/* line 184, ../sass/_booking_widget_modal.scss */
.modal-form .info_ninos {
  position: absolute;
  line-height: 10px;
  text-align: center;
  top: 4px !important;
  left: 155px !important;
  font-size: 9px !important;
}

/* line 193, ../sass/_booking_widget_modal.scss */
.modal-form .envio {
  min-height: 100px;
}

/* line 197, ../sass/_booking_widget_modal.scss */
.modal-form .envio input {
  margin: 0 9px 0 0;
  color: black;
  height: 18px;
  border-radius: 4px;
  border: 1px solid #464646;
  width: 100% !important;
}

/* line 206, ../sass/_booking_widget_modal.scss */
.modal-form .envio button {
  border: none;
  border-radius: 5px;
  font-weight: normal;
  padding: 6px 10px;
  cursor: pointer;
  overflow: visible;
  font-size: 24px;
  display: block;
  margin: 30px auto 0;
  background-color: rgba(192, 207, 166, 0.8);
  color: white;
}

/* line 220, ../sass/_booking_widget_modal.scss */
.modal-form .envio button:hover {
  background: #D47605;
}

/* line 224, ../sass/_booking_widget_modal.scss */
.modal-form .spinner {
  top: 70px !important;
  left: 120px !important;
}

/*=====================================================================
  Selectric
======================================================================*/
/* line 5, ../../../../sass/booking/_selectric.scss */
.selectricWrapper {
  position: relative;
  margin: 0 0 0px;
  width: 80px;
  cursor: pointer;
}

/* line 12, ../../../../sass/booking/_selectric.scss */
.selectricDisabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 23, ../../../../sass/booking/_selectric.scss */
.selectricOpen {
  z-index: 9999;
}

/* line 27, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 31, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

/* line 38, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect select {
  position: absolute;
  left: -100%;
  display: none;
}

/* line 44, ../../../../sass/booking/_selectric.scss */
.selectricInput {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  _font: 0/0 a;
  background: none !important;
}

/* line 60, ../../../../sass/booking/_selectric.scss */
.selectricTempShow {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* line 67, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectric {
  z-index: 9999;
}

/* line 72, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 76, ../../../../sass/booking/_selectric.scss */
.selectric {
  background: white;
  position: relative;
  border-radius: 6px;
  height: 40px;
}

/* line 83, ../../../../sass/booking/_selectric.scss */
.selectric .label {
  display: block;
  overflow: hidden;
  font-size: 16px;
  line-height: 40px;
  color: rgba(192, 207, 166, 0.8);
  text-align: left;
  margin-left: 20px;
}

/* line 93, ../../../../sass/booking/_selectric.scss */
.selectric .button {
  zoom: 1;
  position: absolute;
  font: 0/0 a;
  overflow: hidden;
  margin: auto;
  top: 0;
  right: 2px;
  bottom: 0;
  width: 35px;
  height: 35px;
  border: 0;
  background: rgba(192, 207, 166, 0.8) url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  padding: 0;
}

/* line 109, ../../../../sass/booking/_selectric.scss */
.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */
/* line 115, ../../../../sass/booking/_selectric.scss */
.selectricItems {
  display: none;
  position: absolute;
  overflow: hidden;
  top: 100%;
  left: 0;
  background: #F9F9F9;
  border: 1px solid #CCC;
  z-index: 9998;
  -webkit-box-shadow: 0 0 10px -6px;
  box-shadow: 0 0 10px -6px;
}

/* line 128, ../../../../sass/booking/_selectric.scss */
.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* line 138, ../../../../sass/booking/_selectric.scss */
.selectricItems li {
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 146, ../../../../sass/booking/_selectric.scss */
.selectricItems li:hover {
  background: #F0F0F0;
  color: #444;
}
/* line 151, ../../../../sass/booking/_selectric.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}

/* line 158, ../../../../sass/booking/_selectric.scss */
.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;
  cursor: default;
}

/* line 1, ../../../../sass/news/_news_2.scss */
.new {
  width: 100%;
  background-color: white;
  overflow: hidden;
  margin-top: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid rgba(80, 183, 1, 0.18);
}

/* line 9, ../../../../sass/news/_news_2.scss */
.new:last-child {
  border-bottom: none;
  padding-bottom: 10px;
}

/* line 13, ../../../../sass/news/_news_2.scss */
.photo-container {
  float: left;
  width: 300px;
}
/* line 17, ../../../../sass/news/_news_2.scss */
.photo-container img {
  vertical-align: top;
}

/* line 21, ../../../../sass/news/_news_2.scss */
.block-new-description {
  display: inline-block;
  width: 800px;
  height: 200px;
  padding: 0px 20px;
  position: relative;
}
/* line 28, ../../../../sass/news/_news_2.scss */
.block-new-description .date {
  color: rgba(192, 207, 166, 0.8);
  font-size: 20px;
  margin-bottom: 5px;
  text-align: left;
}
/* line 34, ../../../../sass/news/_news_2.scss */
.block-new-description a {
  color: rgba(192, 207, 166, 0.8);
}
/* line 37, ../../../../sass/news/_news_2.scss */
.block-new-description #news_description p {
  text-align: left;
}
/* line 40, ../../../../sass/news/_news_2.scss */
.block-new-description .news-title {
  text-transform: uppercase;
  font-size: 17px;
  font-weight: 700;
  margin-bottom: 5px;
  color: #4b4b4b;
  display: inline-block;
}
/* line 48, ../../../../sass/news/_news_2.scss */
.block-new-description ul {
  position: absolute;
  bottom: -4px;
}
/* line 52, ../../../../sass/news/_news_2.scss */
.block-new-description ul li {
  display: inline-block;
  vertical-align: middle;
  margin-right: 3px;
}
/* line 57, ../../../../sass/news/_news_2.scss */
.block-new-description ul .read-more-news {
  color: white;
  background: rgba(192, 207, 166, 0.8);
  font-size: 18px;
  font-weight: 100;
  padding: 5px 25px;
  margin-right: 6px;
  margin-top: -3px;
  display: inline-block;
}
/* line 67, ../../../../sass/news/_news_2.scss */
.block-new-description ul .read-more-news:hover {
  background: #D47605;
}

/* line 73, ../../../../sass/news/_news_2.scss */
#new1 {
  margin-top: 20px;
}

@font-face {
  font-family: '1';
  src: url("/static_1/fonts/walkway_ultracondensed/Walkway_UltraCondensed_Bold-webfont.eot");
  src: url("/static_1/fonts/walkway_ultracondensed/Walkway_UltraCondensed_Bold-webfont.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/walkway_ultracondensed/Walkway_UltraCondensed_Bold-webfont.woff2") format("woff2"), url("/static_1/fonts/walkway_ultracondensed/Walkway_UltraCondensed_Bold-webfont.woff") format("woff"), url("/static_1/fonts/walkway_ultracondensed/Walkway_UltraCondensed_Bold-webfont.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: '2';
  src: url("/static_1/fonts/walkway_black/Walkway_Black-webfont.eot");
  src: url("/static_1/fonts/walkway_black/Walkway_Black-webfont.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/walkway_black/Walkway_Black-webfont.woff2") format("woff2"), url("/static_1/fonts/walkway_black/Walkway_Black-webfont.woff") format("woff"), url("/static_1/fonts/walkway_black/Walkway_Black-webfont.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: '3';
  src: url("/static_1/fonts/walkway_condensed/Walkway_Condensed_Bold.eot");
  src: url("/static_1/fonts/walkway_condensed/Walkway_Condensed_Bold.eot?#iefix") format("embedded-opentype"), url("/static_1/fonts/walkway_condensed/Walkway_Condensed_Bold.woff") format("woff"), url("/static_1/fonts/walkway_condensed/Walkway_Condensed_Bold.ttf") format("truetype"), url("/static_1/fonts/walkway_condensed/Walkway_Condensed_Bold.svg#walkway_blackregular") format("svg");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: '4';
  src: url("/static_1/fonts/nexa/nexa-light.otf");
  src: url("/static_1/fonts/nexa/nexa-light.otf?#iefix") format("opentype");
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: '4';
  src: url("/static_1/fonts/nexa/nexa-bold.otf");
  src: url("/static_1/fonts/nexa/nexa-bold.otf?#iefix") format("opentype");
  font-weight: 700;
  font-style: normal;
}
/*=== General ====*/
/* line 2, ../sass/_template_specific.scss */
body {
  font-family: '1';
}
/* line 4, ../sass/_template_specific.scss */
body .aviso_cookie {
  position: fixed;
  top: 10px;
  right: 10px;
  width: 530px;
  height: auto;
  padding: 20px 30px;
  background: rgba(0, 0, 0, 0.8);
}
/* line 12, ../sass/_template_specific.scss */
body .aviso_cookie p {
  padding: 0;
  text-align: left;
  line-height: 20px;
}

/* line 20, ../sass/_template_specific.scss */
strong {
  color: #D47605;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/
/* line 25, ../sass/_template_specific.scss */
.ui-state-default {
  border: 1px solid white !important;
}

/* line 29, ../sass/_template_specific.scss */
.ui-datepicker-title {
  color: white !important;
}

/* line 33, ../sass/_template_specific.scss */
.ui-widget-header {
  background: rgba(192, 207, 166, 0.8) !important;
}

/* line 37, ../sass/_template_specific.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: rgba(192, 207, 166, 0.8) !important;
  color: white;
}

/* line 43, ../sass/_template_specific.scss */
.datepicker_wrapper_element .ui-state-default, .datepicker_wrapper_element .ui-widget-content .ui-state-default, .datepicker_wrapper_element .ui-widget-header .ui-state-default {
  background: none !important;
}

/* line 48, ../sass/_template_specific.scss */
.lb-cancel {
  background: url(/img/onhol/loading_onhotel.gif) no-repeat !important;
}

/* line 52, ../sass/_template_specific.scss */
hr.divider {
  margin: 50px 0;
  border: 1px solid #DDDDDD;
  border-bottom-width: 0;
}

/*===== Header ====*/
/* line 63, ../sass/_template_specific.scss */
header {
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 22;
}
/* line 70, ../sass/_template_specific.scss */
header #main-sections .book_button {
  display: none !important;
}

/* line 76, ../sass/_template_specific.scss */
.wrapper_header_move {
  min-width: 1140px;
}

/* line 80, ../sass/_template_specific.scss */
div#wrapper_header_top {
  background: rgba(192, 207, 166, 0.8);
  height: 35px;
}

/* line 85, ../sass/_template_specific.scss */
div#wrapper_header_bottom {
  background: rgba(255, 255, 255, 0.8);
  height: 35px;
}

/* line 91, ../sass/_template_specific.scss */
ul#main-sections-inner {
  text-align: justify;
  justify-content: space-between;
}
/* line 95, ../sass/_template_specific.scss */
ul#main-sections-inner:after {
  content: "";
  display: inline-block;
  height: 0;
  width: 100%;
}
/* line 102, ../sass/_template_specific.scss */
ul#main-sections-inner li.main-section-div-wrapper {
  display: inline-block;
  text-align: center;
  font-family: '1';
  font-size: 20px;
  text-transform: uppercase;
  letter-spacing: 2px;
  cursor: pointer;
  padding-top: 8px;
  position: relative;
}
/* line 113, ../sass/_template_specific.scss */
ul#main-sections-inner li.main-section-div-wrapper a:hover {
  color: #D47605;
}
/* line 117, ../sass/_template_specific.scss */
ul#main-sections-inner li.main-section-div-wrapper a.special_button_main_div:hover, ul#main-sections-inner li.main-section-div-wrapper a.news_in_header:hover {
  opacity: 0.8;
  color: white;
}
/* line 122, ../sass/_template_specific.scss */
ul#main-sections-inner li.main-section-div-wrapper a {
  text-decoration: none;
  color: white;
  cursor: pointer;
}
/* line 128, ../sass/_template_specific.scss */
ul#main-sections-inner li.main-section-div-wrapper ul {
  display: none;
  position: absolute;
  top: 35px;
  z-index: 2;
}
/* line 134, ../sass/_template_specific.scss */
ul#main-sections-inner li.main-section-div-wrapper ul li {
  padding: 5px 15px;
  background: #bed0a6;
}
/* line 138, ../sass/_template_specific.scss */
ul#main-sections-inner li.main-section-div-wrapper ul li:hover {
  background: #D7E6DA;
}
/* line 142, ../sass/_template_specific.scss */
ul#main-sections-inner li.main-section-div-wrapper ul li a {
  color: white;
  white-space: nowrap;
}
/* line 150, ../sass/_template_specific.scss */
ul#main-sections-inner li.main-section-div-wrapper#section-active a {
  color: #D47605;
}
/* line 154, ../sass/_template_specific.scss */
ul#main-sections-inner li.main-section-div-wrapper#section-active a.special_button_main_div, ul#main-sections-inner li.main-section-div-wrapper#section-active a.news_in_header {
  opacity: 0.8;
  color: white;
}
/* line 161, ../sass/_template_specific.scss */
ul#main-sections-inner li.main-section-div-wrapper.sorteo a {
  background: #D47605 !important;
  padding: 5px;
}

/* line 167, ../sass/_template_specific.scss */
.special_button_main_div {
  color: white;
  background: #D47605;
  padding: 3px;
  width: 65px;
  display: inline-block;
}

/* line 176, ../sass/_template_specific.scss */
ul#main-sections-inner li.main-section-div-wrapper a.news_in_header {
  color: #D47605;
  padding: 3px 5px;
  display: inline-block;
}
/* line 180, ../sass/_template_specific.scss */
ul#main-sections-inner li.main-section-div-wrapper a.news_in_header:hover {
  color: white;
}

/* line 185, ../sass/_template_specific.scss */
ul#main-sections-inner li.main-section-div-wrapper.sorteo_section {
  padding: 8px 11px 5px;
  background: #D47605;
}

/* line 190, ../sass/_template_specific.scss */
.phone_hotel {
  text-transform: uppercase;
  margin-top: 8px;
  font-size: 20px;
  letter-spacing: 2px;
  color: #4E4E4E;
  vertical-align: top;
  display: inline-block;
}
/* line 199, ../sass/_template_specific.scss */
.phone_hotel span {
  color: #D47605;
}

/* line 204, ../sass/_template_specific.scss */
#wifi_information {
  font-size: 16px;
  font-family: "4";
  letter-spacing: 2px;
  width: 670px;
  text-align: center;
}

/* line 213, ../sass/_template_specific.scss */
.wifi_wrapper {
  display: inline-block;
  vertical-align: top;
}
/* line 217, ../sass/_template_specific.scss */
.wifi_wrapper .separator {
  margin: 0 10px;
  margin-top: 6px;
  color: #BDBDBD;
  font-size: 22px;
  display: inline-block;
}
/* line 225, ../sass/_template_specific.scss */
.wifi_wrapper .wifi_text {
  display: inline-block;
  vertical-align: top;
  margin-top: 7px;
  cursor: pointer;
}
/* line 231, ../sass/_template_specific.scss */
.wifi_wrapper .wifi_text:hover {
  opacity: .8;
}
/* line 236, ../sass/_template_specific.scss */
.wifi_wrapper img {
  display: inline-block;
  width: 27px;
}
/* line 241, ../sass/_template_specific.scss */
.wifi_wrapper span {
  font-size: 20px;
  letter-spacing: 2px;
  vertical-align: top;
  margin-top: 8px;
  margin-left: 10px;
}
/* line 248, ../sass/_template_specific.scss */
.wifi_wrapper span.plus {
  color: #D47605;
}

/* line 254, ../sass/_template_specific.scss */
div#lang {
  display: inline-block;
  margin-top: 8px;
  margin-left: 15px;
}
/* line 258, ../sass/_template_specific.scss */
div#lang a {
  display: inline-table;
  margin-right: 3px;
}

/* line 264, ../sass/_template_specific.scss */
div#social {
  float: right;
}
/* line 266, ../sass/_template_specific.scss */
div#social a {
  text-decoration: none;
  margin: 0 3px;
  padding-top: 8px;
  display: inline-block;
}
/* line 272, ../sass/_template_specific.scss */
div#social a:hover {
  opacity: 0.8;
}

/* line 278, ../sass/_template_specific.scss */
div#top-sections {
  margin-top: 2px;
  float: right;
}
/* line 282, ../sass/_template_specific.scss */
div#top-sections a {
  text-decoration: none;
  cursor: pointer;
}
/* line 286, ../sass/_template_specific.scss */
div#top-sections a .top_image {
  display: inline-block;
  vertical-align: top;
  margin-top: 3px;
  margin-right: 6px;
}
/* line 293, ../sass/_template_specific.scss */
div#top-sections a:hover {
  opacity: 0.8;
}
/* line 297, ../sass/_template_specific.scss */
div#top-sections a span {
  color: #4E4E4E;
  font-size: 20px;
  letter-spacing: 2px;
  display: inline-block;
  margin-top: 6px;
}
/* line 306, ../sass/_template_specific.scss */
div#top-sections span.separator {
  margin: 0 10px;
  margin-top: 4px;
  color: #BDBDBD;
  font-size: 22px;
}
/* line 313, ../sass/_template_specific.scss */
div#top-sections img.top_image {
  margin-right: 6px;
  vertical-align: top;
  padding-top: 2px;
}

/* line 320, ../sass/_template_specific.scss */
.whatsapp-img {
  float: right;
  margin-top: 5px;
  cursor: pointer;
}
/* line 325, ../sass/_template_specific.scss */
.whatsapp-img span.separator {
  margin: 0 10px;
  margin-top: 4px;
  color: #BDBDBD;
  font-size: 22px;
}
/* line 332, ../sass/_template_specific.scss */
.whatsapp-img img {
  vertical-align: bottom;
}

/* line 337, ../sass/_template_specific.scss */
.fidelion_image_wrapper {
  float: right;
  background: rgba(255, 255, 255, 0.8);
  padding: 10px 10px 10px;
  border-radius: 0 0 110px 110px;
  position: relative;
}
/* line 344, ../sass/_template_specific.scss */
.fidelion_image_wrapper img, .fidelion_image_wrapper div {
  width: 125px;
  height: 125px;
  background-size: 857px;
  background-position: -348px -128px;
}
/* line 351, ../sass/_template_specific.scss */
.fidelion_image_wrapper .sprite, .fidelion_image_wrapper .mute_video {
  background: url(/img/onhol/logo-clubON-web.png?v=2.1) no-repeat;
  background-position: -23px 3px;
  background-size: 185px;
}

/* line 358, ../sass/_template_specific.scss */
.present-on-image {
  /*margin-left: 57px;
  margin-top: 15px;
  width: 150px;
  top: 0px;
  height: 145px;
  float: left;
  text-align: center;*/
  padding: 10px 10px 10px 0px;
  position: absolute;
  border-radius: 0 0 110px 110px;
  left: -280px;
  bottom: 0;
}

/* line 373, ../sass/_template_specific.scss */
li.main-section-div-wrapper.book_button {
  color: #D47605;
  cursor: pointer;
}

/* line 378, ../sass/_template_specific.scss */
#logoDiv {
  margin-left: 0px;
  width: 210px;
  margin-top: 25px;
}

/*==== Booking Widget ====*/
/* line 385, ../sass/_template_specific.scss */
label#titulo_fecha_entrada, label#titulo_fecha_salida {
  display: none;
}

/* line 389, ../sass/_template_specific.scss */
#contenedor_habitaciones > label {
  display: none;
}

/* line 394, ../sass/_template_specific.scss */
.adultos.numero_personas > label {
  display: none !important;
}

/* line 399, ../sass/_template_specific.scss */
#titulo_ninos {
  display: none !important;
}

/* line 403, ../sass/_template_specific.scss */
#booking fieldset {
  margin: 5px 0 0;
}

/* line 407, ../sass/_template_specific.scss */
#search-button {
  font-size: 12px;
}

/* line 411, ../sass/_template_specific.scss */
ul.ticks_elements {
  text-align: justify;
  width: 640px;
  justify-content: space-between;
  margin-top: 25px;
}
/* line 417, ../sass/_template_specific.scss */
ul.ticks_elements:after {
  display: inline-block;
  width: 100%;
  height: 0;
}
/* line 423, ../sass/_template_specific.scss */
ul.ticks_elements li {
  display: inline-block;
  text-align: center;
  font-size: 14px;
  color: white;
}
/* line 429, ../sass/_template_specific.scss */
ul.ticks_elements li img {
  vertical-align: middle;
}

/* line 435, ../sass/_template_specific.scss */
div#full_wrapper_booking.move_option {
  position: relative;
  bottom: 0;
  background: none;
}

/* line 441, ../sass/_template_specific.scss */
.full_width.showed {
  position: fixed;
  width: 100%;
  bottom: auto;
  top: 75px;
  display: none;
  background: rgba(0, 0, 0, 0.8);
  z-index: 22;
}

/*====== Slider =====*/
/* line 453, ../sass/_template_specific.scss */
section#slider_container .tp-bullets {
  display: none !important;
}
/* line 457, ../sass/_template_specific.scss */
section#slider_container div#logoDiv {
  position: absolute;
  top: 95px;
  left: 0;
  right: 0;
  margin: auto;
  width: 210px;
  z-index: 50;
}

/* line 468, ../sass/_template_specific.scss */
.mute_video {
  position: absolute;
  left: 12%;
  bottom: 174px;
  cursor: pointer;
}

/*====== Section Images ======*/
/* line 487, ../sass/_template_specific.scss */
.selec_section_images_wrapper {
  padding: 75px 0;
  border-bottom: 1px solid #E8E8E8;
}
/* line 491, ../sass/_template_specific.scss */
.selec_section_images_wrapper .lane_select_images {
  display: table;
  margin: auto;
}
/* line 496, ../sass/_template_specific.scss */
.selec_section_images_wrapper .select_image_element {
  display: inline-block;
  margin: 0 30px 20px;
  -webkit-border-radius: 99px;
  -moz-border-radius: 99px;
  border-radius: 99px;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  border: 1px solid transparent;
}
/* line 509, ../sass/_template_specific.scss */
.selec_section_images_wrapper .select_image_element:hover {
  border: 1px solid #D47605;
}
/* line 513, ../sass/_template_specific.scss */
.selec_section_images_wrapper .lane_select_images:last-child .select_image_element {
  margin: 0 30px 0px;
}

/* line 518, ../sass/_template_specific.scss */
.selec_section_images_wrapper.bottom_inner_images {
  border-bottom: 0;
  border-top: 1px solid #E8E8E8;
  margin-top: 55px;
}

/*====== Content Subtitle =======*/
/* line 525, ../sass/_template_specific.scss */
.content_subtitle_wrapper {
  background: url(/img/onhol/marca-agua.png?v=1) no-repeat center center;
  padding: 58px 0;
  background-size: 650px;
  margin: 30px 0;
}
/* line 531, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_title {
  text-align: center;
  font-size: 25px;
  font-family: "4";
  color: #D47605;
  margin-bottom: 45px;
}
/* line 539, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_description {
  width: 725px;
  margin: auto;
  font-family: "4";
  color: gray;
  text-align: justify;
  line-height: 30px;
}

/*===== Fidelion Footer =====*/
/* line 550, ../sass/_template_specific.scss */
.fidelion_footer_wrapper {
  background: #C0CFA6;
}
/* line 553, ../sass/_template_specific.scss */
.fidelion_footer_wrapper .fidelion_background {
  background-attachment: fixed !important;
  position: relative;
}
/* line 558, ../sass/_template_specific.scss */
.fidelion_footer_wrapper h2.fidelion_title {
  font-family: "4";
  margin-bottom: 24px;
  font-size: 24px;
  color: white;
  width: 600px;
}
/* line 565, ../sass/_template_specific.scss */
.fidelion_footer_wrapper h2.fidelion_title img.fidelion_title_image {
  vertical-align: middle;
  margin-right: 15px;
}
/* line 571, ../sass/_template_specific.scss */
.fidelion_footer_wrapper .fidelion_description {
  font-family: "4";
  line-height: 28px;
  color: white;
  text-align: justify;
}
/* line 577, ../sass/_template_specific.scss */
.fidelion_footer_wrapper .fidelion_description strong {
  color: #D47605;
}

/* line 583, ../sass/_template_specific.scss */
.fidelion_left_block {
  width: 50%;
  float: left;
  padding: 35px 0;
}

/* line 589, ../sass/_template_specific.scss */
.fidelion_right_block {
  width: 50%;
  float: right;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  height: 150px;
}

/* line 600, ../sass/_template_specific.scss */
a.button-fidelion {
  border-radius: 0px !important;
  width: 150px;
  background: url(/img/onhol/boton_search_2.png?v=1) no-repeat;
  color: white;
  margin: auto !important;
  text-transform: uppercase;
  background-size: contain;
  text-align: center;
  display: block;
  padding: 70px 0;
  font-family: "4";
  text-decoration: none;
  font-size: 17px;
}

/*=== Menu footer ====*/
/* line 617, ../sass/_template_specific.scss */
.menu_footer_blocks_wrapper {
  padding: 60px 0;
  display: table;
  width: auto;
}
/* line 622, ../sass/_template_specific.scss */
.menu_footer_blocks_wrapper h3.menu_title {
  text-align: center;
  font-size: 29px;
  color: #D47605;
  font-family: "4";
  margin-bottom: 20px;
}
/* line 630, ../sass/_template_specific.scss */
.menu_footer_blocks_wrapper a {
  display: inline-block;
  text-decoration: none;
  text-align: center;
  color: gray;
  font-family: "4";
  font-size: 15px;
  line-height: 28px;
  padding: 0 20px;
  padding: 3px 20px 0px 20px;
  background-color: #c0cfa6;
  color: white;
  border-radius: 25px;
}
/* line 644, ../sass/_template_specific.scss */
.menu_footer_blocks_wrapper a:hover {
  background-color: #8a9968 !important;
}
/* line 647, ../sass/_template_specific.scss */
.menu_footer_blocks_wrapper a:first-of-type {
  background-color: #D47605 !important;
}

/*====== Location Footer =====*/
/* line 654, ../sass/_template_specific.scss */
.location_footer_wrapper {
  background: #F7F7F7;
  padding: 40px 0;
}
/* line 658, ../sass/_template_specific.scss */
.location_footer_wrapper .location_map {
  width: 60%;
  float: left;
}
/* line 662, ../sass/_template_specific.scss */
.location_footer_wrapper .location_map img {
  width: 100%;
}
/* line 667, ../sass/_template_specific.scss */
.location_footer_wrapper .info_location_footer {
  width: 31%;
  float: right;
  text-align: right;
  position: absolute;
  right: 0;
  height: 235px;
  top: 0;
  bottom: 0;
  margin: auto;
}
/* line 680, ../sass/_template_specific.scss */
.location_footer_wrapper h3.location_title {
  text-align: right;
  font-size: 29px;
  color: #D47605;
  font-family: "4";
  margin-bottom: 10px;
}
/* line 688, ../sass/_template_specific.scss */
.location_footer_wrapper .location_description {
  display: block;
  text-align: right;
  color: gray;
  font-family: "4";
  font-size: 15px;
  line-height: 28px;
}
/* line 696, ../sass/_template_specific.scss */
.location_footer_wrapper .location_description a {
  font-weight: 700;
  text-decoration: none;
  color: gray;
}

/* line 704, ../sass/_template_specific.scss */
.location_footer_center {
  position: relative;
}

/* line 708, ../sass/_template_specific.scss */
.whatsapp-footer {
  float: left;
  margin-top: 80px;
  margin-left: -20px;
}

/*=== Footer ====*/
/* line 715, ../sass/_template_specific.scss */
footer {
  background: #AFC280;
  padding: 30px 0;
}
/* line 719, ../sass/_template_specific.scss */
footer .social_like_wrapper {
  position: relative;
}
/* line 723, ../sass/_template_specific.scss */
footer div#facebook_like {
  width: 49%;
  float: left;
  margin-top: 2px;
  text-align: right;
}
/* line 729, ../sass/_template_specific.scss */
footer div#google_plus_one {
  width: 49%;
  float: right;
}
/* line 734, ../sass/_template_specific.scss */
footer .full-copyright {
  text-align: center;
}
/* line 738, ../sass/_template_specific.scss */
footer .footer-copyright {
  color: white;
}
/* line 741, ../sass/_template_specific.scss */
footer .footer-copyright a {
  font-family: "4";
  color: white;
  text-decoration: none;
}
/* line 748, ../sass/_template_specific.scss */
footer div#div-txt-copyright {
  margin-top: 6px;
  font-family: "4";
}

/*==== Header to deploy ====*/
/* line 755, ../sass/_template_specific.scss */
.hidden_top_menu {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 101;
  box-shadow: 0px -2px 10px black;
}
/* line 762, ../sass/_template_specific.scss */
.hidden_top_menu #wrapper_header_bottom {
  background: white;
}
/* line 766, ../sass/_template_specific.scss */
.hidden_top_menu #wrapper_header_top {
  background: #C0CFA6;
}
/* line 770, ../sass/_template_specific.scss */
.hidden_top_menu .fidelion_image_wrapper, .hidden_top_menu .present-on-image, .hidden_top_menu #logoDiv, .hidden_top_menu .cookie_free_wrapper {
  display: none;
}

/* line 775, ../sass/_template_specific.scss */
.hide_booking {
  display: none;
  background: #C0CFA6;
}

/*============== Pop-up Fidelius ============*/
/* line 781, ../sass/_template_specific.scss */
.bottom_popup {
  position: fixed;
  width: 100%;
  height: 60px;
  background: rgba(192, 207, 166, 0.8);
  left: 0;
  bottom: 0;
  z-index: 1000;
}

/* line 791, ../sass/_template_specific.scss */
.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

/* line 797, ../sass/_template_specific.scss */
.bottom_popup .bottom_popup_text {
  width: 450px;
  float: left;
  color: white;
  padding: 10px;
  font-family: Arial, sans-serif;
  font-size: 14px;
  margin-left: 20px;
}

/* line 807, ../sass/_template_specific.scss */
.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

/* line 811, ../sass/_template_specific.scss */
.close_button {
  float: right;
  cursor: pointer;
}

/* line 816, ../sass/_template_specific.scss */
button.bottom_popup_button {
  width: 120px;
  background: #bebebe;
  border: 0;
  height: 36px;
  position: absolute;
  color: white;
  background-position: center;
  border-radius: 5px;
  cursor: pointer;
  bottom: 12px;
  font-size: 16px;
}

/* line 831, ../sass/_template_specific.scss */
#wrapper2 {
  width: 1140px;
  margin: 0 auto;
}

/* line 836, ../sass/_template_specific.scss */
.popup_inicial_fidelion {
  width: 100%;
  height: 100%;
  background-size: cover !important;
}
/* line 840, ../sass/_template_specific.scss */
.popup_inicial_fidelion .email, .popup_inicial_fidelion .discount, .popup_inicial_fidelion .compra {
  text-align: center;
}
/* line 843, ../sass/_template_specific.scss */
.popup_inicial_fidelion .compra {
  padding-top: 5px;
  color: white;
  font-size: 22px;
  font-weight: lighter;
  font-family: "4";
}
/* line 850, ../sass/_template_specific.scss */
.popup_inicial_fidelion .discount {
  padding-top: 7px;
  color: white;
  font-size: 47px;
  text-shadow: 3px 3px black;
  text-transform: uppercase;
  font-family: "2";
}
/* line 858, ../sass/_template_specific.scss */
.popup_inicial_fidelion .email {
  padding-top: 39px;
  color: white;
  font-size: 22px;
  font-weight: lighter;
  font-family: "4";
  height: 120px;
  box-sizing: border-box;
  margin: auto;
  position: absolute;
  width: 100%;
  top: 0;
  bottom: 220px;
}
/* line 872, ../sass/_template_specific.scss */
.popup_inicial_fidelion form.form_popup {
  text-align: center;
  padding-top: 50px;
  height: 120px;
  box-sizing: border-box;
  margin: auto;
  position: absolute;
  width: 100%;
  top: 0;
  bottom: 0;
}
/* line 883, ../sass/_template_specific.scss */
.popup_inicial_fidelion form.form_popup li {
  text-align: center;
}
/* line 886, ../sass/_template_specific.scss */
.popup_inicial_fidelion form.form_popup input#id_email {
  height: 26px;
  text-align: center;
  width: 270px;
  font-size: 17px;
  box-shadow: 2px 2px black;
  border: 0px;
  color: black;
}
/* line 895, ../sass/_template_specific.scss */
.popup_inicial_fidelion form.form_popup button.popup_button_onhotel {
  margin: 7px auto 0px;
  width: 271px;
  height: 32px;
  background: #C0CFA6;
  font-size: 17px;
  border: 0px;
  text-transform: uppercase;
  color: white;
  cursor: pointer;
  box-shadow: 2px 2px black;
}
/* line 908, ../sass/_template_specific.scss */
.popup_inicial_fidelion .spinner_wrapper_faldon {
  padding-top: 20px;
}
/* line 911, ../sass/_template_specific.scss */
.popup_inicial_fidelion .popup_message {
  color: white;
  padding-top: 25px;
  font-size: 20px;
  font-weight: lighter;
}

/*===== Habitaciones =====*/
/* line 920, ../sass/_template_specific.scss */
h2.rooms_section_title {
  margin-top: 75px;
  text-align: center;
  font-size: 23px;
  font-family: "4";
  margin-bottom: 57px;
  color: #D47605;
}

/* line 929, ../sass/_template_specific.scss */
.rooms_elements {
  margin-bottom: 20px;
  position: relative;
}
/* line 933, ../sass/_template_specific.scss */
.rooms_elements span.room_ico {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(212, 118, 5, 0.75);
  color: white;
  font-size: 12px;
  font-family: '4';
  padding: 10px 25px;
  line-height: 20px;
  text-transform: uppercase;
  border-radius: 25px;
}
/* line 947, ../sass/_template_specific.scss */
.rooms_elements img.room_images {
  min-width: 100%;
}
/* line 951, ../sass/_template_specific.scss */
.rooms_elements .exceded {
  height: 300px;
  overflow: hidden;
}
/* line 956, ../sass/_template_specific.scss */
.rooms_elements .room_text_wrapper {
  padding: 20px 30px;
  background: #F7F7F7;
}
/* line 961, ../sass/_template_specific.scss */
.rooms_elements h3.room_title {
  text-align: center;
  font-family: "4";
  font-size: 25px;
  color: rgba(192, 207, 166, 0.8);
}
/* line 968, ../sass/_template_specific.scss */
.rooms_elements .room_description {
  text-align: center;
  font-family: "4";
  margin-top: 15px;
  color: gray;
  line-height: 29px;
  overflow: hidden;
}
/* line 982, ../sass/_template_specific.scss */
.rooms_elements .buttons_wrapper_rooms {
  text-align: center;
  margin-top: 21px;
}
/* line 986, ../sass/_template_specific.scss */
.rooms_elements .buttons_wrapper_rooms a.see_more_room, .rooms_elements .buttons_wrapper_rooms span.button-promotion {
  padding: 13px 20px;
  background: rgba(192, 207, 166, 0.8);
  text-transform: uppercase;
  color: white;
  text-decoration: none;
  font-family: "4";
  font-size: 12px;
  font-weight: 700;
  cursor: pointer;
  width: 150px;
  height: 35px;
  display: inline-block;
  box-sizing: border-box;
  border-radius: 25px;
}
/* line 1002, ../sass/_template_specific.scss */
.rooms_elements .buttons_wrapper_rooms a.see_more_room:hover, .rooms_elements .buttons_wrapper_rooms span.button-promotion:hover {
  opacity: 0.8;
}
/* line 1007, ../sass/_template_specific.scss */
.rooms_elements .buttons_wrapper_rooms span.button-promotion {
  background: #D47605;
}

/* line 1013, ../sass/_template_specific.scss */
.room_row {
  display: table;
}

/* line 1017, ../sass/_template_specific.scss */
.room_hide {
  padding: 20px;
}
/* line 1020, ../sass/_template_specific.scss */
.room_hide h3.room_title {
  text-align: center;
  font-family: "3";
  font-size: 25px;
  letter-spacing: 2px;
  color: rgba(192, 207, 166, 0.8);
}
/* line 1028, ../sass/_template_specific.scss */
.room_hide .room_description {
  text-align: center;
  font-family: "4";
  line-height: 30px;
  margin-top: 15px;
  color: gray;
  line-height: 29px;
}

/*======== Ofertas =======*/
/* line 1039, ../sass/_template_specific.scss */
.offers_wrapper {
  background: white;
}
/* line 1042, ../sass/_template_specific.scss */
.offers_wrapper .cycle_element .cycle_text_wrapper {
  background: #F5F6F8;
}

/* line 1047, ../sass/_template_specific.scss */
.cycle_banners_wrapper {
  padding: 57px 0;
}
/* line 1050, ../sass/_template_specific.scss */
.cycle_banners_wrapper .slides > li {
  display: table;
  width: 100%;
  position: relative;
}
/* line 1056, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element {
  display: table;
  position: relative;
  width: 100%;
}
/* line 1061, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .exceded {
  width: 50%;
  float: left;
  height: 559px;
  overflow: hidden;
}
/* line 1067, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .exceded img {
  width: 100%;
}
/* line 1072, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper {
  width: 50%;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  background: #F7F7F7;
}
/* line 1080, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_title {
  color: rgba(192, 207, 166, 0.8);
  font-weight: 100;
  margin-bottom: 35px;
  font-family: "4";
  font-size: 23px;
}
/* line 1087, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_title strong {
  font-weight: bolder;
}
/* line 1092, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description {
  font-size: 15px;
  color: #757881;
  line-height: 28px;
  font-family: "4";
}
/* line 1098, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description strong {
  font-weight: bolder;
}
/* line 1102, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description a, .cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description span.button-promotion {
  text-decoration: none;
  color: white;
  font-size: 16px;
  font-style: italic;
  margin-top: 26px;
  cursor: pointer;
  background: url(/img/onhol/boton_search_green.png) no-repeat center;
  background-size: contain;
  width: 110px;
  height: 95px;
  padding: 35px 0;
  display: inline-block;
  box-sizing: border-box;
  text-align: center;
}
/* line 1122, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description span.button-promotion {
  background: url(/img/onhol/boton_search.png?v=1) no-repeat center;
  background-size: contain;
  width: 103px;
  height: 95px;
  display: inline-block;
  position: relative;
  padding: 35px 0;
  box-sizing: border-box;
}
/* line 1134, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .center_div {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  display: table;
  text-align: left;
  padding: 0 75px;
}
/* line 1145, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle {
  position: absolute;
  bottom: 80px;
  right: 0px;
  width: 50%;
  text-align: left;
  z-index: 2;
  padding: 0 75px;
  box-sizing: border-box;
}
/* line 1155, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle li {
  display: inline-table;
  margin: 0 3px;
}
/* line 1160, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle span.bottom_lane {
  height: 4px;
  width: 60px;
  background: rgba(192, 207, 166, 0.8);
  display: block;
  opacity: 0.6;
  cursor: pointer;
}
/* line 1168, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle span.bottom_lane.flex-active {
  opacity: 1;
}
/* line 1175, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element.align_right .exceded {
  float: right;
}
/* line 1179, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element.align_right .cycle_text_wrapper {
  right: auto;
  left: 0;
}
/* line 1184, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element.align_right ol.flex-controlador-cycle {
  right: auto;
  left: 0;
}

/*======= Galeria ======*/
/* line 1193, ../sass/_template_specific.scss */
h3.gallery_filt_title {
  margin-top: 75px;
  text-align: left;
  font-size: 23px;
  font-family: "4";
  color: #D47605;
}

/* line 1201, ../sass/_template_specific.scss */
ul.gallery_1 {
  width: 100%;
}
/* line 1204, ../sass/_template_specific.scss */
ul.gallery_1 li .crop {
  position: relative;
  width: 100%;
  height: 100%;
}
/* line 1208, ../sass/_template_specific.scss */
ul.gallery_1 li .crop img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  max-width: initial;
}

/* line 1215, ../sass/_template_specific.scss */
#video_corporativo {
  position: relative;
}
/* line 1218, ../sass/_template_specific.scss */
#video_corporativo img.video_default {
  position: absolute;
  top: 0;
  left: 0;
  min-height: 212px;
  max-width: none;
  min-width: 100%;
}

/* line 1228, ../sass/_template_specific.scss */
img.video-ico {
  position: absolute;
  z-index: 10;
  top: 40%;
  left: 40%;
}

/* line 1235, ../sass/_template_specific.scss */
.wrapper_filt .gallery_1 li.video-li {
  width: 559px !important;
  height: auto !important;
  overflow: hidden;
  padding: 4px;
  cursor: pointer;
}

/*========= Location and Contact ======*/
/* line 1245, ../sass/_template_specific.scss */
.page-localizacion #wrapper_content, .page-localizacion .how-to {
  background: rgba(252, 241, 235, 0.86);
  padding: 20px;
  margin-top: 200px;
  width: 1100px;
}
/* line 1252, ../sass/_template_specific.scss */
.page-localizacion .container12 .column6 {
  width: 530px;
}

/* line 1257, ../sass/_template_specific.scss */
.location-info-and-form-wrapper {
  background: rgba(226, 226, 226, 0.46);
  display: table;
  padding-top: 30px;
  padding-bottom: 20px;
  margin-bottom: 20px;
  margin-top: 40px;
}

/* line 1266, ../sass/_template_specific.scss */
.location-info-and-form-wrapper h1 {
  text-align: left;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  padding-bottom: 6px;
  width: 95%;
  line-height: 20px;
  text-transform: uppercase;
  color: #D47605;
  font-size: 23px;
  font-family: '4';
}

/* line 1280, ../sass/_template_specific.scss */
.location-info strong {
  font-weight: bold;
}

/* line 1284, ../sass/_template_specific.scss */
#location-description-intro {
  margin-bottom: 30px;
}

/* line 1288, ../sass/_template_specific.scss */
.iframe-google-maps-wrapper {
  margin-top: 40px;
  width: 100%;
  margin-bottom: 40px;
}

/* line 1296, ../sass/_template_specific.scss */
.form-contact #title {
  display: none !important;
}

/* line 1300, ../sass/_template_specific.scss */
.form-contact #google-plus {
  display: none !important;
}

/* line 1304, ../sass/_template_specific.scss */
.form-contact .fb-like {
  display: none !important;
}

/* line 1308, ../sass/_template_specific.scss */
.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

/* line 1313, ../sass/_template_specific.scss */
.form-contact #contactContent .bordeInput {
  width: auto;
  margin-right: 5px;
  margin-top: 25px;
}

/* line 1319, ../sass/_template_specific.scss */
.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  font-weight: lighter;
  font-size: 15px;
  line-height: 12px;
  color: gray;
}

/* line 1329, ../sass/_template_specific.scss */
.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 495px;
  border: 0px !important;
  height: 30px;
  background-color: white;
  color: #D47605;
}

/* line 1339, ../sass/_template_specific.scss */
.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 495px;
  border: 0px;
  background-color: white;
  color: #D47605;
  margin-right: 35px;
}

/* line 1349, ../sass/_template_specific.scss */
.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 35px;
}

/* line 1354, ../sass/_template_specific.scss */
.form-contact #contact-button {
  width: auto !important;
  background: #D47605 !important;
  border: 0;
  height: 28px;
  border-radius: 0 !important;
  background-position: center !important;
  color: white;
  text-transform: uppercase;
  font-size: 16px;
  padding: 8px 20px !important;
  display: block;
  margin: 20px auto;
  box-sizing: border-box;
  margin-right: 6px;
}
/* line 1370, ../sass/_template_specific.scss */
.form-contact #contact-button:hover {
  opacity: 0.8;
}

/* line 1376, ../sass/_template_specific.scss */
.form-contact #contact a {
  color: #D47605;
}
/* line 1379, ../sass/_template_specific.scss */
.form-contact #contact label[for="promotions"] {
  display: inline;
  color: #D47605;
}

/* line 1386, ../sass/_template_specific.scss */
.location-info {
  padding-left: 20px;
  box-sizing: border-box;
  font-weight: lighter;
  font-size: 15px;
  line-height: 32px;
  color: gray;
  font-family: "4";
}

/* line 1396, ../sass/_template_specific.scss */
.how-to {
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;
}
/* line 1402, ../sass/_template_specific.scss */
.how-to h3 {
  font-size: 30px;
  margin-bottom: 20px;
  color: rgba(192, 207, 166, 0.8);
}

/* line 1409, ../sass/_template_specific.scss */
.form-contact {
  float: right;
  font-family: "4";
}
/* line 1413, ../sass/_template_specific.scss */
.form-contact h1 {
  margin-bottom: 5px;
}

/* line 1419, ../sass/_template_specific.scss */
.location-info h1 {
  margin-bottom: 19px;
}

/*======= Servicios =======*/
/* line 1425, ../sass/_template_specific.scss */
#wrapper_services {
  text-align: center;
}
/* line 1428, ../sass/_template_specific.scss */
#wrapper_services #services {
  color: black;
}
/* line 1432, ../sass/_template_specific.scss */
#wrapper_services .service_title {
  text-transform: uppercase;
  font-size: 14px;
  margin: 15px 0;
  color: #5B5B5B;
  font-weight: bold;
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
  font-family: 'Lato', sans-serif;
}
/* line 1446, ../sass/_template_specific.scss */
#wrapper_services .service_description {
  color: #5B5B5B;
  font-size: 13px;
  margin-bottom: 10px;
  font-family: "4";
  line-height: 15px;
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}
/* line 1459, ../sass/_template_specific.scss */
#wrapper_services .service {
  text-align: center;
  margin-bottom: 20px;
  float: none;
  display: inline-block;
  width: 262px !important;
  vertical-align: top;
}
/* line 1468, ../sass/_template_specific.scss */
#wrapper_services .service:hover .service_title {
  color: rgba(192, 207, 166, 0.8);
}

/*==== Mini Gallery - Flexslider ====*/
/* line 1477, ../sass/_template_specific.scss */
.mini_gallery_wrapper {
  position: relative;
  margin-bottom: 0px;
}
/* line 1481, ../sass/_template_specific.scss */
.mini_gallery_wrapper .mini_gallery_title {
  text-align: center;
  font-size: 30px;
  color: #D47605;
  font-family: '4';
  font-weight: 100;
  margin-top: 25px;
  margin-bottom: 27px;
}
/* line 1490, ../sass/_template_specific.scss */
.mini_gallery_wrapper .mini_gallery_title strong {
  font-weight: bolder;
}

/* line 1496, ../sass/_template_specific.scss */
.flexslider_mini_gallery {
  height: auto;
  overflow: hidden;
}
/* line 1500, ../sass/_template_specific.scss */
.flexslider_mini_gallery .slides li {
  position: relative;
  overflow: hidden;
  height: 253px;
}
/* line 1504, ../sass/_template_specific.scss */
.flexslider_mini_gallery .slides li .text-bannerx2 img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-height: 100%;
  max-width: none;
  min-width: 100%;
  height: auto;
  width: 100%;
}

/*==== Form ====*/
/* line 1516, ../sass/_template_specific.scss */
.event_form {
  background: #FBFBFC;
  padding: 20px 0;
  font-family: "4";
  text-align: center;
  margin-top: 55px;
}
/* line 1523, ../sass/_template_specific.scss */
.event_form .info {
  font-weight: lighter;
}
/* line 1527, ../sass/_template_specific.scss */
.event_form label {
  text-align: left;
  display: block;
  margin-bottom: 10px;
  color: black;
  font-weight: 300;
  font-family: "4";
}
/* line 1535, ../sass/_template_specific.scss */
.event_form label.error {
  color: #ae0000;
  margin-top: -17px;
}
/* line 1540, ../sass/_template_specific.scss */
.event_form label + input {
  display: initial;
  margin: 0 auto 18px;
  width: 320px;
  border: 1px solid #DFDFE0;
  height: 30px;
  background-color: white;
  color: #474747;
  padding: 0 20px;
  box-sizing: border-box;
}
/* line 1553, ../sass/_template_specific.scss */
.event_form .contInput {
  display: inline-table;
  margin-right: 23px;
}
/* line 1558, ../sass/_template_specific.scss */
.event_form button#submit-button {
  display: block;
  margin: 20px auto;
  width: 100px;
  height: 29px;
  border: 0px;
  color: white;
  text-transform: uppercase;
  font-size: 13px;
  background: rgba(192, 207, 166, 0.8);
}
/* line 1570, ../sass/_template_specific.scss */
.event_form textarea#comments {
  width: 1010px;
  border: 1px solid #DFDFE0;
  margin-bottom: 20px;
}

/*====== Hide Comments Put ====*/
/* line 1578, ../sass/_template_specific.scss */
.hide_comments_put {
  position: absolute;
  z-index: 22;
  top: 70px;
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(192, 207, 166, 0.8);
  padding: 14px 14px 26px;
  border-radius: 0 0 20px 20px;
  display: none;
  border-top: 0;
}
/* line 1589, ../sass/_template_specific.scss */
.hide_comments_put label {
  font-family: "4";
  font-size: 13px;
  text-align: center;
  text-transform: capitalize;
}
/* line 1596, ../sass/_template_specific.scss */
.hide_comments_put input {
  width: 100%;
  margin-top: 5px;
  margin-bottom: 9px;
  box-sizing: border-box;
}
/* line 1603, ../sass/_template_specific.scss */
.hide_comments_put .send_comments {
  text-align: center;
  font-family: "4";
  display: block;
  margin: 0 auto;
  height: 29px;
  border: 0px;
  padding: 10px;
  box-sizing: border-box;
  color: white;
  text-transform: uppercase;
  font-size: 13px;
  background: rgba(192, 207, 166, 0.8);
  cursor: pointer;
}
/* line 1618, ../sass/_template_specific.scss */
.hide_comments_put .send_comments:hover {
  opacity: 0.8;
}
/* line 1623, ../sass/_template_specific.scss */
.hide_comments_put textarea#comments_opinions {
  width: 100%;
  border: 1px solid #C5C5C5;
  box-sizing: border-box;
  margin-top: 5px;
  margin-bottom: 9px;
}

/*=== Top Section Ico ===*/
/* line 1633, ../sass/_template_specific.scss */
.top_ico_section_showed {
  text-align: center;
  margin-top: 55px;
}

/*===== Mis reservas ====*/
/* line 1639, ../sass/_template_specific.scss */
.content_access_wrapper.my_booking_wrapper {
  text-align: center;
  margin: 90px auto 0;
  font-family: "4";
  color: gray;
  line-height: 30px;
}
/* line 1646, ../sass/_template_specific.scss */
.content_access_wrapper.my_booking_wrapper h3.section-title {
  text-align: center;
  font-size: 25px;
  font-family: "4";
  color: #D47605;
  margin-bottom: 45px;
}

/* line 1655, ../sass/_template_specific.scss */
#my-bookings-form-fields {
  margin-top: 20px;
}
/* line 1658, ../sass/_template_specific.scss */
#my-bookings-form-fields label {
  color: #C0CFA6;
}
/* line 1662, ../sass/_template_specific.scss */
#my-bookings-form-fields input {
  display: block;
  margin: auto;
  margin-bottom: 10px;
}

/* line 1669, ../sass/_template_specific.scss */
button#my-bookings-form-search-button, button#cancelButton {
  width: auto !important;
  background: rgba(192, 207, 166, 0.8) !important;
  border: 0;
  height: 28px;
  border-radius: 25px !important;
  background-position: center !important;
  color: white;
  text-transform: uppercase;
  font-size: 13px;
  padding: 6px 20px !important;
  display: block;
  margin: 20px auto;
  box-sizing: border-box;
}

/* line 1685, ../sass/_template_specific.scss */
button#cancelButton {
  display: none;
}

/* line 1689, ../sass/_template_specific.scss */
.grid_12.alpha.my-bookings-booking-info {
  margin: auto;
}

/*=== Events Blocks ===*/
/* line 1694, ../sass/_template_specific.scss */
.dest_wrapper {
  background-color: #F7F7F7;
  min-height: 500px;
  margin-bottom: 40px;
}

/* line 1700, ../sass/_template_specific.scss */
.destinations_elements_wrapper {
  margin-top: 25px;
  display: table;
}

/* line 1705, ../sass/_template_specific.scss */
.title-dest {
  margin-bottom: 20px;
  margin-top: 31px;
  font-family: "2";
  font-size: 21px;
  letter-spacing: 3px;
  text-transform: uppercase;
  text-align: left;
  color: black;
  font-weight: 500;
  padding-left: 30px;
}

/* line 1718, ../sass/_template_specific.scss */
.desc-dest {
  padding-left: 30px;
  padding-bottom: 20px;
  font-size: 15px;
  padding-right: 30px;
  line-height: 24px;
  color: gray;
  margin-bottom: 20px;
  text-align: left;
  width: 1140px;
  box-sizing: border-box;
  font-family: "4";
}
/* line 1731, ../sass/_template_specific.scss */
.desc-dest .self_link_see_more {
  display: table;
  right: 23px;
  bottom: 0;
  position: absolute;
  background: rgba(192, 207, 166, 0.8);
  color: white;
  padding: 7px 20px 3px;
  text-decoration: none;
  line-height: 20px;
  border-radius: 25px;
}

/* line 1745, ../sass/_template_specific.scss */
.link-dest {
  cursor: pointer;
  padding-left: 0px;
  padding-bottom: 20px;
  font-family: "2";
  color: rgba(192, 207, 166, 0.8);
  margin-top: 10px;
  font-weight: bold;
}

/* line 1755, ../sass/_template_specific.scss */
div#dest-img-wrapper-1 {
  float: left;
  width: 529px !important;
  height: 380px !important;
  position: relative;
}

/* line 1762, ../sass/_template_specific.scss */
div#dest-img-wrapper-2 {
  float: left;
  width: 611px;
  height: 194px;
}

/* line 1768, ../sass/_template_specific.scss */
div#dest-img-wrapper-3 {
  float: left;
  width: 305px;
  height: 190px;
}

/* line 1774, ../sass/_template_specific.scss */
div#dest-img-wrapper-4 {
  float: left;
  width: 306px;
  height: 190px;
}

/* line 1780, ../sass/_template_specific.scss */
.gallery_destination_wrapper {
  position: relative;
}

/* line 1784, ../sass/_template_specific.scss */
.dest-img {
  z-index: 20;
  position: relative;
  cursor: pointer;
  height: 100%;
  max-width: none;
}

/* line 1792, ../sass/_template_specific.scss */
.dest-img-wrapper {
  height: 190px !important;
  width: 305.5px !important;
  overflow: hidden;
}
/* line 1797, ../sass/_template_specific.scss */
.dest-img-wrapper iframe {
  position: absolute;
  top: 0px;
  bottom: 0px !important;
  height: 100%;
  left: 0px;
  right: 0px;
}

/* line 1808, ../sass/_template_specific.scss */
.dest_wrapper span.separator {
  display: block;
  height: 3px;
  width: 60px;
  background: rgba(192, 207, 166, 0.8);
  margin-top: 15px;
  margin-left: 30px;
  margin-bottom: 18px;
}

/* line 1820, ../sass/_template_specific.scss */
.popup-start .fancybox-outer {
  background: transparent !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

/************* Sorteo *****************/
/* line 1828, ../sass/_template_specific.scss */
.sort p {
  text-align: center;
}

/* line 1832, ../sass/_template_specific.scss */
.intro-sorteo {
  font-weight: bold;
}

/* line 1836, ../sass/_template_specific.scss */
.video-sorteo {
  text-align: center;
  margin: 0px 0px 40px;
  font-family: "4";
  position: relative;
}
/* line 1842, ../sass/_template_specific.scss */
.video-sorteo .overlay_video {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 700px;
  margin: auto;
  cursor: pointer;
}
/* line 1853, ../sass/_template_specific.scss */
.video-sorteo iframe {
  vertical-align: middle;
}
/* line 1858, ../sass/_template_specific.scss */
.video-sorteo p.part {
  display: block;
  margin: auto;
  color: white;
  font-weight: 300;
  text-transform: uppercase;
  background: #D47605;
  width: 700px;
  padding: 10px 0px;
  font-size: 24px;
  cursor: pointer;
}
/* line 1870, ../sass/_template_specific.scss */
.video-sorteo p.disabled {
  opacity: 0.5;
  pointer-events: none;
  cursor: default;
}

/* line 1877, ../sass/_template_specific.scss */
.sort-content {
  text-align: center;
}
/* line 1880, ../sass/_template_specific.scss */
.sort-content .sorteo-base {
  font-size: 12px;
  color: #afafaf;
  font-family: 'Quicksand', sans-serif;
  line-height: 28px;
  width: 700px;
  margin: auto;
}

/* line 1890, ../sass/_template_specific.scss */
.sort-form {
  width: 700px;
  margin: 0 auto 40px;
}
/* line 1894, ../sass/_template_specific.scss */
.sort-form label {
  display: block;
  margin-bottom: 5px;
  text-align: left;
  margin-top: 20px;
  font-family: "4";
}
/* line 1901, ../sass/_template_specific.scss */
.sort-form input, .sort-form textarea {
  display: block;
  width: 100%;
  border: none;
  background: #f5f5f5;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px;
  font-size: 14px;
  outline: none;
}
/* line 1913, ../sass/_template_specific.scss */
.sort-form input {
  height: 30px;
}
/* line 1916, ../sass/_template_specific.scss */
.sort-form .input-error {
  border: 1px solid red;
}
/* line 1919, ../sass/_template_specific.scss */
.sort-form label.error {
  margin: 0px;
  color: red;
  font-size: 12px;
}
/* line 1924, ../sass/_template_specific.scss */
.sort-form textarea {
  height: 160px;
  margin-bottom: 40px;
  padding: 10px;
}
/* line 1929, ../sass/_template_specific.scss */
.sort-form input[type="button"] {
  background: rgba(192, 207, 166, 0.8);
  color: white;
  height: 30px;
  text-transform: uppercase;
  font-size: 18px;
  border-radius: 3px;
  cursor: pointer;
}
/* line 1938, ../sass/_template_specific.scss */
.sort-form input[type="button"]:hover {
  background: #709912;
}

/*===== Divided Sections =====*/
/* line 1946, ../sass/_template_specific.scss */
ul.divided_sections_filter {
  display: table;
  list-style: none;
  margin: 40px 0;
  padding: 0;
  width: 100%;
  text-align: center;
}
/* line 1954, ../sass/_template_specific.scss */
ul.divided_sections_filter li.divided_title_filter {
  display: inline-block;
  text-align: center;
  padding: 0 5px;
  box-sizing: border-box;
  cursor: pointer;
  opacity: 1;
  text-align: center;
  width: 20%;
  margin: auto;
}
/* line 1965, ../sass/_template_specific.scss */
ul.divided_sections_filter li.divided_title_filter:hover {
  opacity: 0.6;
}
/* line 1969, ../sass/_template_specific.scss */
ul.divided_sections_filter li.divided_title_filter span {
  width: 100%;
  padding: 9px 0 5px;
  display: block;
  font-family: "4";
  color: white;
  font-size: 19px;
}
/* line 1980, ../sass/_template_specific.scss */
ul.divided_sections_filter .divided_green span {
  background: rgba(192, 207, 166, 0.8);
}
/* line 1984, ../sass/_template_specific.scss */
ul.divided_sections_filter .divided_orange span {
  background: #D47605;
}

/* line 1989, ../sass/_template_specific.scss */
.divided_sections_content {
  margin: auto;
  font-family: "4";
  color: gray;
  text-align: justify;
  line-height: 30px;
  margin-bottom: 40px;
}
/* line 1998, ../sass/_template_specific.scss */
.divided_sections_content .automatic_divided_content {
  width: 725px;
  margin: auto;
}
/* line 2003, ../sass/_template_specific.scss */
.divided_sections_content .flexslider_mini_gallery ul.slides {
  position: relative;
  height: auto;
  left: auto;
  top: auto;
}
/* line 2010, ../sass/_template_specific.scss */
.divided_sections_content h3.gallery_filt_title {
  margin-top: 40px;
}

/*============== Bottom Pop-up ============*/
/* line 2017, ../sass/_template_specific.scss */
.bottom_popup {
  position: fixed;
  width: 100%;
  height: 120px;
  background: #D47605;
  box-shadow: 0 -6px 10px -6px #333;
  left: 0;
  bottom: 0;
  z-index: 1000;
}

/* line 2028, ../sass/_template_specific.scss */
.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

/* line 2034, ../sass/_template_specific.scss */
.bottom_popup .bottom_popup_text {
  font-family: Source Sans Pro;
  font-weight: lighter;
  font-size: 16px;
  min-width: 450px;
  margin-top: 10px;
  color: white;
  padding: 10px;
  float: left;
}

/* line 2045, ../sass/_template_specific.scss */
.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

/* line 2049, ../sass/_template_specific.scss */
.close_button {
  float: right;
  cursor: pointer;
}

/* line 2054, ../sass/_template_specific.scss */
.button_wrapper {
  display: inline-block;
  float: right;
  width: 291px;
}

/* line 2060, ../sass/_template_specific.scss */
.button-popup {
  width: 142px;
  margin-top: 30px;
  display: inline-block;
  overflow: hidden;
}

/* line 2068, ../sass/_template_specific.scss */
.button-popup a {
  display: inline-block;
  background: white;
  border: 0;
  color: red;
  background-position: center;
  border-radius: 2px;
  cursor: pointer;
  font-size: 16px;
  padding: 15px 30px;
  text-transform: uppercase;
  text-decoration: none;
  width: 82px;
  text-align: center;
}
/* line 2083, ../sass/_template_specific.scss */
.button-popup a:hover {
  background: #fcbf76;
}
/* line 2086, ../sass/_template_specific.scss */
.button-popup a:hover span {
  color: white !important;
}

/* line 2092, ../sass/_template_specific.scss */
#wrapper2 {
  width: 1140px;
  margin: 0 auto;
}

/* line 2097, ../sass/_template_specific.scss */
.popup_inicial {
  width: 800px;
  height: 100%;
  background-size: cover !important;
  display: table;
}
/* line 2103, ../sass/_template_specific.scss */
.popup_inicial .email, .popup_inicial .discount, .popup_inicial .compra {
  text-align: center;
}
/* line 2107, ../sass/_template_specific.scss */
.popup_inicial .compra {
  padding-top: 5px;
  color: white;
  font-size: 22px;
  font-weight: lighter;
}
/* line 2114, ../sass/_template_specific.scss */
.popup_inicial .discount {
  padding-top: 7px;
  color: white;
  font-size: 47px;
  text-shadow: 3px 3px black;
  text-transform: uppercase;
  font-family: 'Oswald', sans-serif;
}
/* line 2123, ../sass/_template_specific.scss */
.popup_inicial .email {
  padding-top: 39px;
  color: white;
  font-size: 22px;
  font-weight: lighter;
}
/* line 2130, ../sass/_template_specific.scss */
.popup_inicial form.form_popup {
  text-align: center;
  padding-top: 50px;
}
/* line 2134, ../sass/_template_specific.scss */
.popup_inicial form.form_popup li {
  text-align: center;
}
/* line 2138, ../sass/_template_specific.scss */
.popup_inicial form.form_popup input#id_email {
  height: 26px;
  text-align: center;
  width: 270px;
  font-size: 17px;
  box-shadow: 2px 2px black;
  border: 0px;
  color: rgba(192, 207, 166, 0.8);
}
/* line 2148, ../sass/_template_specific.scss */
.popup_inicial form.form_popup button.popup_button {
  margin: 7px 0px 3px 20px;
  width: 277px;
  height: 40px;
  background: rgba(192, 207, 166, 0.8);
  font-size: 17px;
  border: 0px;
  text-transform: uppercase;
  color: white;
  cursor: pointer;
}
/* line 2161, ../sass/_template_specific.scss */
.popup_inicial .spinner_wrapper_faldon {
  padding-top: 20px;
}
/* line 2165, ../sass/_template_specific.scss */
.popup_inicial .popup_message {
  color: white;
  padding-top: 25px;
  font-size: 20px;
  font-weight: lighter;
}

/* line 2173, ../sass/_template_specific.scss */
.picture-bigskirt {
  float: left;
  position: relative;
  bottom: 80px;
  left: 25px;
}

/*============== Gallery Filtered 2 ============*/
/* line 2181, ../sass/_template_specific.scss */
.section_title {
  font-size: 77px;
  font-weight: 200;
  color: #898787;
  text-align: center;
  width: 700px;
  margin: 25px auto 55px;
  line-height: 65px;
}

/* line 2191, ../sass/_template_specific.scss */
.gallery_2 li .crop {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

/* line 2198, ../sass/_template_specific.scss */
.gallery_2 li .crop img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  max-width: initial;
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}

/* line 2207, ../sass/_template_specific.scss */
.gallery_2 li .crop img:hover {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

/* line 2215, ../sass/_template_specific.scss */
.gallery_2 li {
  margin: 1px;
  float: left;
  height: 155px;
}

/* line 2221, ../sass/_template_specific.scss */
ul.gallery_2 {
  margin: 12px 0px 35px;
  display: table;
  box-sizing: border-box;
}

/* line 2227, ../sass/_template_specific.scss */
.gallery_2 li:first-of-type {
  height: 426px;
  width: 542px;
}

/* line 2232, ../sass/_template_specific.scss */
.gallery_2 li:first-of-type .crop {
  width: 100%;
  height: 100%;
}

/* line 2237, ../sass/_template_specific.scss */
.interior .tp-banner-container {
  height: 669px !important;
}

/* line 2241, ../sass/_template_specific.scss */
.interior .tp-revslider-mainul {
  position: fixed;
  z-index: -30;
}

/* line 2246, ../sass/_template_specific.scss */
.gallery_filt_title {
  font-size: 23px !important;
  font-weight: 500;
  color: black;
  text-align: left;
  line-height: 45px;
  border-bottom: 1px solid #8e8b8b;
  width: 100%;
}

/* line 2256, ../sass/_template_specific.scss */
.wrapper_filt .gallery_2 li {
  width: 188px !important;
  overflow: hidden;
}

/* line 2261, ../sass/_template_specific.scss */
.wrapper_filt .gallery_2 li:first-of-type {
  height: 155px;
  width: 283px;
}

/* line 2266, ../sass/_template_specific.scss */
span.gallery_description_title {
  position: absolute;
  bottom: 0px;
  color: white;
  left: 0;
  right: 0;
}

/* line 2274, ../sass/_template_specific.scss */
span.background_gallery_title {
  margin: auto;
  display: table;
  background: #191919;
  padding: 3px 20px 0;
  font-size: 11px;
}

/* line 2283, ../sass/_template_specific.scss */
.video-li #video_corporativo .play_video_gallery {
  position: absolute;
  width: 45px !important;
  min-width: 45px !important;
  height: 45px;
  min-height: 45px !important;
  z-index: 100;
  top: 35%;
  left: 40%;
  margin: 0;
}

/* line 2296, ../sass/_template_specific.scss */
.multi_icon {
  display: inline-block;
  margin-right: 20px;
}

/* line 2302, ../sass/_template_specific.scss */
.merchandising li .crop {
  width: 100%;
  overflow: hidden;
  position: relative;
  height: 232.5px;
}

/* line 2309, ../sass/_template_specific.scss */
.merchandising li .crop img {
  display: block;
  height: 155px;
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
  width: 100%;
  height: 100%;
}

/* line 2321, ../sass/_template_specific.scss */
.merchandising li .crop img:hover {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

/* line 2329, ../sass/_template_specific.scss */
.merchandising li {
  margin: 1px;
  float: left;
}

/* line 2335, ../sass/_template_specific.scss */
ul.merchandising {
  margin: 12px 0px 35px;
  display: table;
  box-sizing: border-box;
}

/* line 2341, ../sass/_template_specific.scss */
.interior .tp-banner-container {
  height: 669px !important;
}

/* line 2345, ../sass/_template_specific.scss */
.interior .tp-revslider-mainul {
  position: fixed;
  z-index: -30;
}

/* line 2350, ../sass/_template_specific.scss */
.gallery_filt_title {
  font-size: 23px !important;
  font-weight: 500;
  color: black;
  text-align: left;
  line-height: 45px;
  border-bottom: 1px solid #8e8b8b;
  width: 100%;
}

/* line 2360, ../sass/_template_specific.scss */
.wrapper_filt .merchandising li {
  width: 564px !important;
  overflow: hidden;
}

/* line 2365, ../sass/_template_specific.scss */
span.gallery_description_title {
  position: absolute;
  bottom: 0px;
  color: white;
  left: 0;
  right: 0;
}

/* line 2373, ../sass/_template_specific.scss */
span.background_gallery_title {
  margin: auto;
  display: table;
  background: #191919;
  padding: 3px 20px 0;
  font-size: 11px;
}

/* line 2382, ../sass/_template_specific.scss */
.video-li #video_corporativo .play_video_gallery {
  position: absolute;
  width: 45px !important;
  min-width: 45px !important;
  height: 45px;
  min-height: 45px !important;
  z-index: 100;
  top: 35%;
  left: 40%;
}

/* line 2398, ../sass/_template_specific.scss */
.gallery-merchandising .box-image {
  float: left;
  width: 564px;
  height: 300px;
  overflow: hidden;
  border: 1px solid white;
  position: relative;
}
/* line 2406, ../sass/_template_specific.scss */
.gallery-merchandising .box-image img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}
/* line 2421, ../sass/_template_specific.scss */
.gallery-merchandising .box-image img:hover {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}
/* line 2429, ../sass/_template_specific.scss */
.gallery-merchandising .banner_product {
  position: absolute;
  width: 100%;
  bottom: 0px;
  text-align: center;
  font-size: 18px;
  padding: 10px 0;
  color: white;
  background: rgba(192, 207, 166, 0.8);
}
/* line 2439, ../sass/_template_specific.scss */
.gallery-merchandising .banner_product .title {
  display: inline-block;
  font-family: "4";
  text-transform: uppercase;
  margin-right: 10px;
}
/* line 2445, ../sass/_template_specific.scss */
.gallery-merchandising .banner_product .price {
  display: inline-block;
  font-family: "4";
  font-weight: bold;
}

/*===== Events Dates =====*/
/* line 2454, ../sass/_template_specific.scss */
.events_dates_wrapper {
  position: relative;
}
/* line 2457, ../sass/_template_specific.scss */
.events_dates_wrapper:before {
  content: "";
  display: block;
  background: #E8E8E8;
  width: 100%;
  height: 1px;
  position: absolute;
  top: -44px;
}

/* line 2468, ../sass/_template_specific.scss */
.events_dates_wrapper_more {
  font-family: "4", sans-serif;
  display: block;
  float: none;
  background-color: #D47605;
  background-image: url(/img/onhol/boton_search.png?v=1);
  background-position: center;
  background-repeat: no-repeat;
  font-size: 20px;
  padding: 60px 0;
  width: 150px;
  border-radius: 50%;
  margin: auto;
  text-align: center;
  text-transform: uppercase;
  text-decoration: none;
  color: white;
}

/* line 2491, ../sass/_template_specific.scss */
.event_date_element {
  width: 338px;
  display: inline-block;
  margin: 0 20px;
  color: #4b4b4b;
  vertical-align: top;
}
/* line 2498, ../sass/_template_specific.scss */
.event_date_element .exceded img {
  width: 100%;
  display: block;
}
/* line 2503, ../sass/_template_specific.scss */
.event_date_element:nth-of-type(3), .event_date_element:nth-of-type(6), .event_date_element:nth-of-type(9), .event_date_element:nth-of-type(12), .event_date_element:nth-of-type(15) {
  margin-right: 0;
}
/* line 2507, ../sass/_template_specific.scss */
.event_date_element .event_date_day {
  background: url(/img/onhol/boton_search.png?v=1);
  width: 120px;
  height: 120px;
  border-radius: 100px;
  text-align: center;
  margin: 0 auto -54px;
  position: relative;
  font-family: "4";
  border: 7px solid white;
  color: white;
  background-size: cover;
}
/* line 2520, ../sass/_template_specific.scss */
.event_date_element .event_date_day span.vcenter_event {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  display: table;
  width: 100%;
}
/* line 2530, ../sass/_template_specific.scss */
.event_date_element .event_date_description_short {
  font-family: "4";
  position: relative;
  background: #bfa54b;
  padding: 21px;
  padding-right: 64px;
  font-size: 14px;
  color: white;
}
/* line 2539, ../sass/_template_specific.scss */
.event_date_element .event_date_description_short hide {
  display: none;
}
/* line 2544, ../sass/_template_specific.scss */
.event_date_element a.event_link {
  position: absolute;
  right: 30px;
  top: 0;
  bottom: 0;
  margin: auto;
  display: none;
}

/* line 2554, ../sass/_template_specific.scss */
.event_date_hidden {
  padding: 20px;
  color: #4b4b4b;
  font-family: "4";
  line-height: 21px;
}
/* line 2559, ../sass/_template_specific.scss */
.event_date_hidden h4 {
  display: none;
}
/* line 2562, ../sass/_template_specific.scss */
.event_date_hidden hide {
  display: block;
}

/*== Cookie free image ==*/
/* line 2568, ../sass/_template_specific.scss */
.cookie_free_wrapper {
  padding: 10px 10px 10px;
  position: absolute;
  border-radius: 0 0 110px 110px;
  left: -146px;
  bottom: 0;
}
/* line 2575, ../sass/_template_specific.scss */
.cookie_free_wrapper .cookie_free_image {
  width: 125px;
}

/*==== Event block see more ===*/
/* line 2581, ../sass/_template_specific.scss */
.event_see_more_wrapper {
  width: 350px;
  display: inline-block;
  overflow: hidden;
  margin: 15px;
  vertical-align: top;
}
/* line 2588, ../sass/_template_specific.scss */
.event_see_more_wrapper .desc-dest {
  width: 100%;
  position: relative;
  padding-bottom: 40px;
}
/* line 2593, ../sass/_template_specific.scss */
.event_see_more_wrapper .desc-dest a.myFancyPopupAuto, .event_see_more_wrapper .desc-dest a.deploy_description {
  display: table;
  right: 23px;
  bottom: 0;
  position: absolute;
  background: #C0CFA6;
  color: white;
  padding: 7px 20px 3px;
  text-decoration: none;
  line-height: 20px;
  border-radius: 25px;
}
/* line 2605, ../sass/_template_specific.scss */
.event_see_more_wrapper .desc-dest a.myFancyPopupAuto:hover, .event_see_more_wrapper .desc-dest a.deploy_description:hover {
  opacity: 0.8;
}
/* line 2611, ../sass/_template_specific.scss */
.event_see_more_wrapper .real_desc_dest {
  height: 75px;
  display: block;
  overflow: hidden;
}

/* line 2619, ../sass/_template_specific.scss */
.event_see_more_hidden {
  padding: 20px;
}
/* line 2622, ../sass/_template_specific.scss */
.event_see_more_hidden .title-dest {
  margin-top: 0;
  padding-left: 0;
}
/* line 2627, ../sass/_template_specific.scss */
.event_see_more_hidden .real_desc_dest {
  font-family: "4";
}

/*========= News ======*/
/* line 2634, ../sass/_template_specific.scss */
.block-new-description .date {
  font-size: 16px;
  color: #D47605;
  font-family: "4";
}

/* line 2640, ../sass/_template_specific.scss */
.block-new-description .news-title {
  margin-top: 31px;
  font-family: "2";
  font-size: 21px;
  letter-spacing: 3px;
  text-transform: uppercase;
  text-align: left;
  color: black;
  font-weight: 500;
  margin-bottom: 9px;
  padding-left: 0;
}

/* line 2653, ../sass/_template_specific.scss */
.block-new-description .news_description {
  display: block;
  font-family: "4";
  height: 75px;
  overflow: hidden;
}

/* line 2660, ../sass/_template_specific.scss */
.block-new-description ul .read-more-news {
  font-family: "4";
  display: table;
  right: 23px;
  bottom: 0;
  background: #C0CFA6;
  color: white;
  padding: 7px 20px 3px;
  text-decoration: none;
  line-height: 20px;
  font-size: 15px;
  border-radius: 25px;
}

/* line 2674, ../sass/_template_specific.scss */
.new:last-child {
  margin-bottom: 30px;
}

/* line 2678, ../sass/_template_specific.scss */
#no-bg {
  text-align: left;
  overflow: hidden;
  padding: 40px 40px 40px;
  font-family: "4";
}
/* line 2684, ../sass/_template_specific.scss */
#no-bg h3 {
  font-size: 24px;
  color: #D47605;
  text-transform: uppercase;
  margin-bottom: 10px;
}
/* line 2690, ../sass/_template_specific.scss */
#no-bg #new-image {
  float: left;
  margin-right: 40px;
  width: 400px !important;
}
/* line 2695, ../sass/_template_specific.scss */
#no-bg #new-image img {
  width: 100% !important;
}
/* line 2699, ../sass/_template_specific.scss */
#no-bg .description {
  width: 620px;
  float: right;
}
/* line 2703, ../sass/_template_specific.scss */
#no-bg .date {
  color: rgba(192, 207, 166, 0.8);
  margin-bottom: 10px;
}
/* line 2707, ../sass/_template_specific.scss */
#no-bg .share {
  float: left;
  margin-right: 10px;
  color: #D47605;
  font-size: 16px;
  margin-top: 10px;
}
/* line 2714, ../sass/_template_specific.scss */
#no-bg .addthis_toolbox {
  padding-top: 9px !important;
}

/* line 2720, ../sass/_template_specific.scss */
.image_1 {
  position: absolute;
  top: -16px;
  left: 0;
}

/* line 2726, ../sass/_template_specific.scss */
.image_2 {
  position: absolute;
  top: -16px;
  right: 0;
}

/* line 2732, ../sass/_template_specific.scss */
.button_download {
  text-decoration: none;
  color: white;
  background: url(/img/onhol/boton_search.png?v=1) no-repeat center;
  background-size: contain;
  display: table-cell;
  width: 150px;
  height: 150px;
  vertical-align: middle;
  text-align: center;
  position: relative;
}
/* line 2744, ../sass/_template_specific.scss */
.button_download .center_link {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 100%;
  display: table;
}

/* line 2754, ../sass/_template_specific.scss */
.button_download:hover {
  opacity: 0.8;
}

/* line 2758, ../sass/_template_specific.scss */
.block-new-description ul {
  position: absolute;
  bottom: -10px;
  right: 22px;
}

/* line 2764, ../sass/_template_specific.scss */
.new {
  width: 345px;
  display: inline-block;
  overflow: hidden;
  margin: 15px;
  vertical-align: top;
  border-bottom: 0;
  background-color: #F7F7F7;
  min-height: 500px;
  padding-bottom: 10px;
}
/* line 2775, ../sass/_template_specific.scss */
.new .photo-container {
  float: left;
  width: 100% !important;
  height: 380px !important;
  position: relative;
}
/* line 2781, ../sass/_template_specific.scss */
.new .photo-container img {
  z-index: 20;
  position: relative;
  cursor: pointer;
  height: 100%;
  max-width: none;
  min-width: 100%;
}
/* line 2791, ../sass/_template_specific.scss */
.new .block-new-description {
  padding-left: 30px;
  padding-bottom: 20px;
  font-size: 15px;
  padding-right: 30px;
  line-height: 24px;
  color: gray;
  margin-bottom: 20px;
  text-align: left;
  width: 100%;
  box-sizing: border-box;
  font-family: "4";
  height: auto;
}

/* line 2807, ../sass/_template_specific.scss */
#new1 {
  margin-top: 15px;
}

/* line 2811, ../sass/_template_specific.scss */
.blog-separator {
  display: none;
}

/*==== Provider blocks ====*/
/* line 2816, ../sass/_template_specific.scss */
.provider_wrapper {
  margin-bottom: 100px;
}
/* line 2819, ../sass/_template_specific.scss */
.provider_wrapper .provider_title {
  text-align: center;
  font-size: 25px;
  font-family: "4";
  color: #D47605;
  margin-bottom: 45px;
}
/* line 2827, ../sass/_template_specific.scss */
.provider_wrapper .provider_content {
  width: 725px;
  margin: auto;
  font-family: "4";
  color: gray;
  text-align: justify;
  line-height: 30px;
  margin-bottom: 50px;
}
/* line 2837, ../sass/_template_specific.scss */
.provider_wrapper .provider_icos_wrapper {
  text-align: center;
}
/* line 2839, ../sass/_template_specific.scss */
.provider_wrapper .provider_icos_wrapper img.provider_ico_element {
  padding: 15px 37px;
}

/* line 2845, ../sass/_template_specific.scss */
.container_popup_booking {
  background: transparent;
}

/* line 2850, ../sass/_template_specific.scss */
.booking_popup_waiting .fancybox-outer {
  background: white;
  box-shadow: none;
  border-radius: 50%;
  padding: 10px !important;
}

/* line 2858, ../sass/_template_specific.scss */
#fancybox-buttons a {
  background: none !important;
}

/* line 2862, ../sass/_template_specific.scss */
#image-fiestas {
  left: -420px !important;
}

/* line 2870, ../sass/_template_specific.scss */
#image-entorno {
  left: -560px !important;
}

/*==== Social rooms ====*/
/* line 1, ../sass/_social_rooms.scss */
img.social_rooms {
  position: fixed;
  right: 0;
  bottom: 230px;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  z-index: 1003;
  cursor: pointer;
}
/* line 13, ../sass/_social_rooms.scss */
img.social_rooms.active {
  right: 100%;
  margin-right: 0;
}

/* line 19, ../sass/_social_rooms.scss */
.social_elements_wrapper {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 100%;
  width: 100%;
  z-index: 1002;
  background: #c0cfa6;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 34, ../sass/_social_rooms.scss */
.social_elements_wrapper.active {
  left: 0;
}

/* line 39, ../sass/_social_rooms.scss */
.all_elements_wrapper_social {
  position: absolute;
  width: 100%;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 48, ../sass/_social_rooms.scss */
.all_elements_wrapper_social .content_wrapper_social {
  width: 750px;
  padding: 20px;
  margin: auto;
  color: white;
  letter-spacing: 1px;
  display: block;
  height: 100%;
}
/* line 56, ../sass/_social_rooms.scss */
.all_elements_wrapper_social .content_wrapper_social h3 {
  text-align: center;
  font-size: 25px;
  font-family: "4", sans-serif;
  margin-bottom: 45px;
  text-transform: uppercase;
}
/* line 63, ../sass/_social_rooms.scss */
.all_elements_wrapper_social .content_wrapper_social h4 {
  font-family: "4", sans-serif;
  font-weight: bold;
  padding-bottom: 10px;
}
/* line 68, ../sass/_social_rooms.scss */
.all_elements_wrapper_social .content_wrapper_social p {
  font-family: "4", sans-serif;
  text-align: justify;
  letter-spacing: 2px;
  padding-bottom: 30px;
}
/* line 74, ../sass/_social_rooms.scss */
.all_elements_wrapper_social .content_wrapper_social ul {
  list-style-type: disc;
  padding-left: 15px;
  padding-bottom: 30px;
}
/* line 78, ../sass/_social_rooms.scss */
.all_elements_wrapper_social .content_wrapper_social ul li {
  font-family: "4", sans-serif;
}
/* line 82, ../sass/_social_rooms.scss */
.all_elements_wrapper_social .content_wrapper_social .content_social {
  height: 400px;
  width: 100%;
  overflow: auto;
  padding: 0 20px;
}
/* line 88, ../sass/_social_rooms.scss */
.all_elements_wrapper_social .content_wrapper_social .button-promotion {
  display: block;
  border-radius: 0px !important;
  height: 93px !important;
  width: 93px !important;
  border-width: 0;
  background: url(/img/onhol/boton_search.png);
  color: white;
  margin: auto !important;
  margin-top: 5px !important;
  text-transform: uppercase;
  background-size: contain;
}
/* line 101, ../sass/_social_rooms.scss */
.all_elements_wrapper_social .content_wrapper_social .button-promotion:hover {
  opacity: 0.8;
}

/* line 108, ../sass/_social_rooms.scss */
.close_social_rooms_button {
  float: right;
  margin-top: 10px;
  margin-right: 20px;
  width: 50px;
  cursor: pointer;
  position: relative;
  z-index: 999;
}
/* line 117, ../sass/_social_rooms.scss */
.close_social_rooms_button:before {
  content: "x";
  color: white;
  font-size: 85px;
  line-height: 36px;
  font-family: 'Montserrat', sans-serif;
}

/* line 2877, ../sass/_template_specific.scss */
.newsletter_wrapper {
  display: inline-block;
  width: 100%;
  text-align: center;
  background: #D47605;
  padding: 20px 0;
}
/* line 2885, ../sass/_template_specific.scss */
.newsletter_wrapper #newsletter #title_newsletter {
  display: inline-block;
  font-family: "4";
  text-transform: uppercase;
  color: white;
  margin-right: 10px;
  vertical-align: top;
  margin-top: 12px;
}
/* line 2895, ../sass/_template_specific.scss */
.newsletter_wrapper #newsletter #form-newsletter {
  display: inline-block;
  vertical-align: middle;
  width: 100%;
}
/* line 2900, ../sass/_template_specific.scss */
.newsletter_wrapper #newsletter #form-newsletter #suscEmail {
  display: inline-block;
  border: 1px solid white;
  background: transparent;
  height: 35px;
  width: 200px;
  color: white;
  padding-left: 10px;
  vertical-align: top;
  box-sizing: border-box;
  border-radius: 50px;
}
/* line 2912, ../sass/_template_specific.scss */
.newsletter_wrapper #newsletter #form-newsletter #suscEmail::-webkit-input-placeholder {
  color: white;
}
/* line 2915, ../sass/_template_specific.scss */
.newsletter_wrapper #newsletter #form-newsletter #suscEmail::-moz-placeholder {
  color: white;
}
/* line 2918, ../sass/_template_specific.scss */
.newsletter_wrapper #newsletter #form-newsletter #suscEmail:-ms-input-placeholder {
  color: white;
}
/* line 2921, ../sass/_template_specific.scss */
.newsletter_wrapper #newsletter #form-newsletter #suscEmail:-moz-placeholder {
  color: white;
}
/* line 2926, ../sass/_template_specific.scss */
.newsletter_wrapper #newsletter #form-newsletter #newsletterButtonExternalDiv {
  display: inline-block;
  vertical-align: middle;
}
/* line 2930, ../sass/_template_specific.scss */
.newsletter_wrapper #newsletter #form-newsletter #newsletterButtonExternalDiv button {
  display: inline-block;
  vertical-align: middle;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  background: white;
  color: #D47605;
  height: 35px;
  text-transform: uppercase;
  width: 90px;
  text-align: center;
  box-sizing: border-box;
  cursor: pointer;
  border-radius: 50px;
}
/* line 2947, ../sass/_template_specific.scss */
.newsletter_wrapper #newsletter #form-newsletter #newsletterButtonExternalDiv button:hover {
  opacity: .8;
}
/* line 2952, ../sass/_template_specific.scss */
.newsletter_wrapper #newsletter #form-newsletter .check_newsletter {
  display: inline-block;
}
/* line 2954, ../sass/_template_specific.scss */
.newsletter_wrapper #newsletter #form-newsletter .check_newsletter .newsletter_checkbox {
  font-size: 12px;
  color: white;
  display: inline-block;
  letter-spacing: 1.5px;
  text-align: left;
  margin-top: 5px;
  white-space: nowrap;
}
/* line 2962, ../sass/_template_specific.scss */
.newsletter_wrapper #newsletter #form-newsletter .check_newsletter .newsletter_checkbox a {
  text-decoration: underline !important;
  color: white;
}
/* line 2966, ../sass/_template_specific.scss */
.newsletter_wrapper #newsletter #form-newsletter .check_newsletter .newsletter_checkbox label {
  display: inline-block;
}

/* line 2975, ../sass/_template_specific.scss */
.datepicker_wrapper_element .header_datepicker, .datepicker_wrapper_element_2 .header_datepicker, .datepicker_wrapper_element_3 .header_datepicker {
  background: #D47605 !important;
}

/* line 2979, ../sass/_template_specific.scss */
.datepicker_wrapper_element .datepicker_ext_inf_sd .ui-widget-header, .datepicker_wrapper_element .datepicker_ext_inf_ed .ui-widget-header, .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-widget-header, .datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-widget-header, .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-widget-header, .datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-widget-header {
  background: #c0cfa6 !important;
}

/* line 2984, ../sass/_template_specific.scss */
body .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-state-active, body .datepicker_wrapper_element .datepicker_ext_inf_ed .ui-state-active, body .datepicker_wrapper_element_2 .datepicker_ext_inf_sd .ui-state-active, body .datepicker_wrapper_element_2 .datepicker_ext_inf_ed .ui-state-active, body .datepicker_wrapper_element_3 .datepicker_ext_inf_sd .ui-state-active, body .datepicker_wrapper_element_3 .datepicker_ext_inf_ed .ui-state-active {
  background: #D47605 !important;
  color: white !important;
}

/* line 2990, ../sass/_template_specific.scss */
.newsletter_additional_wrapper .newsletter_additional_form .popup_policy_newsletter a {
  display: inline-block;
}

/* line 2, ../sass/_booking_popup.scss */
.booking-data-popup .fancybox-outer {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
/* line 8, ../sass/_booking_popup.scss */
.booking-data-popup div#wrapper_booking_fancybox {
  display: table;
  width: 100%;
  position: absolute;
  bottom: 0;
  top: 0;
  margin: auto;
}

/* line 18, ../sass/_booking_popup.scss */
div#data {
  background: #a5c87f;
}
/* line 21, ../sass/_booking_popup.scss */
div#data .booking_title1, div#data .best_price {
  display: none;
}
/* line 25, ../sass/_booking_popup.scss */
div#data div#booking_engine_title {
  display: block;
  float: none;
  text-align: center;
}
/* line 31, ../sass/_booking_popup.scss */
div#data #motor_reserva {
  width: 535px;
  margin: auto;
  display: table;
}
/* line 37, ../sass/_booking_popup.scss */
div#data div#fecha_entrada, div#data div#fecha_salida {
  width: 260px;
  float: left;
  height: 125px;
  background: white;
}
/* line 44, ../sass/_booking_popup.scss */
div#data div#fecha_salida {
  float: right;
  margin-left: 0 !important;
}
/* line 49, ../sass/_booking_popup.scss */
div#data label#titulo_fecha_entrada, div#data label#titulo_fecha_salida {
  display: block;
  color: #D47605;
  width: 100% !important;
  text-align: center;
  margin-top: 20px;
  font-family: "4";
  text-transform: uppercase;
  font-size: 17px;
}
/* line 60, ../sass/_booking_popup.scss */
div#data #contenedor_fechas {
  width: 100%;
  margin-bottom: 10px;
}
/* line 65, ../sass/_booking_popup.scss */
div#data .wrapper-old-web-support {
  display: none !important;
}
/* line 69, ../sass/_booking_popup.scss */
div#data #fecha_entrada input, div#data #fecha_salida input {
  border: 0 !important;
  height: 96px !important;
  width: 100% !important;
  text-align: center !important;
  padding-right: 52px !important;
  box-sizing: border-box !important;
  font-size: 31px !important;
  font-family: "4" !important;
  color: #4b4b4b !important;
  background: transparent url(/img/onhol/calendar_ico.png) no-repeat 90% center !important;
}
/* line 81, ../sass/_booking_popup.scss */
div#data #fecha_entrada input::-webkit-input-placeholder, div#data #fecha_salida input::-webkit-input-placeholder {
  color: #4b4b4b !important;
}
/* line 86, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones {
  margin: auto;
  width: 260px;
  float: left;
  height: 125px;
  background: white;
}
/* line 93, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones label {
  display: block;
  color: #D47605;
  width: 100% !important;
  text-align: center;
  margin-top: 20px;
  font-family: "4";
  text-transform: uppercase;
  font-size: 17px;
  float: none;
}
/* line 105, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones select#selector_habitaciones {
  -webkit-border-radius: 0 !important;
  -moz-border-radius: 0 !important;
  border-radius: 0 !important;
  border: 0;
  width: 260px;
  float: left;
  height: 125px;
  background: white;
  -webkit-appearance: none;
}
/* line 116, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones select#selector_habitaciones option {
  text-align: center;
}
/* line 121, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones .selectric {
  height: 96px;
  border-radius: 0;
}
/* line 125, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones .selectric p.label {
  color: #4b4b4b;
  text-align: center;
  padding-right: 52px !important;
  box-sizing: border-box !important;
  font-size: 31px !important;
  padding-top: 30px;
  font-family: "4" !important;
}
/* line 135, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones .selectric .button {
  background: transparent url(/img/onhol/ico_down.png) no-repeat center !important;
  right: 27px;
}
/* line 141, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones .selectricItems li {
  color: #4b4b4b;
  text-align: center;
  box-sizing: border-box !important;
  font-size: 21px !important;
  padding: 12px 12px 10px;
  font-family: "4" !important;
}
/* line 151, ../sass/_booking_popup.scss */
div#data .selectricWrapper {
  width: 100%;
}
/* line 155, ../sass/_booking_popup.scss */
div#data #contenedor_opciones {
  float: right;
  margin-top: -125px;
}
/* line 159, ../sass/_booking_popup.scss */
div#data #contenedor_opciones #hab1, div#data #contenedor_opciones #hab2, div#data #contenedor_opciones #hab3 {
  margin: auto;
  width: 260px;
  float: left;
  background: white;
  height: 133px;
}
/* line 167, ../sass/_booking_popup.scss */
div#data #contenedor_opciones #hab1 {
  margin-left: 275px;
}
/* line 171, ../sass/_booking_popup.scss */
div#data #contenedor_opciones #hab2, div#data #contenedor_opciones #hab3 {
  margin-top: 20px;
  display: block !important;
}
/* line 176, ../sass/_booking_popup.scss */
div#data #contenedor_opciones #hab3 {
  float: right;
}
/* line 180, ../sass/_booking_popup.scss */
div#data #contenedor_opciones label.numero_habitacion {
  color: #D47605;
  width: 100% !important;
  text-align: center;
  margin-top: 20px;
  font-family: "4";
  display: block !important;
  text-transform: uppercase;
  font-size: 17px;
  float: none;
}
/* line 193, ../sass/_booking_popup.scss */
div#data .adultos.numero_personas {
  width: 100%;
}
/* line 196, ../sass/_booking_popup.scss */
div#data .adultos.numero_personas option {
  display: none;
}
/* line 202, ../sass/_booking_popup.scss */
div#data .selectricWrapper.selector_adultos .selectric {
  height: 96px;
  border-radius: 0;
}
/* line 207, ../sass/_booking_popup.scss */
div#data .selectricWrapper.selector_adultos p.label {
  color: #4b4b4b;
  text-align: center;
  padding-right: 52px !important;
  box-sizing: border-box !important;
  font-size: 31px !important;
  font-family: "4" !important;
  padding-top: 30px;
}
/* line 217, ../sass/_booking_popup.scss */
div#data .selectricWrapper.selector_adultos .button {
  background: transparent url(/img/onhol/ico_down.png) no-repeat center !important;
}
/* line 221, ../sass/_booking_popup.scss */
div#data .selectricWrapper.selector_adultos .selectricItems li {
  color: #4b4b4b;
  text-align: center;
  box-sizing: border-box !important;
  font-size: 16px !important;
  padding: 6px 12px 4px;
  font-family: "4" !important;
}
/* line 231, ../sass/_booking_popup.scss */
div#data fieldset#envio {
  width: 100%;
  margin-left: 0;
}
/* line 235, ../sass/_booking_popup.scss */
div#data fieldset#envio input#promocode {
  float: left;
  width: 260px;
  border-radius: 0;
  border: 0;
  box-sizing: border-box;
  margin-top: 20px;
  height: 90px;
  text-align: center;
  background: rgba(255, 255, 255, 0.3);
  font-family: "4";
  font-size: 31px !important;
  font-weight: lighter;
  color: white;
}
/* line 250, ../sass/_booking_popup.scss */
div#data fieldset#envio input#promocode::-webkit-input-placeholder {
  color: white;
  font-family: "4";
  font-size: 26px;
  font-weight: lighter;
  text-transform: uppercase;
}
/* line 259, ../sass/_booking_popup.scss */
div#data fieldset#envio button#search-button {
  display: block;
  float: right;
  height: 90px;
  width: 260px;
  border-radius: 0;
  border: 0;
  box-sizing: border-box;
  margin-top: 20px;
  background: #e27100;
  font-family: "4";
  color: white;
  text-transform: uppercase;
  font-size: 27px !important;
}
/* line 277, ../sass/_booking_popup.scss */
div#data div#hab2 .disabled_overlay, div#data div#hab3 .disabled_overlay {
  display: none;
}
/* line 281, ../sass/_booking_popup.scss */
div#data div#hab2.disabled, div#data div#hab3.disabled {
  opacity: 0.4;
  position: relative;
}
/* line 285, ../sass/_booking_popup.scss */
div#data div#hab2.disabled .disabled_overlay, div#data div#hab3.disabled .disabled_overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: block;
}
/* line 297, ../sass/_booking_popup.scss */
div#data #booking_engine_title #booking_title1, div#data #booking_engine_title #best_price {
  display: none;
}
/* line 301, ../sass/_booking_popup.scss */
div#data #booking_engine_title h4#booking_title2 {
  font-family: "4";
  color: white;
  margin-bottom: 25px;
  text-transform: uppercase;
  font-size: 22px;
  margin-top: 0;
}
/* line 312, ../sass/_booking_popup.scss */
div#data #booking_engine_title #booking_title2:after {
  content: '';
  display: block;
  width: 70px;
  height: 1px;
  background: white;
  margin: 10px auto;
}
/* line 321, ../sass/_booking_popup.scss */
div#data #booking_engine_title #booking_title2 span {
  font-weight: lighter;
}
/* line 327, ../sass/_booking_popup.scss */
div#data #contenedor_opciones div#hab2.disabled {
  display: none !important;
}
/* line 330, ../sass/_booking_popup.scss */
div#data #contenedor_opciones div#hab2.disabled + #hab3 {
  display: none !important;
}

/* line 337, ../sass/_booking_popup.scss */
.booking-data-popup .fancybox-close {
  position: absolute;
  top: 30px;
  right: 30px;
  background: url(/img/onhol/close_button.png) no-repeat center;
  background: none;
}
/* line 344, ../sass/_booking_popup.scss */
.booking-data-popup .fancybox-close:before {
  content: "x";
  color: white;
  font-size: 85px;
  font-family: "4";
  line-height: 36px;
}

/* line 353, ../sass/_booking_popup.scss */
.contact_bottom_popup {
  display: block;
  width: 535px;
  margin: 20px auto;
  font-family: "4";
  text-align: center;
  color: white;
}
/* line 361, ../sass/_booking_popup.scss */
.contact_bottom_popup .phone_hotel_booking_popup, .contact_bottom_popup .email_hotel {
  display: inline-block;
  padding-left: 30px;
  line-height: 25px;
}
/* line 367, ../sass/_booking_popup.scss */
.contact_bottom_popup .phone_hotel_booking_popup {
  margin-right: 10px;
  background: url(/img/onhol/booking_icos/phone_ico.png) no-repeat left center;
}
/* line 372, ../sass/_booking_popup.scss */
.contact_bottom_popup .email_hotel {
  background: url(/img/onhol/booking_icos/mail_ico.png) no-repeat left center;
}
