@import url(//fonts.googleapis.com/css?family=Montserrat|Source+Sans+Pro:400,300,700,600);
@font-face {
  font-family: 'icomoon';
  src: url("/static_1/fonts/iconmoon/icomoon.eot?ytm7g4");
  src: url("/static_1/fonts/iconmoon/icomoon.eot?ytm7g4#iefix") format("embedded-opentype"), url("/static_1/fonts/iconmoon/icomoon.ttf?ytm7g4") format("truetype"), url("/static_1/fonts/iconmoon/icomoon.woff?ytm7g4") format("woff"), url("/static_1/fonts/iconmoon/icomoon.svg?ytm7g4#icomoon") format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
/* line 13, ../../../../sass/plugins/_iconmoon.scss */
[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* line 28, ../../../../sass/plugins/_iconmoon.scss */
.icon-terrace:before {
  content: "\ea52";
}

/* line 31, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-hotelmanager:before {
  content: "\ea4c";
}

/* line 34, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-paritymaker:before {
  content: "\ea4d";
}

/* line 37, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-priceseeker:before {
  content: "\ea4e";
}

/* line 40, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-ratecheck:before {
  content: "\ea4f";
}

/* line 43, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-rescueseeker:before {
  content: "\ea50";
}

/* line 46, ../../../../sass/plugins/_iconmoon.scss */
.icon-ico-reviewseeker:before {
  content: "\ea51";
}

/* line 49, ../../../../sass/plugins/_iconmoon.scss */
.icon-couponlong:before {
  content: "\ea4a";
}

/* line 52, ../../../../sass/plugins/_iconmoon.scss */
.icon-coupon:before {
  content: "\ea4b";
}

/* line 55, ../../../../sass/plugins/_iconmoon.scss */
.icon-cocktel:before {
  content: "\ea46";
}

/* line 58, ../../../../sass/plugins/_iconmoon.scss */
.icon-gafas:before {
  content: "\ea47";
}

/* line 61, ../../../../sass/plugins/_iconmoon.scss */
.icon-pelota:before {
  content: "\ea48";
}

/* line 64, ../../../../sass/plugins/_iconmoon.scss */
.icon-piscina:before {
  content: "\ea49";
}

/* line 67, ../../../../sass/plugins/_iconmoon.scss */
.icon-email2:before {
  content: "\ea43";
}

/* line 70, ../../../../sass/plugins/_iconmoon.scss */
.icon-luggage2:before {
  content: "\ea44";
}

/* line 73, ../../../../sass/plugins/_iconmoon.scss */
.icon-nodisturb:before {
  content: "\ea45";
}

/* line 76, ../../../../sass/plugins/_iconmoon.scss */
.icon-percent:before {
  content: "\61";
}

/* line 79, ../../../../sass/plugins/_iconmoon.scss */
.icon-bed:before {
  content: "\62";
}

/* line 82, ../../../../sass/plugins/_iconmoon.scss */
.icon-dots:before {
  content: "\63";
}

/* line 85, ../../../../sass/plugins/_iconmoon.scss */
.icon-buffet:before {
  content: "\e900";
}

/* line 88, ../../../../sass/plugins/_iconmoon.scss */
.icon-zen:before {
  content: "\e901";
}

/* line 91, ../../../../sass/plugins/_iconmoon.scss */
.icon-drink:before {
  content: "\e902";
}

/* line 94, ../../../../sass/plugins/_iconmoon.scss */
.icon-bike:before {
  content: "\e903";
}

/* line 97, ../../../../sass/plugins/_iconmoon.scss */
.icon-celiac:before {
  content: "\e904";
}

/* line 100, ../../../../sass/plugins/_iconmoon.scss */
.icon-chart:before {
  content: "\e905";
}

/* line 103, ../../../../sass/plugins/_iconmoon.scss */
.icon-chips:before {
  content: "\e906";
}

/* line 106, ../../../../sass/plugins/_iconmoon.scss */
.icon-clock:before {
  content: "\e907";
}

/* line 109, ../../../../sass/plugins/_iconmoon.scss */
.icon-download:before {
  content: "\e908";
}

/* line 112, ../../../../sass/plugins/_iconmoon.scss */
.icon-friends:before {
  content: "\e909";
}

/* line 115, ../../../../sass/plugins/_iconmoon.scss */
.icon-group:before {
  content: "\e90a";
}

/* line 118, ../../../../sass/plugins/_iconmoon.scss */
.icon-headset:before {
  content: "\e90b";
}

/* line 121, ../../../../sass/plugins/_iconmoon.scss */
.icon-hipster:before {
  content: "\e90c";
}

/* line 124, ../../../../sass/plugins/_iconmoon.scss */
.icon-lamp:before {
  content: "\e90d";
}

/* line 127, ../../../../sass/plugins/_iconmoon.scss */
.icon-like:before {
  content: "\e90e";
}

/* line 130, ../../../../sass/plugins/_iconmoon.scss */
.icon-map:before {
  content: "\e90f";
}

/* line 133, ../../../../sass/plugins/_iconmoon.scss */
.icon-men:before {
  content: "\e910";
}

/* line 136, ../../../../sass/plugins/_iconmoon.scss */
.icon-monument:before {
  content: "\e911";
}

/* line 139, ../../../../sass/plugins/_iconmoon.scss */
.icon-new:before {
  content: "\e912";
}

/* line 142, ../../../../sass/plugins/_iconmoon.scss */
.icon-pig:before {
  content: "\e913";
}

/* line 145, ../../../../sass/plugins/_iconmoon.scss */
.icon-pdf:before {
  content: "\e914";
}

/* line 148, ../../../../sass/plugins/_iconmoon.scss */
.icon-play:before {
  content: "\e915";
}

/* line 151, ../../../../sass/plugins/_iconmoon.scss */
.icon-row:before {
  content: "\e916";
}

/* line 154, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE917:before {
  content: "\e917";
}

/* line 157, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE918:before {
  content: "\e918";
}

/* line 160, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE919:before {
  content: "\e919";
}

/* line 163, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE91A:before {
  content: "\e91a";
}

/* line 166, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE91B:before {
  content: "\e91b";
}

/* line 169, ../../../../sass/plugins/_iconmoon.scss */
.icon-uniE91C:before {
  content: "\e91c";
}

/* line 172, ../../../../sass/plugins/_iconmoon.scss */
.icon-sea:before {
  content: "\e91d";
}

/* line 175, ../../../../sass/plugins/_iconmoon.scss */
.icon-slide:before {
  content: "\e91e";
}

/* line 178, ../../../../sass/plugins/_iconmoon.scss */
.icon-smile:before {
  content: "\e91f";
}

/* line 181, ../../../../sass/plugins/_iconmoon.scss */
.icon-tick:before {
  content: "\e920";
}

/* line 184, ../../../../sass/plugins/_iconmoon.scss */
.icon-ticket:before {
  content: "\e921";
}

/* line 187, ../../../../sass/plugins/_iconmoon.scss */
.icon-trees:before {
  content: "\e922";
}

/* line 190, ../../../../sass/plugins/_iconmoon.scss */
.icon-upgrade:before {
  content: "\e923";
}

/* line 193, ../../../../sass/plugins/_iconmoon.scss */
.icon-watergame:before {
  content: "\e924";
}

/* line 196, ../../../../sass/plugins/_iconmoon.scss */
.icon-wedding:before {
  content: "\e925";
}

/* line 199, ../../../../sass/plugins/_iconmoon.scss */
.icon-basketball:before {
  content: "\e926";
}

/* line 202, ../../../../sass/plugins/_iconmoon.scss */
.icon-books:before {
  content: "\e927";
}

/* line 205, ../../../../sass/plugins/_iconmoon.scss */
.icon-calendar:before {
  content: "\e928";
}

/* line 208, ../../../../sass/plugins/_iconmoon.scss */
.icon-candles:before {
  content: "\e929";
}

/* line 211, ../../../../sass/plugins/_iconmoon.scss */
.icon-coins:before {
  content: "\e92a";
}

/* line 214, ../../../../sass/plugins/_iconmoon.scss */
.icon-cup:before {
  content: "\e92b";
}

/* line 217, ../../../../sass/plugins/_iconmoon.scss */
.icon-cutlery:before {
  content: "\e92c";
}

/* line 220, ../../../../sass/plugins/_iconmoon.scss */
.icon-dice:before {
  content: "\e92d";
}

/* line 223, ../../../../sass/plugins/_iconmoon.scss */
.icon-doc:before {
  content: "\e92e";
}

/* line 226, ../../../../sass/plugins/_iconmoon.scss */
.icon-email:before {
  content: "\e92f";
}

/* line 229, ../../../../sass/plugins/_iconmoon.scss */
.icon-euro:before {
  content: "\e930";
}

/* line 232, ../../../../sass/plugins/_iconmoon.scss */
.icon-info:before {
  content: "\e931";
}

/* line 235, ../../../../sass/plugins/_iconmoon.scss */
.icon-light:before {
  content: "\e932";
}

/* line 238, ../../../../sass/plugins/_iconmoon.scss */
.icon-night:before {
  content: "\e933";
}

/* line 241, ../../../../sass/plugins/_iconmoon.scss */
.icon-pet:before {
  content: "\e934";
}

/* line 244, ../../../../sass/plugins/_iconmoon.scss */
.icon-shell:before {
  content: "\e935";
}

/* line 247, ../../../../sass/plugins/_iconmoon.scss */
.icon-sofa:before {
  content: "\e936";
}

/* line 250, ../../../../sass/plugins/_iconmoon.scss */
.icon-star:before {
  content: "\e937";
}

/* line 253, ../../../../sass/plugins/_iconmoon.scss */
.icon-user:before {
  content: "\e938";
}

/* line 256, ../../../../sass/plugins/_iconmoon.scss */
.icon-wii:before {
  content: "\e939";
}

/* line 259, ../../../../sass/plugins/_iconmoon.scss */
.icon-ball:before {
  content: "\e93a";
}

/* line 262, ../../../../sass/plugins/_iconmoon.scss */
.icon-booking:before {
  content: "\e93b";
}

/* line 265, ../../../../sass/plugins/_iconmoon.scss */
.icon-cleanset:before {
  content: "\e93c";
}

/* line 268, ../../../../sass/plugins/_iconmoon.scss */
.icon-comment:before {
  content: "\e93d";
}

/* line 271, ../../../../sass/plugins/_iconmoon.scss */
.icon-ethernet:before {
  content: "\e93e";
}

/* line 274, ../../../../sass/plugins/_iconmoon.scss */
.icon-eye:before {
  content: "\e93f";
}

/* line 277, ../../../../sass/plugins/_iconmoon.scss */
.icon-feet:before {
  content: "\e940";
}

/* line 280, ../../../../sass/plugins/_iconmoon.scss */
.icon-fridge:before {
  content: "\e941";
}

/* line 283, ../../../../sass/plugins/_iconmoon.scss */
.icon-hairdrier:before {
  content: "\e942";
}

/* line 286, ../../../../sass/plugins/_iconmoon.scss */
.icon-handicap:before {
  content: "\e943";
}

/* line 289, ../../../../sass/plugins/_iconmoon.scss */
.icon-iron:before {
  content: "\e944";
}

/* line 292, ../../../../sass/plugins/_iconmoon.scss */
.icon-key:before {
  content: "\e945";
}

/* line 295, ../../../../sass/plugins/_iconmoon.scss */
.icon-lift:before {
  content: "\e946";
}

/* line 298, ../../../../sass/plugins/_iconmoon.scss */
.icon-mapmarker:before {
  content: "\e947";
}

/* line 301, ../../../../sass/plugins/_iconmoon.scss */
.icon-mask:before {
  content: "\e948";
}

/* line 304, ../../../../sass/plugins/_iconmoon.scss */
.icon-mouse:before {
  content: "\e949";
}

/* line 307, ../../../../sass/plugins/_iconmoon.scss */
.icon-movie:before {
  content: "\e94a";
}

/* line 310, ../../../../sass/plugins/_iconmoon.scss */
.icon-mug:before {
  content: "\e94b";
}

/* line 313, ../../../../sass/plugins/_iconmoon.scss */
.icon-plug:before {
  content: "\e94c";
}

/* line 316, ../../../../sass/plugins/_iconmoon.scss */
.icon-plus:before {
  content: "\e94d";
}

/* line 319, ../../../../sass/plugins/_iconmoon.scss */
.icon-printer:before {
  content: "\e94e";
}

/* line 322, ../../../../sass/plugins/_iconmoon.scss */
.icon-sack:before {
  content: "\e94f";
}

/* line 325, ../../../../sass/plugins/_iconmoon.scss */
.icon-shower:before {
  content: "\e950";
}

/* line 328, ../../../../sass/plugins/_iconmoon.scss */
.icon-solarium:before {
  content: "\e951";
}

/* line 331, ../../../../sass/plugins/_iconmoon.scss */
.icon-tenis:before {
  content: "\e952";
}

/* line 334, ../../../../sass/plugins/_iconmoon.scss */
.icon-tv:before {
  content: "\e953";
}

/* line 337, ../../../../sass/plugins/_iconmoon.scss */
.icon-window:before {
  content: "\e954";
}

/* line 340, ../../../../sass/plugins/_iconmoon.scss */
.icon-apple:before {
  content: "\e955";
}

/* line 343, ../../../../sass/plugins/_iconmoon.scss */
.icon-bathrobe:before {
  content: "\e956";
}

/* line 346, ../../../../sass/plugins/_iconmoon.scss */
.icon-bell:before {
  content: "\e957";
}

/* line 349, ../../../../sass/plugins/_iconmoon.scss */
.icon-building:before {
  content: "\e958";
}

/* line 352, ../../../../sass/plugins/_iconmoon.scss */
.icon-car:before {
  content: "\e959";
}

/* line 355, ../../../../sass/plugins/_iconmoon.scss */
.icon-cigar:before {
  content: "\e95a";
}

/* line 358, ../../../../sass/plugins/_iconmoon.scss */
.icon-comments:before {
  content: "\e95b";
}

/* line 361, ../../../../sass/plugins/_iconmoon.scss */
.icon-coolheart:before {
  content: "\e95c";
}

/* line 364, ../../../../sass/plugins/_iconmoon.scss */
.icon-cupboard:before {
  content: "\e95d";
}

/* line 367, ../../../../sass/plugins/_iconmoon.scss */
.icon-dimensions:before {
  content: "\e95e";
}

/* line 370, ../../../../sass/plugins/_iconmoon.scss */
.icon-family:before {
  content: "\e95f";
}

/* line 373, ../../../../sass/plugins/_iconmoon.scss */
.icon-flattv:before {
  content: "\e960";
}

/* line 376, ../../../../sass/plugins/_iconmoon.scss */
.icon-formaluser:before {
  content: "\e961";
}

/* line 379, ../../../../sass/plugins/_iconmoon.scss */
.icon-guarantee:before {
  content: "\e962";
}

/* line 382, ../../../../sass/plugins/_iconmoon.scss */
.icon-gift:before {
  content: "\e963";
}

/* line 385, ../../../../sass/plugins/_iconmoon.scss */
.icon-lock:before {
  content: "\e964";
}

/* line 388, ../../../../sass/plugins/_iconmoon.scss */
.icon-movie2:before {
  content: "\e965";
}

/* line 391, ../../../../sass/plugins/_iconmoon.scss */
.icon-picasa:before {
  content: "\e966";
}

/* line 394, ../../../../sass/plugins/_iconmoon.scss */
.icon-roulette:before {
  content: "\e967";
}

/* line 397, ../../../../sass/plugins/_iconmoon.scss */
.icon-sauna:before {
  content: "\e968";
}

/* line 400, ../../../../sass/plugins/_iconmoon.scss */
.icon-shower2:before {
  content: "\e969";
}

/* line 403, ../../../../sass/plugins/_iconmoon.scss */
.icon-singlebed:before {
  content: "\e96a";
}

/* line 406, ../../../../sass/plugins/_iconmoon.scss */
.icon-ski:before {
  content: "\e96b";
}

/* line 409, ../../../../sass/plugins/_iconmoon.scss */
.icon-smartphone:before {
  content: "\e96c";
}

/* line 412, ../../../../sass/plugins/_iconmoon.scss */
.icon-student:before {
  content: "\e96d";
}

/* line 415, ../../../../sass/plugins/_iconmoon.scss */
.icon-thermometer:before {
  content: "\e96e";
}

/* line 418, ../../../../sass/plugins/_iconmoon.scss */
.icon-washer:before {
  content: "\e96f";
}

/* line 421, ../../../../sass/plugins/_iconmoon.scss */
.icon-drinks:before {
  content: "\e970";
}

/* line 424, ../../../../sass/plugins/_iconmoon.scss */
.icon-drinks2:before {
  content: "\e971";
}

/* line 427, ../../../../sass/plugins/_iconmoon.scss */
.icon-airconditioner:before {
  content: "\e972";
}

/* line 430, ../../../../sass/plugins/_iconmoon.scss */
.icon-arrowdown:before {
  content: "\e973";
}

/* line 433, ../../../../sass/plugins/_iconmoon.scss */
.icon-arrowleft:before {
  content: "\e974";
}

/* line 436, ../../../../sass/plugins/_iconmoon.scss */
.icon-arrowright:before {
  content: "\e975";
}

/* line 439, ../../../../sass/plugins/_iconmoon.scss */
.icon-arrowup:before {
  content: "\e976";
}

/* line 442, ../../../../sass/plugins/_iconmoon.scss */
.icon-bag:before {
  content: "\e977";
}

/* line 445, ../../../../sass/plugins/_iconmoon.scss */
.icon-bike2:before {
  content: "\e978";
}

/* line 448, ../../../../sass/plugins/_iconmoon.scss */
.icon-biker:before {
  content: "\e979";
}

/* line 451, ../../../../sass/plugins/_iconmoon.scss */
.icon-briefcase:before {
  content: "\e97a";
}

/* line 454, ../../../../sass/plugins/_iconmoon.scss */
.icon-card:before {
  content: "\e97b";
}

/* line 457, ../../../../sass/plugins/_iconmoon.scss */
.icon-cocktail:before {
  content: "\e97c";
}

/* line 460, ../../../../sass/plugins/_iconmoon.scss */
.icon-cooker:before {
  content: "\e97d";
}

/* line 463, ../../../../sass/plugins/_iconmoon.scss */
.icon-drop:before {
  content: "\e97e";
}

/* line 466, ../../../../sass/plugins/_iconmoon.scss */
.icon-gym:before {
  content: "\e97f";
}

/* line 469, ../../../../sass/plugins/_iconmoon.scss */
.icon-info2:before {
  content: "\e980";
}

/* line 472, ../../../../sass/plugins/_iconmoon.scss */
.icon-massage:before {
  content: "\e981";
}

/* line 475, ../../../../sass/plugins/_iconmoon.scss */
.icon-moon:before {
  content: "\e982";
}

/* line 478, ../../../../sass/plugins/_iconmoon.scss */
.icon-music:before {
  content: "\e983";
}

/* line 481, ../../../../sass/plugins/_iconmoon.scss */
.icon-news:before {
  content: "\e984";
}

/* line 484, ../../../../sass/plugins/_iconmoon.scss */
.icon-nosmoke:before {
  content: "\e985";
}

/* line 487, ../../../../sass/plugins/_iconmoon.scss */
.icon-parking:before {
  content: "\e986";
}

/* line 490, ../../../../sass/plugins/_iconmoon.scss */
.icon-phone1:before {
  content: "\e987";
}

/* line 493, ../../../../sass/plugins/_iconmoon.scss */
.icon-phone2:before {
  content: "\e988";
}

/* line 496, ../../../../sass/plugins/_iconmoon.scss */
.icon-pictures:before {
  content: "\e989";
}

/* line 499, ../../../../sass/plugins/_iconmoon.scss */
.icon-plane:before {
  content: "\e98a";
}

/* line 502, ../../../../sass/plugins/_iconmoon.scss */
.icon-shield:before {
  content: "\e98b";
}

/* line 505, ../../../../sass/plugins/_iconmoon.scss */
.icon-spa:before {
  content: "\e98c";
}

/* line 508, ../../../../sass/plugins/_iconmoon.scss */
.icon-sun:before {
  content: "\e98d";
}

/* line 511, ../../../../sass/plugins/_iconmoon.scss */
.icon-ball2:before {
  content: "\e98e";
}

/* line 514, ../../../../sass/plugins/_iconmoon.scss */
.icon-bubbles:before {
  content: "\e98f";
}

/* line 517, ../../../../sass/plugins/_iconmoon.scss */
.icon-cot:before {
  content: "\e990";
}

/* line 520, ../../../../sass/plugins/_iconmoon.scss */
.icon-cutlery2:before {
  content: "\e991";
}

/* line 523, ../../../../sass/plugins/_iconmoon.scss */
.icon-golfplayer:before {
  content: "\e992";
}

/* line 526, ../../../../sass/plugins/_iconmoon.scss */
.icon-heart:before {
  content: "\e993";
}

/* line 529, ../../../../sass/plugins/_iconmoon.scss */
.icon-towels:before {
  content: "\e994";
}

/* line 532, ../../../../sass/plugins/_iconmoon.scss */
.icon-tree:before {
  content: "\e995";
}

/* line 535, ../../../../sass/plugins/_iconmoon.scss */
.icon-wifi:before {
  content: "\e996";
}

/* line 538, ../../../../sass/plugins/_iconmoon.scss */
.icon-alarmclock:before {
  content: "\e997";
}

/* line 541, ../../../../sass/plugins/_iconmoon.scss */
.icon-amenities:before {
  content: "\e998";
}

/* line 544, ../../../../sass/plugins/_iconmoon.scss */
.icon-astronomy:before {
  content: "\e999";
}

/* line 547, ../../../../sass/plugins/_iconmoon.scss */
.icon-barbecue:before {
  content: "\e99a";
}

/* line 550, ../../../../sass/plugins/_iconmoon.scss */
.icon-bells:before {
  content: "\e99b";
}

/* line 553, ../../../../sass/plugins/_iconmoon.scss */
.icon-bottle:before {
  content: "\e99c";
}

/* line 556, ../../../../sass/plugins/_iconmoon.scss */
.icon-bottle2:before {
  content: "\e99d";
}

/* line 559, ../../../../sass/plugins/_iconmoon.scss */
.icon-breakfast:before {
  content: "\e99e";
}

/* line 562, ../../../../sass/plugins/_iconmoon.scss */
.icon-broom:before {
  content: "\e99f";
}

/* line 565, ../../../../sass/plugins/_iconmoon.scss */
.icon-buffet2:before {
  content: "\e9a0";
}

/* line 568, ../../../../sass/plugins/_iconmoon.scss */
.icon-calendar2:before {
  content: "\e9a1";
}

/* line 571, ../../../../sass/plugins/_iconmoon.scss */
.icon-camera:before {
  content: "\e9a2";
}

/* line 574, ../../../../sass/plugins/_iconmoon.scss */
.icon-camera2:before {
  content: "\e9a3";
}

/* line 577, ../../../../sass/plugins/_iconmoon.scss */
.icon-caravan:before {
  content: "\e9a4";
}

/* line 580, ../../../../sass/plugins/_iconmoon.scss */
.icon-champagne:before {
  content: "\e9a5";
}

/* line 583, ../../../../sass/plugins/_iconmoon.scss */
.icon-chocolate:before {
  content: "\e9a6";
}

/* line 586, ../../../../sass/plugins/_iconmoon.scss */
.icon-christmasbauble:before {
  content: "\e9a7";
}

/* line 589, ../../../../sass/plugins/_iconmoon.scss */
.icon-christmasbauble2:before {
  content: "\e9a8";
}

/* line 592, ../../../../sass/plugins/_iconmoon.scss */
.icon-christmastree:before {
  content: "\e9a9";
}

/* line 595, ../../../../sass/plugins/_iconmoon.scss */
.icon-christmastree2:before {
  content: "\e9aa";
}

/* line 598, ../../../../sass/plugins/_iconmoon.scss */
.icon-chronometer:before {
  content: "\e9ab";
}

/* line 601, ../../../../sass/plugins/_iconmoon.scss */
.icon-clic:before {
  content: "\e9ac";
}

/* line 604, ../../../../sass/plugins/_iconmoon.scss */
.icon-cocktail2:before {
  content: "\e9ad";
}

/* line 607, ../../../../sass/plugins/_iconmoon.scss */
.icon-coffee:before {
  content: "\e9ae";
}

/* line 610, ../../../../sass/plugins/_iconmoon.scss */
.icon-coffee2:before {
  content: "\e9af";
}

/* line 613, ../../../../sass/plugins/_iconmoon.scss */
.icon-comment2:before {
  content: "\e9b0";
}

/* line 616, ../../../../sass/plugins/_iconmoon.scss */
.icon-comment3:before {
  content: "\e9b1";
}

/* line 619, ../../../../sass/plugins/_iconmoon.scss */
.icon-comments2:before {
  content: "\e9b2";
}

/* line 622, ../../../../sass/plugins/_iconmoon.scss */
.icon-cushions:before {
  content: "\e9b3";
}

/* line 625, ../../../../sass/plugins/_iconmoon.scss */
.icon-cushions2:before {
  content: "\e9b4";
}

/* line 628, ../../../../sass/plugins/_iconmoon.scss */
.icon-deaf:before {
  content: "\e9b5";
}

/* line 631, ../../../../sass/plugins/_iconmoon.scss */
.icon-design:before {
  content: "\e9b6";
}

/* line 634, ../../../../sass/plugins/_iconmoon.scss */
.icon-desktop:before {
  content: "\e9b7";
}

/* line 637, ../../../../sass/plugins/_iconmoon.scss */
.icon-dishes:before {
  content: "\e9b8";
}

/* line 640, ../../../../sass/plugins/_iconmoon.scss */
.icon-dollar:before {
  content: "\e9b9";
}

/* line 643, ../../../../sass/plugins/_iconmoon.scss */
.icon-download2:before {
  content: "\e9ba";
}

/* line 646, ../../../../sass/plugins/_iconmoon.scss */
.icon-family2:before {
  content: "\e9bb";
}

/* line 649, ../../../../sass/plugins/_iconmoon.scss */
.icon-fireworks:before {
  content: "\e9bc";
}

/* line 652, ../../../../sass/plugins/_iconmoon.scss */
.icon-flipflops:before {
  content: "\e9bd";
}

/* line 655, ../../../../sass/plugins/_iconmoon.scss */
.icon-friends2:before {
  content: "\e9be";
}

/* line 658, ../../../../sass/plugins/_iconmoon.scss */
.icon-fruit:before {
  content: "\e9bf";
}

/* line 661, ../../../../sass/plugins/_iconmoon.scss */
.icon-gender:before {
  content: "\e9c0";
}

/* line 664, ../../../../sass/plugins/_iconmoon.scss */
.icon-gift2:before {
  content: "\e9c1";
}

/* line 667, ../../../../sass/plugins/_iconmoon.scss */
.icon-gifts:before {
  content: "\e9c2";
}

/* line 670, ../../../../sass/plugins/_iconmoon.scss */
.icon-gold:before {
  content: "\e9c3";
}

/* line 673, ../../../../sass/plugins/_iconmoon.scss */
.icon-hairdrier2:before {
  content: "\e9c4";
}

/* line 676, ../../../../sass/plugins/_iconmoon.scss */
.icon-hammock:before {
  content: "\e9c5";
}

/* line 679, ../../../../sass/plugins/_iconmoon.scss */
.icon-hammock2:before {
  content: "\e9c6";
}

/* line 682, ../../../../sass/plugins/_iconmoon.scss */
.icon-heart2:before {
  content: "\e9c7";
}

/* line 685, ../../../../sass/plugins/_iconmoon.scss */
.icon-homepage:before {
  content: "\e9c8";
}

/* line 688, ../../../../sass/plugins/_iconmoon.scss */
.icon-hotel:before {
  content: "\e9c9";
}

/* line 691, ../../../../sass/plugins/_iconmoon.scss */
.icon-ice:before {
  content: "\e9ca";
}

/* line 694, ../../../../sass/plugins/_iconmoon.scss */
.icon-kettle:before {
  content: "\e9cb";
}

/* line 697, ../../../../sass/plugins/_iconmoon.scss */
.icon-kitchen:before {
  content: "\e9cc";
}

/* line 700, ../../../../sass/plugins/_iconmoon.scss */
.icon-latecheckout:before {
  content: "\e9cd";
}

/* line 703, ../../../../sass/plugins/_iconmoon.scss */
.icon-luggage:before {
  content: "\e9ce";
}

/* line 706, ../../../../sass/plugins/_iconmoon.scss */
.icon-meeting:before {
  content: "\e9cf";
}

/* line 709, ../../../../sass/plugins/_iconmoon.scss */
.icon-microwave:before {
  content: "\e9d0";
}

/* line 712, ../../../../sass/plugins/_iconmoon.scss */
.icon-monument2:before {
  content: "\e9d1";
}

/* line 715, ../../../../sass/plugins/_iconmoon.scss */
.icon-mouse1:before {
  content: "\e9d2";
}

/* line 718, ../../../../sass/plugins/_iconmoon.scss */
.icon-nani:before {
  content: "\e9d3";
}

/* line 721, ../../../../sass/plugins/_iconmoon.scss */
.icon-offer2:before {
  content: "\e9d4";
}

/* line 724, ../../../../sass/plugins/_iconmoon.scss */
.icon-offers:before {
  content: "\e9d5";
}

/* line 727, ../../../../sass/plugins/_iconmoon.scss */
.icon-percent2:before {
  content: "\e9d6";
}

/* line 730, ../../../../sass/plugins/_iconmoon.scss */
.icon-player:before {
  content: "\e9d7";
}

/* line 733, ../../../../sass/plugins/_iconmoon.scss */
.icon-romantic:before {
  content: "\e9d8";
}

/* line 736, ../../../../sass/plugins/_iconmoon.scss */
.icon-roomservice:before {
  content: "\e9d9";
}

/* line 739, ../../../../sass/plugins/_iconmoon.scss */
.icon-santa:before {
  content: "\e9da";
}

/* line 742, ../../../../sass/plugins/_iconmoon.scss */
.icon-satellite:before {
  content: "\e9db";
}

/* line 745, ../../../../sass/plugins/_iconmoon.scss */
.icon-sea2:before {
  content: "\e9dc";
}

/* line 748, ../../../../sass/plugins/_iconmoon.scss */
.icon-settings:before {
  content: "\e9dd";
}

/* line 751, ../../../../sass/plugins/_iconmoon.scss */
.icon-shield2:before {
  content: "\e9de";
}

/* line 754, ../../../../sass/plugins/_iconmoon.scss */
.icon-slippers:before {
  content: "\e9df";
}

/* line 757, ../../../../sass/plugins/_iconmoon.scss */
.icon-snowflakeeps:before {
  content: "\e9e0";
}

/* line 760, ../../../../sass/plugins/_iconmoon.scss */
.icon-soap:before {
  content: "\e9e1";
}

/* line 763, ../../../../sass/plugins/_iconmoon.scss */
.icon-sofa2:before {
  content: "\e9e2";
}

/* line 766, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialbreakfast:before {
  content: "\e9e3";
}

/* line 769, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialfamily:before {
  content: "\e9e4";
}

/* line 772, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialreception:before {
  content: "\e9e5";
}

/* line 775, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialshower:before {
  content: "\e9e6";
}

/* line 778, ../../../../sass/plugins/_iconmoon.scss */
.icon-suit:before {
  content: "\e9e7";
}

/* line 781, ../../../../sass/plugins/_iconmoon.scss */
.icon-sun2:before {
  content: "\e9e8";
}

/* line 784, ../../../../sass/plugins/_iconmoon.scss */
.icon-sun3:before {
  content: "\e9e9";
}

/* line 787, ../../../../sass/plugins/_iconmoon.scss */
.icon-tie:before {
  content: "\e9ea";
}

/* line 790, ../../../../sass/plugins/_iconmoon.scss */
.icon-toaster:before {
  content: "\e9eb";
}

/* line 793, ../../../../sass/plugins/_iconmoon.scss */
.icon-toilet:before {
  content: "\e9ec";
}

/* line 796, ../../../../sass/plugins/_iconmoon.scss */
.icon-washer2:before {
  content: "\e9ed";
}

/* line 799, ../../../../sass/plugins/_iconmoon.scss */
.icon-waterpark:before {
  content: "\e9ee";
}

/* line 802, ../../../../sass/plugins/_iconmoon.scss */
.icon-wine:before {
  content: "\e9ef";
}

/* line 805, ../../../../sass/plugins/_iconmoon.scss */
.icon-world:before {
  content: "\e9f0";
}

/* line 808, ../../../../sass/plugins/_iconmoon.scss */
.icon-www:before {
  content: "\e9f1";
}

/* line 811, ../../../../sass/plugins/_iconmoon.scss */
.icon-adults:before {
  content: "\e9f2";
}

/* line 814, ../../../../sass/plugins/_iconmoon.scss */
.icon-percentpig:before {
  content: "\e9f3";
}

/* line 817, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialwifi:before {
  content: "\e9f4";
}

/* line 820, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialbuilding:before {
  content: "\e9f5";
}

/* line 823, ../../../../sass/plugins/_iconmoon.scss */
.icon-speciallunch:before {
  content: "\e9f6";
}

/* line 826, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialweb:before {
  content: "\e9f7";
}

/* line 829, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialbed:before {
  content: "\e9f8";
}

/* line 832, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialevents:before {
  content: "\e9f9";
}

/* line 835, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialpool:before {
  content: "\e9fa";
}

/* line 838, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialbeds:before {
  content: "\e9fb";
}

/* line 841, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialcalendar:before {
  content: "\e9fc";
}

/* line 844, ../../../../sass/plugins/_iconmoon.scss */
.icon-specialfitness:before {
  content: "\e9fd";
}

/* line 847, ../../../../sass/plugins/_iconmoon.scss */
.icon-speciallocation:before {
  content: "\e9fe";
}

/* line 850, ../../../../sass/plugins/_iconmoon.scss */
.icon-settings2:before {
  content: "\e9ff";
}

/* line 853, ../../../../sass/plugins/_iconmoon.scss */
.icon-nopets:before {
  content: "\ea00";
}

/* line 856, ../../../../sass/plugins/_iconmoon.scss */
.icon-videocamera:before {
  content: "\ea01";
}

/* line 859, ../../../../sass/plugins/_iconmoon.scss */
.icon-window1:before {
  content: "\ea02";
}

/* line 862, ../../../../sass/plugins/_iconmoon.scss */
.icon-offer:before {
  content: "\ea03";
}

/* line 865, ../../../../sass/plugins/_iconmoon.scss */
.icon-save:before {
  content: "\ea04";
}

/* line 868, ../../../../sass/plugins/_iconmoon.scss */
.icon-plane2:before {
  content: "\ea05";
}

/* line 871, ../../../../sass/plugins/_iconmoon.scss */
.icon-longarrow:before {
  content: "\ea06";
}

/* line 874, ../../../../sass/plugins/_iconmoon.scss */
.icon-paraty:before {
  content: "\ea07";
}

/* line 877, ../../../../sass/plugins/_iconmoon.scss */
.icon-horseshoe:before {
  content: "\ea08";
}

/* line 880, ../../../../sass/plugins/_iconmoon.scss */
.icon-balloons:before {
  content: "\ea09";
}

/* line 883, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-tiger:before {
  content: "\ea0a";
}

/* line 886, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-2drinks:before {
  content: "\ea0b";
}

/* line 889, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-bbq:before {
  content: "\ea0c";
}

/* line 892, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-disco:before {
  content: "\ea0d";
}

/* line 895, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-pasta:before {
  content: "\ea0e";
}

/* line 898, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-snack:before {
  content: "\ea0f";
}

/* line 901, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-archery:before {
  content: "\ea10";
}

/* line 904, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-football:before {
  content: "\ea11";
}

/* line 907, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-gameboard:before {
  content: "\ea12";
}

/* line 910, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-golf:before {
  content: "\ea13";
}

/* line 913, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-hotbath:before {
  content: "\ea14";
}

/* line 916, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-hotpool:before {
  content: "\ea15";
}

/* line 919, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-iron:before {
  content: "\ea16";
}

/* line 922, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-jetshower:before {
  content: "\ea17";
}

/* line 925, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-massage:before {
  content: "\ea18";
}

/* line 928, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-petanque:before {
  content: "\ea19";
}

/* line 931, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-phone:before {
  content: "\ea1a";
}

/* line 934, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-shooting:before {
  content: "\ea1b";
}

/* line 937, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-volleyball:before {
  content: "\ea1c";
}

/* line 940, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-balloons:before {
  content: "\ea1d";
}

/* line 943, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-bike:before {
  content: "\ea1e";
}

/* line 946, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-gift:before {
  content: "\ea1f";
}

/* line 949, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-jacuzzi:before {
  content: "\ea20";
}

/* line 952, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-mouse:before {
  content: "\ea21";
}

/* line 955, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-movie:before {
  content: "\ea22";
}

/* line 958, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-playground:before {
  content: "\ea23";
}

/* line 961, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-playingcards:before {
  content: "\ea24";
}

/* line 964, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-shopping:before {
  content: "\ea25";
}

/* line 967, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-shower:before {
  content: "\ea26";
}

/* line 970, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-sofa:before {
  content: "\ea27";
}

/* line 973, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-washing:before {
  content: "\ea28";
}

/* line 976, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-bills:before {
  content: "\ea29";
}

/* line 979, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-car:before {
  content: "\ea2a";
}

/* line 982, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-creditcard:before {
  content: "\ea2b";
}

/* line 985, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-drink:before {
  content: "\ea2c";
}

/* line 988, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-fruit:before {
  content: "\ea2d";
}

/* line 991, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-lock:before {
  content: "\ea2e";
}

/* line 994, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-offer:before {
  content: "\ea2f";
}

/* line 997, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-spa:before {
  content: "\ea30";
}

/* line 1000, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-tennis:before {
  content: "\ea31";
}

/* line 1003, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-aircon:before {
  content: "\ea32";
}

/* line 1006, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-hairdryer:before {
  content: "\ea33";
}

/* line 1009, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-kitchen:before {
  content: "\ea34";
}

/* line 1012, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-tv:before {
  content: "\ea35";
}

/* line 1015, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-wifi:before {
  content: "\ea36";
}

/* line 1018, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-clock:before {
  content: "\ea37";
}

/* line 1021, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-lightning:before {
  content: "\ea38";
}

/* line 1024, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-piggybank:before {
  content: "\ea39";
}

/* line 1027, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-pricetag:before {
  content: "\ea3a";
}

/* line 1030, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-creditcard2:before {
  content: "\ea3b";
}

/* line 1033, ../../../../sass/plugins/_iconmoon.scss */
.icon-360:before {
  content: "\ea3c";
}

/* line 1036, ../../../../sass/plugins/_iconmoon.scss */
.icon-contactless:before {
  content: "\ea3d";
}

/* line 1039, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-umbrella:before {
  content: "\ea3e";
}

/* line 1042, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-sun:before {
  content: "\ea3f";
}

/* line 1045, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-slippers:before {
  content: "\ea40";
}

/* line 1048, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-mail:before {
  content: "\ea41";
}

/* line 1051, ../../../../sass/plugins/_iconmoon.scss */
.icon-line-comment:before {
  content: "\ea42";
}

/* line 3, ../../../../sass/booking/_booking_engine_5.scss */
.booking_widget {
  position: relative;
}

/* line 7, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 {
  width: 275px;
  display: block;
}
/* line 11, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 form.booking_form {
  padding: 0 !important;
}
/* line 15, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 .booking_form_title {
  background: #383838;
}

/* line 20, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper {
  border-bottom: 1px solid lightgray;
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat 92% 70% !important;
}
/* line 24, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
  display: inline-block;
  margin: 10px 0 0 10px;
}

/* line 32, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input {
  border: none;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 25px;
  font-weight: 600;
  background: white;
}
/* line 40, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input::-webkit-input-placeholder {
  color: #585d63;
}
/* line 44, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input:-moz-placeholder {
  /* Firefox 18- */
  color: #585d63;
}
/* line 48, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input::-moz-placeholder {
  /* Firefox 19+ */
  color: #585d63;
}
/* line 52, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input:-ms-input-placeholder {
  color: #585d63;
}

/* line 57, ../../../../sass/booking/_booking_engine_5.scss */
.entry_date_wrapper {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 66.6%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}

/* line 72, ../../../../sass/booking/_booking_engine_5.scss */
.departure_date_wrapper, .half_size {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 50%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}
/* line 86, ../../../../sass/booking/_booking_engine_5.scss */
.departure_date_wrapper .date_day, .half_size .date_day {
  font-size: 21px !important;
}

/* line 91, ../../../../sass/booking/_booking_engine_5.scss */
.nights_number_wrapper {
  float: right;
  height: 70px;
  border-bottom: 1px solid lightgrey;
  box-sizing: border-box;
  width: 33.3%;
  padding: 5px 10px;
}

/* line 100, ../../../../sass/booking/_booking_engine_5.scss */
.num_nights_label, .entry_date_label, .children_label, .rooms_label, .adults_label, .promocode_label, .departure_date_label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/* line 106, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 30%;
}

/*===== Entry date =====*/
/* line 113, ../../../../sass/booking/_booking_engine_5.scss */
.date_box.entry_date, .date_box.departure_date {
  font-size: 20px;
  background: url(/static_1/images/booking_5/calendar.png) no-repeat center right;
  margin-top: 9px;
  background-size: 29px;
}
/* line 119, ../../../../sass/booking/_booking_engine_5.scss */
.date_box.entry_date .date_day, .date_box.departure_date .date_day {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 25px;
  font-weight: 600;
}

/*====== Booking button ====*/
/* line 128, ../../../../sass/booking/_booking_engine_5.scss */
button.submit_button {
  width: 100%;
  border: 0;
  border-radius: 0;
  background: #ffd600;
  height: 55px;
  text-transform: uppercase;
  font-weight: bolder;
  font-size: 21px;
  color: #565656;
  cursor: pointer;
}

/*====== Rooms section ======*/
/* line 142, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 33.3%;
  padding: 7px 10px 5px;
  box-sizing: border-box;
}
/* line 149, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper .room_list_wrapper li.room {
  float: right;
  width: 69%;
}
/* line 155, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper .adults_selector {
  height: 70px;
  float: left;
  border-right: 1px solid gray;
  width: 46.5%;
}

/* line 163, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper {
  display: table;
  float: right;
  width: 66.6%;
}
/* line 168, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .room_title {
  display: none;
}
/* line 172, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .adults_selector {
  border-right: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
}
/* line 177, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
  width: 50%;
  display: inline-block;
  height: 70px;
  box-sizing: border-box;
  padding: 7px 10px 5px;
}
/* line 185, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .children_selector {
  width: 50% !important;
  padding: 7px 10px 5px;
  float: right;
}

/* line 192, ../../../../sass/booking/_booking_engine_5.scss */
.room_list .room {
  height: 70px;
}

/* line 196, ../../../../sass/booking/_booking_engine_5.scss */
.room_list .room.room3, .room_list .room.room2 {
  border-top: 1px solid lightgrey;
}

/*==== Promocode section  ====*/
/* line 201, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_wrapper {
  display: table;
  width: 100%;
  padding: 10px 10px 5px;
  border-top: 1px solid lightgrey;
  box-sizing: border-box;
}

/* line 209, ../../../../sass/booking/_booking_engine_5.scss */
label.promocode_label {
  display: block;
}

/* line 213, ../../../../sass/booking/_booking_engine_5.scss */
input.promocode_input {
  border: 0;
  width: 100%;
  display: block;
  height: 30px;
  font-size: 13px;
  font-weight: lighter;
  margin-top: 2px;
  font-family: sans-serif;
}

/*====== Selectric ======*/
/* line 225, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems {
  overflow: scroll !important;
  width: 92px !important;
  margin-left: -11px;
  margin-top: 2px;
}
/* line 231, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 237, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li {
  font-weight: 500;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAADAQMAAABs5if8AAAABlBMVEXOzs7y8vKNS741AAAADklEQVQI12M4wPSAWQAABoQBtgsaY5kAAAAASUVORK5CYII=);
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  line-height: 29px !important;
  font-size: 18px !important;
  font-family: 'Source Sans Pro', sans-serif;
}
/* line 247, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li:hover {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 254, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems ul {
  z-index: 40;
}
/* line 258, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems .room {
  padding-top: 17px;
}

/* line 264, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 .selectric .button {
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 23px;
  margin-left: 0;
  margin-top: 8px;
  text-indent: 999px;
  height: 22px;
  font-weight: 600;
}

/* line 277, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper .selectric .label {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 28px;
  margin-left: 0;
  font-weight: 600;
}

/* line 285, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper .selectric {
  margin-top: 3px;
}

/* line 289, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper {
  width: 78px !important;
}

/*======= Phone web support =======*/
/* line 294, ../../../../sass/booking/_booking_engine_5.scss */
.wrapper-new-web-support.booking_form_title {
  border-radius: 0;
  padding: 5px 10px;
  font-family: 'Montserrat', sans-serif;
  font-size: 11px !important;
  margin-top: 11px;
}

/* line 302, ../../../../sass/booking/_booking_engine_5.scss */
.wrapper-new-web-support .web_support_number, .web_support_label_1 {
  font-size: 11px !important;
  margin-left: 5px;
}

/* line 307, ../../../../sass/booking/_booking_engine_5.scss */
img.phone_support_image {
  float: left;
  width: 28px;
  margin-top: 1px;
  margin-right: 12px;
}

/*======= Header title ======*/
/* line 315, ../../../../sass/booking/_booking_engine_5.scss */
.booking_title_1, .booking_title_2, .best_price {
  display: none;
}

/* line 319, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header {
  font-family: 'Montserrat', sans-serif;
  padding: 10px;
  display: table;
  width: 100%;
  box-sizing: border-box;
  cursor: pointer;
}
/* line 327, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header:hover {
  opacity: 0.8;
}
/* line 331, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header img.booking_header_discount {
  float: left;
  margin-right: 15px;
}
/* line 336, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header p.first_offer_name {
  font-size: 12px;
  color: #8096c7;
  margin-top: 9px;
}
/* line 342, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header p.second_offer_name {
  font-size: 10px;
  margin-top: 3px;
  color: gray;
}

/* line 349, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker {
  width: 283px;
}
/* line 352, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker .ui-widget-header {
  background: none !important;
  border: 0;
}
/* line 356, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker .ui-widget-header .ui-datepicker-title {
  color: #646464 !important;
  font-family: Verdana, Arial, sans-serif;
  font-weight: 300;
}

/* line 365, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker th {
  font-weight: 300;
  font-size: 14px;
}
/* line 371, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker .ui-state-default, body .ui-datepicker .ui-widget-content .ui-state-default, body .ui-datepicker .ui-widget-header .ui-state-default {
  background: transparent !important;
  border: 0 !important;
  color: #646464 !important;
  font-weight: 400;
  font-family: Circular, "Helvetica Neue", Helvetica, Arial, sans-serif;
  text-align: center;
  font-size: 13px;
}
/* line 382, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content .ui-state-hover {
  border: 0;
  background: #4B4B4B !important;
  color: white !important;
}
/* line 388, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content {
  border: 0;
  border-radius: 0;
  padding-bottom: 40px;
}
/* line 394, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-state-default {
  padding: 8px;
}
/* line 398, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-start_date {
  opacity: 1 !important;
}
/* line 401, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-start_date .ui-state-default {
  border: 0;
  background: #346FEF !important;
  color: white !important;
}
/* line 408, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-highlighted .ui-state-default {
  border: 0;
  background: rgba(40, 96, 144, 0.25) !important;
}
/* line 413, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content .ui-state-active {
  border: 0;
  background: #346FEF !important;
  color: white !important;
}
/* line 419, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-e, body span.ui-icon.ui-icon-circle-triangle-w {
  /* background: url(/static_1/images/mobile_img/renovation/flecha_der.png) no-repeat center;*/
  background: none;
  text-indent: 0;
  color: transparent;
  font-size: 0;
}
/* line 425, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-e:before, body span.ui-icon.ui-icon-circle-triangle-w:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  content: '\f105';
  font-family: "FontAwesome", "Font Awesome 5 Pro", sans-serif;
  font-size: 20px;
  color: black;
}
/* line 442, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-w:before {
  content: '\f104';
}
/* line 447, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker .ui-datepicker-next, body .ui-datepicker .ui-datepicker-prev {
  background: none;
}
/* line 451, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-next.ui-state-hover, body .ui-datepicker-prev.ui-datepicker-prev-hover {
  border: 1px solid #636363;
}

/* line 457, ../../../../sass/booking/_booking_engine_5.scss */
.datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-e:before, .datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-w:before {
  display: none;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

/* line 469, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 {
  border-radius: 0;
}
/* line 471, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-skin {
  border-radius: 0;
}
/* line 474, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide > * {
  padding: 0 !important;
  background-color: transparent;
}
/* line 478, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide .calendar_popup_wrapper {
  background-color: transparent;
}
/* line 482, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .full_screen_engine .container_popup_booking {
  background-color: transparent !important;
  padding: 24px !important;
}
/* line 487, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  color: gray;
}
/* line 490, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading {
  width: 50px;
  height: 50px;
  margin: auto;
  position: absolute;
  left: 0;
  right: 0;
  top: 100px;
  bottom: 0;
}
/* line 10, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:before {
  content: '';
  width: 50px;
  height: 5px;
  background: #000;
  opacity: 0.3;
  position: absolute;
  top: 59px;
  left: 0;
  border-radius: 50%;
  animation: box_shadow .5s linear infinite;
}
/* line 22, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:after {
  content: '';
  width: 50px;
  height: 50px;
  background: white;
  animation: box_animate .5s linear infinite;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 3px;
}
@keyframes box_animate {
  17% {
    border-bottom-right-radius: 3px;
  }
  25% {
    transform: translateY(9px) rotate(22.5deg);
  }
  50% {
    transform: translateY(18px) scale(1, 0.9) rotate(45deg);
    border-bottom-right-radius: 40px;
  }
  75% {
    transform: translateY(9px) rotate(67.5deg);
  }
  100% {
    transform: translateY(0) rotate(90deg);
  }
}
@keyframes box_shadow {
  0%, 100% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.2, 1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader {
  padding: 60px 0 70px;
}
/* line 3, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot {
  width: 24px;
  height: 24px;
  margin: 0 3px;
  background: #000;
  border-radius: 100%;
  display: inline-block;
  animation: slide 1s infinite;
}
/* line 11, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(1) {
  animation-delay: 0.1s;
}
/* line 14, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(2) {
  animation-delay: 0.2s;
}
/* line 17, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(3) {
  animation-delay: 0.3s;
}
/* line 20, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(4) {
  animation-delay: 0.4s;
}
/* line 23, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(5) {
  animation-delay: 0.5s;
}
@-moz-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-webkit-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-o-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube {
  margin: 20px auto;
  width: 40px;
  height: 40px;
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}
/* line 7, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube {
  float: left;
  width: 50%;
  height: 50%;
  position: relative;
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}
/* line 15, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  -webkit-animation: sk-foldCubeAngle 2.4s infinite linear both;
  animation: sk-foldCubeAngle 2.4s infinite linear both;
  -webkit-transform-origin: 100% 100%;
  -ms-transform-origin: 100% 100%;
  transform-origin: 100% 100%;
}
/* line 30, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2 {
  -webkit-transform: scale(1.1) rotateZ(90deg);
  transform: scale(1.1) rotateZ(90deg);
}
/* line 34, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3 {
  -webkit-transform: scale(1.1) rotateZ(270deg);
  transform: scale(1.1) rotateZ(270deg);
}
/* line 38, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4 {
  -webkit-transform: scale(1.1) rotateZ(180deg);
  transform: scale(1.1) rotateZ(180deg);
}
/* line 43, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2:before {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
/* line 49, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3:before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
}
/* line 55, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4:before {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
}
@-webkit-keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
@keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
/* line 1, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots {
  padding: 40px 0;
  text-align: center;
  animation: dots-rotate 2s linear infinite;
}
/* line 5, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  content: '';
  animation: dots-margin 2s linear infinite;
}
/* line 9, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots span, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  display: inline-block;
  vertical-align: middle;
  width: 30px;
  height: 30px;
  background-color: white;
  border-radius: 50%;
  margin: 5px;
}
@keyframes dots-margin {
  0% {
    margin: 5px;
  }
  12% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  25% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  50% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  62% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  75% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  100% {
    margin: 5px;
  }
}
@keyframes dots-rotate {
  0% {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  25% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  50% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  75% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/* line 500, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_top_popup_booking {
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: lighter;
  color: gray;
}
/* line 508, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  font-weight: bold;
  text-transform: uppercase;
}
/* line 513, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .container_popup_booking {
  width: 555px;
  display: block;
  padding: 30px 0;
  box-sizing: border-box;
  margin: 7px;
  border: 1px solid #3483b2;
}

/* line 524, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine {
  width: auto !important;
  height: auto !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}
/* line 532, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-outer {
  background: transparent;
}
/* line 537, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine.custom_color_overlay .fancybox-skin {
  background: transparent;
}
/* line 542, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-skin {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}
/* line 551, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-inner {
  overflow: visible;
}
/* line 555, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 568, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  border: 0 !important;
}
/* line 572, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: lighter;
}
/* line 575, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking strong {
  font-weight: bold;
  text-decoration: underline;
  font-size: 14px;
}

/* line 1, ../sass/_booking_engine.scss */
#full_wrapper_booking {
  /*======== Booking Widget =======*/
}
/* line 3, ../sass/_booking_engine.scss */
#full_wrapper_booking div#wrapper_booking {
  position: absolute;
  bottom: 140px;
  left: 0px;
  right: 0px;
  z-index: 35;
  width: 1121px;
  padding: 10px;
  background: #797676;
}
/* line 13, ../sass/_booking_engine.scss */
#full_wrapper_booking div#wrapper_booking .promocode_header {
  display: none;
}
/* line 17, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget {
  position: absolute;
  left: 0px;
}
/* line 22, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 {
  width: 1121px;
}
/* line 26, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 form.booking_form {
  background: white;
}
/* line 30, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form_title .best_price {
  display: none;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}
/* line 38, ../sass/_booking_engine.scss */
#full_wrapper_booking .promocode_header p.first_offer_name {
  color: white;
}
/* line 41, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .date_box, #full_wrapper_booking .booking_widget .selectricWrapper, #full_wrapper_booking #booking_widget_popup .date_box, #full_wrapper_booking #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 45, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .date_box .date_day, #full_wrapper_booking #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 49, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectric {
  height: 38px;
  background: transparent;
}
/* line 54, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .adults_selector, #full_wrapper_booking .room_list_wrapper .children_selector {
  width: 49.8%;
}
/* line 58, ../sass/_booking_engine.scss */
#full_wrapper_booking .children_selector .children_label {
  font-size: 9px;
  width: 100px;
  margin-left: -5px;
}
/* line 64, ../sass/_booking_engine.scss */
#full_wrapper_booking button.submit_button {
  background: #346FEF !important;
  color: white !important;
}
/* line 69, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .web_support_label_1, #full_wrapper_booking .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 74, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support .web_support_number, #full_wrapper_booking .web_support_label_1 {
  line-height: 15px !important;
}
/* line 78, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title {
  background: gray !important;
  margin-top: 0;
  text-align: center;
  padding: 10px;
}
/* line 84, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title .web_support_label_1 {
  display: inline-block;
  vertical-align: middle;
}
/* line 89, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title .web_support_label_2 {
  display: inline-block;
  vertical-align: middle;
}
/* line 93, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title .web_support_label_2:before {
  display: none;
}
/* line 103, ../sass/_booking_engine.scss */
#full_wrapper_booking #data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}
/* line 107, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date {
  margin-top: 6px;
}
/* line 110, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectricWrapper .selectric {
  margin-top: 0px;
}
/* line 114, ../sass/_booking_engine.scss */
#full_wrapper_booking #slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}
/* line 118, ../sass/_booking_engine.scss */
#full_wrapper_booking .promocode_text {
  display: none;
}
/* line 122, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection {
  display: inline-block;
  vertical-align: top;
  float: left;
}
/* line 127, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection .entry_date_wrapper {
  display: inline-block;
  vertical-align: top;
  width: 192px;
  float: left;
  border-top: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
}
/* line 136, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection .nights_number_wrapper {
  display: inline-block;
  width: 95px;
  float: left;
  vertical-align: top;
  border-top: 1px solid lightgrey;
}
/* line 145, ../sass/_booking_engine.scss */
#full_wrapper_booking .rooms_number_wrapper {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 95px;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
}
/* line 155, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper {
  width: 190px;
  display: inline-block;
  vertical-align: top;
  float: left;
  border-bottom: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
  height: 68px;
}
/* line 164, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper ul.room_list {
  background: white;
}
/* line 167, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper ul.room_list .room {
  height: 68px;
}
/* line 173, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .adults_selector, #full_wrapper_booking .room_list_wrapper .children_selector {
  height: 68px;
}
/* line 177, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button {
  display: inline-block;
  width: 548px;
  float: left;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  height: 69px;
}
/* line 185, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .promocode_wrapper {
  display: inline-block;
  width: 285px;
  vertical-align: top;
  float: left;
  height: 70px;
}
/* line 193, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .submit_button {
  width: 270px;
  display: inline-block;
  vertical-align: top;
  float: left;
  height: 70px;
  border: 1px solid lightgrey;
}

/* line 204, ../sass/_booking_engine.scss */
#wrapper_booking_fancybox #booking_widget_popup #full-booking-engine-html-5 .booking_form_title .promocode_header {
  display: none !important;
}

/* line 209, ../sass/_booking_engine.scss */
.datepicker_wrapper_element .header_datepicker, .datepicker_wrapper_element_2 .header_datepicker, .datepicker_wrapper_element_3 .header_datepicker {
  background: #346FEF;
}

/* line 1, ../sass/_booking_widget_modal.scss */
.fancybox-wrap {
  /*width: auto !important;*/
  /*======== Booking Widget =======*/
}
/* line 4, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .fancybox-outer {
  padding: 0 !important;
}
/* line 8, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .fancybox-inner {
  width: auto !important;
  overflow: visible !important;
}
/* line 14, ../sass/_booking_widget_modal.scss */
.fancybox-wrap div#wrapper_booking {
  position: absolute;
  height: 420px;
  top: 145px;
  left: 0px;
  right: 0px;
  z-index: 35;
}
/* line 22, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget {
  position: absolute;
  left: 0px;
}
/* line 27, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 {
  width: 299px;
}
/* line 31, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 form.booking_form {
  background: white;
}
/* line 35, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_form_title .best_price {
  display: none;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}
/* line 43, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.first_offer_name {
  color: white;
}
/* line 46, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .date_box, .fancybox-wrap .booking_widget .selectricWrapper, .fancybox-wrap #booking_widget_popup .date_box, .fancybox-wrap #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 50, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .date_box .date_day, .fancybox-wrap #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 54, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .selectric {
  height: 38px;
  background: transparent;
}
/* line 59, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .room_list_wrapper .adults_selector, .fancybox-wrap .room_list_wrapper .children_selector {
  width: 49.8%;
}
/* line 63, ../sass/_booking_widget_modal.scss */
.fancybox-wrap button.submit_button {
  background: #FCD430 !important;
}
/* line 68, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .web_support_label_1, .fancybox-wrap .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 73, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .wrapper-new-web-support .web_support_number, .fancybox-wrap .web_support_label_1 {
  line-height: 15px !important;
}
/* line 77, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .wrapper-new-web-support.booking_form_title {
  background: gray !important;
}
/* line 81, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 {
  margin-top: 20px !important;
}
/* line 85, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}
/* line 89, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .date_box.entry_date {
  margin-top: 6px;
}
/* line 92, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .selectricWrapper .selectric {
  margin-top: 0px;
}
/* line 96, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}
/* line 100, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_text {
  display: none;
}

/* line 106, ../sass/_booking_widget_modal.scss */
#data .date_year {
  font-family: 'Source Sans Pro', sans-serif;
  color: #346FEF;
  font-size: 25px;
  font-weight: 600;
  display: none;
}
/* line 114, ../sass/_booking_widget_modal.scss */
#data .wrapper_booking_button {
  min-height: 258px;
}
/* line 118, ../sass/_booking_widget_modal.scss */
#data .spinner_wrapper {
  bottom: 31px;
  top: auto;
}

/*=====================================================================
  Selectric
======================================================================*/
/* line 5, ../../../../sass/booking/_selectric.scss */
.selectricWrapper {
  position: relative;
  margin: 0 0 0px;
  width: 80px;
  cursor: pointer;
}

/* line 12, ../../../../sass/booking/_selectric.scss */
.selectricDisabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 23, ../../../../sass/booking/_selectric.scss */
.selectricOpen {
  z-index: 9999;
}

/* line 27, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 31, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

/* line 38, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect select {
  position: absolute;
  left: -100%;
  display: none;
}

/* line 44, ../../../../sass/booking/_selectric.scss */
.selectricInput {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  _font: 0/0 a;
  background: none !important;
}

/* line 60, ../../../../sass/booking/_selectric.scss */
.selectricTempShow {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* line 67, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectric {
  z-index: 9999;
}

/* line 72, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 76, ../../../../sass/booking/_selectric.scss */
.selectric {
  background: white;
  position: relative;
  border-radius: 6px;
  height: 40px;
}

/* line 83, ../../../../sass/booking/_selectric.scss */
.selectric .label {
  display: block;
  overflow: hidden;
  font-size: 16px;
  line-height: 40px;
  color: #346FEF;
  text-align: left;
  margin-left: 20px;
}

/* line 93, ../../../../sass/booking/_selectric.scss */
.selectric .button {
  zoom: 1;
  position: absolute;
  font: 0/0 a;
  overflow: hidden;
  margin: auto;
  top: 0;
  right: 2px;
  bottom: 0;
  width: 35px;
  height: 35px;
  border: 0;
  background: #346FEF url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  padding: 0;
}

/* line 109, ../../../../sass/booking/_selectric.scss */
.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */
/* line 115, ../../../../sass/booking/_selectric.scss */
.selectricItems {
  display: none;
  position: absolute;
  overflow: hidden;
  top: 100%;
  left: 0;
  background: #F9F9F9;
  border: 1px solid #CCC;
  z-index: 9998;
  -webkit-box-shadow: 0 0 10px -6px;
  box-shadow: 0 0 10px -6px;
}

/* line 128, ../../../../sass/booking/_selectric.scss */
.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* line 138, ../../../../sass/booking/_selectric.scss */
.selectricItems li {
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 146, ../../../../sass/booking/_selectric.scss */
.selectricItems li:hover {
  background: #F0F0F0;
  color: #444;
}
/* line 151, ../../../../sass/booking/_selectric.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}

/* line 158, ../../../../sass/booking/_selectric.scss */
.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;
  cursor: default;
}

/* line 3, ../../../../sass/plugins/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 8, ../../../../sass/plugins/_mixins.scss */
.center_xy_before:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 13, ../../../../sass/plugins/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 17, ../../../../sass/plugins/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 21, ../../../../sass/plugins/_mixins.scss */
.center_y_before:before {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 25, ../../../../sass/plugins/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 30, ../../../../sass/plugins/_mixins.scss */
.fs:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
}

/* line 110, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:before, .icon-xcross:after {
  content: '';
  width: 100%;
  height: 2px;
  background: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 124, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -moz-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 61, ../../../../sass/plugins/_mixins.scss */
.display_flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* line 1, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown {
  margin: 40px 20px 20px;
  text-align: center;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  bottom: 180px;
  left: 48% !important;
  color: white;
}
/* line 9, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown div {
  display: inline-block;
  font-size: 16px;
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 0;
  padding-bottom: 0;
  text-transform: uppercase;
}
/* line 20, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .days {
  font-weight: 600;
}
/* line 23, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .date {
  display: block;
  font-size: 4.5rem;
}
/* line 28, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .title_format {
  font-weight: 600;
}

@media screen and (max-width: 800px) {
  /* line 35, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown {
    display: inline-block;
    width: 380px;
    top: 330px;
    left: 45% !important;
  }
  /* line 40, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown div {
    font-size: 10px;
  }
  /* line 43, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown .date {
    font-size: 2.5rem;
  }
}
@font-face {
  font-family: 'FontAwesome';
  src: url("/static_1/fonts/fontawesome/fontawesome/fontawesome-webfont.eot?v=4.7.0");
  src: url("/static_1/fonts/fontawesome/fontawesome-webfont.eot?#iefix&v=4.7.0") format("embedded-opentype"), url("/static_1/fonts/fontawesome/fontawesome-webfont.woff2?v=4.7.0") format("woff2"), url("/static_1/fonts/fontawesome/fontawesome-webfont.woff?v=4.7.0") format("woff"), url("/static_1/fonts/fontawesome/fontawesome-webfont.ttf?v=4.7.0") format("truetype"), url("/static_1/fonts/fontawesome/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular") format("svg");
  font-weight: normal;
  font-style: normal;
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lg {
  font-size: 1.33333333em;
  line-height: .75em;
  vertical-align: -15%;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-3x {
  font-size: 3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-4x {
  font-size: 4em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-5x {
  font-size: 5em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fw {
  width: 1.28571429em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul > li {
  position: relative;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: .14285714em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li.fa-lg {
  left: -1.85714286em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-border {
  padding: .2em .25em .15em;
  border: solid .08em #eee;
  border-radius: .1em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-left {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-right {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-left {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-right {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  transform: scale(-1, 1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical {
  filter: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x, .fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x {
  line-height: inherit;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inverse {
  color: #fff;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glass:before {
  content: "\f000";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-music:before {
  content: "\f001";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search:before {
  content: "\f002";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-o:before {
  content: "\f003";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart:before {
  content: "\f004";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star:before {
  content: "\f005";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-o:before {
  content: "\f006";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user:before {
  content: "\f007";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-film:before {
  content: "\f008";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-large:before {
  content: "\f009";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th:before {
  content: "\f00a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-list:before {
  content: "\f00b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check:before {
  content: "\f00c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-remove:before, .fa-close:before, .fa-times:before {
  content: "\f00d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-plus:before {
  content: "\f00e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-minus:before {
  content: "\f010";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-power-off:before {
  content: "\f011";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signal:before {
  content: "\f012";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gear:before, .fa-cog:before {
  content: "\f013";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash-o:before {
  content: "\f014";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-home:before {
  content: "\f015";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-o:before {
  content: "\f016";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clock-o:before {
  content: "\f017";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-road:before {
  content: "\f018";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-download:before {
  content: "\f019";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-down:before {
  content: "\f01a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-up:before {
  content: "\f01b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inbox:before {
  content: "\f01c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle-o:before {
  content: "\f01d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-right:before, .fa-repeat:before {
  content: "\f01e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-refresh:before {
  content: "\f021";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-alt:before {
  content: "\f022";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lock:before {
  content: "\f023";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag:before {
  content: "\f024";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-headphones:before {
  content: "\f025";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-off:before {
  content: "\f026";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-down:before {
  content: "\f027";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-up:before {
  content: "\f028";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qrcode:before {
  content: "\f029";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-barcode:before {
  content: "\f02a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tag:before {
  content: "\f02b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tags:before {
  content: "\f02c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-book:before {
  content: "\f02d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark:before {
  content: "\f02e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-print:before {
  content: "\f02f";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera:before {
  content: "\f030";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-font:before {
  content: "\f031";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bold:before {
  content: "\f032";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-italic:before {
  content: "\f033";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-height:before {
  content: "\f034";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-width:before {
  content: "\f035";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-left:before {
  content: "\f036";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-center:before {
  content: "\f037";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-right:before {
  content: "\f038";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-justify:before {
  content: "\f039";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list:before {
  content: "\f03a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dedent:before, .fa-outdent:before {
  content: "\f03b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-indent:before {
  content: "\f03c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-video-camera:before {
  content: "\f03d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-photo:before, .fa-image:before, .fa-picture-o:before {
  content: "\f03e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil:before {
  content: "\f040";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-marker:before {
  content: "\f041";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adjust:before {
  content: "\f042";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tint:before {
  content: "\f043";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edit:before, .fa-pencil-square-o:before {
  content: "\f044";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square-o:before {
  content: "\f045";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square-o:before {
  content: "\f046";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows:before {
  content: "\f047";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-backward:before {
  content: "\f048";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-backward:before {
  content: "\f049";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-backward:before {
  content: "\f04a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play:before {
  content: "\f04b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause:before {
  content: "\f04c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop:before {
  content: "\f04d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forward:before {
  content: "\f04e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-forward:before {
  content: "\f050";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-forward:before {
  content: "\f051";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eject:before {
  content: "\f052";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-left:before {
  content: "\f053";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-right:before {
  content: "\f054";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-circle:before {
  content: "\f055";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-circle:before {
  content: "\f056";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle:before {
  content: "\f057";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle:before {
  content: "\f058";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle:before {
  content: "\f059";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info-circle:before {
  content: "\f05a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crosshairs:before {
  content: "\f05b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle-o:before {
  content: "\f05c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle-o:before {
  content: "\f05d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ban:before {
  content: "\f05e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-left:before {
  content: "\f060";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-right:before {
  content: "\f061";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-up:before {
  content: "\f062";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-down:before {
  content: "\f063";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-forward:before, .fa-share:before {
  content: "\f064";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expand:before {
  content: "\f065";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compress:before {
  content: "\f066";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus:before {
  content: "\f067";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus:before {
  content: "\f068";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asterisk:before {
  content: "\f069";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation-circle:before {
  content: "\f06a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gift:before {
  content: "\f06b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leaf:before {
  content: "\f06c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire:before {
  content: "\f06d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye:before {
  content: "\f06e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye-slash:before {
  content: "\f070";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-warning:before, .fa-exclamation-triangle:before {
  content: "\f071";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plane:before {
  content: "\f072";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar:before {
  content: "\f073";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-random:before {
  content: "\f074";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment:before {
  content: "\f075";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magnet:before {
  content: "\f076";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-up:before {
  content: "\f077";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-down:before {
  content: "\f078";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-retweet:before {
  content: "\f079";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-cart:before {
  content: "\f07a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder:before {
  content: "\f07b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open:before {
  content: "\f07c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-v:before {
  content: "\f07d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-h:before {
  content: "\f07e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bar-chart-o:before, .fa-bar-chart:before {
  content: "\f080";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter-square:before {
  content: "\f081";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-square:before {
  content: "\f082";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera-retro:before {
  content: "\f083";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-key:before {
  content: "\f084";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gears:before, .fa-cogs:before {
  content: "\f085";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments:before {
  content: "\f086";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-up:before {
  content: "\f087";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-down:before {
  content: "\f088";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half:before {
  content: "\f089";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart-o:before {
  content: "\f08a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-out:before {
  content: "\f08b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin-square:before {
  content: "\f08c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumb-tack:before {
  content: "\f08d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link:before {
  content: "\f08e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-in:before {
  content: "\f090";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trophy:before {
  content: "\f091";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-square:before {
  content: "\f092";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-upload:before {
  content: "\f093";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lemon-o:before {
  content: "\f094";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone:before {
  content: "\f095";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square-o:before {
  content: "\f096";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark-o:before {
  content: "\f097";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone-square:before {
  content: "\f098";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter:before {
  content: "\f099";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-f:before, .fa-facebook:before {
  content: "\f09a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github:before {
  content: "\f09b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock:before {
  content: "\f09c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card:before {
  content: "\f09d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-feed:before, .fa-rss:before {
  content: "\f09e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hdd-o:before {
  content: "\f0a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullhorn:before {
  content: "\f0a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell:before {
  content: "\f0f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-certificate:before {
  content: "\f0a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-right:before {
  content: "\f0a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-left:before {
  content: "\f0a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-up:before {
  content: "\f0a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-down:before {
  content: "\f0a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-left:before {
  content: "\f0a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-right:before {
  content: "\f0a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-up:before {
  content: "\f0aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-down:before {
  content: "\f0ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-globe:before {
  content: "\f0ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wrench:before {
  content: "\f0ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tasks:before {
  content: "\f0ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-filter:before {
  content: "\f0b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-briefcase:before {
  content: "\f0b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-alt:before {
  content: "\f0b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-group:before, .fa-users:before {
  content: "\f0c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chain:before, .fa-link:before {
  content: "\f0c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud:before {
  content: "\f0c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flask:before {
  content: "\f0c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cut:before, .fa-scissors:before {
  content: "\f0c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copy:before, .fa-files-o:before {
  content: "\f0c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paperclip:before {
  content: "\f0c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-save:before, .fa-floppy-o:before {
  content: "\f0c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square:before {
  content: "\f0c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-navicon:before, .fa-reorder:before, .fa-bars:before {
  content: "\f0c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ul:before {
  content: "\f0ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ol:before {
  content: "\f0cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-strikethrough:before {
  content: "\f0cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-underline:before {
  content: "\f0cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-table:before {
  content: "\f0ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magic:before {
  content: "\f0d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-truck:before {
  content: "\f0d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest:before {
  content: "\f0d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-square:before {
  content: "\f0d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-square:before {
  content: "\f0d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus:before {
  content: "\f0d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-money:before {
  content: "\f0d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-down:before {
  content: "\f0d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-up:before {
  content: "\f0d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-left:before {
  content: "\f0d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-right:before {
  content: "\f0da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-columns:before {
  content: "\f0db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unsorted:before, .fa-sort:before {
  content: "\f0dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-down:before, .fa-sort-desc:before {
  content: "\f0dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-up:before, .fa-sort-asc:before {
  content: "\f0de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope:before {
  content: "\f0e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin:before {
  content: "\f0e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-left:before, .fa-undo:before {
  content: "\f0e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-legal:before, .fa-gavel:before {
  content: "\f0e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashboard:before, .fa-tachometer:before {
  content: "\f0e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment-o:before {
  content: "\f0e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments-o:before {
  content: "\f0e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flash:before, .fa-bolt:before {
  content: "\f0e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sitemap:before {
  content: "\f0e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-umbrella:before {
  content: "\f0e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paste:before, .fa-clipboard:before {
  content: "\f0ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lightbulb-o:before {
  content: "\f0eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exchange:before {
  content: "\f0ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-download:before {
  content: "\f0ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-upload:before {
  content: "\f0ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-md:before {
  content: "\f0f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stethoscope:before {
  content: "\f0f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-suitcase:before {
  content: "\f0f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-o:before {
  content: "\f0a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-coffee:before {
  content: "\f0f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cutlery:before {
  content: "\f0f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text-o:before {
  content: "\f0f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building-o:before {
  content: "\f0f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hospital-o:before {
  content: "\f0f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ambulance:before {
  content: "\f0f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medkit:before {
  content: "\f0fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fighter-jet:before {
  content: "\f0fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-beer:before {
  content: "\f0fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-h-square:before {
  content: "\f0fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square:before {
  content: "\f0fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-left:before {
  content: "\f100";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-right:before {
  content: "\f101";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-up:before {
  content: "\f102";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-down:before {
  content: "\f103";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-left:before {
  content: "\f104";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-right:before {
  content: "\f105";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-up:before {
  content: "\f106";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-down:before {
  content: "\f107";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-desktop:before {
  content: "\f108";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-laptop:before {
  content: "\f109";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tablet:before {
  content: "\f10a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mobile-phone:before, .fa-mobile:before {
  content: "\f10b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o:before {
  content: "\f10c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-left:before {
  content: "\f10d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-right:before {
  content: "\f10e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spinner:before {
  content: "\f110";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle:before {
  content: "\f111";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply:before, .fa-reply:before {
  content: "\f112";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-alt:before {
  content: "\f113";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-o:before {
  content: "\f114";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open-o:before {
  content: "\f115";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-smile-o:before {
  content: "\f118";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-frown-o:before {
  content: "\f119";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meh-o:before {
  content: "\f11a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gamepad:before {
  content: "\f11b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-keyboard-o:before {
  content: "\f11c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-o:before {
  content: "\f11d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-checkered:before {
  content: "\f11e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-terminal:before {
  content: "\f120";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code:before {
  content: "\f121";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply-all:before, .fa-reply-all:before {
  content: "\f122";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before {
  content: "\f123";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-location-arrow:before {
  content: "\f124";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crop:before {
  content: "\f125";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code-fork:before {
  content: "\f126";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlink:before, .fa-chain-broken:before {
  content: "\f127";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question:before {
  content: "\f128";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info:before {
  content: "\f129";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation:before {
  content: "\f12a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superscript:before {
  content: "\f12b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subscript:before {
  content: "\f12c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eraser:before {
  content: "\f12d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-puzzle-piece:before {
  content: "\f12e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone:before {
  content: "\f130";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone-slash:before {
  content: "\f131";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shield:before {
  content: "\f132";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-o:before {
  content: "\f133";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire-extinguisher:before {
  content: "\f134";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rocket:before {
  content: "\f135";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-maxcdn:before {
  content: "\f136";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-left:before {
  content: "\f137";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-right:before {
  content: "\f138";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-up:before {
  content: "\f139";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-down:before {
  content: "\f13a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-html5:before {
  content: "\f13b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-css3:before {
  content: "\f13c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-anchor:before {
  content: "\f13d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock-alt:before {
  content: "\f13e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullseye:before {
  content: "\f140";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-h:before {
  content: "\f141";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-v:before {
  content: "\f142";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rss-square:before {
  content: "\f143";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle:before {
  content: "\f144";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ticket:before {
  content: "\f145";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square:before {
  content: "\f146";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square-o:before {
  content: "\f147";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-up:before {
  content: "\f148";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-down:before {
  content: "\f149";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square:before {
  content: "\f14a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil-square:before {
  content: "\f14b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link-square:before {
  content: "\f14c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square:before {
  content: "\f14d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compass:before {
  content: "\f14e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-down:before, .fa-caret-square-o-down:before {
  content: "\f150";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-up:before, .fa-caret-square-o-up:before {
  content: "\f151";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-right:before, .fa-caret-square-o-right:before {
  content: "\f152";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-euro:before, .fa-eur:before {
  content: "\f153";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gbp:before {
  content: "\f154";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dollar:before, .fa-usd:before {
  content: "\f155";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rupee:before, .fa-inr:before {
  content: "\f156";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before {
  content: "\f157";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ruble:before, .fa-rouble:before, .fa-rub:before {
  content: "\f158";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-won:before, .fa-krw:before {
  content: "\f159";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitcoin:before, .fa-btc:before {
  content: "\f15a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file:before {
  content: "\f15b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text:before {
  content: "\f15c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-asc:before {
  content: "\f15d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-desc:before {
  content: "\f15e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-asc:before {
  content: "\f160";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-desc:before {
  content: "\f161";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-asc:before {
  content: "\f162";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-desc:before {
  content: "\f163";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-up:before {
  content: "\f164";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-down:before {
  content: "\f165";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-square:before {
  content: "\f166";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube:before {
  content: "\f167";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing:before {
  content: "\f168";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing-square:before {
  content: "\f169";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-play:before {
  content: "\f16a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dropbox:before {
  content: "\f16b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-overflow:before {
  content: "\f16c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-instagram:before {
  content: "\f16d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flickr:before {
  content: "\f16e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adn:before {
  content: "\f170";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket:before {
  content: "\f171";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket-square:before {
  content: "\f172";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr:before {
  content: "\f173";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr-square:before {
  content: "\f174";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-down:before {
  content: "\f175";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-up:before {
  content: "\f176";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-left:before {
  content: "\f177";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-right:before {
  content: "\f178";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-apple:before {
  content: "\f179";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-windows:before {
  content: "\f17a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-android:before {
  content: "\f17b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linux:before {
  content: "\f17c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dribbble:before {
  content: "\f17d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skype:before {
  content: "\f17e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-foursquare:before {
  content: "\f180";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trello:before {
  content: "\f181";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-female:before {
  content: "\f182";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-male:before {
  content: "\f183";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gittip:before, .fa-gratipay:before {
  content: "\f184";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sun-o:before {
  content: "\f185";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-moon-o:before {
  content: "\f186";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-archive:before {
  content: "\f187";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bug:before {
  content: "\f188";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vk:before {
  content: "\f189";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-weibo:before {
  content: "\f18a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-renren:before {
  content: "\f18b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pagelines:before {
  content: "\f18c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-exchange:before {
  content: "\f18d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-right:before {
  content: "\f18e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-left:before {
  content: "\f190";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-left:before, .fa-caret-square-o-left:before {
  content: "\f191";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dot-circle-o:before {
  content: "\f192";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair:before {
  content: "\f193";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo-square:before {
  content: "\f194";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-turkish-lira:before, .fa-try:before {
  content: "\f195";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square-o:before {
  content: "\f196";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-space-shuttle:before {
  content: "\f197";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slack:before {
  content: "\f198";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-square:before {
  content: "\f199";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wordpress:before {
  content: "\f19a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-openid:before {
  content: "\f19b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-institution:before, .fa-bank:before, .fa-university:before {
  content: "\f19c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mortar-board:before, .fa-graduation-cap:before {
  content: "\f19d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yahoo:before {
  content: "\f19e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google:before {
  content: "\f1a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit:before {
  content: "\f1a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-square:before {
  content: "\f1a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon-circle:before {
  content: "\f1a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon:before {
  content: "\f1a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-delicious:before {
  content: "\f1a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-digg:before {
  content: "\f1a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-pp:before {
  content: "\f1a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-alt:before {
  content: "\f1a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drupal:before {
  content: "\f1a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-joomla:before {
  content: "\f1aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-language:before {
  content: "\f1ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fax:before {
  content: "\f1ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building:before {
  content: "\f1ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-child:before {
  content: "\f1ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paw:before {
  content: "\f1b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spoon:before {
  content: "\f1b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cube:before {
  content: "\f1b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cubes:before {
  content: "\f1b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance:before {
  content: "\f1b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance-square:before {
  content: "\f1b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam:before {
  content: "\f1b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam-square:before {
  content: "\f1b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-recycle:before {
  content: "\f1b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-automobile:before, .fa-car:before {
  content: "\f1b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cab:before, .fa-taxi:before {
  content: "\f1ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tree:before {
  content: "\f1bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spotify:before {
  content: "\f1bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deviantart:before {
  content: "\f1bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soundcloud:before {
  content: "\f1be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-database:before {
  content: "\f1c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-pdf-o:before {
  content: "\f1c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-word-o:before {
  content: "\f1c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-excel-o:before {
  content: "\f1c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-powerpoint-o:before {
  content: "\f1c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before {
  content: "\f1c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-zip-o:before, .fa-file-archive-o:before {
  content: "\f1c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-sound-o:before, .fa-file-audio-o:before {
  content: "\f1c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-movie-o:before, .fa-file-video-o:before {
  content: "\f1c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-code-o:before {
  content: "\f1c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vine:before {
  content: "\f1ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codepen:before {
  content: "\f1cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-jsfiddle:before {
  content: "\f1cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before {
  content: "\f1cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o-notch:before {
  content: "\f1ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ra:before, .fa-resistance:before, .fa-rebel:before {
  content: "\f1d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ge:before, .fa-empire:before {
  content: "\f1d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git-square:before {
  content: "\f1d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git:before {
  content: "\f1d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before {
  content: "\f1d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tencent-weibo:before {
  content: "\f1d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qq:before {
  content: "\f1d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wechat:before, .fa-weixin:before {
  content: "\f1d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send:before, .fa-paper-plane:before {
  content: "\f1d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send-o:before, .fa-paper-plane-o:before {
  content: "\f1d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-history:before {
  content: "\f1da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-thin:before {
  content: "\f1db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-header:before {
  content: "\f1dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paragraph:before {
  content: "\f1dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sliders:before {
  content: "\f1de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt:before {
  content: "\f1e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt-square:before {
  content: "\f1e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bomb:before {
  content: "\f1e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soccer-ball-o:before, .fa-futbol-o:before {
  content: "\f1e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tty:before {
  content: "\f1e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-binoculars:before {
  content: "\f1e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plug:before {
  content: "\f1e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slideshare:before {
  content: "\f1e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitch:before {
  content: "\f1e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yelp:before {
  content: "\f1e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-newspaper-o:before {
  content: "\f1ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wifi:before {
  content: "\f1eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calculator:before {
  content: "\f1ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paypal:before {
  content: "\f1ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-wallet:before {
  content: "\f1ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-visa:before {
  content: "\f1f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-mastercard:before {
  content: "\f1f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-discover:before {
  content: "\f1f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-amex:before {
  content: "\f1f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-paypal:before {
  content: "\f1f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-stripe:before {
  content: "\f1f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash:before {
  content: "\f1f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash-o:before {
  content: "\f1f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash:before {
  content: "\f1f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copyright:before {
  content: "\f1f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-at:before {
  content: "\f1fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eyedropper:before {
  content: "\f1fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paint-brush:before {
  content: "\f1fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-birthday-cake:before {
  content: "\f1fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-area-chart:before {
  content: "\f1fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pie-chart:before {
  content: "\f200";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-line-chart:before {
  content: "\f201";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm:before {
  content: "\f202";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm-square:before {
  content: "\f203";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-off:before {
  content: "\f204";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-on:before {
  content: "\f205";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bicycle:before {
  content: "\f206";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bus:before {
  content: "\f207";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ioxhost:before {
  content: "\f208";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angellist:before {
  content: "\f209";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc:before {
  content: "\f20a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shekel:before, .fa-sheqel:before, .fa-ils:before {
  content: "\f20b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meanpath:before {
  content: "\f20c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-buysellads:before {
  content: "\f20d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-connectdevelop:before {
  content: "\f20e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashcube:before {
  content: "\f210";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forumbee:before {
  content: "\f211";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leanpub:before {
  content: "\f212";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sellsy:before {
  content: "\f213";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shirtsinbulk:before {
  content: "\f214";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-simplybuilt:before {
  content: "\f215";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skyatlas:before {
  content: "\f216";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-plus:before {
  content: "\f217";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-arrow-down:before {
  content: "\f218";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-diamond:before {
  content: "\f219";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ship:before {
  content: "\f21a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-secret:before {
  content: "\f21b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-motorcycle:before {
  content: "\f21c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-street-view:before {
  content: "\f21d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heartbeat:before {
  content: "\f21e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus:before {
  content: "\f221";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars:before {
  content: "\f222";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mercury:before {
  content: "\f223";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-intersex:before, .fa-transgender:before {
  content: "\f224";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-transgender-alt:before {
  content: "\f225";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-double:before {
  content: "\f226";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-double:before {
  content: "\f227";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-mars:before {
  content: "\f228";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke:before {
  content: "\f229";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-v:before {
  content: "\f22a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-h:before {
  content: "\f22b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-neuter:before {
  content: "\f22c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-genderless:before {
  content: "\f22d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-official:before {
  content: "\f230";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-p:before {
  content: "\f231";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-whatsapp:before {
  content: "\f232";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-server:before {
  content: "\f233";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-plus:before {
  content: "\f234";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-times:before {
  content: "\f235";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hotel:before, .fa-bed:before {
  content: "\f236";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viacoin:before {
  content: "\f237";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-train:before {
  content: "\f238";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subway:before {
  content: "\f239";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medium:before {
  content: "\f23a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yc:before, .fa-y-combinator:before {
  content: "\f23b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-optin-monster:before {
  content: "\f23c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opencart:before {
  content: "\f23d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expeditedssl:before {
  content: "\f23e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before {
  content: "\f240";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-3:before, .fa-battery-three-quarters:before {
  content: "\f241";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-2:before, .fa-battery-half:before {
  content: "\f242";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-1:before, .fa-battery-quarter:before {
  content: "\f243";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-0:before, .fa-battery-empty:before {
  content: "\f244";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mouse-pointer:before {
  content: "\f245";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-i-cursor:before {
  content: "\f246";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-group:before {
  content: "\f247";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-ungroup:before {
  content: "\f248";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note:before {
  content: "\f249";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note-o:before {
  content: "\f24a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-jcb:before {
  content: "\f24b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-diners-club:before {
  content: "\f24c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clone:before {
  content: "\f24d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-balance-scale:before {
  content: "\f24e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-o:before {
  content: "\f250";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-1:before, .fa-hourglass-start:before {
  content: "\f251";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-2:before, .fa-hourglass-half:before {
  content: "\f252";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-3:before, .fa-hourglass-end:before {
  content: "\f253";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass:before {
  content: "\f254";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-grab-o:before, .fa-hand-rock-o:before {
  content: "\f255";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-stop-o:before, .fa-hand-paper-o:before {
  content: "\f256";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-scissors-o:before {
  content: "\f257";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-lizard-o:before {
  content: "\f258";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-spock-o:before {
  content: "\f259";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-pointer-o:before {
  content: "\f25a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-peace-o:before {
  content: "\f25b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trademark:before {
  content: "\f25c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-registered:before {
  content: "\f25d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-creative-commons:before {
  content: "\f25e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg:before {
  content: "\f260";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg-circle:before {
  content: "\f261";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tripadvisor:before {
  content: "\f262";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki:before {
  content: "\f263";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki-square:before {
  content: "\f264";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-get-pocket:before {
  content: "\f265";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wikipedia-w:before {
  content: "\f266";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-safari:before {
  content: "\f267";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chrome:before {
  content: "\f268";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-firefox:before {
  content: "\f269";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opera:before {
  content: "\f26a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-internet-explorer:before {
  content: "\f26b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tv:before, .fa-television:before {
  content: "\f26c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-contao:before {
  content: "\f26d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-500px:before {
  content: "\f26e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-amazon:before {
  content: "\f270";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-plus-o:before {
  content: "\f271";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-minus-o:before {
  content: "\f272";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-times-o:before {
  content: "\f273";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-check-o:before {
  content: "\f274";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-industry:before {
  content: "\f275";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-pin:before {
  content: "\f276";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-signs:before {
  content: "\f277";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-o:before {
  content: "\f278";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map:before {
  content: "\f279";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting:before {
  content: "\f27a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting-o:before {
  content: "\f27b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-houzz:before {
  content: "\f27c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo:before {
  content: "\f27d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-black-tie:before {
  content: "\f27e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fonticons:before {
  content: "\f280";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-alien:before {
  content: "\f281";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edge:before {
  content: "\f282";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card-alt:before {
  content: "\f283";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codiepie:before {
  content: "\f284";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-modx:before {
  content: "\f285";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fort-awesome:before {
  content: "\f286";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-usb:before {
  content: "\f287";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-product-hunt:before {
  content: "\f288";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mixcloud:before {
  content: "\f289";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-scribd:before {
  content: "\f28a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle:before {
  content: "\f28b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle-o:before {
  content: "\f28c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle:before {
  content: "\f28d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle-o:before {
  content: "\f28e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-bag:before {
  content: "\f290";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-basket:before {
  content: "\f291";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hashtag:before {
  content: "\f292";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth:before {
  content: "\f293";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth-b:before {
  content: "\f294";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-percent:before {
  content: "\f295";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gitlab:before {
  content: "\f296";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpbeginner:before {
  content: "\f297";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpforms:before {
  content: "\f298";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envira:before {
  content: "\f299";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-universal-access:before {
  content: "\f29a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair-alt:before {
  content: "\f29b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle-o:before {
  content: "\f29c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-blind:before {
  content: "\f29d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-audio-description:before {
  content: "\f29e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-control-phone:before {
  content: "\f2a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-braille:before {
  content: "\f2a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-assistive-listening-systems:before {
  content: "\f2a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before {
  content: "\f2a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide:before {
  content: "\f2a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide-g:before {
  content: "\f2a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signing:before, .fa-sign-language:before {
  content: "\f2a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-low-vision:before {
  content: "\f2a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo:before {
  content: "\f2a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo-square:before {
  content: "\f2aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat:before {
  content: "\f2ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-ghost:before {
  content: "\f2ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-square:before {
  content: "\f2ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper:before {
  content: "\f2ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-first-order:before {
  content: "\f2b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yoast:before {
  content: "\f2b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-themeisle:before {
  content: "\f2b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-circle:before, .fa-google-plus-official:before {
  content: "\f2b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fa:before, .fa-font-awesome:before {
  content: "\f2b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-handshake-o:before {
  content: "\f2b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open:before {
  content: "\f2b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open-o:before {
  content: "\f2b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linode:before {
  content: "\f2b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book:before {
  content: "\f2b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book-o:before {
  content: "\f2ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard:before, .fa-address-card:before {
  content: "\f2bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard-o:before, .fa-address-card-o:before {
  content: "\f2bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle:before {
  content: "\f2bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle-o:before {
  content: "\f2be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-o:before {
  content: "\f2c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-id-badge:before {
  content: "\f2c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license:before, .fa-id-card:before {
  content: "\f2c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license-o:before, .fa-id-card-o:before {
  content: "\f2c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quora:before {
  content: "\f2c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-free-code-camp:before {
  content: "\f2c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-telegram:before {
  content: "\f2c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before {
  content: "\f2c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-3:before, .fa-thermometer-three-quarters:before {
  content: "\f2c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-2:before, .fa-thermometer-half:before {
  content: "\f2c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-1:before, .fa-thermometer-quarter:before {
  content: "\f2ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-0:before, .fa-thermometer-empty:before {
  content: "\f2cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shower:before {
  content: "\f2cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bathtub:before, .fa-s15:before, .fa-bath:before {
  content: "\f2cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-podcast:before {
  content: "\f2ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-maximize:before {
  content: "\f2d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-minimize:before {
  content: "\f2d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-restore:before {
  content: "\f2d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle:before, .fa-window-close:before {
  content: "\f2d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle-o:before, .fa-window-close-o:before {
  content: "\f2d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bandcamp:before {
  content: "\f2d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-grav:before {
  content: "\f2d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-etsy:before {
  content: "\f2d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-imdb:before {
  content: "\f2d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ravelry:before {
  content: "\f2d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eercast:before {
  content: "\f2da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microchip:before {
  content: "\f2db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snowflake-o:before {
  content: "\f2dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superpowers:before {
  content: "\f2dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpexplorer:before {
  content: "\f2de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meetup:before {
  content: "\f2e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

/*FONFS*/
@font-face {
  font-family: 'gotham';
  src: url("/static_1/fonts/gotham/GothamRnd-Book.otf");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'gotham';
  src: url("/static_1/fonts/gotham/GothamRnd-bold.otf");
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: 'gotham';
  src: url("/static_1/fonts/gotham/GothamRnd-Light.otf");
  font-weight: 300;
  font-style: normal;
}
/* line 1, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  z-index: 1000;
  height: 275px;
  display: none;
  box-shadow: 1px 1px 5px 2px black;
}
/* line 11, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .close_button_faldon {
  position: absolute;
  top: 20px;
  right: 20px;
  display: inline-block;
  z-index: 100;
  color: white;
  cursor: pointer;
  -webkit-transition: transform 0.4s;
  -moz-transition: transform 0.4s;
  -ms-transition: transform 0.4s;
  -o-transition: transform 0.4s;
  transition: transform 0.4s;
}
/* line 21, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .close_button_faldon:hover {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
/* line 30, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
}
/* line 37, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper:after {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  content: "";
  margin: 10px;
  z-index: 2;
  border: 2px solid white;
}
/* line 45, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content, .faldon_footer_wrapper .faldon_content_thanks {
  position: relative;
  height: 100%;
  z-index: 5;
}
/* line 50, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block, .faldon_footer_wrapper .faldon_content_thanks .center_block {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: 100%;
  text-align: center;
  z-index: 3;
}
/* line 56, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_title, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_title {
  font-weight: bolder;
  color: #f19317;
  font-size: 28px;
}
/* line 62, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_description, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_description {
  font-size: 13px;
  color: white;
  line-height: 23px;
  width: 750px;
  margin: 10px auto 0;
}
/* line 70, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_link, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_link {
  display: inline-block;
  text-transform: uppercase;
  padding: 7px 15px;
  background: #f19317;
  color: white;
  text-decoration: none;
  margin-top: 20px;
  cursor: pointer;
  -webkit-transition: background 0.4s;
  -moz-transition: background 0.4s;
  -ms-transition: background 0.4s;
  -o-transition: background 0.4s;
  transition: background 0.4s;
}
/* line 81, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_link:hover, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_link:hover {
  background: #1252de;
}
/* line 86, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter {
  margin-top: 10px;
  display: inline-block;
  width: 100%;
}
/* line 91, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter input#faldon_email, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter input#faldon_email {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  height: 32px;
  width: 200px;
  box-sizing: border-box;
  padding-left: 10px;
  background: white;
  color: #A9A8A8;
}
/* line 103, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter input#faldon_email::-webkit-input-placeholder, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter input#faldon_email::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #A9A8A8;
}
/* line 107, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter input#faldon_email::-moz-placeholder, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter input#faldon_email::-moz-placeholder {
  /* Firefox 19+ */
  color: #A9A8A8;
}
/* line 111, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter input#faldon_email:-ms-input-placeholder, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter input#faldon_email:-ms-input-placeholder {
  /* IE 10+ */
  color: #A9A8A8;
}
/* line 115, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter input#faldon_email:-moz-placeholder, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter input#faldon_email:-moz-placeholder {
  /* Firefox 18- */
  color: #A9A8A8;
}
/* line 121, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter button, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter button {
  height: 32px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  background: #f19317;
  color: white;
  cursor: pointer;
  text-transform: uppercase;
  padding: 0 10px;
  -webkit-transition: background 0.4s;
  -moz-transition: background 0.4s;
  -ms-transition: background 0.4s;
  -o-transition: background 0.4s;
  transition: background 0.4s;
}
/* line 134, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter button:hover, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter button:hover {
  background: #c9770c;
}
/* line 139, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter .check_faldon, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter .check_faldon {
  margin-top: 10px;
}
/* line 142, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter .check_faldon .check_privacy, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter .check_faldon .check_privacy {
  display: inline-block;
  vertical-align: middle;
}
/* line 146, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter .check_faldon .check_privacy.error + .newsletter_popup, .faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter .check_faldon .check_privacy.error + .label_promotions, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter .check_faldon .check_privacy.error + .newsletter_popup, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter .check_faldon .check_privacy.error + .label_promotions {
  color: #ff6b6a !important;
}
/* line 151, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter .check_faldon .newsletter_popup, .faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter .check_faldon label, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter .check_faldon .newsletter_popup, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter .check_faldon label {
  display: inline-block;
  vertical-align: middle;
  color: white;
  font-size: 12px;
}
/* line 159, ../sass/_faldon_v1.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter .faldon_checkbox, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter .faldon_checkbox {
  color: white;
  font-size: 12px;
}

/* line 169, ../sass/_faldon_v1.scss */
.promocode_highlight .promocode_input {
  color: #346FEF;
}

/* line 1, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper {
  padding: 70px 0;
  background-color: #fafafa;
}
/* line 4, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper h3 {
  text-align: center;
  font-family: 'Lato', sans-serif;
  font-size: 33px;
  color: #346FEF;
  margin-bottom: 35px;
}
/* line 10, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper h3 strong {
  font-weight: 700;
}
/* line 13, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper h3:after {
  content: '';
  display: block;
  margin: 15px auto;
  width: 70px;
  height: 6px;
  background-color: #797676;
}
/* line 22, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper .banner_ventajas_icos,
.banner_ventajas_wrapper .banner_ventajas {
  text-align: center;
}
/* line 25, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper .banner_ventajas_icos .banner,
.banner_ventajas_wrapper .banner_ventajas .banner {
  display: inline-block;
  padding: 20px 0 30px;
}
/* line 28, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper .banner_ventajas_icos .banner i.fa,
.banner_ventajas_wrapper .banner_ventajas .banner i.fa {
  display: inline-block;
  vertical-align: middle;
  color: #797676;
  font-size: 50px;
  margin: 10px;
}
/* line 35, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper .banner_ventajas_icos .banner span,
.banner_ventajas_wrapper .banner_ventajas .banner span {
  display: inline-block;
  vertical-align: middle;
  font-weight: 100;
}
/* line 39, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper .banner_ventajas_icos .banner span strong,
.banner_ventajas_wrapper .banner_ventajas .banner span strong {
  font-weight: 700;
}
/* line 46, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper .banner_ventajas .banner {
  width: calc(100% / 5 - 5px);
}
/* line 48, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper .banner_ventajas .banner i.fa {
  display: block;
}
/* line 51, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper .banner_ventajas .banner span {
  color: #346FEF;
  font-size: 11px;
  font-family: 'Lato', sans-serif;
  letter-spacing: 2px;
  font-weight: 500;
}
/* line 57, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper .banner_ventajas .banner span strong {
  font-size: 16px;
}
/* line 65, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper .banner_ventajas_icos .banner {
  width: calc(100% / 3 - 17px);
  margin: 2px 1px;
  background-color: white;
  padding: 0 30px;
  text-align: left;
}
/* line 71, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper .banner_ventajas_icos .banner span {
  font-size: 18px;
}
/* line 73, ../sass/_banner_ventajas.scss */
.banner_ventajas_wrapper .banner_ventajas_icos .banner span strong {
  font-size: 20px;
}

/* line 1, ../sass/_min_contact_popup.scss */
.contact_overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: fixed;
  background-color: #000000;
  opacity: .6;
  z-index: 9999;
}

/* line 8, ../sass/_min_contact_popup.scss */
.contact_popup_wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  position: fixed;
  z-index: 10000;
  background: white;
  border-radius: 18px;
  padding: 30px 55px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}
/* line 19, ../sass/_min_contact_popup.scss */
.contact_popup_wrapper .close_popup {
  position: absolute;
  top: -50px;
  color: white;
  right: 0;
  font-size: 34px;
  cursor: pointer;
}
/* line 28, ../sass/_min_contact_popup.scss */
.contact_popup_wrapper .contact_popup_title {
  display: block;
  font-size: 26px;
  text-align: center;
  color: #346FEF;
  margin-bottom: 10px;
}
/* line 34, ../sass/_min_contact_popup.scss */
.contact_popup_wrapper .contact_popup_title small {
  font-weight: lighter;
  font-size: 18px;
  display: block;
}
/* line 40, ../sass/_min_contact_popup.scss */
.contact_popup_wrapper .form_wrapper {
  width: 100%;
  padding: 0 10px;
}
/* line 43, ../sass/_min_contact_popup.scss */
.contact_popup_wrapper .form_wrapper form {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
  flex-flow: column;
  align-items: center;
}
/* line 47, ../sass/_min_contact_popup.scss */
.contact_popup_wrapper .form_wrapper form .bordeInput {
  margin: 10px 0;
  width: 100%;
  padding: 10px 0;
  border: none;
  border-bottom: 2px solid #346FEF;
  color: #346FEF;
  font-size: 12px;
  outline: none;
}
/* line 56, ../sass/_min_contact_popup.scss */
.contact_popup_wrapper .form_wrapper form .bordeInput::placeholder {
  text-transform: uppercase;
  color: #346FEF;
}
/* line 61, ../sass/_min_contact_popup.scss */
.contact_popup_wrapper .form_wrapper form input::-webkit-outer-spin-button,
.contact_popup_wrapper .form_wrapper form input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
/* line 67, ../sass/_min_contact_popup.scss */
.contact_popup_wrapper .form_wrapper form input[type=number] {
  -moz-appearance: textfield;
}
/* line 70, ../sass/_min_contact_popup.scss */
.contact_popup_wrapper .form_wrapper form #contact-button {
  background: #797676;
  text-transform: uppercase;
  color: white;
  border: none;
  border-radius: 0;
  cursor: pointer;
  width: 110px;
  padding: 10px 20px;
  font-size: 16px;
  margin-top: 20px;
}
/* line 81, ../sass/_min_contact_popup.scss */
.contact_popup_wrapper .form_wrapper form #contact-button:hover {
  background: #346FEF;
}
@media screen and (max-width: 700px) {
  /* line 8, ../sass/_min_contact_popup.scss */
  .contact_popup_wrapper {
    padding: 30px 30px;
    width: 70%;
  }
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/
/* line 2, ../sass/_template_specific.scss */
body {
  font-family: 'Lato', sans-serif;
  font-family: 'Playfair Display', serif;
  color: #797676;
}

/* line 8, ../sass/_template_specific.scss */
.spinner_wrapper {
  display: none;
  position: absolute;
  right: 145px;
  top: 43px;
}

/* line 15, ../sass/_template_specific.scss */
span.phone_wrapper {
  margin-right: 7px;
}

/* line 19, ../sass/_template_specific.scss */
.ui-state-default {
  border: 1px solid white !important;
}

/* line 23, ../sass/_template_specific.scss */
.ui-datepicker-title {
  color: white !important;
}

/* line 27, ../sass/_template_specific.scss */
.ui-widget-header {
  background: #346FEF !important;
}

/* line 31, ../sass/_template_specific.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #346FEF !important;
  color: white;
}

/* line 36, ../sass/_template_specific.scss */
.web_support_label_1 {
  color: white !important;
}

/* line 40, ../sass/_template_specific.scss */
section#content {
  background: white;
}

/* line 44, ../sass/_template_specific.scss */
.slider_inner_description {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 22;
  color: white;
  top: 210px;
  bottom: 0px;
  text-align: center;
  font-size: 35px;
  font-weight: 300;
  font-family: 'Lato', sans-serif;
  margin: auto;
  display: table;
  width: 100%;
}

/* line 61, ../sass/_template_specific.scss */
div#main-sections {
  display: table;
  margin: auto;
}

/* line 67, ../sass/_template_specific.scss */
.booking_engine_inside #wrapper_booking {
  bottom: 0 !important;
}

/*======== Header =========*/
/* line 73, ../sass/_template_specific.scss */
header {
  padding-bottom: 11px;
  position: absolute;
  z-index: 23;
  width: 100%;
  min-width: 1140px;
  letter-spacing: 1px;
}
/* line 80, ../sass/_template_specific.scss */
header .extra_top_header {
  font-size: 15px;
  background: #346FEF;
  background-color: 0.9;
  padding: 15px 0;
  color: #4b4b4b;
  text-align: center;
}
/* line 87, ../sass/_template_specific.scss */
header .extra_top_header i {
  display: inline-block;
  margin-right: 5px;
}
/* line 91, ../sass/_template_specific.scss */
header .extra_top_header p {
  display: inline-block;
}
/* line 94, ../sass/_template_specific.scss */
header .extra_top_header a {
  position: relative;
  color: white;
  text-decoration: none;
}
/* line 98, ../sass/_template_specific.scss */
header .extra_top_header a:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background: white;
  opacity: 0;
  -webkit-transform: translateY(9px);
  transform: translateY(9px);
  -webkit-transition: opacity .3s,-webkit-transform .3s;
  transition: transform .3s,opacity .3s,-webkit-transform .3s;
  pointer-events: none;
}
/* line 113, ../sass/_template_specific.scss */
header .extra_top_header a:hover:before {
  opacity: 1;
  -webkit-transform: translateY(4px);
  transform: translateY(4px);
}

/* line 122, ../sass/_template_specific.scss */
#wrapper-header {
  background: white;
  height: 85px;
  position: relative;
}
/* line 126, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu {
  height: 115px !important;
}
/* line 128, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu #logoDiv {
  display: inline-block;
  padding-top: 20px;
  margin-left: 10px !important;
}
/* line 133, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu #main_menu {
  top: 70px;
}
/* line 135, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu #main_menu #mainMenuDiv a {
  margin: 0 3px;
}
/* line 139, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu #lang {
  margin-top: 25px !important;
}
/* line 142, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu button.deploy_booking {
  font-size: 16px;
}

/* line 148, ../sass/_template_specific.scss */
div#logoDiv {
  width: auto;
  display: inline;
  margin-top: 21px;
  margin-left: 25px;
}

/* line 156, ../sass/_template_specific.scss */
#lang {
  cursor: pointer;
  width: 70px;
  height: 35px;
  display: inline-block;
  margin-top: 25px;
  margin-top: 18px;
  float: right;
  vertical-align: middle;
  margin-right: 20px;
}
/* line 167, ../sass/_template_specific.scss */
#lang #selected-language {
  background: #E9E9E9;
  box-sizing: border-box;
  padding: 11px 10px 17px 10px;
  height: 35px;
  width: 50%;
  display: inline-block;
  font-family: 'Lato', sans-serif;
  color: black;
  font-size: 12px;
  letter-spacing: 1px;
  text-transform: uppercase;
  text-align: center;
}
/* line 182, ../sass/_template_specific.scss */
#lang .arrow {
  display: inline-block;
  background: #797676 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  float: right;
  width: 50%;
  height: 35px;
  margin-top: 0px;
  border: 1px solid #E9E9E9;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
/* line 195, ../sass/_template_specific.scss */
#lang ul li {
  background: #E9E9E9;
  text-align: left;
  width: 71px;
  font-size: 13px;
  box-sizing: border-box;
  font-family: 'Lato', sans-serif;
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  padding-left: 9px;
  color: black;
  border-top: 1px solid #FFF;
}
/* line 210, ../sass/_template_specific.scss */
#lang ul li:hover {
  opacity: 0.8;
}
/* line 214, ../sass/_template_specific.scss */
#lang ul li a {
  color: black;
  text-decoration: none !important;
  font-weight: 600;
  font-size: 10px;
  text-align: center;
}

/* line 224, ../sass/_template_specific.scss */
.wifi_element {
  display: inline-block;
  vertical-align: middle;
  margin-top: -19px;
  font-size: 12px;
  color: white;
  font-family: 'Lato', sans-serif;
}
/* line 232, ../sass/_template_specific.scss */
.wifi_element span {
  margin-top: 7px;
  display: inline-block;
  margin-left: 5px;
  font-size: 10px;
  position: relative;
  vertical-align: top;
}

/* line 242, ../sass/_template_specific.scss */
#language-selector-options {
  display: none;
  box-shadow: 0px 1px 1px #6D6C6C;
}

/* line 247, ../sass/_template_specific.scss */
.weather {
  margin-right: 10px;
  width: 90px;
  float: right;
  margin-top: 18px;
}
/* line 254, ../sass/_template_specific.scss */
.weather .grados {
  float: right;
  background: #E9E9E9;
  padding: 10px 0px 17px 3px;
  box-sizing: border-box;
  height: 35px;
  display: inline-block;
  color: black;
  font-size: 12px;
  font-family: 'Lato', sans-serif;
  text-align: center;
  letter-spacing: 1px;
}
/* line 267, ../sass/_template_specific.scss */
.weather .img_weather {
  float: right;
  background-color: #797676;
  width: 40px;
  height: 35px;
  padding-top: 4px;
  box-sizing: border-box;
  border: 1px solid #E9E9E9;
}
/* line 276, ../sass/_template_specific.scss */
.weather .img_weather img {
  width: 23px;
  text-align: center;
  display: block;
  margin: auto;
  margin-top: 2px;
}

/* line 287, ../sass/_template_specific.scss */
body.secciones_interiores header {
  padding: 0;
  position: relative;
}

/* line 293, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu {
  position: fixed !important;
  top: 0;
  display: none;
  width: 100%;
  z-index: 401;
  height: 60px;
  border-bottom: 1px solid #f0f0f0;
  min-width: 1140px;
}
/* line 303, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu #main-sections-inner {
  text-align: center;
}
/* line 307, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu div#logoDiv {
  position: relative;
  z-index: 3;
  margin-top: 4px;
  width: 265px;
  margin-left: 0;
}
/* line 315, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu .weather {
  display: none;
}
/* line 320, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu #lang {
  margin-top: 14px;
  position: relative;
  z-index: 3;
}
/* line 326, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu button.deploy_booking {
  float: right;
  border: 0;
  background: linear-gradient(to bottom, #346fef 0%, #346fef 50%, #346fef 100%);
  color: white;
  height: 100%;
  text-transform: uppercase;
  line-height: 60px;
  padding: 0 45px;
  position: relative;
  z-index: 3;
  cursor: pointer;
}
/* line 339, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu button.deploy_booking:hover {
  opacity: 0.8;
}
/* line 344, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu #main_menu {
  left: 160px;
  right: 200px;
}
/* line 349, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu #mainMenuDiv {
  padding-left: 40px;
}
/* line 352, ../sass/_template_specific.scss */
#wrapper-header.hidden_top_menu #mainMenuDiv a {
  padding: 5px 6px;
}

/*========== Menu =========*/
/* line 359, ../sass/_template_specific.scss */
#main_menu {
  height: 30px;
  display: inline-block;
  width: 850px !important;
  box-sizing: border-box;
  margin-left: 0px;
  position: absolute;
  margin: auto;
  right: 0;
  top: 7px;
  bottom: 0;
  text-align: center;
}

/* line 374, ../sass/_template_specific.scss */
#mainMenuDiv {
  font-size: 15px;
  z-index: 99;
  clear: both;
}

/* line 380, ../sass/_template_specific.scss */
#mainMenuDiv a {
  font-weight: 500;
  text-decoration: none;
  text-transform: uppercase;
  color: #1d1d1b;
  display: inline-block;
  padding: 5px 9px 5px !important;
  margin: 0 0px;
  font-family: 'Lato', sans-serif;
  border-bottom: 2px solid white;
  cursor: pointer;
}

/* line 393, ../sass/_template_specific.scss */
#mainMenuDiv a:hover {
  border-bottom: 2px solid #346FEF;
}

/* line 397, ../sass/_template_specific.scss */
#mainMenuDiv ul li a {
  width: 100%;
  box-sizing: border-box;
}
/* line 401, ../sass/_template_specific.scss */
#mainMenuDiv ul li a:hover {
  border-bottom: 2px solid white;
  color: white;
}

/* line 407, ../sass/_template_specific.scss */
.menu_active {
  border-bottom: 2px solid #346FEF !important;
}

/* line 411, ../sass/_template_specific.scss */
#section-active a {
  padding: 5px 8px 5px;
  text-decoration: none;
  text-transform: uppercase;
  font-weight: 700;
  display: inline-block;
  border-bottom: 2px solid #346FEF;
}

/* line 420, ../sass/_template_specific.scss */
#section-active ul li a {
  border-bottom: 0 !important;
}

/* line 424, ../sass/_template_specific.scss */
#main-sections-inner ul {
  display: none;
  background: white;
}

/* line 429, ../sass/_template_specific.scss */
#main-sections-inner div ul {
  position: absolute;
  top: 31px;
}

/* line 434, ../sass/_template_specific.scss */
#main-sections-inner li ul {
  position: absolute;
}

/* line 438, ../sass/_template_specific.scss */
#main-sections-inner div li {
  float: none;
  border-bottom: 2px solid #346FEF;
  display: block;
}
/* line 443, ../sass/_template_specific.scss */
#main-sections-inner div li:hover {
  background: #346FEF;
  color: white;
}

/* line 449, ../sass/_template_specific.scss */
#main-sections-inner {
  text-align: justify;
}

/* line 453, ../sass/_template_specific.scss */
#main-sections-inner > div {
  display: inline-block;
  position: relative;
}

/* line 458, ../sass/_template_specific.scss */
.main-section-div-wrapper a {
  line-height: 15px;
  font-size: 11px;
  text-transform: uppercase;
}

/* line 471, ../sass/_template_specific.scss */
.lang_social_wrapper .best_price {
  float: right;
  margin-right: 20px;
  color: white;
  text-transform: uppercase;
  font-weight: 300;
  padding-top: 6px;
}

/* line 481, ../sass/_template_specific.scss */
.hidden_booking_widget_header {
  position: fixed;
  top: 61px;
  display: none;
  width: 100%;
  z-index: 24;
  background: white;
  box-shadow: 3px 2px 20px gray;
}
/* line 490, ../sass/_template_specific.scss */
.hidden_booking_widget_header #full_wrapper_booking div#wrapper_booking {
  position: initial;
  padding: 0;
}

/*===== Slider Revolution =====*/
/* line 497, ../sass/_template_specific.scss */
section#slider_container {
  position: relative;
  min-height: 445px;
}
/* line 502, ../sass/_template_specific.scss */
section#slider_container .tp-bullets {
  display: none !important;
}
/* line 506, ../sass/_template_specific.scss */
section#slider_container .offers_slider_wrapper {
  position: absolute;
  bottom: 0;
  z-index: 22;
  left: 0;
  right: 0;
  background: rgba(121, 118, 118, 0.85);
  padding: 30px 0;
}
/* line 516, ../sass/_template_specific.scss */
section#slider_container .offers_slider_wrapper .slides li {
  display: none;
}
/* line 521, ../sass/_template_specific.scss */
section#slider_container .offers_slider_wrapper h3.slider_offer_title {
  font-family: 'Playfair Display', serif;
  color: white;
  font-size: 18px;
  margin-bottom: 11px;
}
/* line 528, ../sass/_template_specific.scss */
section#slider_container .offers_slider_wrapper .slider_offer_description {
  color: white;
  font-weight: lighter;
  letter-spacing: 1px;
  font-size: 17px;
  font-family: 'Playfair Display', serif;
}
/* line 535, ../sass/_template_specific.scss */
section#slider_container .offers_slider_wrapper .slider_offer_description strong {
  font-weight: bolder;
}
/* line 540, ../sass/_template_specific.scss */
section#slider_container .offers_slider_wrapper .slider_offer_element {
  position: relative;
}
/* line 543, ../sass/_template_specific.scss */
section#slider_container .offers_slider_wrapper .slider_offer_element a {
  text-decoration: none;
  color: white;
  position: absolute;
  right: 23px;
  bottom: 8px;
}
/* line 550, ../sass/_template_specific.scss */
section#slider_container .offers_slider_wrapper .slider_offer_element a button {
  border-radius: 0px;
  width: 255px;
  font-weight: 500;
  font-size: 14px;
  float: none;
  display: block;
  color: white;
  border: 0;
  font-family: 'Lato', sans-serif;
  padding: 21.5px 0;
  cursor: pointer;
  line-height: 2px;
  background-image: linear-gradient(to bottom, #3b5274 0%, #8194a8 100%);
  text-transform: uppercase;
}

/* line 576, ../sass/_template_specific.scss */
body.secciones_interiores section#slider_container {
  position: relative;
  height: 275px;
  width: 100%;
}

/* line 583, ../sass/_template_specific.scss */
.slider_inner_container .slider_image {
  width: 100%;
  height: auto;
  position: fixed;
  top: 0px;
  left: 0;
  z-index: -2;
  min-width: 1920px;
}

/*====== Banners x4 =======*/
/* line 594, ../sass/_template_specific.scss */
.bannersx4_wrapper {
  display: table;
  margin: 30px 0;
}
/* line 599, ../sass/_template_specific.scss */
.bannersx4_wrapper .slides li {
  width: 25%;
  margin-right: 0;
  float: left;
  display: block;
}
/* line 607, ../sass/_template_specific.scss */
.bannersx4_wrapper .bannerx4_element {
  width: 100%;
  height: 285px;
  float: left;
  position: relative;
}
/* line 613, ../sass/_template_specific.scss */
.bannersx4_wrapper .bannerx4_element img.background_image {
  width: 100%;
  display: block;
}
/* line 618, ../sass/_template_specific.scss */
.bannersx4_wrapper .bannerx4_element .bannerx4_text {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 0;
  right: 0;
  width: 100%;
  text-align: center;
}
/* line 634, ../sass/_template_specific.scss */
.bannersx4_wrapper .bannerx4_element .bannerx4_text h4.bannerx4_title {
  font-family: 'Lato', sans-serif;
  color: white;
  letter-spacing: 2px;
}
/* line 640, ../sass/_template_specific.scss */
.bannersx4_wrapper .bannerx4_element .bannerx4_text span.separator {
  display: block;
  height: 3px;
  width: 60px;
  background: white;
  margin: auto;
  margin-top: 15px;
}
/* line 649, ../sass/_template_specific.scss */
.bannersx4_wrapper .bannerx4_element .bannerx4_text .bannerx4_description {
  color: white;
  font-size: 18px;
  margin-top: 30px;
  padding: 0 42px;
  opacity: 0;
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}
/* line 663, ../sass/_template_specific.scss */
.bannersx4_wrapper .bannerx4_element .black_overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  opacity: 1;
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}
/* line 679, ../sass/_template_specific.scss */
.bannersx4_wrapper .bannerx4_element:hover .bannerx4_description {
  opacity: 1;
}
/* line 685, ../sass/_template_specific.scss */
.bannersx4_wrapper .flex-controlador {
  text-align: center;
  margin-top: 16px;
}
/* line 689, ../sass/_template_specific.scss */
.bannersx4_wrapper .flex-controlador li {
  display: inline-table;
  margin: 0 3px;
}
/* line 695, ../sass/_template_specific.scss */
.bannersx4_wrapper span.bottom_lane {
  height: 4px;
  width: 45px;
  background: #346FEF;
  display: block;
  opacity: 0.6;
  cursor: pointer;
}
/* line 703, ../sass/_template_specific.scss */
.bannersx4_wrapper span.bottom_lane.flex-active {
  opacity: 1;
}

/*======= Content Subtitle ======*/
/* line 710, ../sass/_template_specific.scss */
.content_subtitle_wrapper {
  margin-top: 47px;
  margin-bottom: 55px;
}
/* line 714, ../sass/_template_specific.scss */
.content_subtitle_wrapper h3.content_subtitle_title {
  text-align: center;
  font-family: 'Lato', sans-serif;
  font-size: 28px;
  color: #346FEF;
  font-weight: lighter;
  margin-bottom: 16px;
}
/* line 722, ../sass/_template_specific.scss */
.content_subtitle_wrapper h3.content_subtitle_title strong {
  display: block;
  font-weight: bolder;
}
/* line 727, ../sass/_template_specific.scss */
.content_subtitle_wrapper h3.content_subtitle_title .button-promotion {
  display: table;
  text-decoration: none;
  color: #346FEF;
  font-weight: bolder;
  font-size: 16px;
  font-style: italic;
  padding: 7px;
  margin-top: 7px;
  border-top: 1px solid;
  float: right;
  border-bottom: 1px solid;
  margin-right: 2%;
}
/* line 743, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_description {
  text-align: justify;
  width: 815px;
  margin: auto;
  line-height: 30px;
  color: #5f5c5a;
}
/* line 750, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_description strong {
  font-weight: 700;
}
/* line 756, ../sass/_template_specific.scss */
.content_subtitle_wrapper.inner_section .content_subtitle_title {
  margin-top: 90px;
  font-family: 'Lato', sans-serif;
  font-size: 21px;
  letter-spacing: 3px;
  text-transform: uppercase;
  text-align: left;
  color: black;
  font-weight: 500;
}
/* line 767, ../sass/_template_specific.scss */
.content_subtitle_wrapper.inner_section .separator {
  display: block;
  height: 4px;
  width: 120px;
  background: #346FEF;
  margin-top: 20px;
  margin-bottom: 42px;
}
/* line 776, ../sass/_template_specific.scss */
.content_subtitle_wrapper.inner_section .content_subtitle_description {
  display: block;
  font-size: 15px;
  padding-left: 10px;
  padding-right: 10px;
  line-height: 24px;
  color: #5f5c5a;
  box-sizing: border-box;
  margin-bottom: 65px;
  text-align: left;
  width: 1140px;
}
/* line 788, ../sass/_template_specific.scss */
.content_subtitle_wrapper.inner_section .content_subtitle_description ul {
  margin-top: 24px;
}

/*======== Cycle Banners =======*/
/* line 797, ../sass/_template_specific.scss */
.cycle_banners_wrapper {
  background: #F5F6F8;
  padding: 50px 0;
}
/* line 801, ../sass/_template_specific.scss */
.cycle_banners_wrapper .slides > li {
  display: table;
  width: 100%;
  position: relative;
}
/* line 808, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element {
  display: table;
  position: relative;
  width: 100%;
  margin: 30px 0;
}
/* line 814, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .exceded {
  width: 50%;
  float: left;
  height: 559px;
  overflow: hidden;
}
/* line 820, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .exceded img {
  width: 100%;
  height: auto;
}
/* line 825, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .exceded.resize {
  position: relative;
}
/* line 827, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .exceded.resize img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
/* line 836, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper {
  width: 50%;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  background: white;
}
/* line 844, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_title {
  font-family: 'Lato', sans-serif;
  font-size: 33px;
  color: #2B5175;
  font-weight: 100;
  margin-bottom: 35px;
  position: relative;
}
/* line 852, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_title .plus_image {
  position: absolute;
  right: 0;
  top: -10px;
}
/* line 858, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_title strong {
  font-weight: bolder;
}
/* line 863, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description {
  font-size: 15px;
  color: #757881;
  line-height: 28px;
}
/* line 868, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description strong {
  font-weight: bolder;
}
/* line 872, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description a {
  display: table;
  text-decoration: none;
  color: #346FEF;
  font-weight: bolder;
  font-size: 16px;
  font-style: italic;
  padding: 7px;
  margin-top: 26px;
  border-top: 1px solid;
  border-bottom: 1px solid;
  text-transform: uppercase;
}
/* line 886, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description .reset-style {
  display: block;
  text-decoration: none;
  color: black;
  font-weight: normal;
  font-style: normal;
  padding: 0;
  margin-top: 5px;
  border: none;
  text-transform: none;
}
/* line 900, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .center_div {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  text-align: left;
  padding: 0 75px;
}
/* line 916, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle, .cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle-offers {
  position: absolute;
  bottom: 80px;
  right: 25%;
  width: 25%;
  text-align: left;
  z-index: 2;
  padding: 0 75px;
  padding-right: 0;
  box-sizing: border-box;
}
/* line 927, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle li, .cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle-offers li {
  display: inline-table;
  margin: 0 3px;
}
/* line 932, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle span.bottom_lane, .cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle-offers span.bottom_lane {
  height: 4px;
  width: 60px;
  background: #346FEF;
  display: block;
  opacity: 0.6;
  cursor: pointer;
}
/* line 940, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle span.bottom_lane.flex-active, .cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle-offers span.bottom_lane.flex-active {
  opacity: 1;
}
/* line 947, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element.align_right .exceded {
  float: right;
}
/* line 951, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element.align_right .cycle_text_wrapper {
  right: auto;
  left: 0;
}
/* line 956, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element.align_right ol.flex-controlador-cycle, .cycle_banners_wrapper .cycle_element.align_right .flex-controlador-cycle-offers {
  right: auto;
  left: 0;
}
/* line 963, ../sass/_template_specific.scss */
.cycle_banners_wrapper .links {
  position: absolute;
  bottom: 66px;
  left: 77px;
}
/* line 968, ../sass/_template_specific.scss */
.cycle_banners_wrapper .links a.link_cycle {
  color: #ADADAD;
  font-style: italic;
  text-decoration: none;
  font-size: 18px;
  letter-spacing: 1px;
  padding: 9px 0;
  border-top: 1px solid #DAD3D2;
  border-bottom: 1px solid #DAD3D2;
  text-transform: uppercase;
}
/* line 980, ../sass/_template_specific.scss */
.cycle_banners_wrapper .links .button-promotion {
  background: #346FEF;
  text-decoration: none;
  color: white;
  padding: 12px 40px;
  font-family: 'Lato', sans-serif;
  text-transform: uppercase;
}
/* line 988, ../sass/_template_specific.scss */
.cycle_banners_wrapper .links .button-promotion + a.link_cycle {
  margin-left: 20px;
}

/*======== Iframe ========*/
/* line 999, ../sass/_template_specific.scss */
.custom_iframe_wrapper iframe {
  width: 100%;
  margin: 0 auto;
  height: 1020px;
}

/*======== Footer ========*/
/* line 1009, ../sass/_template_specific.scss */
.wrapper_footer_columns {
  padding: 50px 0;
  position: relative;
}
/* line 1013, ../sass/_template_specific.scss */
.wrapper_footer_columns h3.footer_column_title {
  font-size: 23px;
  font-weight: 500;
  color: #2B5175;
  margin-bottom: 23px;
}
/* line 1020, ../sass/_template_specific.scss */
.wrapper_footer_columns .footer_column_description {
  font-size: 14px;
  width: 325px;
  font-family: 'Lato', sans-serif;
  color: #5f5c5a;
}
/* line 1027, ../sass/_template_specific.scss */
.wrapper_footer_columns .top_button {
  position: absolute;
  right: 0;
  bottom: 0;
  font-style: italic;
  background: #346FEF;
  color: white;
  height: 40px;
  width: 60px;
  text-align: center;
  box-sizing: border-box;
  padding-top: 15px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
/* line 1042, ../sass/_template_specific.scss */
.wrapper_footer_columns .top_button:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 40px;
  cursor: pointer;
  left: 0;
  z-index: -1;
  background: url(/img/dorae/arrow_up.png) center 20px no-repeat;
  z-index: 2;
  bottom: 15px;
}

/*======== Footer Newsletter =======*/
/* line 1061, ../sass/_template_specific.scss */
.apartamentos-portofino .social_newsletter_wrapper #social {
  display: none;
}

/* line 1067, ../sass/_template_specific.scss */
.social_newsletter_wrapper {
  height: 140px;
  box-sizing: border-box;
  background: #346FEF;
  padding: 42px 0;
  text-align: center;
  margin-bottom: 12px;
}
/* line 1075, ../sass/_template_specific.scss */
.social_newsletter_wrapper .newsletter_element {
  float: left;
  width: 65%;
  margin-top: -10px;
  text-align: left;
  display: inline-block;
}
/* line 1084, ../sass/_template_specific.scss */
.social_newsletter_wrapper #newsletter label#suscEmailLabel {
  display: none !important;
}
/* line 1088, ../sass/_template_specific.scss */
.social_newsletter_wrapper #newsletter form#form-newsletter {
  display: inline;
  font-family: 'Lato', sans-serif;
  margin-left: 18px;
}
/* line 1093, ../sass/_template_specific.scss */
.social_newsletter_wrapper #newsletter form#form-newsletter .newsletter_checkbox {
  margin-top: 3px;
  font-size: 12px;
  margin-left: 163px;
  color: white;
}
/* line 1099, ../sass/_template_specific.scss */
.social_newsletter_wrapper #newsletter form#form-newsletter .newsletter_checkbox a {
  color: white;
  font-size: 12px;
}
/* line 1106, ../sass/_template_specific.scss */
.social_newsletter_wrapper #newsletter h2#title_newsletter {
  display: inline;
  font-size: 31px;
  font-family: 'Lato', sans-serif;
  font-weight: lighter;
  color: white;
  vertical-align: middle;
}
/* line 1115, ../sass/_template_specific.scss */
.social_newsletter_wrapper #newsletter button#newsletter-button {
  width: 135px;
  height: 37px;
  display: inline;
  text-transform: uppercase;
  border: 0;
  background: white;
  color: #346FEF;
  letter-spacing: 1px;
  font-size: 12px;
}
/* line 1128, ../sass/_template_specific.scss */
.social_newsletter_wrapper div#newsletterButtonExternalDiv {
  display: inline;
}
/* line 1132, ../sass/_template_specific.scss */
.social_newsletter_wrapper input#suscEmail {
  width: 284px;
  box-sizing: border-box;
  height: 36.5px;
  vertical-align: middle;
  margin-bottom: -1px;
  padding-left: 13px;
  background: #93b2f6;
  color: white;
  border: 0;
}
/* line 1144, ../sass/_template_specific.scss */
.social_newsletter_wrapper input#suscEmail::-webkit-input-placeholder {
  color: white;
}
/* line 1148, ../sass/_template_specific.scss */
.social_newsletter_wrapper input#suscEmail:-moz-placeholder {
  color: white;
}
/* line 1152, ../sass/_template_specific.scss */
.social_newsletter_wrapper input#suscEmail::-moz-placeholder {
  color: white;
}
/* line 1156, ../sass/_template_specific.scss */
.social_newsletter_wrapper input#suscEmail:-ms-input-placeholder {
  color: white;
}

/* line 1161, ../sass/_template_specific.scss */
div#social {
  width: 35%;
  float: right;
  margin-top: 10px;
}
/* line 1166, ../sass/_template_specific.scss */
div#social a {
  text-decoration: none;
}
/* line 1169, ../sass/_template_specific.scss */
div#social a img {
  width: 60px;
  height: 60px;
  vertical-align: middle;
  margin-top: -8px;
}
/* line 1177, ../sass/_template_specific.scss */
div#social p.follow_title {
  font-size: 31px;
  font-family: 'Lato', sans-serif;
  font-weight: lighter;
  color: white;
  margin-right: 18px;
  display: inline-block;
}

/* line 1187, ../sass/_template_specific.scss */
.last_footer_wrapper {
  background: #797676;
  background-position: top center;
  display: table;
  width: 100%;
  overflow: hidden;
  padding-bottom: 20px;
}

/*======= Last Footer Wrapper =========*/
/* line 1198, ../sass/_template_specific.scss */
.links_footer_wrapper {
  text-align: left;
  padding-top: 33px;
  width: 1140px;
}
/* line 1203, ../sass/_template_specific.scss */
.links_footer_wrapper a {
  text-decoration: none;
  font-family: 'Lato', sans-serif;
  color: white;
  text-transform: uppercase;
  font-size: 12px;
  margin: 0 10px;
}
/* line 1211, ../sass/_template_specific.scss */
.links_footer_wrapper a:first-of-type {
  margin-left: 0;
}
/* line 1215, ../sass/_template_specific.scss */
.links_footer_wrapper a:hover {
  color: white;
}
/* line 1220, ../sass/_template_specific.scss */
.links_footer_wrapper .separator {
  color: white;
  font-size: 10px;
}

/* line 1226, ../sass/_template_specific.scss */
.left_elements {
  display: block;
}

/* line 1230, ../sass/_template_specific.scss */
footer {
  background: white;
}
/* line 1233, ../sass/_template_specific.scss */
footer div#div-txt-copyright {
  font-size: 12px;
  color: white;
  text-align: left;
  font-family: 'Lato', sans-serif;
  margin-bottom: 2px;
  margin-top: 6px;
  line-height: 30px;
}
/* line 1243, ../sass/_template_specific.scss */
footer .footer-copyright {
  text-align: left;
  color: white;
}
/* line 1247, ../sass/_template_specific.scss */
footer .footer-copyright a {
  text-decoration: none;
  color: white;
  font-family: 'Lato', sans-serif;
  font-size: 12px;
}
/* line 1255, ../sass/_template_specific.scss */
footer div#facebook_like {
  float: left;
  text-align: right;
  margin-top: 2px;
}
/* line 1261, ../sass/_template_specific.scss */
footer div#google_plus_one {
  padding-left: 20px;
  display: block;
  float: left;
}
/* line 1267, ../sass/_template_specific.scss */
footer .social_likes_footer {
  margin-top: 4px;
}

/* line 1272, ../sass/_template_specific.scss */
.list-logos {
  text-align: justify;
  justify-content: space-between;
  height: 160px;
  padding-top: 10px;
  box-sizing: border-box;
  margin-top: 0px !important;
}
/* line 1280, ../sass/_template_specific.scss */
.list-logos li {
  display: inline-block;
  text-align: center;
}
/* line 1284, ../sass/_template_specific.scss */
.list-logos:after {
  content: '';
  display: inline-block;
  width: 100%;
  height: 0;
}

/*========= Habitaciones =======*/
/* line 1293, ../sass/_template_specific.scss */
.rooms_blocks_wrapper {
  margin-top: 55px;
  display: table;
}
/* line 1297, ../sass/_template_specific.scss */
.rooms_blocks_wrapper .room_element {
  width: 370px;
  margin-right: 13px;
  display: inline-block;
  overflow: hidden;
  border-top: 4px solid #346FEF;
  margin-bottom: 70px;
  vertical-align: top;
}
/* line 1306, ../sass/_template_specific.scss */
.rooms_blocks_wrapper .room_element .exceded {
  height: 265px;
  overflow: hidden;
}
/* line 1310, ../sass/_template_specific.scss */
.rooms_blocks_wrapper .room_element .exceded img {
  max-width: none;
}
/* line 1315, ../sass/_template_specific.scss */
.rooms_blocks_wrapper .room_element h3.room_title {
  margin-top: 35px;
  font-family: 'Lato', sans-serif;
  font-size: 21px;
  letter-spacing: 3px;
}
/* line 1322, ../sass/_template_specific.scss */
.rooms_blocks_wrapper .room_element span.separator {
  display: block;
  height: 4px;
  width: 120px;
  background: #346FEF;
  margin-top: 20px;
  margin-bottom: 27px;
}
/* line 1331, ../sass/_template_specific.scss */
.rooms_blocks_wrapper .room_element .room_description {
  font-size: 15px;
  padding-right: 72px;
  line-height: 24px;
  color: gray;
  margin-bottom: 26px;
}
/* line 1338, ../sass/_template_specific.scss */
.rooms_blocks_wrapper .room_element .room_link_wrapper {
  display: inline-block;
}
/* line 1341, ../sass/_template_specific.scss */
.rooms_blocks_wrapper .room_element a.room_link {
  display: block;
  font-style: italic;
  text-decoration: none;
  font-size: 15px;
  color: #346FEF;
  margin-bottom: 2px;
}
/* line 1349, ../sass/_template_specific.scss */
.rooms_blocks_wrapper .room_element a.room_booking {
  display: inline-block;
  text-decoration: none;
  color: white;
  font-weight: bolder;
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  background: #346FEF;
  font-style: italic;
  padding: 12px 40px;
  border-top: 1px solid;
  border-bottom: 1px solid;
  text-transform: uppercase;
  margin-left: 40px;
  vertical-align: bottom;
}

/*======== Individual Rooms ======*/
/* line 1370, ../sass/_template_specific.scss */
.individual_room_wrapper .individual_room_title {
  margin-top: 62px;
  font-family: 'Lato', sans-serif;
  font-size: 21px;
  letter-spacing: 3px;
  text-transform: uppercase;
}
/* line 1376, ../sass/_template_specific.scss */
.individual_room_wrapper .individual_room_title a {
  display: table;
  text-decoration: none;
  color: #346FEF;
  font-weight: bolder;
  font-size: 16px;
  font-style: italic;
  padding: 7px;
  margin-top: 7px;
  border-top: 1px solid;
  float: right;
  border-bottom: 1px solid;
}
/* line 1391, ../sass/_template_specific.scss */
.individual_room_wrapper span.separator {
  display: block;
  height: 4px;
  width: 120px;
  background: #346FEF;
  margin-top: 20px;
  margin-bottom: 27px;
}
/* line 1400, ../sass/_template_specific.scss */
.individual_room_wrapper .individual_room_description {
  font-size: 15px;
  padding-right: 72px;
  line-height: 24px;
  color: gray;
  margin-bottom: 65px;
}
/* line 1407, ../sass/_template_specific.scss */
.individual_room_wrapper .individual_room_description ul {
  margin-top: 24px;
  color: #346FEF;
}
/* line 1410, ../sass/_template_specific.scss */
.individual_room_wrapper .individual_room_description ul li {
  font-style: italic;
}

/*====== Mini Gallery Individual Rooms =====*/
/* line 1418, ../sass/_template_specific.scss */
.mini_gallery_wrapper {
  margin-bottom: 25px;
}
/* line 1421, ../sass/_template_specific.scss */
.mini_gallery_wrapper .pictures_wrapper {
  margin-top: 27px;
}
/* line 1424, ../sass/_template_specific.scss */
.mini_gallery_wrapper .pictures_wrapper .exceded {
  display: inline-block;
  width: 375px;
  height: 240px;
  overflow: hidden;
}
/* line 1430, ../sass/_template_specific.scss */
.mini_gallery_wrapper .pictures_wrapper .exceded img {
  width: 100%;
}

/*===== Mini gallery bottom ====*/
/* line 1438, ../sass/_template_specific.scss */
.mini_gallery_wrapper_advanced {
  margin-bottom: 25px;
}
/* line 1441, ../sass/_template_specific.scss */
.mini_gallery_wrapper_advanced .pictures_wrapper {
  margin-top: 27px;
}
/* line 1444, ../sass/_template_specific.scss */
.mini_gallery_wrapper_advanced .pictures_wrapper .exceded {
  display: inline-block;
  width: 375px;
  height: 240px;
  overflow: hidden;
}
/* line 1450, ../sass/_template_specific.scss */
.mini_gallery_wrapper_advanced .pictures_wrapper .exceded img {
  min-height: 240px;
  width: auto;
  max-width: 100%;
}

/*===== Location and Rservation ======*/
/* line 1460, ../sass/_template_specific.scss */
.location_reservation_wrapper {
  margin-top: 55px;
  display: table;
  width: 100%;
}

/* line 1466, ../sass/_template_specific.scss */
.location_info, .reservation_info {
  width: 560px;
  background: #F6F7F9;
  float: left;
  padding: 31px 42px;
  box-sizing: border-box;
}

/* line 1474, ../sass/_template_specific.scss */
.reservation_info {
  float: right;
}

/* line 1479, ../sass/_template_specific.scss */
.location_info h3.location_title {
  font-family: 'Lato', sans-serif;
  font-size: 21px;
  text-transform: uppercase;
}
/* line 1485, ../sass/_template_specific.scss */
.location_info span.separator {
  display: block;
  height: 2px;
  width: 83px;
  background: #346FEF;
  margin-top: 20px;
  margin-bottom: 20px;
}

/* line 1495, ../sass/_template_specific.scss */
.location_description {
  font-family: 'Lato', sans-serif;
  color: gray;
  font-weight: 300;
}
/* line 1500, ../sass/_template_specific.scss */
.location_description strong {
  font-weight: 500;
}

/* line 1506, ../sass/_template_specific.scss */
.reservation_info .reservation_title {
  font-family: 'Lato', sans-serif;
  font-size: 21px;
  text-transform: uppercase;
}
/* line 1512, ../sass/_template_specific.scss */
.reservation_info span.separator {
  display: block;
  height: 2px;
  width: 83px;
  background: #346FEF;
  margin-top: 20px;
  margin-bottom: 20px;
}
/* line 1521, ../sass/_template_specific.scss */
.reservation_info .reservation_description {
  font-family: 'Lato', sans-serif;
  color: gray;
  font-weight: 300;
}
/* line 1526, ../sass/_template_specific.scss */
.reservation_info .reservation_description strong {
  font-weight: 500;
}

/* line 1532, ../sass/_template_specific.scss */
.google_maps_wrapper {
  margin-top: 15px;
}

/* line 1536, ../sass/_template_specific.scss */
.cancel_booking_questions {
  margin-top: 15px;
}

/* line 1540, ../sass/_template_specific.scss */
.location_reservation_wrapper + #reservation {
  background: #F6F7F9;
}
/* line 1545, ../sass/_template_specific.scss */
.location_reservation_wrapper + #reservation:not(:empty) {
  padding: 40px 0;
}
/* line 1549, ../sass/_template_specific.scss */
.location_reservation_wrapper + #reservation .grid_12.alpha.my-bookings-booking-info {
  margin: auto;
}
/* line 1553, ../sass/_template_specific.scss */
.location_reservation_wrapper + #reservation .fResumenReserva {
  background: white;
  border: 1px solid #346FEF;
  box-shadow: 0px 0px 13px black;
}
/* line 1559, ../sass/_template_specific.scss */
.location_reservation_wrapper + #reservation h2.titulo.mt15.mb15 {
  font-family: 'Lato', sans-serif;
  font-size: 21px;
  text-transform: uppercase;
}
/* line 1565, ../sass/_template_specific.scss */
.location_reservation_wrapper + #reservation b {
  font-weight: bolder;
}
/* line 1569, ../sass/_template_specific.scss */
.location_reservation_wrapper + #reservation h3 {
  font-weight: bolder;
  font-size: 20px;
  margin-bottom: 10px !important;
  font-family: 'Lato', sans-serif;
}

/* line 1577, ../sass/_template_specific.scss */
button#cancelButton {
  margin: 20px auto;
  border-radius: 0px !important;
  height: 39px !important;
  width: 170px !important;
  background: linear-gradient(to bottom, #af9f85 0%, #cfba97 50%, #a29277 100%);
  color: white;
  text-transform: uppercase;
  border: 0;
  display: none;
}

/* line 1589, ../sass/_template_specific.scss */
#my-bookings-form-fields {
  margin-top: 30px;
  display: block !important;
}
/* line 1593, ../sass/_template_specific.scss */
#my-bookings-form-fields input#emailInput, #my-bookings-form-fields input#localizadorInput {
  display: block;
  margin-top: 17px;
  width: 400px;
  height: 40px;
  border: 0;
  box-sizing: border-box;
  border-bottom: 1px solid #346FEF;
  padding-left: 20px;
  font-size: 14px;
  background: #e6e6e6;
}
/* line 1606, ../sass/_template_specific.scss */
#my-bookings-form-fields input#localizadorInput {
  margin-top: 10px;
}
/* line 1610, ../sass/_template_specific.scss */
#my-bookings-form-fields button#my-bookings-form-search-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 102px !important;
  background: linear-gradient(to bottom, #346fef 0%, #346fef 50%, #346fef 100%);
  color: white;
  margin: 13px auto 0 !important;
  text-transform: uppercase;
  border: 0;
}

/* line 1622, ../sass/_template_specific.scss */
.contact_form_wrapper {
  background: #F6F7F9;
  padding: 31px 35px;
  margin-top: 20px;
}
/* line 1627, ../sass/_template_specific.scss */
.contact_form_wrapper h3.contact_title {
  text-align: center;
  font-family: 'Lato', sans-serif;
  font-size: 22px;
  text-transform: uppercase;
}
/* line 1634, ../sass/_template_specific.scss */
.contact_form_wrapper span.separator {
  display: block;
  height: 2px;
  width: 90px;
  background: #346FEF;
  margin: auto;
  margin-top: 21px;
}
/* line 1643, ../sass/_template_specific.scss */
.contact_form_wrapper form {
  margin-top: 0 !important;
}
/* line 1646, ../sass/_template_specific.scss */
.contact_form_wrapper form li {
  width: 33%;
  display: inline-block;
}
/* line 1650, ../sass/_template_specific.scss */
.contact_form_wrapper form li label {
  display: block;
  font-family: 'Lato', sans-serif;
  color: gray;
  margin-bottom: 0;
  margin-top: 19px;
  font-size: 11px;
  letter-spacing: 1px;
}
/* line 1659, ../sass/_template_specific.scss */
.contact_form_wrapper form li label.error {
  display: none !important;
}
/* line 1664, ../sass/_template_specific.scss */
.contact_form_wrapper form li input {
  display: block;
  margin-top: 11px;
  width: 97%;
  height: 40px;
  border: 0;
  box-sizing: border-box;
  border-bottom: 1px solid #346FEF;
  padding-left: 20px;
  font-size: 14px;
}
/* line 1675, ../sass/_template_specific.scss */
.contact_form_wrapper form li input.input-error {
  border-bottom: 1px solid red;
}
/* line 1682, ../sass/_template_specific.scss */
.contact_form_wrapper form .block-right ul li {
  width: 98.3%;
}
/* line 1685, ../sass/_template_specific.scss */
.contact_form_wrapper form .block-right a {
  font-family: 'Lato', sans-serif;
  color: gray;
  margin-bottom: 0;
  margin-top: 19px;
  font-size: 11px;
  letter-spacing: 1px;
}
/* line 1694, ../sass/_template_specific.scss */
.contact_form_wrapper form .block-right .g-recaptcha div {
  margin: 1em 0;
}
/* line 1700, ../sass/_template_specific.scss */
.contact_form_wrapper form textarea#comments {
  width: 100%;
  display: block;
  margin-top: 11px;
  height: 85px;
  border: 0;
  padding-top: 12px;
  box-sizing: border-box;
  border-bottom: 1px solid #346FEF;
  padding-left: 20px;
  font-size: 14px;
}
/* line 1713, ../sass/_template_specific.scss */
.contact_form_wrapper form a#contact-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 102px !important;
  background: linear-gradient(to bottom, #346fef 0%, #346fef 50%, #346fef 100%);
  color: white;
  margin: 29px 0 0 !important;
  text-transform: uppercase;
  border: 0;
  font-size: 13px;
  display: block;
  font-family: 'Lato', sans-serif;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
}
/* line 1729, ../sass/_template_specific.scss */
.contact_form_wrapper form a#contact-button:hover {
  opacity: 0.8;
}

/*===== Automatic Content Access =====*/
/* line 1737, ../sass/_template_specific.scss */
.content_access_wrapper {
  margin-top: 35px;
}

/*== Galleries Personalized ===*/
/* line 1743, ../sass/_template_specific.scss */
#lightbox .bottom_overlay {
  position: absolute;
  height: 60px;
  left: 5px;
  right: 5px;
  bottom: 4px;
  background: rgba(0, 0, 0, 0.66);
}
/* line 1752, ../sass/_template_specific.scss */
#lightbox a.lb-next, #lightbox a.lb-prev {
  position: absolute;
  left: 160px;
  bottom: 5px;
  opacity: 1 !important;
  top: auto !important;
  height: 59px;
  width: 50px !important;
}
/* line 1762, ../sass/_template_specific.scss */
#lightbox a.lb-prev {
  left: 0px;
}
/* line 1766, ../sass/_template_specific.scss */
#lightbox a.lb-close {
  right: 0;
  margin-top: -58px;
  margin-right: 21px;
  background-size: 38px !important;
  height: 40px !important;
  width: 40px !important;
  opacity: 1 !important;
}
/* line 1776, ../sass/_template_specific.scss */
#lightbox .lb-dataContainer {
  position: relative;
  z-index: 120;
}

/*======= Servicios =======*/
/* line 1783, ../sass/_template_specific.scss */
#wrapper_services {
  text-align: center;
}

/* line 1787, ../sass/_template_specific.scss */
#services {
  color: black;
  margin-bottom: 56px;
}

/* line 1792, ../sass/_template_specific.scss */
.service_title {
  text-transform: uppercase;
  font-size: 14px;
  margin: 15px 0;
  color: #5B5B5B;
  font-weight: bold;
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
  font-family: 'Lato', sans-serif;
}

/* line 1806, ../sass/_template_specific.scss */
.service_description {
  color: #5B5B5B;
  font-size: 13px;
  margin-bottom: 10px;
  line-height: 15px;
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}

/* line 1818, ../sass/_template_specific.scss */
.service {
  text-align: center;
  margin-bottom: 20px;
  float: none;
  display: inline-block;
  width: 262px !important;
  vertical-align: top;
}
/* line 1827, ../sass/_template_specific.scss */
.service:hover .service_title {
  color: #346FEF;
}

/*======== Ofertas =======*/
/* line 1835, ../sass/_template_specific.scss */
.offers_wrapper {
  background: white;
  padding: 0;
  margin-top: -10px;
}
/* line 1840, ../sass/_template_specific.scss */
.offers_wrapper .cycle_element .cycle_text_wrapper {
  background: #F5F6F8;
}
/* line 1844, ../sass/_template_specific.scss */
.offers_wrapper .links {
  position: initial;
}
/* line 1847, ../sass/_template_specific.scss */
.offers_wrapper .links .button-promotion {
  color: white !important;
  display: inline-block !important;
  padding: 12px 40px !important;
  font-family: 'Lato', sans-serif;
  text-transform: uppercase !important;
}
/* line 1855, ../sass/_template_specific.scss */
.offers_wrapper .links .link_cycle {
  display: inline-block !important;
  color: #ADADAD !important;
  font-style: italic !important;
  text-decoration: none;
  font-size: 18px !important;
  letter-spacing: 1px !important;
  padding: 9px 0 !important;
  border-top: 1px solid #DAD3D2 !important;
  border-bottom: 1px solid #DAD3D2 !important;
  text-transform: uppercase !important;
}

/*===== Events ======*/
/* line 1872, ../sass/_template_specific.scss */
.dest_wrapper {
  background-color: white;
  min-height: 500px;
  margin-bottom: 40px;
}

/* line 1878, ../sass/_template_specific.scss */
.destinations_elements_wrapper {
  margin-top: 25px;
  display: table;
}

/* line 1883, ../sass/_template_specific.scss */
.title-dest {
  margin-bottom: 20px;
  margin-top: 31px;
  font-family: 'Lato', sans-serif;
  font-size: 21px;
  letter-spacing: 3px;
  text-transform: uppercase;
  text-align: left;
  color: black;
  font-weight: 500;
  padding-left: 30px;
}

/* line 1896, ../sass/_template_specific.scss */
.desc-dest {
  padding-left: 30px;
  padding-bottom: 20px;
  font-size: 15px;
  padding-right: 30px;
  line-height: 24px;
  color: gray;
  margin-bottom: 20px;
  text-align: left;
  width: 1140px;
  box-sizing: border-box;
}

/* line 1909, ../sass/_template_specific.scss */
.link-dest {
  cursor: pointer;
  padding-left: 0px;
  padding-bottom: 20px;
  color: #346FEF;
  margin-top: 10px;
  font-weight: bold;
  font-family: 'Lato', sans-serif;
}

/* line 1919, ../sass/_template_specific.scss */
div#dest-img-wrapper-1 {
  float: left;
  width: 529px !important;
  height: 380px !important;
  position: relative;
}

/* line 1926, ../sass/_template_specific.scss */
div#dest-img-wrapper-2 {
  float: left;
  width: 611px;
  height: 194px;
}

/* line 1932, ../sass/_template_specific.scss */
div#dest-img-wrapper-3 {
  float: left;
  width: 305px;
  height: 190px;
}

/* line 1938, ../sass/_template_specific.scss */
div#dest-img-wrapper-4 {
  float: left;
  width: 306px;
  height: 190px;
}

/* line 1944, ../sass/_template_specific.scss */
.gallery_destination_wrapper {
  position: relative;
}

/* line 1948, ../sass/_template_specific.scss */
.dest-img {
  z-index: 20;
  position: relative;
  cursor: pointer;
  height: 100%;
  max-width: none;
  min-width: 100%;
}

/* line 1957, ../sass/_template_specific.scss */
.dest-img-wrapper {
  height: 190px !important;
  width: 305.5px !important;
  overflow: hidden;
}
/* line 1962, ../sass/_template_specific.scss */
.dest-img-wrapper iframe {
  position: absolute;
  top: 0px;
  bottom: 0px !important;
  height: 100%;
  left: 0px;
  right: 0px;
}

/* line 1973, ../sass/_template_specific.scss */
.dest_wrapper span.separator {
  display: block;
  height: 3px;
  width: 60px;
  background: #346FEF;
  margin-top: 15px;
  margin-left: 30px;
  margin-bottom: 18px;
}

/* line 1984, ../sass/_template_specific.scss */
.gray_background {
  background: #F5F6F8;
  padding: 57px 0;
}

@-moz-document url-prefix() {
  /* line 1990, ../sass/_template_specific.scss */
  .rooms_number .label {
    top: 16px !important;
  }

  /* line 1994, ../sass/_template_specific.scss */
  .bannersx4_wrapper .bannerx4_element .bannerx4_text {
    height: 140px;
    display: block;
  }

  /* line 1999, ../sass/_template_specific.scss */
  .cycle_banners_wrapper .cycle_element .cycle_text_wrapper .center_div {
    display: block;
    height: 503px;
  }

  /* line 2004, ../sass/_template_specific.scss */
  .rooms_blocks_wrapper .room_element {
    width: 367px;
  }
}
/*===== My Bookings ======*/
/* line 2011, ../sass/_template_specific.scss */
h3.section-title {
  margin-top: 62px;
  font-family: 'Lato', sans-serif;
  font-size: 21px;
  letter-spacing: 3px;
  text-transform: uppercase;
  text-align: left;
  color: black;
  font-weight: 500;
  margin-bottom: 15px;
}

/* line 2024, ../sass/_template_specific.scss */
.reservation_content_wrapper h3.section-title {
  margin-top: 0;
  font-family: 'Lato', sans-serif;
  font-size: 21px;
  text-transform: uppercase;
  color: #b5b5b5;
}
/* line 2031, ../sass/_template_specific.scss */
.reservation_content_wrapper h3.section-title:after {
  content: "";
  display: block;
  height: 2px;
  width: 83px;
  background: #346FEF;
  margin-top: 20px;
  margin-bottom: 20px;
}

/* line 2043, ../sass/_template_specific.scss */
.cancel_booking_questions {
  font-family: 'Lato', sans-serif;
}

/*===== Offers Cycle Home =====*/
/* line 2048, ../sass/_template_specific.scss */
a.offers_section_link {
  position: absolute;
  bottom: 66px;
  right: 53px;
  color: #ADADAD;
  font-style: italic;
  text-decoration: none;
  font-size: 18px;
  letter-spacing: 1px;
  padding: 9px 0;
  border-top: 1px solid #DAD3D2;
  border-bottom: 1px solid #DAD3D2;
}
/* line 2061, ../sass/_template_specific.scss */
a.offers_section_link:hover {
  opacity: 0.8;
}

/*======= Upper Headear ======*/
/* line 2067, ../sass/_template_specific.scss */
#upper_header {
  background: #797676;
  position: relative;
  height: 70px;
  z-index: 99;
}
/* line 2073, ../sass/_template_specific.scss */
#upper_header .header_links {
  float: right;
  position: absolute;
  bottom: 0;
  top: 0;
  height: 23px;
  margin: auto;
  right: 190px;
}
/* line 2083, ../sass/_template_specific.scss */
#upper_header .header_links span {
  font-size: 12px;
  color: white;
  font-family: 'Lato', sans-serif;
}
/* line 2089, ../sass/_template_specific.scss */
#upper_header .header_links img {
  vertical-align: top;
  margin-right: 2px;
  display: inline-block;
  margin-top: 5px;
}
/* line 2096, ../sass/_template_specific.scss */
#upper_header .header_links a {
  text-decoration: none;
  color: white;
  font-size: 12px;
  padding: 0 3px;
}
/* line 2102, ../sass/_template_specific.scss */
#upper_header .header_links a span {
  font-family: 'Lato', sans-serif;
}
/* line 2108, ../sass/_template_specific.scss */
#upper_header .socials_links {
  float: left;
  position: absolute;
  height: 30px;
  top: 0;
  bottom: 0;
  margin: auto;
  left: 20px;
}
/* line 2117, ../sass/_template_specific.scss */
#upper_header .socials_links a {
  text-decoration: none;
  display: inline-block;
  margin-right: 6px;
  height: 32px;
}
/* line 2123, ../sass/_template_specific.scss */
#upper_header .socials_links a img {
  display: block;
  width: 35px;
}
/* line 2129, ../sass/_template_specific.scss */
#upper_header .socials_links .separator {
  display: inline-block;
  color: white;
  font-size: 12px;
  vertical-align: top;
  margin-top: 10px;
  font-weight: bold;
}

/* line 2140, ../sass/_template_specific.scss */
.destino {
  width: 211px;
  float: right;
  margin-top: 40px;
  margin-right: -20px;
}

/*===== Iframe Footer =====*/
/* line 2148, ../sass/_template_specific.scss */
.iframe_footer {
  float: right;
  height: 160px;
  width: 138px;
}
/* line 2153, ../sass/_template_specific.scss */
.iframe_footer iframe {
  -moz-transform: scale(0.6, 0.6);
  -webkit-transform: scale(0.6, 0.6);
  -o-transform: scale(0.6, 0.6);
  -ms-transform: scale(0.6, 0.6);
  transform: scale(0.6);
  -moz-transform-origin: top left;
  -webkit-transform-origin: top left;
  -o-transform-origin: top left;
  -ms-transform-origin: top left;
  transform-origin: top right;
  padding-top: 69px;
  height: 149px;
}

/*====== News Section =====*/
/* line 2170, ../sass/_template_specific.scss */
.block-new-description ul {
  bottom: -1px;
}

/* line 2174, ../sass/_template_specific.scss */
.photo-container {
  width: 300px;
  height: 201px;
  overflow: hidden;
  position: relative;
}
/* line 2180, ../sass/_template_specific.scss */
.photo-container img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 509px;
}

/* line 2192, ../sass/_template_specific.scss */
#new-detail .description p {
  text-align: justify;
}

/* line 2196, ../sass/_template_specific.scss */
#new-detail #new-image p {
  font-size: 14px;
  color: #346FEF;
  text-align: left;
  margin-top: 15px;
}

/* line 2203, ../sass/_template_specific.scss */
.share-this {
  margin-top: 35px;
}
/* line 2206, ../sass/_template_specific.scss */
.share-this p {
  text-align: left;
  float: left;
  margin-right: 15px;
  font-weight: 400;
  color: #346FEF;
}

/* line 2216, ../sass/_template_specific.scss */
.block-new-description h4.news-title {
  font-family: 'Lato', sans-serif;
  font-size: 21px;
}
/* line 2220, ../sass/_template_specific.scss */
.block-new-description ul .read-more-news {
  text-decoration: none;
  font-family: 'Lato', sans-serif;
}
/* line 2225, ../sass/_template_specific.scss */
.block-new-description span.news_description {
  font-size: 15px;
  padding-right: 72px;
  line-height: 24px;
  color: gray;
  margin-bottom: 26px;
}
/* line 2233, ../sass/_template_specific.scss */
.block-new-description .date {
  font-size: 17px;
  font-family: 'Lato', sans-serif;
}

/* line 2239, ../sass/_template_specific.scss */
.content-news {
  font-size: 15px;
  padding-right: 72px;
  line-height: 24px;
  color: gray;
  margin-bottom: 26px;
}
/* line 2246, ../sass/_template_specific.scss */
.content-news h1 {
  font-family: 'Lato', sans-serif;
  font-size: 21px;
  letter-spacing: 3px;
}

/* line 2254, ../sass/_template_specific.scss */
.content_access_wrapper h3.news_title {
  display: block !important;
  margin-bottom: 40px;
  position: relative;
  text-align: center;
  color: #346FEF;
  text-transform: uppercase;
  font-family: 'Lato', sans-serif;
  font-size: 26px;
  font-weight: 500;
}

/* line 2268, ../sass/_template_specific.scss */
#new-detail .description {
  font-size: 15px;
  padding-right: 72px;
  line-height: 24px;
  color: gray;
  margin-bottom: 26px;
}
/* line 2276, ../sass/_template_specific.scss */
#new-detail #new-image img + p {
  font-size: 17px;
  font-family: 'Lato', sans-serif;
}

/* line 2283, ../sass/_template_specific.scss */
.datepicker_fixed {
  position: fixed;
  top: 130px !important;
  left: 390px !important;
}

/* line 2291, ../sass/_template_specific.scss */
header .tick_element {
  color: white;
  display: inline-block;
  vertical-align: top;
  font-size: 12px;
  height: 15px;
  margin: 0 5px;
  margin-top: 6px;
  font-family: 'Lato', sans-serif;
}
/* line 2303, ../sass/_template_specific.scss */
header .tick_element .tick_image {
  display: inline-block;
  margin-right: 5px;
}
/* line 2309, ../sass/_template_specific.scss */
header .tick_element .tick_text {
  display: inline-block;
  vertical-align: top;
  margin-top: 3px;
}
/* line 2315, ../sass/_template_specific.scss */
header .logos_slider_wrapper {
  width: auto;
  position: absolute;
  right: 10px;
  display: inline-block;
}
/* line 2320, ../sass/_template_specific.scss */
header .logos_slider_wrapper .logo_slider {
  margin: 15px;
  width: 90px;
  text-align: center;
}

/* line 2330, ../sass/_template_specific.scss */
.ticks_footer {
  margin-top: 15px;
  display: flex;
  width: 100%;
  justify-content: center;
}
/* line 2336, ../sass/_template_specific.scss */
.ticks_footer .tick_element {
  margin-right: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
/* line 2342, ../sass/_template_specific.scss */
.ticks_footer .tick_element .tick_imagen {
  display: inline-block;
  height: 126px;
  width: 127px;
}
/* line 2347, ../sass/_template_specific.scss */
.ticks_footer .tick_element .tick_imagen img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
/* line 2354, ../sass/_template_specific.scss */
.ticks_footer .tick_element .tick_text {
  display: inline-block;
  color: white;
  vertical-align: top;
  margin-top: 5px;
  font-family: 'Lato', sans-serif;
}

/*===== Booking widget personalized =====*/
/* line 2365, ../sass/_template_specific.scss */
.date_box.entry_date .date_day, .date_box.departure_date .date_day, .selectricWrapper .selectric .label {
  color: #346FEF;
}

/* line 2371, ../sass/_template_specific.scss */
#full_wrapper_booking .stay_selection .entry_date_wrapper,
#full_wrapper_booking .stay_selection .departure_date_wrapper {
  width: 170px;
  border-top: 1px solid lightgrey;
}
/* line 2375, ../sass/_template_specific.scss */
#full_wrapper_booking .stay_selection .entry_date_wrapper .date_box.entry_date,
#full_wrapper_booking .stay_selection .entry_date_wrapper .date_box.departure_date,
#full_wrapper_booking .stay_selection .departure_date_wrapper .date_box.entry_date,
#full_wrapper_booking .stay_selection .departure_date_wrapper .date_box.departure_date {
  margin-top: 9px;
}
/* line 2378, ../sass/_template_specific.scss */
#full_wrapper_booking .stay_selection .entry_date_wrapper .date_box.entry_date .date_day,
#full_wrapper_booking .stay_selection .entry_date_wrapper .date_box.departure_date .date_day,
#full_wrapper_booking .stay_selection .departure_date_wrapper .date_box.entry_date .date_day,
#full_wrapper_booking .stay_selection .departure_date_wrapper .date_box.departure_date .date_day {
  border-bottom-width: 0 !important;
}
/* line 2381, ../sass/_template_specific.scss */
#full_wrapper_booking .stay_selection .entry_date_wrapper .date_box.entry_date .date_year,
#full_wrapper_booking .stay_selection .entry_date_wrapper .date_box.departure_date .date_year,
#full_wrapper_booking .stay_selection .departure_date_wrapper .date_box.entry_date .date_year,
#full_wrapper_booking .stay_selection .departure_date_wrapper .date_box.departure_date .date_year {
  display: none;
}
/* line 2387, ../sass/_template_specific.scss */
#full_wrapper_booking .room_list_wrapper {
  position: relative;
  z-index: 2;
}
/* line 2390, ../sass/_template_specific.scss */
#full_wrapper_booking .room_list_wrapper ul.room_list {
  border-bottom: 1px solid lightgrey;
  border-right: 1px solid lightgrey;
}
/* line 2395, ../sass/_template_specific.scss */
#full_wrapper_booking .wrapper_booking_button {
  width: 495px;
}
/* line 2397, ../sass/_template_specific.scss */
#full_wrapper_booking .wrapper_booking_button .promocode_wrapper {
  width: 225px;
}
/* line 2399, ../sass/_template_specific.scss */
#full_wrapper_booking .wrapper_booking_button .promocode_wrapper input.promocode_input {
  font-size: 11px;
}
/* line 2403, ../sass/_template_specific.scss */
#full_wrapper_booking .wrapper_booking_button .submit_button {
  float: right;
}

/* line 2410, ../sass/_template_specific.scss */
#full_wrapper_booking .stay_selection .departure_date_wrapper {
  width: 170px;
  border-right-width: 0;
}

/* line 2416, ../sass/_template_specific.scss */
section#slider_container .offers_slider_wrapper .slider_offer_element a button {
  background: #346FEF;
}

/* line 2420, ../sass/_template_specific.scss */
html[lang="nl"] #full_wrapper_booking .wrapper_booking_button .submit_button.buttonsearch-ratecheck {
  font-size: 19px;
}
/* line 2423, ../sass/_template_specific.scss */
html[lang="nl"] .rooms_blocks_wrapper .room_element a.room_booking {
  font-size: 12px;
}

/* line 2428, ../sass/_template_specific.scss */
#lang ul li {
  padding-left: 5px;
}
/* line 2430, ../sass/_template_specific.scss */
#lang ul li a {
  font-weight: 600;
  font-size: 10px;
  text-align: left;
}
