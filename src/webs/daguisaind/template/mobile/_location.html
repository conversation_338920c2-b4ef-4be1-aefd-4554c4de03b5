<div class="location_wrapper">
    {% if section_content %}
        <div class="location_content">
            {% if section_content.subtitle %}
                <h2 class="section-subtitle">{{ section_content.subtitle|safe }}</h2>
            {% else %}
                <h1 class="section-title">{{ section_content.title|safe }}</h1>
            {% endif %}

            {% if section_content.content %}
                <div class="contact_content_element">
                    {{ section_content.content|safe }}
                </div>
            {% endif %}
        </div>
    {% endif %}

    <form name="contact" id="contact" method="post" action="/utils/?action=contact">

        <input type="hidden" name="action" id="action" value="contact"/>
        <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}">

        <div class="info">

            <div data-role="fieldContain">
                <input type="text" id="name" name="name" value="" placeholder="{{ T_nombre_y_apellidos }}">
            </div>

            <div data-role="fieldContain">
                <input type="text" id="email" name="email" value="" placeholder="{{ T_email }} ">
            </div>

            <div data-role="fieldContain">
                <input type="text" id="emailConfirmation" name="emailConfirmation" value="" placeholder="{{ T_confirm_email }}">
            </div>

            <div data-role="fieldContain">
                <input type="text" id="telephone" name="telephone" value="" placeholder="{{ T_telefono }}">
            </div>

            <div data-role="fieldContain">
                <textarea type="text" id="comments" name="comments" value="" placeholder="{{ T_comentarios }}"></textarea>
            </div>

            {% if captcha_box %}
                <script src='https://www.google.com/recaptcha/api.js'></script>
                <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>
            {% endif %}

            <div data-role="fieldContain" class="check_element">
                <input class="check_privacy" id="privacy" name="privacy" type="checkbox" value="privacy"/>
                <span class="title">
                    <a data-fancybox
                       data-options='{"caption" : "{{ T_politica_de_privacidad }}", "src" : "/{{ language_code }}/?sectionContent={% if custom_lopd_link%}{{ custom_lopd_link }}{% else %}politica-de-privacidad.html{% endif %}", "type" : "iframe", "width" : "100%", "max-width" : "100%"}'
                       data-width="1200"
                       href="/{{ language_code }}/?sectionContent={% if custom_lopd_link%}{{ custom_lopd_link }}{% else %}politica-de-privacidad.html{% endif %}">{{ T_lopd }}</a>
                </span>
            </div>

            <div data-role="fieldContain" class="check_element">
                <input class="check_privacy" id="promotions" name="promotions" type="checkbox" value="promotions"/>
                <span class="title">
                    <label for="promotions">{{T_acepto_promociones}}</label>
                </span>
            </div>

            <div class="contact_button_wrapper">
                <a data-role="button" data-theme="b">
                    <div id="contact-button">
                        {{ T_enviar }}
                    </div>
                </a>
            </div>
        </div>


    </form>
    {% if iframe_google_maps %}
        <div class="map">
            {{ iframe_google_maps|safe }}
        </div>
    {% endif %}
</div>

<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script type="text/javascript">
$(window).load(function () {
    jQuery.validator.addMethod("phone", function (phone_number, element) {
        phone_number = phone_number.replace(/\s+/g, "");
        return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                phone_number.match(/^[0-9 \+]\d+$/);
    }, "Please specify a valid phone number");
    $("#contact").validate({
        rules: {
            name: "required",
            email: {
                required: true,
                email: true
            },
            emailConfirmation: {
                required: true,
                equalTo: "#email",
                email: true
            },
            telephone: {
                required: true,
                phone: true
            },
            comments: "required",
            privacy: "required"
        },
        messages: {
            name: "{{ T_campo_obligatorio}}",
            email: {
                required: "{{ T_campo_obligatorio|safe }}",
                email: "{{ T_campo_valor_invalido|safe }}"
            },
            emailConfirmation: {
                required: "{{ T_campo_obligatorio|safe }}",
                email: "{{ T_campo_valor_invalido|safe }}",
                equalTo: "{{T_not_confirmed_email_warning|safe}}"
            },
            telephone: {
                required: "{{ T_campo_obligatorio|safe }}",
                phone: "{{ T_campo_valor_invalido|safe }}"
            },
            comments: "{{ T_campo_obligatorio|safe }}",
            privacy: "{{ T_campo_obligatorio|safe }}"
        }
    });
    sending_form = false;
    $("#contact-button").click(function () {
        if(!sending_form){
            sending_form = true;
            if ($("#contact").valid()) {
                var params = {
                    'name': $("#name").val(),
                    'telephone': $("#telephone").val(),
                    'email': $("#email").val(),
                    'comments': $("#comments").val(),
                    'section': $("#section-name").val(),
                    'newsletter': $("#promotions").val()
                };
                if (recaptcha_valid()) {
                    params['g-recaptcha-response'] = $("#g-recaptcha-response").val();
                }
                if (recaptcha_valid() || !has_recaptcha()) {
                    $.post(
                        "/utils/?action=contact", params,
                        function (data) {
                            alert("{{ T_gracias_contacto }}");
                            $("#name").val("");
                            $("#telephone").val("");
                            $("#email").val("");
                            $("#emailConfirmation").val("");
                            $("#comments").val("");
                            sending_form = false;
                        }
                    );
                } else {
                    sending_form = false;
                }


            } else {
                    sending_form = false;
                }
        }
    });

    function has_recaptcha() {
        return $(".g-recaptcha").length;
    }

    function recaptcha_valid() {
        return has_recaptcha() && $("#g-recaptcha-response").val()
    }
});
</script>