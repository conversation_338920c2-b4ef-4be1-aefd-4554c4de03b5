
    <div class="main-content-blog container12">

        {% if noticias %}

            {% for currentItem in noticias %}

                {% if not forloop.first %}
                    <div class="blog-separator"></div>
                {% endif %}

                    <div class="wrapper-blog-entry">

                        <div class="entry-img">
                            <img src="{{ currentItem.servingUrl }}" alt="{{ currentItem.title|safe }}" title="{{ currentItem.title|safe }}">
                        </div>

                        <div class="entry-description">

                            <div class="entry-date">{{ currentItem.date|safe }}</div>

                            <div class="entry-title"><h4 class="entries-title">{{ currentItem.title|safe }}</h4></div>

                            <div class="entry-content">{{ currentItem.description|safe }}</div>

                        </div>

                        <a class="blog-view-more myFancyPopupAuto" href="#entry_full_description_{{ forloop.counter}}">{{ T_ver_mas }}</a>

                    </div>


                    <div class="entry_full_description" id="entry_full_description_{{ forloop.counter }}" style="display: none">

                        <div class="entry-date">{{ currentItem.date|safe }}</div>

                        <div class="entry-title"><h4 class="entries-title">{{ currentItem.title|safe }}</h4></div>

                        <div class="entry-content">{{ currentItem.description|safe }}</div>

                    </div>

            {% endfor %}

        {% else %}

             <div class="no-news">{{ accionistas_section.content|safe }}</div>

        {% endif %}

    </div>
