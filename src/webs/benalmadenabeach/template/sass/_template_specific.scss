header {
  #main_menu {
    #main-sections-inner {
      .main-section-div-wrapper:lang(de) {
        padding: 15px 12px;
      }
    }
  }
}

body {
  font-family: $title_family;

  * {
    box-sizing: border-box;
  }


  .default_button {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    background: #3f869f;
    color: white;
    font-size: 16px;
    font-family: "Ropa Sans", sans-serif;
    letter-spacing: 3px;
    text-transform: uppercase;
    overflow: hidden;
    text-align: center;
    width: 100%;
    border-radius: 0;
    height: 65px;
    padding: 15px;
  }

  .aviso_cookie {
    position: fixed;
    top: auto;
    bottom: 10px;
    right: 10px;
    width: 430px;
    height: auto;
    padding: 20px 30px;
    background: rgba(0, 0, 0, 0.8);
  }

  .range-age {
    font-size: 9px;
    white-space: nowrap;
    vertical-align: top;
  }

  &:not(.inner_section) {
    #slider_container {
      height: 100vh;

      .forcefullwidth_wrapper_tp_banner {
        height: 100% !important;
        overflow: hidden;
      }

      .widget_slider {
        @include center_y;
        z-index: 30;
        right: calc((100% - 1140px) / 2);
        width: 120px;
        background: rgba(white, .9);
        overflow: hidden;

        h2 {
          position: relative;
          background: rgba($corporate_1, .9);
          font-size: 8px;
          padding: 5px 30px 5px 5px;
          color: white;
          @extend .fa-caret-down;

          &:before {
            @extend .fa;
            @include center_y;
            right: 10px;
            font-size: 14px;
          }
        }

        .content {
          text-align: center;

          img {
            margin: 10px auto;
          }
        }
      }

      .slider_text {
        background: rgba($corporate_1, .9);
        position: relative;
        z-index: 20;
        text-align: center;
        font-size: 20px;
        color: white;
        padding: 7px 0;
        font-family: 'Montserrat', sans-serif;
      }
    }
  }

  &.overflow-hidden {
    overflow: hidden;
  }

  a {
    text-decoration: none;
  }

  strong {
    font-weight: bolder;
  }

  em {
    font-style: italic;
  }
}

@import "header";

#slider_container {
  position: relative;

  .slider_icos_wrapper {
    a.icon {
      margin-top: 15px;

      span {
        display: inline-flex;
        vertical-align: middle;
      }
    }

    .icon:lang(de) {
      margin: 0;
    }
  }

  .slider_center_title {
    text-align: center;
    color: white;
    text-shadow: 0 0 30px rgba(0, 0, 0, 0.4);
    text-transform: uppercase;
    z-index: 25;
    font-size: 30px;
    margin-top: -50px;
    @include center_xy;

    &:before, &:after {
      content: '';
      display: block;
      margin: 20px auto;
      width: 0;
      height: 100px;
      border-left: 1px solid white;
    }

    &:before {

    }

    &:after {

    }
  }

  .active-revslide {
    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 20;
      background-color: rgba(0, 0, 0, 0.2);
    }
  }

  .inner_slider {
    height: 336px;
    position: relative;
    overflow: hidden;

    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 20;
      background-color: rgba(0, 0, 0, 0.2);
    }
  }

  .tp-bullets {
    display: none;
  }

  .slider_icos_wrapper {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 25;
    display: inline-block;
    width: 100%;
    padding: 9px 0;
    background-color: white;
    text-align: center;

    .more_info {
      width: 150px;
      min-width: 55px;
      position: absolute;
      background: #043e50;
      top: 0;
      text-align: center;
      height: 100%;
      -webkit-animation: blink 3s infinite;
      -o-animation: blink 3s infinite;
      animation: blink 3s infinite;

      a.included_block_title {
        color: white;
        font-size: 12px;
        left: 0;
        width: 100%;
        padding: 0 10px;
      }
    }

    .slider_icos {
      width: 900px;
      z-index: 1;
      margin: auto;
      //margin-right: 40px;
    }

    .icon {
      display: inline-block;
      vertical-align: middle;
      color: $corporate_3;
      font-size: 14px;
      margin: 10px 10px;

      .fa {
        font-size: 30px;
        margin-right: 5px;
        color: $corporate_1;
        vertical-align: middle;
      }
    }

    .owl-nav {
      .owl-prev, .owl-next {
        font-size: 25px;
        color: $corporate_3;
        @include center_y;
      }

      .owl-prev {
        left: -30px;
      }

      .owl-next {
        right: -30px;
      }
    }
  }
}

.content_access {
  background: $corporate_2;
  padding: 40px 0;
  text-align: center;

  h3.section-title {
    font-size: $title_size;
    font-family: $title_family;
    font-weight: 100;
    color: $corporate_3;
    text-transform: uppercase;
    padding-bottom: 20px;
    border-bottom: 1px solid white;

    span {
      span {
        display: block;
        color: white;
        font-size: 60%;
      }
    }
  }

  & .container12 > div {
    font-size: $description_size;
    line-height: $line_height;
    width: 910px;
    margin: 20px auto 0;
    color: $corporate_3;
    font-weight: 200;
  }

  #my-bookings-form {
    display: none;
  }
}

#my-bookings-form {
  margin: 40px auto;

  #reservation {
    margin-top: 0 !important;

    .modify_reservation_widget {
      margin-top: 40px;
      margin-bottom: 0;
    }

    .my-bookings-booking-info {
      margin: 40px auto 0;

      .fResumenReserva {
        margin: auto;
      }
    }
  }

  #my-bookings-form-fields {
    label {
      display: block;
      text-align: center;
      text-transform: uppercase;
      font-family: $title_family;
      font-weight: 100;
    }

    input {
      display: block;
      width: 300px;
      margin: 10px auto;
      height: 40px;
      text-align: center;
      font-size: 14px;
      border: 1px solid $corporate_1;
    }

    ul {
      text-align: center;
      margin-top: 30px;

      li {
        display: inline-block;
        width: 200px;
        vertical-align: middle;

        button {
          height: 40px;
          text-transform: uppercase;
          font-family: $title_family;
          font-size: 16px;
          color: white;
          border: 0;
          cursor: pointer;
          width: 100%;
          font-weight: 100;
          @include transition(background, .4s);

          &.cancelButton {
            background: $corporate_3;
            height: 40px;
            text-transform: uppercase;
            font-family: $title_family;
            font-size: 16px;
            color: white;
            border: 0;
            cursor: pointer;
            width: 200px;
            font-weight: 100;
            @include transition(background, .4s);

            &:hover {
              background: darken($corporate_3, 10%);
            }
          }

          &.modify-reservation {
            background: darken($corporate_2, 10%);

            &:hover {
              background: darken($corporate_2, 20%);
            }
          }

          &.searchForReservation {
            background: $corporate_3;

            &:hover {
              background: darken($corporate_3, 10%);
            }
          }
        }
      }
    }
  }

  #cancelButton {
    display: none;
    background: $corporate_3;
    height: 40px;
    text-transform: uppercase;
    font-family: $title_family;
    font-size: 16px;
    color: white;
    border: 0;
    cursor: pointer;
    width: 200px;
    font-weight: 100;
    margin: 40px auto 0;
    @include transition(background, .4s);

    &:hover {
      background: darken($corporate_3, 10%);
    }
  }
}

.my-bookings-booking-info {
  margin: auto;
}

.content_subtitle_wrapper {
  background: $corporate_2;
  padding: 40px 0;
  text-align: center;
  color: $corporate_3;

  .offers_sections_wrapper {
    margin-bottom: 80px;
    text-align: center;

    .offer_section {
      display: inline-block;
      padding: 10px 30px;
      background: rgba(white, .3);
      cursor: pointer;
      color: white;
      font-family: $title_family;
      -webkit-transition: background .4s, color .4s;
      -moz-transition: background .4s, color .4s;
      -ms-transition: background .4s, color .4s;
      -o-transition: background .4s, color .4s;
      transition: background .4s, color .4s;
      text-transform: uppercase;

      &.active, &:hover {
        background: white;
        color: $corporate_1;
      }
    }
  }

  .content_subtitle_title {
    font-size: $title_size;
    font-family: $title_family;
    font-weight: 300;
    text-transform: uppercase;
    padding-bottom: 20px;
    //border-bottom: 1px solid white;
    span {
      span {
        display: block;
        color: white;
        font-size: 60%;
      }
    }

    & + div {
      &:before {
        content: "";
        height: 1px;
        background: white;
        width: 1140px;
        display: block;
      }
    }
  }

  .content_subtitle_description {
    font-size: $description_size;
    line-height: $line_height;
    width: 910px;
    margin: 20px auto 0;
    font-weight: 200;
    @include transition(all, .3s);
  }

  .read_more {
    position: relative;
    display: inline-block;
    margin: 20px auto;
    color: #4B4B4B;
    padding: 10px 20px;
    border: 1px solid #4B4B4B;

    span {
      position: relative;
      z-index: 2;

      &:first-of-type {
        margin-right: 20px;
      }
    }

    &:before, &:after {
      content: '';
      display: block;
      position: absolute;
      margin: auto;
      top: -1px;
      bottom: -1px;
      left: -1px;
      right: -1px;
      background-color: $corporate_2;
      @include transition(all, .3s);
    }

    &:before {
      width: calc(100% - 10px);
      height: calc(100% + 2px);
      top: -1px;
      bottom: -1px;
      left: 5px;
      right: 5px;
    }

    &:after {
      width: calc(100% + 2px);
      height: calc(100% - 10px);
      top: 5px;
      bottom: 5px;
      left: -1px;
      right: -1px;
    }

    &:hover, &.active {
      &:before {
        width: 0;
      }

      &:after {
        height: 0;
      }
    }
  }

  .button-promotion {
    display: inline-block;
    background: $corporate_1;
    color: white;
    margin-top: 40px;
    padding: 25px 80px;
    font-family: $title_family;
    text-transform: uppercase;
    -webkit-transition: background .4s, color .4s;
    -moz-transition: background .4s, color .4s;
    -ms-transition: background .4s, color .4s;
    -o-transition: background .4s, color .4s;
    transition: background .4s, color .4s;

    &:hover {
      background: darken($corporate_3, 10%);
      color: white;
    }
  }

  .services_slider_wrapper {
    margin-top: 80px;
    border-top: 1px solid white;
    border-bottom: 1px solid white;

    .services_carousel {
      padding: 20px 115px;

      .owl-item {
        height: 60px;
        text-align: center;
        background: #CDBEA2;

        &:nth-child(even) {
          background: #877960;
        }

        .services_element {
          width: 100%;
          left: 0;
        }

        .fa {
          font-size: 18px;
        }

        .fa, img {
          margin-right: 5px;
        }

        img {
          width: auto;
        }
      }

      .owl-nav {
        & > div {
          @include center_y;
          font-size: 32px;
        }

        .owl-prev {
          left: 0;
        }

        .owl-next {
          right: 0;
        }
      }
    }
  }
}

.bannersx2_text_wrapper {
  clear: both;

  .banner_element {
    position: relative;
    display: inline-block;
    width: calc(50% - 1px);
    height: 550px;
    color: #4b4b4b;
    text-align: center;

    &:nth-child(odd) {
      border-right: 2px solid #EFEFEF;
    }

    .banner_title {
      font-size: 30px;
      line-height: 90%;
      text-transform: uppercase;
      width: 230px;

      &:lang(de) {
        width: 90%;
      }

      &:after {
        content: '';
        display: block;
        margin: 20px auto;
        width: 0;
        height: 80px;
        border-left: 1px solid #4b4b4b;
      }
    }

    .banner_description {
      font-size: 14px;
      line-height: 25px;

      &:after {
        content: '';
        display: block;
        margin: 20px auto;
        width: 0;
        height: 40px;
        border-left: 1px solid #4b4b4b;
      }
    }

    .banner_link {
      position: relative;
      color: $corporate_1;
      font-size: 30px;
      text-transform: uppercase;

      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        background: rgba($corporate_1, 0.3);
        width: 0;
        z-index: 1;
        @include transition(width, .4s);
      }
    }

    &:hover {
      .banner_link {
        &:before {
          width: 100%;
        }
      }
    }
  }
}

.bannersx4_wrapper {
  width: 100%;

  .banner_element {
    width: 50%;
    height: 480px;
    float: left;
    display: inline-block;
    position: relative;
    overflow: hidden;

    &:before {
      content: "";
      background: rgba(black, 0);
      z-index: 2;
      @include transition(background, .4s);
      @include full_size;
    }

    .banner_image {
      position: relative;
      width: calc(100% - 300px);
      overflow: hidden;
      height: 480px;

      img {
        z-index: 1;
      }

      .banner_title {
        @include center_x;
        padding: 20px 10px;
        text-align: center;
        text-transform: uppercase;
        font-size: 20px;
        bottom: 20px;
        color: #4B4B4B;
        width: 80%;
        z-index: 2;
        background-color: white;
      }
    }

    .banner_content {
      position: absolute;
      width: 300px;
      top: 0;
      bottom: 0;
      right: 0;
      background-color: $corporate_1;
      color: $corporate_1;
      z-index: 4;
      text-align: center;

      .banner_description {
        text-transform: uppercase;
        font-size: 25px;
        color: white;
        padding: 50px;
        width: 90%;
        margin: 0 auto;

        &:lang(de) {
          width: 90%;
        }

        &:after {
          content: '';
          display: block;
          margin: 20px auto;
          width: 0;
          height: 100px;
          border-left: 1px solid white;
        }
      }

      .banner_link {
        position: absolute;
        bottom: 20px;
        left: 0;
        right: 0;
        width: 100%;
        height: 60px;
        margin-top: 40px;
        display: inline-block;
        font-family: $title_family;

        div {
          width: 100%;
          font-size: 50px;
          color: white;
          display: inline-block;

          &:before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            width: 0;
            z-index: 1;
            @include transition(width, .4s);
          }

          span {
            position: relative;
            width: 100%;
            left: 0;
            z-index: 2;
            text-transform: uppercase;
          }
        }
      }
    }

    &:nth-child(2), &:nth-child(3) {
      .banner_content {
        background-color: darken($corporate_1, 25%);
      }
    }

    &:nth-child(3), &:nth-child(4) {
      .banner_image {
        float: right;
      }

      .banner_content {
        right: auto;
        left: 0;
      }
    }

    &:hover {
      &:before {
        background: rgba(black, .6);
      }

      .banner_link {
        div {
          &:before {
            width: 100%;
          }
        }
      }
    }
  }

  &.cycle_banners {
    .banner_element {
      .banner_content {
        .banner_description {
          text-transform: none;
          font-size: 14px;
          font-weight: 200;
          padding: 50px 30px;

          &:after {
            display: none;
            position: absolute;
            left: 0;
            right: 0;
            bottom: 10px;
          }
        }
      }
    }
  }
}

.minigallery_content_wrapper {
  display: inline-block;
  width: 100%;
  float: left;

  .minigalery_content {
    text-align: center;
    margin-bottom: 50px;

    .minigallery_title {
      font-size: $title_size;
      text-transform: uppercase;
      color: $corporate_1;
      font-family: $title_family;
    }

    .minigallery_description {
      margin-top: 40px;
      font-size: $description_size;
      line-height: $line_height;
    }
  }

  .minigallery_wrapper {
    .owl-item {
      background-color: $corporate_1;
      height: 300px;
      overflow: hidden;

      img {
        width: auto;
        opacity: 1;
        @include transition(all, .6s);
      }

      span {
        display: block;
        width: 90%;
        color: white;
        font-size: 20px;
        text-align: center;
        text-shadow: 0 0 5px rgba(0, 0, 0, .6);
        @include center_xy;

        i.fa {
          display: block;
          text-align: center;
          font-size: 25px;
        }
      }

      &:hover {
        img {
          opacity: .4;
        }

        .minigallery_desc {
          img {
            opacity: .8;
          }
        }
      }

      .minigallery_desc {
        img {
          opacity: .4;
        }
      }
    }

    .owl-nav {
      & > div {
        @include center_y;
        color: white;
        cursor: pointer;
        font-size: 32px;
      }

      .owl-prev {
        left: 15px;
      }

      .owl-next {
        right: 15px;
      }
    }
  }
}

.maps_wrapper {
  position: relative;
  height: 500px;
  display: inline-block;
  float: left;
  width: 100%;

  .maps_content {
    height: 400px;
    width: 250px;
    background: white;
    z-index: 1;

    .center_block {
      width: 100%;
      color: $corporate_3;
      padding: 0 20px;
      font-size: $description_size;
      text-align: center;
      font-weight: 100;

      .title_maps {
        font-size: $title_size;
        font-family: $title_family;
        line-height: 100%;
        font-weight: 100;
        display: inline-block;
        width: 100%;
        text-transform: uppercase;

        &:after {
          content: '';
          display: block;
          margin: 20px auto;
          width: 0;
          height: 100px;
          border-left: 1px solid $corporate_3;
        }
      }

      a {
        color: $corporate_3;
      }
    }
  }

  .maps_iframe {
    position: relative;
    height: 100%;
    background-color: $corporate_3;
    overflow: hidden;

    iframe {
      width: 130%;
      opacity: .8;
      margin-top: -150px;
      height: 650px;
    }
  }
}

.newsletter_and_icons_footer_wrapper {
  background: #F6F3EE;
  padding: 45px 0;
  display: inline-block;
  width: 100%;
  position: relative;
  z-index: 10;

  & > div {
    width: 100%;
    display: inline-block;
    padding: 30px 50px;
  }

  .ticks_wrapper {
    display: inline-block;
    width: 100%;
    margin-top: 10px;
    text-align: center;
    padding: 0;

    &:before {
      content: "";
      width: 1140px;
      margin: 0px auto 30px;
      height: 1px;
      background: grey;
      display: block;
    }

    .tick_element {
      display: inline-block;
      vertical-align: middle;
      color: lighten($corporate_3, 20%);
      font-size: 14px;
      margin: 0 10px;

      .fa {
        font-size: 30px;
        margin-right: 5px;
        vertical-align: middle;
      }
    }
  }

  .newsletter_wrapper {
    .newsletter_container {
      width: auto;
      text-align: center;

      .newsletter_title {
        text-transform: uppercase;
        color: $corporate_3;
        font-size: $title_size;
        font-family: $title_family;
        font-weight: 100;
      }

      .newsletter_description {
        font-size: $description_size;
        line-height: $line_height;
        margin-top: 10px;
      }

      .newsletter_form {
        margin-top: 20px;

        input {
          background: white;
          border-width: 0;
          text-align: center;
          width: 260px;
          height: 60px;
          margin-right: 10px;
          font-size: 16px;
          font-weight: 300;
          display: inline-block;
          vertical-align: top;

          &::-webkit-input-placeholder {
            color: $corporate_3;
          }

          &::-moz-placeholder {
            color: $corporate_3;
          }

          &:-ms-input-placeholder {
            color: $corporate_3;
          }

          &:-moz-placeholder {
            color: $corporate_3;
          }
        }

        .button_newsletter {
          display: inline-block;
          background: $corporate_1;
          color: white;
          font-size: 16px;
          text-transform: uppercase;
          vertical-align: top;
          width: 260px;
          padding: 20px 0;
          margin-right: 30px;
          text-align: center;
          position: relative;
          cursor: pointer;
          font-family: $title_family;

          &:hover:before {
            width: 100%;
          }

          &:before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 0;
            background: darken($corporate_3, 10%);
            z-index: 1;
            @include transition(width, .4s)
          }

          span {
            position: relative;
            z-index: 2;
          }
        }
      }

      .newsletter_checkbox {
        margin-top: 5px;

        a, label {
          color: $corporate_1;
          cursor: pointer;
        }

        a {
          text-decoration: underline;
        }
      }

      input#privacy, input#promotions {
        width: 1em;
        height: 1em;
        vertical-align: baseline;
      }

      .social_newsletter {
        display: inline-block;
        vertical-align: top;
        margin-top: 10px;

        a {
          display: inline-block;
          vertical-align: middle;
          color: $corporate_1;
          font-size: 32px;
          margin: 0 5px;

          &:hover {
            color: $corporate_3;
          }

          i {
            font-family: "fontawesome", sans-serif;
          }
        }
      }
    }
  }

  .icons_wrapper {
    @include center_y;
    right: 0;

    .icons_carousel {
      padding: 0 30px;

      .owl-item {
        text-align: center;
      }

      img {
        width: auto;
        margin: auto;
      }

      .owl-nav {
        & > div {
          @include center_y;
          color: #877960;
          cursor: pointer;
          font-size: 32px;
        }

        .owl-prev {
          left: 0px;
        }

        .owl-next {
          right: 0px;
        }
      }
    }
  }
}

@import "inner_section/rooms";
@import "inner_section/offers";
@import "inner_section/form_contact";
@import "footer";

.gallery_filter_wrapper {
  display: inline-block;
  width: 100%;
  float: left;
}

.gallery_filter_wrapper .gallery_photos {
  width: 100%;
}

.gallery_filter {
  width: 100%;
  display: inline-block;
  float: left;
  @include transition(height, 0.6s);
}

.filters_gallery_wrapper {
  display: inline-block;
  width: 100%;
  float: left;
  padding: 0 0 60px;
  text-align: center;
  background: $corporate_2;

  .filter_element {
    display: inline-block;
    padding: 10px 30px;
    background: rgba(white, .3);
    cursor: pointer;
    margin-right: 4px;
    color: $corporate_1;
    @include transition(all, .4s);
    font-family: $title_family;

    &.active, &:hover {
      background: white;
      color: $corporate_1;
    }

    &.active {
      border-bottom: 2px solid $corporate_1;
    }
  }
}

.full_screen_menu {
  position: fixed;
  bottom: 0;
  left: 0;
  right: auto;
  background: rgba(lightgrey, 0.9);
  top: 86px;
  z-index: 24;
  width: 0;
  overflow-x: hidden;
  overflow-y: auto;
  @include transition(width, 1s);

  &.active {
    width: 40%;

    #mainMenuDiv {
      opacity: 1;
    }
  }

  div#mainMenuDiv ul#main-sections-inner {
    display: block;
    justify-content: none;
  }

  .separator_element {
    display: none;
  }

  div#logoDiv {
    display: table;
    margin: auto;
    margin-bottom: 40px;

    img {
      max-height: 100px;
    }
  }

  #mainMenuDiv {
    position: absolute;
    right: auto;
    left: auto;
    top: 0;
    bottom: 0;
    transform: none;
    display: table;
    width: 400px;
    margin: auto;
    max-width: 100%;
    opacity: 0;
    margin-top: 30px;
    @include center_xy;
    @include transition(opacity, 1s);

    .main-section-div-wrapper {
      text-align: center;
      text-transform: uppercase;
      font-weight: lighter;
      width: auto !important;
      margin: 0 auto 10px;

      .menu_icon {
        display: none;
      }

      a {
        display: block;
        padding: 0 0 5px;
        text-decoration: none;
        color: $corporate_3;
        cursor: pointer;
        font-size: 18px;
        font-family: $title_family;
        @include transition(all, .6s);

        &:before {
          content: '';
          display: inline-block;
          vertical-align: middle;
          background-color: $corporate_3;
          height: 1px;
          width: 0;
          @include transition(width, .6s);
        }

        &:hover {
          color: $corporate_1;

          &:before {
            width: 100px;
            margin-right: 10px;
          }
        }
      }
    }
  }
}

#little-block {
  div {
    margin-top: 50px;

    span {
      font-family: Akrobat;
      display: inline-block;
      padding: 10px 15px;
      background-color: white;
      color: #7AA652;
      font-size: 20px;
      line-height: 30px;
      text-transform: uppercase;
    }
  }
}

#location-subtitles {
  color: white;
  font-size: 19px;
}

div#data {
  .ninos.numero_personas {
    .selectricItems {
      left: 7% !important;
      top: 90% !important;
    }
  }
}

.gallery_filter_wrapper {
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;

  .gallery_photos {
    .owl-item a {
      width: 100%;
    }

    .owl-nav {
      @include center_y;
      left: 0;
      width: 100vw;

      &.disabled {
        display: block;
      }

      & > div {
        @include center_y;
        color: white;
        cursor: pointer;
        font-size: 32px;
        z-index: 10;
      }

      .owl-prev {
        left: 15px;
      }

      .owl-next {
        right: 25px;
      }
    }
  }
}

html[lang=fr] {
  #full_wrapper_booking .wrapper_booking_button .submit_button {
    font-size: 16pt;
    padding: 17px 10px;
  }

  div#full_wrapper_booking.inner_engine .wrapper_booking_button .submit_button, div#full_wrapper_booking.floating_booking.showed .wrapper_booking_button .submit_button {
    padding: 13.5px 10px;
  }

  #slider_container .slider_icos_wrapper .icon {
    font-size: 12px;
  }
}

/*===== Popup Inicial ======*/
#popup_inicial {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.popup_img {
  img {
    @include center_image;
  }
}

.popup_text {
  position: absolute;
  width: 90%;
  box-sizing: border-box;
  text-align: center;
  margin: auto;
  left: 0;
  right: 0;
  //height: 166px;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

.popup_title {
  font-size: 30px;
  padding: 10px;
  color: white !important;
}

.popup_description {
  font-size: 16px;
  line-height: 27px;
  color: white !important;
  width: 79%;
  box-sizing: border-box;
  margin: auto;

}

.popup_form {
  text-align: center;
}

.popup_inicial form {
  padding: 20px;
  padding-bottom: 25px;
  font-size: 18px;
  display: inline-block;
}

.popup_inicial form input {
  width: 250px;
  height: 30px;
  display: inline-block;
  vertical-align: middle;
}

.popup_inicial button {
  background: #63BE7B;
  width: 150px;
  height: 34px;
  font-size: 18px;
  border: 1px solid #CECCCF;
  color: #FFFFFF;
  margin-left: -6px;
  display: inline-block !important;
  vertical-align: middle;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left-color: white;
  text-transform: uppercase;

  &:hover {
    background: lighten(#63BE7B, 10%);
  }
}

.popup_message {
  color: black;
  margin-top: 20px;
}

.covid_container {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;

  .close_covid_container {
    position: absolute;
    right: 0;
    bottom: 100%;
    width: 30px;
    height: 30px;
    background: rgba($corporate_1, 0.8);
    cursor: pointer;

    i {
      color: white;
      @include center_xy;
    }
  }

  a {
    img {
      min-width: 150px;
      max-width: 250px;
    }
  }
}

.mobile_b_beach {
  padding: 10px;
}

// Gift voucher
.wrapper_gift_bono {
  width: auto !important;
  text-align: left;

  .gift_bono_content {
    .left_content_wrapper {
      text-align: left;

      .default_text {
        text-align: left;
      }
    }

    .right_content_wrapper {
      text-align: left;

      .gift_wrapper {
        .title_wrapper {
          .logo_bono {
            margin-bottom: 20px;
          }
        }

        .input_wrapper {
          margin-top: -10px;
          margin-right: 15px;
          vertical-align: middle;

          .input_price {
            &::placeholder {
              top: -15px;
            }
          }

        }

        .input_wrapper.focus:not(.other) {
          .custom_price {
            top: 15px;
            right: 40px;
          }
        }

        .input_wrapper.focus.other {
          &::before {
            left: 40%;
          }

          .message {
            left: 40%;
          }
        }

        .button_bono {
          vertical-align: middle;

          button {
            outline: none;
          }
        }
      }

      .conditions_wrapper {
        text-align: left;
      }
    }
  }
}