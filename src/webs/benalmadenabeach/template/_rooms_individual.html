<div class="rooms_wrapper">
    {% if not hide_filter %}
        <div class="filter_rooms_wrapper">
            <div class="container12">
                {% for x in rooms_individual %}
                        <div class="filter_element {% if x.sectionName == sectionToUse.sectionName %}active{% endif %}"
                             {% if x.sectionName == "Apartamento a pie de playa" %} id="filter_last" {% endif %}
                             data-filter="{{ x.slug_name }}">{{ x.title|safe }}</div>
                {% endfor %}
            </div>
        </div>
    {% endif %}

    <div class="rooms_list_wrapper">
        {% for x in rooms_individual %}
            <div class="room_element" data-filter="{{ x.slug_name }}"
                 style="{% if x.sectionName != sectionToUse.sectionName %}display: none;{% endif %}{% if hide_filter %}margin-top: 0{% endif %}" {% if x.sectionName == "Apartamento a pie de playa" %} id="room_element_last" {% endif %}>
                <div class="container12">
                    <div class="room_content">
                        <div class="title_room">{{ x.subtitle|safe }}</div>
                        <div class="content_room">{{ x.content|safe }}</div>
                    </div>
                </div>

                {% if x.gallery %}
                    <div class="room_gallery">
                        <div class="room_carousel owl-carousel">
                            {% for i in x.gallery %}
                                <a class="gallery_element" href="{{ i.servingUrl|safe }}=s1900" rel="lightbox[room_{{ forloop.parent.counter }}]">
                                    <img data-src="{{ i.servingUrl|safe }}" class="center_image"/>
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}

                {% if x.services %}
                    <div class="services_wrapper">
                        {% if x.services_title %}
                            <div class="services_title">{{ x.services_title|safe }}</div>
                        {% endif %}
                        <div class="container12">
                            <div class="services_container">
                                {% for i in x.services %}
                                    <div class="service_element">
                                        {% if i.title %}
                                            <i class="fa {{ i.title|safe }}"></i>
                                        {% endif %}
                                        <span>{{ i.description|safe }}</span>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                {% endif %}

            </div>
        {% endfor %}
    </div>
</div>

<script>
    $(window).load(function () {
        $(".filter_rooms_wrapper .container12").append($("#filter_last"));
        $(".rooms_list_wrapper").append($("#room_element_last"));

        $(".filter_rooms_wrapper .filter_element").click(function () {
            var filter_active = $(this);
            $(".filter_rooms_wrapper .filter_element.active").removeClass("active");
            $(".room_element").slideUp().promise().done(function () {
                $(".room_element[data-filter=" + filter_active.attr("data-filter") + "]").slideDown().promise().done(setSizeImage);
            });
            filter_active.addClass("active");
        });

        var owl_params = {
            loop: true,
            nav: true,
            dots: false,
            items: 5,
            margin: 0,
            navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
            autoplay: true
        };
        $(".room_carousel").owlCarousel(owl_params);
        setSizeImage();

        if(!$(".filter_element.active").length) {
            $(".filter_element:first-of-type").trigger("click");
            setTimeout(setSizeImage,1000);
        }
    });

    $(window).resize(setSizeImage);

    function setSizeImage() {
        var size_picture = $(".room_carousel:visible .owl-item").width();
        $(".room_carousel .owl-item").height(size_picture);
        $(".gallery_element").height(size_picture);
        $(".gallery_element img").each(function () {
            $(this).attr("src", $(this).attr("data-src") + "=s" + Math.round(size_picture) + "-c");
        })
    }
</script>