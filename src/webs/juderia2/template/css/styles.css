/* line 1, ../../../../sass/booking/_booking_engine.scss */
.booking_widget {
  position: absolute;
  z-index: 400;
  top: 185px;
}

/* line 7, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title {
  background: #04284E;
  text-align: center;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 20px;
  padding-top: 5px;
  padding-bottom: 5px;
}
/* line 15, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title h4 {
  margin-top: 0;
  color: white;
}
/* line 20, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title .booking_title_1,
.booking_form_title .booking_title_2 {
  display: none;
}

/* line 26, ../../../../sass/booking/_booking_engine.scss */
.booking_form {
  font-family: 'Source Sans Pro', sans-serif;
  padding: 20px;
  width: 260px;
  background: white;
}

/* line 33, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper {
  position: relative;
}
/* line 35, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper label {
  color: gray;
  font-size: 12px;
}
/* line 40, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper input {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  height: 40px;
  border: 0;
  width: 250px;
  padding-left: 10px;
}
/* line 48, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper .right_arrow {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  position: absolute;
  background: #04284E url(/static_1/images/booking/flecha_motor_der.png) no-repeat center center;
  right: 2px;
  top: 30px;
  height: 35px;
  width: 35px;
}

/* line 62, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper {
  margin-right: 10px;
}
/* line 67, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper,
.stay_selection .rooms_number_wrapper {
  float: left;
  width: 80px;
}
/* line 74, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper label,
.stay_selection .departure_date_wrapper label,
.stay_selection .rooms_number_wrapper label {
  color: gray;
  font-size: 12px;
}

/* line 83, ../../../../sass/booking/_booking_engine.scss */
.date_box {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  background: white;
  height: 40px;
}
/* line 90, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_day,
.date_box .date_year {
  display: block;
  text-align: center;
}
/* line 96, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_day {
  text-transform: uppercase;
  color: #04284E;
  margin-left: 5px;
  margin-right: 5px;
  font-size: 16px;
  font-weight: bolder;
}
/* line 105, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_year {
  color: white;
  font-size: 12px;
  height: 14px;
  line-height: 14px;
}

/* line 113, ../../../../sass/booking/_booking_engine.scss */
.room {
  clear: both;
  margin-bottom: 5px;
}
/* line 116, ../../../../sass/booking/_booking_engine.scss */
.room .room_title {
  margin-top: 25px;
}
/* line 120, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector {
  margin-right: 10px;
}
/* line 125, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector,
.room .children_selector,
.room .babies_selector {
  float: left;
  width: 80px;
}
/* line 131, ../../../../sass/booking/_booking_engine.scss */
.room .room_title label,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  display: block;
}
/* line 136, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  color: gray;
  font-size: 12px;
}

/* line 145, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button {
  font-family: 'Source Sans Pro', sans-serif;
  position: relative;
  margin-top: 50px;
  text-align: left;
}
/* line 152, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button .promocode_input {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  border: 0;
  padding: 0;
  margin-top: 10px;
  text-align: center;
  color: #04284E;
  width: 170px;
  height: 40px;
  font-size: 16px;
}
/* line 164, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button button {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  color: white;
  border: 0;
  float: right;
  background: #04284E;
  font-size: 16px;
  text-transform: uppercase;
  height: 40px;
  width: 80px;
  margin-top: 10px;
}
/* line 177, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button .spinner_wrapper {
  position: absolute;
  right: 30px;
  bottom: 20px;
}

/* line 184, ../../../../sass/booking/_booking_engine.scss */
.horizontal_engine {
  height: 379px;
  background: white !important;
}

/* line 189, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal {
  width: 600px;
  margin: 0 auto;
  padding-top: 172px;
}
/* line 194, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 199, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form {
  border-left: 1px solid white;
  border-right: 1px solid white;
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
}
/* line 207, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form_title {
  display: none;
}
/* line 211, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form {
  width: 560px;
}
/* line 215, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .destination_wrapper {
  float: left;
}
/* line 219, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .stay_selection {
  float: right;
}
/* line 223, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .room {
  float: right;
}
/* line 227, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .wrapper_booking_button {
  clear: both;
}
/* line 231, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .promocode_input {
  width: 260px !important;
}
/* line 235, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .wrapper_booking_button button {
  width: 170px !important;
}

/* line 241, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline {
  width: 1140px;
  height: 75px;
  margin: 0 auto;
  padding-top: 20px;
  background-color: gray;
}
/* line 248, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 253, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form {
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
  background-color: gray;
}
/* line 260, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form_title {
  display: none;
}
/* line 264, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form {
  width: 1140px;
  padding: 0;
}
/* line 269, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .destination_wrapper {
  float: left;
}
/* line 273, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .stay_selection {
  float: left;
  margin-left: 90px;
}
/* line 277, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .stay_selection .entry_date_wrapper label,
.boking_widget_inline .stay_selection .departure_date_wrapper label,
.boking_widget_inline .stay_selection .rooms_number_wrapper label {
  color: #787878;
  font-size: 12px;
}
/* line 285, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room_list_wrapper {
  float: left;
  margin-left: 20px;
  margin-right: 20px;
  margin-top: 5px;
}
/* line 291, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room {
  float: right;
}
/* line 294, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room .room_title,
.boking_widget_inline .room .adults_selector label,
.boking_widget_inline .room .children_selector label,
.boking_widget_inline .room .babies_selector label {
  color: gray;
  font-size: 12px;
}
/* line 301, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room .room_title {
  text-align: right;
}
/* line 307, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button {
  display: inline;
  margin-top: 10px;
}
/* line 311, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  float: none;
}
/* line 316, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .promocode_input {
  width: 200px !important;
  margin-right: 20px;
  margin-top: 19px;
  background-color: #5a5a5a;
}
/* line 323, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  width: 170px !important;
}
/* line 327, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .selectric, .boking_widget_inline .date_box {
  background: #5a5a5a;
}

/* line 333, ../../../../sass/booking/_booking_engine.scss */
.booking_footer_message {
  margin-top: 65px;
}

/* line 338, ../../../../sass/booking/_booking_engine.scss */
.booking-form-center-text {
  text-align: center;
}

/* line 342, ../../../../sass/booking/_booking_engine.scss */
.babies_selector {
  margin-left: 10px;
}

/* line 346, ../../../../sass/booking/_booking_engine.scss */
.room_title.room_title_with_babies {
  float: none;
  margin-bottom: 5px;
  padding-top: 10px;
  color: #04284E;
  text-align: center;
  width: 100%;
  text-transform: uppercase;
}

/* line 359, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text {
  margin-top: 7px;
}
/* line 362, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .stay_selection {
  margin-left: 45px !important;
}
/* line 366, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .room_title.room_title_with_babies {
  margin-top: -30px;
  text-align: center;
}
/* line 371, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .room_list_wrapper {
  margin-left: 60px;
}

/* line 379, ../../../../sass/booking/_booking_engine.scss */
.range-age {
  width: 55px;
  display: inline-block;
  float: right;
  white-space: nowrap;
}

/*=====================================================================
  Selectric
======================================================================*/
/* line 5, ../../../../sass/booking/_selectric.scss */
.selectricWrapper {
  position: relative;
  margin: 0 0 0px;
  width: 80px;
  cursor: pointer;
}

/* line 12, ../../../../sass/booking/_selectric.scss */
.selectricDisabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 23, ../../../../sass/booking/_selectric.scss */
.selectricOpen {
  z-index: 9999;
}

/* line 27, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 31, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

/* line 38, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect select {
  position: absolute;
  left: -100%;
  display: none;
}

/* line 44, ../../../../sass/booking/_selectric.scss */
.selectricInput {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  _font: 0/0 a;
  background: none !important;
}

/* line 60, ../../../../sass/booking/_selectric.scss */
.selectricTempShow {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* line 67, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectric {
  z-index: 9999;
}

/* line 72, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 76, ../../../../sass/booking/_selectric.scss */
.selectric {
  background: white;
  position: relative;
  border-radius: 6px;
  height: 40px;
}

/* line 83, ../../../../sass/booking/_selectric.scss */
.selectric .label {
  display: block;
  overflow: hidden;
  font-size: 16px;
  line-height: 40px;
  color: #04284E;
  text-align: left;
  margin-left: 20px;
}

/* line 93, ../../../../sass/booking/_selectric.scss */
.selectric .button {
  zoom: 1;
  position: absolute;
  font: 0/0 a;
  overflow: hidden;
  margin: auto;
  top: 0;
  right: 2px;
  bottom: 0;
  width: 35px;
  height: 35px;
  border: 0;
  background: #04284E url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  padding: 0;
}

/* line 109, ../../../../sass/booking/_selectric.scss */
.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */
/* line 115, ../../../../sass/booking/_selectric.scss */
.selectricItems {
  display: none;
  position: absolute;
  overflow: hidden;
  top: 100%;
  left: 0;
  background: #F9F9F9;
  border: 1px solid #CCC;
  z-index: 9998;
  -webkit-box-shadow: 0 0 10px -6px;
  box-shadow: 0 0 10px -6px;
}

/* line 128, ../../../../sass/booking/_selectric.scss */
.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* line 138, ../../../../sass/booking/_selectric.scss */
.selectricItems li {
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 146, ../../../../sass/booking/_selectric.scss */
.selectricItems li:hover {
  background: #F0F0F0;
  color: #444;
}
/* line 151, ../../../../sass/booking/_selectric.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}

/* line 158, ../../../../sass/booking/_selectric.scss */
.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;
  cursor: default;
}

/* line 1, ../../../../sass/news/_news_2.scss */
.new {
  width: 100%;
  background-color: white;
  overflow: hidden;
  margin-top: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid rgba(80, 183, 1, 0.18);
}

/* line 9, ../../../../sass/news/_news_2.scss */
.new:last-child {
  border-bottom: none;
  padding-bottom: 10px;
}

/* line 13, ../../../../sass/news/_news_2.scss */
.photo-container {
  float: left;
  width: 300px;
}
/* line 17, ../../../../sass/news/_news_2.scss */
.photo-container img {
  vertical-align: top;
}

/* line 21, ../../../../sass/news/_news_2.scss */
.block-new-description {
  display: inline-block;
  width: 800px;
  height: 200px;
  padding: 0px 20px;
  position: relative;
}
/* line 28, ../../../../sass/news/_news_2.scss */
.block-new-description .date {
  color: #04284E;
  font-size: 20px;
  margin-bottom: 5px;
  text-align: left;
}
/* line 34, ../../../../sass/news/_news_2.scss */
.block-new-description a {
  color: #04284E;
}
/* line 37, ../../../../sass/news/_news_2.scss */
.block-new-description #news_description p {
  text-align: left;
}
/* line 40, ../../../../sass/news/_news_2.scss */
.block-new-description .news-title {
  text-transform: uppercase;
  font-size: 17px;
  font-weight: 700;
  margin-bottom: 5px;
  color: #4b4b4b;
  display: inline-block;
}
/* line 48, ../../../../sass/news/_news_2.scss */
.block-new-description ul {
  position: absolute;
  bottom: -4px;
}
/* line 52, ../../../../sass/news/_news_2.scss */
.block-new-description ul li {
  display: inline-block;
  vertical-align: middle;
  margin-right: 3px;
}
/* line 57, ../../../../sass/news/_news_2.scss */
.block-new-description ul .read-more-news {
  color: white;
  background: #04284E;
  font-size: 18px;
  font-weight: 100;
  padding: 5px 25px;
  margin-right: 6px;
  margin-top: -3px;
  display: inline-block;
}
/* line 67, ../../../../sass/news/_news_2.scss */
.block-new-description ul .read-more-news:hover {
  background: #58AAD9;
}

/* line 73, ../../../../sass/news/_news_2.scss */
#new1 {
  margin-top: 20px;
}

/*==== General ====*/
/* line 2, ../sass/_template_specific.scss */
body {
  font-family: 'Source Sans Pro', sans-serif;
}

/* line 6, ../sass/_template_specific.scss */
div#logoDiv {
  margin-top: 10px;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/
/* line 12, ../sass/_template_specific.scss */
.ui-state-default {
  border: 1px solid white !important;
}

/* line 16, ../sass/_template_specific.scss */
.ui-datepicker-title {
  color: white !important;
}

/* line 20, ../sass/_template_specific.scss */
.ui-widget-header {
  background: #04284E !important;
}

/* line 24, ../sass/_template_specific.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #04284E !important;
  color: white;
}

/* line 29, ../sass/_template_specific.scss */
.g-recaptcha {
  width: 280px;
}

/*==== Inners sections ====*/
/* line 35, ../sass/_template_specific.scss */
body.inner_section_custom .booking_widget {
  top: 180px;
}
/* line 39, ../sass/_template_specific.scss */
body.inner_section_custom .slider_description_wrapper {
  display: none !important;
}
/* line 43, ../sass/_template_specific.scss */
body.inner_section_custom .wrapper-new-web-support {
  border-radius: 0;
  padding-bottom: 24px;
}
/* line 49, ../sass/_template_specific.scss */
body.inner_section_custom #slider_container .tp-leftarrow, body.inner_section_custom #slider_container .tp-rightarrow {
  display: none !important;
}
/* line 53, ../sass/_template_specific.scss */
body.inner_section_custom #slider_container .tp-bullets {
  bottom: 145px !important;
}

/*====== Header =====*/
/* line 60, ../sass/_template_specific.scss */
header {
  position: absolute;
  width: 100%;
  z-index: 900;
  background: rgba(0, 0, 0, 0.6);
  top: 0;
}
/* line 67, ../sass/_template_specific.scss */
header #full_wrapper_booking {
  position: fixed;
  top: 0;
  background: rgba(0, 0, 0, 0.6) !important;
  padding-bottom: 5px;
}
/* line 73, ../sass/_template_specific.scss */
header #full_wrapper_booking #motor_reserva {
  margin-bottom: 0;
}
/* line 77, ../sass/_template_specific.scss */
header #full_wrapper_booking .wrapper-old-web-support {
  display: none !important;
}

/* line 83, ../sass/_template_specific.scss */
#lang_header {
  color: white;
  display: block;
  text-align: center;
}

/* line 89, ../sass/_template_specific.scss */
#lang, .phone_reservas, .phone_reservas_ind {
  margin: 0 0px;
  padding: 0 20px;
}

/* line 99, ../sass/_template_specific.scss */
#lang {
  margin-left: 5px;
}
/* line 102, ../sass/_template_specific.scss */
#lang a {
  color: white;
  background: #04284E;
  border-radius: 22px;
  width: 35px;
  height: 35px;
  text-align: center;
  display: inline-block;
  padding: 8px 0;
  box-sizing: border-box;
  font-size: 14px;
  font-weight: bolder;
}
/* line 116, ../sass/_template_specific.scss */
#lang .selected, #lang a:hover {
  background-color: #58AAD9;
}

/* line 121, ../sass/_template_specific.scss */
.lang-button {
  border: 0;
  background-color: #df9c65;
  padding: 6px 6px 6px 6px;
  font-size: 12px;
  text-decoration: none;
  align-self: center;
  color: white;
  border-radius: 100%;
  font-weight: 700;
}

/* line 133, ../sass/_template_specific.scss */
#lang .selected .lang-button {
  background-color: #58AAD9;
}

/* line 139, ../sass/_template_specific.scss */
#top-sections {
  padding-bottom: 16px;
}
/* line 142, ../sass/_template_specific.scss */
#top-sections a {
  text-decoration: none;
  color: white;
  font-weight: 100;
  font-size: 18px;
  margin-left: 21px;
}
/* line 149, ../sass/_template_specific.scss */
#top-sections a img {
  margin-bottom: -3px;
  margin-right: 4px;
}

/* line 156, ../sass/_template_specific.scss */
.top_section_element {
  color: white;
  font-weight: 100;
  font-size: 18px;
  margin-left: 26px;
}
/* line 162, ../sass/_template_specific.scss */
.top_section_element img {
  margin-bottom: -3px;
  margin-right: 4px;
}

/* line 168, ../sass/_template_specific.scss */
a.last_top_section {
  float: right;
}

/* line 172, ../sass/_template_specific.scss */
.bottom_header {
  display: table;
  float: right;
  margin-bottom: 10px;
}

/* line 178, ../sass/_template_specific.scss */
.whatsapp {
  display: inline-block;
  padding: 4px;
  margin-right: 15px;
  cursor: pointer;
}
/* line 184, ../sass/_template_specific.scss */
.whatsapp img {
  vertical-align: middle;
  width: 110px;
  margin-top: -2px;
}

/* line 191, ../sass/_template_specific.scss */
.whatsapp-wrapper {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  width: 270px;
  text-align: center;
  margin: 20px auto;
  background: white;
  -webkit-border-radius: 9px;
  -moz-border-radius: 9px;
  border-radius: 9px;
  position: relative;
  top: -1px;
}
/* line 205, ../sass/_template_specific.scss */
.whatsapp-wrapper .whatsapp-img {
  background: #465a64;
  padding: 10px 0px;
  -webkit-border-top-right-radius: 9px;
  -moz-border-top-right-radius: 9px;
  border-top-right-radius: 9px;
  -webkit-border-top-left-radius: 9px;
  -moz-border-top-left-radius: 9px;
  border-top-left-radius: 9px;
}
/* line 215, ../sass/_template_specific.scss */
.whatsapp-wrapper .whatsapp-img img {
  vertical-align: bottom;
}
/* line 219, ../sass/_template_specific.scss */
.whatsapp-wrapper .whatsapp-content {
  color: #04284E;
  font-size: 12px;
  text-transform: initial;
  line-height: 14px;
  padding: 10px;
}
/* line 226, ../sass/_template_specific.scss */
.whatsapp-wrapper .tel {
  font-size: 18px;
  margin-top: 10px;
  font-weight: bold;
}

/* line 233, ../sass/_template_specific.scss */
.newsletter_banner, div#lang, .wifi_banner {
  display: inline-block;
  color: white;
  font-weight: 100;
  font-size: 18px;
}
/* line 239, ../sass/_template_specific.scss */
.newsletter_banner a, div#lang a, .wifi_banner a {
  text-decoration: none;
  color: white;
}

/* line 246, ../sass/_template_specific.scss */
.newsletter_banner img {
  margin-bottom: -5px;
  margin-right: 4px;
}

/* line 252, ../sass/_template_specific.scss */
.wifi_banner {
  margin-left: 20px;
}
/* line 255, ../sass/_template_specific.scss */
.wifi_banner img {
  margin-bottom: -11px;
  margin-right: 7px;
}

/* line 261, ../sass/_template_specific.scss */
.phone_reservas {
  width: 170px;
  text-align: center;
  height: 70px;
  margin-top: -9px;
}
/* line 267, ../sass/_template_specific.scss */
.phone_reservas ul {
  margin-top: -10px;
}

/* line 272, ../sass/_template_specific.scss */
.phone_reservas * {
  top: 0;
}

/* line 276, ../sass/_template_specific.scss */
.phone_reservas_ind * {
  top: 0;
}

/* line 280, ../sass/_template_specific.scss */
span.txt_reservas {
  margin-left: 13px;
  color: white;
  font-size: 13px;
  text-transform: uppercase;
}

/* line 287, ../sass/_template_specific.scss */
.number_reservas {
  color: white;
  font-weight: 700;
  font-size: 18px;
}

/* line 295, ../sass/_template_specific.scss */
.phone_reservas_ind {
  width: 162px;
  text-align: right;
}

/* line 300, ../sass/_template_specific.scss */
.phone_reservas ind * {
  top: 0;
}

/* line 305, ../sass/_template_specific.scss */
.phone_reservas_ind span.txt_reservas {
  margin-left: 20px;
  color: white;
  font-size: 25px;
  text-transform: uppercase;
}

/* line 312, ../sass/_template_specific.scss */
.phone_reservas_ind .number_reservas {
  color: white;
  font-weight: 700;
  font-size: 20px;
}

/*========= Main Menu =========*/
/* line 319, ../sass/_template_specific.scss */
#main_menu {
  background-color: #04284E;
  width: 100%;
  z-index: 1;
}

/* line 325, ../sass/_template_specific.scss */
#mainMenuDiv {
  font-size: 15px;
  z-index: 99;
  position: relative;
  height: 45px;
  clear: both;
  background-color: #04284E;
}

/* line 334, ../sass/_template_specific.scss */
#mainMenuDiv a {
  text-decoration: none;
  text-transform: uppercase;
  color: white;
  display: inline-block;
  padding: 11px 0 !important;
  font-weight: 100;
}
/* line 342, ../sass/_template_specific.scss */
#mainMenuDiv a:hover {
  opacity: 0.8;
}
/* line 346, ../sass/_template_specific.scss */
#mainMenuDiv a.button-promotion {
  padding: 5px 20px !important;
  color: #58AAD9;
}

/* line 353, ../sass/_template_specific.scss */
#section-active a {
  text-decoration: none;
  text-transform: uppercase;
  display: inline-block;
  color: white !important;
  font-weight: bold !important;
  padding: 5px 0 5px !important;
}

/* line 362, ../sass/_template_specific.scss */
#main-sections-inner {
  text-align: justify;
  height: 30px;
}

/* line 367, ../sass/_template_specific.scss */
#main-sections-inner ul {
  display: none;
}

/* line 371, ../sass/_template_specific.scss */
#main-sections-inner div:hover > ul {
  display: block;
}

/* line 375, ../sass/_template_specific.scss */
#main-sections-inner div ul {
  position: absolute;
}

/* line 379, ../sass/_template_specific.scss */
#main-sections-inner li ul {
  position: absolute;
}

/* line 383, ../sass/_template_specific.scss */
#main-sections-inner div li {
  float: none;
  display: block;
}

/* line 388, ../sass/_template_specific.scss */
#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

/* line 395, ../sass/_template_specific.scss */
#main-sections-inner > div {
  display: inline-block;
}
/* line 397, ../sass/_template_specific.scss */
#main-sections-inner > div img {
  vertical-align: middle;
  margin-top: -5px;
}

/* line 403, ../sass/_template_specific.scss */
.main-section-div-wrapper a {
  text-transform: uppercase;
  font-size: 17px;
}

/* line 408, ../sass/_template_specific.scss */
.main-section-div-wrapper a:hover {
  color: red;
}

/* line 412, ../sass/_template_specific.scss */
.button-promotion {
  font-weight: 700;
}

/* line 416, ../sass/_template_specific.scss */
.top_header {
  display: table;
  float: right;
  border-bottom: 1px solid white;
  margin-top: 14px;
  margin-bottom: 13px;
}

/*====== Booking Widget =====*/
/* line 425, ../sass/_template_specific.scss */
.date_box .date_year {
  color: #878378;
}

/* line 429, ../sass/_template_specific.scss */
.room {
  padding-top: 7px;
}

/* line 433, ../sass/_template_specific.scss */
.room .room_title, .room .adults_selector label, .room .children_selector label, .room .babies_selector label {
  color: #696667;
  font-weight: 300;
}

/* line 438, ../sass/_template_specific.scss */
.date_box, .wrapper_booking_button .promocode_input, .selectric {
  border-radius: 0px;
  background: #F5F5F5;
}

/* line 447, ../sass/_template_specific.scss */
.promocode_text {
  text-align: center;
  cursor: pointer;
  margin-top: 26px;
  font-family: 'Cabin', sans-serif;
  font-size: 12px;
  color: #696667;
  padding-left: 10px;
  font-weight: 300;
  padding-top: 15px;
}
/* line 457, ../sass/_template_specific.scss */
.promocode_text strong {
  font-weight: inherit;
}

/* line 462, ../sass/_template_specific.scss */
.wrapper_booking_button button {
  width: 100%;
  border-radius: 0px;
  background: #58AAD9;
}

/* line 468, ../sass/_template_specific.scss */
.wrapper_booking_button .promocode_input {
  display: none;
  width: 100%;
}

/* line 473, ../sass/_template_specific.scss */
.stay_selection .entry_date_wrapper label, .stay_selection .departure_date_wrapper label, .stay_selection .rooms_number_wrapper label {
  color: #696667;
  font-weight: 300;
  font-size: 11px;
  padding-left: 5px;
}

/* line 480, ../sass/_template_specific.scss */
.booking_widget {
  bottom: auto;
  top: 200px;
  width: 300px;
  left: 11%;
}

/* line 487, ../sass/_template_specific.scss */
.booking_form {
  background: white;
  padding-top: 15px;
}

/* line 492, ../sass/_template_specific.scss */
.booking_form_title {
  background: #04284E;
  font-family: 'Cabin', sans-serif;
  text-transform: uppercase;
  font-weight: 300;
  font-size: 18px;
  padding: 22px 0;
  position: relative;
}
/* line 501, ../sass/_template_specific.scss */
.booking_form_title:before {
  position: absolute;
  left: 0px;
  margin: 0px auto;
  right: 0px;
  bottom: 0;
  content: "";
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid white;
}
/* line 515, ../sass/_template_specific.scss */
.booking_form_title .best_price {
  display: block !important;
}
/* line 518, ../sass/_template_specific.scss */
.booking_form_title .booking_title_1 {
  display: none !important;
}

/* line 523, ../sass/_template_specific.scss */
.date_box .date_day, .selectric .label {
  color: #696667;
}

/* line 527, ../sass/_template_specific.scss */
.selectric .button {
  background: white url(/img/jude2/arrow_down.png) no-repeat center center !important;
}

/* line 531, ../sass/_template_specific.scss */
.wrapper-new-web-support {
  opacity: 1;
}
/* line 534, ../sass/_template_specific.scss */
.wrapper-new-web-support .web_support_number {
  font-size: 18px !important;
}
/* line 538, ../sass/_template_specific.scss */
.wrapper-new-web-support:before {
  display: none;
}

/* line 543, ../sass/_template_specific.scss */
.booking_form_title {
  font-size: 13px;
}

/* line 547, ../sass/_template_specific.scss */
.wrapper-new-web-support.booking_form_title {
  font-size: 10px;
  padding: 12px 0px !important;
}
/* line 551, ../sass/_template_specific.scss */
.wrapper-new-web-support.booking_form_title .web_support_number {
  font-size: 13px !important;
}
/* line 554, ../sass/_template_specific.scss */
.wrapper-new-web-support.booking_form_title .web_support_label_2:before {
  font-size: 12px !important;
}

/*==== Slider Container ====*/
/* line 560, ../sass/_template_specific.scss */
section#slider_container {
  position: relative;
}
/* line 563, ../sass/_template_specific.scss */
section#slider_container .forcefullwidth_wrapper_tp_banner .tp-banner-container {
  min-height: 668px;
}
/* line 567, ../sass/_template_specific.scss */
section#slider_container .tp-bullets {
  opacity: 1 !important;
  bottom: 130px !important;
  z-index: 22;
}
/* line 573, ../sass/_template_specific.scss */
section#slider_container .tp-bullets .bullet {
  color: transparent;
  width: 15px;
  height: 15px;
  display: block;
  position: relative;
  border-radius: 22px;
  background: #DFDFDF;
  margin-right: 9px;
  cursor: pointer;
}
/* line 584, ../sass/_template_specific.scss */
section#slider_container .tp-bullets .bullet:before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  width: 15px;
  border: 1px solid #DFDFDF;
  height: 15px;
  padding: 2px;
  border-radius: 18px;
}
/* line 596, ../sass/_template_specific.scss */
section#slider_container .tp-bullets .bullet.selected {
  background: #58AAD9;
}
/* line 602, ../sass/_template_specific.scss */
section#slider_container .slider_description_wrapper {
  width: 100%;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  position: absolute;
  bottom: 0;
  z-index: 22;
}
/* line 609, ../sass/_template_specific.scss */
section#slider_container .slider_description_wrapper strong {
  font-size: 26px;
  display: block;
  font-weight: lighter;
}
/* line 615, ../sass/_template_specific.scss */
section#slider_container .slider_description_wrapper .description_slider {
  width: 56%;
  font-size: 16px;
  font-weight: lighter;
  padding: 20px 50px;
  float: left;
}
/* line 623, ../sass/_template_specific.scss */
section#slider_container .slider_description_wrapper .button-promotion {
  text-decoration: none;
  text-transform: uppercase;
  color: white;
  display: inline-block;
  font-weight: 100;
  background: #58AAD9;
  padding: 5px 20px;
  margin-top: 30px;
  float: right;
  font-size: 25px;
  margin-right: 20px;
}
/* line 636, ../sass/_template_specific.scss */
section#slider_container .slider_description_wrapper .button-promotion:hover {
  opacity: 0.8;
}
/* line 642, ../sass/_template_specific.scss */
section#slider_container .tp-leftarrow {
  opacity: 1 !important;
  bottom: 26px !important;
  top: auto !important;
  background: url(/img/jude2/arrow_slider.png) no-repeat;
  width: 62px;
  height: 62px;
}
/* line 651, ../sass/_template_specific.scss */
section#slider_container .tp-rightarrow {
  opacity: 1 !important;
  bottom: 26px !important;
  top: auto !important;
  background: url(/img/jude2/arrow_slider.png) no-repeat;
  width: 62px;
  height: 62px;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}

/*======= Tiny Carousel ======*/
/* line 668, ../sass/_template_specific.scss */
.carousel {
  clear: both;
  text-align: center;
  height: 100%;
  margin: 0 auto;
  width: 1140px;
  position: relative;
}

/* line 677, ../sass/_template_specific.scss */
.carousel #carousel_title {
  color: #58AAD9;
  padding: 5px;
  margin-bottom: 10px;
  font-size: 35px;
  margin-top: 30px;
}

/* line 685, ../sass/_template_specific.scss */
.carousel .viewport {
  width: 1140px;
  height: 222px;
  overflow: hidden;
  position: relative;
  float: left;
  margin-left: -2px;
}
/* line 693, ../sass/_template_specific.scss */
.carousel .viewport .exceded {
  height: 177px;
  overflow: hidden;
}

/* line 699, ../sass/_template_specific.scss */
.carousel .disable {
  visibility: hidden;
}

/* line 703, ../sass/_template_specific.scss */
.carousel .overview {
  list-style: none;
  position: absolute;
  left: 0;
  top: 0;
  padding: 0;
  margin: 0;
}

/* line 712, ../sass/_template_specific.scss */
.carousel .overview li {
  float: left;
  margin: 0 1px;
  height: 100%;
  text-align: center;
  font-size: 12px;
  width: 378px;
  position: relative;
}
/* line 721, ../sass/_template_specific.scss */
.carousel .overview li a {
  text-decoration: none;
}

/* line 726, ../sass/_template_specific.scss */
.carousel .overview li img {
  height: auto;
  width: 100%;
  margin-bottom: -6px;
  min-height: 177px;
}

/* line 733, ../sass/_template_specific.scss */
.carousel .overview li img:hover {
  opacity: 0.6;
  filter: alpha(opacity=60);
}

/* line 738, ../sass/_template_specific.scss */
.carousel .buttons {
  float: left;
  text-indent: -999em;
  width: 70px;
  height: 200px;
  overflow: hidden;
  position: absolute;
  margin: 0;
}

/* line 749, ../sass/_template_specific.scss */
.carousel .prev {
  background: url("/img/jude2/carousel_arrow.png") no-repeat center;
  margin-right: 3px !important;
  margin-left: 3px;
  right: 1108px;
  left: auto;
  z-index: 2;
  -ms-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* line 761, ../sass/_template_specific.scss */
.carousel .next {
  background: url("/img/jude2/carousel_arrow.png") no-repeat center;
  left: 1108px;
  z-index: 2;
}

/* line 767, ../sass/_template_specific.scss */
.carousel .disable {
  visibility: visible;
}

/* line 771, ../sass/_template_specific.scss */
.bannerTitle {
  background: #04284E;
  height: 45px;
  padding: 12px 0;
  font-size: 14px;
  color: white;
  box-sizing: border-box;
}

/* line 780, ../sass/_template_specific.scss */
.car-bold-title {
  font-weight: 700;
}

/* line 784, ../sass/_template_specific.scss */
.bannerText {
  position: absolute;
  line-height: 15px;
  top: 20px;
  width: 307px;
  height: 71px;
  color: white;
  margin-left: 16px;
  margin-top: 60px;
  background-color: rgba(0, 0, 0, 0.69);
  padding: 7px;
  font-weight: 400;
  text-align: left;
}

/* line 799, ../sass/_template_specific.scss */
div#promotionsSection {
  margin-top: 20px;
  display: table;
}

/*======= Content by Subtitle ======*/
/* line 805, ../sass/_template_specific.scss */
.content_subtitle_wrapper {
  background: #F5F5F5;
  padding: 40px 200px 20px;
  text-align: center;
  position: relative;
  margin-bottom: 35px;
  margin-top: 40px;
}
/* line 813, ../sass/_template_specific.scss */
.content_subtitle_wrapper h3.content_subtitle_title {
  text-transform: uppercase;
  color: #58AAD9;
  font-size: 23px;
  font-weight: lighter;
  margin-bottom: 8px;
}
/* line 821, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_description {
  font-weight: lighter;
  font-size: 14px;
  line-height: 32px;
  color: gray;
}

/* line 830, ../sass/_template_specific.scss */
.flex-prev img {
  -ms-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* line 837, ../sass/_template_specific.scss */
.flex-control-nav {
  margin-top: 20px;
}
/* line 840, ../sass/_template_specific.scss */
.flex-control-nav li {
  display: inline-block;
  margin: 5px;
}
/* line 844, ../sass/_template_specific.scss */
.flex-control-nav li a {
  color: transparent;
  width: 15px;
  height: 15px;
  display: block;
  position: relative;
  border-radius: 22px;
  background: #DFDFDF;
  cursor: pointer;
}
/* line 854, ../sass/_template_specific.scss */
.flex-control-nav li a.flex-active {
  background: #58AAD9;
}
/* line 858, ../sass/_template_specific.scss */
.flex-control-nav li a:before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  width: 15px;
  border: 1px solid #DFDFDF;
  height: 15px;
  padding: 2px;
  border-radius: 18px;
}

/*=== Bannerx2 Pictures and Locate ====*/
/* line 875, ../sass/_template_specific.scss */
.pictures_block {
  margin: 0 18px 30px 0 !important;
  position: relative;
}
/* line 879, ../sass/_template_specific.scss */
.pictures_block .plus-link {
  position: absolute;
  right: 20px;
  top: 17px;
  background: url("/img/jude2/flechaModulos.png") no-repeat center;
  width: 35px;
  height: 35px;
}

/* line 891, ../sass/_template_specific.scss */
.pictures_block .header {
  padding: 20px;
  background-color: #AFAFAF;
  height: 70px;
  box-sizing: border-box;
}
/* line 897, ../sass/_template_specific.scss */
.pictures_block .header h3 {
  margin-left: 60px;
  color: white;
  text-transform: uppercase;
  font-size: 25px;
  font-weight: lighter;
}
/* line 905, ../sass/_template_specific.scss */
.pictures_block .header p {
  margin-left: 60px;
  line-height: 14px;
  margin-top: 2px;
  font-size: 14px;
}

/* line 914, ../sass/_template_specific.scss */
.pictures_block .picture {
  width: 540px;
}

/* line 918, ../sass/_template_specific.scss */
.pictures_block .picture li {
  float: left;
  height: 83px;
  overflow: hidden;
}

/* line 926, ../sass/_template_specific.scss */
.picture img {
  width: 140px;
  height: 85px;
  margin: 1px;
}
/* line 932, ../sass/_template_specific.scss */
.picture ul {
  width: 110%;
}

/* line 937, ../sass/_template_specific.scss */
.myFancyPopupVideo img {
  width: 172px;
  height: 165px;
  margin-top: 1px;
}

/* line 943, ../sass/_template_specific.scss */
.sun {
  width: 381px !important;
  margin-left: 0px;
  margin-right: 0px;
  position: relative;
}
/* line 949, ../sass/_template_specific.scss */
.sun .header {
  background-color: #AFAFAF;
  padding: 17px;
  background-repeat: no-repeat;
  background-position: 20px 20px;
  height: 70px;
  box-sizing: border-box;
}
/* line 957, ../sass/_template_specific.scss */
.sun .header .arrow_right {
  position: absolute;
  top: 20px;
  right: 20px;
}
/* line 963, ../sass/_template_specific.scss */
.sun .header h3 {
  padding-left: 40px;
  color: white;
  text-transform: uppercase;
  font-size: 25px;
  padding-top: 3px;
  font-weight: lighter;
}
/* line 971, ../sass/_template_specific.scss */
.sun .header p {
  font-size: 14px;
  padding-left: 40px;
  line-height: 14px;
  margin-top: 2px;
}
/* line 978, ../sass/_template_specific.scss */
.sun li {
  float: left;
  width: auto;
  height: 167px;
}
/* line 983, ../sass/_template_specific.scss */
.sun li img {
  width: 125px;
  height: 135px;
  margin: 1px 1px 0 1px;
}

/* line 991, ../sass/_template_specific.scss */
.value_title {
  color: white;
  background-color: black;
  margin: 1px;
  margin-top: -4px;
  text-transform: uppercase;
  font-weight: 700;
  text-align: center;
  padding-top: 4px;
  height: 26px;
}

/* line 1003, ../sass/_template_specific.scss */
.camera {
  position: absolute;
  left: 20px;
  top: 20px;
  margin-top: 0px;
}

/* line 1011, ../sass/_template_specific.scss */
.sun li a {
  display: block;
  height: 166px;
  overflow: hidden;
}
/* line 1017, ../sass/_template_specific.scss */
.sun li img {
  width: 380px !important;
  height: auto;
}

/* line 1023, ../sass/_template_specific.scss */
.sun .value_title {
  width: 380px !important;
}

/*===== Banners x3 Bottom =====*/
/* line 1029, ../sass/_template_specific.scss */
.bannersx3_bottom {
  margin-bottom: 40px;
}
/* line 1032, ../sass/_template_specific.scss */
.bannersx3_bottom .title-banner {
  background-color: #AFAFAF;
  color: white;
  font-size: 22px;
  font-weight: lighter;
  padding: 25px;
  text-align: center;
  text-transform: uppercase;
}
/* line 1042, ../sass/_template_specific.scss */
.bannersx3_bottom .columna_info {
  margin: 1px;
  width: 378px;
}
/* line 1047, ../sass/_template_specific.scss */
.bannersx3_bottom .know_more {
  margin-top: -3px;
  background-color: #04284E;
  color: white;
  padding: 11px 0 4px 20px;
  text-transform: uppercase;
  font-weight: bold;
  position: relative;
  margin-left: 0px;
  width: 100%;
  height: 45px;
  box-sizing: border-box;
}
/* line 1060, ../sass/_template_specific.scss */
.bannersx3_bottom .know_more a {
  background: #04284E;
  width: 65px;
  position: absolute;
  right: 15px;
  top: 9px;
}
/* line 1068, ../sass/_template_specific.scss */
.bannersx3_bottom .know_more img {
  position: absolute;
  right: 10px;
  top: 15px;
}
/* line 1075, ../sass/_template_specific.scss */
.bannersx3_bottom .content {
  height: 136px;
  background-color: #F8F8F8;
  font-weight: 300;
  text-align: center;
}
/* line 1081, ../sass/_template_specific.scss */
.bannersx3_bottom .content p {
  padding: 15px;
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
  color: gray;
}
/* line 1089, ../sass/_template_specific.scss */
.bannersx3_bottom .content ul {
  padding: 15px;
  text-transform: uppercase;
  list-style-type: disc;
  font-weight: lighter;
  font-size: 14px;
  line-height: 20px;
  color: gray;
  list-style-position: inside;
}

/*========= Footer ======*/
/* line 1103, ../sass/_template_specific.scss */
footer {
  padding-bottom: 40px;
  width: 100%;
  text-align: center;
  background-color: #04284E;
  color: white;
}

/* line 1111, ../sass/_template_specific.scss */
footer a {
  color: rgba(255, 255, 255, 0.73) !important;
  text-decoration: none;
  font-weight: lighter;
  font-size: 15px;
}
/* line 1117, ../sass/_template_specific.scss */
footer a.button-promotion {
  color: #58AAD9 !important;
}

/* line 1122, ../sass/_template_specific.scss */
.full-copyright {
  text-align: center;
  margin-top: 5px;
  font-size: 12px;
}

/* line 1128, ../sass/_template_specific.scss */
.full-copyright a {
  font-weight: 100;
  font-size: 13px;
}

/* line 1133, ../sass/_template_specific.scss */
#google {
  text-align: right;
}

/* line 1137, ../sass/_template_specific.scss */
#facebook {
  text-align: left;
}

/* line 1141, ../sass/_template_specific.scss */
.footer_column {
  border-left: 1px solid white;
  margin: 45px 8px 0;
  height: auto;
  min-height: 260px;
  text-transform: uppercase;
  line-height: 25px;
}

/* line 1150, ../sass/_template_specific.scss */
.footer_column.column4.last {
  border-right: 1px solid white;
  padding-right: 8px;
}

/* line 1157, ../sass/_template_specific.scss */
#title_newsletter, #suscEmailLabel {
  display: none !important;
}

/* line 1161, ../sass/_template_specific.scss */
h3.footer_column_title {
  font-weight: 700;
  font-size: 15px;
}

/* line 1166, ../sass/_template_specific.scss */
div#newsletter {
  position: relative;
}

/* line 1170, ../sass/_template_specific.scss */
button#newsletter-button {
  width: 75%;
  background: #58AAD9;
  border: 0;
  height: 35px;
  background-position: center;
  color: white;
  text-transform: uppercase;
  font-size: 13px;
  margin-top: 5px;
}

/* line 1182, ../sass/_template_specific.scss */
input#suscEmail {
  height: 35px;
  width: 75%;
  background: rgba(255, 255, 255, 0.33);
  border: 0;
  padding: 0 20px;
  box-sizing: border-box;
  text-align: center;
  color: white;
  font-size: 14px;
  font-weight: lighter;
}

/* line 1195, ../sass/_template_specific.scss */
.newsletter_tit_label, .siguenos {
  font-weight: 700;
}

/* line 1199, ../sass/_template_specific.scss */
.newsletter_tit_label {
  margin-bottom: 10px;
}

/* line 1203, ../sass/_template_specific.scss */
.siguenos {
  margin-top: 40px;
  margin-bottom: 10px;
}

/* line 1208, ../sass/_template_specific.scss */
.social_likes_footer {
  margin-top: 25px;
}
/* line 1211, ../sass/_template_specific.scss */
.social_likes_footer #facebook_like {
  width: 49%;
  float: left;
  text-align: right;
  margin-top: 2px;
}
/* line 1218, ../sass/_template_specific.scss */
.social_likes_footer #google_plus_one {
  width: 49%;
  float: right;
  text-align: left;
}

/* line 1225, ../sass/_template_specific.scss */
div#div-txt-copyright {
  font-weight: lighter;
  color: rgba(255, 255, 255, 0.73);
}

/*==== Social Likes ====*/
/* line 1231, ../sass/_template_specific.scss */
.social_likes_bottom {
  text-align: center;
  background: #DFDFDF url("/img/jude2/background_social.png") no-repeat center 25px;
  padding: 40px;
}
/* line 1236, ../sass/_template_specific.scss */
.social_likes_bottom .socials_title {
  text-transform: uppercase;
  color: #58AAD9;
  font-size: 23px;
  font-weight: lighter;
  margin-bottom: 33px;
}
/* line 1245, ../sass/_template_specific.scss */
.social_likes_bottom #social a {
  text-decoration: none;
}
/* line 1249, ../sass/_template_specific.scss */
.social_likes_bottom #social img {
  width: auto;
  height: auto;
}
/* line 1254, ../sass/_template_specific.scss */
.social_likes_bottom img.pattern_element.middle_images {
  margin: 0 0px;
}
/* line 1258, ../sass/_template_specific.scss */
.social_likes_bottom #social {
  margin-bottom: 33px;
}

/* line 1264, ../sass/_template_specific.scss */
.pattern_images ul {
  margin-top: 40px;
  text-align: justify;
  justify-content: space-between;
}
/* line 1269, ../sass/_template_specific.scss */
.pattern_images ul:after {
  content: '';
  display: inline-block;
  width: 100%;
  height: 0;
}
/* line 1275, ../sass/_template_specific.scss */
.pattern_images li {
  display: inline-block;
  text-align: center;
}
/* line 1279, ../sass/_template_specific.scss */
.pattern_images li img {
  vertical-align: middle;
}

/*=== Experiences Carousel ===*/
/* line 1286, ../sass/_template_specific.scss */
.carousel_experience_wrapper {
  position: relative;
  margin-bottom: 40px;
}
/* line 1290, ../sass/_template_specific.scss */
.carousel_experience_wrapper h3.experience_title {
  background-color: #AFAFAF;
  height: 70px;
  box-sizing: border-box;
  text-align: center;
  color: white;
  text-transform: uppercase;
  font-size: 25px;
  font-weight: lighter;
  padding: 18px;
  margin-bottom: 5px;
}
/* line 1303, ../sass/_template_specific.scss */
.carousel_experience_wrapper .experiences_wrapper {
  position: relative;
}
/* line 1308, ../sass/_template_specific.scss */
.carousel_experience_wrapper .experience_element {
  margin: 0 5px;
}
/* line 1311, ../sass/_template_specific.scss */
.carousel_experience_wrapper .experience_element .exceded {
  height: 175px;
  overflow: hidden;
}
/* line 1315, ../sass/_template_specific.scss */
.carousel_experience_wrapper .experience_element .exceded img {
  width: 100%;
}
/* line 1320, ../sass/_template_specific.scss */
.carousel_experience_wrapper .experience_element .experience_description {
  background-color: #F8F8F8;
  text-align: center;
  padding: 20px 25px;
  min-height: 140px;
}
/* line 1326, ../sass/_template_specific.scss */
.carousel_experience_wrapper .experience_element .experience_description .experience_element_title {
  color: #58AAD9;
  font-weight: lighter;
}
/* line 1331, ../sass/_template_specific.scss */
.carousel_experience_wrapper .experience_element .experience_description .experience_element_content {
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
  color: gray;
}
/* line 1337, ../sass/_template_specific.scss */
.carousel_experience_wrapper .experience_element .experience_description .experience_element_content .hide_me {
  display: none;
}
/* line 1341, ../sass/_template_specific.scss */
.carousel_experience_wrapper .experience_element .experience_description .experience_element_content ul {
  list-style: circle inside;
}
/* line 1344, ../sass/_template_specific.scss */
.carousel_experience_wrapper .experience_element .experience_description .experience_element_content ul li {
  margin: auto;
}
/* line 1347, ../sass/_template_specific.scss */
.carousel_experience_wrapper .experience_element .experience_description .experience_element_content ul li strong {
  font-weight: bolder;
}
/* line 1353, ../sass/_template_specific.scss */
.carousel_experience_wrapper .experience_element .experience_description .experience_element_content .time {
  color: #58AAD9;
  font-size: 15px;
  margin-top: 7px;
  font-style: italic;
  display: block;
}
/* line 1361, ../sass/_template_specific.scss */
.carousel_experience_wrapper .experience_element .experience_description .experience_element_content .requeriments {
  color: #58AAD9;
  text-transform: uppercase;
  font-style: italic;
  font-size: 17px;
}

/*=== Dish Carousel ===*/
/* line 1374, ../sass/_template_specific.scss */
.carousel_dish_wrapper {
  position: relative;
  margin-bottom: 40px;
}
/* line 1378, ../sass/_template_specific.scss */
.carousel_dish_wrapper h3.dish_title {
  background-color: #AFAFAF;
  height: 70px;
  box-sizing: border-box;
  text-align: center;
  color: white;
  text-transform: uppercase;
  font-size: 25px;
  font-weight: lighter;
  padding: 18px;
  margin-bottom: 5px;
}
/* line 1391, ../sass/_template_specific.scss */
.carousel_dish_wrapper .dish_wrapper {
  position: relative;
}
/* line 1396, ../sass/_template_specific.scss */
.carousel_dish_wrapper .dish_element {
  margin: 0 5px;
}
/* line 1401, ../sass/_template_specific.scss */
.carousel_dish_wrapper .dish_element .exceded {
  height: 212px;
  overflow: hidden;
  position: relative;
}
/* line 1406, ../sass/_template_specific.scss */
.carousel_dish_wrapper .dish_element .exceded img {
  width: 100%;
  position: absolute;
  margin: auto;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
/* line 1417, ../sass/_template_specific.scss */
.carousel_dish_wrapper .dish_element .dish_description {
  background-color: #F8F8F8;
  text-align: center;
  padding: 0px 25px;
}
/* line 1423, ../sass/_template_specific.scss */
.carousel_dish_wrapper .dish_element .dish_description .dish_element_title {
  color: #58AAD9;
  font-weight: lighter;
  text-transform: uppercase;
  height: 60px;
  box-sizing: border-box;
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  width: 277.5px;
}
/* line 1435, ../sass/_template_specific.scss */
.carousel_dish_wrapper .dish_element .dish_description .dish_element_content {
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
  color: gray;
}
/* line 1441, ../sass/_template_specific.scss */
.carousel_dish_wrapper .dish_element .dish_description .dish_element_content .hide_me {
  display: none;
}
/* line 1445, ../sass/_template_specific.scss */
.carousel_dish_wrapper .dish_element .dish_description .dish_element_content ul {
  list-style: circle inside;
}
/* line 1448, ../sass/_template_specific.scss */
.carousel_dish_wrapper .dish_element .dish_description .dish_element_content ul li {
  margin: auto;
}
/* line 1451, ../sass/_template_specific.scss */
.carousel_dish_wrapper .dish_element .dish_description .dish_element_content ul li strong {
  font-weight: bolder;
}

/*====== Habitaciones ====*/
/* line 1463, ../sass/_template_specific.scss */
.rooms_wrapper {
  margin-top: 40px;
}
/* line 1466, ../sass/_template_specific.scss */
.rooms_wrapper .room_element {
  height: auto;
  margin-bottom: 20px;
  width: 100%;
  display: table;
}
/* line 1472, ../sass/_template_specific.scss */
.rooms_wrapper .room_element h3.room_title {
  font-size: 24px;
  color: #58AAD9;
  margin-bottom: 12px;
}
/* line 1477, ../sass/_template_specific.scss */
.rooms_wrapper .room_element h3.room_title span.capacity {
  font-weight: lighter;
}
/* line 1480, ../sass/_template_specific.scss */
.rooms_wrapper .room_element h3.room_title span.capacity .capacity_image {
  vertical-align: middle;
  padding-bottom: 4px;
}
/* line 1487, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_description {
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
  color: gray;
  padding-bottom: 11px;
  border-bottom: 1px solid #CECECE;
  margin-bottom: 33px;
}
/* line 1496, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_description .hide_me {
  display: none;
}
/* line 1501, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .exceded {
  width: 32%;
  height: 278px;
  float: left;
  position: relative;
  overflow: hidden;
}
/* line 1508, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .exceded img.room_image {
  min-height: 100%;
  max-width: none;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translateY(-50%) translateX(-50%);
}
/* line 1517, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .exceded img.plus_image {
  position: absolute;
  left: 20px;
  top: 20px;
  z-index: 1;
}
/* line 1525, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_description_wrapper {
  background: #F8F8F8;
  float: right;
  width: 68%;
  padding: 25px 40px;
  min-height: 278px;
  box-sizing: border-box;
  position: relative;
}
/* line 1536, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .service_elements .divide_0, .rooms_wrapper .room_element .service_elements .divide_1, .rooms_wrapper .room_element .service_elements .divide_2 {
  width: 32%;
  display: inline-table;
}
/* line 1540, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .service_elements .divide_0 li, .rooms_wrapper .room_element .service_elements .divide_1 li, .rooms_wrapper .room_element .service_elements .divide_2 li {
  font-size: 13px;
  color: #58AAD9;
  font-weight: lighter;
}
/* line 1545, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .service_elements .divide_0 li img, .rooms_wrapper .room_element .service_elements .divide_1 li img, .rooms_wrapper .room_element .service_elements .divide_2 li img {
  vertical-align: middle;
  margin-right: 5px;
}
/* line 1553, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_buttons_wrapper {
  position: absolute;
  top: 17px;
  right: 40px;
}
/* line 1558, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_buttons_wrapper img {
  vertical-align: middle;
  width: auto;
  display: none;
  height: 37px;
}
/* line 1565, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_buttons_wrapper .room_book {
  color: white;
  padding: 8px;
  background: #58AAD9;
  width: 117px;
  box-sizing: border-box;
  height: 37px;
  display: inline-block;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  float: right;
}
/* line 1580, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_types {
  position: absolute;
  top: 16px;
  right: 27%;
}
/* line 1585, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_types a {
  text-decoration: none;
}

/* line 1592, ../sass/_template_specific.scss */
.room_type_wrapper, .room_complete_description {
  display: none;
}
/* line 1595, ../sass/_template_specific.scss */
.room_type_wrapper h3, .room_complete_description h3 {
  font-size: 24px;
  color: #58AAD9;
  margin-bottom: 12px;
}
/* line 1601, ../sass/_template_specific.scss */
.room_type_wrapper > div, .room_complete_description > div {
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
  color: gray;
}

/*==== Automatic Content ====*/
/* line 1610, ../sass/_template_specific.scss */
.content_access_wrapper {
  background: #F5F5F5;
  padding: 40px 200px 20px;
  text-align: center;
  position: relative;
  margin-bottom: 35px;
  margin-top: 40px;
}
/* line 1618, ../sass/_template_specific.scss */
.content_access_wrapper#no-bg {
  padding: 40px 40px 40px;
}
/* line 1622, ../sass/_template_specific.scss */
.content_access_wrapper h3.section-title {
  text-transform: uppercase;
  color: #58AAD9;
  font-size: 23px;
  font-weight: lighter;
  margin-bottom: 8px;
}
/* line 1630, ../sass/_template_specific.scss */
.content_access_wrapper > div {
  font-weight: lighter;
  font-size: 14px;
  line-height: 32px;
  color: gray;
}
/* line 1637, ../sass/_template_specific.scss */
.content_access_wrapper div#my-bookings-form-fields {
  width: 300px;
  margin: auto;
  text-align: center;
  margin-top: 25px;
}
/* line 1643, ../sass/_template_specific.scss */
.content_access_wrapper div#my-bookings-form-fields label {
  font-weight: lighter;
  font-size: 14px;
  color: gray;
  min-width: 121px;
  display: block;
  margin-top: 8px;
}
/* line 1652, ../sass/_template_specific.scss */
.content_access_wrapper div#my-bookings-form-fields input {
  text-align: center;
}
/* line 1656, ../sass/_template_specific.scss */
.content_access_wrapper div#my-bookings-form-fields button#my-bookings-form-search-button {
  width: auto;
  background: #58AAD9;
  border: 0;
  height: 28px;
  background-position: center;
  color: white;
  text-transform: uppercase;
  font-size: 13px;
  padding: 0 20px;
  display: block;
  margin: 20px auto;
}

/* line 1672, ../sass/_template_specific.scss */
button#cancelButton {
  width: auto;
  background: #58AAD9;
  border: 0;
  height: 28px;
  background-position: center;
  color: white;
  text-transform: uppercase;
  font-size: 13px;
  padding: 0 20px;
  display: block;
  margin: 20px auto;
  display: none;
}

/* Ro0ms icons tooltips*/
/* line 1688, ../sass/_template_specific.scss */
.tooltip {
  display: inline;
  position: relative;
}

/* line 1693, ../sass/_template_specific.scss */
.tooltip:hover:after {
  background: #58AAD9;
  border-radius: 5px;
  bottom: 26px;
  color: white;
  content: attr(title);
  left: 20%;
  top: -54px;
  height: 22px;
  padding: 5px 15px;
  position: absolute;
  z-index: 98;
  text-align: center;
  text-transform: uppercase;
}

/*========= News ======*/
/* line 1711, ../sass/_template_specific.scss */
.block-new-description .date {
  font-size: 16px;
  color: #58AAD9;
}

/* line 1715, ../sass/_template_specific.scss */
.block-new-description .news-title {
  font-size: 18px;
}

/* line 1718, ../sass/_template_specific.scss */
.new:last-child {
  margin-bottom: 30px;
}

/* line 1723, ../sass/_template_specific.scss */
#no-bg {
  text-align: left;
  overflow: hidden;
}
/* line 1727, ../sass/_template_specific.scss */
#no-bg h3 {
  font-size: 24px;
  color: #58AAD9;
  text-transform: uppercase;
}
/* line 1733, ../sass/_template_specific.scss */
#no-bg #new-image {
  float: left;
  margin-right: 40px;
  width: 400px !important;
}
/* line 1738, ../sass/_template_specific.scss */
#no-bg #new-image img {
  width: 100% !important;
}
/* line 1742, ../sass/_template_specific.scss */
#no-bg .description {
  width: 620px;
  float: right;
}
/* line 1746, ../sass/_template_specific.scss */
#no-bg .date {
  color: #04284E;
}
/* line 1749, ../sass/_template_specific.scss */
#no-bg .share {
  float: left;
  margin-right: 10px;
  color: #58AAD9;
  font-size: 16px;
}
/* line 1755, ../sass/_template_specific.scss */
#no-bg .addthis_toolbox {
  padding-top: 9px !important;
}

/*========= Location and Contact ======*/
/* line 1763, ../sass/_template_specific.scss */
.page-localizacion #wrapper_content, .page-localizacion .how-to {
  background: rgba(252, 241, 235, 0.86);
  padding: 20px;
  margin-top: 200px;
  width: 1100px;
}
/* line 1770, ../sass/_template_specific.scss */
.page-localizacion .container12 .column6 {
  width: 530px;
}

/* line 1775, ../sass/_template_specific.scss */
.location-info-and-form-wrapper {
  background: rgba(226, 226, 226, 0.46);
  display: table;
  padding-top: 30px;
  padding-bottom: 20px;
  margin-bottom: 20px;
  margin-top: 40px;
}

/* line 1784, ../sass/_template_specific.scss */
.location-info-and-form-wrapper h1 {
  font-weight: 700;
  text-align: left;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  padding-bottom: 6px;
  width: 95%;
  line-height: 20px;
  text-transform: uppercase;
  color: #58AAD9;
  font-size: 23px;
  font-weight: lighter;
  margin-bottom: 8px;
}

/* line 1800, ../sass/_template_specific.scss */
.location-info strong {
  font-weight: bold;
}

/* line 1804, ../sass/_template_specific.scss */
#location-description-intro {
  margin-bottom: 30px;
}

/* line 1808, ../sass/_template_specific.scss */
.iframe-google-maps-wrapper {
  margin-top: 40px;
  width: 100%;
  margin-bottom: 40px;
}

/* line 1816, ../sass/_template_specific.scss */
.form-contact #title {
  display: none !important;
}

/* line 1820, ../sass/_template_specific.scss */
.form-contact #google-plus {
  display: none !important;
}

/* line 1824, ../sass/_template_specific.scss */
.form-contact .fb-like {
  display: none !important;
}

/* line 1828, ../sass/_template_specific.scss */
.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

/* line 1833, ../sass/_template_specific.scss */
.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  font-weight: lighter;
  font-size: 14px;
  line-height: 12px;
  color: gray;
}

/* line 1843, ../sass/_template_specific.scss */
.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 495px;
  border: 0px !important;
  height: 30px;
  background-color: white;
  color: #5a5a5a;
}

/* line 1853, ../sass/_template_specific.scss */
.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 495px;
  border: 0px;
  background-color: white;
  color: #5a5a5a;
  margin-right: 35px;
}

/* line 1863, ../sass/_template_specific.scss */
.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 35px;
}

/* line 1868, ../sass/_template_specific.scss */
.form-contact #contact-button {
  width: auto !important;
  background: #58AAD9 !important;
  border: 0;
  height: 28px;
  border-radius: 0 !important;
  background-position: center !important;
  color: white;
  text-transform: uppercase;
  font-size: 16px;
  padding: 3px 20px !important;
  display: block;
  margin: 24px auto;
  box-sizing: border-box;
  margin-right: 6px;
}
/* line 1884, ../sass/_template_specific.scss */
.form-contact #contact-button:hover {
  opacity: 0.8;
}

/* line 1889, ../sass/_template_specific.scss */
.location-info {
  padding-left: 20px;
  box-sizing: border-box;
  font-weight: lighter;
  font-size: 14px;
  line-height: 32px;
  color: gray;
}

/* line 1899, ../sass/_template_specific.scss */
.how-to {
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;
}
/* line 1905, ../sass/_template_specific.scss */
.how-to h3 {
  font-size: 30px;
  margin-bottom: 20px;
  color: #04284E;
}

/* line 1912, ../sass/_template_specific.scss */
.form-contact {
  float: right;
}

/*====== Galeria de Imagenes ====*/
/* line 1917, ../sass/_template_specific.scss */
.border-gallery {
  margin-top: 40px;
}

/* line 1921, ../sass/_template_specific.scss */
.border-gallery {
  margin-top: 20px;
}

/* line 1925, ../sass/_template_specific.scss */
cite {
  color: gray;
  text-align: right;
  display: block;
  margin-top: 20px;
  font-style: italic;
}

/*.gallery_1 li .crop{
  width: 322px !important;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
    height: 212px;
}
.gallery_1 li .crop img{
  height: 283px !important;
    width: 322px;
    left: 50%;
    margin-left: -200px;
    position: absolute;
    top: -51px;
}*/
/* line 1948, ../sass/_template_specific.scss */
.gallery_1 li .crop img {
  width: 283px;
  min-height: 215px;
}

/*====== Experiencias blocks ======*/
/* line 1954, ../sass/_template_specific.scss */
.experiencias_element {
  width: 270px;
  margin-right: 20px;
  float: left;
  margin-bottom: 40px;
}
/* line 1960, ../sass/_template_specific.scss */
.experiencias_element .exceded {
  position: relative;
}
/* line 1964, ../sass/_template_specific.scss */
.experiencias_element .since_pax {
  position: absolute;
  top: 15px;
  left: 15px;
  width: 100%;
}
/* line 1971, ../sass/_template_specific.scss */
.experiencias_element .since {
  background: #58AAD9;
  color: white;
  width: auto;
  height: auto;
  padding: 10px 10px;
  text-transform: uppercase;
  font-style: italic;
  text-align: center;
  box-sizing: border-box;
  display: inline-block;
  float: left;
  margin-right: 10px;
}
/* line 1989, ../sass/_template_specific.scss */
.experiencias_element .pax {
  background: #58AAD9;
  padding: 10px 5px;
  height: 40px;
  box-sizing: border-box;
  display: inline-block;
}
/* line 2000, ../sass/_template_specific.scss */
.experiencias_element .experience_description_wrapper {
  background: #F4F4F4;
  padding: 20px 25px;
  text-align: center;
  min-height: 140px;
}
/* line 2006, ../sass/_template_specific.scss */
.experiencias_element .experience_description_wrapper h3.experience_title {
  color: #58AAD9;
  font-weight: lighter;
}
/* line 2010, ../sass/_template_specific.scss */
.experiencias_element .experience_description_wrapper .experience_description {
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
  color: gray;
}
/* line 2016, ../sass/_template_specific.scss */
.experiencias_element .experience_description_wrapper .experience_description ul {
  list-style: circle inside;
}
/* line 2019, ../sass/_template_specific.scss */
.experiencias_element .experience_description_wrapper .experience_description ul li {
  margin: auto;
}
/* line 2022, ../sass/_template_specific.scss */
.experiencias_element .experience_description_wrapper .experience_description ul li strong {
  font-weight: bolder;
}
/* line 2028, ../sass/_template_specific.scss */
.experiencias_element .experience_description_wrapper .experience_description .time {
  color: #58AAD9;
  font-size: 15px;
  margin-top: 7px;
  font-style: italic;
  display: block;
}
/* line 2036, ../sass/_template_specific.scss */
.experiencias_element .experience_description_wrapper .experience_description .requeriments {
  color: #58AAD9;
  text-transform: uppercase;
  font-style: italic;
  font-size: 17px;
}
/* line 2045, ../sass/_template_specific.scss */
.experiencias_element .see_more_experience {
  display: none;
  cursor: pointer;
  float: left;
}
/* line 2050, ../sass/_template_specific.scss */
.experiencias_element .see_more_experience:hover {
  opacity: 0.8;
}
/* line 2054, ../sass/_template_specific.scss */
.experiencias_element .see_more_experience img {
  width: 40px;
  height: 40px;
}
/* line 2061, ../sass/_template_specific.scss */
.experiencias_element .buttons_wrapper .button-promotion {
  width: 100%;
  text-align: center;
  background: #58AAD9;
  color: white;
  display: inline-block;
  height: 40px;
  text-transform: uppercase;
  text-decoration: none;
  box-sizing: border-box;
  padding: 9px 0;
}
/* line 2073, ../sass/_template_specific.scss */
.experiencias_element .buttons_wrapper .button-promotion:hover {
  opacity: 0.8;
}
/* line 2079, ../sass/_template_specific.scss */
.experiencias_element .hide_me {
  display: none;
}

/* line 2085, ../sass/_template_specific.scss */
.hide_experience h3.experience_title {
  color: #58AAD9;
  font-weight: lighter;
  font-size: 19px;
}
/* line 2091, ../sass/_template_specific.scss */
.hide_experience .experience_description {
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
  color: gray;
  margin-top: 20px;
}
/* line 2099, ../sass/_template_specific.scss */
.hide_experience ul {
  list-style: circle inside;
  margin-bottom: 15px;
}

/*=== Vive Blocks ====*/
/* line 2106, ../sass/_template_specific.scss */
.vive_element {
  width: 370px;
  margin-right: 15px;
  float: left;
  margin-bottom: 23px;
  position: relative;
}
/* line 2113, ../sass/_template_specific.scss */
.vive_element .exceded {
  height: 230px;
  position: relative;
}
/* line 2117, ../sass/_template_specific.scss */
.vive_element .exceded img.plus_image {
  position: absolute;
  top: 20px;
  left: 20px;
  width: auto;
  cursor: pointer;
}
/* line 2124, ../sass/_template_specific.scss */
.vive_element .exceded img.plus_image:hover {
  opacity: 0.8;
}
/* line 2130, ../sass/_template_specific.scss */
.vive_element .vive_image {
  width: 100%;
  display: block;
}
/* line 2135, ../sass/_template_specific.scss */
.vive_element h3.vive_title {
  color: #58AAD9;
  text-align: center;
  font-weight: lighter;
  margin-bottom: 14px;
}
/* line 2141, ../sass/_template_specific.scss */
.vive_element h3.vive_title strong {
  display: block;
  font-weight: bolder;
}
/* line 2147, ../sass/_template_specific.scss */
.vive_element .vive_description_wrapper {
  background: #F4F4F4;
  text-align: center;
  padding: 18px 23px;
}
/* line 2152, ../sass/_template_specific.scss */
.vive_element .vive_description_wrapper .hide_me {
  display: none;
}
/* line 2156, ../sass/_template_specific.scss */
.vive_element .vive_description_wrapper .vive_description {
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
  height: 120px;
  color: gray;
  overflow: hidden;
}
/* line 2164, ../sass/_template_specific.scss */
.vive_element .vive_description_wrapper .vive_description strong {
  color: #58AAD9;
  font-weight: bolder;
}
/* line 2169, ../sass/_template_specific.scss */
.vive_element .vive_description_wrapper .vive_description .destacado {
  position: absolute;
  top: 177px;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: #58AAD9;
  padding: 11px;
  font-size: 20px;
}
/* line 2181, ../sass/_template_specific.scss */
.vive_element a.vive_button {
  color: white;
  background: #58AAD9;
  width: 100%;
  display: block;
  text-align: center;
  height: 40px;
  text-decoration: none;
  text-transform: uppercase;
  box-sizing: border-box;
  padding: 10px 0;
}
/* line 2193, ../sass/_template_specific.scss */
.vive_element a.vive_button:hover {
  opacity: 0.8;
}

/* line 2200, ../sass/_template_specific.scss */
.hide_vive_description .vive_title {
  color: #58AAD9;
  font-weight: lighter;
  margin-bottom: 14px;
}
/* line 2205, ../sass/_template_specific.scss */
.hide_vive_description .vive_title strong {
  display: block;
  font-weight: bolder;
}
/* line 2211, ../sass/_template_specific.scss */
.hide_vive_description .vive_description {
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
  color: gray;
}
/* line 2218, ../sass/_template_specific.scss */
.hide_vive_description .hide_me {
  display: inline;
}
/* line 2222, ../sass/_template_specific.scss */
.hide_vive_description .destacado {
  display: none;
}

/*======= Service Icos ======*/
/* line 2228, ../sass/_template_specific.scss */
.services_block_ul {
  min-height: 242px;
}

/* line 2232, ../sass/_template_specific.scss */
.service_block_0, .service_block_1, .service_block_2 {
  width: 33%;
  float: left;
  padding-left: 70px;
  box-sizing: border-box;
}
/* line 2238, ../sass/_template_specific.scss */
.service_block_0 .service_ico_element, .service_block_1 .service_ico_element, .service_block_2 .service_ico_element {
  font-weight: lighter;
  color: #58AAD9;
  margin-bottom: 5px;
}
/* line 2243, ../sass/_template_specific.scss */
.service_block_0 .service_ico_element img.ico, .service_block_1 .service_ico_element img.ico, .service_block_2 .service_ico_element img.ico {
  vertical-align: middle;
}

/* line 2249, ../sass/_template_specific.scss */
.service_block_0, .service_block_2 {
  border-left: 1px solid gray;
  border-right: 1px solid gray;
}

/* line 2254, ../sass/_template_specific.scss */
.service_icos_wrapper {
  display: table;
  width: 100%;
  margin-bottom: 40px;
}

/*===== Service Blocks ====*/
/* line 2261, ../sass/_template_specific.scss */
.service_blocks_wrapper {
  margin-bottom: 40px;
  display: table;
}
/* line 2265, ../sass/_template_specific.scss */
.service_blocks_wrapper h3.services_titles {
  height: 70px;
  font-size: 27px;
  background: #58AAD9;
  color: white;
  font-weight: lighter;
  text-align: center;
  padding: 17px 0;
  box-sizing: border-box;
  margin-bottom: 3px;
}
/* line 2277, ../sass/_template_specific.scss */
.service_blocks_wrapper .service_block_element {
  float: left;
  width: 138.1px;
  margin-right: 5px;
  overflow: hidden;
  margin-bottom: 5px;
}
/* line 2286, ../sass/_template_specific.scss */
.service_blocks_wrapper .exceded img {
  display: block;
  width: 100%;
}
/* line 2292, ../sass/_template_specific.scss */
.service_blocks_wrapper a.service_contact_button {
  color: white;
  text-align: center;
  background: #04284E;
  text-transform: uppercase;
  font-size: 15px;
  height: 41px;
  width: 70%;
  display: inline-block;
  float: right;
  box-sizing: border-box;
  padding: 10px 0;
  text-decoration: none;
}
/* line 2307, ../sass/_template_specific.scss */
.service_blocks_wrapper img.plus_service_image {
  display: block;
  width: 100%;
  height: auto;
}
/* line 2313, ../sass/_template_specific.scss */
.service_blocks_wrapper .buttons_wrapper {
  width: 100%;
}
/* line 2316, ../sass/_template_specific.scss */
.service_blocks_wrapper .buttons_wrapper a.image_link {
  text-align: center;
  width: 30%;
  display: block;
  float: left;
}

/* line 2325, ../sass/_template_specific.scss */
.hide_service_blocks {
  text-align: center;
  width: 500px;
}
/* line 2329, ../sass/_template_specific.scss */
.hide_service_blocks h3.service_block_title {
  color: #58AAD9;
  font-weight: lighter;
  margin-bottom: 10px;
  font-size: 23px;
}
/* line 2335, ../sass/_template_specific.scss */
.hide_service_blocks h3.service_block_title strong {
  display: block;
  font-weight: bolder;
}
/* line 2341, ../sass/_template_specific.scss */
.hide_service_blocks .service_block_description {
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
  color: gray;
}

/* line 2350, ../sass/_template_specific.scss */
#form_contact_promotion label.title {
  display: block;
  font-weight: lighter;
  font-size: 14px;
  line-height: 32px;
  color: gray;
}
/* line 2358, ../sass/_template_specific.scss */
#form_contact_promotion input {
  width: 296px;
  height: 20px;
}
/* line 2363, ../sass/_template_specific.scss */
#form_contact_promotion textarea#comments {
  width: 296px;
  border-color: #cccccc;
}
/* line 2368, ../sass/_template_specific.scss */
#form_contact_promotion a#contact-button-promo {
  color: white;
  text-align: center;
  background: #04284E;
  text-transform: uppercase;
  font-size: 15px;
  height: 33px;
  width: 100px;
  display: inline-block;
  float: left;
  box-sizing: border-box;
  padding: 7px 0;
  text-decoration: none;
  margin-top: 14px;
  cursor: pointer;
}
/* line 2384, ../sass/_template_specific.scss */
#form_contact_promotion a#contact-button-promo:hover {
  opacity: 0.8;
}

/*==== Hide Newsletter ====*/
/* line 2391, ../sass/_template_specific.scss */
div#hide_newsletter {
  position: fixed;
  top: 110px;
  z-index: 900;
  background: #04284E;
  padding: 10px;
  display: none;
}
/* line 2399, ../sass/_template_specific.scss */
div#hide_newsletter button#newsletter-button, div#hide_newsletter input#suscEmail {
  width: 100%;
}

/* line 2404, ../sass/_template_specific.scss */
.newsletter_banner {
  cursor: pointer;
}
/* line 2407, ../sass/_template_specific.scss */
.newsletter_banner:hover {
  opacity: 0.8;
}

/*==== Ofertas ===*/
/* line 2413, ../sass/_template_specific.scss */
.offers_wrapper {
  display: table;
  margin-top: 40px;
}
/* line 2417, ../sass/_template_specific.scss */
.offers_wrapper h3.vive_title {
  font-size: 20px;
  margin-top: 10px;
}
/* line 2423, ../sass/_template_specific.scss */
.offers_wrapper .vive_element .vive_button, .offers_wrapper .vive_element .button-promotion {
  width: 50%;
  display: inline-block;
  float: left;
}
/* line 2430, ../sass/_template_specific.scss */
.offers_wrapper .vive_element .button-promotion {
  color: white;
  background: #04284E;
  text-align: center;
  height: 40px;
  text-decoration: none;
  text-transform: uppercase;
  box-sizing: border-box;
  padding: 10px 0;
}
/* line 2440, ../sass/_template_specific.scss */
.offers_wrapper .vive_element .button-promotion:hover {
  opacity: 0.8;
}

@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (-webkit-min-device-pixel-ratio: 1) {
  /* line 2449, ../sass/_template_specific.scss */
  .carousel .next {
    left: 1080px !important;
  }

  /* line 2452, ../sass/_template_specific.scss */
  .experiences_wrapper .flex-next img {
    left: 1078px !important;
  }

  /* line 2455, ../sass/_template_specific.scss */
  .social_likes_bottom .container12 {
    width: 1040px !important;
  }
}
/*==== Hidden Booking Engine ===*/
/* line 2461, ../sass/_template_specific.scss */
#full_wrapper_booking {
  width: 100%;
  background-color: white;
  z-index: 22;
  box-shadow: 3px 2px 20px gray;
}

/* line 2471, ../sass/_template_specific.scss */
#full_wrapper_booking #wrapper_booking {
  position: relative;
  padding: 0;
}

/* line 2477, ../sass/_template_specific.scss */
#full_wrapper_booking #motor_reserva {
  position: relative !important;
  top: 0px !important;
  color: #5a5a5a;
  font-size: 12px;
  padding: 0px !important;
  margin: 10px;
  margin-bottom: -7px;
}

/* line 2488, ../sass/_template_specific.scss */
#full_wrapper_booking #fecha_entrada input, #fecha_salida input {
  background: #F4F4F4 url(/img/jude2/calendar_ico.png) no-repeat 104px !important;
  width: 120px !important;
  height: 30px !important;
  border: 0 !important;
  border-radius: 0px !important;
  padding-left: 15px;
  font-size: 15px;
  font-weight: 300;
  background-size: 20px !important;
}
/* line 2499, ../sass/_template_specific.scss */
#full_wrapper_booking #fecha_entrada input::-webkit-input-placeholder, #fecha_salida input::-webkit-input-placeholder {
  color: #7D7D7D;
}
/* line 2503, ../sass/_template_specific.scss */
#full_wrapper_booking #fecha_entrada input:-moz-placeholder, #fecha_salida input:-moz-placeholder {
  /* Firefox 18- */
  color: #7D7D7D;
}
/* line 2508, ../sass/_template_specific.scss */
#full_wrapper_booking #fecha_entrada input::-moz-placeholder, #fecha_salida input::-moz-placeholder {
  /* Firefox 19+ */
  color: #7D7D7D;
}
/* line 2513, ../sass/_template_specific.scss */
#full_wrapper_booking #fecha_entrada input:-ms-input-placeholder, #fecha_salida input:-ms-input-placeholder {
  color: #7D7D7D;
}

/* line 2518, ../sass/_template_specific.scss */
.colocar_fechas {
  margin: 0 0 5px 20px !important;
  height: 47px;
}

/* line 2523, ../sass/_template_specific.scss */
div#fecha_salida {
  height: auto;
}

/* line 2527, ../sass/_template_specific.scss */
#full_wrapper_booking #contenedor_habitaciones,
#full_wrapper_booking #contenedor_opciones {
  width: auto !important;
  margin: 0 0 0 5px !important;
}

/* line 2533, ../sass/_template_specific.scss */
#full_wrapper_booking #contenedor_fechas {
  width: 170px !important;
}

/* line 2537, ../sass/_template_specific.scss */
#full_wrapper_booking #contenedor_habitaciones {
  width: 130px !important;
  height: auto;
  display: inline-block;
  vertical-align: top;
}

/* line 2544, ../sass/_template_specific.scss */
#full_wrapper_booking #contenedor_opciones {
  display: inline-block;
  vertical-align: top;
  margin: 0;
  margin-left: 15px !important;
}

/* line 2551, ../sass/_template_specific.scss */
#full_wrapper_booking #titulo_fecha_entrada, #titulo_fecha_salida {
  float: left;
  margin-right: 10px;
  width: 120px !important;
  margin-top: 10px;
}

/* line 2558, ../sass/_template_specific.scss */
#full_wrapper_booking #booking_engine_title {
  padding: 20px;
  width: 175px;
  font-weight: 500;
  background-color: #04284E;
  display: inline-block;
  vertical-align: top;
}

/* line 2568, ../sass/_template_specific.scss */
div#booking_engine_title.wrapper-old-web-support {
  width: 100% !important;
  background: transparent !important;
  color: #04284E !important;
  padding: 0 20px 20px !important;
}
/* line 2574, ../sass/_template_specific.scss */
div#booking_engine_title.wrapper-old-web-support .web_support_label_1, div#booking_engine_title.wrapper-old-web-support .web_support_label_2 {
  display: inline-block;
}

/* line 2579, ../sass/_template_specific.scss */
.wrapper-old-web-support .web_support_label_2:before, .wrapper-new-web-support .web_support_label_2:before {
  margin-left: 3px !important;
}

/* line 2583, ../sass/_template_specific.scss */
#full_wrapper_booking #booking_title1 {
  display: none;
  text-align: center;
  font-size: 18px;
  color: #04284E;
  text-transform: uppercase;
}

/* line 2591, ../sass/_template_specific.scss */
#full_wrapper_booking #booking_title2 {
  text-align: center;
  font-size: 18px;
  line-height: 30px;
  color: white;
  text-transform: uppercase;
}

/* line 2599, ../sass/_template_specific.scss */
#full_wrapper_booking #best_price {
  text-align: center;
  font-size: 11px;
  color: white;
  text-transform: uppercase;
}

/* line 2606, ../sass/_template_specific.scss */
#full_wrapper_booking #info_ninos {
  font-size: 9px !important;
  top: 3px;
  left: 170px;
  width: 100px;
  display: none;
}

/* line 2614, ../sass/_template_specific.scss */
#full_wrapper_booking #search-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background-color: #58AAD9;
  color: white;
  margin: auto !important;
  width: 150px;
  text-transform: uppercase;
  font-size: 15px;
}
/* line 2626, ../sass/_template_specific.scss */
#full_wrapper_booking #search-button:hover {
  opacity: 0.8;
}

/* line 2631, ../sass/_template_specific.scss */
#full_wrapper_booking #search-button:hover {
  background-color: #58AAD9;
}

/* line 2635, ../sass/_template_specific.scss */
#full_wrapper_booking .spinner {
  text-align: center;
  height: 30px;
}

/* line 2640, ../sass/_template_specific.scss */
#full_wrapper_booking #envio input {
  width: 130px !important;
  height: 30px !important;
  border: 0px !important;
  border-radius: 0px !important;
  margin: 0px auto 23px !important;
  text-align: center;
  font-size: 14px;
  font-weight: 300;
}
/* line 2650, ../sass/_template_specific.scss */
#full_wrapper_booking #envio input::-webkit-input-placeholder {
  color: #7D7D7D;
  font-size: 12px;
}
/* line 2655, ../sass/_template_specific.scss */
#full_wrapper_booking #envio input:-moz-placeholder {
  /* Firefox 18- */
  color: #7D7D7D;
  font-size: 12px;
}
/* line 2661, ../sass/_template_specific.scss */
#full_wrapper_booking #envio input::-moz-placeholder {
  /* Firefox 19+ */
  color: #7D7D7D;
  font-size: 12px;
}
/* line 2667, ../sass/_template_specific.scss */
#full_wrapper_booking #envio input:-ms-input-placeholder {
  color: #7D7D7D;
  font-size: 12px;
}

/* line 2673, ../sass/_template_specific.scss */
#full_wrapper_booking #envio {
  text-align: center;
  display: inline-block;
  vertical-align: top;
  width: 150px;
  margin: 0 !important;
  margin-left: 10px !important;
  padding-bottom: 5px;
}

/* line 2684, ../sass/_template_specific.scss */
#full_wrapper_booking #ui-datepicker div {
  z-index: 9999 !important;
}

/* line 2688, ../sass/_template_specific.scss */
#full_wrapper_booking #selector_habitaciones {
  width: 75px !important;
}

/* line 2692, ../sass/_template_specific.scss */
#full_wrapper_booking .adultos {
  margin: 0px 5px 0 0 !important;
}

/* line 2696, ../sass/_template_specific.scss */
#full_wrapper_booking .ninos {
  float: left;
  margin: 0px 0 0 12px;
}

/* line 2701, ../sass/_template_specific.scss */
#full_wrapper_booking #ui-datepicker-div {
  z-index: 99999 !important;
}

/* line 2705, ../sass/_template_specific.scss */
#full_wrapper_booking #motor_reserva select {
  width: 130px !important;
  height: 32px !important;
  padding: 5px;
  padding-left: 15px;
  font-size: 15px;
  line-height: 100%;
  border: 0px;
  border-radius: 0;
  -webkit-appearance: none;
  color: #7D7D7D;
  font-weight: 300;
  margin-bottom: 15px;
  background: #F4F4F4 url(/img/jude2/arrow_down_2.png) no-repeat 114px !important;
}

/* line 2721, ../sass/_template_specific.scss */
#ui-datepicker-div {
  z-index: 999999 !important;
}

/* line 2725, ../sass/_template_specific.scss */
#motor_reserva label, #motor_reserva p {
  color: white;
}

/* line 2729, ../sass/_template_specific.scss */
#contenedor_fechas {
  display: inline-block;
  vertical-align: top;
  margin: 0 !important;
}

/* line 2735, ../sass/_template_specific.scss */
#booking label {
  display: none !important;
}

/* line 2739, ../sass/_template_specific.scss */
#full_wrapper_booking #envio input {
  background-color: #F4F4F4;
}

/* line 2743, ../sass/_template_specific.scss */
#contenedor_hotel {
  display: none;
}

/*============== Bottom Pop-up ============*/
/* line 2750, ../sass/_template_specific.scss */
.bottom_popup {
  position: fixed;
  width: 100%;
  height: 60px;
  background: #04284E;
  left: 0;
  bottom: 0;
  z-index: 1000;
}

/* line 2760, ../sass/_template_specific.scss */
.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

/* line 2766, ../sass/_template_specific.scss */
.bottom_popup .bottom_popup_text {
  width: 890px;
  float: left;
  color: white;
  padding: 10px;
  font-family: Arial, sans-serif;
  font-size: 14px;
}

/* line 2775, ../sass/_template_specific.scss */
.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

/* line 2779, ../sass/_template_specific.scss */
.close_button {
  float: right;
  cursor: pointer;
}

/* line 2784, ../sass/_template_specific.scss */
button.bottom_popup_button {
  width: 120px;
  background: #bebebe;
  border: 0;
  height: 36px;
  position: absolute;
  color: white;
  background-position: center;
  border-radius: 5px;
  cursor: pointer;
  bottom: 12px;
  font-size: 16px;
}

/* line 2799, ../sass/_template_specific.scss */
#wrapper2 {
  width: 1140px;
  margin: 0 auto;
}

/* line 2804, ../sass/_template_specific.scss */
.popup_inicial_booknow {
  width: 100%;
  height: 100%;
  background-size: cover !important;
}
/* line 2808, ../sass/_template_specific.scss */
.popup_inicial_booknow .email, .popup_inicial_booknow .discount, .popup_inicial_booknow .compra, .popup_inicial_booknow .popup_description {
  text-align: center;
}
/* line 2811, ../sass/_template_specific.scss */
.popup_inicial_booknow .compra {
  padding-top: 5px;
  color: white;
  font-size: 22px;
  font-weight: lighter;
}
/* line 2817, ../sass/_template_specific.scss */
.popup_inicial_booknow .discount {
  padding-top: 7px;
  color: white;
  font-size: 47px;
  text-shadow: 3px 3px black;
  text-transform: uppercase;
  font-family: 'Oswald', sans-serif;
}
/* line 2825, ../sass/_template_specific.scss */
.popup_inicial_booknow .popup_description {
  padding-top: 39px;
  color: white;
  font-size: 22px;
  font-weight: lighter;
}
/* line 2831, ../sass/_template_specific.scss */
.popup_inicial_booknow form.form_popup {
  text-align: center;
  padding-top: 50px;
}
/* line 2834, ../sass/_template_specific.scss */
.popup_inicial_booknow form.form_popup li {
  text-align: center;
}
/* line 2837, ../sass/_template_specific.scss */
.popup_inicial_booknow form.form_popup input#id_email, .popup_inicial_booknow form.form_popup input#id_name, .popup_inicial_booknow form.form_popup input#id_surname {
  height: 26px;
  text-align: center;
  width: 270px;
  font-size: 17px;
  box-shadow: 2px 2px black;
  border: 0px;
  color: #04284E;
  margin: 10px 0;
}
/* line 2847, ../sass/_template_specific.scss */
.popup_inicial_booknow form.form_popup button.popup_button {
  margin: 7px auto 0px;
  width: 277px;
  height: 40px;
  background: #04284E;
  font-size: 17px;
  border: 0px;
  text-transform: uppercase;
  color: white;
  cursor: pointer;
}
/* line 2859, ../sass/_template_specific.scss */
.popup_inicial_booknow .spinner_wrapper_faldon {
  padding-top: 20px;
  width: 80px !important;
}
/* line 2863, ../sass/_template_specific.scss */
.popup_inicial_booknow .popup_message {
  color: white;
  padding-top: 25px;
  font-size: 20px;
  font-weight: lighter;
}

/* line 2873, ../sass/_template_specific.scss */
.politica_fancybox p {
  font-size: 11px;
  padding-top: 5px;
  line-height: 15px;
  font-family: "Open Sans", sans-serif;
  text-align: justify;
}
/* line 2881, ../sass/_template_specific.scss */
.politica_fancybox strong, .politica_fancybox h3 {
  color: #B99E87;
  text-align: center;
  font-size: 15px;
  margin-bottom: 25px;
  font-weight: lighter;
}
/* line 2889, ../sass/_template_specific.scss */
.politica_fancybox h3 {
  font-family: arial, helvetica, sans-serif;
  font-size: 20px;
  margin-top: 20px;
}
/* line 2895, ../sass/_template_specific.scss */
.politica_fancybox .politica_img img {
  width: 100%;
}
/* line 2899, ../sass/_template_specific.scss */
.politica_fancybox ul {
  padding-left: 20px;
}
/* line 2903, ../sass/_template_specific.scss */
.politica_fancybox ul li {
  font-size: 11px;
  padding-top: 5px;
  font-family: "Open Sans", sans-serif;
  list-style-type: disc;
  text-align: left;
}
/* line 2911, ../sass/_template_specific.scss */
.politica_fancybox .popup_description {
  color: black;
  padding-top: 0;
}
/* line 2915, ../sass/_template_specific.scss */
.politica_fancybox .popup_description div {
  font-size: 11px;
  padding-top: 5px;
  line-height: 15px;
  font-family: "Open Sans", sans-serif;
  text-align: justify;
}
/* line 2924, ../sass/_template_specific.scss */
.politica_fancybox .fancybox-inner {
  width: auto !important;
}
/* line 2928, ../sass/_template_specific.scss */
.politica_fancybox #popup_politica {
  height: 400px;
}
