@import url(//fonts.googleapis.com/css?family=Montserrat|Source+Sans+Pro:400,300,700,600);
/* line 8, ../../../../sass/plugins/_effects.scss */
.effects_sass {
  visibility: hidden;
}

/*=== Translate None with fadeOut ===*/
/*=== Translate From Bottom with fadeOut ===*/
@-webkit-keyframes slide_up {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-moz-keyframes slide_up {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-o-keyframes slide_up {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@keyframes slide_up {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
/* line 76, ../../../../sass/plugins/_effects.scss */
.slide_up_effect {
  -webkit-animation: slide_up 1s;
  /* Safari 4+ */
  -moz-animation: slide_up 1s;
  /* Fx 5+ */
  -o-animation: slide_up 1s;
  /* Opera 12+ */
  animation: slide_up 1s;
}

@-webkit-keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-moz-keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-o-keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
/* line 117, ../../../../sass/plugins/_effects.scss */
.slide_right_effect {
  -webkit-animation: slide_right 1s;
  /* Safari 4+ */
  -moz-animation: slide_right 1s;
  /* Fx 5+ */
  -o-animation: slide_right 1s;
  /* Opera 12+ */
  animation: slide_right 1s;
}

/*=== Translate From Bottom witout fadeOut ===*/
@-webkit-keyframes slide_up_no_fade_out {
  0% {
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-moz-keyframes slide_up_no_fade_out {
  0% {
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-o-keyframes slide_up_no_fade_out {
  0% {
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@keyframes slide_up_no_fade_out {
  0% {
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
/* line 174, ../../../../sass/plugins/_effects.scss */
.slide_up_no_fade_out_effect {
  -webkit-animation: slide_up_no_fade_out 1s;
  /* Safari 4+ */
  -moz-animation: slide_up_no_fade_out 1s;
  /* Fx 5+ */
  -o-animation: slide_up_no_fade_out 1s;
  /* Opera 12+ */
  animation: slide_up_no_fade_out 1s;
}

/*=== Fade Out Effect ===*/
@-webkit-keyframes fade_out_effect {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-moz-keyframes fade_out_effect {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-o-keyframes fade_out_effect {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fade_out_effect {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* line 223, ../../../../sass/plugins/_effects.scss */
.fade_out_trigger {
  -webkit-animation: fade_out_effect 1.5s;
  /* Safari 4+ */
  -moz-animation: fade_out_effect 1.5s;
  /* Fx 5+ */
  -o-animation: fade_out_effect 1.5s;
  /* Opera 12+ */
  animation: fade_out_effect 1.5s;
}

/*=== Pendule Effect ===*/
/*=== initial transform ===*/
@-webkit-keyframes pendule {
  0% {
    -webkit-transform: rotate(10deg);
    -moz-transform: rotate(10deg);
    -ms-transform: rotate(10deg);
    -o-transform: rotate(10deg);
    transform: rotate(10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
  100% {
    -webkit-transform: rotate(-10deg);
    -moz-transform: rotate(-10deg);
    -ms-transform: rotate(-10deg);
    -o-transform: rotate(-10deg);
    transform: rotate(-10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
}
@-moz-keyframes pendule {
  0% {
    -webkit-transform: rotate(10deg);
    -moz-transform: rotate(10deg);
    -ms-transform: rotate(10deg);
    -o-transform: rotate(10deg);
    transform: rotate(10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
  100% {
    -webkit-transform: rotate(-10deg);
    -moz-transform: rotate(-10deg);
    -ms-transform: rotate(-10deg);
    -o-transform: rotate(-10deg);
    transform: rotate(-10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
}
@-o-keyframes pendule {
  0% {
    -webkit-transform: rotate(10deg);
    -moz-transform: rotate(10deg);
    -ms-transform: rotate(10deg);
    -o-transform: rotate(10deg);
    transform: rotate(10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
  100% {
    -webkit-transform: rotate(-10deg);
    -moz-transform: rotate(-10deg);
    -ms-transform: rotate(-10deg);
    -o-transform: rotate(-10deg);
    transform: rotate(-10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
}
@keyframes pendule {
  0% {
    -webkit-transform: rotate(10deg);
    -moz-transform: rotate(10deg);
    -ms-transform: rotate(10deg);
    -o-transform: rotate(10deg);
    transform: rotate(10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
  100% {
    -webkit-transform: rotate(-10deg);
    -moz-transform: rotate(-10deg);
    -ms-transform: rotate(-10deg);
    -o-transform: rotate(-10deg);
    transform: rotate(-10deg);
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
}
/* line 276, ../../../../sass/plugins/_effects.scss */
.pendule {
  -webkit-animation: pendule 2s ease-in-out 0s infinite alternate;
  /* Safari 4+ */
  -moz-animation: pendule 2s ease-in-out 0s infinite alternate;
  /* Fx 5+ */
  -o-animation: pendule 2s ease-in-out 0s infinite alternate;
  /* Opera 12+ */
  animation: pendule 2s ease-in-out 0s infinite alternate;
}

/*=== Text Translate ===*/
/* line 281, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom {
  overflow: hidden;
  position: relative;
}
/* line 285, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom::after {
  content: attr(data-content);
  left: 0;
  position: absolute;
  -moz-transition: -moz-transform, 0.6s;
  -o-transition: -o-transform, 0.6s;
  -webkit-transition: -webkit-transform, 0.6s;
  transition: transform, 0.6s;
  -moz-transform: translate(0, 100%);
  -ms-transform: translate(0, 100%);
  -webkit-transform: translate(0, 100%);
  transform: translate(0, 100%);
}
/* line 293, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom span {
  -moz-transition: -moz-transform, 0.6s;
  -o-transition: -o-transform, 0.6s;
  -webkit-transition: -webkit-transform, 0.6s;
  transition: transform, 0.6s;
  -moz-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}
/* line 299, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom:hover::after {
  -moz-transform: translate(0, 0%);
  -ms-transform: translate(0, 0%);
  -webkit-transform: translate(0, 0%);
  transform: translate(0, 0%);
}
/* line 303, ../../../../sass/plugins/_effects.scss */
.text_translate_bottom:hover span {
  -moz-transform: translate(0, -100%);
  -ms-transform: translate(0, -100%);
  -webkit-transform: translate(0, -100%);
  transform: translate(0, -100%);
}

/*=== Flip Effect ===*/
@-webkit-keyframes flip {
  0% {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
  100% {
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
}
@-moz-keyframes flip {
  0% {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
  100% {
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
}
@-o-keyframes flip {
  0% {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
  100% {
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
}
@keyframes flip {
  0% {
    -webkit-transform: rotateY(0deg);
    -moz-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
  100% {
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg);
    /*-webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;*/
  }
}
/* line 358, ../../../../sass/plugins/_effects.scss */
.flip {
  -webkit-animation: flip 3s ease-in-out 0s infinite;
  /* Safari 4+ */
  -moz-animation: flip 3s ease-in-out 0s infinite;
  /* Fx 5+ */
  -o-animation: flip 3s ease-in-out 0s infinite;
  /* Opera 12+ */
  animation: flip 3s ease-in-out 0s infinite;
}

/*=== moveBright Effect ===*/
@-webkit-keyframes moveBrightToRight {
  0% {
    left: -500%;
  }
  100% {
    left: 500%;
  }
}
@-moz-keyframes moveBrightToRight {
  0% {
    left: -500%;
  }
  100% {
    left: 500%;
  }
}
@-o-keyframes moveBrightToRight {
  0% {
    left: -500%;
  }
  100% {
    left: 500%;
  }
}
@keyframes moveBrightToRight {
  0% {
    left: -500%;
  }
  100% {
    left: 500%;
  }
}
@font-face {
  font-family: 'FontAwesome';
  src: url("//maxcdn.bootstrapcdn.com/font-awesome/4.7.0/fonts/fontawesome-webfont.eot?v=4.7.0");
  src: url("//maxcdn.bootstrapcdn.com/font-awesome/4.7.0/fonts/fontawesome-webfont.eot?#iefix&v=4.7.0") format("embedded-opentype"), url("//maxcdn.bootstrapcdn.com/font-awesome/4.7.0/fonts/fontawesome-webfont.woff2?v=4.7.0") format("woff2"), url("//maxcdn.bootstrapcdn.com/font-awesome/4.7.0/fonts/fontawesome-webfont.woff?v=4.7.0") format("woff"), url("//maxcdn.bootstrapcdn.com/font-awesome/4.7.0/fonts/fontawesome-webfont.ttf?v=4.7.0") format("truetype"), url("//maxcdn.bootstrapcdn.com/font-awesome/4.7.0/fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular") format("svg");
  font-weight: normal;
  font-style: normal;
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa, #dataBono .rooms_number_wrapper .plus_night:before, #dataBono .rooms_number_wrapper .less_night:before {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lg {
  font-size: 1.33333333em;
  line-height: .75em;
  vertical-align: -15%;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-3x {
  font-size: 3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-4x {
  font-size: 4em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-5x {
  font-size: 5em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fw {
  width: 1.28571429em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul > li {
  position: relative;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: .14285714em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li.fa-lg {
  left: -1.85714286em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-border {
  padding: .2em .25em .15em;
  border: solid .08em #eee;
  border-radius: .1em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-left, #dataBono .rooms_number_wrapper .fa-pull-left.plus_night:before, #dataBono .rooms_number_wrapper .fa-pull-left.less_night:before {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-right, #dataBono .rooms_number_wrapper .fa-pull-right.plus_night:before, #dataBono .rooms_number_wrapper .fa-pull-right.less_night:before {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-left, #dataBono .rooms_number_wrapper .pull-left.plus_night:before, #dataBono .rooms_number_wrapper .pull-left.less_night:before {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-right, #dataBono .rooms_number_wrapper .pull-right.plus_night:before, #dataBono .rooms_number_wrapper .pull-right.less_night:before {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  transform: scale(-1, 1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical {
  filter: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x, .fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x {
  line-height: inherit;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inverse {
  color: #fff;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glass:before {
  content: "\f000";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-music:before {
  content: "\f001";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search:before {
  content: "\f002";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-o:before {
  content: "\f003";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart:before {
  content: "\f004";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star:before {
  content: "\f005";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-o:before {
  content: "\f006";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user:before {
  content: "\f007";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-film:before {
  content: "\f008";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-large:before {
  content: "\f009";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th:before {
  content: "\f00a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-list:before {
  content: "\f00b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check:before {
  content: "\f00c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-remove:before, .fa-close:before, .fa-times:before {
  content: "\f00d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-plus:before {
  content: "\f00e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-minus:before {
  content: "\f010";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-power-off:before {
  content: "\f011";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signal:before {
  content: "\f012";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gear:before, .fa-cog:before {
  content: "\f013";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash-o:before {
  content: "\f014";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-home:before {
  content: "\f015";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-o:before {
  content: "\f016";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clock-o:before {
  content: "\f017";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-road:before {
  content: "\f018";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-download:before {
  content: "\f019";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-down:before {
  content: "\f01a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-up:before {
  content: "\f01b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inbox:before {
  content: "\f01c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle-o:before {
  content: "\f01d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-right:before, .fa-repeat:before {
  content: "\f01e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-refresh:before {
  content: "\f021";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-alt:before {
  content: "\f022";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lock:before {
  content: "\f023";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag:before {
  content: "\f024";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-headphones:before {
  content: "\f025";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-off:before {
  content: "\f026";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-down:before {
  content: "\f027";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-up:before {
  content: "\f028";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qrcode:before {
  content: "\f029";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-barcode:before {
  content: "\f02a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tag:before {
  content: "\f02b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tags:before {
  content: "\f02c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-book:before {
  content: "\f02d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark:before {
  content: "\f02e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-print:before {
  content: "\f02f";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera:before {
  content: "\f030";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-font:before {
  content: "\f031";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bold:before {
  content: "\f032";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-italic:before {
  content: "\f033";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-height:before {
  content: "\f034";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-width:before {
  content: "\f035";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-left:before {
  content: "\f036";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-center:before {
  content: "\f037";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-right:before {
  content: "\f038";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-justify:before {
  content: "\f039";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list:before {
  content: "\f03a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dedent:before, .fa-outdent:before {
  content: "\f03b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-indent:before {
  content: "\f03c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-video-camera:before {
  content: "\f03d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-photo:before, .fa-image:before, .fa-picture-o:before {
  content: "\f03e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil:before {
  content: "\f040";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-marker:before {
  content: "\f041";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adjust:before {
  content: "\f042";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tint:before {
  content: "\f043";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edit:before, .fa-pencil-square-o:before {
  content: "\f044";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square-o:before {
  content: "\f045";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square-o:before {
  content: "\f046";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows:before {
  content: "\f047";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-backward:before {
  content: "\f048";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-backward:before {
  content: "\f049";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-backward:before {
  content: "\f04a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play:before {
  content: "\f04b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause:before {
  content: "\f04c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop:before {
  content: "\f04d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forward:before {
  content: "\f04e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-forward:before {
  content: "\f050";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-forward:before {
  content: "\f051";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eject:before {
  content: "\f052";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-left:before, #dataBono .rooms_number_wrapper .less_night:before {
  content: "\f053";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-right:before, #dataBono .rooms_number_wrapper .plus_night:before {
  content: "\f054";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-circle:before {
  content: "\f055";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-circle:before {
  content: "\f056";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle:before {
  content: "\f057";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle:before {
  content: "\f058";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle:before {
  content: "\f059";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info-circle:before {
  content: "\f05a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crosshairs:before {
  content: "\f05b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle-o:before {
  content: "\f05c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle-o:before {
  content: "\f05d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ban:before {
  content: "\f05e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-left:before {
  content: "\f060";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-right:before {
  content: "\f061";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-up:before {
  content: "\f062";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-down:before {
  content: "\f063";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-forward:before, .fa-share:before {
  content: "\f064";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expand:before {
  content: "\f065";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compress:before {
  content: "\f066";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus:before {
  content: "\f067";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus:before {
  content: "\f068";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asterisk:before {
  content: "\f069";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation-circle:before {
  content: "\f06a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gift:before {
  content: "\f06b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leaf:before {
  content: "\f06c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire:before {
  content: "\f06d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye:before {
  content: "\f06e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye-slash:before {
  content: "\f070";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-warning:before, .fa-exclamation-triangle:before {
  content: "\f071";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plane:before {
  content: "\f072";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar:before {
  content: "\f073";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-random:before {
  content: "\f074";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment:before {
  content: "\f075";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magnet:before {
  content: "\f076";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-up:before {
  content: "\f077";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-down:before {
  content: "\f078";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-retweet:before {
  content: "\f079";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-cart:before {
  content: "\f07a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder:before {
  content: "\f07b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open:before {
  content: "\f07c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-v:before {
  content: "\f07d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-h:before {
  content: "\f07e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bar-chart-o:before, .fa-bar-chart:before {
  content: "\f080";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter-square:before {
  content: "\f081";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-square:before {
  content: "\f082";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera-retro:before {
  content: "\f083";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-key:before {
  content: "\f084";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gears:before, .fa-cogs:before {
  content: "\f085";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments:before {
  content: "\f086";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-up:before {
  content: "\f087";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-down:before {
  content: "\f088";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half:before {
  content: "\f089";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart-o:before {
  content: "\f08a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-out:before {
  content: "\f08b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin-square:before {
  content: "\f08c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumb-tack:before {
  content: "\f08d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link:before {
  content: "\f08e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-in:before {
  content: "\f090";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trophy:before {
  content: "\f091";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-square:before {
  content: "\f092";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-upload:before {
  content: "\f093";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lemon-o:before {
  content: "\f094";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone:before {
  content: "\f095";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square-o:before {
  content: "\f096";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark-o:before {
  content: "\f097";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone-square:before {
  content: "\f098";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter:before {
  content: "\f099";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-f:before, .fa-facebook:before {
  content: "\f09a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github:before {
  content: "\f09b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock:before {
  content: "\f09c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card:before {
  content: "\f09d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-feed:before, .fa-rss:before {
  content: "\f09e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hdd-o:before {
  content: "\f0a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullhorn:before {
  content: "\f0a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell:before {
  content: "\f0f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-certificate:before {
  content: "\f0a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-right:before {
  content: "\f0a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-left:before {
  content: "\f0a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-up:before {
  content: "\f0a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-down:before {
  content: "\f0a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-left:before {
  content: "\f0a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-right:before {
  content: "\f0a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-up:before {
  content: "\f0aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-down:before {
  content: "\f0ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-globe:before {
  content: "\f0ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wrench:before {
  content: "\f0ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tasks:before {
  content: "\f0ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-filter:before {
  content: "\f0b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-briefcase:before {
  content: "\f0b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-alt:before {
  content: "\f0b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-group:before, .fa-users:before {
  content: "\f0c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chain:before, .fa-link:before {
  content: "\f0c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud:before {
  content: "\f0c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flask:before {
  content: "\f0c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cut:before, .fa-scissors:before {
  content: "\f0c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copy:before, .fa-files-o:before {
  content: "\f0c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paperclip:before {
  content: "\f0c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-save:before, .fa-floppy-o:before {
  content: "\f0c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square:before {
  content: "\f0c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-navicon:before, .fa-reorder:before, .fa-bars:before {
  content: "\f0c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ul:before {
  content: "\f0ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ol:before {
  content: "\f0cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-strikethrough:before {
  content: "\f0cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-underline:before {
  content: "\f0cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-table:before {
  content: "\f0ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magic:before {
  content: "\f0d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-truck:before {
  content: "\f0d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest:before {
  content: "\f0d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-square:before {
  content: "\f0d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-square:before {
  content: "\f0d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus:before {
  content: "\f0d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-money:before {
  content: "\f0d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-down:before {
  content: "\f0d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-up:before {
  content: "\f0d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-left:before {
  content: "\f0d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-right:before {
  content: "\f0da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-columns:before {
  content: "\f0db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unsorted:before, .fa-sort:before {
  content: "\f0dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-down:before, .fa-sort-desc:before {
  content: "\f0dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-up:before, .fa-sort-asc:before {
  content: "\f0de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope:before {
  content: "\f0e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin:before {
  content: "\f0e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-left:before, .fa-undo:before {
  content: "\f0e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-legal:before, .fa-gavel:before {
  content: "\f0e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashboard:before, .fa-tachometer:before {
  content: "\f0e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment-o:before {
  content: "\f0e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments-o:before {
  content: "\f0e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flash:before, .fa-bolt:before {
  content: "\f0e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sitemap:before {
  content: "\f0e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-umbrella:before {
  content: "\f0e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paste:before, .fa-clipboard:before {
  content: "\f0ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lightbulb-o:before {
  content: "\f0eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exchange:before {
  content: "\f0ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-download:before {
  content: "\f0ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-upload:before {
  content: "\f0ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-md:before {
  content: "\f0f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stethoscope:before {
  content: "\f0f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-suitcase:before {
  content: "\f0f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-o:before {
  content: "\f0a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-coffee:before {
  content: "\f0f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cutlery:before {
  content: "\f0f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text-o:before {
  content: "\f0f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building-o:before {
  content: "\f0f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hospital-o:before {
  content: "\f0f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ambulance:before {
  content: "\f0f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medkit:before {
  content: "\f0fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fighter-jet:before {
  content: "\f0fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-beer:before {
  content: "\f0fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-h-square:before {
  content: "\f0fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square:before {
  content: "\f0fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-left:before {
  content: "\f100";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-right:before {
  content: "\f101";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-up:before {
  content: "\f102";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-down:before {
  content: "\f103";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-left:before {
  content: "\f104";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-right:before {
  content: "\f105";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-up:before {
  content: "\f106";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-down:before {
  content: "\f107";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-desktop:before {
  content: "\f108";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-laptop:before {
  content: "\f109";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tablet:before {
  content: "\f10a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mobile-phone:before, .fa-mobile:before {
  content: "\f10b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o:before {
  content: "\f10c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-left:before {
  content: "\f10d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-right:before {
  content: "\f10e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spinner:before {
  content: "\f110";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle:before {
  content: "\f111";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply:before, .fa-reply:before {
  content: "\f112";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-alt:before {
  content: "\f113";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-o:before {
  content: "\f114";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open-o:before {
  content: "\f115";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-smile-o:before {
  content: "\f118";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-frown-o:before {
  content: "\f119";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meh-o:before {
  content: "\f11a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gamepad:before {
  content: "\f11b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-keyboard-o:before {
  content: "\f11c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-o:before {
  content: "\f11d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-checkered:before {
  content: "\f11e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-terminal:before {
  content: "\f120";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code:before {
  content: "\f121";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply-all:before, .fa-reply-all:before {
  content: "\f122";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before {
  content: "\f123";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-location-arrow:before {
  content: "\f124";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crop:before {
  content: "\f125";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code-fork:before {
  content: "\f126";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlink:before, .fa-chain-broken:before {
  content: "\f127";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question:before {
  content: "\f128";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info:before {
  content: "\f129";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation:before {
  content: "\f12a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superscript:before {
  content: "\f12b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subscript:before {
  content: "\f12c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eraser:before {
  content: "\f12d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-puzzle-piece:before {
  content: "\f12e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone:before {
  content: "\f130";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone-slash:before {
  content: "\f131";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shield:before {
  content: "\f132";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-o:before {
  content: "\f133";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire-extinguisher:before {
  content: "\f134";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rocket:before {
  content: "\f135";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-maxcdn:before {
  content: "\f136";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-left:before {
  content: "\f137";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-right:before {
  content: "\f138";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-up:before {
  content: "\f139";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-down:before {
  content: "\f13a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-html5:before {
  content: "\f13b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-css3:before {
  content: "\f13c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-anchor:before {
  content: "\f13d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock-alt:before {
  content: "\f13e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullseye:before {
  content: "\f140";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-h:before {
  content: "\f141";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-v:before {
  content: "\f142";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rss-square:before {
  content: "\f143";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle:before {
  content: "\f144";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ticket:before {
  content: "\f145";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square:before {
  content: "\f146";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square-o:before {
  content: "\f147";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-up:before {
  content: "\f148";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-down:before {
  content: "\f149";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square:before {
  content: "\f14a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil-square:before {
  content: "\f14b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link-square:before {
  content: "\f14c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square:before {
  content: "\f14d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compass:before {
  content: "\f14e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-down:before, .fa-caret-square-o-down:before {
  content: "\f150";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-up:before, .fa-caret-square-o-up:before {
  content: "\f151";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-right:before, .fa-caret-square-o-right:before {
  content: "\f152";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-euro:before, .fa-eur:before {
  content: "\f153";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gbp:before {
  content: "\f154";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dollar:before, .fa-usd:before {
  content: "\f155";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rupee:before, .fa-inr:before {
  content: "\f156";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before {
  content: "\f157";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ruble:before, .fa-rouble:before, .fa-rub:before {
  content: "\f158";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-won:before, .fa-krw:before {
  content: "\f159";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitcoin:before, .fa-btc:before {
  content: "\f15a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file:before {
  content: "\f15b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text:before {
  content: "\f15c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-asc:before {
  content: "\f15d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-desc:before {
  content: "\f15e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-asc:before {
  content: "\f160";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-desc:before {
  content: "\f161";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-asc:before {
  content: "\f162";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-desc:before {
  content: "\f163";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-up:before {
  content: "\f164";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-down:before {
  content: "\f165";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-square:before {
  content: "\f166";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube:before {
  content: "\f167";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing:before {
  content: "\f168";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing-square:before {
  content: "\f169";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-play:before {
  content: "\f16a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dropbox:before {
  content: "\f16b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-overflow:before {
  content: "\f16c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-instagram:before {
  content: "\f16d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flickr:before {
  content: "\f16e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adn:before {
  content: "\f170";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket:before {
  content: "\f171";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket-square:before {
  content: "\f172";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr:before {
  content: "\f173";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr-square:before {
  content: "\f174";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-down:before {
  content: "\f175";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-up:before {
  content: "\f176";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-left:before {
  content: "\f177";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-right:before {
  content: "\f178";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-apple:before {
  content: "\f179";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-windows:before {
  content: "\f17a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-android:before {
  content: "\f17b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linux:before {
  content: "\f17c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dribbble:before {
  content: "\f17d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skype:before {
  content: "\f17e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-foursquare:before {
  content: "\f180";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trello:before {
  content: "\f181";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-female:before {
  content: "\f182";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-male:before {
  content: "\f183";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gittip:before, .fa-gratipay:before {
  content: "\f184";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sun-o:before {
  content: "\f185";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-moon-o:before {
  content: "\f186";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-archive:before {
  content: "\f187";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bug:before {
  content: "\f188";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vk:before {
  content: "\f189";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-weibo:before {
  content: "\f18a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-renren:before {
  content: "\f18b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pagelines:before {
  content: "\f18c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-exchange:before {
  content: "\f18d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-right:before {
  content: "\f18e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-left:before {
  content: "\f190";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-left:before, .fa-caret-square-o-left:before {
  content: "\f191";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dot-circle-o:before {
  content: "\f192";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair:before {
  content: "\f193";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo-square:before {
  content: "\f194";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-turkish-lira:before, .fa-try:before {
  content: "\f195";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square-o:before {
  content: "\f196";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-space-shuttle:before {
  content: "\f197";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slack:before {
  content: "\f198";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-square:before {
  content: "\f199";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wordpress:before {
  content: "\f19a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-openid:before {
  content: "\f19b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-institution:before, .fa-bank:before, .fa-university:before {
  content: "\f19c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mortar-board:before, .fa-graduation-cap:before {
  content: "\f19d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yahoo:before {
  content: "\f19e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google:before {
  content: "\f1a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit:before {
  content: "\f1a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-square:before {
  content: "\f1a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon-circle:before {
  content: "\f1a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon:before {
  content: "\f1a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-delicious:before {
  content: "\f1a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-digg:before {
  content: "\f1a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-pp:before {
  content: "\f1a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-alt:before {
  content: "\f1a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drupal:before {
  content: "\f1a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-joomla:before {
  content: "\f1aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-language:before {
  content: "\f1ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fax:before {
  content: "\f1ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building:before {
  content: "\f1ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-child:before {
  content: "\f1ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paw:before {
  content: "\f1b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spoon:before {
  content: "\f1b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cube:before {
  content: "\f1b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cubes:before {
  content: "\f1b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance:before {
  content: "\f1b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance-square:before {
  content: "\f1b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam:before {
  content: "\f1b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam-square:before {
  content: "\f1b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-recycle:before {
  content: "\f1b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-automobile:before, .fa-car:before {
  content: "\f1b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cab:before, .fa-taxi:before {
  content: "\f1ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tree:before {
  content: "\f1bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spotify:before {
  content: "\f1bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deviantart:before {
  content: "\f1bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soundcloud:before {
  content: "\f1be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-database:before {
  content: "\f1c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-pdf-o:before {
  content: "\f1c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-word-o:before {
  content: "\f1c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-excel-o:before {
  content: "\f1c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-powerpoint-o:before {
  content: "\f1c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before {
  content: "\f1c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-zip-o:before, .fa-file-archive-o:before {
  content: "\f1c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-sound-o:before, .fa-file-audio-o:before {
  content: "\f1c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-movie-o:before, .fa-file-video-o:before {
  content: "\f1c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-code-o:before {
  content: "\f1c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vine:before {
  content: "\f1ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codepen:before {
  content: "\f1cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-jsfiddle:before {
  content: "\f1cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before {
  content: "\f1cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o-notch:before {
  content: "\f1ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ra:before, .fa-resistance:before, .fa-rebel:before {
  content: "\f1d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ge:before, .fa-empire:before {
  content: "\f1d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git-square:before {
  content: "\f1d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git:before {
  content: "\f1d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before {
  content: "\f1d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tencent-weibo:before {
  content: "\f1d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qq:before {
  content: "\f1d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wechat:before, .fa-weixin:before {
  content: "\f1d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send:before, .fa-paper-plane:before {
  content: "\f1d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send-o:before, .fa-paper-plane-o:before {
  content: "\f1d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-history:before {
  content: "\f1da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-thin:before {
  content: "\f1db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-header:before {
  content: "\f1dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paragraph:before {
  content: "\f1dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sliders:before {
  content: "\f1de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt:before {
  content: "\f1e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt-square:before {
  content: "\f1e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bomb:before {
  content: "\f1e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soccer-ball-o:before, .fa-futbol-o:before {
  content: "\f1e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tty:before {
  content: "\f1e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-binoculars:before {
  content: "\f1e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plug:before {
  content: "\f1e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slideshare:before {
  content: "\f1e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitch:before {
  content: "\f1e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yelp:before {
  content: "\f1e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-newspaper-o:before {
  content: "\f1ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wifi:before {
  content: "\f1eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calculator:before {
  content: "\f1ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paypal:before {
  content: "\f1ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-wallet:before {
  content: "\f1ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-visa:before {
  content: "\f1f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-mastercard:before {
  content: "\f1f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-discover:before {
  content: "\f1f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-amex:before {
  content: "\f1f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-paypal:before {
  content: "\f1f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-stripe:before {
  content: "\f1f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash:before {
  content: "\f1f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash-o:before {
  content: "\f1f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash:before {
  content: "\f1f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copyright:before {
  content: "\f1f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-at:before {
  content: "\f1fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eyedropper:before {
  content: "\f1fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paint-brush:before {
  content: "\f1fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-birthday-cake:before {
  content: "\f1fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-area-chart:before {
  content: "\f1fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pie-chart:before {
  content: "\f200";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-line-chart:before {
  content: "\f201";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm:before {
  content: "\f202";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm-square:before {
  content: "\f203";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-off:before {
  content: "\f204";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-on:before {
  content: "\f205";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bicycle:before {
  content: "\f206";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bus:before {
  content: "\f207";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ioxhost:before {
  content: "\f208";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angellist:before {
  content: "\f209";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc:before {
  content: "\f20a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shekel:before, .fa-sheqel:before, .fa-ils:before {
  content: "\f20b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meanpath:before {
  content: "\f20c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-buysellads:before {
  content: "\f20d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-connectdevelop:before {
  content: "\f20e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashcube:before {
  content: "\f210";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forumbee:before {
  content: "\f211";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leanpub:before {
  content: "\f212";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sellsy:before {
  content: "\f213";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shirtsinbulk:before {
  content: "\f214";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-simplybuilt:before {
  content: "\f215";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skyatlas:before {
  content: "\f216";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-plus:before {
  content: "\f217";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-arrow-down:before {
  content: "\f218";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-diamond:before {
  content: "\f219";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ship:before {
  content: "\f21a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-secret:before {
  content: "\f21b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-motorcycle:before {
  content: "\f21c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-street-view:before {
  content: "\f21d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heartbeat:before {
  content: "\f21e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus:before {
  content: "\f221";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars:before {
  content: "\f222";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mercury:before {
  content: "\f223";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-intersex:before, .fa-transgender:before {
  content: "\f224";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-transgender-alt:before {
  content: "\f225";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-double:before {
  content: "\f226";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-double:before {
  content: "\f227";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-mars:before {
  content: "\f228";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke:before {
  content: "\f229";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-v:before {
  content: "\f22a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-h:before {
  content: "\f22b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-neuter:before {
  content: "\f22c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-genderless:before {
  content: "\f22d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-official:before {
  content: "\f230";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-p:before {
  content: "\f231";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-whatsapp:before {
  content: "\f232";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-server:before {
  content: "\f233";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-plus:before {
  content: "\f234";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-times:before {
  content: "\f235";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hotel:before, .fa-bed:before {
  content: "\f236";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viacoin:before {
  content: "\f237";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-train:before {
  content: "\f238";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subway:before {
  content: "\f239";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medium:before {
  content: "\f23a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yc:before, .fa-y-combinator:before {
  content: "\f23b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-optin-monster:before {
  content: "\f23c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opencart:before {
  content: "\f23d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expeditedssl:before {
  content: "\f23e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before {
  content: "\f240";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-3:before, .fa-battery-three-quarters:before {
  content: "\f241";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-2:before, .fa-battery-half:before {
  content: "\f242";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-1:before, .fa-battery-quarter:before {
  content: "\f243";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-0:before, .fa-battery-empty:before {
  content: "\f244";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mouse-pointer:before {
  content: "\f245";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-i-cursor:before {
  content: "\f246";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-group:before {
  content: "\f247";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-ungroup:before {
  content: "\f248";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note:before {
  content: "\f249";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note-o:before {
  content: "\f24a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-jcb:before {
  content: "\f24b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-diners-club:before {
  content: "\f24c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clone:before {
  content: "\f24d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-balance-scale:before {
  content: "\f24e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-o:before {
  content: "\f250";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-1:before, .fa-hourglass-start:before {
  content: "\f251";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-2:before, .fa-hourglass-half:before {
  content: "\f252";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-3:before, .fa-hourglass-end:before {
  content: "\f253";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass:before {
  content: "\f254";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-grab-o:before, .fa-hand-rock-o:before {
  content: "\f255";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-stop-o:before, .fa-hand-paper-o:before {
  content: "\f256";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-scissors-o:before {
  content: "\f257";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-lizard-o:before {
  content: "\f258";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-spock-o:before {
  content: "\f259";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-pointer-o:before {
  content: "\f25a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-peace-o:before {
  content: "\f25b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trademark:before {
  content: "\f25c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-registered:before {
  content: "\f25d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-creative-commons:before {
  content: "\f25e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg:before {
  content: "\f260";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg-circle:before {
  content: "\f261";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tripadvisor:before {
  content: "\f262";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki:before {
  content: "\f263";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki-square:before {
  content: "\f264";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-get-pocket:before {
  content: "\f265";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wikipedia-w:before {
  content: "\f266";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-safari:before {
  content: "\f267";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chrome:before {
  content: "\f268";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-firefox:before {
  content: "\f269";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opera:before {
  content: "\f26a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-internet-explorer:before {
  content: "\f26b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tv:before, .fa-television:before {
  content: "\f26c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-contao:before {
  content: "\f26d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-500px:before {
  content: "\f26e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-amazon:before {
  content: "\f270";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-plus-o:before {
  content: "\f271";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-minus-o:before {
  content: "\f272";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-times-o:before {
  content: "\f273";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-check-o:before {
  content: "\f274";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-industry:before {
  content: "\f275";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-pin:before {
  content: "\f276";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-signs:before {
  content: "\f277";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-o:before {
  content: "\f278";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map:before {
  content: "\f279";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting:before {
  content: "\f27a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting-o:before {
  content: "\f27b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-houzz:before {
  content: "\f27c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo:before {
  content: "\f27d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-black-tie:before {
  content: "\f27e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fonticons:before {
  content: "\f280";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-alien:before {
  content: "\f281";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edge:before {
  content: "\f282";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card-alt:before {
  content: "\f283";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codiepie:before {
  content: "\f284";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-modx:before {
  content: "\f285";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fort-awesome:before {
  content: "\f286";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-usb:before {
  content: "\f287";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-product-hunt:before {
  content: "\f288";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mixcloud:before {
  content: "\f289";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-scribd:before {
  content: "\f28a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle:before {
  content: "\f28b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle-o:before {
  content: "\f28c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle:before {
  content: "\f28d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle-o:before {
  content: "\f28e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-bag:before {
  content: "\f290";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-basket:before {
  content: "\f291";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hashtag:before {
  content: "\f292";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth:before {
  content: "\f293";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth-b:before {
  content: "\f294";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-percent:before {
  content: "\f295";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gitlab:before {
  content: "\f296";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpbeginner:before {
  content: "\f297";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpforms:before {
  content: "\f298";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envira:before {
  content: "\f299";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-universal-access:before {
  content: "\f29a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair-alt:before {
  content: "\f29b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle-o:before {
  content: "\f29c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-blind:before {
  content: "\f29d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-audio-description:before {
  content: "\f29e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-control-phone:before {
  content: "\f2a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-braille:before {
  content: "\f2a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-assistive-listening-systems:before {
  content: "\f2a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before {
  content: "\f2a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide:before {
  content: "\f2a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide-g:before {
  content: "\f2a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signing:before, .fa-sign-language:before {
  content: "\f2a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-low-vision:before {
  content: "\f2a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo:before {
  content: "\f2a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo-square:before {
  content: "\f2aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat:before {
  content: "\f2ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-ghost:before {
  content: "\f2ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-square:before {
  content: "\f2ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper:before {
  content: "\f2ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-first-order:before {
  content: "\f2b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yoast:before {
  content: "\f2b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-themeisle:before {
  content: "\f2b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-circle:before, .fa-google-plus-official:before {
  content: "\f2b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fa:before, .fa-font-awesome:before {
  content: "\f2b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-handshake-o:before {
  content: "\f2b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open:before {
  content: "\f2b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open-o:before {
  content: "\f2b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linode:before {
  content: "\f2b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book:before {
  content: "\f2b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book-o:before {
  content: "\f2ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard:before, .fa-address-card:before {
  content: "\f2bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard-o:before, .fa-address-card-o:before {
  content: "\f2bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle:before {
  content: "\f2bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle-o:before {
  content: "\f2be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-o:before {
  content: "\f2c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-id-badge:before {
  content: "\f2c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license:before, .fa-id-card:before {
  content: "\f2c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license-o:before, .fa-id-card-o:before {
  content: "\f2c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quora:before {
  content: "\f2c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-free-code-camp:before {
  content: "\f2c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-telegram:before {
  content: "\f2c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before {
  content: "\f2c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-3:before, .fa-thermometer-three-quarters:before {
  content: "\f2c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-2:before, .fa-thermometer-half:before {
  content: "\f2c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-1:before, .fa-thermometer-quarter:before {
  content: "\f2ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-0:before, .fa-thermometer-empty:before {
  content: "\f2cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shower:before {
  content: "\f2cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bathtub:before, .fa-s15:before, .fa-bath:before {
  content: "\f2cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-podcast:before {
  content: "\f2ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-maximize:before {
  content: "\f2d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-minimize:before {
  content: "\f2d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-restore:before {
  content: "\f2d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle:before, .fa-window-close:before {
  content: "\f2d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle-o:before, .fa-window-close-o:before {
  content: "\f2d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bandcamp:before {
  content: "\f2d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-grav:before {
  content: "\f2d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-etsy:before {
  content: "\f2d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-imdb:before {
  content: "\f2d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ravelry:before {
  content: "\f2d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eercast:before {
  content: "\f2da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microchip:before {
  content: "\f2db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snowflake-o:before {
  content: "\f2dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superpowers:before {
  content: "\f2dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpexplorer:before {
  content: "\f2de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meetup:before {
  content: "\f2e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

/*=== Effects With Delay ===*/
/* line 28, ../sass/_defaults.scss */
.slide_up_t1 {
  -webkit-animation: slide_up_no_fade_out 1s;
  /* Safari 4+ */
  -moz-animation: slide_up_no_fade_out 1s;
  /* Fx 5+ */
  -o-animation: slide_up_no_fade_out 1s;
  /* Opera 12+ */
  animation: slide_up_no_fade_out 1s;
}

/* line 32, ../sass/_defaults.scss */
.slide_up_t2 {
  -webkit-animation: slide_up_no_fade_out 1.5s;
  /* Safari 4+ */
  -moz-animation: slide_up_no_fade_out 1.5s;
  /* Fx 5+ */
  -o-animation: slide_up_no_fade_out 1.5s;
  /* Opera 12+ */
  animation: slide_up_no_fade_out 1.5s;
}

/* line 3, ../../../../sass/booking/_booking_engine_5.scss */
.booking_widget {
  position: relative;
}

/* line 7, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 {
  width: 275px;
  display: block;
}
/* line 11, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 form.booking_form {
  padding: 0 !important;
}
/* line 15, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 .booking_form_title {
  background: #383838;
}

/* line 20, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper {
  border-bottom: 1px solid lightgray;
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat 92% 70% !important;
}
/* line 24, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
  display: inline-block;
  margin: 10px 0 0 10px;
}

/* line 32, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input {
  border: none;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 25px;
  font-weight: 600;
  background: white;
}
/* line 40, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input::-webkit-input-placeholder {
  color: #585d63;
}
/* line 44, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input:-moz-placeholder {
  /* Firefox 18- */
  color: #585d63;
}
/* line 48, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input::-moz-placeholder {
  /* Firefox 19+ */
  color: #585d63;
}
/* line 52, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input:-ms-input-placeholder {
  color: #585d63;
}

/* line 57, ../../../../sass/booking/_booking_engine_5.scss */
.entry_date_wrapper {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 66.6%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}

/* line 72, ../../../../sass/booking/_booking_engine_5.scss */
.departure_date_wrapper, .half_size {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 50%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}
/* line 86, ../../../../sass/booking/_booking_engine_5.scss */
.departure_date_wrapper .date_day, .half_size .date_day {
  font-size: 21px !important;
}

/* line 91, ../../../../sass/booking/_booking_engine_5.scss */
.nights_number_wrapper {
  float: right;
  height: 70px;
  border-bottom: 1px solid lightgrey;
  box-sizing: border-box;
  width: 33.3%;
  padding: 5px 10px;
}

/* line 100, ../../../../sass/booking/_booking_engine_5.scss */
.num_nights_label, .entry_date_label, .children_label, .rooms_label, .adults_label, .promocode_label, .departure_date_label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/* line 106, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 30%;
}

/*===== Entry date =====*/
/* line 113, ../../../../sass/booking/_booking_engine_5.scss */
.date_box.entry_date, .date_box.departure_date {
  font-size: 20px;
  background: url(/static_1/images/booking_5/calendar.png) no-repeat center right;
  margin-top: 9px;
  background-size: 29px;
}
/* line 119, ../../../../sass/booking/_booking_engine_5.scss */
.date_box.entry_date .date_day, .date_box.departure_date .date_day {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 25px;
  font-weight: 600;
}

/*====== Booking button ====*/
/* line 128, ../../../../sass/booking/_booking_engine_5.scss */
button.submit_button {
  width: 100%;
  border: 0;
  border-radius: 0;
  background: #ffd600;
  height: 55px;
  text-transform: uppercase;
  font-weight: bolder;
  font-size: 21px;
  color: #565656;
  cursor: pointer;
}

/*====== Rooms section ======*/
/* line 142, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 33.3%;
  padding: 7px 10px 5px;
  box-sizing: border-box;
}
/* line 149, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper .room_list_wrapper li.room {
  float: right;
  width: 69%;
}
/* line 155, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper .adults_selector {
  height: 70px;
  float: left;
  border-right: 1px solid gray;
  width: 46.5%;
}

/* line 163, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper {
  display: table;
  float: right;
  width: 66.6%;
}
/* line 168, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .room_title {
  display: none;
}
/* line 172, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .adults_selector {
  border-right: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
}
/* line 177, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
  width: 50%;
  display: inline-block;
  height: 70px;
  box-sizing: border-box;
  padding: 7px 10px 5px;
}
/* line 185, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .children_selector {
  width: 50% !important;
  padding: 7px 10px 5px;
  float: right;
}

/* line 192, ../../../../sass/booking/_booking_engine_5.scss */
.room_list .room {
  height: 70px;
}

/* line 196, ../../../../sass/booking/_booking_engine_5.scss */
.room_list .room.room3, .room_list .room.room2 {
  border-top: 1px solid lightgrey;
}

/*==== Promocode section  ====*/
/* line 201, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_wrapper {
  display: table;
  width: 100%;
  padding: 10px 10px 5px;
  border-top: 1px solid lightgrey;
  box-sizing: border-box;
}

/* line 209, ../../../../sass/booking/_booking_engine_5.scss */
label.promocode_label {
  display: block;
}

/* line 213, ../../../../sass/booking/_booking_engine_5.scss */
input.promocode_input {
  border: 0;
  width: 100%;
  display: block;
  height: 30px;
  font-size: 13px;
  font-weight: lighter;
  margin-top: 2px;
  font-family: sans-serif;
}

/*====== Selectric ======*/
/* line 225, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems {
  overflow: scroll !important;
  width: 92px !important;
  margin-left: -11px;
  margin-top: 2px;
}
/* line 231, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 237, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li {
  font-weight: 500;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAADAQMAAABs5if8AAAABlBMVEXOzs7y8vKNS741AAAADklEQVQI12M4wPSAWQAABoQBtgsaY5kAAAAASUVORK5CYII=);
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  line-height: 29px !important;
  font-size: 18px !important;
  font-family: 'Source Sans Pro', sans-serif;
}
/* line 247, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li:hover {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 254, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems ul {
  z-index: 40;
}
/* line 258, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems .room {
  padding-top: 17px;
}

/* line 264, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 .selectric .button {
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 23px;
  margin-left: 0;
  margin-top: 8px;
  text-indent: 999px;
  height: 22px;
  font-weight: 600;
}

/* line 277, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper .selectric .label {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 28px;
  margin-left: 0;
  font-weight: 600;
}

/* line 285, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper .selectric {
  margin-top: 3px;
}

/* line 289, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper {
  width: 78px !important;
}

/*======= Phone web support =======*/
/* line 294, ../../../../sass/booking/_booking_engine_5.scss */
.wrapper-new-web-support.booking_form_title {
  border-radius: 0;
  padding: 5px 10px;
  font-family: 'Montserrat', sans-serif;
  font-size: 11px !important;
  margin-top: 11px;
}

/* line 302, ../../../../sass/booking/_booking_engine_5.scss */
.wrapper-new-web-support .web_support_number, .web_support_label_1 {
  font-size: 11px !important;
  margin-left: 5px;
}

/* line 307, ../../../../sass/booking/_booking_engine_5.scss */
img.phone_support_image {
  float: left;
  width: 28px;
  margin-top: 1px;
  margin-right: 12px;
}

/*======= Header title ======*/
/* line 315, ../../../../sass/booking/_booking_engine_5.scss */
.booking_title_1, .booking_title_2, .best_price {
  display: none;
}

/* line 319, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header {
  font-family: 'Montserrat', sans-serif;
  padding: 10px;
  display: table;
  width: 100%;
  box-sizing: border-box;
  cursor: pointer;
}
/* line 327, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header:hover {
  opacity: 0.8;
}
/* line 331, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header img.booking_header_discount {
  float: left;
  margin-right: 15px;
}
/* line 336, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header p.first_offer_name {
  font-size: 12px;
  color: #8096c7;
  margin-top: 9px;
}
/* line 342, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header p.second_offer_name {
  font-size: 10px;
  margin-top: 3px;
  color: gray;
}

/* line 349, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker {
  width: 283px;
}
/* line 352, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker .ui-widget-header {
  background: none !important;
  border: 0;
}
/* line 356, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker .ui-widget-header .ui-datepicker-title {
  color: #646464 !important;
  font-family: Verdana, Arial, sans-serif;
  font-weight: 300;
}

/* line 365, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker th {
  font-weight: 300;
  font-size: 14px;
}
/* line 371, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker .ui-state-default, body .ui-datepicker .ui-widget-content .ui-state-default, body .ui-datepicker .ui-widget-header .ui-state-default {
  background: transparent !important;
  border: 0 !important;
  color: #646464 !important;
  font-weight: 400;
  font-family: Circular, "Helvetica Neue", Helvetica, Arial, sans-serif;
  text-align: center;
  font-size: 13px;
}
/* line 382, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content .ui-state-hover {
  border: 0;
  background: #4B4B4B !important;
  color: white !important;
}
/* line 388, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content {
  border: 0;
  border-radius: 0;
  padding-bottom: 40px;
}
/* line 394, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-state-default {
  padding: 8px;
}
/* line 398, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-start_date {
  opacity: 1 !important;
}
/* line 401, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-start_date .ui-state-default {
  border: 0;
  background: #52ADCA !important;
  color: white !important;
}
/* line 408, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-highlighted .ui-state-default {
  border: 0;
  background: rgba(40, 96, 144, 0.25) !important;
}
/* line 413, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content .ui-state-active {
  border: 0;
  background: #52ADCA !important;
  color: white !important;
}
/* line 419, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-e, body span.ui-icon.ui-icon-circle-triangle-w {
  /* background: url(/static_1/images/mobile_img/renovation/flecha_der.png) no-repeat center;*/
  background: none;
  text-indent: 0;
  color: transparent;
  font-size: 0;
}
/* line 425, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-e:before, body span.ui-icon.ui-icon-circle-triangle-w:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  content: '\f105';
  font-family: "FontAwesome", sans-serif;
  font-size: 20px;
  color: black;
}
/* line 442, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-w:before {
  content: '\f104';
}
/* line 447, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker .ui-datepicker-next, body .ui-datepicker .ui-datepicker-prev {
  background: none;
}
/* line 451, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-next.ui-state-hover, body .ui-datepicker-prev.ui-datepicker-prev-hover {
  border: 1px solid #636363;
}

/* line 457, ../../../../sass/booking/_booking_engine_5.scss */
.datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-e:before, .datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-w:before {
  display: none;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 487, ../../../../sass/booking/_booking_engine_5.scss */
.fancy-booking-search_v2 {
  border-radius: 0;
}
/* line 490, ../../../../sass/booking/_booking_engine_5.scss */
.fancy-booking-search_v2 .fancybox-skin {
  border-radius: 0;
}
/* line 494, ../../../../sass/booking/_booking_engine_5.scss */
.fancy-booking-search_v2 .gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}
/* line 500, ../../../../sass/booking/_booking_engine_5.scss */
.fancy-booking-search_v2 .gif_wrapper .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
/* line 513, ../../../../sass/booking/_booking_engine_5.scss */
.fancy-booking-search_v2 .description_top_popup_booking {
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: lighter;
  color: gray;
}
/* line 521, ../../../../sass/booking/_booking_engine_5.scss */
.fancy-booking-search_v2 .description_bottom_popup_booking {
  font-weight: bold;
  text-transform: uppercase;
  color: gray;
}
/* line 527, ../../../../sass/booking/_booking_engine_5.scss */
.fancy-booking-search_v2 .container_popup_booking {
  width: 555px;
  display: block;
  padding: 30px 0;
  box-sizing: border-box;
  margin: 7px;
  border: 1px solid #3483b2;
}

/* line 537, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine {
  width: auto !important;
  height: auto !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}
/* line 545, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-outer {
  background: transparent;
}
/* line 549, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-skin {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}
/* line 558, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-inner {
  overflow: visible;
}
/* line 562, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 575, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  border: 0 !important;
}
/* line 579, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: lighter;
}
/* line 582, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking strong {
  font-weight: bold;
  text-decoration: underline;
  font-size: 14px;
}

/*=====================================================================
  Selectric
======================================================================*/
/* line 5, ../../../../sass/booking/_selectric.scss */
.selectricWrapper {
  position: relative;
  margin: 0 0 0px;
  width: 80px;
  cursor: pointer;
}

/* line 12, ../../../../sass/booking/_selectric.scss */
.selectricDisabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 23, ../../../../sass/booking/_selectric.scss */
.selectricOpen {
  z-index: 9999;
}

/* line 27, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 31, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

/* line 38, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect select {
  position: absolute;
  left: -100%;
  display: none;
}

/* line 44, ../../../../sass/booking/_selectric.scss */
.selectricInput {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  _font: 0/0 a;
  background: none !important;
}

/* line 60, ../../../../sass/booking/_selectric.scss */
.selectricTempShow {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* line 67, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectric {
  z-index: 9999;
}

/* line 72, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 76, ../../../../sass/booking/_selectric.scss */
.selectric {
  background: white;
  position: relative;
  border-radius: 6px;
  height: 40px;
}

/* line 83, ../../../../sass/booking/_selectric.scss */
.selectric .label {
  display: block;
  overflow: hidden;
  font-size: 16px;
  line-height: 40px;
  color: #52ADCA;
  text-align: left;
  margin-left: 20px;
}

/* line 93, ../../../../sass/booking/_selectric.scss */
.selectric .button {
  zoom: 1;
  position: absolute;
  font: 0/0 a;
  overflow: hidden;
  margin: auto;
  top: 0;
  right: 2px;
  bottom: 0;
  width: 35px;
  height: 35px;
  border: 0;
  background: #52ADCA url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  padding: 0;
}

/* line 109, ../../../../sass/booking/_selectric.scss */
.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */
/* line 115, ../../../../sass/booking/_selectric.scss */
.selectricItems {
  display: none;
  position: absolute;
  overflow: hidden;
  top: 100%;
  left: 0;
  background: #F9F9F9;
  border: 1px solid #CCC;
  z-index: 9998;
  -webkit-box-shadow: 0 0 10px -6px;
  box-shadow: 0 0 10px -6px;
}

/* line 128, ../../../../sass/booking/_selectric.scss */
.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* line 138, ../../../../sass/booking/_selectric.scss */
.selectricItems li {
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 146, ../../../../sass/booking/_selectric.scss */
.selectricItems li:hover {
  background: #F0F0F0;
  color: #444;
}
/* line 151, ../../../../sass/booking/_selectric.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}

/* line 158, ../../../../sass/booking/_selectric.scss */
.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;
  cursor: default;
}

/* line 1, ../sass/_booking_engine.scss */
#full_wrapper_booking {
  /*======== Booking Widget =======*/
}
/* line 3, ../sass/_booking_engine.scss */
#full_wrapper_booking div#wrapper_booking {
  position: absolute;
  height: 70px;
  bottom: 100px;
  left: 0px;
  right: 0px;
  z-index: 1000;
  width: 1022px;
  padding: 10px;
  background: #324F55;
}
/* line 14, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget {
  position: absolute;
  left: 0px;
}
/* line 19, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 {
  width: 1022px;
}
/* line 23, ../sass/_booking_engine.scss */
#full_wrapper_booking .departure_date_wrapper {
  border-right: 0;
}
/* line 27, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 form.booking_form {
  background: white;
}
/* line 31, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 .booking_form_title {
  background: #324F55;
  color: white;
  padding: 16px 30px;
  font-size: 18px;
  display: none;
}
/* line 39, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form_title .best_price {
  display: none;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}
/* line 47, ../sass/_booking_engine.scss */
#full_wrapper_booking .promocode_header p.first_offer_name {
  color: white;
}
/* line 50, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .date_box, #full_wrapper_booking .booking_widget .selectricWrapper, #full_wrapper_booking #booking_widget_popup .date_box, #full_wrapper_booking #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 54, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .date_box .date_day, #full_wrapper_booking #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 58, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectric {
  height: 38px;
  background: transparent;
}
/* line 63, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .adults_selector, #full_wrapper_booking .room_list_wrapper .children_selector, #full_wrapper_booking .room_list_wrapper .babies_selector {
  width: 32.1% !important;
  height: 69px;
}
/* line 67, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .children_selector {
  float: none;
  border-right: 1px solid lightgrey;
  padding-right: 0;
  padding-bottom: 4px;
  position: relative;
}
/* line 74, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .children_selector .range-age {
  font-size: 8px;
  position: absolute;
  left: 10px;
  top: 19px;
}
/* line 82, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .babies_selector {
  position: relative;
}
/* line 85, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .babies_selector .range-age {
  font-size: 8px;
  position: absolute;
  left: 10px;
  top: 10px;
}
/* line 93, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .babies_selector {
  display: inline-block;
  box-sizing: border-box;
  padding-left: 10px;
}
/* line 97, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .babies_selector label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}
/* line 104, ../sass/_booking_engine.scss */
#full_wrapper_booking button.submit_button {
  background: #6AB2D8 !important;
  color: white !important;
}
/* line 109, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .web_support_label_1, #full_wrapper_booking .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 114, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support .web_support_number, #full_wrapper_booking .web_support_label_1 {
  line-height: 15px !important;
}
/* line 118, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title {
  background: gray !important;
}
/* line 126, ../sass/_booking_engine.scss */
#full_wrapper_booking #data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}
/* line 130, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date {
  margin-top: 6px;
  background: none;
}
/* line 135, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.departure_date {
  background: none;
}
/* line 139, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectricWrapper .selectric {
  margin-top: 0px;
}
/* line 143, ../sass/_booking_engine.scss */
#full_wrapper_booking #slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}
/* line 147, ../sass/_booking_engine.scss */
#full_wrapper_booking .promocode_text {
  display: none;
}
/* line 151, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection {
  display: inline-block;
  vertical-align: top;
  float: left;
}
/* line 156, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection .entry_date_wrapper, #full_wrapper_booking .stay_selection .departure_date_wrapper {
  display: inline-block;
  vertical-align: top;
  width: 160px;
  float: left;
  border-top: 1px solid lightgrey;
  padding: 8px 10px;
}
/* line 165, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection .nights_number_wrapper {
  display: inline-block;
  width: 95px;
  float: left;
  vertical-align: top;
  border-top: 1px solid lightgrey;
  padding: 8px 10px;
}
/* line 175, ../sass/_booking_engine.scss */
#full_wrapper_booking .rooms_number_wrapper {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 95px;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
}
/* line 185, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper {
  width: 293px;
  display: inline-block;
  vertical-align: top;
  float: left;
  border-bottom: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
  height: 68px;
}
/* line 194, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list {
  background: white;
}
/* line 197, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room {
  height: 69px;
}
/* line 200, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room_list .room.room2 {
  border-bottom: 1px solid lightgrey;
}
/* line 207, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button {
  display: inline-block;
  width: auto;
  float: left;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  height: 69px;
}
/* line 215, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .promocode_wrapper {
  display: inline-block;
  width: 161px;
  vertical-align: top;
  float: left;
  height: 70px;
}
/* line 223, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .submit_button {
  width: 151px;
  display: inline-block;
  vertical-align: top;
  float: left;
  height: 70px;
  border: 1px solid lightgrey;
  border-right: 0;
}
/* line 232, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .submit_button:hover {
  opacity: .8;
}

/* line 239, ../sass/_booking_engine.scss */
.fancybox-wrap {
  /*======== Booking Widget =======*/
}
/* line 241, ../sass/_booking_engine.scss */
.fancybox-wrap .fancybox-outer {
  padding: 0 !important;
}
/* line 245, ../sass/_booking_engine.scss */
.fancybox-wrap .fancybox-inner {
  width: auto !important;
}
/* line 250, ../sass/_booking_engine.scss */
.fancybox-wrap div#wrapper_booking {
  position: absolute;
  height: 420px;
  top: 145px;
  left: 0px;
  right: 0px;
  z-index: 35;
}
/* line 258, ../sass/_booking_engine.scss */
.fancybox-wrap .booking_widget {
  position: absolute;
  left: 0px;
}
/* line 263, ../sass/_booking_engine.scss */
.fancybox-wrap #full-booking-engine-html-5 {
  width: 299px;
}
/* line 267, ../sass/_booking_engine.scss */
.fancybox-wrap #full-booking-engine-html-5 form.booking_form {
  background: white;
}
/* line 271, ../sass/_booking_engine.scss */
.fancybox-wrap .booking_form_title .best_price {
  display: none;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}
/* line 279, ../sass/_booking_engine.scss */
.fancybox-wrap .promocode_header p.first_offer_name {
  color: white;
}
/* line 282, ../sass/_booking_engine.scss */
.fancybox-wrap .booking_widget .date_box, .fancybox-wrap .booking_widget .selectricWrapper, .fancybox-wrap #booking_widget_popup .date_box, .fancybox-wrap #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 286, ../sass/_booking_engine.scss */
.fancybox-wrap .booking_widget .date_box .date_day, .fancybox-wrap #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 290, ../sass/_booking_engine.scss */
.fancybox-wrap .selectric {
  height: 38px;
  background: transparent;
}
/* line 294, ../sass/_booking_engine.scss */
.fancybox-wrap .selectricWrapper {
  width: 62px !important;
}
/* line 298, ../sass/_booking_engine.scss */
.fancybox-wrap .room_list_wrapper .adults_selector, .fancybox-wrap .room_list_wrapper .children_selector, .fancybox-wrap .room_list_wrapper .babies_selector {
  width: 32.2% !important;
  height: 69px;
}
/* line 302, ../sass/_booking_engine.scss */
.fancybox-wrap .room_list_wrapper .children_selector {
  float: none;
  border-right: 1px solid lightgrey;
  padding-right: 0;
  padding-bottom: 4px;
}
/* line 308, ../sass/_booking_engine.scss */
.fancybox-wrap .room_list_wrapper .babies_selector {
  display: inline-block;
  box-sizing: border-box;
  padding-left: 10px;
}
/* line 312, ../sass/_booking_engine.scss */
.fancybox-wrap .room_list_wrapper .babies_selector label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}
/* line 318, ../sass/_booking_engine.scss */
.fancybox-wrap .rooms_number_wrapper {
  width: 29.3%;
}
/* line 321, ../sass/_booking_engine.scss */
.fancybox-wrap .room_list_wrapper {
  width: 70.6%;
}
/* line 325, ../sass/_booking_engine.scss */
.fancybox-wrap button.submit_button {
  background: #FCD430 !important;
}
/* line 330, ../sass/_booking_engine.scss */
.fancybox-wrap .booking_widget .web_support_label_1, .fancybox-wrap .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 335, ../sass/_booking_engine.scss */
.fancybox-wrap .wrapper-new-web-support .web_support_number, .fancybox-wrap .web_support_label_1 {
  line-height: 15px !important;
}
/* line 339, ../sass/_booking_engine.scss */
.fancybox-wrap .wrapper-new-web-support.booking_form_title {
  background: gray !important;
}
/* line 343, ../sass/_booking_engine.scss */
.fancybox-wrap #full-booking-engine-html-5 {
  margin-top: 20px !important;
}
/* line 347, ../sass/_booking_engine.scss */
.fancybox-wrap #data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}
/* line 351, ../sass/_booking_engine.scss */
.fancybox-wrap .date_box.entry_date {
  margin-top: 6px;
}
/* line 354, ../sass/_booking_engine.scss */
.fancybox-wrap .selectricWrapper .selectric {
  margin-top: 0px;
}
/* line 358, ../sass/_booking_engine.scss */
.fancybox-wrap #slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}
/* line 362, ../sass/_booking_engine.scss */
.fancybox-wrap .promocode_text {
  display: none;
}

/* line 368, ../sass/_booking_engine.scss */
#data .destination_wrapper, #dataBono .destination_wrapper {
  width: 100%;
  cursor: pointer;
  box-sizing: border-box;
}
/* line 373, ../sass/_booking_engine.scss */
#data .destination_wrapper input, #dataBono .destination_wrapper input {
  padding-left: 8px;
  color: #4b4b4b;
}
/* line 378, ../sass/_booking_engine.scss */
#data .hotel_selector, #dataBono .hotel_selector {
  display: none;
  position: absolute;
  top: 70px;
  background: white;
  z-index: 22;
  width: 100%;
  box-shadow: 0 4px 8px black;
  height: 123px;
}
/* line 389, ../sass/_booking_engine.scss */
#data .date_year, #dataBono .date_year {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 25px;
  font-weight: 600;
  display: none;
}
/* line 397, ../sass/_booking_engine.scss */
#data .room_list .room.room3, #dataBono .room_list .room.room3 {
  border-top: 1px solid lightgrey;
  border-bottom: 0;
}

/* line 403, ../sass/_booking_engine.scss */
.fancybox-inner {
  overflow: visible !important;
}

/* line 408, ../sass/_booking_engine.scss */
.room_list .room.room2, .room_list .room.room3 {
  border-right: 1px solid lightgrey;
}
/* line 412, ../sass/_booking_engine.scss */
.room_list .room.room3 {
  border-bottom: 1px solid lightgrey;
  border-top: 0;
}

/* line 418, ../sass/_booking_engine.scss */
.selectricItems {
  overflow: auto !important;
}

/* line 1, ../sass/_booking_widget_modal.scss */
.fancybox-wrap {
  /*======== Booking Widget =======*/
}
/* line 3, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .fancybox-outer {
  padding: 0 !important;
}
/* line 7, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .fancybox-inner {
  width: auto !important;
}
/* line 12, ../sass/_booking_widget_modal.scss */
.fancybox-wrap div#wrapper_booking {
  position: absolute;
  height: 420px;
  top: 145px;
  left: 0px;
  right: 0px;
  z-index: 35;
}
/* line 20, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget {
  position: absolute;
  left: 0px;
}
/* line 25, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_title_custom {
  text-align: center;
  color: white;
  padding: 20px 0;
}
/* line 31, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header {
  text-align: center;
}
/* line 35, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.first_offer_name {
  display: none;
}
/* line 39, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.second_offer_name {
  display: none;
}
/* line 43, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.second_offer_name_popup {
  font-size: 10px;
  margin-top: 3px;
  color: gray;
}
/* line 49, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.first_offer_name_popup {
  font-size: 16px;
  text-transform: uppercase;
}
/* line 54, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header img.booking_header_discount {
  display: none;
}
/* line 58, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .date_box.entry_date, .fancybox-wrap .date_box.departure_date {
  background: none;
}
/* line 61, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .date_box.entry_date .date_year, .fancybox-wrap .date_box.departure_date .date_year {
  display: none;
}
/* line 66, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 {
  width: 305px;
}
/* line 70, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 form.booking_form {
  background: white;
}
/* line 74, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_form_title .best_price {
  display: block;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}
/* line 82, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.first_offer_name_popup {
  color: white;
}
/* line 85, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .date_box, .fancybox-wrap .booking_widget .selectricWrapper, .fancybox-wrap #booking_widget_popup .date_box, .fancybox-wrap #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 89, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .date_box .date_day, .fancybox-wrap #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 93, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .selectric {
  height: 38px;
  background: transparent;
}
/* line 98, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .room_list_wrapper .adults_selector, .fancybox-wrap .room_list_wrapper .children_selector {
  width: 49.8%;
}
/* line 102, ../sass/_booking_widget_modal.scss */
.fancybox-wrap button.submit_button {
  background: #52ADCA !important;
  color: white !important;
}
/* line 107, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .web_support_label_1, .fancybox-wrap .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 112, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .wrapper-new-web-support .web_support_number, .fancybox-wrap .web_support_label_1 {
  line-height: 15px !important;
}
/* line 116, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .wrapper-new-web-support.booking_form_title {
  background: gray !important;
}
/* line 120, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 {
  margin-top: 20px !important;
}
/* line 124, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}
/* line 128, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .date_box.entry_date {
  margin-top: 6px;
}
/* line 131, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .selectricWrapper .selectric {
  margin-top: 0px;
}
/* line 135, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}
/* line 139, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_text {
  display: none;
}

/* line 144, ../sass/_booking_widget_modal.scss */
#dataBono {
  width: 300px;
}
/* line 147, ../sass/_booking_widget_modal.scss */
#dataBono #custom_bonoregalo_title_widget {
  background: #383838;
}
/* line 151, ../sass/_booking_widget_modal.scss */
#dataBono .rooms_number_wrapper {
  width: 50%;
}
/* line 154, ../sass/_booking_widget_modal.scss */
#dataBono .rooms_number_wrapper .rooms_number {
  width: 100% !important;
}
/* line 158, ../sass/_booking_widget_modal.scss */
#dataBono .rooms_number_wrapper .plus_night {
  display: inline-block;
  float: right;
  margin-top: 13px;
}
/* line 163, ../sass/_booking_widget_modal.scss */
#dataBono .rooms_number_wrapper .plus_night:before {
  color: #52ADCA;
}
/* line 170, ../sass/_booking_widget_modal.scss */
#dataBono .rooms_number_wrapper .less_night {
  display: inline-block;
  float: left;
  margin-top: 13px;
}
/* line 175, ../sass/_booking_widget_modal.scss */
#dataBono .rooms_number_wrapper .less_night:before {
  color: #52ADCA;
}
/* line 182, ../sass/_booking_widget_modal.scss */
#dataBono .rooms_number_wrapper .promocode_input {
  background: transparent;
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 28px;
  margin-left: 42px;
  font-weight: 600;
  display: inline-block;
  width: 40px;
  margin-top: 5px;
}
/* line 195, ../sass/_booking_widget_modal.scss */
#dataBono .room_list_wrapper {
  width: 100%;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/
/* line 5, ../sass/_template_specific.scss */
.ui-state-default {
  border: 1px solid white !important;
}

/* line 9, ../sass/_template_specific.scss */
.ui-datepicker-title {
  color: white !important;
}

/* line 13, ../sass/_template_specific.scss */
.ui-widget-header {
  background: #52ADCA !important;
}

/* line 17, ../sass/_template_specific.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #52ADCA !important;
  color: white;
}

/* line 22, ../sass/_template_specific.scss */
.highlight {
  color: #52ADCA;
}

/* line 26, ../sass/_template_specific.scss */
body {
  font-family: "Source Sans Pro";
}
/* line 29, ../sass/_template_specific.scss */
body .ui-dialog {
  padding-bottom: 0;
}
/* line 32, ../sass/_template_specific.scss */
body .ui-dialog .ui-button-text {
  font-weight: lighter;
  font-size: 16px;
}
/* line 38, ../sass/_template_specific.scss */
body a {
  text-decoration: none;
}
/* line 42, ../sass/_template_specific.scss */
body img {
  vertical-align: bottom;
}
/* line 46, ../sass/_template_specific.scss */
body strong {
  font-weight: bold;
}
/* line 51, ../sass/_template_specific.scss */
body.interior #slider_container {
  height: 200px;
  background: #324F55;
}
/* line 55, ../sass/_template_specific.scss */
body.interior #slider_container #wrapper_booking {
  bottom: 0;
  padding: 30px 0;
}

/* line 63, ../sass/_template_specific.scss */
#content {
  overflow: hidden;
}

/*=== Header ===*/
/* line 68, ../sass/_template_specific.scss */
header {
  position: relative;
}
/* line 71, ../sass/_template_specific.scss */
header #wrapper-header {
  background: white;
  position: relative;
  padding: 10px 0;
}
/* line 76, ../sass/_template_specific.scss */
header #wrapper-header #logoDiv {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  top: 0;
  box-shadow: 1px 1px 1px gray;
  background: white;
  text-align: center;
  z-index: 2000;
  height: 130px;
  width: auto;
}
/* line 86, ../sass/_template_specific.scss */
header #wrapper-header #logoDiv img {
  height: 130px;
}
/* line 91, ../sass/_template_specific.scss */
header #wrapper-header .left_header {
  float: left;
  font-size: 13px;
  font-weight: bold;
}
/* line 96, ../sass/_template_specific.scss */
header #wrapper-header .left_header .phone_wrapper {
  display: inline-block;
  vertical-align: middle;
  border-right: 2px solid #d3d3d3;
  padding-right: 10px;
  margin-right: 10px;
}
/* line 103, ../sass/_template_specific.scss */
header #wrapper-header .left_header .phone_wrapper img, header #wrapper-header .left_header .phone_wrapper span {
  display: inline-block;
  vertical-align: middle;
}
/* line 108, ../sass/_template_specific.scss */
header #wrapper-header .left_header .phone_wrapper img {
  margin-right: 5px;
}
/* line 113, ../sass/_template_specific.scss */
header #wrapper-header .left_header .email_header_wrapper {
  display: inline-block;
  vertical-align: middle;
}
/* line 117, ../sass/_template_specific.scss */
header #wrapper-header .left_header .email_header_wrapper a {
  text-decoration: none;
  color: black;
}
/* line 121, ../sass/_template_specific.scss */
header #wrapper-header .left_header .email_header_wrapper a:hover {
  opacity: 0.8;
}
/* line 126, ../sass/_template_specific.scss */
header #wrapper-header .left_header .email_header_wrapper img, header #wrapper-header .left_header .email_header_wrapper span {
  display: inline-block;
  vertical-align: middle;
}
/* line 131, ../sass/_template_specific.scss */
header #wrapper-header .left_header .email_header_wrapper img {
  margin-right: 5px;
}
/* line 137, ../sass/_template_specific.scss */
header #wrapper-header .right_header {
  float: right;
}
/* line 140, ../sass/_template_specific.scss */
header #wrapper-header .right_header #lang {
  display: inline-block;
  float: right;
  margin-left: 20px;
  margin-top: 2px;
}
/* line 146, ../sass/_template_specific.scss */
header #wrapper-header .right_header #lang a {
  text-decoration: none;
  display: inline-block;
  cursor: pointer;
  vertical-align: middle;
}
/* line 152, ../sass/_template_specific.scss */
header #wrapper-header .right_header #lang a img {
  display: block;
  width: 22px;
}
/* line 159, ../sass/_template_specific.scss */
header #wrapper-header .right_header .social_wrapper {
  display: inline-block;
  vertical-align: middle;
  border-right: 2px solid #d3d3d3;
  margin-right: 10px;
  padding-right: 10px;
}
/* line 166, ../sass/_template_specific.scss */
header #wrapper-header .right_header .social_wrapper a {
  display: inline-block;
  vertical-align: middle;
  margin-right: 5px;
  float: left;
  position: relative;
  background: #324F55;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
}
/* line 178, ../sass/_template_specific.scss */
header #wrapper-header .right_header .social_wrapper a:last-child {
  margin-right: 0;
}
/* line 182, ../sass/_template_specific.scss */
header #wrapper-header .right_header .social_wrapper a .fa, header #wrapper-header .right_header .social_wrapper a #dataBono .rooms_number_wrapper .plus_night:before, #dataBono .rooms_number_wrapper header #wrapper-header .right_header .social_wrapper a .plus_night:before, header #wrapper-header .right_header .social_wrapper a #dataBono .rooms_number_wrapper .less_night:before, #dataBono .rooms_number_wrapper header #wrapper-header .right_header .social_wrapper a .less_night:before {
  font-size: 12px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 189, ../sass/_template_specific.scss */
header #wrapper-header .right_header .top_sections {
  display: inline-block;
  vertical-align: middle;
}
/* line 193, ../sass/_template_specific.scss */
header #wrapper-header .right_header .top_sections a {
  color: black;
  font-size: 13px;
  font-weight: bold;
  border-right: 2px solid #d3d3d3;
  margin-right: 10px;
  padding-right: 10px;
}
/* line 201, ../sass/_template_specific.scss */
header #wrapper-header .right_header .top_sections a img {
  margin-right: 5px;
}
/* line 205, ../sass/_template_specific.scss */
header #wrapper-header .right_header .top_sections a:last-child {
  border-right: 0;
  margin-right: 0px;
  padding-right: 0px;
}
/* line 211, ../sass/_template_specific.scss */
header #wrapper-header .right_header .top_sections a:hover {
  text-decoration: underline;
}
/* line 219, ../sass/_template_specific.scss */
header #main_menu {
  background: rgba(226, 235, 242, 0.9);
  padding: 25px 0;
  position: absolute;
  top: 100%;
  width: 100%;
  z-index: 21;
}
/* line 227, ../sass/_template_specific.scss */
header #main_menu .menu_left {
  float: left;
  width: 410px;
  text-align: justify;
  height: 20px;
}
/* line 233, ../sass/_template_specific.scss */
header #main_menu .menu_left:after {
  content: " ";
  display: inline-block;
  margin-left: 100%;
}
/* line 240, ../sass/_template_specific.scss */
header #main_menu .menu_right {
  float: right;
  width: 387px;
  text-align: justify;
  height: 20px;
}
/* line 246, ../sass/_template_specific.scss */
header #main_menu .menu_right:after {
  content: " ";
  display: inline-block;
  margin-left: 100%;
}
/* line 253, ../sass/_template_specific.scss */
header #main_menu a {
  color: gray;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 14px;
  display: inline-block;
  font-family: "Montserrat";
}
/* line 261, ../sass/_template_specific.scss */
header #main_menu a:hover, header #main_menu a#section-active {
  opacity: .6;
}

/*=== Banners x6 ===*/
/* line 269, ../sass/_template_specific.scss */
.gallery_home_wrapper {
  display: inline-block;
  width: 100%;
}
/* line 273, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element {
  width: calc(99.99% / 3);
  float: left;
  height: 315px;
  position: relative;
}
/* line 279, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n) {
  height: 550px;
}
/* line 283, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n):hover .gallery_content {
  height: 500px !important;
}
/* line 286, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n):hover .gallery_content .gallery_description, .gallery_home_wrapper .gallery_element:nth-child(2n):hover .gallery_content .gallery_link {
  opacity: 1;
  display: inline-block;
}
/* line 293, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n) .gallery_content {
  position: absolute;
  bottom: 0;
  width: 100%;
  color: white;
  box-sizing: border-box;
  padding: 30px;
  background: -webkit-linear-gradient(transparent, black);
  background: -o-linear-gradient(transparent, black);
  background: -moz-linear-gradient(transparent, black);
  background: linear-gradient(rgba(0, 0, 0, 0), #000000);
  -webkit-transition: height .7s;
  -moz-transition: height .7s;
  -ms-transition: height .7s;
  -o-transition: height .7s;
  transition: height .7s;
}
/* line 310, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n) .gallery_content .gallery_title {
  font-size: 28px;
  font-family: "Montserrat";
}
/* line 315, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n) .gallery_content .gallery_description {
  font-size: 18px;
  font-weight: lighter;
  margin-top: 10px;
  opacity: 0;
  -webkit-transition: opacity .7s;
  -moz-transition: opacity .7s;
  -ms-transition: opacity .7s;
  -o-transition: opacity .7s;
  transition: opacity .7s;
  display: none;
  width: 100%;
}
/* line 329, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n) .gallery_content .gallery_link {
  display: inline-block;
  margin-top: 25px;
  opacity: 0;
  -webkit-transition: opacity .7s;
  -moz-transition: opacity .7s;
  -ms-transition: opacity .7s;
  -o-transition: opacity .7s;
  transition: opacity .7s;
  display: none;
}
/* line 340, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n) .gallery_content .gallery_link a {
  color: white;
  border: 1px solid white;
  padding: 10px 20px;
  display: inline-block;
  -webkit-transition: background .7s;
  -moz-transition: background .7s;
  -ms-transition: background .7s;
  -o-transition: background .7s;
  transition: background .7s;
}
/* line 351, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n) .gallery_content .gallery_link a:hover {
  background: #52ADCA;
}
/* line 361, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n-1):hover .gallery_content .center_block {
  opacity: 1;
}
/* line 365, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n-1):hover .overlay_block {
  opacity: 1;
}
/* line 371, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n-1) .gallery_content .center_block {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  text-align: center;
  width: 100%;
  color: white;
  opacity: 0;
  -webkit-transition: opacity .7s;
  -moz-transition: opacity .7s;
  -ms-transition: opacity .7s;
  -o-transition: opacity .7s;
  transition: opacity .7s;
}
/* line 383, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n-1) .gallery_content .center_block .gallery_title {
  font-size: 24px;
}
/* line 387, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n-1) .gallery_content .center_block .gallery_description {
  margin: 10px auto 0;
  width: 70%;
}
/* line 392, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n-1) .gallery_content .center_block .gallery_link {
  display: inline-block;
  margin-top: 10px;
}
/* line 396, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n-1) .gallery_content .center_block .gallery_link a {
  color: white;
  border: 1px solid white;
  padding: 10px 20px;
  display: inline-block;
  -webkit-transition: background .7s;
  -moz-transition: background .7s;
  -ms-transition: background .7s;
  -o-transition: background .7s;
  transition: background .7s;
}
/* line 407, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(2n-1) .gallery_content .center_block .gallery_link a:hover {
  background: #52ADCA;
}
/* line 416, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(4) {
  clear: left;
}
/* line 420, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element:nth-child(4), .gallery_home_wrapper .gallery_element:nth-child(6) {
  margin-top: -235px;
}
/* line 424, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element .overlay_block {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  -webkit-transition: opacity .7s;
  -moz-transition: opacity .7s;
  -ms-transition: opacity .7s;
  -o-transition: opacity .7s;
  transition: opacity .7s;
}
/* line 438, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element .gallery_image {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}
/* line 444, ../sass/_template_specific.scss */
.gallery_home_wrapper .gallery_element .gallery_image img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  max-width: none;
  max-height: 100%;
  min-width: 100%;
  min-height: 100%;
}

/*=== Paralax ===*/
/* line 456, ../sass/_template_specific.scss */
.paralax_title {
  text-align: center;
  padding: 50px 0;
  color: #52ADCA;
  font-size: 32px;
  font-family: "Montserrat";
}

/* line 465, ../sass/_template_specific.scss */
.paralax_wrapper .paralax_content {
  padding: 50px 0;
  text-align: center;
}
/* line 469, ../sass/_template_specific.scss */
.paralax_wrapper .paralax_content .paralax_description {
  padding: 20px 50px;
  box-sizing: border-box;
  display: inline-block;
  width: 435px;
  background: white url(/img/douro/bgc_title.png?v=1) no-repeat center center;
}
/* line 476, ../sass/_template_specific.scss */
.paralax_wrapper .paralax_content .paralax_description .highlight {
  color: #52ADCA;
}
/* line 481, ../sass/_template_specific.scss */
.paralax_wrapper .paralax_content .paralax_link {
  display: inline-block;
}
/* line 483, ../sass/_template_specific.scss */
.paralax_wrapper .paralax_content .paralax_link a {
  color: white;
  background: transparent;
  padding: 15px 10px;
  display: inline-block;
  margin-left: -685px;
  border: 1px solid white;
  font-size: 24px;
  width: 230px;
  -webkit-transition: background .7s;
  -moz-transition: background .7s;
  -ms-transition: background .7s;
  -o-transition: background .7s;
  transition: background .7s;
}
/* line 498, ../sass/_template_specific.scss */
.paralax_wrapper .paralax_content .paralax_link a:hover {
  background: #52ADCA;
}

/*=== Footer ===*/
/* line 507, ../sass/_template_specific.scss */
footer {
  background: #324F55 url(/img/douro/bgc_footer.png?v=1) no-repeat center 45%;
  padding: 40px 0 0;
}
/* line 511, ../sass/_template_specific.scss */
footer .logo_footer {
  text-align: center;
  margin-bottom: 40px;
}
/* line 516, ../sass/_template_specific.scss */
footer .wrapper_footer_columns {
  padding-bottom: 40px;
}
/* line 520, ../sass/_template_specific.scss */
footer .footer_column {
  position: relative;
}
/* line 524, ../sass/_template_specific.scss */
footer .footer_column:nth-child(4) .footer_column_title {
  margin-left: 15px;
  position: relative;
  z-index: 1;
}
/* line 531, ../sass/_template_specific.scss */
footer .footer_column .footer_column_title {
  text-transform: uppercase;
  color: white;
  margin-bottom: 20px;
  font-family: "Montserrat";
}
/* line 538, ../sass/_template_specific.scss */
footer .footer_column .footer_column_description {
  color: white;
  font-size: 14px;
}
/* line 543, ../sass/_template_specific.scss */
footer .footer_column .logo_see_more img {
  position: absolute;
  top: 10px;
}
/* line 549, ../sass/_template_specific.scss */
footer .footer_column .newsletter_wrapper #title_newsletter, footer .footer_column .newsletter_wrapper #suscEmailLabel {
  display: none !important;
}
/* line 553, ../sass/_template_specific.scss */
footer .footer_column .newsletter_wrapper .newsletter_container {
  width: auto;
}
/* line 557, ../sass/_template_specific.scss */
footer .footer_column .newsletter_wrapper #suscEmail {
  border: 1px solid white;
  height: 55px;
  background: #5D7077;
  color: white;
  width: 260px;
  box-sizing: border-box;
  margin-bottom: 10px;
}
/* line 567, ../sass/_template_specific.scss */
footer .footer_column .newsletter_wrapper .button_newsletter {
  background: #5D7077;
  border: 1px solid white;
  color: white;
  width: 118px;
  height: 55px;
  padding: 15.5px 30px;
  box-sizing: border-box;
  font-size: 18px;
  text-transform: uppercase;
}
/* line 579, ../sass/_template_specific.scss */
footer .footer_column .newsletter_wrapper .newsletter_checkbox {
  margin-top: 3px;
  color: white;
  font-size: 12px;
}
/* line 584, ../sass/_template_specific.scss */
footer .footer_column .newsletter_wrapper .newsletter_checkbox a {
  text-decoration: underline;
  display: inline;
}
/* line 589, ../sass/_template_specific.scss */
footer .footer_column .newsletter_wrapper input#promotions {
  margin-bottom: 16px;
  float: left;
}
/* line 595, ../sass/_template_specific.scss */
footer .footer_column .social_wrapper {
  margin-top: 10px;
}
/* line 597, ../sass/_template_specific.scss */
footer .footer_column .social_wrapper a {
  display: inline-block;
  vertical-align: middle;
  width: auto;
}
/* line 602, ../sass/_template_specific.scss */
footer .footer_column .social_wrapper a img {
  display: inline-block;
  vertical-align: middle;
}
/* line 609, ../sass/_template_specific.scss */
footer .footer_column a {
  color: white;
  font-size: 14px;
  display: inline-block;
  width: 100%;
}
/* line 615, ../sass/_template_specific.scss */
footer .footer_column a:hover {
  text-decoration: underline;
}
/* line 621, ../sass/_template_specific.scss */
footer .full-copyright {
  text-align: center;
  background: #203337;
  padding: 20px 0;
  color: white;
}
/* line 627, ../sass/_template_specific.scss */
footer .full-copyright a {
  color: white;
  font-size: 14px;
  margin: 0 10px;
}
/* line 632, ../sass/_template_specific.scss */
footer .full-copyright a:hover {
  text-decoration: underline;
}
/* line 638, ../sass/_template_specific.scss */
footer .full-copyright #div-txt-copyright img {
  margin: 10px auto;
  display: block;
}

/*=== Slider ===*/
/* line 647, ../sass/_template_specific.scss */
#slider_container {
  position: relative;
}
/* line 650, ../sass/_template_specific.scss */
#slider_container .logo_slider_wrapper {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  top: 110px;
  width: 1140px;
  display: inline-block;
  z-index: 100;
}
/* line 657, ../sass/_template_specific.scss */
#slider_container .logo_slider_wrapper a {
  display: inline-block;
  position: absolute;
  right: 0;
}
/* line 662, ../sass/_template_specific.scss */
#slider_container .logo_slider_wrapper a img {
  vertical-align: middle;
}
/* line 669, ../sass/_template_specific.scss */
#slider_container .tp-bullets .bullet {
  background: white;
  border-radius: 50%;
  width: 15px;
  height: 15px;
  vertical-align: middle;
  float: none;
  display: inline-block;
}
/* line 678, ../sass/_template_specific.scss */
#slider_container .tp-bullets .bullet.selected {
  width: 20px;
  height: 20px;
}

/*=== Content Subtitle ===*/
/* line 687, ../sass/_template_specific.scss */
.content_subtitle_wrapper {
  display: inline-block;
  width: 100%;
  padding: 40px 0;
  text-align: center;
}
/* line 693, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_title {
  text-transform: uppercase;
  font-weight: bold;
  font-size: 24px;
  background: url(/img/douro/bgc_title.png?v=1) no-repeat center center;
  background-size: contain;
  display: inline-block;
  padding: 35px 20px;
  font-family: "Montserrat";
}
/* line 703, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_title span {
  display: block;
  font-weight: 300;
  color: gray;
}
/* line 710, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_description {
  font-size: 14px;
  color: grey;
  width: 725px;
  margin: 20px auto;
}
/* line 716, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_description .highlight {
  color: #52ADCA;
}

/*=== Booking Button ===*/
/* line 723, ../sass/_template_specific.scss */
.booking_button_wrapper {
  text-align: center;
}
/* line 726, ../sass/_template_specific.scss */
.booking_button_wrapper a {
  background: #52ADCA;
  padding: 20px 80px;
  color: white;
  text-transform: uppercase;
  display: inline-block;
}
/* line 733, ../sass/_template_specific.scss */
.booking_button_wrapper a:hover {
  opacity: .8;
}

/*=== Automatic Content ===*/
/* line 740, ../sass/_template_specific.scss */
.automatic_content_wrapper {
  display: inline-block;
  width: 100%;
  padding: 0px 0 40px;
  text-align: center;
}
/* line 746, ../sass/_template_specific.scss */
.automatic_content_wrapper .section-title {
  text-transform: uppercase;
  font-weight: bold;
  font-size: 24px;
  background: url(/img/douro/bgc_title.png?v=1) no-repeat center center;
  display: inline-block;
  padding: 35px 20px;
}
/* line 755, ../sass/_template_specific.scss */
.automatic_content_wrapper > div {
  font-size: 14px;
  color: grey;
  width: 725px;
  margin: 20px auto;
}
/* line 761, ../sass/_template_specific.scss */
.automatic_content_wrapper > div .highlight {
  color: #52ADCA;
}
/* line 765, ../sass/_template_specific.scss */
.automatic_content_wrapper > div.border-gallery {
  width: 100%;
}
/* line 770, ../sass/_template_specific.scss */
.automatic_content_wrapper .modify_reservation_widget {
  width: 475px;
  margin: 0 auto 20px;
  padding: 0;
  text-align: left;
}
/* line 776, ../sass/_template_specific.scss */
.automatic_content_wrapper .modify_reservation_widget .numero_personas {
  float: left;
}
/* line 781, ../sass/_template_specific.scss */
.automatic_content_wrapper .modify_reservation_widget #motor_reserva #contenedor_fechas {
  text-align: center;
}
/* line 784, ../sass/_template_specific.scss */
.automatic_content_wrapper .modify_reservation_widget #motor_reserva #contenedor_fechas .colocar_fechas {
  display: inline-block;
  float: none;
}
/* line 788, ../sass/_template_specific.scss */
.automatic_content_wrapper .modify_reservation_widget #motor_reserva #contenedor_fechas .colocar_fechas#contador_noches {
  display: none;
}
/* line 792, ../sass/_template_specific.scss */
.automatic_content_wrapper .modify_reservation_widget #motor_reserva #contenedor_fechas .colocar_fechas input {
  margin: auto;
}
/* line 798, ../sass/_template_specific.scss */
.automatic_content_wrapper .modify_reservation_widget #motor_reserva #contenedor_habitaciones {
  text-align: center;
}
/* line 801, ../sass/_template_specific.scss */
.automatic_content_wrapper .modify_reservation_widget #motor_reserva #contenedor_habitaciones label, .automatic_content_wrapper .modify_reservation_widget #motor_reserva #contenedor_habitaciones select {
  display: inline-block;
  float: none;
}
/* line 807, ../sass/_template_specific.scss */
.automatic_content_wrapper .modify_reservation_widget #motor_reserva #contenedor_opciones {
  margin: auto;
  margin-bottom: 10px;
}
/* line 812, ../sass/_template_specific.scss */
.automatic_content_wrapper .modify_reservation_widget #motor_reserva #envio {
  text-align: center;
}
/* line 818, ../sass/_template_specific.scss */
.automatic_content_wrapper .modify_reservation_widget #info_ninos {
  text-align: center;
  left: 155px;
}
/* line 823, ../sass/_template_specific.scss */
.automatic_content_wrapper #my-bookings-form-fields {
  display: table;
  margin: auto;
  margin-top: 20px;
}
/* line 828, ../sass/_template_specific.scss */
.automatic_content_wrapper #my-bookings-form-fields label#my-bookings-email-label, .automatic_content_wrapper #my-bookings-form-fields label#my-bookings-localizador-label {
  font-weight: lighter;
  margin-right: 15px;
  vertical-align: middle;
}
/* line 834, ../sass/_template_specific.scss */
.automatic_content_wrapper #my-bookings-form-fields #emailInput {
  padding: 10px;
  margin-right: 25px;
  width: 141px;
  vertical-align: middle;
}
/* line 841, ../sass/_template_specific.scss */
.automatic_content_wrapper #my-bookings-form-fields input#localizadorInput {
  padding: 10px;
  width: 141px;
  vertical-align: middle;
}
/* line 847, ../sass/_template_specific.scss */
.automatic_content_wrapper #my-bookings-form-fields > ul {
  margin-top: 20px;
  text-align: center;
}
/* line 851, ../sass/_template_specific.scss */
.automatic_content_wrapper #my-bookings-form-fields > ul > li {
  display: inline-block;
}
/* line 857, ../sass/_template_specific.scss */
.automatic_content_wrapper #my-bookings-form-fields .modify-reservation {
  background: #52ADCA;
  cursor: pointer;
}
/* line 861, ../sass/_template_specific.scss */
.automatic_content_wrapper #my-bookings-form-fields .modify-reservation:hover {
  opacity: .8;
}
/* line 866, ../sass/_template_specific.scss */
.automatic_content_wrapper #my-bookings-form-fields .cancelButton, .automatic_content_wrapper #my-bookings-form-fields .searchForReservation {
  background: black;
  cursor: pointer;
}
/* line 870, ../sass/_template_specific.scss */
.automatic_content_wrapper #my-bookings-form-fields .cancelButton:hover, .automatic_content_wrapper #my-bookings-form-fields .searchForReservation:hover {
  opacity: .8;
}
/* line 875, ../sass/_template_specific.scss */
.automatic_content_wrapper #my-bookings-form-fields .modify-reservation, .automatic_content_wrapper #my-bookings-form-fields .cancelButton, .automatic_content_wrapper #my-bookings-form-fields .searchForReservation {
  color: white;
  font-weight: 300;
  font-size: 15px;
  text-transform: uppercase;
  padding: 10px 20px;
  border: 0;
  font-family: 'Oswald', sans-serif;
}
/* line 886, ../sass/_template_specific.scss */
.automatic_content_wrapper #cancel-button-container {
  display: table;
  margin: auto;
  margin-top: 20px;
  margin-bottom: 20px;
}
/* line 892, ../sass/_template_specific.scss */
.automatic_content_wrapper #cancel-button-container button {
  color: white;
  font-weight: 300;
  font-size: 15px;
  text-transform: uppercase;
  padding: 10px 20px;
  border: 0;
  background: #324F55;
  display: none;
  font-family: 'Oswald', sans-serif;
  cursor: pointer;
}
/* line 904, ../sass/_template_specific.scss */
.automatic_content_wrapper #cancel-button-container button:hover {
  opacity: 0.8;
}
/* line 910, ../sass/_template_specific.scss */
.automatic_content_wrapper .my-bookings-booking-info {
  margin: auto !important;
  display: table;
}

/* line 917, ../sass/_template_specific.scss */
#reservation-cancellation-popup form > label {
  margin-bottom: 20px;
}
/* line 921, ../sass/_template_specific.scss */
#reservation-cancellation-popup #cancellation-reservation-buttons {
  display: table;
  margin: auto;
  margin-top: 10px;
}
/* line 926, ../sass/_template_specific.scss */
#reservation-cancellation-popup #cancellation-reservation-buttons button#cancellation-confirmation-button {
  color: white;
  font-weight: 300;
  font-size: 15px;
  text-transform: uppercase;
  padding: 10px 20px;
  border: 0;
  background: #324F55;
  font-family: 'Oswald', sans-serif;
  cursor: pointer;
}

/* line 940, ../sass/_template_specific.scss */
textarea#cancellation-reasons {
  width: 100%;
  box-sizing: border-box;
  margin-top: 10px;
}

/*=== Maps Iframe ===*/
/* line 947, ../sass/_template_specific.scss */
.maps_title {
  font-size: 24px;
  text-align: center;
  text-transform: uppercase;
  margin: 30px 0;
  font-family: "Montserrat";
}

/* line 955, ../sass/_template_specific.scss */
.maps_wrapper {
  height: 585px;
  position: relative;
}
/* line 959, ../sass/_template_specific.scss */
.maps_wrapper .buttons_wrapper {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1;
}
/* line 965, ../sass/_template_specific.scss */
.maps_wrapper .buttons_wrapper .search_input {
  display: inline-block;
  height: 90px;
  width: 400px;
  background: #324F55;
  border: 0;
  color: white;
  font-size: 18px;
  box-sizing: border-box;
  padding-left: 30px;
  vertical-align: middle;
}
/* line 977, ../sass/_template_specific.scss */
.maps_wrapper .buttons_wrapper .search_input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: white;
}
/* line 981, ../sass/_template_specific.scss */
.maps_wrapper .buttons_wrapper .search_input::-moz-placeholder {
  /* Firefox 19+ */
  color: white;
}
/* line 985, ../sass/_template_specific.scss */
.maps_wrapper .buttons_wrapper .search_input:-ms-input-placeholder {
  /* IE 10+ */
  color: white;
}
/* line 989, ../sass/_template_specific.scss */
.maps_wrapper .buttons_wrapper .search_input:-moz-placeholder {
  /* Firefox 18- */
  color: white;
}
/* line 995, ../sass/_template_specific.scss */
.maps_wrapper .buttons_wrapper .search_button_maps {
  display: inline-block;
  background: #52ADCA;
  color: white;
  font-size: 18px;
  text-transform: uppercase;
  padding: 33.5px 40px;
  vertical-align: middle;
  -webkit-transition: opacity .7s;
  -moz-transition: opacity .7s;
  -ms-transition: opacity .7s;
  -o-transition: opacity .7s;
  transition: opacity .7s;
}
/* line 1009, ../sass/_template_specific.scss */
.maps_wrapper .buttons_wrapper .search_button_maps:hover {
  opacity: .6;
}

/* line 1017, ../sass/_template_specific.scss */
.maps_content .overlay_black {
  background: rgba(0, 0, 0, 0.15);
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
}
/* line 1025, ../sass/_template_specific.scss */
.maps_content .maps_iframe {
  overflow: hidden;
}
/* line 1028, ../sass/_template_specific.scss */
.maps_content .maps_iframe iframe {
  width: 100%;
  height: 710px;
  margin-top: -125px;
}

/*=== Info BLocks ===*/
/* line 1037, ../sass/_template_specific.scss */
.filter_wrapper {
  text-align: justify;
  margin-bottom: 10px;
}
/* line 1040, ../sass/_template_specific.scss */
.filter_wrapper:after {
  content: '';
  margin-left: 100%;
  display: inline-block;
}
/* line 1045, ../sass/_template_specific.scss */
.filter_wrapper .showFilter {
  display: inline-block;
  padding: 10px 40px;
  font-size: 18px;
  text-transform: uppercase;
  background-color: #324F55;
  color: #52ADCA;
  clear: both;
  border-width: 0;
  box-sizing: border-box;
  text-align: center;
}
/* line 1056, ../sass/_template_specific.scss */
.filter_wrapper .showFilter.active {
  background-color: #52ADCA;
  color: white;
}
/* line 1061, ../sass/_template_specific.scss */
.filter_wrapper .showFilter:hover {
  background-color: #3794b2;
  color: white;
}
/* line 1065, ../sass/_template_specific.scss */
.filter_wrapper .showFilter:focus {
  outline: none;
}

/* line 1070, ../sass/_template_specific.scss */
.info_blocks_wrapper {
  background: #F6F4F5;
  display: inline-block;
  padding-top: 30px;
  width: 100%;
}
/* line 1076, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element {
  width: 855px;
  height: 450px;
  position: relative;
  margin: 0 auto 30px;
  overflow: hidden;
}
/* line 1083, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_image {
  float: left;
  width: 625px;
  height: 100%;
  position: relative;
  overflow: hidden;
}
/* line 1090, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_image .info_slider {
  height: 450px;
  position: relative;
  overflow: hidden;
}
/* line 1096, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_image > img, .info_blocks_wrapper .info_element .info_image .info_slider img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  max-height: 100%;
  max-width: none;
  min-width: 100%;
  min-height: 100%;
}
/* line 1104, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_image .flex-direction-nav {
  position: absolute;
  bottom: 20px;
  left: 20px;
  width: 100px;
}
/* line 1110, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_image .flex-direction-nav img {
  vertical-align: middle;
}
/* line 1114, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_image .flex-direction-nav .flex-prev {
  display: inline-block;
  float: left;
  vertical-align: middle;
}
/* line 1120, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_image .flex-direction-nav .flex-next {
  display: inline-block;
  float: right;
  vertical-align: middle;
}
/* line 1128, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_content {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 0;
  width: 365px;
  height: 395px;
  background: white;
}
/* line 1135, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_content .center_block {
  box-sizing: border-box;
  padding: 40px 30px;
  height: 100%;
  position: relative;
}
/* line 1141, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_content .center_block .info_title {
  text-transform: uppercase;
  margin-bottom: 30px;
  font-family: "Montserrat";
}
/* line 1146, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_content .center_block .info_title span {
  color: gray;
  display: block;
  font-size: 15px;
}
/* line 1153, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_content .center_block .info_description {
  font-size: 14px;
}
/* line 1156, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_content .center_block .info_description .highlight {
  color: #52ADCA;
}
/* line 1161, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_content .center_block .info_links {
  position: absolute;
  bottom: 20px;
  right: 20px;
}
/* line 1166, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_content .center_block .info_links a:hover {
  opacity: .8;
}
/* line 1170, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_content .center_block .info_links .info_link {
  display: inline-block;
}
/* line 1173, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_content .center_block .info_links .info_link a {
  text-transform: uppercase;
  color: #000000;
  font-weight: bold;
}
/* line 1180, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_content .center_block .info_links .data_popup {
  display: inline-block;
}
/* line 1183, ../sass/_template_specific.scss */
.info_blocks_wrapper .info_element .info_content .center_block .info_links .data_popup a {
  text-transform: uppercase;
  color: #52ADCA;
  font-weight: bold;
  margin-left: 10px;
}

/*=== Extra Data ===*/
/* line 1197, ../sass/_template_specific.scss */
.extra_data_wrapper {
  background: #F6F4F5;
  padding: 20px 0;
  text-align: center;
}
/* line 1202, ../sass/_template_specific.scss */
.extra_data_wrapper .extra_data_title {
  text-transform: uppercase;
  margin-bottom: 20px;
  font-family: "Montserrat";
}
/* line 1208, ../sass/_template_specific.scss */
.extra_data_wrapper .extra_element {
  display: inline-block;
  vertical-align: top;
}
/* line 1212, ../sass/_template_specific.scss */
.extra_data_wrapper .extra_element .extra_title {
  font-size: 36px;
  font-weight: 100;
  margin-bottom: 10px;
}
/* line 1218, ../sass/_template_specific.scss */
.extra_data_wrapper .extra_element .extra_description {
  color: gray;
  text-transform: uppercase;
  font-size: 14px;
}
/* line 1225, ../sass/_template_specific.scss */
.extra_data_wrapper .separator {
  display: inline-block;
  vertical-align: top;
  margin: 0 30px;
  background: #d3d3d3;
  width: 2px;
  height: 90px;
}

/*=== Minigallery ===*/
/* line 1236, ../sass/_template_specific.scss */
.minigallery_wrapper {
  display: inline-block;
  width: 100%;
  margin: 20px 0;
}
/* line 1241, ../sass/_template_specific.scss */
.minigallery_wrapper .minigallery_big {
  height: 450px;
  width: 635px;
  position: relative;
  float: left;
}
/* line 1247, ../sass/_template_specific.scss */
.minigallery_wrapper .minigallery_big .big_element {
  position: relative;
  overflow: hidden;
  height: 450px;
}
/* line 1252, ../sass/_template_specific.scss */
.minigallery_wrapper .minigallery_big .big_element img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  max-width: none;
  min-width: 100%;
  min-height: 100%;
  max-height: 100%;
}
/* line 1261, ../sass/_template_specific.scss */
.minigallery_wrapper .minigallery_big .flex-direction-nav {
  position: absolute;
  bottom: 20px;
  left: 20px;
  width: 100px;
}
/* line 1267, ../sass/_template_specific.scss */
.minigallery_wrapper .minigallery_big .flex-direction-nav .flex-prev {
  float: left;
}
/* line 1271, ../sass/_template_specific.scss */
.minigallery_wrapper .minigallery_big .flex-direction-nav .flex-next {
  float: right;
}
/* line 1276, ../sass/_template_specific.scss */
.minigallery_wrapper .minigallery_big .flex-control-nav {
  position: absolute;
  left: 102%;
  top: 0;
  width: 493px;
}
/* line 1282, ../sass/_template_specific.scss */
.minigallery_wrapper .minigallery_big .flex-control-nav li {
  display: inline-block;
  width: 49%;
  float: left;
  margin-bottom: 10px;
  height: 220px;
  overflow: hidden;
  position: relative;
}
/* line 1291, ../sass/_template_specific.scss */
.minigallery_wrapper .minigallery_big .flex-control-nav li:nth-child(2n) {
  float: right;
}
/* line 1295, ../sass/_template_specific.scss */
.minigallery_wrapper .minigallery_big .flex-control-nav li img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-height: 100%;
  min-width: 100%;
  max-width: none;
  max-height: 100%;
  box-sizing: border-box;
  -webkit-transition: padding .7s;
  -moz-transition: padding .7s;
  -ms-transition: padding .7s;
  -o-transition: padding .7s;
  transition: padding .7s;
  cursor: pointer;
}
/* line 1309, ../sass/_template_specific.scss */
.minigallery_wrapper .minigallery_big .flex-control-nav li img.flex-active {
  padding: 10px;
}

/*=== Dropdown $ List_blocks ===*/
/* line 1319, ../sass/_template_specific.scss */
.dropdown_wrapper, .list_blocks_wrapper {
  display: inline-block;
  width: 100%;
  margin: 40px 0;
}
/* line 1324, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_title, .dropdown_wrapper .list_blocks_title, .list_blocks_wrapper .dropdown_title, .list_blocks_wrapper .list_blocks_title {
  text-transform: uppercase;
  font-size: 22px;
  margin-bottom: 30px;
  color: #52ADCA;
  font-family: "Montserrat";
}
/* line 1332, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_subtitle, .dropdown_wrapper .list_blocks_subtitle, .list_blocks_wrapper .dropdown_subtitle, .list_blocks_wrapper .list_blocks_subtitle {
  color: gray;
  font-size: 14px;
  margin-bottom: 20px;
}
/* line 1339, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .dropdown_element, .dropdown_wrapper .list_blocks_content .dropdown_element, .list_blocks_wrapper .dropdown_content .dropdown_element, .list_blocks_wrapper .list_blocks_content .dropdown_element {
  display: inline-block;
  width: 100%;
  border-bottom: 1px solid gray;
  padding: 15px 0;
}
/* line 1345, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .dropdown_element:first-child, .dropdown_wrapper .list_blocks_content .dropdown_element:first-child, .list_blocks_wrapper .dropdown_content .dropdown_element:first-child, .list_blocks_wrapper .list_blocks_content .dropdown_element:first-child {
  border-top: 1px solid gray;
}
/* line 1349, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .dropdown_element .dropdown_name, .dropdown_wrapper .list_blocks_content .dropdown_element .dropdown_name, .list_blocks_wrapper .dropdown_content .dropdown_element .dropdown_name, .list_blocks_wrapper .list_blocks_content .dropdown_element .dropdown_name {
  float: left;
  text-transform: uppercase;
  font-size: 22px;
  vertical-align: middle;
  cursor: pointer;
  font-family: "Montserrat";
}
/* line 1358, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .dropdown_element .dropdown_description, .dropdown_wrapper .list_blocks_content .dropdown_element .dropdown_description, .list_blocks_wrapper .dropdown_content .dropdown_element .dropdown_description, .list_blocks_wrapper .list_blocks_content .dropdown_element .dropdown_description {
  clear: both;
  display: none;
  margin-top: 50px;
  width: 100%;
  color: gray;
  font-size: 14px;
}
/* line 1366, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .dropdown_element .dropdown_description .highlight_offer, .dropdown_wrapper .list_blocks_content .dropdown_element .dropdown_description .highlight_offer, .list_blocks_wrapper .dropdown_content .dropdown_element .dropdown_description .highlight_offer, .list_blocks_wrapper .list_blocks_content .dropdown_element .dropdown_description .highlight_offer {
  color: #324F55;
  font-size: 18px;
  font-weight: bold;
}
/* line 1373, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .dropdown_element .dropdown_image, .dropdown_wrapper .list_blocks_content .dropdown_element .dropdown_image, .list_blocks_wrapper .dropdown_content .dropdown_element .dropdown_image, .list_blocks_wrapper .list_blocks_content .dropdown_element .dropdown_image {
  float: right;
  vertical-align: middle;
  cursor: pointer;
}
/* line 1378, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .dropdown_element .dropdown_image img, .dropdown_wrapper .list_blocks_content .dropdown_element .dropdown_image img, .list_blocks_wrapper .dropdown_content .dropdown_element .dropdown_image img, .list_blocks_wrapper .list_blocks_content .dropdown_element .dropdown_image img {
  vertical-align: middle;
  -webkit-transition: all .7s;
  -moz-transition: all .7s;
  -ms-transition: all .7s;
  -o-transition: all .7s;
  transition: all .7s;
}
/* line 1387, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .dropdown_element .dropdown_image.selected img, .dropdown_wrapper .list_blocks_content .dropdown_element .dropdown_image.selected img, .list_blocks_wrapper .dropdown_content .dropdown_element .dropdown_image.selected img, .list_blocks_wrapper .list_blocks_content .dropdown_element .dropdown_image.selected img {
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-transition: all .7s;
  -moz-transition: all .7s;
  -ms-transition: all .7s;
  -o-transition: all .7s;
  transition: all .7s;
}
/* line 1401, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .list_block, .dropdown_wrapper .list_blocks_content .list_block, .list_blocks_wrapper .dropdown_content .list_block, .list_blocks_wrapper .list_blocks_content .list_block {
  width: calc(100%/2);
  display: block;
  float: left;
  padding: 10px;
  box-sizing: border-box;
}
/* line 1407, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .list_block a.image_link, .dropdown_wrapper .list_blocks_content .list_block a.image_link, .list_blocks_wrapper .dropdown_content .list_block a.image_link, .list_blocks_wrapper .list_blocks_content .list_block a.image_link {
  position: relative;
  display: block;
  height: 300px;
  width: 100%;
  overflow: hidden;
}
/* line 1413, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .list_block a.image_link img, .dropdown_wrapper .dropdown_content .list_block a.image_link .list_block_background, .dropdown_wrapper .list_blocks_content .list_block a.image_link img, .dropdown_wrapper .list_blocks_content .list_block a.image_link .list_block_background, .list_blocks_wrapper .dropdown_content .list_block a.image_link img, .list_blocks_wrapper .dropdown_content .list_block a.image_link .list_block_background, .list_blocks_wrapper .list_blocks_content .list_block a.image_link img, .list_blocks_wrapper .list_blocks_content .list_block a.image_link .list_block_background {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}
/* line 1423, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .list_block a.image_link .list_block_background, .dropdown_wrapper .list_blocks_content .list_block a.image_link .list_block_background, .list_blocks_wrapper .dropdown_content .list_block a.image_link .list_block_background, .list_blocks_wrapper .list_blocks_content .list_block a.image_link .list_block_background {
  background-color: rgba(0, 0, 0, 0.3);
}
/* line 1426, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .list_block a.image_link .list_block_name, .dropdown_wrapper .list_blocks_content .list_block a.image_link .list_block_name, .list_blocks_wrapper .dropdown_content .list_block a.image_link .list_block_name, .list_blocks_wrapper .list_blocks_content .list_block a.image_link .list_block_name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  text-transform: uppercase;
  color: white;
  padding: 10px 50px;
  font-size: 25px;
}
/* line 1439, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .list_block a.image_link:hover .list_block_background, .dropdown_wrapper .list_blocks_content .list_block a.image_link:hover .list_block_background, .list_blocks_wrapper .dropdown_content .list_block a.image_link:hover .list_block_background, .list_blocks_wrapper .list_blocks_content .list_block a.image_link:hover .list_block_background {
  opacity: 0;
}
/* line 1444, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .list_block .list_link a, .dropdown_wrapper .list_blocks_content .list_block .list_link a, .list_blocks_wrapper .dropdown_content .list_block .list_link a, .list_blocks_wrapper .list_blocks_content .list_block .list_link a {
  text-transform: uppercase;
  display: inline-block;
  border: 1px solid white;
  background-color: #52ADCA;
  color: white;
  padding: 10px 25px;
  font-size: 16px;
  margin: 20px auto;
  letter-spacing: 1px;
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}
/* line 1460, ../sass/_template_specific.scss */
.dropdown_wrapper .dropdown_content .list_block .list_link a:hover, .dropdown_wrapper .list_blocks_content .list_block .list_link a:hover, .list_blocks_wrapper .dropdown_content .list_block .list_link a:hover, .list_blocks_wrapper .list_blocks_content .list_block .list_link a:hover {
  color: #52ADCA;
  border: 1px solid #52ADCA;
  background-color: white;
}

/*=== Paralax Block ===*/
/* line 1472, ../sass/_template_specific.scss */
.paralax_block_wrapper {
  display: inline-block;
  width: 100%;
}
/* line 1476, ../sass/_template_specific.scss */
.paralax_block_wrapper .paralax_content {
  padding: 50px 0;
  text-align: center;
}
/* line 1480, ../sass/_template_specific.scss */
.paralax_block_wrapper .paralax_content .paralax_element {
  width: 210px;
  height: 210px;
  position: relative;
  display: inline-block;
  vertical-align: top;
  margin-right: 10px;
}
/* line 1488, ../sass/_template_specific.scss */
.paralax_block_wrapper .paralax_content .paralax_element.block_light {
  background: url(/img/douro/light_background.png);
}
/* line 1492, ../sass/_template_specific.scss */
.paralax_block_wrapper .paralax_content .paralax_element.block_dark {
  background: url(/img/douro/dark_background.png);
}
/* line 1500, ../sass/_template_specific.scss */
.paralax_block_wrapper .paralax_content .paralax_element .paralax_text {
  position: relative;
  text-align: center;
  color: white;
  width: 80%;
  margin: auto;
}
/* line 1510, ../sass/_template_specific.scss */
.paralax_block_wrapper .paralax_content .paralax_element .center_blocks {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 100%;
  text-align: center;
}
/* line 1521, ../sass/_template_specific.scss */
.paralax_block_wrapper .paralax_content .paralax_element .center_blocks img {
  height: 60px;
  margin-bottom: 10px;
}
/* line 1533, ../sass/_template_specific.scss */
.paralax_block_wrapper .paralax_content .paralax_element .paralax_icon img {
  height: 60px;
}

/*=== List Rooms ===*/
/* line 1547, ../sass/_template_specific.scss */
.list_rooms_wrapper {
  display: inline-block;
  width: 100%;
  padding-bottom: 20px;
}
/* line 1552, ../sass/_template_specific.scss */
.list_rooms_wrapper .list_rooms_title {
  text-transform: uppercase;
  font-size: 20px;
  padding: 20px 0;
  text-align: center;
  font-family: "Montserrat";
}
/* line 1561, ../sass/_template_specific.scss */
.list_rooms_wrapper .list_rooms_content .room_element {
  width: 50%;
  box-sizing: border-box;
  padding: 0 20px;
  text-align: center;
  float: left;
}
/* line 1568, ../sass/_template_specific.scss */
.list_rooms_wrapper .list_rooms_content .room_element .room_image {
  position: relative;
  width: 100%;
  height: 290px;
  overflow: hidden;
}
/* line 1574, ../sass/_template_specific.scss */
.list_rooms_wrapper .list_rooms_content .room_element .room_image img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  max-width: none;
  min-width: 100%;
}
/* line 1581, ../sass/_template_specific.scss */
.list_rooms_wrapper .list_rooms_content .room_element .room_title {
  text-transform: uppercase;
  font-size: 20px;
  padding: 20px 0;
  font-family: "Montserrat";
}
/* line 1587, ../sass/_template_specific.scss */
.list_rooms_wrapper .list_rooms_content .room_element .room_title span {
  display: none;
}
/* line 1592, ../sass/_template_specific.scss */
.list_rooms_wrapper .list_rooms_content .room_element .room_links {
  display: inline-block;
  width: 100%;
}
/* line 1597, ../sass/_template_specific.scss */
.list_rooms_wrapper .list_rooms_content .room_element .room_links:last-child .booking_button a {
  border-left: 0;
}
/* line 1602, ../sass/_template_specific.scss */
.list_rooms_wrapper .list_rooms_content .room_element .room_links .see_room {
  width: 50%;
  display: inline-block;
  float: left;
}
/* line 1607, ../sass/_template_specific.scss */
.list_rooms_wrapper .list_rooms_content .room_element .room_links .see_room a {
  border: 2px solid #d3d3d3;
  padding: 20px 0;
  display: inline-block;
  width: 100%;
  color: #52ADCA;
  font-style: italic;
  font-weight: bold;
  font-size: 18px;
  -webkit-transition: all .7s;
  -moz-transition: all .7s;
  -ms-transition: all .7s;
  -o-transition: all .7s;
  transition: all .7s;
}
/* line 1622, ../sass/_template_specific.scss */
.list_rooms_wrapper .list_rooms_content .room_element .room_links .see_room a:hover {
  background: #324F55;
  color: white;
}
/* line 1629, ../sass/_template_specific.scss */
.list_rooms_wrapper .list_rooms_content .room_element .room_links .booking_button {
  width: 50%;
  display: inline-block;
  float: left;
}
/* line 1634, ../sass/_template_specific.scss */
.list_rooms_wrapper .list_rooms_content .room_element .room_links .booking_button a {
  border: 2px solid #d3d3d3;
  padding: 20px 0;
  display: inline-block;
  width: 100%;
  color: #52ADCA;
  font-style: italic;
  font-weight: bold;
  font-size: 18px;
  -webkit-transition: all .7s;
  -moz-transition: all .7s;
  -ms-transition: all .7s;
  -o-transition: all .7s;
  transition: all .7s;
}
/* line 1649, ../sass/_template_specific.scss */
.list_rooms_wrapper .list_rooms_content .room_element .room_links .booking_button a:hover {
  background: #52ADCA;
  color: white;
}

/*=== Contact Form ===*/
/* line 1661, ../sass/_template_specific.scss */
.contact_iframe_background {
  background: #eeeeee;
  padding: 40px 0;
}
/* line 1665, ../sass/_template_specific.scss */
.contact_iframe_background .contact_title {
  color: #585858;
  text-align: center;
  text-transform: uppercase;
  margin-bottom: 20px;
  font-size: 24px;
  font-family: "Montserrat";
}
/* line 1674, ../sass/_template_specific.scss */
.contact_iframe_background h1#title {
  display: none;
}
/* line 1678, ../sass/_template_specific.scss */
.contact_iframe_background div#google-plus, .contact_iframe_background .fb_iframe_widget {
  display: none;
}
/* line 1682, ../sass/_template_specific.scss */
.contact_iframe_background .contact_form {
  background: white;
  width: 100%;
  float: left;
  padding: 0 41px;
  box-sizing: border-box;
}
/* line 1689, ../sass/_template_specific.scss */
.contact_iframe_background .contact_form label.title {
  display: block;
  clear: both;
  width: 100% !important;
  font-weight: 400;
  margin-bottom: 15px;
  color: #585858;
  font-size: 17px;
}
/* line 1699, ../sass/_template_specific.scss */
.contact_iframe_background .contact_form .bordeInput {
  margin-left: 0 !important;
  width: 100% !important;
  box-sizing: border-box;
  border: 0 !important;
  background: #eeeeee;
  height: 40px;
  padding-left: 30px;
}
/* line 1709, ../sass/_template_specific.scss */
.contact_iframe_background .contact_form textarea.bordeInput {
  padding-top: 20px;
}
/* line 1713, ../sass/_template_specific.scss */
.contact_iframe_background .contact_form div#contact-button {
  width: 155px !important;
  height: 42px !important;
  background: #52ADCA !important;
  text-transform: uppercase;
  text-align: center;
  box-sizing: border-box;
  padding: 11px 0 !important;
  border-radius: 0 !important;
}
/* line 1724, ../sass/_template_specific.scss */
.contact_iframe_background .contact_form div#contact-button-wrapper {
  padding-right: 0 !important;
}
/* line 1728, ../sass/_template_specific.scss */
.contact_iframe_background .contact_form input#privacy, .contact_iframe_background .contact_form input#has_reservation {
  display: inline-block;
  float: left;
  width: auto !important;
  vertical-align: middle;
  height: auto;
  margin-right: 10px;
  margin-top: 4px;
}
/* line 1738, ../sass/_template_specific.scss */
.contact_iframe_background .contact_form input#privacy + .title {
  margin-top: -3px;
  width: auto;
}
/* line 1743, ../sass/_template_specific.scss */
.contact_iframe_background .contact_form input#privacy + span a {
  font-size: 12px;
  margin-bottom: 15px;
  color: #585858;
  text-decoration: underline;
}
/* line 1750, ../sass/_template_specific.scss */
.contact_iframe_background .contact_form .has_reservation_wrapper {
  display: block;
  margin-top: -3px;
}
/* line 1754, ../sass/_template_specific.scss */
.contact_iframe_background .contact_form .has_reservation_wrapper span {
  font-size: 12px;
  margin-bottom: 15px;
  color: #585858;
  text-decoration: none;
}

/*=== Floating Widget ===*/
/* line 1766, ../sass/_template_specific.scss */
body.interior .floating_booking #wrapper_booking {
  bottom: 0 !important;
}

/* line 1771, ../sass/_template_specific.scss */
.floating_booking {
  top: 0;
  z-index: 900;
  width: 100%;
  height: 130px;
  position: fixed !important;
  bottom: inherit !important;
  background: #324F55;
  -webkit-box-shadow: 1px 1px 1px gray;
  -moz-box-shadow: 1px 1px 1px gray;
  box-shadow: 1px 1px 1px gray;
}
/* line 1783, ../sass/_template_specific.scss */
.floating_booking #wrapper_booking {
  padding: 30px 0;
  bottom: 20px !important;
}
/* line 1788, ../sass/_template_specific.scss */
.floating_booking #full_wrapper_booking {
  background: transparent;
}
/* line 1792, ../sass/_template_specific.scss */
.floating_booking .destination_wrapper input {
  background: white;
}
/* line 1796, ../sass/_template_specific.scss */
.floating_booking #full_wrapper_booking .date_box {
  background-color: white;
}
/* line 1800, ../sass/_template_specific.scss */
.floating_booking #full_wrapper_booking .selectric {
  background: white;
}
/* line 1804, ../sass/_template_specific.scss */
.floating_booking #full_wrapper_booking .boking_widget_inline .room_list_wrapper {
  background: white;
}

/* line 1809, ../sass/_template_specific.scss */
.floating_room_list {
  top: 100%;
  bottom: inherit !important;
}
/* line 1813, ../sass/_template_specific.scss */
.floating_room_list:before {
  border-top: 0 !important;
  border-bottom: 13px solid #52ADCA;
  bottom: 100% !important;
}
/* line 1819, ../sass/_template_specific.scss */
.floating_room_list:after {
  border-top: 0 !important;
  border-bottom: 10px solid rgba(255, 255, 255, 0.9);
  bottom: 100% !important;
}

/* line 1826, ../sass/_template_specific.scss */
.fixed_datepicker {
  position: fixed !important;
  top: 75px !important;
}

/*=== Opinion Home ===*/
/* line 1832, ../sass/_template_specific.scss */
.opinion_home_wrapper {
  display: inline-block;
  width: 100%;
  padding: 20px 0;
}
/* line 1837, ../sass/_template_specific.scss */
.opinion_home_wrapper .opinion_link {
  float: right;
  margin-bottom: 20px;
}
/* line 1841, ../sass/_template_specific.scss */
.opinion_home_wrapper .opinion_link a {
  color: black;
}
/* line 1844, ../sass/_template_specific.scss */
.opinion_home_wrapper .opinion_link a:hover {
  color: #52ADCA;
}
/* line 1850, ../sass/_template_specific.scss */
.opinion_home_wrapper .opinion_element {
  display: inline-block;
  width: 100%;
  position: relative;
  border-top: 1px solid #52ADCA;
  border-bottom: 1px solid #52ADCA;
  margin: 10px 0;
}
/* line 1858, ../sass/_template_specific.scss */
.opinion_home_wrapper .opinion_element .opinion_channel {
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  padding: 20px;
  margin: 10px;
  color: gray;
  width: 150px;
}
/* line 1867, ../sass/_template_specific.scss */
.opinion_home_wrapper .opinion_element .opinion_channel .opinion_user {
  margin-bottom: 10px;
}
/* line 1872, ../sass/_template_specific.scss */
.opinion_home_wrapper .opinion_element .opinion_description {
  display: inline-block;
  vertical-align: middle;
  border-left: 1px solid #52ADCA;
  border-right: 1px solid #52ADCA;
  padding: 20px;
  font-size: 14px;
  color: gray;
  box-sizing: border-box;
  margin: 10px;
  width: 830px;
}
/* line 1884, ../sass/_template_specific.scss */
.opinion_home_wrapper .opinion_element .opinion_description .opinion_highlight {
  display: block;
  color: #324F55;
  margin-bottom: 10px;
  font-size: 16px;
}
/* line 1892, ../sass/_template_specific.scss */
.opinion_home_wrapper .opinion_element .opinion_rate {
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  padding: 20px;
  margin: 10px;
  font-size: 32px;
  color: #52ADCA;
}

/*=== Form Job ===*/
/* line 1905, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs {
  box-sizing: border-box;
  background: #F6F4F5;
  margin-bottom: 50px;
  padding: 20px;
  display: table;
}
/* line 1912, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs h1.job_title_work {
  text-transform: uppercase;
  font-size: 22px;
  margin-bottom: 10px;
  color: #52ADCA;
  font-family: "Montserrat";
  margin-top: 10px;
}
/* line 1921, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs input[type="text"] {
  border: 0;
  border-bottom: 2px solid #324F55;
}
/* line 1926, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs input#file_cv {
  border-bottom: 2px solid #324F55;
  width: 100% !important;
  background: white;
  display: block;
  padding: 10px 0;
  padding-left: 10px;
}
/* line 1934, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs input#file_cv::-webkit-file-upload-button {
  -webkit-appearance: none;
  background: white;
  border: 0;
  font-size: 17px;
}
/* line 1942, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs li {
  float: left;
  margin-left: 0;
  width: 49%;
  min-height: 56px;
  margin-bottom: 20px;
}
/* line 1949, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs li.right_block {
  float: right;
}
/* line 1954, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs li > label {
  display: block;
  text-transform: uppercase;
  font-size: 14px;
  color: #52ADCA;
  margin-bottom: 3px;
}
/* line 1962, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs li input[type="text"] {
  width: 100%;
  height: 30px;
  font-size: 16px;
}
/* line 1968, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs li input[type="radio"] {
  margin-top: 10px;
}
/* line 1972, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs li select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  height: 36px;
  border: 1px solid #e6e6e6;
  border-radius: 0;
  background: white;
  padding-left: 10px;
  font-size: 16px;
  border-bottom: 2px solid #324F55;
}
/* line 1985, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs li textarea {
  width: 740px;
}
/* line 1989, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs .with-check-full {
  width: 1140px;
}
/* line 1992, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs .with-check-full > label {
  margin-bottom: 10px;
}
/* line 1996, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs .with-check-full p {
  width: 285px;
  float: left;
  margin-bottom: 5px;
}
/* line 2002, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs .with-check-full input {
  float: left;
  margin-right: 6px;
}
/* line 2007, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs .with-check-full label {
  width: 320px;
}
/* line 2011, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs .languages {
  width: 740px;
}
/* line 2014, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs .languages p {
  width: 148px;
  float: left;
}
/* line 2019, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs .software {
  width: 740px;
}
/* line 2022, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs .software p {
  float: left;
  width: 123px;
}
/* line 2028, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs .findus,
div#form_jobs_wrapper.form-jobs .work-here {
  width: 740px;
}
/* line 2032, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs #privacity {
  float: left;
}
/* line 2036, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs a {
  color: black;
  text-decoration: underline;
}
/* line 2041, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs #contact-button {
  padding: 8px 20px;
  border-radius: 0;
  cursor: pointer;
  background: #52ADCA;
  color: white;
  text-decoration: none;
  float: right;
  text-transform: uppercase;
  font-size: 20px;
}
/* line 2054, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs input.error,
div#form_jobs_wrapper.form-jobs textarea.error {
  border: 1px solid red;
}
/* line 2059, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs input.error[type="checkbox"] {
  outline: 1px solid red;
}
/* line 2062, ../sass/_template_specific.scss */
div#form_jobs_wrapper.form-jobs input.error[type="radio"] {
  outline: 1px solid red;
}

/* line 2069, ../sass/_template_specific.scss */
.opinions .header, .opinions-total .header {
  background-color: #52ADCA;
  padding: 20px;
  position: relative;
  color: white;
}
/* line 2075, ../sass/_template_specific.scss */
.opinions .header img, .opinions-total .header img {
  position: absolute;
  top: 20px;
  left: 20px;
}
/* line 2081, ../sass/_template_specific.scss */
.opinions .header h3, .opinions-total .header h3 {
  margin-left: 52px;
  text-transform: uppercase;
  font-size: 22px;
}
/* line 2086, ../sass/_template_specific.scss */
.opinions .header p, .opinions-total .header p {
  font-size: 14px;
  margin-left: 52px;
  line-height: 14px;
  margin-top: 2px;
}

/* line 2094, ../sass/_template_specific.scss */
.opinions-total {
  margin: 40px 0 40px 0;
  background: #eef7fa;
}

/* line 2099, ../sass/_template_specific.scss */
.opinions {
  width: 379px !important;
  margin-left: 0px;
  margin-right: 0px;
}

/* line 2105, ../sass/_template_specific.scss */
.opinions .value, .opinions-total .value {
  text-align: center;
  padding: 14px 0;
  border-top: 2px solid white;
  font-size: 16px;
  text-transform: uppercase;
  color: #52ADCA;
}
/* line 2113, ../sass/_template_specific.scss */
.opinions .value .media, .opinions-total .value .media {
  font-size: 30px;
  font-family: nexabold;
  color: #52ADCA;
  margin-right: 10px;
}

/* line 2121, ../sass/_template_specific.scss */
.opinions-button {
  position: absolute;
  right: 20px;
  top: 17px;
  display: block;
  background: white;
  color: #52ADCA;
  padding: 10px;
}

/* line 2131, ../sass/_template_specific.scss */
.opinions-button:hover {
  color: white;
  background: #2b748b;
}

/* line 2136, ../sass/_template_specific.scss */
.opinions .coment, .opinions-total .coment {
  background: #324F55;
  padding: 15px 0;
  border-top: 1px solid white;
  position: relative;
}
/* line 2143, ../sass/_template_specific.scss */
.opinions .coment .plus-link, .opinions-total .coment .plus-link {
  position: absolute;
  right: 15px;
  top: 12px;
  background-color: #52ADCA;
  padding: 8px 8px 0 !important;
}
/* line 2150, ../sass/_template_specific.scss */
.opinions .coment .plus-link:hover, .opinions-total .coment .plus-link:hover {
  background-color: #324F55;
}
/* line 2154, ../sass/_template_specific.scss */
.opinions .coment span, .opinions-total .coment span {
  font-size: 12px;
}
/* line 2157, ../sass/_template_specific.scss */
.opinions .coment span p, .opinions-total .coment span p {
  display: inline;
}
/* line 2161, ../sass/_template_specific.scss */
.opinions .coment .calification, .opinions-total .coment .calification {
  color: white;
  background-color: #787878;
  padding: 10px 10px 9px;
  margin-right: 20px;
  margin-left: 15px;
  font-size: 14px;
}

/* line 2171, ../sass/_template_specific.scss */
.opinions-total table {
  width: 100%;
}
/* line 2174, ../sass/_template_specific.scss */
.opinions-total table tr {
  background-color: #eef7fa;
  border-top: 2px solid white;
  color: #5a5a5a;
}
/* line 2179, ../sass/_template_specific.scss */
.opinions-total table tr .name {
  text-transform: uppercase;
  width: 250px;
  border-right: 2px solid white;
  padding: 20px;
}
/* line 2185, ../sass/_template_specific.scss */
.opinions-total table tr .opinion-description {
  width: 800px;
  padding: 20px;
}
/* line 2189, ../sass/_template_specific.scss */
.opinions-total table tr .calification {
  border-left: 2px solid white;
  vertical-align: middle;
  padding: 20px;
}
/* line 2194, ../sass/_template_specific.scss */
.opinions-total table tr .calification span {
  color: white;
  background-color: #787878;
  padding: 10px 10px 9px;
  font-size: 14px;
}
/* line 2201, ../sass/_template_specific.scss */
.opinions-total table tr p {
  margin-bottom: 0;
}

/* line 2207, ../sass/_template_specific.scss */
.form-general {
  padding: 20px 20px 0;
  background: white;
  margin-top: 20px;
  overflow: hidden;
}
/* line 2213, ../sass/_template_specific.scss */
.form-general h3 {
  margin-bottom: 20px;
}
/* line 2217, ../sass/_template_specific.scss */
.form-general li {
  display: inline-block;
  width: 268px;
  margin-bottom: 10px;
}
/* line 2222, ../sass/_template_specific.scss */
.form-general li label {
  display: block;
  font-size: 12px;
  color: #5a5a5a;
}
/* line 2227, ../sass/_template_specific.scss */
.form-general li input, .form-general li textarea {
  border: none;
  width: 254px;
  padding: 10px 5px;
  background: #e6e6e6;
}
/* line 2234, ../sass/_template_specific.scss */
.form-general li select {
  width: 265px;
  background-color: white;
  padding: 10px 5px;
  height: 35px;
  border: none;
  -webkit-appearance: none;
  border-radius: 0;
  background: #e6e6e6;
}
/* line 2244, ../sass/_template_specific.scss */
.form-general li textarea {
  height: 13px;
  width: 524px;
}
/* line 2249, ../sass/_template_specific.scss */
.form-general li #check_from, .form-general li #check_till {
  background: white url("/img/holiy/calendar.png?v=1") no-repeat 235px;
}
/* line 2252, ../sass/_template_specific.scss */
.form-general li #check_date, .form-general li #end_date {
  background: white url("/img/holiy/calendar.png?v=1") no-repeat 100px;
}
/* line 2256, ../sass/_template_specific.scss */
.form-general li.comment-box {
  width: 100%;
}
/* line 2259, ../sass/_template_specific.scss */
.form-general .short {
  width: 132px;
}
/* line 2262, ../sass/_template_specific.scss */
.form-general .short input {
  width: 118px;
}
/* line 2266, ../sass/_template_specific.scss */
.form-general a {
  color: white;
  margin-top: 10px;
  display: inline-block;
}
/* line 2272, ../sass/_template_specific.scss */
.form-general .btn-corporate {
  font-size: 14px;
  padding: 5px 10px 2px;
  border-radius: 3px;
  cursor: pointer;
  float: right;
  background: #324F55;
}
/* line 2280, ../sass/_template_specific.scss */
.form-general span a {
  color: #52ADCA;
  font-size: 12px;
}
/* line 2284, ../sass/_template_specific.scss */
.form-general .form-bottom {
  display: inline-block;
  width: 400px;
}
/* line 2288, ../sass/_template_specific.scss */
.form-general .form-bottom p {
  margin-bottom: 0;
  line-height: 15px;
}
/* line 2292, ../sass/_template_specific.scss */
.form-general .form-bottom label.error {
  display: none !important;
}
/* line 2296, ../sass/_template_specific.scss */
.form-general .last {
  margin-top: 38px;
}
/* line 2299, ../sass/_template_specific.scss */
.form-general .last .form-bottom {
  margin-top: 10px;
}
/* line 2304, ../sass/_template_specific.scss */
.form-general .double_li_form {
  width: 536px;
}
/* line 2307, ../sass/_template_specific.scss */
.form-general .double_li_form input {
  width: 526px;
}

/* line 2315, ../sass/_template_specific.scss */
.form-general.form-opinion li {
  display: block !important;
}

/* line 2319, ../sass/_template_specific.scss */
.form-general.form-opinion .btn-corporate {
  float: none !important;
  margin-bottom: 20px;
}

/*===== Offer individual =====*/
/* line 2325, ../sass/_template_specific.scss */
.detailed_offer_wrapper {
  margin-top: 30px;
}

/* line 2329, ../sass/_template_specific.scss */
.offer_detail_image_wrapper {
  height: 675px;
  position: relative;
  overflow: hidden;
}
/* line 2334, ../sass/_template_specific.scss */
.offer_detail_image_wrapper .offer_detail_image {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  max-width: initial;
}

/* line 2339, ../sass/_template_specific.scss */
.offer_details_text {
  display: block;
  width: 825px;
  margin-top: -125px;
  z-index: 2;
  position: relative;
  background: white;
  margin-left: 30px;
  padding: 40px;
}
/* line 2349, ../sass/_template_specific.scss */
.offer_details_text h1.offer_title {
  text-transform: uppercase;
  font-weight: bold;
  font-size: 24px;
  background: url(/img/douro/bgc_title.png?v=1) no-repeat center center;
  background-size: contain;
  display: inline-block;
  font-family: "Montserrat";
  padding: 25px 20px;
}
/* line 2359, ../sass/_template_specific.scss */
.offer_details_text h1.offer_title span {
  display: block;
  font-weight: 300;
  color: gray;
}
/* line 2366, ../sass/_template_specific.scss */
.offer_details_text .button-promotion {
  background: #52ADCA;
  padding: 20px 80px;
  color: white;
  text-transform: uppercase;
  /*display: inline-block;*/
  display: none;
  float: right;
  margin-top: 19px;
}
/* line 2377, ../sass/_template_specific.scss */
.offer_details_text .offer_description {
  font-size: 14px;
  color: grey;
  width: 725px;
  margin: 20px 20px;
}
/* line 2384, ../sass/_template_specific.scss */
.offer_details_text div#shareSocialArea {
  float: right;
  font-size: 14px;
  color: grey;
}

/*===== Carousel Banners ======*/
/* line 2392, ../sass/_template_specific.scss */
.banner_carousel_element {
  margin-top: 50px;
}

/* line 2397, ../sass/_template_specific.scss */
.banners_carousel_wrapper h2.banner_carousel_title {
  margin: 4px 0px;
  font-family: "Montserrat";
  font-weight: bold;
  font-size: 24px;
}
/* line 2404, ../sass/_template_specific.scss */
.banners_carousel_wrapper .banner_carousel_description {
  font-size: 14px;
  color: grey;
}
/* line 2409, ../sass/_template_specific.scss */
.banners_carousel_wrapper .banner_carousel_images {
  margin-top: 30px;
  position: relative;
  margin-bottom: 60px;
}
/* line 2414, ../sass/_template_specific.scss */
.banners_carousel_wrapper .banner_carousel_images .flex-direction-nav {
  position: absolute;
  top: 46%;
  width: 100%;
}
/* line 2419, ../sass/_template_specific.scss */
.banners_carousel_wrapper .banner_carousel_images .flex-direction-nav .flex-nav-prev {
  position: absolute;
  left: -50px;
}
/* line 2423, ../sass/_template_specific.scss */
.banners_carousel_wrapper .banner_carousel_images .flex-direction-nav .flex-nav-next {
  position: absolute;
  right: -50px;
}
/* line 2428, ../sass/_template_specific.scss */
.banners_carousel_wrapper .banner_carousel_images .flex-direction-nav .flex-prev {
  display: inline-block;
  background: url("/img/douro/flex-arrow-leftb.png") no-repeat;
  color: transparent;
  width: 45px;
  height: 56px;
}
/* line 2435, ../sass/_template_specific.scss */
.banners_carousel_wrapper .banner_carousel_images .flex-direction-nav .flex-next {
  display: inline-block;
  background: url("/img/douro/flex-arrow-rightb.png") no-repeat;
  color: transparent;
  width: 45px;
  height: 56px;
}
/* line 2446, ../sass/_template_specific.scss */
.banners_carousel_wrapper .carousel_banner_image_element {
  padding: 0 5px;
  box-sizing: border-box;
}
/* line 2451, ../sass/_template_specific.scss */
.banners_carousel_wrapper .carousel_banner_image_element .text_carousel_image h3 {
  color: white;
  font-size: 17px;
  font-weight: bold;
  margin: 14px 0px 7px;
}
/* line 2459, ../sass/_template_specific.scss */
.banners_carousel_wrapper .carousel_banner_image_element .carousel_image_top {
  width: 100%;
}
/* line 2463, ../sass/_template_specific.scss */
.banners_carousel_wrapper .carousel_banner_image_element .exceded {
  position: relative;
  height: 180px;
  overflow: hidden;
}
/* line 2468, ../sass/_template_specific.scss */
.banners_carousel_wrapper .carousel_banner_image_element .exceded img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 2476, ../sass/_template_specific.scss */
.banners_carousel_wrapper .carousel_banner_image_element .content_carousel_image {
  height: 130px;
  overflow: hidden;
  font-size: 14px;
  color: white;
}
/* line 2483, ../sass/_template_specific.scss */
.banners_carousel_wrapper .carousel_banner_image_element .content_carousel_image hide {
  display: none;
}
/* line 2489, ../sass/_template_specific.scss */
.banners_carousel_wrapper .text_carousel_image {
  display: table;
  box-sizing: border-box;
  width: 100%;
  padding: 0 20px 20px;
  background: #324F55;
  height: 252px;
}
/* line 2498, ../sass/_template_specific.scss */
.banners_carousel_wrapper a.contact {
  color: white;
  font-size: 14px;
  margin: 20px 0 0 !important;
  display: block;
}

/* line 2508, ../sass/_template_specific.scss */
.hidden_carousel .image_hidden_banner {
  width: 100%;
}
/* line 2512, ../sass/_template_specific.scss */
.hidden_carousel h3.title_hidden_banner {
  color: #897966;
  font-size: 24px;
  font-family: 'Droid Serif', serif;
  margin: 15px 0 15px;
  display: block;
}
/* line 2520, ../sass/_template_specific.scss */
.hidden_carousel .content_hidden_banner {
  font-size: 16px;
  clear: both;
}
/* line 2524, ../sass/_template_specific.scss */
.hidden_carousel .content_hidden_banner .new-intro {
  font-weight: bold;
  margin-bottom: 10px;
}
/* line 2528, ../sass/_template_specific.scss */
.hidden_carousel .content_hidden_banner p {
  margin-bottom: 15px;
}
/* line 2532, ../sass/_template_specific.scss */
.hidden_carousel .date_new {
  color: #52ADCA;
  font-style: italic;
  float: left;
  margin-bottom: 30px;
}
/* line 2538, ../sass/_template_specific.scss */
.hidden_carousel #shareSocialArea {
  float: right;
  margin-top: 0px;
  clear: initial;
}

/* line 2545, ../sass/_template_specific.scss */
.flex-control-nav {
  text-align: right;
  padding: 10px 0;
}
/* line 2549, ../sass/_template_specific.scss */
.flex-control-nav li {
  display: inline-block;
}
/* line 2552, ../sass/_template_specific.scss */
.flex-control-nav li a {
  background: #324F55;
  display: block;
  width: 10px;
  height: 10px;
  cursor: pointer;
  margin: 4px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  border-radius: 20px;
  position: relative;
  text-indent: 9999px;
}
/* line 2565, ../sass/_template_specific.scss */
.flex-control-nav li a.flex-active:before {
  content: '';
  padding: 7px;
  -webkit-border: 1px solid #324F55;
  -moz-border: 1px solid #324F55;
  border: 1px solid #324F55;
  position: absolute;
  top: -3px;
  bottom: 0;
  left: -3px;
  right: 0;
  border-radius: 20px;
}

/* line 2582, ../sass/_template_specific.scss */
.spaItem {
  display: block;
  float: left;
  max-width: 100%;
  width: calc(97%/3);
  box-sizing: border-box;
  background: #F6F4F5 url(/img/douro/bgc_title.png?v=2) no-repeat center center;
  background-size: 70%;
  text-align: left;
  padding: 20px;
  margin: 5px;
}
/* line 2593, ../sass/_template_specific.scss */
.spaItem .spaItem_title {
  display: block;
  color: #52ADCA;
  font-size: 25px;
}
/* line 2598, ../sass/_template_specific.scss */
.spaItem .spaItem_subtitle {
  display: block;
  color: #CCCCCC;
  font-size: 20px;
}

/*=== Fixing gallery crop ===*/
/* line 2606, ../sass/_template_specific.scss */
.border-gallery .wrapper_filt .gallery_1 li .crop {
  position: relative;
  width: 100%;
  height: 100%;
}
/* line 2611, ../sass/_template_specific.scss */
.border-gallery .wrapper_filt .gallery_1 li .crop img {
  position: absolute;
  top: -100%;
  bottom: -100%;
  left: -100%;
  right: -100%;
  margin: auto;
  min-height: 100%;
  max-width: none;
  min-width: 100%;
  height: auto;
}

/* line 2625, ../sass/_template_specific.scss */
.awards_wrapper {
  padding: 40px;
}
/* line 2628, ../sass/_template_specific.scss */
.awards_wrapper > div {
  display: inline-block;
  vertical-align: middle;
  margin: 0 15px;
}
/* line 2634, ../sass/_template_specific.scss */
.awards_wrapper .award_element {
  display: inline-block;
  vertical-align: middle;
  margin: 0 15px;
}
/* line 2639, ../sass/_template_specific.scss */
.awards_wrapper .award_element img {
  vertical-align: middle;
  height: 130px;
}

/* line 13, ../sass/styles.scss */
.douro-palace .content_subtitle_wrapper .content_subtitle_title,
.douro-palace .offer_details_text h1.offer_title,
.douro-palace .spaItem {
  background: url(/img/douro/bgc_title_palace2.jpg?v=1) no-repeat center center;
}
/* line 19, ../sass/styles.scss */
.douro-palace .spaItem {
  background: #F6F4F5;
}
/* line 23, ../sass/styles.scss */
.douro-palace footer {
  background: #324F55 url(/img/douro/bgc_footer_palace2.png?v=2.1) no-repeat center 45%;
}
