@import url(//fonts.googleapis.com/css?family=Montserrat|Source+Sans+Pro:400,300,700,600);
@-webkit-keyframes fade_in {
  0% {
    opacity: 0;
    display: none;
  }
  100% {
    opacity: 1;
    display: block;
  }
}
@-moz-keyframes fade_in {
  0% {
    opacity: 0;
    display: none;
  }
  100% {
    opacity: 1;
    display: block;
  }
}
@-o-keyframes fade_in {
  0% {
    opacity: 0;
    display: none;
  }
  100% {
    opacity: 1;
    display: block;
  }
}
@keyframes fade_in {
  0% {
    opacity: 0;
    display: none;
  }
  100% {
    opacity: 1;
    display: block;
  }
}
/* line 153, ../sass/_defaults.scss */
.fade_in_effect {
  -webkit-animation: fade_in;
  /* Safari 4+ */
  -moz-animation: fade_in;
  /* Fx 5+ */
  -o-animation: fade_in;
  /* Opera 12+ */
  animation: fade_in;
}

@-webkit-keyframes slide_down {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, -100%, 0);
    -moz-transform: translate3d(-100%, -100%, 0);
    -ms-transform: translate3d(-100%, -100%, 0);
    -o-transform: translate3d(-100%, -100%, 0);
    transform: translate3d(-100%, -100%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-moz-keyframes slide_down {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, -100%, 0);
    -moz-transform: translate3d(-100%, -100%, 0);
    -ms-transform: translate3d(-100%, -100%, 0);
    -o-transform: translate3d(-100%, -100%, 0);
    transform: translate3d(-100%, -100%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-o-keyframes slide_down {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, -100%, 0);
    -moz-transform: translate3d(-100%, -100%, 0);
    -ms-transform: translate3d(-100%, -100%, 0);
    -o-transform: translate3d(-100%, -100%, 0);
    transform: translate3d(-100%, -100%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@keyframes slide_down {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, -100%, 0);
    -moz-transform: translate3d(-100%, -100%, 0);
    -ms-transform: translate3d(-100%, -100%, 0);
    -o-transform: translate3d(-100%, -100%, 0);
    transform: translate3d(-100%, -100%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
/* line 180, ../sass/_defaults.scss */
.slide_down_effect {
  -webkit-animation: slide_down 0.7s;
  /* Safari 4+ */
  -moz-animation: slide_down 0.7s;
  /* Fx 5+ */
  -o-animation: slide_down 0.7s;
  /* Opera 12+ */
  animation: slide_down 0.7s;
}

@-webkit-keyframes slide_left {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-moz-keyframes slide_left {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-o-keyframes slide_left {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@keyframes slide_left {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0%, 0);
    -moz-transform: translate3d(-100%, 0%, 0);
    -ms-transform: translate3d(-100%, 0%, 0);
    -o-transform: translate3d(-100%, 0%, 0);
    transform: translate3d(-100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
/* line 207, ../sass/_defaults.scss */
.slide_left_effect {
  -webkit-animation: slide_left 0.7s;
  /* Safari 4+ */
  -moz-animation: slide_left 0.7s;
  /* Fx 5+ */
  -o-animation: slide_left 0.7s;
  /* Opera 12+ */
  animation: slide_left 0.7s;
}

@-webkit-keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0%, 0);
    -moz-transform: translate3d(100%, 0%, 0);
    -ms-transform: translate3d(100%, 0%, 0);
    -o-transform: translate3d(100%, 0%, 0);
    transform: translate3d(100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-moz-keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0%, 0);
    -moz-transform: translate3d(100%, 0%, 0);
    -ms-transform: translate3d(100%, 0%, 0);
    -o-transform: translate3d(100%, 0%, 0);
    transform: translate3d(100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-o-keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0%, 0);
    -moz-transform: translate3d(100%, 0%, 0);
    -ms-transform: translate3d(100%, 0%, 0);
    -o-transform: translate3d(100%, 0%, 0);
    transform: translate3d(100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@keyframes slide_right {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0%, 0);
    -moz-transform: translate3d(100%, 0%, 0);
    -ms-transform: translate3d(100%, 0%, 0);
    -o-transform: translate3d(100%, 0%, 0);
    transform: translate3d(100%, 0%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
/* line 234, ../sass/_defaults.scss */
.slide_right_effect {
  -webkit-animation: slide_right 0.7s;
  /* Safari 4+ */
  -moz-animation: slide_right 0.7s;
  /* Fx 5+ */
  -o-animation: slide_right 0.7s;
  /* Opera 12+ */
  animation: slide_right 0.7s;
}

@-webkit-keyframes slide_bottom {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-moz-keyframes slide_bottom {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@-o-keyframes slide_bottom {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
@keyframes slide_bottom {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0%, 100%, 0);
    -moz-transform: translate3d(0%, 100%, 0);
    -ms-transform: translate3d(0%, 100%, 0);
    -o-transform: translate3d(0%, 100%, 0);
    transform: translate3d(0%, 10%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }
}
/* line 261, ../sass/_defaults.scss */
.slide_bottom_effect {
  -webkit-animation: slide_bottom 0.7s;
  /* Safari 4+ */
  -moz-animation: slide_bottom 0.7s;
  /* Fx 5+ */
  -o-animation: slide_bottom 0.7s;
  /* Opera 12+ */
  animation: slide_bottom 0.7s;
}

/* line 3, ../../../../sass/booking/_booking_engine_5.scss */
.booking_widget {
  position: relative;
}

/* line 7, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 {
  width: 275px;
  display: block;
}
/* line 11, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 form.booking_form {
  padding: 0 !important;
}
/* line 15, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 .booking_form_title {
  background: #383838;
}

/* line 20, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper {
  border-bottom: 1px solid lightgray;
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat 92% 70% !important;
}
/* line 24, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
  display: inline-block;
  margin: 10px 0 0 10px;
}

/* line 32, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input {
  border: none;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 25px;
  font-weight: 600;
  background: white;
}
/* line 40, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input::-webkit-input-placeholder {
  color: #585d63;
}
/* line 44, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input:-moz-placeholder {
  /* Firefox 18- */
  color: #585d63;
}
/* line 48, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input::-moz-placeholder {
  /* Firefox 19+ */
  color: #585d63;
}
/* line 52, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input:-ms-input-placeholder {
  color: #585d63;
}

/* line 57, ../../../../sass/booking/_booking_engine_5.scss */
.entry_date_wrapper {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 66.6%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}

/* line 72, ../../../../sass/booking/_booking_engine_5.scss */
.departure_date_wrapper, .half_size {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 50%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}
/* line 86, ../../../../sass/booking/_booking_engine_5.scss */
.departure_date_wrapper .date_day, .half_size .date_day {
  font-size: 21px !important;
}

/* line 91, ../../../../sass/booking/_booking_engine_5.scss */
.nights_number_wrapper {
  float: right;
  height: 70px;
  border-bottom: 1px solid lightgrey;
  box-sizing: border-box;
  width: 33.3%;
  padding: 5px 10px;
}

/* line 100, ../../../../sass/booking/_booking_engine_5.scss */
.num_nights_label, .entry_date_label, .children_label, .rooms_label, .adults_label, .promocode_label, .departure_date_label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/* line 106, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 30%;
}

/*===== Entry date =====*/
/* line 113, ../../../../sass/booking/_booking_engine_5.scss */
.date_box.entry_date, .date_box.departure_date {
  font-size: 20px;
  background: url(/static_1/images/booking_5/calendar.png) no-repeat center right;
  margin-top: 9px;
  background-size: 29px;
}
/* line 119, ../../../../sass/booking/_booking_engine_5.scss */
.date_box.entry_date .date_day, .date_box.departure_date .date_day {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 25px;
  font-weight: 600;
}

/*====== Booking button ====*/
/* line 128, ../../../../sass/booking/_booking_engine_5.scss */
button.submit_button {
  width: 100%;
  border: 0;
  border-radius: 0;
  background: #ffd600;
  height: 55px;
  text-transform: uppercase;
  font-weight: bolder;
  font-size: 21px;
  color: #565656;
  cursor: pointer;
}

/*====== Rooms section ======*/
/* line 142, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 33.3%;
  padding: 7px 10px 5px;
  box-sizing: border-box;
}
/* line 149, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper .room_list_wrapper li.room {
  float: right;
  width: 69%;
}
/* line 155, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper .adults_selector {
  height: 70px;
  float: left;
  border-right: 1px solid gray;
  width: 46.5%;
}

/* line 163, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper {
  display: table;
  float: right;
  width: 66.6%;
}
/* line 168, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .room_title {
  display: none;
}
/* line 172, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .adults_selector {
  border-right: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
}
/* line 177, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
  width: 50%;
  display: inline-block;
  height: 70px;
  box-sizing: border-box;
  padding: 7px 10px 5px;
}
/* line 185, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .children_selector {
  width: 50% !important;
  padding: 7px 10px 5px;
  float: right;
}

/* line 192, ../../../../sass/booking/_booking_engine_5.scss */
.room_list .room {
  height: 70px;
}

/* line 196, ../../../../sass/booking/_booking_engine_5.scss */
.room_list .room.room3, .room_list .room.room2 {
  border-top: 1px solid lightgrey;
}

/*==== Promocode section  ====*/
/* line 201, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_wrapper {
  display: table;
  width: 100%;
  padding: 10px 10px 5px;
  border-top: 1px solid lightgrey;
  box-sizing: border-box;
}

/* line 209, ../../../../sass/booking/_booking_engine_5.scss */
label.promocode_label {
  display: block;
}

/* line 213, ../../../../sass/booking/_booking_engine_5.scss */
input.promocode_input {
  border: 0;
  width: 100%;
  display: block;
  height: 30px;
  font-size: 13px;
  font-weight: lighter;
  margin-top: 2px;
  font-family: sans-serif;
}

/*====== Selectric ======*/
/* line 225, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems {
  overflow: scroll !important;
  width: 92px !important;
  margin-left: -11px;
  margin-top: 2px;
}
/* line 231, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 237, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li {
  font-weight: 500;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAADAQMAAABs5if8AAAABlBMVEXOzs7y8vKNS741AAAADklEQVQI12M4wPSAWQAABoQBtgsaY5kAAAAASUVORK5CYII=);
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  line-height: 29px !important;
  font-size: 18px !important;
  font-family: 'Source Sans Pro', sans-serif;
}
/* line 247, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li:hover {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 254, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems ul {
  z-index: 40;
}
/* line 258, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems .room {
  padding-top: 17px;
}

/* line 264, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 .selectric .button {
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 23px;
  margin-left: 0;
  margin-top: 8px;
  text-indent: 999px;
  height: 22px;
  font-weight: 600;
}

/* line 277, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper .selectric .label {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 28px;
  margin-left: 0;
  font-weight: 600;
}

/* line 285, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper .selectric {
  margin-top: 3px;
}

/* line 289, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper {
  width: 78px !important;
}

/*======= Phone web support =======*/
/* line 294, ../../../../sass/booking/_booking_engine_5.scss */
.wrapper-new-web-support.booking_form_title {
  border-radius: 0;
  padding: 5px 10px;
  font-family: 'Montserrat', sans-serif;
  font-size: 11px !important;
  margin-top: 11px;
}

/* line 302, ../../../../sass/booking/_booking_engine_5.scss */
.wrapper-new-web-support .web_support_number, .web_support_label_1 {
  font-size: 11px !important;
  margin-left: 5px;
}

/* line 307, ../../../../sass/booking/_booking_engine_5.scss */
img.phone_support_image {
  float: left;
  width: 28px;
  margin-top: 1px;
  margin-right: 12px;
}

/*======= Header title ======*/
/* line 315, ../../../../sass/booking/_booking_engine_5.scss */
.booking_title_1, .booking_title_2, .best_price {
  display: none;
}

/* line 319, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header {
  font-family: 'Montserrat', sans-serif;
  padding: 10px;
  display: table;
  width: 100%;
  box-sizing: border-box;
  cursor: pointer;
}
/* line 327, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header:hover {
  opacity: 0.8;
}
/* line 331, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header img.booking_header_discount {
  float: left;
  margin-right: 15px;
}
/* line 336, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header p.first_offer_name {
  font-size: 12px;
  color: #8096c7;
  margin-top: 9px;
}
/* line 342, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header p.second_offer_name {
  font-size: 10px;
  margin-top: 3px;
  color: gray;
}

/* line 349, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker {
  width: 283px;
}
/* line 352, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker .ui-widget-header {
  background: none !important;
  border: 0;
}
/* line 356, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker .ui-widget-header .ui-datepicker-title {
  color: #646464 !important;
  font-family: Verdana, Arial, sans-serif;
  font-weight: 300;
}

/* line 365, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker th {
  font-weight: 300;
  font-size: 14px;
}
/* line 371, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker .ui-state-default, body .ui-datepicker .ui-widget-content .ui-state-default, body .ui-datepicker .ui-widget-header .ui-state-default {
  background: transparent !important;
  border: 0 !important;
  color: #646464 !important;
  font-weight: 400;
  font-family: Circular, "Helvetica Neue", Helvetica, Arial, sans-serif;
  text-align: center;
  font-size: 13px;
}
/* line 382, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content .ui-state-hover {
  border: 0;
  background: #4B4B4B !important;
  color: white !important;
}
/* line 388, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content {
  border: 0;
  border-radius: 0;
  padding-bottom: 40px;
}
/* line 394, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-state-default {
  padding: 8px;
}
/* line 398, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-start_date {
  opacity: 1 !important;
}
/* line 401, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-start_date .ui-state-default {
  border: 0;
  background: #C66047 !important;
  color: white !important;
}
/* line 408, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-highlighted .ui-state-default {
  border: 0;
  background: rgba(40, 96, 144, 0.25) !important;
}
/* line 413, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content .ui-state-active {
  border: 0;
  background: #C66047 !important;
  color: white !important;
}
/* line 419, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-e, body span.ui-icon.ui-icon-circle-triangle-w {
  /* background: url(/static_1/images/mobile_img/renovation/flecha_der.png) no-repeat center;*/
  background: none;
  text-indent: 0;
  color: transparent;
  font-size: 0;
}
/* line 425, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-e:before, body span.ui-icon.ui-icon-circle-triangle-w:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  content: '\f105';
  font-family: "FontAwesome", "Font Awesome 5 Pro", sans-serif;
  font-size: 20px;
  color: black;
}
/* line 442, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-w:before {
  content: '\f104';
}
/* line 447, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker .ui-datepicker-next, body .ui-datepicker .ui-datepicker-prev {
  background: none;
}
/* line 451, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-next.ui-state-hover, body .ui-datepicker-prev.ui-datepicker-prev-hover {
  border: 1px solid #636363;
}

/* line 457, ../../../../sass/booking/_booking_engine_5.scss */
.datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-e:before, .datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-w:before {
  display: none;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

/* line 469, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 {
  border-radius: 0;
}
/* line 471, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-skin {
  border-radius: 0;
}
/* line 474, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide > * {
  padding: 0 !important;
  background-color: transparent;
}
/* line 478, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide .calendar_popup_wrapper {
  background-color: transparent;
}
/* line 482, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .full_screen_engine .container_popup_booking {
  background-color: transparent !important;
  padding: 24px !important;
}
/* line 487, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  color: gray;
}
/* line 490, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading {
  width: 50px;
  height: 50px;
  margin: auto;
  position: absolute;
  left: 0;
  right: 0;
  top: 100px;
  bottom: 0;
}
/* line 10, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:before {
  content: '';
  width: 50px;
  height: 5px;
  background: #000;
  opacity: 0.3;
  position: absolute;
  top: 59px;
  left: 0;
  border-radius: 50%;
  animation: box_shadow .5s linear infinite;
}
/* line 22, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:after {
  content: '';
  width: 50px;
  height: 50px;
  background: white;
  animation: box_animate .5s linear infinite;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 3px;
}
@keyframes box_animate {
  17% {
    border-bottom-right-radius: 3px;
  }
  25% {
    transform: translateY(9px) rotate(22.5deg);
  }
  50% {
    transform: translateY(18px) scale(1, 0.9) rotate(45deg);
    border-bottom-right-radius: 40px;
  }
  75% {
    transform: translateY(9px) rotate(67.5deg);
  }
  100% {
    transform: translateY(0) rotate(90deg);
  }
}
@keyframes box_shadow {
  0%, 100% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.2, 1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader {
  padding: 60px 0 70px;
}
/* line 3, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot {
  width: 24px;
  height: 24px;
  margin: 0 3px;
  background: #000;
  border-radius: 100%;
  display: inline-block;
  animation: slide 1s infinite;
}
/* line 11, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(1) {
  animation-delay: 0.1s;
}
/* line 14, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(2) {
  animation-delay: 0.2s;
}
/* line 17, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(3) {
  animation-delay: 0.3s;
}
/* line 20, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(4) {
  animation-delay: 0.4s;
}
/* line 23, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(5) {
  animation-delay: 0.5s;
}
@-moz-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-webkit-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-o-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube {
  margin: 20px auto;
  width: 40px;
  height: 40px;
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}
/* line 7, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube {
  float: left;
  width: 50%;
  height: 50%;
  position: relative;
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}
/* line 15, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  -webkit-animation: sk-foldCubeAngle 2.4s infinite linear both;
  animation: sk-foldCubeAngle 2.4s infinite linear both;
  -webkit-transform-origin: 100% 100%;
  -ms-transform-origin: 100% 100%;
  transform-origin: 100% 100%;
}
/* line 30, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2 {
  -webkit-transform: scale(1.1) rotateZ(90deg);
  transform: scale(1.1) rotateZ(90deg);
}
/* line 34, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3 {
  -webkit-transform: scale(1.1) rotateZ(270deg);
  transform: scale(1.1) rotateZ(270deg);
}
/* line 38, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4 {
  -webkit-transform: scale(1.1) rotateZ(180deg);
  transform: scale(1.1) rotateZ(180deg);
}
/* line 43, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2:before {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
/* line 49, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3:before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
}
/* line 55, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4:before {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
}
@-webkit-keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
@keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
/* line 1, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots {
  padding: 40px 0;
  text-align: center;
  animation: dots-rotate 2s linear infinite;
}
/* line 5, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  content: '';
  animation: dots-margin 2s linear infinite;
}
/* line 9, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots span, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  display: inline-block;
  vertical-align: middle;
  width: 30px;
  height: 30px;
  background-color: white;
  border-radius: 50%;
  margin: 5px;
}
@keyframes dots-margin {
  0% {
    margin: 5px;
  }
  12% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  25% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  50% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  62% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  75% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  100% {
    margin: 5px;
  }
}
@keyframes dots-rotate {
  0% {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  25% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  50% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  75% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/* line 500, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_top_popup_booking {
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: lighter;
  color: gray;
}
/* line 508, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  font-weight: bold;
  text-transform: uppercase;
}
/* line 513, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .container_popup_booking {
  width: 555px;
  display: block;
  padding: 30px 0;
  box-sizing: border-box;
  margin: 7px;
  border: 1px solid #3483b2;
}

/* line 524, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine {
  width: auto !important;
  height: auto !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}
/* line 532, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-outer {
  background: transparent;
}
/* line 537, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine.custom_color_overlay .fancybox-skin {
  background: transparent;
}
/* line 542, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-skin {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}
/* line 551, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-inner {
  overflow: visible;
}
/* line 555, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 568, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  border: 0 !important;
}
/* line 572, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: lighter;
}
/* line 575, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking strong {
  font-weight: bold;
  text-decoration: underline;
  font-size: 14px;
}

/* line 1, ../sass/_booking_engine.scss */
#full_wrapper_booking {
  position: absolute;
  bottom: 0%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.6);
  /*====== Booking engine ======*/
  /*======== Booking Widget =======*/
}
/* line 8, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form_title {
  position: absolute;
  bottom: 100%;
  background: rgba(0, 0, 0, 0.6);
  left: 10px;
  color: white;
  padding: 10px;
  width: 175px;
  font-size: 22px;
  text-align: center;
}
/* line 20, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form_title .link_booking {
  display: inline-block;
  cursor: pointer;
}
/* line 29, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form_title .separator {
  display: inline-block;
  margin: 0 10px;
}
/* line 36, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date, #full_wrapper_booking .date_box.departure_date {
  background: url(/static_1/images/booking_5/entry_date.png) no-repeat center left;
  padding-left: 34px;
}
/* line 41, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.departure_date {
  background: url(/static_1/images/booking_5/departure_date.png) no-repeat center left;
  margin-top: 6px;
}
/* line 46, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectricWrapper .selectric .label {
  font-size: 21px;
  line-height: 35px;
}
/* line 51, ../sass/_booking_engine.scss */
#full_wrapper_booking .departure_date_wrapper, #full_wrapper_booking .half_size, #full_wrapper_booking .departure_date_wrapper, #full_wrapper_booking .half_size, #full_wrapper_booking .room_list_wrapper .room {
  height: 70px;
}
/* line 55, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .adults_selector, #full_wrapper_booking .room_list_wrapper .children_selector {
  height: 68px !important;
  border-right: 1px solid lightgrey;
}
/* line 60, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper {
  height: 62px;
  position: absolute;
  top: 79px;
  left: 530px;
}
/* line 65, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .children_selector .selectricItems {
  left: 6px !important;
}
/* line 70, ../sass/_booking_engine.scss */
#full_wrapper_booking #full_wrapper_booking .room_list_wrapper .room {
  height: 62px;
}
/* line 74, ../sass/_booking_engine.scss */
#full_wrapper_booking #full_wrapper_booking .wrapper_booking_button {
  height: 63px;
}
/* line 78, ../sass/_booking_engine.scss */
#full_wrapper_booking #full_wrapper_booking .wrapper_booking_button .submit_button, #full_wrapper_booking .rooms_number_wrapper {
  height: 70px;
}
/* line 82, ../sass/_booking_engine.scss */
#full_wrapper_booking input.promocode_input {
  background: white url("/static_1/images/booking_5/promocode.png") no-repeat center left;
  padding-left: 35px;
  box-sizing: border-box;
}
/* line 89, ../sass/_booking_engine.scss */
#full_wrapper_booking div#wrapper_booking {
  width: 1121px;
  padding: 10px;
  position: relative;
}
/* line 96, ../sass/_booking_engine.scss */
#full_wrapper_booking div#wrapper_booking.home {
  bottom: 70px;
}
/* line 100, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget {
  position: absolute;
  left: 0px;
}
/* line 105, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 {
  width: 1121px;
}
/* line 108, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 .promocode_header {
  display: none;
}
/* line 113, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 form.booking_form {
  background: white;
}
/* line 117, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form_title .best_price {
  display: none;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}
/* line 125, ../sass/_booking_engine.scss */
#full_wrapper_booking .promocode_header p.first_offer_name {
  color: white;
}
/* line 128, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .date_box, #full_wrapper_booking .booking_widget .selectricWrapper, #full_wrapper_booking #booking_widget_popup .date_box, #full_wrapper_booking #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 132, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .date_box .date_day, #full_wrapper_booking #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 136, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectric {
  height: 38px;
  background: transparent;
}
/* line 141, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .adults_selector, #full_wrapper_booking .room_list_wrapper .children_selector {
  width: 50% !important;
  height: 68px;
  float: left;
  box-sizing: border-box;
}
/* line 148, ../sass/_booking_engine.scss */
#full_wrapper_booking button.submit_button {
  background: #8fc9f1 !important;
  color: white !important;
}
/* line 153, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .web_support_label_1, #full_wrapper_booking .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 158, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support .web_support_number, #full_wrapper_booking .web_support_label_1 {
  line-height: 15px !important;
  font-size: 14px !important;
}
/* line 163, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title {
  margin-top: 0;
  text-align: center;
  /*display: none;*/
}
/* line 169, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title .web_support_label_2 {
  display: inline-block;
  margin: 0 10px;
}
/* line 174, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title .phone_support_image {
  display: none;
}
/* line 183, ../sass/_booking_engine.scss */
#full_wrapper_booking #data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}
/* line 187, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date, #full_wrapper_booking .date_box.departure_date {
  margin-top: 6px;
}
/* line 190, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date .date_year, #full_wrapper_booking .date_box.departure_date .date_year {
  display: none;
}
/* line 194, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date .date_day, #full_wrapper_booking .date_box.departure_date .date_day {
  border-bottom: 0 !important;
}
/* line 199, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectricWrapper {
  width: 100% !important;
}
/* line 202, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectricWrapper .selectric {
  margin-top: 0;
}
/* line 207, ../sass/_booking_engine.scss */
#full_wrapper_booking #slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}
/* line 211, ../sass/_booking_engine.scss */
#full_wrapper_booking .promocode_text {
  display: none;
}
/* line 215, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection {
  display: inline-block;
  vertical-align: top;
  float: left;
}
/* line 220, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection .entry_date_wrapper, #full_wrapper_booking .stay_selection .departure_date_wrapper {
  display: inline-block;
  vertical-align: top;
  width: 175px;
  float: left;
  border-top: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
}
/* line 229, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection .departure_date_wrapper {
  border-left: 0;
  border-right: 0;
}
/* line 234, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection .nights_number_wrapper {
  display: inline-block;
  width: 95px;
  float: left;
  vertical-align: top;
  border-top: 1px solid lightgrey;
}
/* line 243, ../sass/_booking_engine.scss */
#full_wrapper_booking .rooms_number_wrapper {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 169px;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
}
/* line 252, ../sass/_booking_engine.scss */
#full_wrapper_booking .rooms_number_wrapper .rooms_number {
  padding-left: 45px;
  box-sizing: border-box;
  background: url(/static_1/images/booking_5/rooms_number.png) no-repeat center left;
}
/* line 259, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper {
  display: none;
  vertical-align: top;
  float: left;
  border-bottom: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
  width: 199px;
  height: 68px;
  z-index: 100;
}
/* line 269, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room {
  background: white;
  height: 68px;
}
/* line 273, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room.room2 .children_selector, #full_wrapper_booking .room_list_wrapper .room.room3 .children_selector {
  border-right: 1px solid lightgray;
}
/* line 277, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room.room3, #full_wrapper_booking .room_list_wrapper .room.room2 {
  border-bottom: 1px solid lightgray;
}
/* line 281, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room.room3 {
  border-top: 0;
}
/* line 287, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button {
  display: inline-block;
  width: auto;
  float: left;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  height: 69px;
}
/* line 295, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .promocode_wrapper {
  display: inline-block;
  vertical-align: top;
  float: left;
  height: 70px;
  width: 165px;
}
/* line 303, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .submit_button {
  width: 263px;
  display: inline-block;
  vertical-align: top;
  float: left;
  height: 70px;
  border: 1px solid lightgrey;
}
/* line 311, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .submit_button:hover {
  background: #bcdff7 !important;
}

/* line 318, ../sass/_booking_engine.scss */
.babies_selector {
  width: 33.3%;
  display: inline-block;
  padding: 7px 10px 5px;
  box-sizing: border-box;
}
/* line 324, ../sass/_booking_engine.scss */
.babies_selector label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/*=== Ocupancy selector ====*/
/* line 332, ../sass/_booking_engine.scss */
.guest_selector {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 173px;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
  padding: 7px 10px 5px;
  box-sizing: border-box;
  height: 70px;
  cursor: pointer;
}
/* line 345, ../sass/_booking_engine.scss */
.guest_selector span.placeholder_text {
  color: darkgray;
  font-size: 13px;
  font-weight: lighter;
  font-family: sans-serif;
  padding-top: 12px;
  float: left;
  display: block;
  text-transform: uppercase;
  padding-left: 32px;
  box-sizing: border-box;
  background: url(/static_1/images/booking_5/ocupancy.png) no-repeat bottom left;
  padding-bottom: 3px;
}
/* line 359, ../sass/_booking_engine.scss */
.guest_selector span.placeholder_text.selected_value {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 21px;
  padding-top: 3px;
  background-position-y: 8px;
  font-weight: 600;
}
/* line 369, ../sass/_booking_engine.scss */
.guest_selector > label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
  cursor: pointer;
}
/* line 376, ../sass/_booking_engine.scss */
.guest_selector b.button {
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 23px;
  margin-left: 0;
  text-indent: 999px;
  height: 22px;
  font-weight: 600;
  float: right;
}

/* line 389, ../sass/_booking_engine.scss */
#booking label {
  cursor: pointer;
}

/*===== Slider container ====*/
/* line 394, ../sass/_booking_engine.scss */
#slider_container {
  position: relative;
}

/* line 1, ../sass/_booking_widget_modal.scss */
.fancybox-wrap {
  /*======== Booking Widget =======*/
}
/* line 8, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .fancybox-inner {
  width: auto !important;
}
/* line 13, ../sass/_booking_widget_modal.scss */
.fancybox-wrap div#wrapper_booking {
  position: absolute;
  height: 420px;
  top: 145px;
  left: 0px;
  right: 0px;
  z-index: 35;
}
/* line 21, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget {
  position: absolute;
  left: 0px;
}
/* line 26, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header {
  text-align: center;
}
/* line 30, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.first_offer_name {
  display: none;
}
/* line 34, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.second_offer_name {
  display: none;
}
/* line 38, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.second_offer_name_popup {
  font-size: 10px;
  margin-top: 3px;
  color: gray;
}
/* line 44, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.first_offer_name_popup {
  font-size: 16px;
  text-transform: uppercase;
}
/* line 49, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header img.booking_header_discount {
  display: none;
}
/* line 53, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .date_box.entry_date, .fancybox-wrap .date_box.departure_date {
  background: none;
}
/* line 56, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .date_box.entry_date .date_year, .fancybox-wrap .date_box.departure_date .date_year {
  display: none;
}
/* line 61, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 {
  width: 305px;
}
/* line 65, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 form.booking_form {
  background: white;
}
/* line 69, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_form_title .best_price {
  display: block;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}
/* line 77, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.first_offer_name_popup {
  color: white;
}
/* line 80, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .date_box, .fancybox-wrap .booking_widget .selectricWrapper, .fancybox-wrap #booking_widget_popup .date_box, .fancybox-wrap #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 84, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .date_box .date_day, .fancybox-wrap #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 88, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .selectric {
  height: 38px;
  background: transparent;
}
/* line 93, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .room_list_wrapper .adults_selector, .fancybox-wrap .room_list_wrapper .children_selector {
  width: 49.8%;
}
/* line 97, ../sass/_booking_widget_modal.scss */
.fancybox-wrap button.submit_button {
  background: #8fc9f1 !important;
  color: white !important;
}
/* line 101, ../sass/_booking_widget_modal.scss */
.fancybox-wrap button.submit_button:hover {
  background: #bcdff7 !important;
}
/* line 106, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .web_support_label_1, .fancybox-wrap .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 111, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .wrapper-new-web-support .web_support_number, .fancybox-wrap .web_support_label_1 {
  line-height: 15px !important;
}
/* line 115, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .wrapper-new-web-support.booking_form_title {
  background: gray !important;
}
/* line 119, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 {
  margin-top: 20px !important;
}
/* line 123, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}
/* line 127, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .date_box.entry_date {
  margin-top: 6px;
}
/* line 130, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .selectricWrapper .selectric {
  margin-top: 0px;
}
/* line 134, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}
/* line 138, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_text {
  display: none;
}

/* line 143, ../sass/_booking_widget_modal.scss */
.fancy-data {
  padding: 20px !important;
  background: rgba(0, 0, 0, 0.4);
}
/* line 147, ../sass/_booking_widget_modal.scss */
.fancy-data .fancybox-outer {
  background: transparent;
  padding: 0 !important;
}
/* line 152, ../sass/_booking_widget_modal.scss */
.fancy-data #full-booking-engine-html-5 .booking_form_title {
  background: transparent;
  padding: 20px;
  text-align: center;
  font-size: 16px;
  color: white;
}
/* line 160, ../sass/_booking_widget_modal.scss */
.fancy-data .link_booking {
  display: inline-block;
  cursor: pointer;
}
/* line 164, ../sass/_booking_widget_modal.scss */
.fancy-data .link_booking.selected {
  color: #C66047;
}
/* line 169, ../sass/_booking_widget_modal.scss */
.fancy-data .separator {
  display: inline-block;
  margin: 0 10px;
}

/*=====================================================================
  Selectric
======================================================================*/
/* line 5, ../../../../sass/booking/_selectric.scss */
.selectricWrapper {
  position: relative;
  margin: 0 0 0px;
  width: 80px;
  cursor: pointer;
}

/* line 12, ../../../../sass/booking/_selectric.scss */
.selectricDisabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 23, ../../../../sass/booking/_selectric.scss */
.selectricOpen {
  z-index: 9999;
}

/* line 27, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 31, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

/* line 38, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect select {
  position: absolute;
  left: -100%;
  display: none;
}

/* line 44, ../../../../sass/booking/_selectric.scss */
.selectricInput {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  _font: 0/0 a;
  background: none !important;
}

/* line 60, ../../../../sass/booking/_selectric.scss */
.selectricTempShow {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* line 67, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectric {
  z-index: 9999;
}

/* line 72, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 76, ../../../../sass/booking/_selectric.scss */
.selectric {
  background: white;
  position: relative;
  border-radius: 6px;
  height: 40px;
}

/* line 83, ../../../../sass/booking/_selectric.scss */
.selectric .label {
  display: block;
  overflow: hidden;
  font-size: 16px;
  line-height: 40px;
  color: #C66047;
  text-align: left;
  margin-left: 20px;
}

/* line 93, ../../../../sass/booking/_selectric.scss */
.selectric .button {
  zoom: 1;
  position: absolute;
  font: 0/0 a;
  overflow: hidden;
  margin: auto;
  top: 0;
  right: 2px;
  bottom: 0;
  width: 35px;
  height: 35px;
  border: 0;
  background: #C66047 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  padding: 0;
}

/* line 109, ../../../../sass/booking/_selectric.scss */
.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */
/* line 115, ../../../../sass/booking/_selectric.scss */
.selectricItems {
  display: none;
  position: absolute;
  overflow: hidden;
  top: 100%;
  left: 0;
  background: #F9F9F9;
  border: 1px solid #CCC;
  z-index: 9998;
  -webkit-box-shadow: 0 0 10px -6px;
  box-shadow: 0 0 10px -6px;
}

/* line 128, ../../../../sass/booking/_selectric.scss */
.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* line 138, ../../../../sass/booking/_selectric.scss */
.selectricItems li {
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 146, ../../../../sass/booking/_selectric.scss */
.selectricItems li:hover {
  background: #F0F0F0;
  color: #444;
}
/* line 151, ../../../../sass/booking/_selectric.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}

/* line 158, ../../../../sass/booking/_selectric.scss */
.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;
  cursor: default;
}

@font-face {
  font-family: 'FontAwesome';
  src: url("/static_1/fonts/fontawesome/fontawesome/fontawesome-webfont.eot?v=4.7.0");
  src: url("/static_1/fonts/fontawesome/fontawesome-webfont.eot?#iefix&v=4.7.0") format("embedded-opentype"), url("/static_1/fonts/fontawesome/fontawesome-webfont.woff2?v=4.7.0") format("woff2"), url("/static_1/fonts/fontawesome/fontawesome-webfont.woff?v=4.7.0") format("woff"), url("/static_1/fonts/fontawesome/fontawesome-webfont.ttf?v=4.7.0") format("truetype"), url("/static_1/fonts/fontawesome/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular") format("svg");
  font-weight: normal;
  font-style: normal;
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lg {
  font-size: 1.33333333em;
  line-height: .75em;
  vertical-align: -15%;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-3x {
  font-size: 3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-4x {
  font-size: 4em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-5x {
  font-size: 5em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fw {
  width: 1.28571429em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul > li {
  position: relative;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: .14285714em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li.fa-lg {
  left: -1.85714286em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-border {
  padding: .2em .25em .15em;
  border: solid .08em #eee;
  border-radius: .1em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-left {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-right {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-left {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-right {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  transform: scale(-1, 1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical {
  filter: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x, .fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x {
  line-height: inherit;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inverse {
  color: #fff;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glass:before {
  content: "\f000";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-music:before {
  content: "\f001";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search:before {
  content: "\f002";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-o:before {
  content: "\f003";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart:before {
  content: "\f004";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star:before {
  content: "\f005";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-o:before {
  content: "\f006";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user:before {
  content: "\f007";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-film:before {
  content: "\f008";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-large:before {
  content: "\f009";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th:before {
  content: "\f00a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-list:before {
  content: "\f00b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check:before {
  content: "\f00c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-remove:before, .fa-close:before, .fa-times:before {
  content: "\f00d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-plus:before {
  content: "\f00e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-minus:before {
  content: "\f010";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-power-off:before {
  content: "\f011";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signal:before {
  content: "\f012";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gear:before, .fa-cog:before {
  content: "\f013";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash-o:before {
  content: "\f014";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-home:before {
  content: "\f015";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-o:before {
  content: "\f016";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clock-o:before {
  content: "\f017";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-road:before {
  content: "\f018";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-download:before {
  content: "\f019";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-down:before {
  content: "\f01a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-up:before {
  content: "\f01b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inbox:before {
  content: "\f01c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle-o:before {
  content: "\f01d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-right:before, .fa-repeat:before {
  content: "\f01e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-refresh:before {
  content: "\f021";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-alt:before {
  content: "\f022";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lock:before {
  content: "\f023";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag:before {
  content: "\f024";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-headphones:before {
  content: "\f025";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-off:before {
  content: "\f026";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-down:before {
  content: "\f027";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-up:before {
  content: "\f028";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qrcode:before {
  content: "\f029";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-barcode:before {
  content: "\f02a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tag:before {
  content: "\f02b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tags:before {
  content: "\f02c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-book:before {
  content: "\f02d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark:before {
  content: "\f02e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-print:before {
  content: "\f02f";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera:before {
  content: "\f030";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-font:before {
  content: "\f031";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bold:before {
  content: "\f032";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-italic:before {
  content: "\f033";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-height:before {
  content: "\f034";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-width:before {
  content: "\f035";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-left:before {
  content: "\f036";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-center:before {
  content: "\f037";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-right:before {
  content: "\f038";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-justify:before {
  content: "\f039";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list:before {
  content: "\f03a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dedent:before, .fa-outdent:before {
  content: "\f03b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-indent:before {
  content: "\f03c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-video-camera:before {
  content: "\f03d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-photo:before, .fa-image:before, .fa-picture-o:before {
  content: "\f03e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil:before {
  content: "\f040";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-marker:before {
  content: "\f041";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adjust:before {
  content: "\f042";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tint:before {
  content: "\f043";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edit:before, .fa-pencil-square-o:before {
  content: "\f044";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square-o:before {
  content: "\f045";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square-o:before {
  content: "\f046";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows:before {
  content: "\f047";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-backward:before {
  content: "\f048";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-backward:before {
  content: "\f049";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-backward:before {
  content: "\f04a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play:before {
  content: "\f04b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause:before {
  content: "\f04c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop:before {
  content: "\f04d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forward:before {
  content: "\f04e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-forward:before {
  content: "\f050";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-forward:before {
  content: "\f051";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eject:before {
  content: "\f052";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-left:before {
  content: "\f053";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-right:before {
  content: "\f054";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-circle:before {
  content: "\f055";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-circle:before {
  content: "\f056";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle:before {
  content: "\f057";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle:before {
  content: "\f058";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle:before {
  content: "\f059";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info-circle:before {
  content: "\f05a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crosshairs:before {
  content: "\f05b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle-o:before {
  content: "\f05c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle-o:before {
  content: "\f05d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ban:before {
  content: "\f05e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-left:before {
  content: "\f060";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-right:before {
  content: "\f061";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-up:before {
  content: "\f062";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-down:before {
  content: "\f063";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-forward:before, .fa-share:before {
  content: "\f064";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expand:before {
  content: "\f065";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compress:before {
  content: "\f066";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus:before {
  content: "\f067";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus:before {
  content: "\f068";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asterisk:before {
  content: "\f069";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation-circle:before {
  content: "\f06a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gift:before {
  content: "\f06b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leaf:before {
  content: "\f06c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire:before {
  content: "\f06d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye:before {
  content: "\f06e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye-slash:before {
  content: "\f070";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-warning:before, .fa-exclamation-triangle:before {
  content: "\f071";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plane:before {
  content: "\f072";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar:before {
  content: "\f073";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-random:before {
  content: "\f074";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment:before {
  content: "\f075";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magnet:before {
  content: "\f076";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-up:before {
  content: "\f077";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-down:before {
  content: "\f078";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-retweet:before {
  content: "\f079";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-cart:before {
  content: "\f07a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder:before {
  content: "\f07b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open:before {
  content: "\f07c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-v:before {
  content: "\f07d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-h:before {
  content: "\f07e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bar-chart-o:before, .fa-bar-chart:before {
  content: "\f080";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter-square:before {
  content: "\f081";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-square:before {
  content: "\f082";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera-retro:before {
  content: "\f083";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-key:before {
  content: "\f084";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gears:before, .fa-cogs:before {
  content: "\f085";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments:before {
  content: "\f086";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-up:before {
  content: "\f087";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-down:before {
  content: "\f088";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half:before {
  content: "\f089";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart-o:before {
  content: "\f08a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-out:before {
  content: "\f08b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin-square:before {
  content: "\f08c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumb-tack:before {
  content: "\f08d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link:before {
  content: "\f08e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-in:before {
  content: "\f090";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trophy:before {
  content: "\f091";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-square:before {
  content: "\f092";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-upload:before {
  content: "\f093";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lemon-o:before {
  content: "\f094";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone:before {
  content: "\f095";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square-o:before {
  content: "\f096";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark-o:before {
  content: "\f097";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone-square:before {
  content: "\f098";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter:before {
  content: "\f099";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-f:before, .fa-facebook:before {
  content: "\f09a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github:before {
  content: "\f09b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock:before {
  content: "\f09c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card:before {
  content: "\f09d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-feed:before, .fa-rss:before {
  content: "\f09e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hdd-o:before {
  content: "\f0a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullhorn:before {
  content: "\f0a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell:before {
  content: "\f0f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-certificate:before {
  content: "\f0a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-right:before {
  content: "\f0a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-left:before {
  content: "\f0a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-up:before {
  content: "\f0a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-down:before {
  content: "\f0a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-left:before {
  content: "\f0a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-right:before {
  content: "\f0a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-up:before {
  content: "\f0aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-down:before {
  content: "\f0ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-globe:before {
  content: "\f0ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wrench:before {
  content: "\f0ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tasks:before {
  content: "\f0ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-filter:before {
  content: "\f0b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-briefcase:before {
  content: "\f0b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-alt:before {
  content: "\f0b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-group:before, .fa-users:before {
  content: "\f0c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chain:before, .fa-link:before {
  content: "\f0c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud:before {
  content: "\f0c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flask:before {
  content: "\f0c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cut:before, .fa-scissors:before {
  content: "\f0c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copy:before, .fa-files-o:before {
  content: "\f0c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paperclip:before {
  content: "\f0c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-save:before, .fa-floppy-o:before {
  content: "\f0c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square:before {
  content: "\f0c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-navicon:before, .fa-reorder:before, .fa-bars:before {
  content: "\f0c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ul:before {
  content: "\f0ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ol:before {
  content: "\f0cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-strikethrough:before {
  content: "\f0cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-underline:before {
  content: "\f0cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-table:before {
  content: "\f0ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magic:before {
  content: "\f0d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-truck:before {
  content: "\f0d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest:before {
  content: "\f0d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-square:before {
  content: "\f0d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-square:before {
  content: "\f0d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus:before {
  content: "\f0d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-money:before {
  content: "\f0d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-down:before {
  content: "\f0d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-up:before {
  content: "\f0d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-left:before {
  content: "\f0d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-right:before {
  content: "\f0da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-columns:before {
  content: "\f0db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unsorted:before, .fa-sort:before {
  content: "\f0dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-down:before, .fa-sort-desc:before {
  content: "\f0dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-up:before, .fa-sort-asc:before {
  content: "\f0de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope:before {
  content: "\f0e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin:before {
  content: "\f0e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-left:before, .fa-undo:before {
  content: "\f0e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-legal:before, .fa-gavel:before {
  content: "\f0e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashboard:before, .fa-tachometer:before {
  content: "\f0e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment-o:before {
  content: "\f0e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments-o:before {
  content: "\f0e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flash:before, .fa-bolt:before {
  content: "\f0e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sitemap:before {
  content: "\f0e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-umbrella:before {
  content: "\f0e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paste:before, .fa-clipboard:before {
  content: "\f0ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lightbulb-o:before {
  content: "\f0eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exchange:before {
  content: "\f0ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-download:before {
  content: "\f0ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-upload:before {
  content: "\f0ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-md:before {
  content: "\f0f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stethoscope:before {
  content: "\f0f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-suitcase:before {
  content: "\f0f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-o:before {
  content: "\f0a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-coffee:before {
  content: "\f0f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cutlery:before {
  content: "\f0f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text-o:before {
  content: "\f0f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building-o:before {
  content: "\f0f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hospital-o:before {
  content: "\f0f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ambulance:before {
  content: "\f0f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medkit:before {
  content: "\f0fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fighter-jet:before {
  content: "\f0fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-beer:before {
  content: "\f0fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-h-square:before {
  content: "\f0fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square:before {
  content: "\f0fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-left:before {
  content: "\f100";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-right:before {
  content: "\f101";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-up:before {
  content: "\f102";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-down:before {
  content: "\f103";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-left:before {
  content: "\f104";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-right:before {
  content: "\f105";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-up:before {
  content: "\f106";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-down:before {
  content: "\f107";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-desktop:before {
  content: "\f108";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-laptop:before {
  content: "\f109";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tablet:before {
  content: "\f10a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mobile-phone:before, .fa-mobile:before {
  content: "\f10b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o:before {
  content: "\f10c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-left:before {
  content: "\f10d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-right:before {
  content: "\f10e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spinner:before {
  content: "\f110";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle:before {
  content: "\f111";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply:before, .fa-reply:before {
  content: "\f112";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-alt:before {
  content: "\f113";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-o:before {
  content: "\f114";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open-o:before {
  content: "\f115";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-smile-o:before {
  content: "\f118";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-frown-o:before {
  content: "\f119";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meh-o:before {
  content: "\f11a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gamepad:before {
  content: "\f11b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-keyboard-o:before {
  content: "\f11c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-o:before {
  content: "\f11d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-checkered:before {
  content: "\f11e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-terminal:before {
  content: "\f120";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code:before {
  content: "\f121";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply-all:before, .fa-reply-all:before {
  content: "\f122";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before {
  content: "\f123";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-location-arrow:before {
  content: "\f124";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crop:before {
  content: "\f125";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code-fork:before {
  content: "\f126";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlink:before, .fa-chain-broken:before {
  content: "\f127";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question:before {
  content: "\f128";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info:before {
  content: "\f129";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation:before {
  content: "\f12a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superscript:before {
  content: "\f12b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subscript:before {
  content: "\f12c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eraser:before {
  content: "\f12d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-puzzle-piece:before {
  content: "\f12e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone:before {
  content: "\f130";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone-slash:before {
  content: "\f131";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shield:before {
  content: "\f132";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-o:before {
  content: "\f133";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire-extinguisher:before {
  content: "\f134";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rocket:before {
  content: "\f135";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-maxcdn:before {
  content: "\f136";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-left:before {
  content: "\f137";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-right:before {
  content: "\f138";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-up:before {
  content: "\f139";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-down:before {
  content: "\f13a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-html5:before {
  content: "\f13b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-css3:before {
  content: "\f13c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-anchor:before {
  content: "\f13d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock-alt:before {
  content: "\f13e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullseye:before {
  content: "\f140";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-h:before {
  content: "\f141";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-v:before {
  content: "\f142";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rss-square:before {
  content: "\f143";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle:before {
  content: "\f144";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ticket:before {
  content: "\f145";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square:before {
  content: "\f146";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square-o:before {
  content: "\f147";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-up:before {
  content: "\f148";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-down:before {
  content: "\f149";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square:before {
  content: "\f14a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil-square:before {
  content: "\f14b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link-square:before {
  content: "\f14c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square:before {
  content: "\f14d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compass:before {
  content: "\f14e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-down:before, .fa-caret-square-o-down:before {
  content: "\f150";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-up:before, .fa-caret-square-o-up:before {
  content: "\f151";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-right:before, .fa-caret-square-o-right:before {
  content: "\f152";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-euro:before, .fa-eur:before {
  content: "\f153";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gbp:before {
  content: "\f154";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dollar:before, .fa-usd:before {
  content: "\f155";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rupee:before, .fa-inr:before {
  content: "\f156";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before {
  content: "\f157";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ruble:before, .fa-rouble:before, .fa-rub:before {
  content: "\f158";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-won:before, .fa-krw:before {
  content: "\f159";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitcoin:before, .fa-btc:before {
  content: "\f15a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file:before {
  content: "\f15b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text:before {
  content: "\f15c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-asc:before {
  content: "\f15d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-desc:before {
  content: "\f15e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-asc:before {
  content: "\f160";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-desc:before {
  content: "\f161";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-asc:before {
  content: "\f162";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-desc:before {
  content: "\f163";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-up:before {
  content: "\f164";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-down:before {
  content: "\f165";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-square:before {
  content: "\f166";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube:before {
  content: "\f167";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing:before {
  content: "\f168";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing-square:before {
  content: "\f169";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-play:before {
  content: "\f16a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dropbox:before {
  content: "\f16b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-overflow:before {
  content: "\f16c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-instagram:before {
  content: "\f16d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flickr:before {
  content: "\f16e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adn:before {
  content: "\f170";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket:before {
  content: "\f171";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket-square:before {
  content: "\f172";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr:before {
  content: "\f173";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr-square:before {
  content: "\f174";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-down:before {
  content: "\f175";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-up:before {
  content: "\f176";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-left:before {
  content: "\f177";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-right:before {
  content: "\f178";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-apple:before {
  content: "\f179";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-windows:before {
  content: "\f17a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-android:before {
  content: "\f17b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linux:before {
  content: "\f17c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dribbble:before {
  content: "\f17d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skype:before {
  content: "\f17e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-foursquare:before {
  content: "\f180";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trello:before {
  content: "\f181";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-female:before {
  content: "\f182";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-male:before {
  content: "\f183";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gittip:before, .fa-gratipay:before {
  content: "\f184";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sun-o:before {
  content: "\f185";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-moon-o:before {
  content: "\f186";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-archive:before {
  content: "\f187";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bug:before {
  content: "\f188";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vk:before {
  content: "\f189";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-weibo:before {
  content: "\f18a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-renren:before {
  content: "\f18b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pagelines:before {
  content: "\f18c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-exchange:before {
  content: "\f18d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-right:before {
  content: "\f18e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-left:before {
  content: "\f190";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-left:before, .fa-caret-square-o-left:before {
  content: "\f191";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dot-circle-o:before {
  content: "\f192";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair:before {
  content: "\f193";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo-square:before {
  content: "\f194";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-turkish-lira:before, .fa-try:before {
  content: "\f195";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square-o:before {
  content: "\f196";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-space-shuttle:before {
  content: "\f197";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slack:before {
  content: "\f198";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-square:before {
  content: "\f199";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wordpress:before {
  content: "\f19a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-openid:before {
  content: "\f19b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-institution:before, .fa-bank:before, .fa-university:before {
  content: "\f19c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mortar-board:before, .fa-graduation-cap:before {
  content: "\f19d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yahoo:before {
  content: "\f19e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google:before {
  content: "\f1a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit:before {
  content: "\f1a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-square:before {
  content: "\f1a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon-circle:before {
  content: "\f1a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon:before {
  content: "\f1a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-delicious:before {
  content: "\f1a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-digg:before {
  content: "\f1a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-pp:before {
  content: "\f1a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-alt:before {
  content: "\f1a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drupal:before {
  content: "\f1a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-joomla:before {
  content: "\f1aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-language:before {
  content: "\f1ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fax:before {
  content: "\f1ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building:before {
  content: "\f1ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-child:before {
  content: "\f1ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paw:before {
  content: "\f1b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spoon:before {
  content: "\f1b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cube:before {
  content: "\f1b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cubes:before {
  content: "\f1b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance:before {
  content: "\f1b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance-square:before {
  content: "\f1b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam:before {
  content: "\f1b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam-square:before {
  content: "\f1b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-recycle:before {
  content: "\f1b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-automobile:before, .fa-car:before {
  content: "\f1b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cab:before, .fa-taxi:before {
  content: "\f1ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tree:before {
  content: "\f1bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spotify:before {
  content: "\f1bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deviantart:before {
  content: "\f1bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soundcloud:before {
  content: "\f1be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-database:before {
  content: "\f1c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-pdf-o:before {
  content: "\f1c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-word-o:before {
  content: "\f1c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-excel-o:before {
  content: "\f1c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-powerpoint-o:before {
  content: "\f1c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before {
  content: "\f1c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-zip-o:before, .fa-file-archive-o:before {
  content: "\f1c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-sound-o:before, .fa-file-audio-o:before {
  content: "\f1c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-movie-o:before, .fa-file-video-o:before {
  content: "\f1c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-code-o:before {
  content: "\f1c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vine:before {
  content: "\f1ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codepen:before {
  content: "\f1cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-jsfiddle:before {
  content: "\f1cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before {
  content: "\f1cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o-notch:before {
  content: "\f1ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ra:before, .fa-resistance:before, .fa-rebel:before {
  content: "\f1d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ge:before, .fa-empire:before {
  content: "\f1d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git-square:before {
  content: "\f1d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git:before {
  content: "\f1d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before {
  content: "\f1d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tencent-weibo:before {
  content: "\f1d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qq:before {
  content: "\f1d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wechat:before, .fa-weixin:before {
  content: "\f1d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send:before, .fa-paper-plane:before {
  content: "\f1d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send-o:before, .fa-paper-plane-o:before {
  content: "\f1d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-history:before {
  content: "\f1da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-thin:before {
  content: "\f1db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-header:before {
  content: "\f1dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paragraph:before {
  content: "\f1dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sliders:before {
  content: "\f1de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt:before {
  content: "\f1e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt-square:before {
  content: "\f1e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bomb:before {
  content: "\f1e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soccer-ball-o:before, .fa-futbol-o:before {
  content: "\f1e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tty:before {
  content: "\f1e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-binoculars:before {
  content: "\f1e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plug:before {
  content: "\f1e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slideshare:before {
  content: "\f1e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitch:before {
  content: "\f1e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yelp:before {
  content: "\f1e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-newspaper-o:before {
  content: "\f1ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wifi:before {
  content: "\f1eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calculator:before {
  content: "\f1ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paypal:before {
  content: "\f1ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-wallet:before {
  content: "\f1ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-visa:before {
  content: "\f1f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-mastercard:before {
  content: "\f1f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-discover:before {
  content: "\f1f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-amex:before {
  content: "\f1f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-paypal:before {
  content: "\f1f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-stripe:before {
  content: "\f1f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash:before {
  content: "\f1f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash-o:before {
  content: "\f1f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash:before {
  content: "\f1f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copyright:before {
  content: "\f1f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-at:before {
  content: "\f1fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eyedropper:before {
  content: "\f1fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paint-brush:before {
  content: "\f1fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-birthday-cake:before {
  content: "\f1fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-area-chart:before {
  content: "\f1fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pie-chart:before {
  content: "\f200";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-line-chart:before {
  content: "\f201";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm:before {
  content: "\f202";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm-square:before {
  content: "\f203";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-off:before {
  content: "\f204";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-on:before {
  content: "\f205";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bicycle:before {
  content: "\f206";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bus:before {
  content: "\f207";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ioxhost:before {
  content: "\f208";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angellist:before {
  content: "\f209";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc:before {
  content: "\f20a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shekel:before, .fa-sheqel:before, .fa-ils:before {
  content: "\f20b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meanpath:before {
  content: "\f20c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-buysellads:before {
  content: "\f20d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-connectdevelop:before {
  content: "\f20e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashcube:before {
  content: "\f210";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forumbee:before {
  content: "\f211";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leanpub:before {
  content: "\f212";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sellsy:before {
  content: "\f213";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shirtsinbulk:before {
  content: "\f214";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-simplybuilt:before {
  content: "\f215";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skyatlas:before {
  content: "\f216";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-plus:before {
  content: "\f217";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-arrow-down:before {
  content: "\f218";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-diamond:before {
  content: "\f219";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ship:before {
  content: "\f21a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-secret:before {
  content: "\f21b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-motorcycle:before {
  content: "\f21c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-street-view:before {
  content: "\f21d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heartbeat:before {
  content: "\f21e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus:before {
  content: "\f221";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars:before {
  content: "\f222";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mercury:before {
  content: "\f223";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-intersex:before, .fa-transgender:before {
  content: "\f224";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-transgender-alt:before {
  content: "\f225";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-double:before {
  content: "\f226";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-double:before {
  content: "\f227";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-mars:before {
  content: "\f228";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke:before {
  content: "\f229";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-v:before {
  content: "\f22a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-h:before {
  content: "\f22b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-neuter:before {
  content: "\f22c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-genderless:before {
  content: "\f22d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-official:before {
  content: "\f230";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-p:before {
  content: "\f231";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-whatsapp:before {
  content: "\f232";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-server:before {
  content: "\f233";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-plus:before {
  content: "\f234";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-times:before {
  content: "\f235";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hotel:before, .fa-bed:before {
  content: "\f236";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viacoin:before {
  content: "\f237";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-train:before {
  content: "\f238";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subway:before {
  content: "\f239";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medium:before {
  content: "\f23a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yc:before, .fa-y-combinator:before {
  content: "\f23b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-optin-monster:before {
  content: "\f23c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opencart:before {
  content: "\f23d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expeditedssl:before {
  content: "\f23e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before {
  content: "\f240";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-3:before, .fa-battery-three-quarters:before {
  content: "\f241";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-2:before, .fa-battery-half:before {
  content: "\f242";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-1:before, .fa-battery-quarter:before {
  content: "\f243";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-0:before, .fa-battery-empty:before {
  content: "\f244";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mouse-pointer:before {
  content: "\f245";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-i-cursor:before {
  content: "\f246";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-group:before {
  content: "\f247";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-ungroup:before {
  content: "\f248";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note:before {
  content: "\f249";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note-o:before {
  content: "\f24a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-jcb:before {
  content: "\f24b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-diners-club:before {
  content: "\f24c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clone:before {
  content: "\f24d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-balance-scale:before {
  content: "\f24e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-o:before {
  content: "\f250";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-1:before, .fa-hourglass-start:before {
  content: "\f251";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-2:before, .fa-hourglass-half:before {
  content: "\f252";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-3:before, .fa-hourglass-end:before {
  content: "\f253";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass:before {
  content: "\f254";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-grab-o:before, .fa-hand-rock-o:before {
  content: "\f255";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-stop-o:before, .fa-hand-paper-o:before {
  content: "\f256";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-scissors-o:before {
  content: "\f257";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-lizard-o:before {
  content: "\f258";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-spock-o:before {
  content: "\f259";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-pointer-o:before {
  content: "\f25a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-peace-o:before {
  content: "\f25b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trademark:before {
  content: "\f25c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-registered:before {
  content: "\f25d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-creative-commons:before {
  content: "\f25e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg:before {
  content: "\f260";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg-circle:before {
  content: "\f261";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tripadvisor:before {
  content: "\f262";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki:before {
  content: "\f263";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki-square:before {
  content: "\f264";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-get-pocket:before {
  content: "\f265";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wikipedia-w:before {
  content: "\f266";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-safari:before {
  content: "\f267";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chrome:before {
  content: "\f268";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-firefox:before {
  content: "\f269";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opera:before {
  content: "\f26a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-internet-explorer:before {
  content: "\f26b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tv:before, .fa-television:before {
  content: "\f26c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-contao:before {
  content: "\f26d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-500px:before {
  content: "\f26e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-amazon:before {
  content: "\f270";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-plus-o:before {
  content: "\f271";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-minus-o:before {
  content: "\f272";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-times-o:before {
  content: "\f273";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-check-o:before {
  content: "\f274";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-industry:before {
  content: "\f275";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-pin:before {
  content: "\f276";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-signs:before {
  content: "\f277";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-o:before {
  content: "\f278";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map:before {
  content: "\f279";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting:before {
  content: "\f27a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting-o:before {
  content: "\f27b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-houzz:before {
  content: "\f27c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo:before {
  content: "\f27d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-black-tie:before {
  content: "\f27e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fonticons:before {
  content: "\f280";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-alien:before {
  content: "\f281";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edge:before {
  content: "\f282";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card-alt:before {
  content: "\f283";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codiepie:before {
  content: "\f284";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-modx:before {
  content: "\f285";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fort-awesome:before {
  content: "\f286";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-usb:before {
  content: "\f287";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-product-hunt:before {
  content: "\f288";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mixcloud:before {
  content: "\f289";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-scribd:before {
  content: "\f28a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle:before {
  content: "\f28b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle-o:before {
  content: "\f28c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle:before {
  content: "\f28d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle-o:before {
  content: "\f28e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-bag:before {
  content: "\f290";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-basket:before {
  content: "\f291";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hashtag:before {
  content: "\f292";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth:before {
  content: "\f293";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth-b:before {
  content: "\f294";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-percent:before {
  content: "\f295";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gitlab:before {
  content: "\f296";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpbeginner:before {
  content: "\f297";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpforms:before {
  content: "\f298";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envira:before {
  content: "\f299";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-universal-access:before {
  content: "\f29a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair-alt:before {
  content: "\f29b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle-o:before {
  content: "\f29c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-blind:before {
  content: "\f29d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-audio-description:before {
  content: "\f29e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-control-phone:before {
  content: "\f2a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-braille:before {
  content: "\f2a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-assistive-listening-systems:before {
  content: "\f2a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before {
  content: "\f2a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide:before {
  content: "\f2a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide-g:before {
  content: "\f2a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signing:before, .fa-sign-language:before {
  content: "\f2a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-low-vision:before {
  content: "\f2a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo:before {
  content: "\f2a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo-square:before {
  content: "\f2aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat:before {
  content: "\f2ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-ghost:before {
  content: "\f2ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-square:before {
  content: "\f2ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper:before {
  content: "\f2ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-first-order:before {
  content: "\f2b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yoast:before {
  content: "\f2b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-themeisle:before {
  content: "\f2b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-circle:before, .fa-google-plus-official:before {
  content: "\f2b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fa:before, .fa-font-awesome:before {
  content: "\f2b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-handshake-o:before {
  content: "\f2b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open:before {
  content: "\f2b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open-o:before {
  content: "\f2b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linode:before {
  content: "\f2b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book:before {
  content: "\f2b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book-o:before {
  content: "\f2ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard:before, .fa-address-card:before {
  content: "\f2bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard-o:before, .fa-address-card-o:before {
  content: "\f2bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle:before {
  content: "\f2bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle-o:before {
  content: "\f2be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-o:before {
  content: "\f2c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-id-badge:before {
  content: "\f2c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license:before, .fa-id-card:before {
  content: "\f2c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license-o:before, .fa-id-card-o:before {
  content: "\f2c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quora:before {
  content: "\f2c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-free-code-camp:before {
  content: "\f2c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-telegram:before {
  content: "\f2c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before {
  content: "\f2c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-3:before, .fa-thermometer-three-quarters:before {
  content: "\f2c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-2:before, .fa-thermometer-half:before {
  content: "\f2c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-1:before, .fa-thermometer-quarter:before {
  content: "\f2ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-0:before, .fa-thermometer-empty:before {
  content: "\f2cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shower:before {
  content: "\f2cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bathtub:before, .fa-s15:before, .fa-bath:before {
  content: "\f2cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-podcast:before {
  content: "\f2ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-maximize:before {
  content: "\f2d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-minimize:before {
  content: "\f2d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-restore:before {
  content: "\f2d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle:before, .fa-window-close:before {
  content: "\f2d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle-o:before, .fa-window-close-o:before {
  content: "\f2d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bandcamp:before {
  content: "\f2d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-grav:before {
  content: "\f2d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-etsy:before {
  content: "\f2d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-imdb:before {
  content: "\f2d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ravelry:before {
  content: "\f2d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eercast:before {
  content: "\f2da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microchip:before {
  content: "\f2db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snowflake-o:before {
  content: "\f2dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superpowers:before {
  content: "\f2dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpexplorer:before {
  content: "\f2de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meetup:before {
  content: "\f2e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

/*=== General ===*/
/* line 2, ../sass/_template_specific.scss */
body {
  font-family: 'Raleway', sans-serif;
}

/* line 7, ../sass/_template_specific.scss */
body.interior section#inner_slider_container {
  height: 468px;
  width: 100%;
  display: inline-block;
  position: relative;
}
/* line 14, ../sass/_template_specific.scss */
body.interior section#inner_slider_container .exceded_image_slider {
  height: 468px;
  overflow: hidden;
  position: relative;
}
/* line 19, ../sass/_template_specific.scss */
body.interior section#inner_slider_container .exceded_image_slider img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  max-width: none;
  min-width: 100%;
}
/* line 25, ../sass/_template_specific.scss */
body.interior section#inner_slider_container .exceded_image_slider .overlay_top {
  position: absolute;
  top: 0;
  width: 100%;
  height: 150px;
  z-index: 100;
  background: -moz-linear-gradient(top, rgba(0, 0, 0, 0.85) 35%, rgba(0, 0, 0, 0.85) 25%, transparent 100%);
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0.85) 35%, rgba(0, 0, 0, 0.85) 25%, transparent 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.85) 35%, rgba(0, 0, 0, 0.85) 25%, rgba(0, 0, 0, 0) 100%);
}
/* line 38, ../sass/_template_specific.scss */
body.interior section#inner_slider_container #full_wrapper_booking {
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/
/* line 47, ../sass/_template_specific.scss */
.ui-state-default {
  border: 1px solid white !important;
}

/* line 51, ../sass/_template_specific.scss */
.ui-datepicker-title {
  color: white !important;
}

/* line 55, ../sass/_template_specific.scss */
.ui-widget-header {
  background: #C66047 !important;
}

/* line 59, ../sass/_template_specific.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #C66047 !important;
  color: white;
}

/* line 64, ../sass/_template_specific.scss */
.wrapper-new-web-support.booking_form_title {
  position: relative !important;
  bottom: auto;
  top: auto;
  background: none !important;
  left: 0 !important;
  padding: 10px !important;
  padding-bottom: 0 !important;
}

/* line 74, ../sass/_template_specific.scss */
.fancy-data #full-booking-engine-html-5 .wrapper-new-web-support.booking_form_title {
  margin-top: 0 !important;
  padding-bottom: 10px !important;
}

/*=== Header ===*/
/* line 80, ../sass/_template_specific.scss */
header {
  width: 100%;
  z-index: 101;
  background: transparent;
  min-width: 1140px;
  top: 0;
  position: absolute;
}
/* line 88, ../sass/_template_specific.scss */
header div#logoDiv {
  margin-top: 14px;
  margin-left: 0;
}
/* line 93, ../sass/_template_specific.scss */
header .left_header {
  float: left;
  padding: 3px 0;
}
/* line 97, ../sass/_template_specific.scss */
header .left_header .tick_header_element {
  display: inline-block;
  vertical-align: middle;
  margin-right: 10px;
}
/* line 102, ../sass/_template_specific.scss */
header .left_header .tick_header_element:last-child {
  margin-right: 0;
}
/* line 106, ../sass/_template_specific.scss */
header .left_header .tick_header_element .icon {
  display: inline-block;
  vertical-align: middle;
  margin-right: 5px;
}
/* line 112, ../sass/_template_specific.scss */
header .left_header .tick_header_element a, header .left_header .tick_header_element span {
  display: inline-block;
  vertical-align: middle;
  color: white;
  text-decoration: none;
  font-size: 14px;
}
/* line 119, ../sass/_template_specific.scss */
header .left_header .tick_header_element a.phone, header .left_header .tick_header_element span.phone {
  font-size: 18px;
}
/* line 124, ../sass/_template_specific.scss */
header .left_header .tick_header_element a:hover {
  color: #C66047;
}
/* line 130, ../sass/_template_specific.scss */
header .right_header {
  float: right;
}
/* line 133, ../sass/_template_specific.scss */
header .right_header .separator {
  display: inline-block;
  vertical-align: middle;
}
/* line 138, ../sass/_template_specific.scss */
header .right_header .link_maps {
  display: inline-block;
  vertical-align: middle;
}
/* line 142, ../sass/_template_specific.scss */
header .right_header .link_maps a {
  text-decoration: none;
  color: white;
  font-size: 14px;
}
/* line 147, ../sass/_template_specific.scss */
header .right_header .link_maps a:hover {
  color: #C66047;
}
/* line 153, ../sass/_template_specific.scss */
header .right_header .top_sections_wrapper {
  display: inline-block;
  vertical-align: middle;
}
/* line 157, ../sass/_template_specific.scss */
header .right_header .top_sections_wrapper .top_element {
  display: inline-block;
  vertical-align: middle;
}
/* line 161, ../sass/_template_specific.scss */
header .right_header .top_sections_wrapper .top_element a {
  display: inline-block;
  color: white;
  text-decoration: none;
  font-size: 14px;
}
/* line 167, ../sass/_template_specific.scss */
header .right_header .top_sections_wrapper .top_element a:hover {
  color: #C66047;
}

/* line 176, ../sass/_template_specific.scss */
.top_header {
  text-align: right;
  margin-top: 14px;
  float: right;
  margin-right: 0;
  color: white;
  font-weight: lighter;
  font-size: 14px;
}

/* line 186, ../sass/_template_specific.scss */
#lang {
  display: inline-block;
  vertical-align: middle;
}
/* line 190, ../sass/_template_specific.scss */
#lang ul li {
  text-align: left;
  font-size: 14px;
  padding: 5px;
  cursor: pointer;
  display: inline-block;
  color: white;
}
/* line 199, ../sass/_template_specific.scss */
#lang ul li a {
  color: white;
  text-decoration: none;
  font-size: 14px;
  text-transform: uppercase;
}
/* line 206, ../sass/_template_specific.scss */
#lang ul li.language_selected a {
  color: #C66047;
  font-weight: bold;
}

/*===== Menu =====*/
/* line 214, ../sass/_template_specific.scss */
#mainMenuDiv {
  margin-top: 15px;
  float: right;
  margin-right: 0;
}
/* line 219, ../sass/_template_specific.scss */
#mainMenuDiv ul {
  text-align: justify;
}
/* line 222, ../sass/_template_specific.scss */
#mainMenuDiv ul:after {
  content: "";
  width: 100%;
  display: inline-block;
  height: 0;
}
/* line 229, ../sass/_template_specific.scss */
#mainMenuDiv ul .separator {
  display: inline-block;
  height: 25px;
  border-left: 1px solid white;
  vertical-align: middle;
}
/* line 236, ../sass/_template_specific.scss */
#mainMenuDiv ul li {
  display: inline-block;
  text-align: center;
  position: relative;
}
/* line 241, ../sass/_template_specific.scss */
#mainMenuDiv ul li:first-of-type {
  padding-left: 0;
}
/* line 245, ../sass/_template_specific.scss */
#mainMenuDiv ul li:nth-last-of-type(2), #mainMenuDiv ul li:last-of-type {
  border-right: 0;
}
/* line 249, ../sass/_template_specific.scss */
#mainMenuDiv ul li:last-of-type {
  padding-right: 0;
}
/* line 253, ../sass/_template_specific.scss */
#mainMenuDiv ul li a {
  text-decoration: none;
  font-size: 16px;
  font-weight: lighter;
  color: white;
  padding: 6px 0 5px;
}
/* line 260, ../sass/_template_specific.scss */
#mainMenuDiv ul li a.button-promotion {
  color: white !important;
  background: #8fc9f1;
  font-weight: bold;
  font-size: 12px;
  text-transform: uppercase;
  padding: 9px 25px;
}
/* line 268, ../sass/_template_specific.scss */
#mainMenuDiv ul li a.button-promotion:hover {
  background: #bcdff7;
}
/* line 273, ../sass/_template_specific.scss */
#mainMenuDiv ul li a .icon_home {
  vertical-align: middle;
}
/* line 278, ../sass/_template_specific.scss */
#mainMenuDiv ul li:hover a {
  color: #C66047;
}
/* line 282, ../sass/_template_specific.scss */
#mainMenuDiv ul li#section-active a {
  color: #C66047;
}
/* line 286, ../sass/_template_specific.scss */
#mainMenuDiv ul li ul {
  display: none;
  position: absolute;
  top: 30px;
  left: -10px;
  padding: 10px;
  background: rgba(0, 0, 0, 0.65);
  width: 125px;
}
/* line 295, ../sass/_template_specific.scss */
#mainMenuDiv ul li ul:after {
  display: none;
}
/* line 299, ../sass/_template_specific.scss */
#mainMenuDiv ul li ul:before {
  content: "";
  display: block;
  border-bottom: 10px solid rgba(0, 0, 0, 0.65);
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  position: absolute;
  bottom: 100%;
  left: 25px;
}
/* line 310, ../sass/_template_specific.scss */
#mainMenuDiv ul li ul .separator_submenu {
  display: block;
  width: 100%;
  border-bottom: 1px solid white;
  margin: 5px 0;
}
/* line 318, ../sass/_template_specific.scss */
#mainMenuDiv ul li ul li a {
  color: white !important;
}
/* line 321, ../sass/_template_specific.scss */
#mainMenuDiv ul li ul li a#subsection-active {
  color: #C66047 !important;
}
/* line 325, ../sass/_template_specific.scss */
#mainMenuDiv ul li ul li a:hover {
  color: #C66047 !important;
}

/*=== Slider ===*/
/* line 336, ../sass/_template_specific.scss */
#slider_container {
  position: relative;
}
/* line 341, ../sass/_template_specific.scss */
#slider_container .forcefullwidth_wrapper_tp_banner .tp-banner-container .tp-banner {
  z-index: 0;
}

/* line 347, ../sass/_template_specific.scss */
.left_slider_arrow {
  position: absolute;
  top: 315px;
  z-index: 22;
  left: 20%;
  cursor: pointer;
}

/* line 355, ../sass/_template_specific.scss */
.tp-rightarrow {
  background: url(/img/torra/flex_left.png) !important;
  width: 40px !important;
  height: 80px !important;
  right: 0 !important;
  -webkit-transform: translate(0%, -50%) rotate(180deg);
  -moz-transform: translate(0%, -50%) rotate(180deg);
  -ms-transform: translate(0%, -50%) rotate(180deg);
  -o-transform: translate(0%, -50%) rotate(180deg);
  transform: translate(0%, -50%) rotate(180deg);
  z-index: 1001 !important;
}

/* line 368, ../sass/_template_specific.scss */
.tp-leftarrow {
  background: url(/img/torra/flex_left.png) !important;
  width: 40px !important;
  height: 80px !important;
  left: 0 !important;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  z-index: 1001 !important;
}

/* line 381, ../sass/_template_specific.scss */
.right_slider_arrow {
  position: absolute;
  top: 315px;
  z-index: 22;
  right: 20%;
  -moz-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
  filter: FlipH;
  -ms-filter: "FlipH";
  cursor: pointer;
}

/* line 395, ../sass/_template_specific.scss */
.tp-bullets {
  bottom: 85% !important;
  opacity: 1 !important;
}

/* line 400, ../sass/_template_specific.scss */
.tp-bullets .bullet {
  background: url("/img/torra/bullet_flexslider.png") no-repeat !important;
  background-position: center !important;
  display: inline-block !important;
  float: none !important;
}

/* line 407, ../sass/_template_specific.scss */
.tp-bullets .bullet.selected {
  background: url("/img/torra/bullet_flexslider_active.png") no-repeat center !important;
  background-size: cover;
}

/* line 412, ../sass/_template_specific.scss */
.slide_inner {
  height: 630px;
  width: 100%;
  overflow: hidden;
  display: inline-block;
}
/* line 418, ../sass/_template_specific.scss */
.slide_inner img {
  width: 100%;
  display: block;
}

/* line 424, ../sass/_template_specific.scss */
.down_slider_arrow {
  position: absolute;
  bottom: 175px;
  z-index: 22;
  right: 20%;
  cursor: pointer;
}
/* line 431, ../sass/_template_specific.scss */
.down_slider_arrow:hover {
  opacity: 0.8;
}

/*======= Content Access =====*/
/* line 437, ../sass/_template_specific.scss */
.my-bookings-booking-info {
  margin: 0 auto !important;
}

/* line 441, ../sass/_template_specific.scss */
.content_access {
  padding-top: 60px;
  padding-bottom: 60px;
  background: #E7E3D7;
  display: inline-block;
  width: 100%;
  margin-top: -5px;
  margin-bottom: 60px;
}
/* line 450, ../sass/_template_specific.scss */
.content_access h3.section-title {
  font-size: 22px;
  text-align: center;
  text-transform: uppercase;
  color: #C66047;
  margin-bottom: 30px;
}
/* line 457, ../sass/_template_specific.scss */
.content_access h3.section-title + div {
  font-size: 14px;
  color: #000000;
  text-align: center;
  width: 80%;
  margin: 0 auto;
}
/* line 466, ../sass/_template_specific.scss */
.content_access form#my-bookings-form {
  text-align: center;
  padding-bottom: 1px;
}
/* line 471, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields {
  text-align: center;
  margin-top: 20px;
}
/* line 475, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields label {
  display: block;
  line-height: 18px;
  font-size: 17px;
  font-weight: lighter;
  color: #636363;
  text-align: center;
}
/* line 484, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields input {
  width: 160px;
  text-align: center;
}
/* line 489, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields input#emailInput {
  margin-bottom: 6px;
}
/* line 493, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields button#my-bookings-form-search-button {
  display: block;
  margin: 20px auto 0;
  width: 165px;
  border: 0;
  background: #C66047;
  color: white;
  text-transform: uppercase;
  padding: 7px;
  cursor: pointer;
}
/* line 504, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields button#my-bookings-form-search-button:hover {
  opacity: 0.8;
}
/* line 509, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields ul {
  margin-top: 10px;
}
/* line 512, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields ul li {
  display: inline;
}
/* line 516, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields ul li button.modify-reservation, .content_access #my-bookings-form-fields ul li button.searchForReservation {
  width: 165px;
  border: 0;
  color: white;
  text-transform: uppercase;
  padding: 7px;
  cursor: pointer;
}
/* line 526, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields ul li button.modify-reservation {
  background: #8fc9f1;
}
/* line 530, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields ul li button.searchForReservation {
  background: #C66047;
}
/* line 539, ../sass/_template_specific.scss */
.content_access #reservation .modify_reservation_widget {
  margin: 0 auto;
  width: 1200px;
  margin-left: 0;
  margin-bottom: 20px;
  height: 100px;
  margin: 0 auto;
}
/* line 547, ../sass/_template_specific.scss */
.content_access #reservation .modify_reservation_widget #motor_reserva {
  width: 1200px;
}
/* line 551, ../sass/_template_specific.scss */
.content_access #reservation .modify_reservation_widget #motor_reserva #contenedor_fechas .colocar_fechas {
  margin: 0;
}
/* line 555, ../sass/_template_specific.scss */
.content_access #reservation .modify_reservation_widget #motor_reserva #contenedor_fechas #titulo_fecha_entrada, .content_access #reservation .modify_reservation_widget #motor_reserva #contenedor_fechas #titulo_fecha_salida {
  width: 125px;
  top: 10px;
  position: relative;
}
/* line 562, ../sass/_template_specific.scss */
.content_access #reservation .modify_reservation_widget #motor_reserva #contenedor_habitaciones {
  margin-top: 0;
}
/* line 565, ../sass/_template_specific.scss */
.content_access #reservation .modify_reservation_widget #motor_reserva #contenedor_habitaciones label {
  text-align: left;
  padding-bottom: 10px;
}
/* line 571, ../sass/_template_specific.scss */
.content_access #reservation .modify_reservation_widget #motor_reserva #contenedor_opciones {
  width: 250px;
}
/* line 574, ../sass/_template_specific.scss */
.content_access #reservation .modify_reservation_widget #motor_reserva #contenedor_opciones .selector_adultos, .content_access #reservation .modify_reservation_widget #motor_reserva #contenedor_opciones .selector_ninos {
  padding-bottom: 10px;
  text-align: left;
}
/* line 579, ../sass/_template_specific.scss */
.content_access #reservation .modify_reservation_widget #motor_reserva #contenedor_opciones #info_ninos {
  left: 150px;
  top: 4px;
}
/* line 585, ../sass/_template_specific.scss */
.content_access #reservation .modify_reservation_widget #motor_reserva #envio {
  width: 250px;
  margin-top: 20px;
  margin-left: 0;
}
/* line 590, ../sass/_template_specific.scss */
.content_access #reservation .modify_reservation_widget #motor_reserva #envio input, .content_access #reservation .modify_reservation_widget #motor_reserva #envio button {
  display: inline;
}
/* line 599, ../sass/_template_specific.scss */
.content_access button#cancelButton {
  display: block;
  margin: 55px auto 0px;
  width: 165px;
  border: 0;
  background: #C66047;
  color: white;
  text-transform: uppercase;
  padding: 7px;
  cursor: pointer;
  display: none;
}

/*========= Location and Contact ======*/
/* line 615, ../sass/_template_specific.scss */
.page-localizacion #wrapper_content, .page-localizacion .how-to {
  background: rgba(252, 241, 235, 0.86);
  padding: 20px;
  margin-top: 200px;
  width: 1100px;
}
/* line 622, ../sass/_template_specific.scss */
.page-localizacion .container12 .column6 {
  width: 530px;
}

/* line 627, ../sass/_template_specific.scss */
.location-info-and-form-wrapper {
  background: rgba(226, 226, 226, 0.46);
  background: #C66047;
  display: table;
  padding-top: 30px;
  padding-bottom: 20px;
  margin-bottom: 20px;
  margin-top: 30px;
  height: 300px;
}

/* line 638, ../sass/_template_specific.scss */
.location-info-and-form-wrapper h1 {
  font-weight: 700;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  padding-bottom: 6px;
  color: #C66047;
  width: 95%;
  line-height: 20px;
}

/* line 651, ../sass/_template_specific.scss */
.location-info strong {
  font-weight: bold;
}

/* line 655, ../sass/_template_specific.scss */
#location-description-intro {
  margin-bottom: 30px;
}

/* line 659, ../sass/_template_specific.scss */
.iframe-google-maps-wrapper {
  margin-top: 30px;
  width: 100%;
  margin-bottom: 60px;
}

/* line 665, ../sass/_template_specific.scss */
.map_default_visible {
  margin-top: 50px;
}

/* line 669, ../sass/_template_specific.scss */
div#map-canvas {
  height: 350px !important;
}

/* line 675, ../sass/_template_specific.scss */
.form-contact #title {
  display: none !important;
}

/* line 679, ../sass/_template_specific.scss */
.form-contact #google-plus {
  display: none !important;
}

/* line 683, ../sass/_template_specific.scss */
.form-contact .fb-like {
  display: none !important;
}

/* line 687, ../sass/_template_specific.scss */
.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

/* line 692, ../sass/_template_specific.scss */
.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: black;
  font-weight: 300;
  font-size: 14px;
  color: #717171;
}

/* line 702, ../sass/_template_specific.scss */
.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px !important;
  height: 30px;
  background-color: white;
}

/* line 712, ../sass/_template_specific.scss */
.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px;
  background-color: white;
  margin-right: 35px;
}

/* line 722, ../sass/_template_specific.scss */
.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 35px;
}

/* line 727, ../sass/_template_specific.scss */
.form-contact #contact-button {
  border-radius: 0 !important;
  height: 30px !important;
  width: 130px !important;
  background-color: #E7E3D7 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0 !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0 !important;
  line-height: 32px;
}
/* line 741, ../sass/_template_specific.scss */
.form-contact #contact-button:hover {
  opacity: 0.8;
}

/* line 746, ../sass/_template_specific.scss */
.location-info {
  font-weight: 300;
  padding-left: 20px;
  box-sizing: border-box;
  font-size: 14px;
  color: #717171;
  line-height: 30px;
}

/* line 755, ../sass/_template_specific.scss */
.how-to {
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;
}
/* line 761, ../sass/_template_specific.scss */
.how-to h3 {
  font-size: 30px;
  margin-bottom: 20px;
  color: #C66047;
}

/*======= Location and Contact =========*/
/* line 769, ../sass/_template_specific.scss */
.location_block_separator {
  display: block;
  width: 100%;
  margin: 60px 0;
}

/* line 776, ../sass/_template_specific.scss */
.page-localizacion #wrapper_content, .page-localizacion .how-to {
  background: rgba(252, 241, 235, 0.86);
  padding: 20px;
  margin-top: 200px;
  width: 1100px;
}
/* line 783, ../sass/_template_specific.scss */
.page-localizacion .container12 .column6 {
  width: 530px;
}

/* line 789, ../sass/_template_specific.scss */
.iframe-google-maps-wrapper iframe {
  width: 100%;
}

/* line 794, ../sass/_template_specific.scss */
.location-info-and-form-wrapper {
  margin-top: 35px;
  display: table;
  padding-top: 0;
  padding-bottom: 0;
  margin-bottom: 30px;
}

/* line 802, ../sass/_template_specific.scss */
.aditional-info {
  float: left;
  margin-left: 0;
  margin-right: 0;
  width: 570px !important;
  position: relative;
  overflow: hidden;
}
/* line 810, ../sass/_template_specific.scss */
.aditional-info .info_main img {
  min-width: 100%;
  max-width: none;
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  max-width: initial;
}

/* line 825, ../sass/_template_specific.scss */
.recaptcha_box_wrapper {
  display: block !important;
}

/* line 829, ../sass/_template_specific.scss */
.check_privacy {
  float: left;
}
/* line 832, ../sass/_template_specific.scss */
.check_privacy + span a {
  color: black !important;
}

/* line 837, ../sass/_template_specific.scss */
.location-info-and-form-wrapper h1 {
  font-family: 'Lato', sans-serif;
  font-size: 33px;
  color: #2c73b5;
  font-weight: 100;
  margin-bottom: 35px;
  position: relative;
  padding: 0 20px;
}

/* line 847, ../sass/_template_specific.scss */
.location-info strong {
  font-weight: bold;
}

/* line 851, ../sass/_template_specific.scss */
#location-description-intro {
  margin-bottom: 30px;
}

/* line 855, ../sass/_template_specific.scss */
.iframe-google-maps-wrapper {
  margin-top: 30px;
  width: 100%;
  margin-bottom: 30px;
}

/* line 861, ../sass/_template_specific.scss */
li.how-to-go {
  cursor: pointer;
  color: #C66047;
  background: url("/img/amera/icons_maps/walk.png") left center no-repeat;
  padding-left: 30px;
  margin-left: -10px;
}
/* line 868, ../sass/_template_specific.scss */
li.how-to-go .car {
  background: url("/img/amera/icons_maps/car.png") left center no-repeat;
}

/* line 873, ../sass/_template_specific.scss */
.form-contact #title {
  display: none !important;
}

/* line 877, ../sass/_template_specific.scss */
.form-contact #google-plus {
  display: none !important;
}

/* line 881, ../sass/_template_specific.scss */
.form-contact .fb-like {
  display: none !important;
}

/* line 889, ../sass/_template_specific.scss */
.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

/* line 894, ../sass/_template_specific.scss */
.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 6px;
  font-size: 17px;
  font-weight: 100;
}
/* line 901, ../sass/_template_specific.scss */
.form-contact #contact .contInput label:first-of-type {
  margin-top: 0;
}

/* line 906, ../sass/_template_specific.scss */
#contactContent .info {
  margin-top: 5px !important;
  padding-left: 32px !important;
  padding-top: 25px;
  background: #efefef;
  background: #E7E3D7;
  box-sizing: border-box;
  width: 100%;
  display: block;
  padding-bottom: 35px;
}

/* line 918, ../sass/_template_specific.scss */
.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px !important;
  height: 30px;
  background-color: white;
  color: black;
}

/* line 928, ../sass/_template_specific.scss */
.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  border: 0;
  background-color: white;
  color: black;
  width: 1070px;
  margin-right: 0;
}

/* line 938, ../sass/_template_specific.scss */
.form-contact #contact-button-wrapper {
  padding-right: 0 !important;
  margin-right: 0;
}

/* line 943, ../sass/_template_specific.scss */
.form-contact #contact-button {
  border-radius: 0 !important;
  height: 30px !important;
  width: 130px !important;
  background: #C66047 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0 !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0 !important;
  line-height: 32px;
}
/* line 957, ../sass/_template_specific.scss */
.form-contact #contact-button:hover {
  opacity: 0.8;
}

/* line 962, ../sass/_template_specific.scss */
.location-info {
  font-weight: 300;
  box-sizing: border-box;
  font-size: 14px;
  color: #4b4b4b;
  line-height: 25px;
  padding: 30px 0;
}

/* line 975, ../sass/_template_specific.scss */
.how-to {
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;
}
/* line 981, ../sass/_template_specific.scss */
.how-to h3 {
  font-size: 30px;
  margin-bottom: 20px;
  color: #C66047;
}

/* line 988, ../sass/_template_specific.scss */
ul.location_destiny {
  margin-top: 40px;
}
/* line 991, ../sass/_template_specific.scss */
ul.location_destiny li {
  padding: 10px 0 10px 35px;
  background: url("/img/torra/destiny.png") left center no-repeat;
  cursor: pointer;
}
/* line 996, ../sass/_template_specific.scss */
ul.location_destiny li a {
  text-decoration: none;
  color: #C66047;
}
/* line 1001, ../sass/_template_specific.scss */
ul.location_destiny li a.active {
  color: white;
}
/* line 1006, ../sass/_template_specific.scss */
ul.location_destiny .car {
  background: url("/img/torra/car.png") left center no-repeat;
}
/* line 1010, ../sass/_template_specific.scss */
ul.location_destiny .walk {
  background: url("/img/torra/walk.png") left center no-repeat;
}

/* line 1016, ../sass/_template_specific.scss */
.form-contact.location_section h1 {
  width: 100%;
  padding: 10px 30px;
  background-color: #e2ebf4;
  color: #2c73b5;
  font-size: 18px;
  text-transform: uppercase;
  font-weight: 100;
  border-bottom: 2px solid white;
  box-sizing: border-box;
}

/* line 1029, ../sass/_template_specific.scss */
.location_description_wrapper {
  color: white;
  padding: 0 55px;
  font-weight: 100;
  line-height: 24px;
  font-size: 16px;
}

/* line 1037, ../sass/_template_specific.scss */
#contactContent .info {
  padding-right: 32px;
  margin-bottom: 30px;
}
/* line 1041, ../sass/_template_specific.scss */
#contactContent .info .contInput {
  margin-bottom: 10px;
  display: inline-block;
  width: auto;
  clear: both;
}
/* line 1047, ../sass/_template_specific.scss */
#contactContent .info .contInput:nth-of-type(2), #contactContent .info .contInput:nth-of-type(4) {
  float: right;
}

/* line 1053, ../sass/_template_specific.scss */
.iframe_google_maps {
  margin-bottom: 30px;
}

/* line 1057, ../sass/_template_specific.scss */
.destacados_wrapper {
  display: inline-block;
  width: 100%;
  margin-bottom: 20px;
  visibility: hidden;
}
/* line 1063, ../sass/_template_specific.scss */
.destacados_wrapper .destacados_title {
  text-align: center;
  background: #C66047;
  text-transform: uppercase;
  color: white;
  padding: 10px 0;
  margin-bottom: 10px;
}
/* line 1072, ../sass/_template_specific.scss */
.destacados_wrapper .destacados_content {
  display: inline-block;
  width: 100%;
}
/* line 1076, ../sass/_template_specific.scss */
.destacados_wrapper .destacados_content a {
  display: inline-block;
  text-decoration: none;
}
/* line 1081, ../sass/_template_specific.scss */
.destacados_wrapper .destacados_content .destacado_element {
  display: inline-block;
  width: calc(100% / 3 - 5px);
  height: 230px;
  float: left;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  margin-right: 7px;
}
/* line 1091, ../sass/_template_specific.scss */
.destacados_wrapper .destacados_content .destacado_element:last-child {
  margin-right: 0;
}
/* line 1096, ../sass/_template_specific.scss */
.destacados_wrapper .destacados_content .destacado_element:hover a {
  opacity: .8;
}
/* line 1101, ../sass/_template_specific.scss */
.destacados_wrapper .destacados_content .destacado_element .destacado_image {
  display: inline-block;
  height: 100%;
}
/* line 1105, ../sass/_template_specific.scss */
.destacados_wrapper .destacados_content .destacado_element .destacado_image img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 1110, ../sass/_template_specific.scss */
.destacados_wrapper .destacados_content .destacado_element .destacado_icon {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 1117, ../sass/_template_specific.scss */
.home_content_wrapper {
  background: #E7E3D7;
  position: relative;
  padding: 20px 0;
  display: inline-block;
  width: 100%;
  height: 370px;
  margin: 60px 0;
}
/* line 1126, ../sass/_template_specific.scss */
.home_content_wrapper .home_box {
  position: relative;
}
/* line 1130, ../sass/_template_specific.scss */
.home_content_wrapper .prev_arrow {
  width: 27px;
  height: 45px;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  -webkit-transform: translate(0%, -50%) rotate(180deg);
  -moz-transform: translate(0%, -50%) rotate(180deg);
  -ms-transform: translate(0%, -50%) rotate(180deg);
  -o-transform: translate(0%, -50%) rotate(180deg);
  transform: translate(0%, -50%) rotate(180deg);
  left: 0;
  background: url(/img/torra/home_right.png);
}
/* line 1143, ../sass/_template_specific.scss */
.home_content_wrapper .next_arrow {
  width: 27px;
  height: 45px;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 0;
  background: url(/img/torra/home_right.png);
}
/* line 1152, ../sass/_template_specific.scss */
.home_content_wrapper .home_box .home_element {
  position: relative;
  height: 370px;
}
/* line 1157, ../sass/_template_specific.scss */
.home_content_wrapper .home_box .content_element {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: 100%;
  text-align: center;
}
/* line 1162, ../sass/_template_specific.scss */
.home_content_wrapper .home_box .content_element .title_element {
  color: #C66047;
  font-size: 22px;
  margin-bottom: 28px;
}
/* line 1167, ../sass/_template_specific.scss */
.home_content_wrapper .home_box .content_element .title_element .subtitle {
  display: block;
  width: 100%;
  font-weight: bold;
  font-size: 14px;
  color: black;
  margin-top: 28px;
}
/* line 1177, ../sass/_template_specific.scss */
.home_content_wrapper .home_box .content_element .description_element {
  width: 80%;
  font-size: 14px;
  margin: 0 auto;
}

/* line 1186, ../sass/_template_specific.scss */
.flex_banner_wrapper {
  position: relative;
  background-color: white;
  height: 300px;
  width: 100%;
  overflow: hidden;
}
/* line 1193, ../sass/_template_specific.scss */
.flex_banner_wrapper .prev_arrow {
  width: 40px;
  height: 80px;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 0;
  background: url(/img/torra/flex_left.png);
}
/* line 1201, ../sass/_template_specific.scss */
.flex_banner_wrapper .next_arrow {
  width: 40px;
  height: 80px;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%) rotate(180deg);
  -moz-transform: translate(0%, -50%) rotate(180deg);
  -ms-transform: translate(0%, -50%) rotate(180deg);
  -o-transform: translate(0%, -50%) rotate(180deg);
  transform: translate(0%, -50%) rotate(180deg);
  right: 0;
  background: url(/img/torra/flex_left.png);
}
/* line 1215, ../sass/_template_specific.scss */
.flex_banner_wrapper .flex_element {
  height: 300px !important;
  position: relative;
  transition: all 0.3s ease;
  visibility: hidden;
}
/* line 1222, ../sass/_template_specific.scss */
.flex_banner_wrapper .flex_element:hover .flex_overlay {
  box-shadow: inset 0 0 0 400px rgba(198, 96, 71, 0.6);
}
/* line 1226, ../sass/_template_specific.scss */
.flex_banner_wrapper .flex_element:hover .flex_content {
  border: 1px solid white;
  padding: 10px 10px 40px 10px;
  background: url(/img/torra/plus_icon_white.png) no-repeat bottom center;
  background-position-y: 50px;
}
/* line 1234, ../sass/_template_specific.scss */
.flex_banner_wrapper .flex_element .flex_overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  transition: all 1.5s ease;
}
/* line 1245, ../sass/_template_specific.scss */
.flex_banner_wrapper .flex_element .flex_image {
  display: inline-block;
  width: 100%;
  height: 100%;
}
/* line 1250, ../sass/_template_specific.scss */
.flex_banner_wrapper .flex_element .flex_image img {
  width: 100%;
}
/* line 1255, ../sass/_template_specific.scss */
.flex_banner_wrapper .flex_element .flex_content {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: 1;
  transition: border 1.5s ease, padding .5s ease;
  box-sizing: border-box;
  border-color: white;
  display: table;
}
/* line 1263, ../sass/_template_specific.scss */
.flex_banner_wrapper .flex_element .flex_content .flex_title {
  color: white;
  font-size: 24px;
}

/* line 1271, ../sass/_template_specific.scss */
.ticks_wrapper {
  position: relative;
  top: 45px;
  z-index: 101;
  width: 100%;
  color: #ffffff;
  padding-bottom: 30px;
}
/* line 1279, ../sass/_template_specific.scss */
.ticks_wrapper .ticks_element {
  display: inline-block;
  width: calc(100% / 3 - 10px);
  text-align: center;
  float: left;
  margin-right: 15px;
}
/* line 1286, ../sass/_template_specific.scss */
.ticks_wrapper .ticks_element:last-child {
  margin-right: 0;
}
/* line 1290, ../sass/_template_specific.scss */
.ticks_wrapper .ticks_element .title_tick {
  text-transform: uppercase;
  font-size: 25px;
  color: #E16043;
  font-weight: bold;
}
/* line 1297, ../sass/_template_specific.scss */
.ticks_wrapper .ticks_element .description_tick {
  font-weight: normal;
  width: 80%;
  color: black;
  margin: 0 auto;
}

/* line 1306, ../sass/_template_specific.scss */
.overlay_top {
  position: absolute;
  top: 0;
  width: 100%;
  height: 150px;
  z-index: 100;
  background: -moz-linear-gradient(top, rgba(0, 0, 0, 0.85) 35%, rgba(0, 0, 0, 0.85) 25%, transparent 100%);
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0.85) 35%, rgba(0, 0, 0, 0.85) 25%, transparent 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.85) 35%, rgba(0, 0, 0, 0.85) 25%, rgba(0, 0, 0, 0) 100%);
}

/* line 1318, ../sass/_template_specific.scss */
.overlay_bottom {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 150px;
  z-index: 100;
}

/* line 1327, ../sass/_template_specific.scss */
.rooms_home_wrapper {
  display: inline-block;
  width: 100%;
  margin-bottom: 20px;
}
/* line 1332, ../sass/_template_specific.scss */
.rooms_home_wrapper .hotel_block_wrapper {
  display: inline-block;
  width: 49%;
  height: 345px;
  float: left;
  box-sizing: border-box;
  visibility: hidden;
}
/* line 1340, ../sass/_template_specific.scss */
.rooms_home_wrapper .hotel_block_wrapper .flex_hotel {
  position: relative;
  height: 345px;
  width: 100%;
}
/* line 1345, ../sass/_template_specific.scss */
.rooms_home_wrapper .hotel_block_wrapper .flex_hotel .center_block {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.6);
  z-index: 1;
  padding: 10px;
  color: white;
  width: 65%;
  box-sizing: border-box;
  text-align: center;
  text-transform: uppercase;
  font-size: 22px;
}
/* line 1357, ../sass/_template_specific.scss */
.rooms_home_wrapper .hotel_block_wrapper .flex_hotel .center_block .icon_hotel {
  display: block;
  margin: 10px auto 0;
}
/* line 1362, ../sass/_template_specific.scss */
.rooms_home_wrapper .hotel_block_wrapper .flex_hotel .center_block a {
  text-decoration: none;
  color: white;
  display: inline-block;
}
/* line 1369, ../sass/_template_specific.scss */
.rooms_home_wrapper .hotel_block_wrapper .flex_hotel .hotel_element {
  position: relative;
  height: 345px;
  width: 100%;
  overflow: hidden;
}
/* line 1376, ../sass/_template_specific.scss */
.rooms_home_wrapper .hotel_block_wrapper .flex_hotel .hotel_element .hotel_image img {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: 100%;
}
/* line 1382, ../sass/_template_specific.scss */
.rooms_home_wrapper .hotel_block_wrapper .flex_hotel .hotel_element .hotel_title {
  position: absolute;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
  padding: 10px 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  text-align: center;
  text-transform: uppercase;
}
/* line 1393, ../sass/_template_specific.scss */
.rooms_home_wrapper .hotel_block_wrapper .flex_hotel .hotel_element .hotel_title a {
  text-decoration: none;
  color: #C66047;
}
/* line 1397, ../sass/_template_specific.scss */
.rooms_home_wrapper .hotel_block_wrapper .flex_hotel .hotel_element .hotel_title a:hover {
  opacity: .8;
}
/* line 1404, ../sass/_template_specific.scss */
.rooms_home_wrapper .hotel_block_wrapper .flex_hotel .prev_arrow {
  width: 40px;
  height: 80px;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 0;
  background: url(/img/torra/block_left.png);
}
/* line 1412, ../sass/_template_specific.scss */
.rooms_home_wrapper .hotel_block_wrapper .flex_hotel .next_arrow {
  width: 40px;
  height: 80px;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 0;
  background: url(/img/torra/block_right.png);
}
/* line 1422, ../sass/_template_specific.scss */
.rooms_home_wrapper .apartamento_block_wrapper {
  display: inline-block;
  width: 49%;
  height: 345px;
  float: right;
  box-sizing: border-box;
  visibility: hidden;
}
/* line 1430, ../sass/_template_specific.scss */
.rooms_home_wrapper .apartamento_block_wrapper .flex_apartamento {
  position: relative;
  height: 345px;
  width: 100%;
}
/* line 1435, ../sass/_template_specific.scss */
.rooms_home_wrapper .apartamento_block_wrapper .flex_apartamento .center_block {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.6);
  z-index: 1;
  padding: 10px;
  color: white;
  width: 65%;
  box-sizing: border-box;
  text-align: center;
  text-transform: uppercase;
  font-size: 22px;
}
/* line 1447, ../sass/_template_specific.scss */
.rooms_home_wrapper .apartamento_block_wrapper .flex_apartamento .center_block .icon_apartamento {
  display: block;
  margin: 10px auto 0;
}
/* line 1452, ../sass/_template_specific.scss */
.rooms_home_wrapper .apartamento_block_wrapper .flex_apartamento .center_block a {
  text-decoration: none;
  color: white;
  display: inline-block;
}
/* line 1459, ../sass/_template_specific.scss */
.rooms_home_wrapper .apartamento_block_wrapper .flex_apartamento .apartamento_element {
  position: relative;
  height: 345px;
  width: 100%;
  overflow: hidden;
}
/* line 1466, ../sass/_template_specific.scss */
.rooms_home_wrapper .apartamento_block_wrapper .flex_apartamento .apartamento_element .apartamento_image img {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: 100%;
}
/* line 1472, ../sass/_template_specific.scss */
.rooms_home_wrapper .apartamento_block_wrapper .flex_apartamento .apartamento_element .apartamento_title {
  position: absolute;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
  padding: 10px 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  text-align: center;
  text-transform: uppercase;
}
/* line 1483, ../sass/_template_specific.scss */
.rooms_home_wrapper .apartamento_block_wrapper .flex_apartamento .apartamento_element .apartamento_title a {
  text-decoration: none;
  color: #C66047;
}
/* line 1487, ../sass/_template_specific.scss */
.rooms_home_wrapper .apartamento_block_wrapper .flex_apartamento .apartamento_element .apartamento_title a:hover {
  opacity: .8;
}
/* line 1494, ../sass/_template_specific.scss */
.rooms_home_wrapper .apartamento_block_wrapper .flex_apartamento .prev_arrow {
  width: 40px;
  height: 80px;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 0;
  background: url(/img/torra/block_left.png);
}
/* line 1502, ../sass/_template_specific.scss */
.rooms_home_wrapper .apartamento_block_wrapper .flex_apartamento .next_arrow {
  width: 40px;
  height: 80px;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 0;
  background: url(/img/torra/block_right.png);
}

/* line 1513, ../sass/_template_specific.scss */
footer {
  background: #C66047;
}
/* line 1516, ../sass/_template_specific.scss */
footer .wrapper_footer_columns {
  padding: 40px 0;
}
/* line 1519, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column {
  color: #ffffff;
  text-align: center;
  border-right: 1px solid white;
  font-weight: lighter;
  box-sizing: border-box;
  margin: 0;
  width: calc(100% / 3);
}
/* line 1529, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column:first-child {
  border-left: 1px solid white;
}
/* line 1533, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column .footer_element {
  display: block;
  text-align: center;
  text-decoration: none;
  color: white;
  margin-bottom: 5px;
}
/* line 1540, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column .footer_element:hover {
  opacity: .8;
}
/* line 1545, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column .footer_column_description {
  width: 70%;
  margin: 0 auto;
}
/* line 1549, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column .footer_column_description #suscEmail, footer .wrapper_footer_columns .footer_column .footer_column_description #suscName {
  width: 100%;
  border: 0;
  padding: 15px 10px;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  margin-bottom: 5px;
  text-align: center;
}
/* line 1559, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column .footer_column_description #suscEmail::-webkit-input-placeholder, footer .wrapper_footer_columns .footer_column .footer_column_description #suscName::-webkit-input-placeholder {
  color: white;
}
/* line 1563, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column .footer_column_description #suscEmail:-moz-placeholder, footer .wrapper_footer_columns .footer_column .footer_column_description #suscName:-moz-placeholder {
  /* Firefox 18- */
  color: white;
}
/* line 1568, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column .footer_column_description #suscEmail::-moz-placeholder, footer .wrapper_footer_columns .footer_column .footer_column_description #suscName::-moz-placeholder {
  /* Firefox 19+ */
  color: white;
}
/* line 1573, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column .footer_column_description #suscEmail:-ms-input-placeholder, footer .wrapper_footer_columns .footer_column .footer_column_description #suscName:-ms-input-placeholder {
  color: white;
}
/* line 1578, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column .footer_column_description #newsletter-button {
  width: 100%;
  border: 0;
  background: white;
  color: #C66047;
  padding: 15px 0;
  text-transform: uppercase;
  cursor: pointer;
  margin-bottom: 10px;
}
/* line 1588, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column .footer_column_description #newsletter-button:hover {
  opacity: .8;
}
/* line 1593, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column .footer_column_description .newsletter_checkbox {
  font-size: 11px;
}
/* line 1596, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column .footer_column_description .newsletter_checkbox label {
  display: inline;
}
/* line 1600, ../sass/_template_specific.scss */
footer .wrapper_footer_columns .footer_column .footer_column_description .newsletter_checkbox a {
  color: white;
}
/* line 1609, ../sass/_template_specific.scss */
footer #newsletter {
  margin-top: 20px;
}
/* line 1612, ../sass/_template_specific.scss */
footer #newsletter #title_newsletter, footer #newsletter #suscEmailLabel {
  display: none !important;
}
/* line 1617, ../sass/_template_specific.scss */
footer .full-copyright {
  text-align: center;
}
/* line 1620, ../sass/_template_specific.scss */
footer .full-copyright .footer-copyright {
  display: inline-block;
  color: white;
  width: auto;
  font-size: 12px;
  font-weight: lighter;
  margin-bottom: 15px;
}
/* line 1628, ../sass/_template_specific.scss */
footer .full-copyright .footer-copyright a {
  color: white;
  text-decoration: none;
}
/* line 1635, ../sass/_template_specific.scss */
footer #facebook_like {
  display: inline-block;
}
/* line 1639, ../sass/_template_specific.scss */
footer #google_plus_one {
  display: inline-block;
  vertical-align: middle;
}
/* line 1644, ../sass/_template_specific.scss */
footer .social_wrapper {
  background: #E7E3D7;
  display: table;
  width: 100%;
}
/* line 1649, ../sass/_template_specific.scss */
footer .social_wrapper .content_social {
  display: table;
  padding: 60px;
}
/* line 1653, ../sass/_template_specific.scss */
footer .social_wrapper .content_social .footer_logos {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  width: 750px;
}
/* line 1660, ../sass/_template_specific.scss */
footer .social_wrapper .content_social .social_box {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
}
/* line 1665, ../sass/_template_specific.scss */
footer .social_wrapper .content_social .social_box .social_title {
  font-size: 26px;
  color: #C66047;
  text-transform: uppercase;
  margin-bottom: 30px;
}
/* line 1672, ../sass/_template_specific.scss */
footer .social_wrapper .content_social .social_box a {
  display: inline-block;
  vertical-align: middle;
}

/*====== Habitaciones ====*/
/* line 1682, ../sass/_template_specific.scss */
.rooms_wrapper {
  margin-top: 30px;
  margin-bottom: 60px;
}
/* line 1686, ../sass/_template_specific.scss */
.rooms_wrapper .room_element {
  height: auto;
  margin-bottom: 20px;
  width: 100%;
  display: table;
}
/* line 1692, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .sub-description-rooms {
  font-weight: bold;
  margin-bottom: 15px;
}
/* line 1697, ../sass/_template_specific.scss */
.rooms_wrapper .room_element h3.room_title {
  font-size: 24px;
  color: #C66047;
  margin-bottom: 12px;
  padding-left: 0 !important;
}
/* line 1703, ../sass/_template_specific.scss */
.rooms_wrapper .room_element h3.room_title span.capacity {
  font-weight: lighter;
  font-size: 16px;
  text-transform: capitalize;
  vertical-align: top;
  margin-top: 8px;
  display: inline-block;
}
/* line 1711, ../sass/_template_specific.scss */
.rooms_wrapper .room_element h3.room_title span.capacity.kids_wrapper {
  margin-top: 10px;
}
/* line 1715, ../sass/_template_specific.scss */
.rooms_wrapper .room_element h3.room_title span.capacity > span {
  margin-right: 2px;
}
/* line 1723, ../sass/_template_specific.scss */
.rooms_wrapper .room_element h3.room_title span.capacity .capacity_image.kids {
  padding-bottom: 0;
  padding-top: 1px;
  vertical-align: top;
}
/* line 1731, ../sass/_template_specific.scss */
.rooms_wrapper .room_element h3.room_title span.plus_image {
  display: inline-block;
  vertical-align: bottom;
  line-height: 22px;
}
/* line 1738, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_description {
  padding-bottom: 11px;
  border-bottom: 1px solid #CECECE;
  margin-bottom: 33px;
  color: #656565;
  font-weight: 100;
  line-height: 24px;
  font-size: 16px;
}
/* line 1747, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_description .hide_me {
  display: none;
}
/* line 1752, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .service_elements li {
  display: inline-block;
  padding-right: 20px;
  font-size: 13px;
  color: #C66047;
  font-weight: lighter;
}
/* line 1759, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .service_elements li img {
  vertical-align: middle;
  margin-right: 5px;
}
/* line 1765, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .exceded {
  width: 32%;
  height: 278px;
  float: left;
  position: relative;
  overflow: hidden;
}
/* line 1772, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .exceded img.room_image {
  min-height: 100%;
  max-width: none;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  max-height: 100%;
}
/* line 1786, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .exceded img.plus_image {
  position: absolute;
  left: 20px;
  top: 20px;
  z-index: 1;
}
/* line 1794, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_description_wrapper {
  background: #F8F8F8;
  float: right;
  width: 68%;
  padding: 25px 40px;
  min-height: 278px;
  box-sizing: border-box;
  position: relative;
}
/* line 1805, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .service_elements .divide_0, .rooms_wrapper .room_element .service_elements .divide_1, .rooms_wrapper .room_element .service_elements .divide_2 {
  width: 32%;
  display: inline-table;
}
/* line 1809, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .service_elements .divide_0 li, .rooms_wrapper .room_element .service_elements .divide_1 li, .rooms_wrapper .room_element .service_elements .divide_2 li {
  font-size: 13px;
  color: #E7E3D7;
  font-weight: lighter;
}
/* line 1814, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .service_elements .divide_0 li img, .rooms_wrapper .room_element .service_elements .divide_1 li img, .rooms_wrapper .room_element .service_elements .divide_2 li img {
  vertical-align: middle;
  margin-right: 5px;
}
/* line 1822, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_buttons_wrapper {
  position: absolute;
  top: 17px;
  right: 40px;
}
/* line 1827, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_buttons_wrapper img {
  vertical-align: middle;
  width: auto;
  display: none;
  height: 37px;
}
/* line 1834, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_buttons_wrapper .room_book {
  color: white;
  padding: 8px;
  background: #8fc9f1;
  width: 117px;
  box-sizing: border-box;
  display: inline-block;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  float: right;
  height: 33px;
}
/* line 1847, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_buttons_wrapper .room_book:hover {
  background: #bcdff7;
}
/* line 1853, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_types {
  position: absolute;
  top: 16px;
  right: 27%;
}
/* line 1858, ../sass/_template_specific.scss */
.rooms_wrapper .room_element .room_types a {
  text-decoration: none;
}

/* line 1865, ../sass/_template_specific.scss */
.room_type_wrapper, .room_complete_description {
  display: none;
}
/* line 1868, ../sass/_template_specific.scss */
.room_type_wrapper h3, .room_complete_description h3 {
  font-size: 24px;
  color: #E7E3D7;
  margin-bottom: 12px;
}
/* line 1874, ../sass/_template_specific.scss */
.room_type_wrapper > div, .room_complete_description > div {
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
}
/* line 1880, ../sass/_template_specific.scss */
.room_type_wrapper > .hide_popup, .room_complete_description > .hide_popup {
  color: white;
  font-family: 'Roboto', sans-serif;
  width: 50%;
  margin: 20px auto;
  text-align: center;
}
/* line 1887, ../sass/_template_specific.scss */
.room_type_wrapper > .hide_popup strong, .room_complete_description > .hide_popup strong {
  font-weight: bolder;
}

/* Rooms icons tooltips */
/* line 1894, ../sass/_template_specific.scss */
.tooltip {
  display: inline;
  position: relative;
  cursor: pointer;
}

/* line 1900, ../sass/_template_specific.scss */
.tooltip:hover:after {
  background: #C66047;
  border-radius: 5px;
  bottom: 26px;
  color: white;
  content: attr(title);
  left: 20%;
  top: -54px;
  height: 22px;
  padding: 5px 15px;
  position: absolute;
  z-index: 98;
  text-align: center;
  text-transform: uppercase;
}

/* line 1916, ../sass/_template_specific.scss */
.content_subtitle_wrapper {
  padding-top: 60px;
  padding-bottom: 60px;
  background: #E7E3D7;
  display: inline-block;
  width: 100%;
  margin-top: -5px;
}
/* line 1924, ../sass/_template_specific.scss */
.content_subtitle_wrapper.no_background {
  background: transparent;
  padding-bottom: 30px;
}
/* line 1928, ../sass/_template_specific.scss */
.content_subtitle_wrapper.no_background .subtitle_title {
  margin-bottom: 0;
}
/* line 1933, ../sass/_template_specific.scss */
.content_subtitle_wrapper .subtitle_title {
  font-size: 22px;
  text-align: center;
  text-transform: uppercase;
  color: #C66047;
  margin-bottom: 30px;
}
/* line 1941, ../sass/_template_specific.scss */
.content_subtitle_wrapper .subtitle_description {
  font-size: 14px;
  color: #000000;
  width: 80%;
  margin: 0 auto;
  text-align: center;
}

/*================  Banners x2 Cycle ===============*/
/* line 1951, ../sass/_template_specific.scss */
.bannerx2_image img {
  width: 100%;
}

/* line 1955, ../sass/_template_specific.scss */
.banners_x2_wrapper {
  margin-top: 30px;
  margin-bottom: 60px;
}

/* line 1960, ../sass/_template_specific.scss */
p.bannerx2_title_cycle {
  color: white;
  text-align: center;
  font-size: 20px;
  margin-bottom: 30px;
  font-weight: bold;
  text-transform: uppercase;
}
/* line 1968, ../sass/_template_specific.scss */
p.bannerx2_title_cycle:after {
  content: "";
  width: 55px;
  border-bottom: 2px solid white;
  display: block;
  margin: 17px auto 0;
}

/* line 1977, ../sass/_template_specific.scss */
.bannerx2_row {
  position: relative;
  clear: both;
  display: block;
  width: 100%;
  margin-bottom: 5px;
  height: 400px;
  overflow: hidden;
}

/* line 1989, ../sass/_template_specific.scss */
.bannerx2_row.left .bannerx2_image {
  float: left;
  width: 65%;
  margin-bottom: -5px;
  position: relative;
}
/* line 1996, ../sass/_template_specific.scss */
.bannerx2_row.left .bannerx2_text {
  width: 35%;
  float: right;
  position: absolute;
  right: 0px;
}

/* line 2005, ../sass/_template_specific.scss */
.bannerx2_row.left.inverted_banners .bannerx2_image {
  width: 35%;
}
/* line 2009, ../sass/_template_specific.scss */
.bannerx2_row.left.inverted_banners .bannerx2_text {
  width: 65%;
}
/* line 2012, ../sass/_template_specific.scss */
.bannerx2_row.left.inverted_banners .bannerx2_text .banner_center_container {
  position: relative;
  top: inherit;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  -o-transform: none;
  transform: none;
  margin-top: 30px;
  min-height: 370px;
}

/* line 2027, ../sass/_template_specific.scss */
.bannerx2_row.right .bannerx2_image {
  float: right;
  width: 65%;
  margin-bottom: -5px;
  position: relative;
}
/* line 2034, ../sass/_template_specific.scss */
.bannerx2_row.right .bannerx2_text {
  width: 35%;
  float: left;
  position: absolute;
  left: 0;
}

/* line 2043, ../sass/_template_specific.scss */
.bannerx2_row.right.inverted_banners .bannerx2_image {
  width: 35%;
}
/* line 2047, ../sass/_template_specific.scss */
.bannerx2_row.right.inverted_banners .bannerx2_text {
  width: 65%;
}
/* line 2050, ../sass/_template_specific.scss */
.bannerx2_row.right.inverted_banners .bannerx2_text .banner_center_container {
  position: relative;
  top: inherit;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  -o-transform: none;
  transform: none;
  margin-top: 30px;
  min-height: 370px;
}

/* line 2064, ../sass/_template_specific.scss */
.bannerx2_text {
  height: 100%;
  background: #C66047;
}

/* line 2069, ../sass/_template_specific.scss */
.banner_center_container {
  position: absolute;
  margin: auto;
  text-align: center;
  padding: 0 60px;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: 100%;
  box-sizing: border-box;
}
/* line 2087, ../sass/_template_specific.scss */
.banner_center_container img.banner_cycle_icon {
  margin-bottom: 8px;
}
/* line 2091, ../sass/_template_specific.scss */
.banner_center_container a.see_more_cycle {
  background: white;
  color: #8fc9f1;
  padding: 6px 13px;
  text-align: center;
  font-size: 17px;
  margin-top: 12px;
  display: inline-block;
}
/* line 2100, ../sass/_template_specific.scss */
.banner_center_container a.see_more_cycle:hover {
  opacity: 0.8;
}

/* line 2107, ../sass/_template_specific.scss */
.see_more_cycle_popup h2.popup_cycle_title {
  color: #C66047;
  font-size: 20px;
  margin-bottom: 30px;
  font-weight: bold;
  text-transform: uppercase;
}
/* line 2115, ../sass/_template_specific.scss */
.see_more_cycle_popup .popup_cycle_description {
  color: #656565;
  font-weight: 100;
  line-height: 24px;
  font-size: 17px;
}
/* line 2122, ../sass/_template_specific.scss */
.see_more_cycle_popup .download_button {
  display: none;
}

@-moz-document url-prefix() {}
/* line 2133, ../sass/_template_specific.scss */
.bannerx2_description {
  line-height: 22px;
  font-size: 16px;
  font-weight: 100;
  color: white;
  overflow: hidden;
}

/* line 2145, ../sass/_template_specific.scss */
.inverted_banners {
  min-height: 400px;
  height: auto;
}
/* line 2149, ../sass/_template_specific.scss */
.inverted_banners .see_more_cycle {
  bottom: 20px;
  background: white;
  color: #C66047;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  padding: 10px 25px;
  cursor: pointer;
}
/* line 2158, ../sass/_template_specific.scss */
.inverted_banners .bannerx2_text {
  position: relative !important;
}
/* line 2162, ../sass/_template_specific.scss */
.inverted_banners .bannerx2_image {
  height: 400px !important;
  overflow: hidden;
}
/* line 2167, ../sass/_template_specific.scss */
.inverted_banners .bannerx2_image img {
  min-height: 100%;
  max-width: none;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  max-height: 100%;
  width: initial;
}

/*======== Image Gallery ========*/
/* line 2177, ../sass/_template_specific.scss */
.gallery-image {
  background: white;
  padding: 60px 0 30px;
}

/* line 2182, ../sass/_template_specific.scss */
.gallery_filters_wrapper {
  text-align: center;
  margin-bottom: 50px;
}

/* line 2188, ../sass/_template_specific.scss */
body.lang_en .filter_gallery_element {
  font-size: 16px;
}

/* line 2193, ../sass/_template_specific.scss */
.filter_gallery_element {
  display: inline-block;
  background: #C66047;
  color: white;
  padding: 12px 5px;
  font-size: 15px;
  font-weight: 100;
  cursor: pointer;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  float: left;
  border-right: 6px solid white;
}
/* line 2207, ../sass/_template_specific.scss */
.filter_gallery_element:last-of-type {
  border-right: 0;
}
/* line 2211, ../sass/_template_specific.scss */
.filter_gallery_element:hover, .filter_gallery_element.active {
  opacity: 0.7;
}

/* line 2216, ../sass/_template_specific.scss */
.filter-gallery {
  background: #C66047;
  height: 75px;
  line-height: 75px;
  color: white;
  text-transform: uppercase;
  font-size: 24px;
  font-weight: 200;
  margin-bottom: 50px;
  cursor: pointer;
  position: relative;
}
/* line 2228, ../sass/_template_specific.scss */
.filter-gallery .element_hide {
  display: none;
}
/* line 2232, ../sass/_template_specific.scss */
.filter-gallery h3 {
  padding-left: 30px;
}
/* line 2236, ../sass/_template_specific.scss */
.filter-gallery span {
  display: inline-block;
  position: absolute;
  height: 75px;
  width: 75px;
  background: #E7E3D7 url(/img/holi2/arrow-newsletter.png) no-repeat center center;
  right: 0px;
  top: 0px;
  border-left: 2px solid white;
}
/* line 2248, ../sass/_template_specific.scss */
.filter-gallery ul {
  background: #eecfc7;
  font-size: 18px;
  line-height: 1;
  display: none;
}
/* line 2257, ../sass/_template_specific.scss */
.filter-gallery li {
  padding: 10px 30px;
  cursor: pointer;
  color: #C66047;
}
/* line 2263, ../sass/_template_specific.scss */
.filter-gallery li:hover {
  background: #e4b4a8;
}

/* line 2269, ../sass/_template_specific.scss */
.gallery-image-wrapper .big-img {
  width: 100%;
}

/* line 2274, ../sass/_template_specific.scss */
.big-img {
  text-align: center;
  /*max-height: 760px;
  overflow: hidden;*/
  -webkit-transition: all 2s;
  -moz-transition: all 2s;
  -ms-transition: all 2s;
  -o-transition: all 2s;
  transition: all 2s;
  position: relative;
}
/* line 2285, ../sass/_template_specific.scss */
.big-img .gallery_image_title {
  position: absolute;
  top: 40px;
  left: 40px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px 40px;
  text-transform: uppercase;
}
/* line 2295, ../sass/_template_specific.scss */
.big-img img.main_image {
  width: 100%;
  height: 500px;
  object-fit: cover;
}
/* line 2301, ../sass/_template_specific.scss */
.big-img img.gallery_previous_image, .big-img img.gallery_next_image {
  position: absolute;
  height: 70px;
  top: 0;
  bottom: 0;
  right: 30px;
  margin: auto;
  cursor: pointer;
}
/* line 2310, ../sass/_template_specific.scss */
.big-img img.gallery_previous_image:hover, .big-img img.gallery_next_image:hover {
  opacity: 0.8;
}
/* line 2315, ../sass/_template_specific.scss */
.big-img img.gallery_previous_image {
  -ms-transform: rotate(180deg);
  /* IE 9 */
  -webkit-transform: rotate(180deg);
  /* Chrome, Safari, Opera */
  transform: rotate(180deg);
  right: auto;
  left: 30px;
}
/* line 2323, ../sass/_template_specific.scss */
.big-img iframe {
  height: 300px;
}

/* line 2328, ../sass/_template_specific.scss */
.image-grid {
  margin-top: 20px;
}
/* line 2332, ../sass/_template_specific.scss */
.image-grid ul {
  overflow: hidden;
  text-align: center;
}
/* line 2337, ../sass/_template_specific.scss */
.image-grid li {
  display: inline-block;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  width: 50px;
  height: 50px;
  overflow: hidden;
  border: 1px solid white;
  position: relative;
}
/* line 2349, ../sass/_template_specific.scss */
.image-grid li img {
  position: absolute;
  top: 0;
  left: -50%;
  bottom: 0;
  right: -50%;
  margin: 0 auto;
  min-width: 120%;
  min-height: 50px;
  height: auto;
  vertical-align: bottom;
  cursor: pointer;
}
/* line 2364, ../sass/_template_specific.scss */
.image-grid li:hover, .image-grid li.active {
  border: 2px solid #E7E3D7;
}

/* line 2369, ../sass/_template_specific.scss */
.gallery-wrapper .big-img {
  width: 100%;
  height: 650px;
  overflow: hidden;
}

/* line 2377, ../sass/_template_specific.scss */
.info .check_privacy {
  width: 15px !important;
}
/* line 2382, ../sass/_template_specific.scss */
.info span a {
  text-align: left;
  display: inline-block;
  color: white;
  text-decoration: underline;
}
/* line 2390, ../sass/_template_specific.scss */
.info label.error[for="privacy"] {
  display: inline-block;
}

/*======== Ofertas =======*/
/* line 2396, ../sass/_template_specific.scss */
.cycle_banners_wrapper.offers_wrapper {
  background: white;
  margin-top: 30px;
}
/* line 2401, ../sass/_template_specific.scss */
.cycle_banners_wrapper.offers_wrapper .cycle_element .cycle_text_wrapper {
  background: #F5F6F8;
  float: right;
  position: relative;
  top: auto;
  right: auto;
  left: auto;
  bottom: auto;
}
/* line 2410, ../sass/_template_specific.scss */
.cycle_banners_wrapper.offers_wrapper .cycle_element .cycle_text_wrapper .center_div {
  position: relative;
  top: auto;
  right: auto;
  left: auto;
  bottom: auto;
  padding: 20px 75px;
  box-sizing: border-box;
}
/* line 2419, ../sass/_template_specific.scss */
.cycle_banners_wrapper.offers_wrapper .cycle_element .cycle_text_wrapper .center_div .cycle_description {
  overflow: hidden;
}
/* line 2423, ../sass/_template_specific.scss */
.cycle_banners_wrapper.offers_wrapper .cycle_element .cycle_text_wrapper .center_div a {
  margin-top: 21px;
}
/* line 2429, ../sass/_template_specific.scss */
.cycle_banners_wrapper.offers_wrapper .cycle_element .cycle_description hide {
  display: none;
}

/* line 2439, ../sass/_template_specific.scss */
.cycle_banners_wrapper {
  padding: 0 0 20px;
}
/* line 2442, ../sass/_template_specific.scss */
.cycle_banners_wrapper .slides > li {
  display: table;
  width: 100%;
  position: relative;
}
/* line 2448, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element {
  display: table;
  position: relative;
  width: 100%;
  margin-bottom: 20px;
}
/* line 2454, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .exceded {
  width: 50%;
  float: left;
  height: 275px;
  overflow: hidden;
}
/* line 2460, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .exceded img {
  min-width: 100%;
  max-width: none;
}
/* line 2466, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper {
  width: 50%;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  background: #F8F8F8;
}
/* line 2474, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_title {
  font-family: 'Lato', sans-serif;
  font-size: 15px;
  color: #C66047;
  margin-bottom: 35px;
  font-weight: bolder;
  position: relative;
}
/* line 2482, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_title .plus_image {
  position: absolute;
  right: 0;
  top: -10px;
}
/* line 2488, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_title strong {
  font-weight: bolder;
}
/* line 2493, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description {
  font-size: 15px;
  color: #757881;
  line-height: 28px;
}
/* line 2498, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .cycle_description strong {
  font-weight: bolder;
}
/* line 2503, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper a {
  display: inline-table;
  text-decoration: none;
  color: white;
  font-size: 15px;
  background: #C66047;
  padding: 4px 21px;
  margin-top: 26px;
  border-top: 1px solid;
  font-weight: 300;
  border-bottom: 1px solid;
  text-transform: uppercase;
  cursor: pointer;
}
/* line 2517, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper a:hover {
  background: #d2826e;
}
/* line 2521, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper a.button-promotion {
  background: #8fc9f1;
  font-weight: 500;
}
/* line 2525, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper a.button-promotion:hover {
  background: #bcdff7;
}
/* line 2531, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .cycle_text_wrapper .center_div {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  display: table;
  text-align: left;
  padding: 0 75px;
}
/* line 2542, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle, .cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle-offers {
  position: absolute;
  bottom: 80px;
  right: 25%;
  width: 25%;
  text-align: left;
  z-index: 2;
  padding: 0 75px;
  padding-right: 0;
  box-sizing: border-box;
}
/* line 2553, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle li, .cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle-offers li {
  display: inline-table;
  margin: 0 3px;
}
/* line 2558, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle span.bottom_lane, .cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle-offers span.bottom_lane {
  height: 4px;
  width: 60px;
  background: #C66047;
  display: block;
  opacity: 0.6;
  cursor: pointer;
}
/* line 2566, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle span.bottom_lane.flex-active, .cycle_banners_wrapper .cycle_element ol.flex-controlador-cycle-offers span.bottom_lane.flex-active {
  opacity: 1;
}
/* line 2573, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element.align_right .exceded {
  float: right;
}
/* line 2577, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element.align_right .cycle_text_wrapper {
  right: auto;
  left: 0;
}
/* line 2582, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element.align_right ol.flex-controlador-cycle, .cycle_banners_wrapper .cycle_element.align_right .flex-controlador-cycle-offers {
  right: auto;
  left: 0;
}
/* line 2589, ../sass/_template_specific.scss */
.cycle_banners_wrapper a.link_cycle {
  position: absolute;
  bottom: 66px;
  right: 53px;
  color: #ADADAD;
  font-style: italic;
  text-decoration: none;
  font-size: 18px;
  letter-spacing: 1px;
  padding: 9px 0;
  border-top: 1px solid #DAD3D2;
  border-bottom: 1px solid #DAD3D2;
}

/* line 2604, ../sass/_template_specific.scss */
.link_blocks {
  display: block;
  background: white;
  color: #C66047;
  padding: 10px 20px;
  margin: 20px auto 0;
  width: 120px;
}

/* line 2614, ../sass/_template_specific.scss */
.fancy_maps .fancybox-outer {
  padding: 15px !important;
}

/* line 2620, ../sass/_template_specific.scss */
.floating_booking#full_wrapper_booking {
  top: 0 !important;
  position: fixed !important;
  bottom: inherit !important;
  -webkit-box-shadow: 1px 1px 1px gray;
  -moz-box-shadow: 1px 1px 1px gray;
  box-shadow: 1px 1px 1px gray;
  -webkit-transform: none !important;
  -moz-transform: none !important;
  -ms-transform: none !important;
  -o-transform: none !important;
  transform: none !important;
}
/* line 2638, ../sass/_template_specific.scss */
.floating_booking#full_wrapper_booking #full_wrapper_booking {
  background: transparent;
}
/* line 2642, ../sass/_template_specific.scss */
.floating_booking#full_wrapper_booking .destination_wrapper input {
  background: white url("/img/hoteo/hotel_booking.png") no-repeat center left;
}
/* line 2646, ../sass/_template_specific.scss */
.floating_booking#full_wrapper_booking #full_wrapper_booking .date_box {
  background-color: white;
}
/* line 2650, ../sass/_template_specific.scss */
.floating_booking#full_wrapper_booking #full_wrapper_booking .selectric {
  background: white url("/img/hoteo/down_arrow.png") no-repeat 90%;
}
/* line 2654, ../sass/_template_specific.scss */
.floating_booking#full_wrapper_booking #full_wrapper_booking .boking_widget_inline .room_list_wrapper {
  background: white;
}
/* line 2658, ../sass/_template_specific.scss */
.floating_booking#full_wrapper_booking .guest_selector {
  background-color: white;
}
/* line 2662, ../sass/_template_specific.scss */
.floating_booking#full_wrapper_booking div#wrapper_booking {
  background: transparent !important;
  padding-top: 0;
}
/* line 2667, ../sass/_template_specific.scss */
.floating_booking#full_wrapper_booking .booking_form_title {
  position: relative;
  text-align: center;
  background: transparent;
}
/* line 2673, ../sass/_template_specific.scss */
.floating_booking#full_wrapper_booking .floating_room_list {
  top: 93%;
}

/* line 2678, ../sass/_template_specific.scss */
.fixed_datepicker {
  position: fixed !important;
  top: 75px !important;
}

/* line 2683, ../sass/_template_specific.scss */
.faldon_footer_wrapper {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  z-index: 1000;
  height: 275px;
  display: none;
  box-shadow: 1px 1px 5px 2px black;
}
/* line 2693, ../sass/_template_specific.scss */
.faldon_footer_wrapper .close_button_faldon {
  position: absolute;
  top: 20px;
  right: 20px;
  display: inline-block;
  z-index: 5;
  color: white;
  cursor: pointer;
  -moz-transition: -moz-transform, 0.4s;
  -o-transition: -o-transform, 0.4s;
  -webkit-transition: -webkit-transform, 0.4s;
  transition: transform, 0.4s;
}
/* line 2703, ../sass/_template_specific.scss */
.faldon_footer_wrapper .close_button_faldon:hover {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
/* line 2712, ../sass/_template_specific.scss */
.faldon_footer_wrapper:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
}
/* line 2719, ../sass/_template_specific.scss */
.faldon_footer_wrapper:after {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  content: "";
  margin: 10px;
  z-index: 2;
  border: 2px solid white;
}
/* line 2727, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content, .faldon_footer_wrapper .faldon_content_thanks {
  position: relative;
  height: 100%;
  z-index: 5;
}
/* line 2732, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block, .faldon_footer_wrapper .faldon_content_thanks .center_block {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: 100%;
  text-align: center;
  z-index: 3;
}
/* line 2738, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_title, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_title {
  font-family: "Montserrat Alternates";
  font-weight: bolder;
  color: #C66047;
  font-size: 28px;
}
/* line 2745, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_description, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_description {
  font-size: 13px;
  color: white;
  line-height: 23px;
  width: 750px;
  margin: 10px auto 0;
}
/* line 2753, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_link, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_link {
  display: inline-block;
  text-transform: uppercase;
  padding: 7px 15px;
  background: #C66047;
  color: white;
  text-decoration: none;
  margin-top: 20px;
  cursor: pointer;
  -moz-transition: background, 0.4s;
  -o-transition: background, 0.4s;
  -webkit-transition: background, 0.4s;
  transition: background, 0.4s;
}
/* line 2764, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_link:hover, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_link:hover {
  background: #a64a34;
}
/* line 2769, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter {
  margin-top: 10px;
  display: inline-block;
  width: 100%;
}
/* line 2774, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter input#faldon_email, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter input#faldon_email {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  height: 32px;
  width: 200px;
  box-sizing: border-box;
  padding-left: 10px;
  background: #E7E3D7;
  color: white;
}
/* line 2786, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter input#faldon_email::-webkit-input-placeholder, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter input#faldon_email::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: white;
}
/* line 2791, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter input#faldon_email::-moz-placeholder, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter input#faldon_email::-moz-placeholder {
  /* Firefox 19+ */
  color: white;
}
/* line 2796, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter input#faldon_email:-ms-input-placeholder, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter input#faldon_email:-ms-input-placeholder {
  /* IE 10+ */
  color: white;
}
/* line 2801, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter input#faldon_email:-moz-placeholder, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter input#faldon_email:-moz-placeholder {
  /* Firefox 18- */
  color: white;
}
/* line 2807, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter button, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter button {
  height: 32px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  background: #C66047;
  color: white;
  cursor: pointer;
  text-transform: uppercase;
  padding: 0 10px;
  -moz-transition: background, 0.4s;
  -o-transition: background, 0.4s;
  -webkit-transition: background, 0.4s;
  transition: background, 0.4s;
}
/* line 2820, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter button:hover, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter button:hover {
  background: #a64a34;
}
/* line 2825, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter .check_faldon, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter .check_faldon {
  margin-top: 10px;
}
/* line 2828, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter .check_faldon .check_privacy, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter .check_faldon .check_privacy {
  display: inline-block;
  vertical-align: middle;
}
/* line 2832, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter .check_faldon .check_privacy.error + .newsletter_popup, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter .check_faldon .check_privacy.error + .newsletter_popup {
  color: #ff6b6a !important;
}
/* line 2837, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter .check_faldon .newsletter_popup, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter .check_faldon .newsletter_popup {
  display: inline-block;
  vertical-align: middle;
  color: #C66047;
  font-size: 12px;
}
/* line 2845, ../sass/_template_specific.scss */
.faldon_footer_wrapper .faldon_content .center_block .faldon_newsletter .faldon_checkbox, .faldon_footer_wrapper .faldon_content_thanks .center_block .faldon_newsletter .faldon_checkbox {
  color: white;
  font-size: 12px;
}

/* line 2858, ../sass/_template_specific.scss */
#slider_container #full_wrapper_booking #wrapper_booking #booking .rooms_number_wrapper .rooms_number .selectricItems, #inner_slider_container #wrapper_booking #booking .rooms_number_wrapper .rooms_number .selectricItems {
  top: 120% !important;
  width: 100% !important;
}
/* line 2863, ../sass/_template_specific.scss */
#slider_container #full_wrapper_booking #wrapper_booking #booking .room_list_wrapper .room_list .room .children_selector, #inner_slider_container #wrapper_booking #booking .room_list_wrapper .room_list .room .children_selector {
  padding: 7px 4px 5px;
}
