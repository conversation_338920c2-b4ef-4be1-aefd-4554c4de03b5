/* -=- --- -- fonts -- --- -=- */
@import url("https://fonts.googleapis.com/css?family=Source+Sans+Pro");
@import url("https://fonts.googleapis.com/css?family=Oswald");
@import url(//fonts.googleapis.com/css?family=Montserrat|Source+Sans+Pro:400,300,700,600);
@font-face {
  font-family: ambrosia;
  src: url("/static_1/fonts/ambrosia_demo/ambrosia_demo.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
}
/* line 3, ../../../../sass/plugins/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 8, ../../../../sass/plugins/_mixins.scss */
.center_xy_before:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 13, ../../../../sass/plugins/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 17, ../../../../sass/plugins/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 21, ../../../../sass/plugins/_mixins.scss */
.center_y_before:before {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 25, ../../../../sass/plugins/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 30, ../../../../sass/plugins/_mixins.scss */
.fs:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
}

/* line 110, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:before, .icon-xcross:after {
  content: '';
  width: 100%;
  height: 2px;
  background: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 124, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -moz-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 61, ../../../../sass/plugins/_mixins.scss */
.display_flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* line 1, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown {
  margin: 40px 20px 20px;
  text-align: center;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  bottom: 180px;
  left: 48% !important;
  color: white;
}
/* line 9, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown div {
  display: inline-block;
  font-size: 16px;
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 0;
  padding-bottom: 0;
  text-transform: uppercase;
}
/* line 20, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .days {
  font-weight: 600;
}
/* line 23, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .date {
  display: block;
  font-size: 4.5rem;
}
/* line 28, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .title_format {
  font-weight: 600;
}

@media screen and (max-width: 800px) {
  /* line 35, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown {
    display: inline-block;
    width: 380px;
    top: 330px;
    left: 45% !important;
  }
  /* line 40, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown div {
    font-size: 10px;
  }
  /* line 43, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown .date {
    font-size: 2.5rem;
  }
}
@font-face {
  font-family: 'FontAwesome';
  src: url("/static_1/fonts/fontawesome/fontawesome/fontawesome-webfont.eot?v=4.7.0");
  src: url("/static_1/fonts/fontawesome/fontawesome-webfont.eot?#iefix&v=4.7.0") format("embedded-opentype"), url("/static_1/fonts/fontawesome/fontawesome-webfont.woff2?v=4.7.0") format("woff2"), url("/static_1/fonts/fontawesome/fontawesome-webfont.woff?v=4.7.0") format("woff"), url("/static_1/fonts/fontawesome/fontawesome-webfont.ttf?v=4.7.0") format("truetype"), url("/static_1/fonts/fontawesome/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular") format("svg");
  font-weight: normal;
  font-style: normal;
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lg {
  font-size: 1.33333333em;
  line-height: .75em;
  vertical-align: -15%;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-3x {
  font-size: 3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-4x {
  font-size: 4em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-5x {
  font-size: 5em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fw {
  width: 1.28571429em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul > li {
  position: relative;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: .14285714em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li.fa-lg {
  left: -1.85714286em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-border {
  padding: .2em .25em .15em;
  border: solid .08em #eee;
  border-radius: .1em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-left {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-right {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-left {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-right {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  transform: scale(-1, 1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical {
  filter: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x, .fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x {
  line-height: inherit;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inverse {
  color: #fff;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glass:before {
  content: "\f000";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-music:before {
  content: "\f001";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search:before {
  content: "\f002";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-o:before {
  content: "\f003";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart:before {
  content: "\f004";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star:before {
  content: "\f005";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-o:before {
  content: "\f006";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user:before {
  content: "\f007";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-film:before {
  content: "\f008";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-large:before {
  content: "\f009";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th:before {
  content: "\f00a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-list:before {
  content: "\f00b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check:before {
  content: "\f00c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-remove:before, .fa-close:before, .fa-times:before {
  content: "\f00d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-plus:before {
  content: "\f00e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-minus:before {
  content: "\f010";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-power-off:before {
  content: "\f011";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signal:before {
  content: "\f012";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gear:before, .fa-cog:before {
  content: "\f013";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash-o:before {
  content: "\f014";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-home:before {
  content: "\f015";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-o:before {
  content: "\f016";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clock-o:before {
  content: "\f017";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-road:before {
  content: "\f018";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-download:before {
  content: "\f019";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-down:before {
  content: "\f01a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-up:before {
  content: "\f01b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inbox:before {
  content: "\f01c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle-o:before {
  content: "\f01d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-right:before, .fa-repeat:before {
  content: "\f01e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-refresh:before {
  content: "\f021";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-alt:before {
  content: "\f022";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lock:before {
  content: "\f023";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag:before {
  content: "\f024";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-headphones:before {
  content: "\f025";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-off:before {
  content: "\f026";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-down:before {
  content: "\f027";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-up:before {
  content: "\f028";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qrcode:before {
  content: "\f029";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-barcode:before {
  content: "\f02a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tag:before {
  content: "\f02b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tags:before {
  content: "\f02c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-book:before {
  content: "\f02d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark:before {
  content: "\f02e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-print:before {
  content: "\f02f";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera:before {
  content: "\f030";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-font:before {
  content: "\f031";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bold:before {
  content: "\f032";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-italic:before {
  content: "\f033";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-height:before {
  content: "\f034";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-width:before {
  content: "\f035";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-left:before {
  content: "\f036";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-center:before {
  content: "\f037";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-right:before {
  content: "\f038";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-justify:before {
  content: "\f039";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list:before {
  content: "\f03a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dedent:before, .fa-outdent:before {
  content: "\f03b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-indent:before {
  content: "\f03c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-video-camera:before {
  content: "\f03d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-photo:before, .fa-image:before, .fa-picture-o:before {
  content: "\f03e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil:before {
  content: "\f040";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-marker:before {
  content: "\f041";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adjust:before {
  content: "\f042";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tint:before {
  content: "\f043";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edit:before, .fa-pencil-square-o:before {
  content: "\f044";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square-o:before {
  content: "\f045";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square-o:before {
  content: "\f046";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows:before {
  content: "\f047";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-backward:before {
  content: "\f048";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-backward:before {
  content: "\f049";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-backward:before {
  content: "\f04a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play:before {
  content: "\f04b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause:before {
  content: "\f04c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop:before {
  content: "\f04d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forward:before {
  content: "\f04e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-forward:before {
  content: "\f050";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-forward:before {
  content: "\f051";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eject:before {
  content: "\f052";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-left:before {
  content: "\f053";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-right:before {
  content: "\f054";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-circle:before {
  content: "\f055";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-circle:before {
  content: "\f056";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle:before {
  content: "\f057";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle:before {
  content: "\f058";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle:before {
  content: "\f059";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info-circle:before {
  content: "\f05a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crosshairs:before {
  content: "\f05b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle-o:before {
  content: "\f05c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle-o:before {
  content: "\f05d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ban:before {
  content: "\f05e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-left:before {
  content: "\f060";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-right:before {
  content: "\f061";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-up:before {
  content: "\f062";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-down:before {
  content: "\f063";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-forward:before, .fa-share:before {
  content: "\f064";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expand:before {
  content: "\f065";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compress:before {
  content: "\f066";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus:before {
  content: "\f067";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus:before {
  content: "\f068";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asterisk:before {
  content: "\f069";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation-circle:before {
  content: "\f06a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gift:before {
  content: "\f06b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leaf:before {
  content: "\f06c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire:before {
  content: "\f06d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye:before {
  content: "\f06e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye-slash:before {
  content: "\f070";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-warning:before, .fa-exclamation-triangle:before {
  content: "\f071";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plane:before {
  content: "\f072";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar:before {
  content: "\f073";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-random:before {
  content: "\f074";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment:before {
  content: "\f075";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magnet:before {
  content: "\f076";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-up:before {
  content: "\f077";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-down:before {
  content: "\f078";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-retweet:before {
  content: "\f079";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-cart:before {
  content: "\f07a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder:before {
  content: "\f07b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open:before {
  content: "\f07c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-v:before {
  content: "\f07d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-h:before {
  content: "\f07e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bar-chart-o:before, .fa-bar-chart:before {
  content: "\f080";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter-square:before {
  content: "\f081";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-square:before {
  content: "\f082";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera-retro:before {
  content: "\f083";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-key:before {
  content: "\f084";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gears:before, .fa-cogs:before {
  content: "\f085";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments:before {
  content: "\f086";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-up:before {
  content: "\f087";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-down:before {
  content: "\f088";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half:before {
  content: "\f089";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart-o:before {
  content: "\f08a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-out:before {
  content: "\f08b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin-square:before {
  content: "\f08c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumb-tack:before {
  content: "\f08d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link:before {
  content: "\f08e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-in:before {
  content: "\f090";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trophy:before {
  content: "\f091";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-square:before {
  content: "\f092";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-upload:before {
  content: "\f093";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lemon-o:before {
  content: "\f094";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone:before {
  content: "\f095";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square-o:before {
  content: "\f096";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark-o:before {
  content: "\f097";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone-square:before {
  content: "\f098";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter:before {
  content: "\f099";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-f:before, .fa-facebook:before {
  content: "\f09a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github:before {
  content: "\f09b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock:before {
  content: "\f09c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card:before {
  content: "\f09d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-feed:before, .fa-rss:before {
  content: "\f09e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hdd-o:before {
  content: "\f0a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullhorn:before {
  content: "\f0a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell:before {
  content: "\f0f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-certificate:before {
  content: "\f0a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-right:before {
  content: "\f0a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-left:before {
  content: "\f0a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-up:before {
  content: "\f0a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-down:before {
  content: "\f0a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-left:before {
  content: "\f0a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-right:before {
  content: "\f0a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-up:before {
  content: "\f0aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-down:before {
  content: "\f0ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-globe:before {
  content: "\f0ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wrench:before {
  content: "\f0ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tasks:before {
  content: "\f0ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-filter:before {
  content: "\f0b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-briefcase:before {
  content: "\f0b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-alt:before {
  content: "\f0b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-group:before, .fa-users:before {
  content: "\f0c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chain:before, .fa-link:before {
  content: "\f0c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud:before {
  content: "\f0c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flask:before {
  content: "\f0c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cut:before, .fa-scissors:before {
  content: "\f0c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copy:before, .fa-files-o:before {
  content: "\f0c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paperclip:before {
  content: "\f0c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-save:before, .fa-floppy-o:before {
  content: "\f0c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square:before {
  content: "\f0c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-navicon:before, .fa-reorder:before, .fa-bars:before {
  content: "\f0c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ul:before {
  content: "\f0ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ol:before {
  content: "\f0cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-strikethrough:before {
  content: "\f0cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-underline:before {
  content: "\f0cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-table:before {
  content: "\f0ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magic:before {
  content: "\f0d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-truck:before {
  content: "\f0d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest:before {
  content: "\f0d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-square:before {
  content: "\f0d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-square:before {
  content: "\f0d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus:before {
  content: "\f0d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-money:before {
  content: "\f0d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-down:before {
  content: "\f0d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-up:before {
  content: "\f0d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-left:before {
  content: "\f0d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-right:before {
  content: "\f0da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-columns:before {
  content: "\f0db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unsorted:before, .fa-sort:before {
  content: "\f0dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-down:before, .fa-sort-desc:before {
  content: "\f0dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-up:before, .fa-sort-asc:before {
  content: "\f0de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope:before {
  content: "\f0e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin:before {
  content: "\f0e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-left:before, .fa-undo:before {
  content: "\f0e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-legal:before, .fa-gavel:before {
  content: "\f0e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashboard:before, .fa-tachometer:before {
  content: "\f0e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment-o:before {
  content: "\f0e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments-o:before {
  content: "\f0e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flash:before, .fa-bolt:before {
  content: "\f0e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sitemap:before {
  content: "\f0e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-umbrella:before {
  content: "\f0e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paste:before, .fa-clipboard:before {
  content: "\f0ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lightbulb-o:before {
  content: "\f0eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exchange:before {
  content: "\f0ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-download:before {
  content: "\f0ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-upload:before {
  content: "\f0ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-md:before {
  content: "\f0f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stethoscope:before {
  content: "\f0f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-suitcase:before {
  content: "\f0f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-o:before {
  content: "\f0a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-coffee:before {
  content: "\f0f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cutlery:before {
  content: "\f0f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text-o:before {
  content: "\f0f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building-o:before {
  content: "\f0f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hospital-o:before {
  content: "\f0f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ambulance:before {
  content: "\f0f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medkit:before {
  content: "\f0fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fighter-jet:before {
  content: "\f0fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-beer:before {
  content: "\f0fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-h-square:before {
  content: "\f0fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square:before {
  content: "\f0fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-left:before {
  content: "\f100";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-right:before {
  content: "\f101";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-up:before {
  content: "\f102";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-down:before {
  content: "\f103";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-left:before {
  content: "\f104";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-right:before {
  content: "\f105";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-up:before {
  content: "\f106";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-down:before {
  content: "\f107";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-desktop:before {
  content: "\f108";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-laptop:before {
  content: "\f109";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tablet:before {
  content: "\f10a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mobile-phone:before, .fa-mobile:before {
  content: "\f10b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o:before {
  content: "\f10c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-left:before {
  content: "\f10d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-right:before {
  content: "\f10e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spinner:before {
  content: "\f110";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle:before {
  content: "\f111";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply:before, .fa-reply:before {
  content: "\f112";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-alt:before {
  content: "\f113";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-o:before {
  content: "\f114";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open-o:before {
  content: "\f115";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-smile-o:before {
  content: "\f118";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-frown-o:before {
  content: "\f119";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meh-o:before {
  content: "\f11a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gamepad:before {
  content: "\f11b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-keyboard-o:before {
  content: "\f11c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-o:before {
  content: "\f11d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-checkered:before {
  content: "\f11e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-terminal:before {
  content: "\f120";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code:before {
  content: "\f121";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply-all:before, .fa-reply-all:before {
  content: "\f122";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before {
  content: "\f123";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-location-arrow:before {
  content: "\f124";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crop:before {
  content: "\f125";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code-fork:before {
  content: "\f126";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlink:before, .fa-chain-broken:before {
  content: "\f127";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question:before {
  content: "\f128";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info:before {
  content: "\f129";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation:before {
  content: "\f12a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superscript:before {
  content: "\f12b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subscript:before {
  content: "\f12c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eraser:before {
  content: "\f12d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-puzzle-piece:before {
  content: "\f12e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone:before {
  content: "\f130";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone-slash:before {
  content: "\f131";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shield:before {
  content: "\f132";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-o:before {
  content: "\f133";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire-extinguisher:before {
  content: "\f134";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rocket:before {
  content: "\f135";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-maxcdn:before {
  content: "\f136";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-left:before {
  content: "\f137";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-right:before {
  content: "\f138";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-up:before {
  content: "\f139";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-down:before {
  content: "\f13a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-html5:before {
  content: "\f13b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-css3:before {
  content: "\f13c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-anchor:before {
  content: "\f13d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock-alt:before {
  content: "\f13e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullseye:before {
  content: "\f140";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-h:before {
  content: "\f141";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-v:before {
  content: "\f142";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rss-square:before {
  content: "\f143";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle:before {
  content: "\f144";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ticket:before {
  content: "\f145";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square:before {
  content: "\f146";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square-o:before {
  content: "\f147";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-up:before {
  content: "\f148";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-down:before {
  content: "\f149";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square:before {
  content: "\f14a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil-square:before {
  content: "\f14b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link-square:before {
  content: "\f14c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square:before {
  content: "\f14d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compass:before {
  content: "\f14e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-down:before, .fa-caret-square-o-down:before {
  content: "\f150";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-up:before, .fa-caret-square-o-up:before {
  content: "\f151";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-right:before, .fa-caret-square-o-right:before {
  content: "\f152";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-euro:before, .fa-eur:before {
  content: "\f153";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gbp:before {
  content: "\f154";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dollar:before, .fa-usd:before {
  content: "\f155";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rupee:before, .fa-inr:before {
  content: "\f156";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before {
  content: "\f157";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ruble:before, .fa-rouble:before, .fa-rub:before {
  content: "\f158";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-won:before, .fa-krw:before {
  content: "\f159";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitcoin:before, .fa-btc:before {
  content: "\f15a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file:before {
  content: "\f15b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text:before {
  content: "\f15c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-asc:before {
  content: "\f15d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-desc:before {
  content: "\f15e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-asc:before {
  content: "\f160";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-desc:before {
  content: "\f161";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-asc:before {
  content: "\f162";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-desc:before {
  content: "\f163";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-up:before {
  content: "\f164";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-down:before {
  content: "\f165";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-square:before {
  content: "\f166";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube:before {
  content: "\f167";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing:before {
  content: "\f168";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing-square:before {
  content: "\f169";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-play:before {
  content: "\f16a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dropbox:before {
  content: "\f16b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-overflow:before {
  content: "\f16c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-instagram:before {
  content: "\f16d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flickr:before {
  content: "\f16e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adn:before {
  content: "\f170";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket:before {
  content: "\f171";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket-square:before {
  content: "\f172";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr:before {
  content: "\f173";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr-square:before {
  content: "\f174";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-down:before {
  content: "\f175";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-up:before {
  content: "\f176";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-left:before {
  content: "\f177";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-right:before {
  content: "\f178";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-apple:before {
  content: "\f179";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-windows:before {
  content: "\f17a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-android:before {
  content: "\f17b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linux:before {
  content: "\f17c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dribbble:before {
  content: "\f17d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skype:before {
  content: "\f17e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-foursquare:before {
  content: "\f180";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trello:before {
  content: "\f181";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-female:before {
  content: "\f182";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-male:before {
  content: "\f183";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gittip:before, .fa-gratipay:before {
  content: "\f184";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sun-o:before {
  content: "\f185";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-moon-o:before {
  content: "\f186";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-archive:before {
  content: "\f187";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bug:before {
  content: "\f188";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vk:before {
  content: "\f189";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-weibo:before {
  content: "\f18a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-renren:before {
  content: "\f18b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pagelines:before {
  content: "\f18c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-exchange:before {
  content: "\f18d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-right:before {
  content: "\f18e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-left:before {
  content: "\f190";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-left:before, .fa-caret-square-o-left:before {
  content: "\f191";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dot-circle-o:before {
  content: "\f192";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair:before {
  content: "\f193";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo-square:before {
  content: "\f194";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-turkish-lira:before, .fa-try:before {
  content: "\f195";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square-o:before {
  content: "\f196";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-space-shuttle:before {
  content: "\f197";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slack:before {
  content: "\f198";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-square:before {
  content: "\f199";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wordpress:before {
  content: "\f19a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-openid:before {
  content: "\f19b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-institution:before, .fa-bank:before, .fa-university:before {
  content: "\f19c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mortar-board:before, .fa-graduation-cap:before {
  content: "\f19d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yahoo:before {
  content: "\f19e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google:before {
  content: "\f1a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit:before {
  content: "\f1a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-square:before {
  content: "\f1a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon-circle:before {
  content: "\f1a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon:before {
  content: "\f1a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-delicious:before {
  content: "\f1a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-digg:before {
  content: "\f1a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-pp:before {
  content: "\f1a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-alt:before {
  content: "\f1a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drupal:before {
  content: "\f1a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-joomla:before {
  content: "\f1aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-language:before {
  content: "\f1ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fax:before {
  content: "\f1ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building:before {
  content: "\f1ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-child:before {
  content: "\f1ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paw:before {
  content: "\f1b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spoon:before {
  content: "\f1b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cube:before {
  content: "\f1b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cubes:before {
  content: "\f1b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance:before {
  content: "\f1b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance-square:before {
  content: "\f1b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam:before {
  content: "\f1b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam-square:before {
  content: "\f1b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-recycle:before {
  content: "\f1b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-automobile:before, .fa-car:before {
  content: "\f1b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cab:before, .fa-taxi:before {
  content: "\f1ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tree:before {
  content: "\f1bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spotify:before {
  content: "\f1bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deviantart:before {
  content: "\f1bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soundcloud:before {
  content: "\f1be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-database:before {
  content: "\f1c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-pdf-o:before {
  content: "\f1c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-word-o:before {
  content: "\f1c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-excel-o:before {
  content: "\f1c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-powerpoint-o:before {
  content: "\f1c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before {
  content: "\f1c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-zip-o:before, .fa-file-archive-o:before {
  content: "\f1c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-sound-o:before, .fa-file-audio-o:before {
  content: "\f1c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-movie-o:before, .fa-file-video-o:before {
  content: "\f1c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-code-o:before {
  content: "\f1c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vine:before {
  content: "\f1ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codepen:before {
  content: "\f1cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-jsfiddle:before {
  content: "\f1cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before {
  content: "\f1cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o-notch:before {
  content: "\f1ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ra:before, .fa-resistance:before, .fa-rebel:before {
  content: "\f1d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ge:before, .fa-empire:before {
  content: "\f1d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git-square:before {
  content: "\f1d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git:before {
  content: "\f1d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before {
  content: "\f1d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tencent-weibo:before {
  content: "\f1d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qq:before {
  content: "\f1d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wechat:before, .fa-weixin:before {
  content: "\f1d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send:before, .fa-paper-plane:before {
  content: "\f1d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send-o:before, .fa-paper-plane-o:before {
  content: "\f1d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-history:before {
  content: "\f1da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-thin:before {
  content: "\f1db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-header:before {
  content: "\f1dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paragraph:before {
  content: "\f1dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sliders:before {
  content: "\f1de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt:before {
  content: "\f1e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt-square:before {
  content: "\f1e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bomb:before {
  content: "\f1e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soccer-ball-o:before, .fa-futbol-o:before {
  content: "\f1e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tty:before {
  content: "\f1e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-binoculars:before {
  content: "\f1e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plug:before {
  content: "\f1e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slideshare:before {
  content: "\f1e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitch:before {
  content: "\f1e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yelp:before {
  content: "\f1e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-newspaper-o:before {
  content: "\f1ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wifi:before {
  content: "\f1eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calculator:before {
  content: "\f1ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paypal:before {
  content: "\f1ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-wallet:before {
  content: "\f1ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-visa:before {
  content: "\f1f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-mastercard:before {
  content: "\f1f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-discover:before {
  content: "\f1f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-amex:before {
  content: "\f1f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-paypal:before {
  content: "\f1f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-stripe:before {
  content: "\f1f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash:before {
  content: "\f1f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash-o:before {
  content: "\f1f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash:before {
  content: "\f1f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copyright:before {
  content: "\f1f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-at:before {
  content: "\f1fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eyedropper:before {
  content: "\f1fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paint-brush:before {
  content: "\f1fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-birthday-cake:before {
  content: "\f1fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-area-chart:before {
  content: "\f1fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pie-chart:before {
  content: "\f200";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-line-chart:before {
  content: "\f201";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm:before {
  content: "\f202";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm-square:before {
  content: "\f203";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-off:before {
  content: "\f204";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-on:before {
  content: "\f205";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bicycle:before {
  content: "\f206";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bus:before {
  content: "\f207";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ioxhost:before {
  content: "\f208";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angellist:before {
  content: "\f209";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc:before {
  content: "\f20a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shekel:before, .fa-sheqel:before, .fa-ils:before {
  content: "\f20b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meanpath:before {
  content: "\f20c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-buysellads:before {
  content: "\f20d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-connectdevelop:before {
  content: "\f20e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashcube:before {
  content: "\f210";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forumbee:before {
  content: "\f211";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leanpub:before {
  content: "\f212";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sellsy:before {
  content: "\f213";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shirtsinbulk:before {
  content: "\f214";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-simplybuilt:before {
  content: "\f215";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skyatlas:before {
  content: "\f216";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-plus:before {
  content: "\f217";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-arrow-down:before {
  content: "\f218";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-diamond:before {
  content: "\f219";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ship:before {
  content: "\f21a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-secret:before {
  content: "\f21b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-motorcycle:before {
  content: "\f21c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-street-view:before {
  content: "\f21d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heartbeat:before {
  content: "\f21e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus:before {
  content: "\f221";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars:before {
  content: "\f222";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mercury:before {
  content: "\f223";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-intersex:before, .fa-transgender:before {
  content: "\f224";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-transgender-alt:before {
  content: "\f225";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-double:before {
  content: "\f226";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-double:before {
  content: "\f227";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-mars:before {
  content: "\f228";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke:before {
  content: "\f229";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-v:before {
  content: "\f22a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-h:before {
  content: "\f22b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-neuter:before {
  content: "\f22c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-genderless:before {
  content: "\f22d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-official:before {
  content: "\f230";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-p:before {
  content: "\f231";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-whatsapp:before {
  content: "\f232";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-server:before {
  content: "\f233";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-plus:before {
  content: "\f234";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-times:before {
  content: "\f235";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hotel:before, .fa-bed:before {
  content: "\f236";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viacoin:before {
  content: "\f237";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-train:before {
  content: "\f238";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subway:before {
  content: "\f239";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medium:before {
  content: "\f23a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yc:before, .fa-y-combinator:before {
  content: "\f23b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-optin-monster:before {
  content: "\f23c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opencart:before {
  content: "\f23d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expeditedssl:before {
  content: "\f23e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before {
  content: "\f240";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-3:before, .fa-battery-three-quarters:before {
  content: "\f241";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-2:before, .fa-battery-half:before {
  content: "\f242";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-1:before, .fa-battery-quarter:before {
  content: "\f243";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-0:before, .fa-battery-empty:before {
  content: "\f244";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mouse-pointer:before {
  content: "\f245";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-i-cursor:before {
  content: "\f246";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-group:before {
  content: "\f247";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-ungroup:before {
  content: "\f248";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note:before {
  content: "\f249";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note-o:before {
  content: "\f24a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-jcb:before {
  content: "\f24b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-diners-club:before {
  content: "\f24c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clone:before {
  content: "\f24d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-balance-scale:before {
  content: "\f24e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-o:before {
  content: "\f250";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-1:before, .fa-hourglass-start:before {
  content: "\f251";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-2:before, .fa-hourglass-half:before {
  content: "\f252";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-3:before, .fa-hourglass-end:before {
  content: "\f253";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass:before {
  content: "\f254";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-grab-o:before, .fa-hand-rock-o:before {
  content: "\f255";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-stop-o:before, .fa-hand-paper-o:before {
  content: "\f256";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-scissors-o:before {
  content: "\f257";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-lizard-o:before {
  content: "\f258";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-spock-o:before {
  content: "\f259";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-pointer-o:before {
  content: "\f25a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-peace-o:before {
  content: "\f25b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trademark:before {
  content: "\f25c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-registered:before {
  content: "\f25d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-creative-commons:before {
  content: "\f25e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg:before {
  content: "\f260";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg-circle:before {
  content: "\f261";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tripadvisor:before {
  content: "\f262";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki:before {
  content: "\f263";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki-square:before {
  content: "\f264";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-get-pocket:before {
  content: "\f265";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wikipedia-w:before {
  content: "\f266";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-safari:before {
  content: "\f267";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chrome:before {
  content: "\f268";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-firefox:before {
  content: "\f269";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opera:before {
  content: "\f26a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-internet-explorer:before {
  content: "\f26b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tv:before, .fa-television:before {
  content: "\f26c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-contao:before {
  content: "\f26d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-500px:before {
  content: "\f26e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-amazon:before {
  content: "\f270";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-plus-o:before {
  content: "\f271";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-minus-o:before {
  content: "\f272";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-times-o:before {
  content: "\f273";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-check-o:before {
  content: "\f274";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-industry:before {
  content: "\f275";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-pin:before {
  content: "\f276";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-signs:before {
  content: "\f277";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-o:before {
  content: "\f278";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map:before {
  content: "\f279";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting:before {
  content: "\f27a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting-o:before {
  content: "\f27b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-houzz:before {
  content: "\f27c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo:before {
  content: "\f27d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-black-tie:before {
  content: "\f27e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fonticons:before {
  content: "\f280";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-alien:before {
  content: "\f281";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edge:before {
  content: "\f282";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card-alt:before {
  content: "\f283";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codiepie:before {
  content: "\f284";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-modx:before {
  content: "\f285";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fort-awesome:before {
  content: "\f286";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-usb:before {
  content: "\f287";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-product-hunt:before {
  content: "\f288";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mixcloud:before {
  content: "\f289";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-scribd:before {
  content: "\f28a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle:before {
  content: "\f28b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle-o:before {
  content: "\f28c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle:before {
  content: "\f28d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle-o:before {
  content: "\f28e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-bag:before {
  content: "\f290";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-basket:before {
  content: "\f291";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hashtag:before {
  content: "\f292";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth:before {
  content: "\f293";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth-b:before {
  content: "\f294";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-percent:before {
  content: "\f295";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gitlab:before {
  content: "\f296";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpbeginner:before {
  content: "\f297";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpforms:before {
  content: "\f298";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envira:before {
  content: "\f299";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-universal-access:before {
  content: "\f29a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair-alt:before {
  content: "\f29b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle-o:before {
  content: "\f29c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-blind:before {
  content: "\f29d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-audio-description:before {
  content: "\f29e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-control-phone:before {
  content: "\f2a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-braille:before {
  content: "\f2a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-assistive-listening-systems:before {
  content: "\f2a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before {
  content: "\f2a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide:before {
  content: "\f2a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide-g:before {
  content: "\f2a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signing:before, .fa-sign-language:before {
  content: "\f2a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-low-vision:before {
  content: "\f2a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo:before {
  content: "\f2a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo-square:before {
  content: "\f2aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat:before {
  content: "\f2ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-ghost:before {
  content: "\f2ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-square:before {
  content: "\f2ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper:before {
  content: "\f2ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-first-order:before {
  content: "\f2b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yoast:before {
  content: "\f2b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-themeisle:before {
  content: "\f2b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-circle:before, .fa-google-plus-official:before {
  content: "\f2b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fa:before, .fa-font-awesome:before {
  content: "\f2b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-handshake-o:before {
  content: "\f2b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open:before {
  content: "\f2b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open-o:before {
  content: "\f2b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linode:before {
  content: "\f2b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book:before {
  content: "\f2b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book-o:before {
  content: "\f2ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard:before, .fa-address-card:before {
  content: "\f2bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard-o:before, .fa-address-card-o:before {
  content: "\f2bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle:before {
  content: "\f2bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle-o:before {
  content: "\f2be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-o:before {
  content: "\f2c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-id-badge:before {
  content: "\f2c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license:before, .fa-id-card:before {
  content: "\f2c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license-o:before, .fa-id-card-o:before {
  content: "\f2c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quora:before {
  content: "\f2c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-free-code-camp:before {
  content: "\f2c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-telegram:before {
  content: "\f2c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before {
  content: "\f2c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-3:before, .fa-thermometer-three-quarters:before {
  content: "\f2c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-2:before, .fa-thermometer-half:before {
  content: "\f2c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-1:before, .fa-thermometer-quarter:before {
  content: "\f2ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-0:before, .fa-thermometer-empty:before {
  content: "\f2cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shower:before {
  content: "\f2cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bathtub:before, .fa-s15:before, .fa-bath:before {
  content: "\f2cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-podcast:before {
  content: "\f2ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-maximize:before {
  content: "\f2d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-minimize:before {
  content: "\f2d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-restore:before {
  content: "\f2d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle:before, .fa-window-close:before {
  content: "\f2d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle-o:before, .fa-window-close-o:before {
  content: "\f2d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bandcamp:before {
  content: "\f2d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-grav:before {
  content: "\f2d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-etsy:before {
  content: "\f2d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-imdb:before {
  content: "\f2d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ravelry:before {
  content: "\f2d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eercast:before {
  content: "\f2da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microchip:before {
  content: "\f2db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snowflake-o:before {
  content: "\f2dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superpowers:before {
  content: "\f2dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpexplorer:before {
  content: "\f2de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meetup:before {
  content: "\f2e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

/* line 3, ../../../../sass/booking/_booking_engine_5.scss */
.booking_widget {
  position: relative;
}

/* line 7, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 {
  width: 275px;
  display: block;
}
/* line 11, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 form.booking_form {
  padding: 0 !important;
}
/* line 15, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 .booking_form_title {
  background: #383838;
}

/* line 20, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper {
  border-bottom: 1px solid lightgray;
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat 92% 70% !important;
}
/* line 24, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
  display: inline-block;
  margin: 10px 0 0 10px;
}

/* line 32, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input {
  border: none;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 25px;
  font-weight: 600;
  background: white;
}
/* line 40, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input::-webkit-input-placeholder {
  color: #585d63;
}
/* line 44, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input:-moz-placeholder {
  /* Firefox 18- */
  color: #585d63;
}
/* line 48, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input::-moz-placeholder {
  /* Firefox 19+ */
  color: #585d63;
}
/* line 52, ../../../../sass/booking/_booking_engine_5.scss */
.destination_wrapper input:-ms-input-placeholder {
  color: #585d63;
}

/* line 57, ../../../../sass/booking/_booking_engine_5.scss */
.entry_date_wrapper {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 66.6%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}

/* line 72, ../../../../sass/booking/_booking_engine_5.scss */
.departure_date_wrapper, .half_size {
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-top: 0;
  width: 50%;
  height: 70px;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}
/* line 86, ../../../../sass/booking/_booking_engine_5.scss */
.departure_date_wrapper .date_day, .half_size .date_day {
  font-size: 21px !important;
}

/* line 91, ../../../../sass/booking/_booking_engine_5.scss */
.nights_number_wrapper {
  float: right;
  height: 70px;
  border-bottom: 1px solid lightgrey;
  box-sizing: border-box;
  width: 33.3%;
  padding: 5px 10px;
}

/* line 100, ../../../../sass/booking/_booking_engine_5.scss */
.num_nights_label, .entry_date_label, .children_label, .rooms_label, .adults_label, .promocode_label, .departure_date_label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/* line 106, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 30%;
}

/*===== Entry date =====*/
/* line 113, ../../../../sass/booking/_booking_engine_5.scss */
.date_box.entry_date, .date_box.departure_date {
  font-size: 20px;
  background: url(/static_1/images/booking_5/calendar.png) no-repeat center right;
  margin-top: 9px;
  background-size: 29px;
}
/* line 119, ../../../../sass/booking/_booking_engine_5.scss */
.date_box.entry_date .date_day, .date_box.departure_date .date_day {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 25px;
  font-weight: 600;
}

/*====== Booking button ====*/
/* line 128, ../../../../sass/booking/_booking_engine_5.scss */
button.submit_button {
  width: 100%;
  border: 0;
  border-radius: 0;
  background: #ffd600;
  height: 55px;
  text-transform: uppercase;
  font-weight: bolder;
  font-size: 21px;
  color: #565656;
  cursor: pointer;
}

/*====== Rooms section ======*/
/* line 142, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper {
  float: left;
  height: 70px;
  width: 33.3%;
  padding: 7px 10px 5px;
  box-sizing: border-box;
}
/* line 149, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper .room_list_wrapper li.room {
  float: right;
  width: 69%;
}
/* line 155, ../../../../sass/booking/_booking_engine_5.scss */
.rooms_number_wrapper .adults_selector {
  height: 70px;
  float: left;
  border-right: 1px solid gray;
  width: 46.5%;
}

/* line 163, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper {
  display: table;
  float: right;
  width: 66.6%;
}
/* line 168, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .room_title {
  display: none;
}
/* line 172, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .adults_selector {
  border-right: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
}
/* line 177, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
  width: 50%;
  display: inline-block;
  height: 70px;
  box-sizing: border-box;
  padding: 7px 10px 5px;
}
/* line 185, ../../../../sass/booking/_booking_engine_5.scss */
.room_list_wrapper .children_selector {
  width: 50% !important;
  padding: 7px 10px 5px;
  float: right;
}

/* line 192, ../../../../sass/booking/_booking_engine_5.scss */
.room_list .room {
  height: 70px;
}

/* line 196, ../../../../sass/booking/_booking_engine_5.scss */
.room_list .room.room3, .room_list .room.room2 {
  border-top: 1px solid lightgrey;
}

/*==== Promocode section  ====*/
/* line 201, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_wrapper {
  display: table;
  width: 100%;
  padding: 10px 10px 5px;
  border-top: 1px solid lightgrey;
  box-sizing: border-box;
}

/* line 209, ../../../../sass/booking/_booking_engine_5.scss */
label.promocode_label {
  display: block;
}

/* line 214, ../../../../sass/booking/_booking_engine_5.scss */
input.promocode_input {
  border: 0;
  width: 100%;
  display: block;
  height: 30px;
  font-size: 13px;
  font-weight: lighter;
  margin-top: 2px;
  font-family: sans-serif;
}

/*====== Selectric ======*/
/* line 226, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems {
  overflow: scroll !important;
  width: 92px !important;
  margin-left: -11px;
  margin-top: 2px;
}
/* line 232, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 238, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li {
  font-weight: 500;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAADAQMAAABs5if8AAAABlBMVEXOzs7y8vKNS741AAAADklEQVQI12M4wPSAWQAABoQBtgsaY5kAAAAASUVORK5CYII=);
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  line-height: 29px !important;
  font-size: 18px !important;
  font-family: 'Source Sans Pro', sans-serif;
}
/* line 248, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems li:hover {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 255, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems ul {
  z-index: 40;
}
/* line 259, ../../../../sass/booking/_booking_engine_5.scss */
.selectricItems .room {
  padding-top: 17px;
}

/* line 265, ../../../../sass/booking/_booking_engine_5.scss */
#full-booking-engine-html-5 .selectric .button {
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 23px;
  margin-left: 0;
  margin-top: 8px;
  text-indent: 999px;
  height: 22px;
  font-weight: 600;
}

/* line 278, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper .selectric .label {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 28px;
  margin-left: 0;
  font-weight: 600;
}

/* line 286, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper .selectric {
  margin-top: 3px;
}

/* line 290, ../../../../sass/booking/_booking_engine_5.scss */
.selectricWrapper {
  width: 78px !important;
}

/*======= Phone web support =======*/
/* line 295, ../../../../sass/booking/_booking_engine_5.scss */
.wrapper-new-web-support.booking_form_title {
  border-radius: 0;
  padding: 5px 10px;
  font-family: 'Montserrat', sans-serif;
  font-size: 11px !important;
  margin-top: 11px;
}

/* line 303, ../../../../sass/booking/_booking_engine_5.scss */
.wrapper-new-web-support .web_support_number, .web_support_label_1 {
  font-size: 11px !important;
  margin-left: 5px;
}

/* line 308, ../../../../sass/booking/_booking_engine_5.scss */
img.phone_support_image {
  float: left;
  width: 28px;
  margin-top: 1px;
  margin-right: 12px;
}

/*======= Header title ======*/
/* line 316, ../../../../sass/booking/_booking_engine_5.scss */
.booking_title_1, .booking_title_2, .best_price {
  display: none;
}

/* line 320, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header {
  font-family: 'Montserrat', sans-serif;
  padding: 10px;
  display: table;
  width: 100%;
  box-sizing: border-box;
  cursor: pointer;
}
/* line 328, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header:hover {
  opacity: 0.8;
}
/* line 332, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header img.booking_header_discount {
  float: left;
  margin-right: 15px;
}
/* line 337, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header p.first_offer_name {
  font-size: 12px;
  color: #8096c7;
  margin-top: 9px;
}
/* line 343, ../../../../sass/booking/_booking_engine_5.scss */
.promocode_header p.second_offer_name {
  font-size: 10px;
  margin-top: 3px;
  color: gray;
}

/* line 350, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker {
  width: 283px;
}
/* line 353, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker .ui-widget-header {
  background: none !important;
  border: 0;
}
/* line 357, ../../../../sass/booking/_booking_engine_5.scss */
.ui-datepicker .ui-widget-header .ui-datepicker-title {
  color: #646464 !important;
  font-family: Verdana, Arial, sans-serif;
  font-weight: 300;
}

/* line 366, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker th {
  font-weight: 300;
  font-size: 14px;
}
/* line 372, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker .ui-state-default, body .ui-datepicker .ui-widget-content .ui-state-default, body .ui-datepicker .ui-widget-header .ui-state-default {
  background: transparent !important;
  border: 0 !important;
  color: #646464 !important;
  font-weight: 400;
  font-family: Circular, "Helvetica Neue", Helvetica, Arial, sans-serif;
  text-align: center;
  font-size: 13px;
}
/* line 383, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content .ui-state-hover {
  border: 0;
  background: #4B4B4B !important;
  color: white !important;
}
/* line 389, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content {
  border: 0;
  border-radius: 0;
  padding-bottom: 40px;
}
/* line 395, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-state-default {
  padding: 8px;
}
/* line 399, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-start_date {
  opacity: 1 !important;
}
/* line 402, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-start_date .ui-state-default {
  border: 0;
  background: #2872b6 !important;
  color: white !important;
}
/* line 409, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-highlighted .ui-state-default {
  border: 0;
  background: rgba(40, 96, 144, 0.25) !important;
}
/* line 414, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-widget-content .ui-state-active {
  border: 0;
  background: #2872b6 !important;
  color: white !important;
}
/* line 420, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-e, body span.ui-icon.ui-icon-circle-triangle-w {
  /* background: url(/static_1/images/mobile_img/renovation/flecha_der.png) no-repeat center;*/
  background: none;
  text-indent: 0;
  color: transparent;
  font-size: 0;
}
/* line 426, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-e:before, body span.ui-icon.ui-icon-circle-triangle-w:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  content: '\f105';
  font-family: "FontAwesome", "Font Awesome 5 Pro", sans-serif;
  font-size: 20px;
  color: black;
}
/* line 443, ../../../../sass/booking/_booking_engine_5.scss */
body span.ui-icon.ui-icon-circle-triangle-w:before {
  content: '\f104';
}
/* line 448, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker .ui-datepicker-next, body .ui-datepicker .ui-datepicker-prev {
  background: none;
}
/* line 452, ../../../../sass/booking/_booking_engine_5.scss */
body .ui-datepicker-next.ui-state-hover, body .ui-datepicker-prev.ui-datepicker-prev-hover {
  border: 1px solid #636363;
}

/* line 458, ../../../../sass/booking/_booking_engine_5.scss */
.datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-e:before, .datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-w:before {
  display: none;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

/* line 470, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 {
  border-radius: 0;
}
/* line 472, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-skin {
  border-radius: 0;
}
/* line 475, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide > * {
  padding: 0 !important;
  background-color: transparent;
}
/* line 479, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide .calendar_popup_wrapper {
  background-color: transparent;
}
/* line 483, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .full_screen_engine .container_popup_booking {
  background-color: transparent !important;
  padding: 24px !important;
}
/* line 488, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  color: gray;
}
/* line 491, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading {
  width: 50px;
  height: 50px;
  margin: auto;
  position: absolute;
  left: 0;
  right: 0;
  top: 100px;
  bottom: 0;
}
/* line 10, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:before {
  content: '';
  width: 50px;
  height: 5px;
  background: #000;
  opacity: 0.3;
  position: absolute;
  top: 59px;
  left: 0;
  border-radius: 50%;
  animation: box_shadow .5s linear infinite;
}
/* line 22, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:after {
  content: '';
  width: 50px;
  height: 50px;
  background: white;
  animation: box_animate .5s linear infinite;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 3px;
}
@keyframes box_animate {
  17% {
    border-bottom-right-radius: 3px;
  }
  25% {
    transform: translateY(9px) rotate(22.5deg);
  }
  50% {
    transform: translateY(18px) scale(1, 0.9) rotate(45deg);
    border-bottom-right-radius: 40px;
  }
  75% {
    transform: translateY(9px) rotate(67.5deg);
  }
  100% {
    transform: translateY(0) rotate(90deg);
  }
}
@keyframes box_shadow {
  0%, 100% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.2, 1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader {
  padding: 60px 0 70px;
}
/* line 3, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot {
  width: 24px;
  height: 24px;
  margin: 0 3px;
  background: #000;
  border-radius: 100%;
  display: inline-block;
  animation: slide 1s infinite;
}
/* line 11, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(1) {
  animation-delay: 0.1s;
}
/* line 14, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(2) {
  animation-delay: 0.2s;
}
/* line 17, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(3) {
  animation-delay: 0.3s;
}
/* line 20, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(4) {
  animation-delay: 0.4s;
}
/* line 23, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(5) {
  animation-delay: 0.5s;
}
@-moz-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-webkit-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-o-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube {
  margin: 20px auto;
  width: 40px;
  height: 40px;
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}
/* line 7, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube {
  float: left;
  width: 50%;
  height: 50%;
  position: relative;
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}
/* line 15, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  -webkit-animation: sk-foldCubeAngle 2.4s infinite linear both;
  animation: sk-foldCubeAngle 2.4s infinite linear both;
  -webkit-transform-origin: 100% 100%;
  -ms-transform-origin: 100% 100%;
  transform-origin: 100% 100%;
}
/* line 30, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2 {
  -webkit-transform: scale(1.1) rotateZ(90deg);
  transform: scale(1.1) rotateZ(90deg);
}
/* line 34, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3 {
  -webkit-transform: scale(1.1) rotateZ(270deg);
  transform: scale(1.1) rotateZ(270deg);
}
/* line 38, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4 {
  -webkit-transform: scale(1.1) rotateZ(180deg);
  transform: scale(1.1) rotateZ(180deg);
}
/* line 43, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2:before {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
/* line 49, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3:before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
}
/* line 55, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4:before {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
}
@-webkit-keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
@keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
/* line 1, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots {
  padding: 40px 0;
  text-align: center;
  animation: dots-rotate 2s linear infinite;
}
/* line 5, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  content: '';
  animation: dots-margin 2s linear infinite;
}
/* line 9, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots span, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  display: inline-block;
  vertical-align: middle;
  width: 30px;
  height: 30px;
  background-color: white;
  border-radius: 50%;
  margin: 5px;
}
@keyframes dots-margin {
  0% {
    margin: 5px;
  }
  12% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  25% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  50% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  62% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  75% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  100% {
    margin: 5px;
  }
}
@keyframes dots-rotate {
  0% {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  25% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  50% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  75% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/* line 501, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_top_popup_booking {
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: lighter;
  color: gray;
}
/* line 509, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  font-weight: bold;
  text-transform: uppercase;
}
/* line 514, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2 .container_popup_booking {
  width: 555px;
  display: block;
  padding: 30px 0;
  box-sizing: border-box;
  margin: 7px;
  border: 1px solid #3483b2;
}

/* line 525, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine {
  width: auto !important;
  height: auto !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}
/* line 533, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-outer {
  background: transparent;
}
/* line 538, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine.custom_color_overlay .fancybox-skin {
  background: transparent;
}
/* line 543, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-skin {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}
/* line 552, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-inner {
  overflow: visible;
}
/* line 556, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 569, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  border: 0 !important;
}
/* line 573, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: lighter;
}
/* line 576, ../../../../sass/booking/_booking_engine_5.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking strong {
  font-weight: bold;
  text-decoration: underline;
  font-size: 14px;
}

/* line 4, ../sass/_booking_engine.scss */
.fixed_datepicker {
  position: fixed !important;
  top: 75px !important;
}

/* line 9, ../sass/_booking_engine.scss */
#full_wrapper_booking {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 50;
  top: 50%;
  transform: translateY(-58%);
  /*======== Booking Widget =======*/
}
/* line 17, ../sass/_booking_engine.scss */
#full_wrapper_booking.deployed {
  position: fixed;
  top: 0;
  background: white;
  transform: none;
  box-shadow: 0 -6px 32px black;
}
/* line 26, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectricWrapper .selectric .label {
  font-size: 27px !important;
  color: #487fbf !important;
}
/* line 31, ../sass/_booking_engine.scss */
#full_wrapper_booking input.promocode_input {
  font-size: 12px;
  padding: 7px 0;
  box-sizing: border-box;
}
/* line 38, ../sass/_booking_engine.scss */
#full_wrapper_booking div#wrapper_booking {
  width: 1121px;
  padding: 5px;
  background: rgba(255, 255, 255, 0.6);
  position: relative;
}
/* line 45, ../sass/_booking_engine.scss */
#full_wrapper_booking div#wrapper_booking.home {
  bottom: 70px;
}
/* line 49, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget {
  position: absolute;
  left: 0px;
}
/* line 54, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 {
  width: 1121px;
}
/* line 57, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 .promocode_header {
  display: none;
}
/* line 62, ../sass/_booking_engine.scss */
#full_wrapper_booking #full-booking-engine-html-5 form.booking_form {
  background: white;
}
/* line 66, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_form_title .best_price {
  display: none;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}
/* line 74, ../sass/_booking_engine.scss */
#full_wrapper_booking .promocode_header p.first_offer_name {
  color: white;
}
/* line 77, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .date_box, #full_wrapper_booking .booking_widget .selectricWrapper, #full_wrapper_booking #booking_widget_popup .date_box, #full_wrapper_booking #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 81, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .date_box .date_day, #full_wrapper_booking #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 85, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectric {
  height: 38px;
  background: transparent;
}
/* line 90, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .adults_selector, #full_wrapper_booking .room_list_wrapper .children_selector {
  width: 50% !important;
  height: 68px;
  float: left;
  box-sizing: border-box;
}
/* line 97, ../sass/_booking_engine.scss */
#full_wrapper_booking button.submit_button {
  background: #75aadb !important;
  color: white !important;
}
/* line 102, ../sass/_booking_engine.scss */
#full_wrapper_booking .booking_widget .web_support_label_1, #full_wrapper_booking .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 107, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support .web_support_number, #full_wrapper_booking .web_support_label_1 {
  line-height: 15px !important;
  font-size: 14px !important;
}
/* line 112, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title {
  margin-top: 0;
  text-align: center;
}
/* line 117, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title .web_support_label_2 {
  display: inline-block;
  margin: 0 10px;
}
/* line 122, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support.booking_form_title .phone_support_image {
  display: none;
}
/* line 131, ../sass/_booking_engine.scss */
#full_wrapper_booking #data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}
/* line 135, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date, #full_wrapper_booking .date_box.departure_date {
  margin-top: 6px;
}
/* line 138, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date .date_year, #full_wrapper_booking .date_box.departure_date .date_year {
  display: none;
}
/* line 142, ../sass/_booking_engine.scss */
#full_wrapper_booking .date_box.entry_date .date_day, #full_wrapper_booking .date_box.departure_date .date_day {
  border-bottom: 0 !important;
}
/* line 147, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectricWrapper {
  width: 100% !important;
}
/* line 150, ../sass/_booking_engine.scss */
#full_wrapper_booking .selectricWrapper .selectric {
  margin-top: 0;
}
/* line 155, ../sass/_booking_engine.scss */
#full_wrapper_booking #slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}
/* line 159, ../sass/_booking_engine.scss */
#full_wrapper_booking .promocode_text {
  display: none;
}
/* line 163, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection {
  display: inline-block;
  vertical-align: top;
  float: left;
}
/* line 168, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection .entry_date_wrapper, #full_wrapper_booking .stay_selection .departure_date_wrapper {
  display: inline-block;
  vertical-align: top;
  width: 200px;
  float: left;
  border-top: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
}
/* line 177, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection .departure_date_wrapper {
  border-left: 0;
  border-right: 0;
}
/* line 182, ../sass/_booking_engine.scss */
#full_wrapper_booking .stay_selection .nights_number_wrapper {
  display: inline-block;
  width: 95px;
  float: left;
  vertical-align: top;
  border-top: 1px solid lightgrey;
}
/* line 191, ../sass/_booking_engine.scss */
#full_wrapper_booking .rooms_number_wrapper {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 95px;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
}
/* line 200, ../sass/_booking_engine.scss */
#full_wrapper_booking .rooms_number_wrapper .rooms_number {
  box-sizing: border-box;
}
/* line 205, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper {
  vertical-align: top;
  float: left;
  border-bottom: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
  width: 190px;
  height: 68px;
}
/* line 213, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room {
  background: white;
  height: 68px;
}
/* line 217, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room.room2 .children_selector, #full_wrapper_booking .room_list_wrapper .room.room3 .children_selector {
  border-right: 1px solid lightgray;
}
/* line 221, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room.room3, #full_wrapper_booking .room_list_wrapper .room.room2 {
  border-bottom: 1px solid lightgray;
}
/* line 225, ../sass/_booking_engine.scss */
#full_wrapper_booking .room_list_wrapper .room.room3 {
  border-top: 0;
}
/* line 231, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button {
  display: inline-block;
  width: auto;
  float: left;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  height: 69px;
}
/* line 239, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .promocode_wrapper {
  display: inline-block;
  vertical-align: top;
  float: left;
  height: 70px;
  width: 160px;
}
/* line 247, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper_booking_button .submit_button {
  width: 275px;
  display: inline-block;
  vertical-align: top;
  float: left;
  height: 70px;
  font-family: "Oswald", sans-serif;
  border: 1px solid lightgrey;
  font-weight: 500;
}

/* line 260, ../sass/_booking_engine.scss */
.departure_date_wrapper .date_day, .half_size .date_day {
  font-size: 27px !important;
  color: #75aadb !important;
}

/* line 265, ../sass/_booking_engine.scss */
.babies_selector {
  width: 33.3%;
  display: inline-block;
  padding: 7px 10px 5px;
  box-sizing: border-box;
}
/* line 271, ../sass/_booking_engine.scss */
.babies_selector label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

/*=== Ocupancy selector ====*/
/* line 279, ../sass/_booking_engine.scss */
.guest_selector {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 173px;
  border-bottom: 1px solid lightgrey;
  border-left: 1px solid lightgrey;
  border-top: 1px solid lightgrey;
  padding: 7px 10px 5px;
  box-sizing: border-box;
  height: 64px;
  cursor: pointer;
}
/* line 292, ../sass/_booking_engine.scss */
.guest_selector span.placeholder_text {
  color: darkgray;
  font-size: 13px;
  font-weight: lighter;
  font-family: sans-serif;
  padding-top: 12px;
  float: left;
  display: block;
  text-transform: uppercase;
  padding-left: 33px;
  box-sizing: border-box;
  background: url(/static_1/images/booking_5/ocupancy.png) no-repeat bottom left;
  padding-bottom: 3px;
}
/* line 306, ../sass/_booking_engine.scss */
.guest_selector span.placeholder_text.selected_value {
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 21px;
  padding-top: 3px;
  background-position-y: 8px;
  font-weight: 600;
}
/* line 316, ../sass/_booking_engine.scss */
.guest_selector > label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
  cursor: pointer;
}
/* line 323, ../sass/_booking_engine.scss */
.guest_selector b.button {
  background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
  font-family: 'Source Sans Pro', sans-serif;
  color: #585d63;
  font-size: 23px;
  margin-left: 0;
  text-indent: 999px;
  height: 22px;
  font-weight: 600;
  float: right;
}

/* line 336, ../sass/_booking_engine.scss */
#booking label {
  cursor: pointer;
}

/*===== Slider container ====*/
/* line 341, ../sass/_booking_engine.scss */
#slider_container {
  position: relative;
}

/* line 346, ../sass/_booking_engine.scss */
.wrapper-new-web-support.booking_form_title .web_support_label_1 {
  display: none;
}

/* line 351, ../sass/_booking_engine.scss */
.wrapper-old-web-support .web_support_label_2:before, .wrapper-new-web-support .web_support_label_2:before {
  margin-bottom: 2px;
  color: #75aadb;
  background: white;
  vertical-align: middle;
  margin-top: 0;
}

/* line 359, ../sass/_booking_engine.scss */
#full_wrapper_booking .wrapper-new-web-support .web_support_number {
  vertical-align: top;
  margin-top: 6px;
  display: inline-block;
}

/* line 1, ../sass/_booking_widget_modal.scss */
.fancybox-wrap {
  /*======== Booking Widget =======*/
}
/* line 3, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .fancybox-outer {
  padding: 0 !important;
}
/* line 7, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .fancybox-inner {
  width: auto !important;
  overflow: visible !important;
}
/* line 13, ../sass/_booking_widget_modal.scss */
.fancybox-wrap div#wrapper_booking {
  position: absolute;
  height: 420px;
  top: 145px;
  left: 0;
  right: 0;
  z-index: 35;
}
/* line 21, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget {
  position: absolute;
  left: 0;
}
/* line 26, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header {
  text-align: center;
}
/* line 30, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.first_offer_name {
  font-size: 16px;
  text-transform: uppercase;
}
/* line 35, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header img.booking_header_discount {
  display: none;
}
/* line 39, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .date_box.entry_date, .fancybox-wrap .date_box.departure_date {
  background: none;
}
/* line 42, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .date_box.entry_date .date_year, .fancybox-wrap .date_box.departure_date .date_year {
  display: none;
}
/* line 47, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 {
  width: 305px;
}
/* line 51, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 form.booking_form {
  background: white;
}
/* line 55, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_form_title .best_price {
  display: none;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}
/* line 63, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_header p.first_offer_name {
  color: white;
}
/* line 66, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .date_box, .fancybox-wrap .booking_widget .selectricWrapper, .fancybox-wrap #booking_widget_popup .date_box, .fancybox-wrap #booking_widget_popup .selectricWrapper {
  border: 0;
}
/* line 70, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .date_box .date_day, .fancybox-wrap #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}
/* line 74, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .selectric {
  height: 38px;
  background: transparent;
}
/* line 79, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .room_list_wrapper .adults_selector, .fancybox-wrap .room_list_wrapper .children_selector {
  width: 49.8%;
}
/* line 83, ../sass/_booking_widget_modal.scss */
.fancybox-wrap button.submit_button {
  background: #2872b6 !important;
  color: white !important;
}
/* line 88, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .booking_widget .web_support_label_1, .fancybox-wrap .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}
/* line 93, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .wrapper-new-web-support .web_support_number, .fancybox-wrap .web_support_label_1 {
  line-height: 15px !important;
}
/* line 97, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .wrapper-new-web-support.booking_form_title {
  background: #383838 !important;
}
/* line 101, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #full-booking-engine-html-5 {
  margin-top: 20px !important;
}
/* line 105, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}
/* line 109, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .date_box.entry_date {
  margin-top: 6px;
}
/* line 112, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .selectricWrapper .selectric {
  margin-top: 0px;
}
/* line 116, ../sass/_booking_widget_modal.scss */
.fancybox-wrap #slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}
/* line 120, ../sass/_booking_widget_modal.scss */
.fancybox-wrap .promocode_text {
  display: none;
}

/* line 2, ../sass/_booking_popup.scss */
.booking-data-popup .fancybox-outer {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
/* line 8, ../sass/_booking_popup.scss */
.booking-data-popup div#wrapper_booking_fancybox {
  display: table;
  width: 100%;
  position: absolute;
  bottom: 0;
  top: 0;
  margin: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 20, ../sass/_booking_popup.scss */
.booking-data-popup .adultos.numero_personas > label, .booking-data-popup .ninos.numero_personas > label, .booking-data-popup .bebes.numero_personas > label {
  display: none !important;
}

/* line 27, ../sass/_booking_popup.scss */
div#data {
  background: #75aadb;
}
/* line 30, ../sass/_booking_popup.scss */
div#data .booking_title1, div#data .best_price {
  display: none;
}
/* line 34, ../sass/_booking_popup.scss */
div#data div#booking_engine_title {
  display: block;
  float: none;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
}
/* line 41, ../sass/_booking_popup.scss */
div#data #motor_reserva {
  width: 595px;
  margin: auto;
  display: table;
}
/* line 47, ../sass/_booking_popup.scss */
div#data div#fecha_entrada, div#data div#fecha_salida {
  width: 290px;
  float: left;
  height: 125px;
  background: white;
}
/* line 54, ../sass/_booking_popup.scss */
div#data div#fecha_salida {
  float: right;
  margin-left: 0 !important;
}
/* line 59, ../sass/_booking_popup.scss */
div#data label#titulo_fecha_entrada, div#data label#titulo_fecha_salida {
  display: block;
  color: #2872b6;
  width: 100% !important;
  text-align: center;
  margin-top: 20px;
  text-transform: uppercase;
  font-size: 17px;
  font-weight: bolder;
  font-family: 'Montserrat', sans-serif;
}
/* line 71, ../sass/_booking_popup.scss */
div#data #contenedor_fechas {
  width: 100%;
  margin-bottom: 10px;
}
/* line 76, ../sass/_booking_popup.scss */
div#data .wrapper-old-web-support {
  display: none !important;
}
/* line 80, ../sass/_booking_popup.scss */
div#data #fecha_entrada input, div#data #fecha_salida input {
  border: 0 !important;
  height: 84px !important;
  width: 100% !important;
  text-align: center !important;
  box-sizing: border-box !important;
  font-size: 31px !important;
  color: #4b4b4b !important;
  padding-right: 40px;
  background: transparent url(/static_1/images/booking_5/entry_date.png) no-repeat 85% center !important;
}
/* line 91, ../sass/_booking_popup.scss */
div#data #fecha_entrada input::-webkit-input-placeholder, div#data #fecha_salida input::-webkit-input-placeholder {
  color: #4b4b4b !important;
}
/* line 96, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones {
  margin: auto;
  width: 290px;
  float: left;
  height: 125px;
  background: white;
}
/* line 103, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones label {
  display: block;
  color: #2872b6;
  width: 100% !important;
  text-align: center;
  margin-top: 20px;
  text-transform: uppercase;
  font-size: 17px;
  float: none;
  font-weight: bolder;
  font-family: 'Montserrat', sans-serif;
}
/* line 116, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones select#selector_habitaciones {
  -webkit-border-radius: 0 !important;
  -moz-border-radius: 0 !important;
  border-radius: 0 !important;
  border: 0;
  width: 260px;
  float: left;
  height: 125px;
  background: white;
  -webkit-appearance: none;
}
/* line 127, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones select#selector_habitaciones option {
  text-align: center;
}
/* line 132, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones .selectric {
  height: 83px;
  border-radius: 0;
}
/* line 136, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones .selectric p.label {
  color: #4b4b4b;
  text-align: center;
  box-sizing: border-box !important;
  font-size: 31px !important;
  padding-top: 22px;
}
/* line 144, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones .selectric .button {
  background: transparent url(/img/fuenh/ico_down.png) no-repeat center !important;
  right: 27px;
}
/* line 150, ../sass/_booking_popup.scss */
div#data #contenedor_habitaciones .selectricItems li {
  color: #4b4b4b;
  text-align: center;
  box-sizing: border-box !important;
  font-size: 21px !important;
  padding: 12px 12px 10px;
}
/* line 159, ../sass/_booking_popup.scss */
div#data .selectricWrapper {
  width: 100% !important;
}
/* line 163, ../sass/_booking_popup.scss */
div#data #contenedor_opciones {
  float: right;
  margin-top: -125px;
}
/* line 167, ../sass/_booking_popup.scss */
div#data #contenedor_opciones #hab1, div#data #contenedor_opciones #hab2, div#data #contenedor_opciones #hab3 {
  margin: auto;
  width: 290px;
  float: left;
  background: white;
  height: 125px;
}
/* line 175, ../sass/_booking_popup.scss */
div#data #contenedor_opciones #hab1 {
  margin-left: 305px;
}
/* line 179, ../sass/_booking_popup.scss */
div#data #contenedor_opciones #hab2, div#data #contenedor_opciones #hab3 {
  margin-top: 20px;
  display: block !important;
}
/* line 184, ../sass/_booking_popup.scss */
div#data #contenedor_opciones #hab3 {
  float: right;
}
/* line 188, ../sass/_booking_popup.scss */
div#data #contenedor_opciones label.numero_habitacion {
  color: #2872b6;
  font-weight: bolder;
  width: 100% !important;
  text-align: center;
  margin-top: 20px;
  display: block !important;
  text-transform: uppercase;
  font-size: 17px;
  float: none;
  font-family: 'Montserrat', sans-serif;
}
/* line 202, ../sass/_booking_popup.scss */
div#data .adultos.numero_personas, div#data .ninos.numero_personas, div#data .bebes.numero_personas {
  margin: 0;
  position: relative;
  display: inline-block;
}
/* line 207, ../sass/_booking_popup.scss */
div#data .adultos.numero_personas option, div#data .ninos.numero_personas option, div#data .bebes.numero_personas option {
  display: none;
}
/* line 212, ../sass/_booking_popup.scss */
div#data .adultos.numero_personas {
  width: 50%;
  text-align: center;
}
/* line 217, ../sass/_booking_popup.scss */
div#data .ninos.numero_personas {
  width: 50%;
  text-align: center;
}
/* line 221, ../sass/_booking_popup.scss */
div#data .ninos.numero_personas .selectricItems {
  left: -134px !important;
}
/* line 226, ../sass/_booking_popup.scss */
div#data .bebes.numero_personas {
  width: 32%;
}
/* line 229, ../sass/_booking_popup.scss */
div#data .bebes.numero_personas .selectricItems {
  left: -186px !important;
}
/* line 234, ../sass/_booking_popup.scss */
div#data .ninos {
  float: left;
}
/* line 237, ../sass/_booking_popup.scss */
div#data .ninos label#info_ninos {
  position: absolute;
  top: 20px;
  color: black;
  right: 0px;
  font-size: 9px !important;
  display: inline-block;
}
/* line 248, ../sass/_booking_popup.scss */
div#data .selectricWrapper.selector_adultos .selectric, div#data .selectricWrapper.selector_ninos .selectric, div#data .selectricWrapper.selector_bebes .selectric {
  height: 83px;
  border-radius: 0;
}
/* line 253, ../sass/_booking_popup.scss */
div#data .selectricWrapper.selector_adultos p.label, div#data .selectricWrapper.selector_ninos p.label, div#data .selectricWrapper.selector_bebes p.label {
  color: #4b4b4b;
  text-align: center;
  padding-right: 0 !important;
  box-sizing: border-box !important;
  padding-top: 23px;
  font-size: 18px !important;
}
/* line 262, ../sass/_booking_popup.scss */
div#data .selectricWrapper.selector_adultos .button, div#data .selectricWrapper.selector_ninos .button, div#data .selectricWrapper.selector_bebes .button {
  background: transparent url(/img/fuenh/ico_down.png) no-repeat center !important;
  width: 16px;
  height: 20px;
  top: 5px;
  right: 10px !important;
}
/* line 270, ../sass/_booking_popup.scss */
div#data .selectricWrapper.selector_adultos .selectricItems li, div#data .selectricWrapper.selector_ninos .selectricItems li, div#data .selectricWrapper.selector_bebes .selectricItems li {
  color: #4b4b4b;
  text-align: center;
  box-sizing: border-box !important;
  font-size: 16px !important;
  padding: 6px 12px 4px;
}
/* line 279, ../sass/_booking_popup.scss */
div#data fieldset#envio {
  width: 100%;
  margin-left: 0;
}
/* line 283, ../sass/_booking_popup.scss */
div#data fieldset#envio input#promocode {
  float: left;
  width: 290px;
  border-radius: 0;
  border: 0;
  box-sizing: border-box;
  margin-top: 10px;
  height: 90px;
  text-align: center;
  background: rgba(255, 255, 255, 0.3);
  font-size: 31px !important;
  font-weight: lighter;
  color: white;
}
/* line 297, ../sass/_booking_popup.scss */
div#data fieldset#envio input#promocode::-webkit-input-placeholder {
  color: white;
  font-size: 18px;
  font-weight: lighter;
  text-transform: uppercase;
}
/* line 305, ../sass/_booking_popup.scss */
div#data fieldset#envio button#search-button {
  display: block;
  float: right;
  height: 90px;
  width: 290px;
  border-radius: 0;
  border: 0;
  box-sizing: border-box;
  margin-top: 10px;
  background: #2872b6;
  color: white;
  text-transform: uppercase;
  font-size: 27px !important;
}
/* line 319, ../sass/_booking_popup.scss */
div#data fieldset#envio button#search-button:hover {
  opacity: 0.8;
}
/* line 326, ../sass/_booking_popup.scss */
div#data div#hab2 .disabled_overlay, div#data div#hab3 .disabled_overlay {
  display: none;
}
/* line 330, ../sass/_booking_popup.scss */
div#data div#hab2.disabled, div#data div#hab3.disabled {
  opacity: 0.4;
  position: relative;
}
/* line 334, ../sass/_booking_popup.scss */
div#data div#hab2.disabled .disabled_overlay, div#data div#hab3.disabled .disabled_overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: block;
}
/* line 346, ../sass/_booking_popup.scss */
div#data #booking_engine_title #booking_title1, div#data #booking_engine_title #best_price {
  display: none;
}
/* line 350, ../sass/_booking_popup.scss */
div#data #booking_engine_title h4#booking_title2 {
  color: white;
  margin-bottom: 25px;
  text-transform: uppercase;
  font-size: 22px;
  margin-top: 0;
}
/* line 360, ../sass/_booking_popup.scss */
div#data #booking_engine_title #booking_title2:after {
  content: '';
  display: block;
  width: 70px;
  height: 1px;
  background: white;
  margin: 10px auto;
}
/* line 369, ../sass/_booking_popup.scss */
div#data #booking_engine_title #booking_title2 span {
  font-weight: lighter;
}
/* line 375, ../sass/_booking_popup.scss */
div#data #contenedor_opciones div#hab2.disabled {
  display: none !important;
}
/* line 378, ../sass/_booking_popup.scss */
div#data #contenedor_opciones div#hab2.disabled + #hab3 {
  display: none !important;
}
/* line 384, ../sass/_booking_popup.scss */
div#data .selectricItems {
  top: 84% !important;
  left: 11px !important;
}

/* line 390, ../sass/_booking_popup.scss */
.booking-data-popup .fancybox-close {
  position: absolute;
  top: 30px;
  right: 30px;
  background: url(/img/fuenh/close_button.png) no-repeat center;
  background: none;
}
/* line 397, ../sass/_booking_popup.scss */
.booking-data-popup .fancybox-close:before {
  content: "x";
  color: white;
  font-size: 85px;
  line-height: 36px;
  font-family: 'Montserrat', sans-serif;
}

/* line 406, ../sass/_booking_popup.scss */
.contact_bottom_popup {
  display: block;
  width: 535px;
  margin: 20px auto;
  text-align: center;
  color: white;
}
/* line 413, ../sass/_booking_popup.scss */
.contact_bottom_popup .phone_hotel_booking_popup, .contact_bottom_popup .email_hotel {
  display: inline-block;
  padding-left: 30px;
  line-height: 25px;
}
/* line 419, ../sass/_booking_popup.scss */
.contact_bottom_popup .phone_hotel_booking_popup {
  margin-right: 10px;
  background: url(/img/fuenh/booking_icos/phone_ico.png) no-repeat left center;
}
/* line 424, ../sass/_booking_popup.scss */
.contact_bottom_popup .email_hotel {
  background: url(/img/fuenh/booking_icos/mail_ico.png) no-repeat left center;
}

/*=====================================================================
  Selectric
======================================================================*/
/* line 5, ../../../../sass/booking/_selectric.scss */
.selectricWrapper {
  position: relative;
  margin: 0 0 0px;
  width: 80px;
  cursor: pointer;
}

/* line 12, ../../../../sass/booking/_selectric.scss */
.selectricDisabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 23, ../../../../sass/booking/_selectric.scss */
.selectricOpen {
  z-index: 9999;
}

/* line 27, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 31, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

/* line 38, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect select {
  position: absolute;
  left: -100%;
  display: none;
}

/* line 44, ../../../../sass/booking/_selectric.scss */
.selectricInput {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  _font: 0/0 a;
  background: none !important;
}

/* line 60, ../../../../sass/booking/_selectric.scss */
.selectricTempShow {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* line 67, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectric {
  z-index: 9999;
}

/* line 72, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 76, ../../../../sass/booking/_selectric.scss */
.selectric {
  background: white;
  position: relative;
  border-radius: 6px;
  height: 40px;
}

/* line 83, ../../../../sass/booking/_selectric.scss */
.selectric .label {
  display: block;
  overflow: hidden;
  font-size: 16px;
  line-height: 40px;
  color: #2872b6;
  text-align: left;
  margin-left: 20px;
}

/* line 93, ../../../../sass/booking/_selectric.scss */
.selectric .button {
  zoom: 1;
  position: absolute;
  font: 0/0 a;
  overflow: hidden;
  margin: auto;
  top: 0;
  right: 2px;
  bottom: 0;
  width: 35px;
  height: 35px;
  border: 0;
  background: #2872b6 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  padding: 0;
}

/* line 109, ../../../../sass/booking/_selectric.scss */
.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */
/* line 115, ../../../../sass/booking/_selectric.scss */
.selectricItems {
  display: none;
  position: absolute;
  overflow: hidden;
  top: 100%;
  left: 0;
  background: #F9F9F9;
  border: 1px solid #CCC;
  z-index: 9998;
  -webkit-box-shadow: 0 0 10px -6px;
  box-shadow: 0 0 10px -6px;
}

/* line 128, ../../../../sass/booking/_selectric.scss */
.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* line 138, ../../../../sass/booking/_selectric.scss */
.selectricItems li {
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 146, ../../../../sass/booking/_selectric.scss */
.selectricItems li:hover {
  background: #F0F0F0;
  color: #444;
}
/* line 151, ../../../../sass/booking/_selectric.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}

/* line 158, ../../../../sass/booking/_selectric.scss */
.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;
  cursor: default;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/
/* line 3, ../sass/_template_specific.scss */
.ui-state-default {
  border: 1px solid white !important;
}

/* line 7, ../sass/_template_specific.scss */
.ui-datepicker-title {
  color: white !important;
}

/* line 11, ../sass/_template_specific.scss */
.ui-widget-header {
  background: #2872b6 !important;
}

/* line 15, ../sass/_template_specific.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #2872b6 !important;
  color: white;
}

/* line 20, ../sass/_template_specific.scss */
body {
  font-family: 'Source Sans Pro', sans-serif;
}

/* line 24, ../sass/_template_specific.scss */
.tp-bullets {
  display: none !important;
}

/* line 28, ../sass/_template_specific.scss */
#slider_container {
  box-shadow: 0px 0px 60px rgba(0, 0, 0, 0.6);
}
/* line 31, ../sass/_template_specific.scss */
#slider_container .slotholder:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0px;
  right: 0px;
  margin: auto;
  width: 100%;
  height: 300px;
  z-index: 40;
  background: transparent;
  /* For browsers that do not support gradients */
  background: -webkit-linear-gradient(rgba(0, 0, 0, 0.7), transparent);
  /* For Safari 5.1 to 6.0 */
  background: -o-linear-gradient(rgba(0, 0, 0, 0.7), transparent);
  /* For Opera 11.1 to 12.0 */
  background: -moz-linear-gradient(rgba(0, 0, 0, 0.7), transparent);
  /* For Firefox 3.6 to 15 */
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
  /* Standard syntax */
}
/* line 49, ../sass/_template_specific.scss */
#slider_container .caption {
  position: absolute;
  top: 220px;
  width: 1140px;
  z-index: 50 !important;
  margin-left: -570px !important;
  left: 50% !important;
}
/* line 56, ../sass/_template_specific.scss */
#slider_container .caption div {
  text-shadow: rgba(0, 0, 0, 0.5) 5px 5px 25px;
}
/* line 59, ../sass/_template_specific.scss */
#slider_container .caption div:nth-child(odd) {
  margin-bottom: 100px;
}
/* line 63, ../sass/_template_specific.scss */
#slider_container .slider_head {
  position: relative;
  top: 0px;
  left: 0px;
  right: 0px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #75aadb;
}
/* line 72, ../sass/_template_specific.scss */
#slider_container .slider_head img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  opacity: 0.3;
}

/* line 79, ../sass/_template_specific.scss */
.hidden_menu_wrapper {
  position: fixed;
  top: 0;
  width: 100%;
  min-width: 1140px;
  display: none;
  background: white;
  -webkit-box-shadow: 2px 2px 8px 0 grey;
  -moz-box-shadow: 2px 2px 8px 0 grey;
  box-shadow: 2px 2px 8px 0 grey;
  height: 95px;
  z-index: 99;
}
/* line 92, ../sass/_template_specific.scss */
.hidden_menu_wrapper .logoDiv {
  float: left;
  position: relative;
  z-index: 2;
}
/* line 97, ../sass/_template_specific.scss */
.hidden_menu_wrapper .logoDiv img {
  height: 70px;
  margin-top: 10px;
}
/* line 103, ../sass/_template_specific.scss */
.hidden_menu_wrapper div#mainMenuDiv {
  position: absolute;
  top: 50%;
  right: 0;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 114, ../sass/_template_specific.scss */
.hidden_menu_wrapper nav {
  padding-top: 0;
  display: block;
  height: 95px;
  position: relative;
}
/* line 120, ../sass/_template_specific.scss */
.hidden_menu_wrapper nav a {
  padding: 0 8px;
  text-decoration: none;
  color: #2872b6;
  letter-spacing: 0px;
  font-weight: 100;
}
/* line 127, ../sass/_template_specific.scss */
.hidden_menu_wrapper nav a.button-promotion {
  background: #2872b6;
  color: white;
  padding: 10px;
}
/* line 132, ../sass/_template_specific.scss */
.hidden_menu_wrapper nav a.button-promotion:hover {
  opacity: 0.8;
}
/* line 139, ../sass/_template_specific.scss */
.hidden_menu_wrapper .main-section-div-wrapper, .hidden_menu_wrapper .visita_virtual {
  display: inline-block;
}
/* line 142, ../sass/_template_specific.scss */
.hidden_menu_wrapper .main-section-div-wrapper:hover, .hidden_menu_wrapper .visita_virtual:hover {
  opacity: 0.8;
}
/* line 147, ../sass/_template_specific.scss */
.hidden_menu_wrapper #section-active a {
  font-weight: bolder;
}

/* line 152, ../sass/_template_specific.scss */
header {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  height: 300px;
  z-index: 20;
  padding-bottom: 30px;
}
/* line 161, ../sass/_template_specific.scss */
header #lang {
  position: absolute;
  top: 25px;
}
/* line 166, ../sass/_template_specific.scss */
header #logoDiv {
  width: 340px;
}
/* line 168, ../sass/_template_specific.scss */
header #logoDiv img {
  max-height: 80px;
  margin-top: 10px;
}
/* line 174, ../sass/_template_specific.scss */
header #wrapper-header .right_header {
  width: 760px;
}
/* line 178, ../sass/_template_specific.scss */
header a {
  text-decoration: none;
  color: white;
  letter-spacing: 1px;
  font-weight: 100;
  display: inline-block;
  vertical-align: middle;
}
/* line 186, ../sass/_template_specific.scss */
header a:hover {
  text-decoration: underline;
}
/* line 191, ../sass/_template_specific.scss */
header a.separator:hover {
  text-decoration: none;
}
/* line 195, ../sass/_template_specific.scss */
header #social, header nav {
  text-align: right;
  padding-top: 20px;
}
/* line 198, ../sass/_template_specific.scss */
header #social img, header nav img {
  vertical-align: middle;
  margin-left: 10px;
  margin-right: 10px;
}
/* line 206, ../sass/_template_specific.scss */
header #social a .fa {
  color: white;
  position: relative;
  width: 32px;
  height: 32px;
  background-color: #2872b6;
  border-radius: 50%;
  -webkit-transition: opacity 0.4s;
  -moz-transition: opacity 0.4s;
  -ms-transition: opacity 0.4s;
  -o-transition: opacity 0.4s;
  transition: opacity 0.4s;
}
/* line 215, ../sass/_template_specific.scss */
header #social a .fa:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 219, ../sass/_template_specific.scss */
header #social a .fa:hover {
  opacity: .8;
}
/* line 225, ../sass/_template_specific.scss */
header nav {
  padding-top: 10px;
  display: block;
}
/* line 228, ../sass/_template_specific.scss */
header nav .main-section-div-wrapper, header nav .visita_virtual {
  display: inline-block;
}
/* line 231, ../sass/_template_specific.scss */
header nav .main-section-div-wrapper:last-of-type a, header nav .visita_virtual:last-of-type a {
  padding-right: 12px;
}
/* line 234, ../sass/_template_specific.scss */
header nav .main-section-div-wrapper .destacado, header nav .visita_virtual .destacado {
  color: white;
  font-weight: bolder;
  background: #2872b6;
}
/* line 238, ../sass/_template_specific.scss */
header nav .main-section-div-wrapper .destacado i, header nav .visita_virtual .destacado i {
  font-size: 20px;
  display: inline-block;
  vertical-align: top;
}
/* line 245, ../sass/_template_specific.scss */
header nav #section-active a {
  color: #2872b6;
  font-weight: bolder;
}
/* line 249, ../sass/_template_specific.scss */
header nav a {
  padding: 0px 7px;
}

/* line 255, ../sass/_template_specific.scss */
#full_wrapper_booking {
  top: 550px;
}

/* line 259, ../sass/_template_specific.scss */
#full_wrapper_booking div#wrapper_booking {
  background-color: white;
  z-index: 999999999;
}
/* line 262, ../sass/_template_specific.scss */
#full_wrapper_booking div#wrapper_booking div {
  border-top: 0px !important;
  border-bottom: 0px !important;
}
/* line 266, ../sass/_template_specific.scss */
#full_wrapper_booking div#wrapper_booking .entry_date_wrapper {
  border-left: 0px !important;
}
/* line 269, ../sass/_template_specific.scss */
#full_wrapper_booking div#wrapper_booking .submit_button {
  background-color: #2872b6 !important;
}
/* line 272, ../sass/_template_specific.scss */
#full_wrapper_booking div#wrapper_booking .date_day, #full_wrapper_booking div#wrapper_booking .label {
  color: #666666 !important;
}

/* line 277, ../sass/_template_specific.scss */
.block_x3 {
  position: relative;
  display: table;
  margin-top: -70px;
  z-index: 40;
}
/* line 282, ../sass/_template_specific.scss */
.block_x3 .block {
  display: inline-block;
  float: left;
  position: relative;
  width: calc(100% / 3);
  margin: 0;
  box-sizing: border-box;
  cursor: pointer;
}
/* line 290, ../sass/_template_specific.scss */
.block_x3 .block img {
  max-width: 100%;
}
/* line 293, ../sass/_template_specific.scss */
.block_x3 .block h3 {
  margin-top: -5px;
  background: #2872b6;
  color: #dddddd;
  font-weight: 100;
  padding: 10px;
  font-size: 18px;
  border-top: 1px solid #2872b6 !important;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 306, ../sass/_template_specific.scss */
.block_x3 .block h3 span {
  display: block;
  color: white;
  font-size: 28px;
  font-weight: normal;
}
/* line 313, ../sass/_template_specific.scss */
.block_x3 .block span.description {
  position: absolute;
  top: 70px;
  right: 0px;
  background: rgba(40, 114, 182, 0.7);
  color: white;
  margin: 5px;
  padding: 17px 15px;
  border-radius: 50px;
  font-size: 38px;
}
/* line 326, ../sass/_template_specific.scss */
.block_x3 .block:hover h3 {
  opacity: 0.8;
}

/* line 333, ../sass/_template_specific.scss */
.content_subtitle_wrapper {
  float: none;
  text-align: center;
  margin: 100px 0px;
}
/* line 337, ../sass/_template_specific.scss */
.content_subtitle_wrapper h3 {
  position: relative;
  text-align: center;
  color: #75aadb;
  font-weight: 100;
  padding: 10px;
  padding-bottom: 30px;
  margin-bottom: 10px;
  font-size: 18px;
}
/* line 346, ../sass/_template_specific.scss */
.content_subtitle_wrapper h3 span {
  display: block;
  color: #2872b6;
  font-size: 28px;
  font-weight: normal;
}
/* line 351, ../sass/_template_specific.scss */
.content_subtitle_wrapper h3 span:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0px;
  right: 0px;
  margin: auto;
  width: 15%;
  height: 1px;
  background: #2872b6;
  transform: skew(0deg);
}
/* line 366, ../sass/_template_specific.scss */
.content_subtitle_wrapper .subtitle_description {
  padding: 0px 100px;
  margin: auto;
  margin-bottom: 50px;
  display: table;
}
/* line 372, ../sass/_template_specific.scss */
.content_subtitle_wrapper .subtitle_description .pcolx2 {
  display: inline-block;
  text-align: justify;
  float: left;
  width: calc(100% / 2);
  box-sizing: border-box;
  padding: 0px 10px;
}
/* line 380, ../sass/_template_specific.scss */
.content_subtitle_wrapper .subtitle_description li {
  display: inline-block;
  background-color: #efefef;
  border: 1px solid #dddddd;
  color: #004a8e;
  border-radius: 3px;
  padding: 5px;
  margin: 5px 10px;
  width: calc(100% / 4);
}
/* line 390, ../sass/_template_specific.scss */
.content_subtitle_wrapper .subtitle_description .list2col {
  margin-top: 10px;
}
/* line 392, ../sass/_template_specific.scss */
.content_subtitle_wrapper .subtitle_description .list2col li {
  width: 45%;
}
/* line 397, ../sass/_template_specific.scss */
.content_subtitle_wrapper .titleList {
  display: inline-block;
  padding-bottom: 5px;
  margin-bottom: 10px;
  color: #2872b6;
  border-bottom: 1px solid #75aadb;
}
/* line 404, ../sass/_template_specific.scss */
.content_subtitle_wrapper .btn {
  clear: both;
  text-transform: uppercase;
  background: white;
  border: 1px solid #2872b6;
  font-size: 20px;
  padding: 15px 70px;
  color: #2872b6;
  text-decoration: none;
  margin: 30px auto;
  -webkit-transition: all 0.75s;
  -moz-transition: all 0.75s;
  -ms-transition: all 0.75s;
  -o-transition: all 0.75s;
  transition: all 0.75s;
}
/* line 420, ../sass/_template_specific.scss */
.content_subtitle_wrapper .btn:hover {
  background: #2872b6;
  color: white;
}

/* line 427, ../sass/_template_specific.scss */
.slider_room {
  position: relative;
  margin-top: 20px;
  padding: 20px;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.2);
}
/* line 433, ../sass/_template_specific.scss */
.slider_room .flex-viewport {
  border: 2px solid white;
  width: 100%;
  display: table;
}
/* line 438, ../sass/_template_specific.scss */
.slider_room .flex-viewport .item_slider {
  position: relative;
  margin: auto;
  height: 450px;
}
/* line 442, ../sass/_template_specific.scss */
.slider_room .flex-viewport .item_slider img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 447, ../sass/_template_specific.scss */
.slider_room .arrow {
  position: absolute;
  width: 60px;
  height: 60px;
  top: 50%;
  margin-left: -15px;
  background: #222222 url("/img/fuenh/arrow_left.png") no-repeat center;
  border-radius: 50px;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.4);
}
/* line 458, ../sass/_template_specific.scss */
.slider_room .next_arrow {
  right: 0px;
  margin-right: 0px;
  background-image: url("/img/fuenh/arrow_right.png");
}

/* line 465, ../sass/_template_specific.scss */
.rooms_wrapper {
  display: table;
  margin-top: -50px;
  width: 100%;
}
/* line 469, ../sass/_template_specific.scss */
.rooms_wrapper .room {
  width: calc((100% / 2) - 10px);
  display: inline-block;
  height: 350px;
  position: relative;
  overflow: hidden;
  margin: 0 10px 15px 0;
}
/* line 476, ../sass/_template_specific.scss */
.rooms_wrapper .room:nth-child(2n) {
  margin: 0 0 15px 10px;
}
/* line 479, ../sass/_template_specific.scss */
.rooms_wrapper .room .description {
  position: absolute;
  top: 290px;
  left: 0px;
  right: 0px;
  height: 350px;
  padding: 5px;
  color: #dddddd;
  font-weight: 100;
  background-color: rgba(40, 114, 182, 0.6);
  -webkit-transition-duration: 1s;
  /* Safari */
  transition-duration: 1s;
}
/* line 491, ../sass/_template_specific.scss */
.rooms_wrapper .room .description span {
  display: block;
  font-weight: bolder;
  font-size: 24px;
  color: white;
}
/* line 497, ../sass/_template_specific.scss */
.rooms_wrapper .room .description .plus {
  font-size: 100px;
  border: 2px solid white;
  line-height: 100px;
  border-radius: 100px;
  width: 100px;
  margin: 100px auto;
}
/* line 506, ../sass/_template_specific.scss */
.rooms_wrapper .room a:hover .description {
  top: 0px;
}
/* line 509, ../sass/_template_specific.scss */
.rooms_wrapper .room img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 515, ../sass/_template_specific.scss */
.misReservas {
  text-align: center;
  margin-top: 50px;
}
/* line 518, ../sass/_template_specific.scss */
.misReservas h3 {
  position: relative;
  text-align: center;
  padding: 10px;
  padding-bottom: 30px;
  margin-bottom: 10px;
  color: #2872b6;
  font-size: 28px;
  font-weight: normal;
}
/* line 527, ../sass/_template_specific.scss */
.misReservas h3:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0px;
  right: 0px;
  margin: auto;
  width: 15%;
  height: 1px;
  background: #2872b6;
  transform: skew(0deg);
}
/* line 541, ../sass/_template_specific.scss */
.misReservas #my-bookings-form-fields {
  width: 400px;
  margin: auto;
  padding-top: 30px;
}
/* line 546, ../sass/_template_specific.scss */
.misReservas label {
  border: 1px solid #75aadb !important;
  background-color: #75aadb;
  color: white;
  padding: 8px 10px;
  padding-bottom: 10px;
  border-radius: 5px 0px 0px 5px;
}
/* line 554, ../sass/_template_specific.scss */
.misReservas input[type=text] {
  border: 1px solid #75aadb !important;
  border-radius: 0px 5px 5px 0px;
  padding: 10px;
  margin-left: -5px;
  width: 250px;
  margin-bottom: 20px;
  vertical-align: initial !important;
  font-size: initial !important;
  font-family: initial !important;
}
/* line 566, ../sass/_template_specific.scss */
.misReservas .conditions_my_booking, .misReservas .label_element {
  display: inline;
  vertical-align: top;
  font-size: 12px;
  position: relative;
}
/* line 572, ../sass/_template_specific.scss */
.misReservas .conditions_my_booking a, .misReservas .label_element a {
  vertical-align: top;
}
/* line 577, ../sass/_template_specific.scss */
.misReservas .conditions_my_booking.error:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  border: 1px solid red;
}
/* line 587, ../sass/_template_specific.scss */
.misReservas button {
  clear: both;
  text-transform: uppercase;
  background: white;
  border: 1px solid #2872b6;
  font-size: 20px;
  padding: 15px 70px;
  color: #2872b6;
  text-decoration: none;
  margin: 10px auto;
  cursor: pointer;
}
/* line 599, ../sass/_template_specific.scss */
.misReservas .my-bookings-booking-info {
  margin: auto !important;
}
/* line 601, ../sass/_template_specific.scss */
.misReservas .my-bookings-booking-info .fResumenReserva {
  background-color: #efefef;
  border-width: 0px;
}
/* line 606, ../sass/_template_specific.scss */
.misReservas #cancelButton {
  display: none;
}

/* line 611, ../sass/_template_specific.scss */
.contact-form_wrapper {
  text-align: right;
  margin-top: 100px;
}
/* line 613, ../sass/_template_specific.scss */
.contact-form_wrapper h1 {
  text-align: center;
  color: #2872b6 !important;
  font-size: 28px !important;
  font-weight: normal !important;
}
/* line 619, ../sass/_template_specific.scss */
.contact-form_wrapper .info {
  width: 600px;
  margin: auto;
}
/* line 623, ../sass/_template_specific.scss */
.contact-form_wrapper input[type=text], .contact-form_wrapper textarea {
  border: 1px solid #75aadb !important;
  border-radius: 5px;
  padding: 5px;
}
/* line 628, ../sass/_template_specific.scss */
.contact-form_wrapper input[type=checkbox] {
  width: 12px !important;
}
/* line 631, ../sass/_template_specific.scss */
.contact-form_wrapper .title {
  float: none !important;
}
/* line 633, ../sass/_template_specific.scss */
.contact-form_wrapper .title a {
  color: #75aadb;
  text-decoration: underline;
  margin-right: -10px;
}
/* line 641, ../sass/_template_specific.scss */
.contact-form_wrapper #google-plus, .contact-form_wrapper .fb_iframe_widget {
  display: none;
}
/* line 644, ../sass/_template_specific.scss */
.contact-form_wrapper #contact-button-wrapper {
  float: none !important;
  padding: 0 !important;
  text-align: center;
}
/* line 648, ../sass/_template_specific.scss */
.contact-form_wrapper #contact-button-wrapper #contact-button {
  text-transform: uppercase !important;
  background: white !important;
  border: 1px solid #2872b6 !important;
  font-size: 20px !important;
  padding: 15px 70px !important;
  color: #2872b6 !important;
  margin-top: 30px;
  -webkit-transition: all 0.75s;
  -moz-transition: all 0.75s;
  -ms-transition: all 0.75s;
  -o-transition: all 0.75s;
  transition: all 0.75s;
}
/* line 662, ../sass/_template_specific.scss */
.contact-form_wrapper #contact-button-wrapper #contact-button:hover {
  background: #2872b6 !important;
  color: white !important;
}

/* line 671, ../sass/_template_specific.scss */
#contactContent h1#title {
  display: none;
}
/* line 674, ../sass/_template_specific.scss */
#contactContent .contInput {
  display: flex !important;
  flex-direction: column;
  text-align: left;
}
/* line 679, ../sass/_template_specific.scss */
#contactContent .privacy_checkbox {
  text-align: left;
}
/* line 681, ../sass/_template_specific.scss */
#contactContent .privacy_checkbox .title {
  width: 100%;
}
/* line 685, ../sass/_template_specific.scss */
#contactContent .has_reservation_wrapper {
  float: left;
}

/* line 690, ../sass/_template_specific.scss */
.gallery {
  text-align: center;
  margin-bottom: 100px;
  display: table;
}
/* line 694, ../sass/_template_specific.scss */
.gallery a {
  display: inline-block;
  width: calc(100% / 4);
  height: 150px;
  overflow: hidden;
  position: relative;
}
/* line 700, ../sass/_template_specific.scss */
.gallery a img {
  /*@include center-image();*/
  min-width: 100%;
  min-height: 100%;
}
/* line 705, ../sass/_template_specific.scss */
.gallery a img.video_ico {
  min-width: auto;
  min-height: auto;
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

/* line 723, ../sass/_template_specific.scss */
.gallery_filt_title {
  background-color: #efefef;
  border: 1px solid #dddddd !important;
  color: #004a8e !important;
  border-radius: 3px;
  padding: 5px 25px;
  box-sizing: border-box;
}

/* line 732, ../sass/_template_specific.scss */
.iframe_video_gallery {
  width: 800px;
  height: 600px;
  overflow: hidden;
}
/* line 737, ../sass/_template_specific.scss */
.iframe_video_gallery iframe {
  width: 800px;
  height: 600px;
}

/* line 743, ../sass/_template_specific.scss */
.topShadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: transparent;
  /* For browsers that do not support gradients */
  background: -webkit-linear-gradient(rgba(0, 0, 0, 0.3), transparent);
  /* For Safari 5.1 to 6.0 */
  background: -o-linear-gradient(rgba(0, 0, 0, 0.3), transparent);
  /* For Opera 11.1 to 12.0 */
  background: -moz-linear-gradient(rgba(0, 0, 0, 0.3), transparent);
  /* For Firefox 3.6 to 15 */
  background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
  /* Standard syntax */
}

/* line 756, ../sass/_template_specific.scss */
.bottomShadow {
  position: absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  height: 30px;
  background: transparent;
  /* For browsers that do not support gradients */
  background: -webkit-linear-gradient(transparent, rgba(0, 0, 0, 0.3));
  /* For Safari 5.1 to 6.0 */
  background: -o-linear-gradient(transparent, rgba(0, 0, 0, 0.3));
  /* For Opera 11.1 to 12.0 */
  background: -moz-linear-gradient(transparent, rgba(0, 0, 0, 0.3));
  /* For Firefox 3.6 to 15 */
  background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.3));
  /* Standard syntax */
}

/* line 769, ../sass/_template_specific.scss */
.block_gallery {
  display: table;
  position: relative;
  background: #2872b6;
  padding: 30px 0px 50px 0px;
  overflow: hidden;
  width: 100%;
}
/* line 776, ../sass/_template_specific.scss */
.block_gallery .background {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  opacity: 0.2;
}
/* line 780, ../sass/_template_specific.scss */
.block_gallery .block_x3-wrapper {
  margin: auto;
}
/* line 784, ../sass/_template_specific.scss */
.block_gallery h3 {
  position: relative;
  text-align: center;
  color: #75aadb;
  font-weight: 100;
  padding: 10px;
  text-shadow: rgba(0, 0, 0, 0.5) 0px 0px 10px;
  padding-bottom: 30px;
  margin-bottom: 10px;
  font-size: 18px;
}
/* line 794, ../sass/_template_specific.scss */
.block_gallery h3 span {
  display: block;
  text-shadow: transparent 0px 0px 0px;
  color: white;
  font-size: 28px;
  font-weight: normal;
}
/* line 804, ../sass/_template_specific.scss */
.block_gallery .bottom-gallery .small,
.block_gallery .bottom-gallery .big {
  margin-right: 5px;
  margin-bottom: 5px;
  float: left;
  overflow: hidden;
  width: 280px;
  position: relative;
}
/* line 813, ../sass/_template_specific.scss */
.block_gallery .bottom-gallery .small.item-6, .block_gallery .bottom-gallery .small.item-7,
.block_gallery .bottom-gallery .big.item-6,
.block_gallery .bottom-gallery .big.item-7 {
  margin-top: -195px;
}
/* line 816, ../sass/_template_specific.scss */
.block_gallery .bottom-gallery .small.item-7,
.block_gallery .bottom-gallery .big.item-7 {
  margin-left: 570px;
  height: 190px;
}
/* line 821, ../sass/_template_specific.scss */
.block_gallery .bottom-gallery .small img,
.block_gallery .bottom-gallery .big img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 826, ../sass/_template_specific.scss */
.block_gallery .bottom-gallery .small {
  height: 190px;
}
/* line 830, ../sass/_template_specific.scss */
.block_gallery .bottom-gallery .big {
  height: 385px;
}
/* line 833, ../sass/_template_specific.scss */
.block_gallery .description {
  position: absolute;
  bottom: -200px;
  left: 0px;
  right: 0px;
  padding: 5px;
  color: #dddddd;
  font-weight: 100;
  background-color: rgba(0, 0, 0, 0.6);
  -webkit-transition-duration: 1s;
  /* Safari */
  transition-duration: 1s;
}
/* line 844, ../sass/_template_specific.scss */
.block_gallery .description span {
  display: block;
  font-weight: bolder;
  font-size: 24px;
  color: white;
}
/* line 851, ../sass/_template_specific.scss */
.block_gallery a:hover .description {
  bottom: 0px;
}

/* line 856, ../sass/_template_specific.scss */
.block_x3_horizontal {
  display: table;
  position: relative;
  background: #222222;
  padding: 30px 0px 50px 0px;
  overflow: hidden;
  width: 100%;
}
/* line 863, ../sass/_template_specific.scss */
.block_x3_horizontal .background {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  opacity: 0.2;
}
/* line 867, ../sass/_template_specific.scss */
.block_x3_horizontal .block_x3-wrapper {
  margin: auto;
}
/* line 871, ../sass/_template_specific.scss */
.block_x3_horizontal h3 {
  position: absolute;
  text-align: left;
  background: white url("/img/fuenh/corner.png?v=1") no-repeat 208px 5px;
  top: 0px;
  left: -190px;
  width: 420px;
  font-size: 28px;
  font-family: 'Oswald', sans-serif;
  color: #222222;
  -ms-transform: rotate(-90deg);
  /* IE 9 */
  -webkit-transform: rotate(-90deg);
  /* Chrome, Safari, Opera */
  transform: rotate(-90eg);
  font-weight: bold;
  padding: 10px;
  margin-bottom: 10px;
}
/* line 887, ../sass/_template_specific.scss */
.block_x3_horizontal h3 span {
  display: block;
  color: white;
  font-size: 28px;
  font-weight: normal;
}
/* line 895, ../sass/_template_specific.scss */
.block_x3_horizontal .description {
  position: absolute;
  width: 420px;
  top: 60px;
  right: 0px;
  font-weight: 100;
  background-color: rgba(40, 114, 182, 0.9);
  color: white;
  padding: 20px;
}
/* line 904, ../sass/_template_specific.scss */
.block_x3_horizontal .description span {
  display: block;
  font-weight: normal;
  font-size: 22px;
}
/* line 910, ../sass/_template_specific.scss */
.block_x3_horizontal .block {
  position: relative;
  width: 100%;
  overflow: hidden;
  margin: 20px 0;
  height: 250px;
}
/* line 916, ../sass/_template_specific.scss */
.block_x3_horizontal .block img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 924, ../sass/_template_specific.scss */
.block_video .block_video_desc, .block_video iframe {
  display: inline-block;
  width: calc(95% / 2);
  box-sizing: border-box;
}
/* line 929, ../sass/_template_specific.scss */
.block_video iframe {
  height: 350px;
  vertical-align: top;
}
/* line 933, ../sass/_template_specific.scss */
.block_video .subtitle_description {
  padding: 0px 30px;
}

/* line 938, ../sass/_template_specific.scss */
.google_maps {
  margin-bottom: 100px;
  padding: 0px 30px;
  box-sizing: border-box;
}
/* line 942, ../sass/_template_specific.scss */
.google_maps .block {
  display: table;
  background-color: white;
  box-shadow: 3px 3px 15px rgba(0, 0, 0, 0.3);
}
/* line 946, ../sass/_template_specific.scss */
.google_maps .block .content_subtitle_wrapper {
  margin: auto;
}
/* line 950, ../sass/_template_specific.scss */
.google_maps .google_maps_desc, .google_maps iframe {
  display: inline-block;
  width: calc(99% / 2);
  box-sizing: border-box;
}
/* line 955, ../sass/_template_specific.scss */
.google_maps .google_maps_desc {
  margin-top: 35px;
}
/* line 958, ../sass/_template_specific.scss */
.google_maps iframe {
  height: 446px;
  margin-left: -6px;
  vertical-align: top;
}
/* line 963, ../sass/_template_specific.scss */
.google_maps .subtitle_description {
  padding: 0px 30px;
}
/* line 966, ../sass/_template_specific.scss */
.google_maps .mapForm {
  width: 362px;
  margin: 40px auto;
}
/* line 970, ../sass/_template_specific.scss */
.google_maps input {
  float: left;
  border: 1px solid #2872b6;
  background-color: white;
  height: 72px;
  margin-right: 0px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 30px;
  font-size: 20px;
  color: #2872b6;
  vertical-align: middle;
}
/* line 985, ../sass/_template_specific.scss */
.google_maps button {
  height: 72px;
  width: 72px;
  background: #2872b6 url("/img/fuenh/lupa.png?v=1") no-repeat center;
  display: block;
  border: 0;
  text-indent: 99px;
  overflow: hidden;
  cursor: pointer;
}
/* line 995, ../sass/_template_specific.scss */
.google_maps button:hover {
  opacity: 0.8;
}

/* line 1001, ../sass/_template_specific.scss */
.banner_booking {
  position: relative;
  overflow: hidden;
  background: #2872b6;
  box-sizing: border-box;
  height: 200px;
  margin-bottom: 200px;
  padding: 50px;
}
/* line 1009, ../sass/_template_specific.scss */
.banner_booking img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  opacity: 0.3;
  top: -120%;
}
/* line 1015, ../sass/_template_specific.scss */
.banner_booking .description {
  position: absolute;
  text-align: center;
  width: 420px;
  top: 60px;
  left: 100px;
  font-weight: 100;
  color: white;
  font-size: 24px;
}
/* line 1024, ../sass/_template_specific.scss */
.banner_booking .description span {
  display: block;
  font-weight: normal;
  background-color: #004a8e;
  font-size: 16px;
}
/* line 1031, ../sass/_template_specific.scss */
.banner_booking .button-promotion {
  position: absolute;
  top: 60px;
  right: 100px;
  background: #004a8e;
  font-size: 30px;
  padding: 20px 80px;
  color: white;
  text-decoration: none;
}
/* line 1041, ../sass/_template_specific.scss */
.banner_booking .button-promotion:hover {
  opacity: 0.9;
}

/* -=- --- --|     Carousel info     |-- --- -=- */
/* line 1048, ../sass/_template_specific.scss */
.carousel_info_full_background {
  background: #eeeeee;
  padding: 60px 0;
  margin-bottom: 65px;
}

/* line 1054, ../sass/_template_specific.scss */
.carousel_info_wrapper {
  position: relative;
}
/* line 1058, ../sass/_template_specific.scss */
.carousel_info_wrapper .carousel_info_element .carousel_info_content_wrapper {
  width: 70%;
  float: left;
  margin-bottom: 100px;
  box-sizing: border-box;
  padding: 133px 60px 70px;
  background: #efefef;
}
/* line 1066, ../sass/_template_specific.scss */
.carousel_info_wrapper .carousel_info_element .carousel_info_content_wrapper .actual_position {
  position: absolute;
  top: 20px;
  color: #222222;
  border-bottom: 1px solid #222222;
  padding-bottom: 8px;
  font-size: 18px;
  padding-left: 5px;
  padding-right: 5px;
}
/* line 1077, ../sass/_template_specific.scss */
.carousel_info_wrapper .carousel_info_element .carousel_info_content_wrapper .carousel_info_title {
  font-style: italic;
  font-size: 18px;
  color: #75aadb;
  margin-bottom: 28px;
}
/* line 1082, ../sass/_template_specific.scss */
.carousel_info_wrapper .carousel_info_element .carousel_info_content_wrapper .carousel_info_title span {
  color: #2872b6;
  font-size: 28px;
  font-style: normal;
}
/* line 1089, ../sass/_template_specific.scss */
.carousel_info_wrapper .carousel_info_element .carousel_info_content_wrapper .carousel_info_text {
  font-weight: 100;
  font-family: 'Open Sans', sans-serif;
  margin-top: 8px;
  font-size: 11px;
  line-height: 23px;
  color: #222222;
}
/* line 1096, ../sass/_template_specific.scss */
.carousel_info_wrapper .carousel_info_element .carousel_info_content_wrapper .carousel_info_text h3 {
  color: #2872b6;
}
/* line 1102, ../sass/_template_specific.scss */
.carousel_info_wrapper .carousel_info_element .carousel_info_picture_wrapper {
  width: 30%;
  float: right;
  height: 310px;
}
/* line 1107, ../sass/_template_specific.scss */
.carousel_info_wrapper .carousel_info_element .carousel_info_picture_wrapper .exceded {
  height: 310px;
  position: relative;
  overflow: hidden;
}
/* line 1112, ../sass/_template_specific.scss */
.carousel_info_wrapper .carousel_info_element .carousel_info_picture_wrapper .exceded .carousel_info_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 1119, ../sass/_template_specific.scss */
.carousel_info_wrapper li.flex-nav-prev {
  position: absolute;
  top: 0;
  right: 430px;
  padding: 20px;
  overflow: hidden;
  text-align: center;
  background: #2872b6;
}
/* line 1128, ../sass/_template_specific.scss */
.carousel_info_wrapper li.flex-nav-prev img {
  display: block;
}
/* line 1133, ../sass/_template_specific.scss */
.carousel_info_wrapper li.flex-nav-next {
  position: absolute;
  top: 0;
  right: 354px;
  padding: 20px;
  overflow: hidden;
  text-align: center;
  background: #2872b6;
}
/* line 1142, ../sass/_template_specific.scss */
.carousel_info_wrapper li.flex-nav-next img {
  display: block;
}

/* -=- --- --|     Cycle Banners     |-- --- -=- */
/* line 1149, ../sass/_template_specific.scss */
.cycle_banners_wrapper {
  text-align: center;
}
/* line 1152, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element {
  box-sizing: border-box;
  padding: 20px;
  display: inline-block;
  width: 49%;
  vertical-align: top;
  color: #222222;
}
/* line 1159, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .center_div {
  margin-top: -5px;
  text-align: left;
}
/* line 1162, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .center_div h3 {
  color: #2872b6;
  font-size: 22px;
  padding: 10px 5px 0px 5px;
}
/* line 1167, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .center_div .cycle_description {
  color: #75aadb;
  height: 60px;
  font-weight: 100;
  padding: 0px 5px;
  display: table;
}
/* line 1174, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .center_div .btn {
  display: block;
  text-align: center;
  margin-top: 20px;
  border: 1px solid #2872b6;
  background-color: white;
  font-weight: normal;
  font-size: 18px;
  text-transform: uppercase;
  padding: 15px 50px;
  color: #2872b6;
  text-decoration: none;
}
/* line 1187, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .center_div .btn:hover {
  opacity: 0.8;
}
/* line 1191, ../sass/_template_specific.scss */
.cycle_banners_wrapper .cycle_element .center_div .btn.seeMore {
  background-color: #2872b6;
  color: white;
}

/* -=- --- --|     Footer     |-- --- -=- */
/* line 1201, ../sass/_template_specific.scss */
footer {
  position: relative;
  padding-top: 100px;
  background-color: #efefef;
}
/* line 1205, ../sass/_template_specific.scss */
footer .blutterTop {
  background: #2872b6;
  height: 100px;
  transform: skewY(-5deg);
  margin-top: 100px;
}
/* line 1211, ../sass/_template_specific.scss */
footer .blutter {
  background: #2872b6;
  position: relative;
  margin-top: -50px;
  padding: 0px 20px;
  box-sizing: border-box;
}
/* line 1217, ../sass/_template_specific.scss */
footer .blutter .footer_column {
  display: inline-block;
  float: left;
  width: calc(100% / 4);
  padding: 10px;
  box-sizing: border-box;
  color: #75aadb;
  line-height: 18px;
}
/* line 1225, ../sass/_template_specific.scss */
footer .blutter .footer_column h3, footer .blutter .footer_column h2 {
  font-size: 28px;
  color: white;
  margin-bottom: 20px;
}
/* line 1230, ../sass/_template_specific.scss */
footer .blutter .footer_column a {
  display: inline-block;
  color: white;
  text-decoration: none;
  padding: 5px;
  margin: 2px;
  background: #004a8e;
  font-weight: 100;
}
/* line 1239, ../sass/_template_specific.scss */
footer .blutter .footer_column a:hover {
  opacity: 0.8;
}
/* line 1243, ../sass/_template_specific.scss */
footer .blutter .footer_column img {
  max-width: 75%;
}
/* line 1247, ../sass/_template_specific.scss */
footer .blutter .footer_column .newsletter_container {
  width: auto;
}
/* line 1252, ../sass/_template_specific.scss */
footer .blutter .footer_column .newsletter_form input[type=text] {
  width: 90%;
  margin-top: 10px;
  padding: 7px;
  border: 0px;
  border-radius: 5px;
}
/* line 1259, ../sass/_template_specific.scss */
footer .blutter .footer_column .newsletter_form .button_newsletter {
  background: #004a8e;
  border: 0;
  margin-top: 10px;
  padding: 10px 40px;
  letter-spacing: 2px;
  color: white;
  text-transform: uppercase;
  font-size: 14px;
  cursor: pointer;
}
/* line 1270, ../sass/_template_specific.scss */
footer .blutter .footer_column .newsletter_form .button_newsletter:hover {
  opacity: 0.8;
}
/* line 1275, ../sass/_template_specific.scss */
footer .blutter .footer_column .newsletter_form .newsletter_checkbox {
  font-size: 12px;
}
/* line 1278, ../sass/_template_specific.scss */
footer .blutter .footer_column .newsletter_form .newsletter_checkbox a {
  text-decoration: underline !important;
  background: none !important;
}
/* line 1284, ../sass/_template_specific.scss */
footer .blutter .footer_column .newsletter_form input#privacy {
  margin-right: -3px;
}
/* line 1288, ../sass/_template_specific.scss */
footer .blutter .footer_column .newsletter_form input#promotions {
  float: left;
  margin-bottom: 4px;
}
/* line 1294, ../sass/_template_specific.scss */
footer .blutter .first {
  margin-top: -100px;
  background: white;
  min-height: 300px;
  box-shadow: 2px 2px 15px rgba(0, 0, 0, 0.5);
  margin-bottom: 50px;
}
/* line 1300, ../sass/_template_specific.scss */
footer .blutter .first h3, footer .blutter .first h2 {
  font-size: 28px;
  color: #2872b6;
}
/* line 1306, ../sass/_template_specific.scss */
footer .blutter .extra_footer_info_container {
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
/* line 1312, ../sass/_template_specific.scss */
footer .blutter .extra_footer_info_container .info_item_wrapper {
  color: white;
  font-weight: 100;
  line-height: 25px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 30px;
}
/* line 1322, ../sass/_template_specific.scss */
footer .blutter .extra_footer_info_container .info_item_wrapper .info_img_wrapper {
  max-width: 90%;
  margin: 0 auto;
}
/* line 1326, ../sass/_template_specific.scss */
footer .blutter .extra_footer_info_container .info_item_wrapper .info_img_wrapper img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
/* line 1335, ../sass/_template_specific.scss */
footer .blutter .social {
  text-align: center;
  clear: both;
}
/* line 1338, ../sass/_template_specific.scss */
footer .blutter .social a {
  display: inline-block;
  margin: 10px;
  text-decoration: none;
}
/* line 1343, ../sass/_template_specific.scss */
footer .blutter .social a:hover {
  opacity: 0.8;
}
/* line 1349, ../sass/_template_specific.scss */
footer .full-copyright {
  background-color: #666666;
  color: white;
  text-align: center;
  padding: 20px 0px;
  font-size: 14px;
}
/* line 1355, ../sass/_template_specific.scss */
footer .full-copyright a {
  color: white;
  text-decoration: none;
}

/*============== Bottom Pop-up ============*/
/* line 1364, ../sass/_template_specific.scss */
.bottom_popup {
  position: fixed;
  width: 100%;
  height: 120px;
  background: #2872b6;
  left: 0;
  bottom: 0;
  z-index: 1000;
}

/* line 1374, ../sass/_template_specific.scss */
.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

/* line 1380, ../sass/_template_specific.scss */
.bottom_popup .bottom_popup_text {
  width: 942px;
  float: left;
  color: white;
  padding: 10px;
  font-family: Arial, sans-serif;
  position: relative;
  padding-top: 40px;
}

/* line 1390, ../sass/_template_specific.scss */
.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

/* line 1394, ../sass/_template_specific.scss */
.close_button {
  float: right;
  cursor: pointer;
}

/* line 1399, ../sass/_template_specific.scss */
button.bottom_popup_button_book {
  width: 175px;
  background: white;
  border: 0;
  height: 50px;
  position: absolute;
  text-transform: uppercase;
  color: #2872b6;
  background-position: center;
  border-radius: 0px;
  cursor: pointer;
  font-size: 19px;
  top: 0;
  bottom: 0;
  margin: auto;
}

/* line 1416, ../sass/_template_specific.scss */
#wrapper2 {
  width: 1140px;
  margin: 0 auto;
}

/* line 1421, ../sass/_template_specific.scss */
.popup_thanks {
  box-sizing: border-box;
  padding-top: 20px;
  text-align: center;
}
/* line 1425, ../sass/_template_specific.scss */
.popup_thanks #new_gracias_newsletter {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  margin: 20px;
  padding: 20px;
}
/* line 1431, ../sass/_template_specific.scss */
.popup_thanks button {
  background-color: #2872b6;
  color: white;
  font-size: 20px;
  border-width: 0;
  padding: 10px 20px;
}
/* line 1437, ../sass/_template_specific.scss */
.popup_thanks button:hover {
  background-color: #1f588c;
}

/* line 1442, ../sass/_template_specific.scss */
.applyed {
  background-color: #75aadb !important;
  color: white !important;
  text-align: center;
  font-size: 18px !important;
}

/* line 1448, ../sass/_template_specific.scss */
.popup_inicial, .popup_thanks {
  width: 100%;
  height: 100%;
  background-size: cover !important;
}
/* line 1452, ../sass/_template_specific.scss */
.popup_inicial .email, .popup_inicial .discount, .popup_inicial .compra, .popup_thanks .email, .popup_thanks .discount, .popup_thanks .compra {
  text-align: center;
}
/* line 1455, ../sass/_template_specific.scss */
.popup_inicial .compra, .popup_thanks .compra {
  padding-top: 5px;
  color: white;
  font-size: 22px;
  font-weight: lighter;
}
/* line 1461, ../sass/_template_specific.scss */
.popup_inicial .discount, .popup_thanks .discount {
  padding-top: 7px;
  color: white;
  font-size: 47px;
  text-shadow: 3px 3px black;
  text-transform: uppercase;
  font-family: 'Oswald', sans-serif;
}
/* line 1469, ../sass/_template_specific.scss */
.popup_inicial .email, .popup_thanks .email {
  padding-top: 39px;
  color: white;
  font-size: 22px;
  font-weight: lighter;
}
/* line 1475, ../sass/_template_specific.scss */
.popup_inicial form.form_popup, .popup_thanks form.form_popup {
  text-align: center;
  padding-top: 50px;
}
/* line 1478, ../sass/_template_specific.scss */
.popup_inicial form.form_popup li, .popup_thanks form.form_popup li {
  text-align: center;
}
/* line 1481, ../sass/_template_specific.scss */
.popup_inicial form.form_popup input#id_email, .popup_thanks form.form_popup input#id_email {
  height: 26px;
  text-align: center;
  width: 270px;
  font-size: 17px;
  box-shadow: 2px 2px black;
  border: 0px;
  color: #2872b6;
}
/* line 1490, ../sass/_template_specific.scss */
.popup_inicial form.form_popup button.popup_button, .popup_thanks form.form_popup button.popup_button {
  margin: 7px auto 0px;
  width: 277px;
  height: 40px;
  background: #2872b6;
  font-size: 17px;
  border: 0px;
  text-transform: uppercase;
  color: white;
  cursor: pointer;
}
/* line 1502, ../sass/_template_specific.scss */
.popup_inicial .spinner_wrapper_faldon, .popup_thanks .spinner_wrapper_faldon {
  padding-top: 20px;
}
/* line 1505, ../sass/_template_specific.scss */
.popup_inicial .popup_message, .popup_thanks .popup_message {
  color: white;
  padding-top: 25px;
  font-size: 20px;
  font-weight: lighter;
}

/* line 1513, ../sass/_template_specific.scss */
img.image_before_text {
  width: 150px !important;
  position: absolute !important;
  bottom: 10px;
}

/* line 1519, ../sass/_template_specific.scss */
.bottom_popup_text_inside {
  text-align: center;
  padding-top: 0;
  font-size: 15px;
  padding-right: 33px;
  padding-left: 191px;
  font-family: gotham;
  line-height: 22px;
  font-weight: lighter;
}
/* line 1529, ../sass/_template_specific.scss */
.bottom_popup_text_inside strong {
  font-weight: bolder;
}

/*=== Languages ===*/
/* line 1536, ../sass/_template_specific.scss */
html[lang="en"] #my-bookings-form-fields #localizadorInput {
  width: 160px;
}

/* line 1542, ../sass/_template_specific.scss */
html[lang="fr"] #my-bookings-form-fields #localizadorInput {
  width: 140px;
}
