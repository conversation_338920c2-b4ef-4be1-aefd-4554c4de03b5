/* line 1, ../../../../sass/booking/_booking_engine.scss */
.booking_widget {
  position: absolute;
  z-index: 400;
  top: 185px;
}

/* line 7, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title {
  background: #cdb480;
  text-align: center;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 20px;
  padding-top: 5px;
  padding-bottom: 5px;
}
/* line 15, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title > * {
  margin-top: 0;
  color: white;
}
/* line 20, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title .booking_title_1,
.booking_form_title .booking_title_2 {
  display: none;
}

/* line 26, ../../../../sass/booking/_booking_engine.scss */
.booking_form {
  font-family: 'Source Sans Pro', sans-serif;
  padding: 20px;
  width: 260px;
  background: white;
}

/* line 33, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper {
  position: relative;
}
/* line 35, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper label {
  color: gray;
  font-size: 12px;
}
/* line 40, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper input {
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  height: 40px;
  border: 0;
  width: 250px;
  padding-left: 10px;
}
/* line 48, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper .right_arrow {
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  position: absolute;
  background: #cdb480 url(/static_1/images/booking/flecha_motor_der.png) no-repeat center center;
  right: 2px;
  top: 30px;
  height: 35px;
  width: 35px;
}

/* line 62, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper {
  margin-right: 10px;
}
/* line 67, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper,
.stay_selection .rooms_number_wrapper {
  float: left;
  width: 80px;
}
/* line 74, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper label,
.stay_selection .departure_date_wrapper label,
.stay_selection .rooms_number_wrapper label {
  color: gray;
  font-size: 12px;
}

/* line 83, ../../../../sass/booking/_booking_engine.scss */
.date_box {
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  background: white;
  height: 40px;
}
/* line 90, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_day,
.date_box .date_year {
  display: block;
  text-align: center;
}
/* line 96, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_day {
  text-transform: uppercase;
  color: #cdb480;
  margin-left: 5px;
  margin-right: 5px;
  font-size: 16px;
  font-weight: bolder;
}
/* line 105, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_year {
  color: white;
  font-size: 12px;
  height: 14px;
  line-height: 14px;
}

/* line 113, ../../../../sass/booking/_booking_engine.scss */
.room {
  clear: both;
  margin-bottom: 5px;
}
/* line 116, ../../../../sass/booking/_booking_engine.scss */
.room .room_title {
  margin-top: 25px;
}
/* line 120, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector {
  margin-right: 10px;
}
/* line 125, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector,
.room .children_selector,
.room .babies_selector {
  float: left;
  width: 80px;
}
/* line 131, ../../../../sass/booking/_booking_engine.scss */
.room .room_title label,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  display: block;
}
/* line 136, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  color: gray;
  font-size: 12px;
}

/* line 145, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button {
  font-family: 'Source Sans Pro', sans-serif;
  position: relative;
  margin-top: 50px;
  text-align: left;
}
/* line 152, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button .promocode_input {
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  border: 0;
  padding: 0;
  margin-top: 10px;
  text-align: center;
  color: #cdb480;
  width: 170px;
  height: 40px;
  font-size: 16px;
}
/* line 164, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button button {
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  color: white;
  border: 0;
  float: right;
  background: #cdb480;
  font-size: 16px;
  text-transform: uppercase;
  height: 40px;
  width: 80px;
  margin-top: 10px;
}
/* line 177, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button .spinner_wrapper {
  position: absolute;
  right: 30px;
  bottom: 20px;
}

/* line 184, ../../../../sass/booking/_booking_engine.scss */
.horizontal_engine {
  height: 379px;
  background: white !important;
}

/* line 189, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal {
  width: 600px;
  margin: 0 auto;
  padding-top: 172px;
}
/* line 194, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 199, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form {
  border-left: 1px solid white;
  border-right: 1px solid white;
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
}
/* line 207, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form_title {
  display: none;
}
/* line 211, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form {
  width: 560px;
}
/* line 215, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .destination_wrapper {
  float: left;
}
/* line 219, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .stay_selection {
  float: right;
}
/* line 223, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .room {
  float: right;
}
/* line 227, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .wrapper_booking_button {
  clear: both;
}
/* line 231, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .promocode_input {
  width: 260px !important;
}
/* line 235, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .wrapper_booking_button button {
  width: 170px !important;
}

/* line 241, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline {
  width: 1140px;
  height: 75px;
  margin: 0 auto;
  padding-top: 20px;
  background-color: gray;
}
/* line 248, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 253, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form {
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
  background-color: gray;
}
/* line 260, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form_title {
  display: none;
}
/* line 264, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form {
  width: 1140px;
  padding: 0;
}
/* line 269, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .destination_wrapper {
  float: left;
}
/* line 273, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .stay_selection {
  float: left;
  margin-left: 90px;
}
/* line 277, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .stay_selection .entry_date_wrapper label,
.boking_widget_inline .stay_selection .departure_date_wrapper label,
.boking_widget_inline .stay_selection .rooms_number_wrapper label {
  color: #787878;
  font-size: 12px;
}
/* line 285, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room_list_wrapper {
  float: left;
  margin-left: 20px;
  margin-right: 20px;
  margin-top: 5px;
}
/* line 291, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room {
  float: right;
}
/* line 294, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room .room_title,
.boking_widget_inline .room .adults_selector label,
.boking_widget_inline .room .children_selector label,
.boking_widget_inline .room .babies_selector label {
  color: gray;
  font-size: 12px;
}
/* line 301, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room .room_title {
  text-align: right;
}
/* line 307, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button {
  display: inline;
  margin-top: 10px;
}
/* line 311, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  float: none;
}
/* line 316, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .promocode_input {
  width: 200px !important;
  margin-right: 20px;
  margin-top: 19px;
  background-color: #5a5a5a;
}
/* line 323, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  width: 170px !important;
}
/* line 327, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .selectric, .boking_widget_inline .date_box {
  background: #5a5a5a;
}

/* line 333, ../../../../sass/booking/_booking_engine.scss */
.booking_footer_message {
  margin-top: 65px;
}

/* line 338, ../../../../sass/booking/_booking_engine.scss */
.booking-form-center-text {
  text-align: center;
}

/* line 342, ../../../../sass/booking/_booking_engine.scss */
.babies_selector {
  margin-left: 10px;
}

/* line 346, ../../../../sass/booking/_booking_engine.scss */
.room_title.room_title_with_babies {
  float: none;
  margin-bottom: 5px;
  padding-top: 10px;
  color: #cdb480;
  text-align: center;
  width: 100%;
  text-transform: uppercase;
}

/* line 359, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text {
  margin-top: 7px;
}
/* line 362, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .stay_selection {
  margin-left: 45px !important;
}
/* line 366, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .room_title.room_title_with_babies {
  margin-top: -30px;
  text-align: center;
}
/* line 371, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .room_list_wrapper {
  margin-left: 60px;
}

/* line 379, ../../../../sass/booking/_booking_engine.scss */
.range-age {
  width: 55px;
  display: inline-block;
  float: right;
  white-space: nowrap;
}

/* line 387, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 {
  border-radius: 0;
}
/* line 389, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-skin {
  border-radius: 0;
}
/* line 392, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide > * {
  padding: 0 !important;
  background-color: transparent;
}
/* line 396, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide .calendar_popup_wrapper {
  background-color: transparent;
}
/* line 400, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .full_screen_engine .container_popup_booking {
  background-color: transparent !important;
  padding: 24px !important;
}
/* line 405, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  color: gray;
}
/* line 408, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading {
  width: 50px;
  height: 50px;
  margin: auto;
  position: absolute;
  left: 0;
  right: 0;
  top: 100px;
  bottom: 0;
}
/* line 10, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:before {
  content: '';
  width: 50px;
  height: 5px;
  background: #000;
  opacity: 0.3;
  position: absolute;
  top: 59px;
  left: 0;
  border-radius: 50%;
  animation: box_shadow .5s linear infinite;
}
/* line 22, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:after {
  content: '';
  width: 50px;
  height: 50px;
  background: white;
  animation: box_animate .5s linear infinite;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 3px;
}
@keyframes box_animate {
  17% {
    border-bottom-right-radius: 3px;
  }
  25% {
    transform: translateY(9px) rotate(22.5deg);
  }
  50% {
    transform: translateY(18px) scale(1, 0.9) rotate(45deg);
    border-bottom-right-radius: 40px;
  }
  75% {
    transform: translateY(9px) rotate(67.5deg);
  }
  100% {
    transform: translateY(0) rotate(90deg);
  }
}
@keyframes box_shadow {
  0%, 100% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.2, 1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader {
  padding: 60px 0 70px;
}
/* line 3, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot {
  width: 24px;
  height: 24px;
  margin: 0 3px;
  background: #000;
  border-radius: 100%;
  display: inline-block;
  animation: slide 1s infinite;
}
/* line 11, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(1) {
  animation-delay: 0.1s;
}
/* line 14, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(2) {
  animation-delay: 0.2s;
}
/* line 17, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(3) {
  animation-delay: 0.3s;
}
/* line 20, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(4) {
  animation-delay: 0.4s;
}
/* line 23, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(5) {
  animation-delay: 0.5s;
}
@-moz-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-webkit-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-o-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube {
  margin: 20px auto;
  width: 40px;
  height: 40px;
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}
/* line 7, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube {
  float: left;
  width: 50%;
  height: 50%;
  position: relative;
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}
/* line 15, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  -webkit-animation: sk-foldCubeAngle 2.4s infinite linear both;
  animation: sk-foldCubeAngle 2.4s infinite linear both;
  -webkit-transform-origin: 100% 100%;
  -ms-transform-origin: 100% 100%;
  transform-origin: 100% 100%;
}
/* line 30, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2 {
  -webkit-transform: scale(1.1) rotateZ(90deg);
  transform: scale(1.1) rotateZ(90deg);
}
/* line 34, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3 {
  -webkit-transform: scale(1.1) rotateZ(270deg);
  transform: scale(1.1) rotateZ(270deg);
}
/* line 38, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4 {
  -webkit-transform: scale(1.1) rotateZ(180deg);
  transform: scale(1.1) rotateZ(180deg);
}
/* line 43, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2:before {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
/* line 49, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3:before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
}
/* line 55, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4:before {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
}
@-webkit-keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
@keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
/* line 1, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots {
  padding: 40px 0;
  text-align: center;
  animation: dots-rotate 2s linear infinite;
}
/* line 5, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  content: '';
  animation: dots-margin 2s linear infinite;
}
/* line 9, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots span, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  display: inline-block;
  vertical-align: middle;
  width: 30px;
  height: 30px;
  background-color: white;
  border-radius: 50%;
  margin: 5px;
}
@keyframes dots-margin {
  0% {
    margin: 5px;
  }
  12% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  25% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  50% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  62% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  75% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  100% {
    margin: 5px;
  }
}
@keyframes dots-rotate {
  0% {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  25% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  50% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  75% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/* line 415, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_top_popup_booking {
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: lighter;
  color: gray;
}
/* line 423, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  font-weight: bold;
  text-transform: uppercase;
}
/* line 428, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .container_popup_booking {
  width: 555px;
  display: block;
  padding: 30px 0;
  box-sizing: border-box;
  margin: 7px;
  border: 1px solid #3483b2;
}

/* line 439, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine {
  width: auto !important;
  height: auto !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}
/* line 447, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-outer {
  background: transparent;
}
/* line 452, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine.custom_color_overlay .fancybox-skin {
  background: transparent;
}
/* line 457, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-skin {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}
/* line 466, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-inner {
  overflow: visible;
}
/* line 470, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 483, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  border: 0 !important;
}
/* line 487, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: lighter;
}
/* line 490, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking strong {
  font-weight: bold;
  text-decoration: underline;
  font-size: 14px;
}

/*=====================================================================
  Selectric
======================================================================*/
/* line 5, ../../../../sass/booking/_selectric.scss */
.selectricWrapper {
  position: relative;
  margin: 0 0 0px;
  width: 80px;
  cursor: pointer;
}

/* line 12, ../../../../sass/booking/_selectric.scss */
.selectricDisabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 23, ../../../../sass/booking/_selectric.scss */
.selectricOpen {
  z-index: 9999;
}

/* line 27, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 31, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

/* line 38, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect select {
  position: absolute;
  left: -100%;
  display: none;
}

/* line 44, ../../../../sass/booking/_selectric.scss */
.selectricInput {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  _font: 0/0 a;
  background: none !important;
}

/* line 60, ../../../../sass/booking/_selectric.scss */
.selectricTempShow {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* line 67, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectric {
  z-index: 9999;
}

/* line 72, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 76, ../../../../sass/booking/_selectric.scss */
.selectric {
  background: white;
  position: relative;
  border-radius: 6px;
  height: 40px;
}

/* line 83, ../../../../sass/booking/_selectric.scss */
.selectric .label {
  display: block;
  overflow: hidden;
  font-size: 16px;
  line-height: 40px;
  color: #cdb480;
  text-align: left;
  margin-left: 20px;
}

/* line 93, ../../../../sass/booking/_selectric.scss */
.selectric .button {
  zoom: 1;
  position: absolute;
  font: 0/0 a;
  overflow: hidden;
  margin: auto;
  top: 0;
  right: 2px;
  bottom: 0;
  width: 35px;
  height: 35px;
  border: 0;
  background: #cdb480 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  padding: 0;
}

/* line 109, ../../../../sass/booking/_selectric.scss */
.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */
/* line 115, ../../../../sass/booking/_selectric.scss */
.selectricItems {
  display: none;
  position: absolute;
  overflow: hidden;
  top: 100%;
  left: 0;
  background: #F9F9F9;
  border: 1px solid #CCC;
  z-index: 9998;
  -webkit-box-shadow: 0 0 10px -6px;
  box-shadow: 0 0 10px -6px;
}

/* line 128, ../../../../sass/booking/_selectric.scss */
.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* line 138, ../../../../sass/booking/_selectric.scss */
.selectricItems li {
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 146, ../../../../sass/booking/_selectric.scss */
.selectricItems li:hover {
  background: #F0F0F0;
  color: #444;
}
/* line 151, ../../../../sass/booking/_selectric.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}

/* line 158, ../../../../sass/booking/_selectric.scss */
.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;
  cursor: default;
}

@font-face {
  font-family: 'FontAwesome';
  src: url("/static_1/fonts/fontawesome/fontawesome/fontawesome-webfont.eot?v=4.7.0");
  src: url("/static_1/fonts/fontawesome/fontawesome-webfont.eot?#iefix&v=4.7.0") format("embedded-opentype"), url("/static_1/fonts/fontawesome/fontawesome-webfont.woff2?v=4.7.0") format("woff2"), url("/static_1/fonts/fontawesome/fontawesome-webfont.woff?v=4.7.0") format("woff"), url("/static_1/fonts/fontawesome/fontawesome-webfont.ttf?v=4.7.0") format("truetype"), url("/static_1/fonts/fontawesome/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular") format("svg");
  font-weight: normal;
  font-style: normal;
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lg {
  font-size: 1.33333333em;
  line-height: .75em;
  vertical-align: -15%;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-3x {
  font-size: 3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-4x {
  font-size: 4em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-5x {
  font-size: 5em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fw {
  width: 1.28571429em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul > li {
  position: relative;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: .14285714em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li.fa-lg {
  left: -1.85714286em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-border {
  padding: .2em .25em .15em;
  border: solid .08em #eee;
  border-radius: .1em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-left {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-right {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-left {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-right {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  transform: scale(-1, 1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical {
  filter: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x, .fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x {
  line-height: inherit;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inverse {
  color: #fff;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glass:before {
  content: "\f000";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-music:before {
  content: "\f001";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search:before {
  content: "\f002";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-o:before {
  content: "\f003";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart:before {
  content: "\f004";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star:before {
  content: "\f005";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-o:before {
  content: "\f006";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user:before {
  content: "\f007";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-film:before {
  content: "\f008";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-large:before {
  content: "\f009";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th:before {
  content: "\f00a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-list:before {
  content: "\f00b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check:before {
  content: "\f00c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-remove:before, .fa-close:before, .fa-times:before {
  content: "\f00d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-plus:before {
  content: "\f00e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-minus:before {
  content: "\f010";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-power-off:before {
  content: "\f011";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signal:before {
  content: "\f012";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gear:before, .fa-cog:before {
  content: "\f013";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash-o:before {
  content: "\f014";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-home:before {
  content: "\f015";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-o:before {
  content: "\f016";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clock-o:before {
  content: "\f017";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-road:before {
  content: "\f018";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-download:before {
  content: "\f019";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-down:before {
  content: "\f01a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-up:before {
  content: "\f01b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inbox:before {
  content: "\f01c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle-o:before {
  content: "\f01d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-right:before, .fa-repeat:before {
  content: "\f01e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-refresh:before {
  content: "\f021";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-alt:before {
  content: "\f022";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lock:before {
  content: "\f023";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag:before {
  content: "\f024";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-headphones:before {
  content: "\f025";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-off:before {
  content: "\f026";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-down:before {
  content: "\f027";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-up:before {
  content: "\f028";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qrcode:before {
  content: "\f029";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-barcode:before {
  content: "\f02a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tag:before {
  content: "\f02b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tags:before {
  content: "\f02c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-book:before {
  content: "\f02d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark:before {
  content: "\f02e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-print:before {
  content: "\f02f";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera:before {
  content: "\f030";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-font:before {
  content: "\f031";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bold:before {
  content: "\f032";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-italic:before {
  content: "\f033";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-height:before {
  content: "\f034";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-width:before {
  content: "\f035";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-left:before {
  content: "\f036";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-center:before {
  content: "\f037";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-right:before {
  content: "\f038";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-justify:before {
  content: "\f039";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list:before {
  content: "\f03a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dedent:before, .fa-outdent:before {
  content: "\f03b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-indent:before {
  content: "\f03c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-video-camera:before {
  content: "\f03d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-photo:before, .fa-image:before, .fa-picture-o:before {
  content: "\f03e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil:before {
  content: "\f040";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-marker:before {
  content: "\f041";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adjust:before {
  content: "\f042";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tint:before {
  content: "\f043";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edit:before, .fa-pencil-square-o:before {
  content: "\f044";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square-o:before {
  content: "\f045";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square-o:before {
  content: "\f046";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows:before {
  content: "\f047";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-backward:before {
  content: "\f048";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-backward:before {
  content: "\f049";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-backward:before {
  content: "\f04a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play:before {
  content: "\f04b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause:before {
  content: "\f04c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop:before {
  content: "\f04d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forward:before {
  content: "\f04e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-forward:before {
  content: "\f050";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-forward:before {
  content: "\f051";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eject:before {
  content: "\f052";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-left:before {
  content: "\f053";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-right:before {
  content: "\f054";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-circle:before {
  content: "\f055";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-circle:before {
  content: "\f056";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle:before {
  content: "\f057";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle:before {
  content: "\f058";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle:before {
  content: "\f059";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info-circle:before {
  content: "\f05a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crosshairs:before {
  content: "\f05b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle-o:before {
  content: "\f05c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle-o:before {
  content: "\f05d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ban:before {
  content: "\f05e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-left:before {
  content: "\f060";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-right:before {
  content: "\f061";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-up:before {
  content: "\f062";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-down:before {
  content: "\f063";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-forward:before, .fa-share:before {
  content: "\f064";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expand:before {
  content: "\f065";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compress:before {
  content: "\f066";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus:before {
  content: "\f067";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus:before {
  content: "\f068";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asterisk:before {
  content: "\f069";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation-circle:before {
  content: "\f06a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gift:before {
  content: "\f06b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leaf:before {
  content: "\f06c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire:before {
  content: "\f06d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye:before {
  content: "\f06e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye-slash:before {
  content: "\f070";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-warning:before, .fa-exclamation-triangle:before {
  content: "\f071";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plane:before {
  content: "\f072";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar:before {
  content: "\f073";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-random:before {
  content: "\f074";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment:before {
  content: "\f075";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magnet:before {
  content: "\f076";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-up:before {
  content: "\f077";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-down:before {
  content: "\f078";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-retweet:before {
  content: "\f079";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-cart:before {
  content: "\f07a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder:before {
  content: "\f07b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open:before {
  content: "\f07c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-v:before {
  content: "\f07d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-h:before {
  content: "\f07e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bar-chart-o:before, .fa-bar-chart:before {
  content: "\f080";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter-square:before {
  content: "\f081";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-square:before {
  content: "\f082";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera-retro:before {
  content: "\f083";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-key:before {
  content: "\f084";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gears:before, .fa-cogs:before {
  content: "\f085";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments:before {
  content: "\f086";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-up:before {
  content: "\f087";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-down:before {
  content: "\f088";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half:before {
  content: "\f089";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart-o:before {
  content: "\f08a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-out:before {
  content: "\f08b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin-square:before {
  content: "\f08c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumb-tack:before {
  content: "\f08d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link:before {
  content: "\f08e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-in:before {
  content: "\f090";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trophy:before {
  content: "\f091";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-square:before {
  content: "\f092";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-upload:before {
  content: "\f093";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lemon-o:before {
  content: "\f094";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone:before {
  content: "\f095";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square-o:before {
  content: "\f096";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark-o:before {
  content: "\f097";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone-square:before {
  content: "\f098";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter:before {
  content: "\f099";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-f:before, .fa-facebook:before {
  content: "\f09a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github:before {
  content: "\f09b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock:before {
  content: "\f09c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card:before {
  content: "\f09d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-feed:before, .fa-rss:before {
  content: "\f09e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hdd-o:before {
  content: "\f0a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullhorn:before {
  content: "\f0a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell:before {
  content: "\f0f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-certificate:before {
  content: "\f0a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-right:before {
  content: "\f0a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-left:before {
  content: "\f0a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-up:before {
  content: "\f0a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-down:before {
  content: "\f0a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-left:before {
  content: "\f0a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-right:before {
  content: "\f0a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-up:before {
  content: "\f0aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-down:before {
  content: "\f0ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-globe:before {
  content: "\f0ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wrench:before {
  content: "\f0ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tasks:before {
  content: "\f0ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-filter:before {
  content: "\f0b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-briefcase:before {
  content: "\f0b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-alt:before {
  content: "\f0b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-group:before, .fa-users:before {
  content: "\f0c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chain:before, .fa-link:before {
  content: "\f0c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud:before {
  content: "\f0c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flask:before {
  content: "\f0c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cut:before, .fa-scissors:before {
  content: "\f0c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copy:before, .fa-files-o:before {
  content: "\f0c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paperclip:before {
  content: "\f0c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-save:before, .fa-floppy-o:before {
  content: "\f0c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square:before {
  content: "\f0c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-navicon:before, .fa-reorder:before, .fa-bars:before {
  content: "\f0c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ul:before {
  content: "\f0ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ol:before {
  content: "\f0cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-strikethrough:before {
  content: "\f0cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-underline:before {
  content: "\f0cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-table:before {
  content: "\f0ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magic:before {
  content: "\f0d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-truck:before {
  content: "\f0d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest:before {
  content: "\f0d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-square:before {
  content: "\f0d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-square:before {
  content: "\f0d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus:before {
  content: "\f0d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-money:before {
  content: "\f0d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-down:before {
  content: "\f0d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-up:before {
  content: "\f0d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-left:before {
  content: "\f0d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-right:before {
  content: "\f0da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-columns:before {
  content: "\f0db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unsorted:before, .fa-sort:before {
  content: "\f0dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-down:before, .fa-sort-desc:before {
  content: "\f0dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-up:before, .fa-sort-asc:before {
  content: "\f0de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope:before {
  content: "\f0e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin:before {
  content: "\f0e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-left:before, .fa-undo:before {
  content: "\f0e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-legal:before, .fa-gavel:before {
  content: "\f0e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashboard:before, .fa-tachometer:before {
  content: "\f0e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment-o:before {
  content: "\f0e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments-o:before {
  content: "\f0e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flash:before, .fa-bolt:before {
  content: "\f0e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sitemap:before {
  content: "\f0e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-umbrella:before {
  content: "\f0e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paste:before, .fa-clipboard:before {
  content: "\f0ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lightbulb-o:before {
  content: "\f0eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exchange:before {
  content: "\f0ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-download:before {
  content: "\f0ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-upload:before {
  content: "\f0ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-md:before {
  content: "\f0f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stethoscope:before {
  content: "\f0f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-suitcase:before {
  content: "\f0f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-o:before {
  content: "\f0a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-coffee:before {
  content: "\f0f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cutlery:before {
  content: "\f0f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text-o:before {
  content: "\f0f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building-o:before {
  content: "\f0f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hospital-o:before {
  content: "\f0f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ambulance:before {
  content: "\f0f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medkit:before {
  content: "\f0fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fighter-jet:before {
  content: "\f0fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-beer:before {
  content: "\f0fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-h-square:before {
  content: "\f0fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square:before {
  content: "\f0fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-left:before {
  content: "\f100";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-right:before {
  content: "\f101";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-up:before {
  content: "\f102";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-down:before {
  content: "\f103";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-left:before {
  content: "\f104";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-right:before {
  content: "\f105";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-up:before {
  content: "\f106";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-down:before {
  content: "\f107";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-desktop:before {
  content: "\f108";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-laptop:before {
  content: "\f109";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tablet:before {
  content: "\f10a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mobile-phone:before, .fa-mobile:before {
  content: "\f10b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o:before {
  content: "\f10c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-left:before {
  content: "\f10d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-right:before {
  content: "\f10e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spinner:before {
  content: "\f110";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle:before {
  content: "\f111";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply:before, .fa-reply:before {
  content: "\f112";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-alt:before {
  content: "\f113";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-o:before {
  content: "\f114";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open-o:before {
  content: "\f115";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-smile-o:before {
  content: "\f118";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-frown-o:before {
  content: "\f119";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meh-o:before {
  content: "\f11a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gamepad:before {
  content: "\f11b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-keyboard-o:before {
  content: "\f11c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-o:before {
  content: "\f11d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-checkered:before {
  content: "\f11e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-terminal:before {
  content: "\f120";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code:before {
  content: "\f121";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply-all:before, .fa-reply-all:before {
  content: "\f122";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before {
  content: "\f123";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-location-arrow:before {
  content: "\f124";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crop:before {
  content: "\f125";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code-fork:before {
  content: "\f126";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlink:before, .fa-chain-broken:before {
  content: "\f127";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question:before {
  content: "\f128";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info:before {
  content: "\f129";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation:before {
  content: "\f12a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superscript:before {
  content: "\f12b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subscript:before {
  content: "\f12c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eraser:before {
  content: "\f12d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-puzzle-piece:before {
  content: "\f12e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone:before {
  content: "\f130";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone-slash:before {
  content: "\f131";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shield:before {
  content: "\f132";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-o:before {
  content: "\f133";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire-extinguisher:before {
  content: "\f134";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rocket:before {
  content: "\f135";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-maxcdn:before {
  content: "\f136";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-left:before {
  content: "\f137";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-right:before {
  content: "\f138";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-up:before {
  content: "\f139";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-down:before {
  content: "\f13a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-html5:before {
  content: "\f13b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-css3:before {
  content: "\f13c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-anchor:before {
  content: "\f13d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock-alt:before {
  content: "\f13e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullseye:before {
  content: "\f140";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-h:before {
  content: "\f141";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-v:before {
  content: "\f142";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rss-square:before {
  content: "\f143";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle:before {
  content: "\f144";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ticket:before {
  content: "\f145";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square:before {
  content: "\f146";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square-o:before {
  content: "\f147";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-up:before {
  content: "\f148";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-down:before {
  content: "\f149";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square:before {
  content: "\f14a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil-square:before {
  content: "\f14b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link-square:before {
  content: "\f14c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square:before {
  content: "\f14d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compass:before {
  content: "\f14e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-down:before, .fa-caret-square-o-down:before {
  content: "\f150";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-up:before, .fa-caret-square-o-up:before {
  content: "\f151";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-right:before, .fa-caret-square-o-right:before {
  content: "\f152";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-euro:before, .fa-eur:before {
  content: "\f153";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gbp:before {
  content: "\f154";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dollar:before, .fa-usd:before {
  content: "\f155";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rupee:before, .fa-inr:before {
  content: "\f156";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before {
  content: "\f157";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ruble:before, .fa-rouble:before, .fa-rub:before {
  content: "\f158";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-won:before, .fa-krw:before {
  content: "\f159";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitcoin:before, .fa-btc:before {
  content: "\f15a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file:before {
  content: "\f15b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text:before {
  content: "\f15c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-asc:before {
  content: "\f15d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-desc:before {
  content: "\f15e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-asc:before {
  content: "\f160";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-desc:before {
  content: "\f161";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-asc:before {
  content: "\f162";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-desc:before {
  content: "\f163";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-up:before {
  content: "\f164";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-down:before {
  content: "\f165";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-square:before {
  content: "\f166";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube:before {
  content: "\f167";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing:before {
  content: "\f168";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing-square:before {
  content: "\f169";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-play:before {
  content: "\f16a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dropbox:before {
  content: "\f16b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-overflow:before {
  content: "\f16c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-instagram:before {
  content: "\f16d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flickr:before {
  content: "\f16e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adn:before {
  content: "\f170";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket:before {
  content: "\f171";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket-square:before {
  content: "\f172";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr:before {
  content: "\f173";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr-square:before {
  content: "\f174";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-down:before {
  content: "\f175";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-up:before {
  content: "\f176";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-left:before {
  content: "\f177";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-right:before {
  content: "\f178";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-apple:before {
  content: "\f179";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-windows:before {
  content: "\f17a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-android:before {
  content: "\f17b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linux:before {
  content: "\f17c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dribbble:before {
  content: "\f17d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skype:before {
  content: "\f17e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-foursquare:before {
  content: "\f180";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trello:before {
  content: "\f181";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-female:before {
  content: "\f182";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-male:before {
  content: "\f183";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gittip:before, .fa-gratipay:before {
  content: "\f184";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sun-o:before {
  content: "\f185";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-moon-o:before {
  content: "\f186";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-archive:before {
  content: "\f187";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bug:before {
  content: "\f188";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vk:before {
  content: "\f189";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-weibo:before {
  content: "\f18a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-renren:before {
  content: "\f18b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pagelines:before {
  content: "\f18c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-exchange:before {
  content: "\f18d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-right:before {
  content: "\f18e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-left:before {
  content: "\f190";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-left:before, .fa-caret-square-o-left:before {
  content: "\f191";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dot-circle-o:before {
  content: "\f192";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair:before {
  content: "\f193";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo-square:before {
  content: "\f194";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-turkish-lira:before, .fa-try:before {
  content: "\f195";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square-o:before {
  content: "\f196";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-space-shuttle:before {
  content: "\f197";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slack:before {
  content: "\f198";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-square:before {
  content: "\f199";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wordpress:before {
  content: "\f19a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-openid:before {
  content: "\f19b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-institution:before, .fa-bank:before, .fa-university:before {
  content: "\f19c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mortar-board:before, .fa-graduation-cap:before {
  content: "\f19d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yahoo:before {
  content: "\f19e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google:before {
  content: "\f1a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit:before {
  content: "\f1a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-square:before {
  content: "\f1a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon-circle:before {
  content: "\f1a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon:before {
  content: "\f1a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-delicious:before {
  content: "\f1a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-digg:before {
  content: "\f1a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-pp:before {
  content: "\f1a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-alt:before {
  content: "\f1a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drupal:before {
  content: "\f1a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-joomla:before {
  content: "\f1aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-language:before {
  content: "\f1ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fax:before {
  content: "\f1ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building:before {
  content: "\f1ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-child:before {
  content: "\f1ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paw:before {
  content: "\f1b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spoon:before {
  content: "\f1b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cube:before {
  content: "\f1b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cubes:before {
  content: "\f1b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance:before {
  content: "\f1b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance-square:before {
  content: "\f1b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam:before {
  content: "\f1b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam-square:before {
  content: "\f1b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-recycle:before {
  content: "\f1b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-automobile:before, .fa-car:before {
  content: "\f1b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cab:before, .fa-taxi:before {
  content: "\f1ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tree:before {
  content: "\f1bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spotify:before {
  content: "\f1bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deviantart:before {
  content: "\f1bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soundcloud:before {
  content: "\f1be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-database:before {
  content: "\f1c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-pdf-o:before {
  content: "\f1c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-word-o:before {
  content: "\f1c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-excel-o:before {
  content: "\f1c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-powerpoint-o:before {
  content: "\f1c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before {
  content: "\f1c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-zip-o:before, .fa-file-archive-o:before {
  content: "\f1c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-sound-o:before, .fa-file-audio-o:before {
  content: "\f1c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-movie-o:before, .fa-file-video-o:before {
  content: "\f1c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-code-o:before {
  content: "\f1c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vine:before {
  content: "\f1ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codepen:before {
  content: "\f1cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-jsfiddle:before {
  content: "\f1cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before {
  content: "\f1cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o-notch:before {
  content: "\f1ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ra:before, .fa-resistance:before, .fa-rebel:before {
  content: "\f1d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ge:before, .fa-empire:before {
  content: "\f1d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git-square:before {
  content: "\f1d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git:before {
  content: "\f1d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before {
  content: "\f1d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tencent-weibo:before {
  content: "\f1d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qq:before {
  content: "\f1d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wechat:before, .fa-weixin:before {
  content: "\f1d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send:before, .fa-paper-plane:before {
  content: "\f1d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send-o:before, .fa-paper-plane-o:before {
  content: "\f1d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-history:before {
  content: "\f1da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-thin:before {
  content: "\f1db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-header:before {
  content: "\f1dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paragraph:before {
  content: "\f1dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sliders:before {
  content: "\f1de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt:before {
  content: "\f1e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt-square:before {
  content: "\f1e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bomb:before {
  content: "\f1e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soccer-ball-o:before, .fa-futbol-o:before {
  content: "\f1e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tty:before {
  content: "\f1e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-binoculars:before {
  content: "\f1e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plug:before {
  content: "\f1e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slideshare:before {
  content: "\f1e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitch:before {
  content: "\f1e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yelp:before {
  content: "\f1e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-newspaper-o:before {
  content: "\f1ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wifi:before {
  content: "\f1eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calculator:before {
  content: "\f1ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paypal:before {
  content: "\f1ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-wallet:before {
  content: "\f1ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-visa:before {
  content: "\f1f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-mastercard:before {
  content: "\f1f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-discover:before {
  content: "\f1f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-amex:before {
  content: "\f1f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-paypal:before {
  content: "\f1f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-stripe:before {
  content: "\f1f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash:before {
  content: "\f1f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash-o:before {
  content: "\f1f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash:before {
  content: "\f1f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copyright:before {
  content: "\f1f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-at:before {
  content: "\f1fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eyedropper:before {
  content: "\f1fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paint-brush:before {
  content: "\f1fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-birthday-cake:before {
  content: "\f1fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-area-chart:before {
  content: "\f1fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pie-chart:before {
  content: "\f200";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-line-chart:before {
  content: "\f201";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm:before {
  content: "\f202";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm-square:before {
  content: "\f203";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-off:before {
  content: "\f204";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-on:before {
  content: "\f205";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bicycle:before {
  content: "\f206";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bus:before {
  content: "\f207";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ioxhost:before {
  content: "\f208";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angellist:before {
  content: "\f209";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc:before {
  content: "\f20a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shekel:before, .fa-sheqel:before, .fa-ils:before {
  content: "\f20b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meanpath:before {
  content: "\f20c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-buysellads:before {
  content: "\f20d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-connectdevelop:before {
  content: "\f20e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashcube:before {
  content: "\f210";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forumbee:before {
  content: "\f211";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leanpub:before {
  content: "\f212";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sellsy:before {
  content: "\f213";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shirtsinbulk:before {
  content: "\f214";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-simplybuilt:before {
  content: "\f215";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skyatlas:before {
  content: "\f216";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-plus:before {
  content: "\f217";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-arrow-down:before {
  content: "\f218";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-diamond:before {
  content: "\f219";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ship:before {
  content: "\f21a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-secret:before {
  content: "\f21b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-motorcycle:before {
  content: "\f21c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-street-view:before {
  content: "\f21d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heartbeat:before {
  content: "\f21e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus:before {
  content: "\f221";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars:before {
  content: "\f222";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mercury:before {
  content: "\f223";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-intersex:before, .fa-transgender:before {
  content: "\f224";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-transgender-alt:before {
  content: "\f225";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-double:before {
  content: "\f226";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-double:before {
  content: "\f227";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-mars:before {
  content: "\f228";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke:before {
  content: "\f229";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-v:before {
  content: "\f22a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-h:before {
  content: "\f22b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-neuter:before {
  content: "\f22c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-genderless:before {
  content: "\f22d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-official:before {
  content: "\f230";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-p:before {
  content: "\f231";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-whatsapp:before {
  content: "\f232";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-server:before {
  content: "\f233";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-plus:before {
  content: "\f234";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-times:before {
  content: "\f235";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hotel:before, .fa-bed:before {
  content: "\f236";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viacoin:before {
  content: "\f237";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-train:before {
  content: "\f238";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subway:before {
  content: "\f239";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medium:before {
  content: "\f23a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yc:before, .fa-y-combinator:before {
  content: "\f23b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-optin-monster:before {
  content: "\f23c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opencart:before {
  content: "\f23d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expeditedssl:before {
  content: "\f23e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before {
  content: "\f240";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-3:before, .fa-battery-three-quarters:before {
  content: "\f241";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-2:before, .fa-battery-half:before {
  content: "\f242";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-1:before, .fa-battery-quarter:before {
  content: "\f243";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-0:before, .fa-battery-empty:before {
  content: "\f244";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mouse-pointer:before {
  content: "\f245";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-i-cursor:before {
  content: "\f246";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-group:before {
  content: "\f247";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-ungroup:before {
  content: "\f248";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note:before {
  content: "\f249";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note-o:before {
  content: "\f24a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-jcb:before {
  content: "\f24b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-diners-club:before {
  content: "\f24c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clone:before {
  content: "\f24d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-balance-scale:before {
  content: "\f24e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-o:before {
  content: "\f250";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-1:before, .fa-hourglass-start:before {
  content: "\f251";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-2:before, .fa-hourglass-half:before {
  content: "\f252";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-3:before, .fa-hourglass-end:before {
  content: "\f253";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass:before {
  content: "\f254";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-grab-o:before, .fa-hand-rock-o:before {
  content: "\f255";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-stop-o:before, .fa-hand-paper-o:before {
  content: "\f256";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-scissors-o:before {
  content: "\f257";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-lizard-o:before {
  content: "\f258";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-spock-o:before {
  content: "\f259";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-pointer-o:before {
  content: "\f25a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-peace-o:before {
  content: "\f25b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trademark:before {
  content: "\f25c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-registered:before {
  content: "\f25d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-creative-commons:before {
  content: "\f25e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg:before {
  content: "\f260";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg-circle:before {
  content: "\f261";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tripadvisor:before {
  content: "\f262";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki:before {
  content: "\f263";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki-square:before {
  content: "\f264";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-get-pocket:before {
  content: "\f265";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wikipedia-w:before {
  content: "\f266";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-safari:before {
  content: "\f267";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chrome:before {
  content: "\f268";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-firefox:before {
  content: "\f269";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opera:before {
  content: "\f26a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-internet-explorer:before {
  content: "\f26b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tv:before, .fa-television:before {
  content: "\f26c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-contao:before {
  content: "\f26d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-500px:before {
  content: "\f26e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-amazon:before {
  content: "\f270";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-plus-o:before {
  content: "\f271";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-minus-o:before {
  content: "\f272";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-times-o:before {
  content: "\f273";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-check-o:before {
  content: "\f274";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-industry:before {
  content: "\f275";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-pin:before {
  content: "\f276";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-signs:before {
  content: "\f277";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-o:before {
  content: "\f278";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map:before {
  content: "\f279";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting:before {
  content: "\f27a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting-o:before {
  content: "\f27b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-houzz:before {
  content: "\f27c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo:before {
  content: "\f27d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-black-tie:before {
  content: "\f27e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fonticons:before {
  content: "\f280";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-alien:before {
  content: "\f281";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edge:before {
  content: "\f282";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card-alt:before {
  content: "\f283";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codiepie:before {
  content: "\f284";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-modx:before {
  content: "\f285";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fort-awesome:before {
  content: "\f286";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-usb:before {
  content: "\f287";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-product-hunt:before {
  content: "\f288";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mixcloud:before {
  content: "\f289";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-scribd:before {
  content: "\f28a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle:before {
  content: "\f28b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle-o:before {
  content: "\f28c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle:before {
  content: "\f28d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle-o:before {
  content: "\f28e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-bag:before {
  content: "\f290";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-basket:before {
  content: "\f291";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hashtag:before {
  content: "\f292";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth:before {
  content: "\f293";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth-b:before {
  content: "\f294";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-percent:before {
  content: "\f295";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gitlab:before {
  content: "\f296";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpbeginner:before {
  content: "\f297";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpforms:before {
  content: "\f298";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envira:before {
  content: "\f299";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-universal-access:before {
  content: "\f29a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair-alt:before {
  content: "\f29b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle-o:before {
  content: "\f29c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-blind:before {
  content: "\f29d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-audio-description:before {
  content: "\f29e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-control-phone:before {
  content: "\f2a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-braille:before {
  content: "\f2a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-assistive-listening-systems:before {
  content: "\f2a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before {
  content: "\f2a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide:before {
  content: "\f2a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide-g:before {
  content: "\f2a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signing:before, .fa-sign-language:before {
  content: "\f2a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-low-vision:before {
  content: "\f2a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo:before {
  content: "\f2a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo-square:before {
  content: "\f2aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat:before {
  content: "\f2ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-ghost:before {
  content: "\f2ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-square:before {
  content: "\f2ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper:before {
  content: "\f2ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-first-order:before {
  content: "\f2b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yoast:before {
  content: "\f2b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-themeisle:before {
  content: "\f2b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-circle:before, .fa-google-plus-official:before {
  content: "\f2b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fa:before, .fa-font-awesome:before {
  content: "\f2b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-handshake-o:before {
  content: "\f2b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open:before {
  content: "\f2b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open-o:before {
  content: "\f2b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linode:before {
  content: "\f2b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book:before {
  content: "\f2b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book-o:before {
  content: "\f2ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard:before, .fa-address-card:before {
  content: "\f2bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard-o:before, .fa-address-card-o:before {
  content: "\f2bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle:before {
  content: "\f2bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle-o:before {
  content: "\f2be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-o:before {
  content: "\f2c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-id-badge:before {
  content: "\f2c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license:before, .fa-id-card:before {
  content: "\f2c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license-o:before, .fa-id-card-o:before {
  content: "\f2c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quora:before {
  content: "\f2c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-free-code-camp:before {
  content: "\f2c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-telegram:before {
  content: "\f2c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before {
  content: "\f2c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-3:before, .fa-thermometer-three-quarters:before {
  content: "\f2c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-2:before, .fa-thermometer-half:before {
  content: "\f2c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-1:before, .fa-thermometer-quarter:before {
  content: "\f2ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-0:before, .fa-thermometer-empty:before {
  content: "\f2cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shower:before {
  content: "\f2cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bathtub:before, .fa-s15:before, .fa-bath:before {
  content: "\f2cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-podcast:before {
  content: "\f2ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-maximize:before {
  content: "\f2d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-minimize:before {
  content: "\f2d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-restore:before {
  content: "\f2d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle:before, .fa-window-close:before {
  content: "\f2d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle-o:before, .fa-window-close-o:before {
  content: "\f2d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bandcamp:before {
  content: "\f2d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-grav:before {
  content: "\f2d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-etsy:before {
  content: "\f2d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-imdb:before {
  content: "\f2d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ravelry:before {
  content: "\f2d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eercast:before {
  content: "\f2da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microchip:before {
  content: "\f2db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snowflake-o:before {
  content: "\f2dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superpowers:before {
  content: "\f2dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpexplorer:before {
  content: "\f2de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meetup:before {
  content: "\f2e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

/*============ Booking Widget ============*/
/* line 2, ../sass/_booking_styles.scss */
.boking_widget_inline .booking_form {
  width: auto;
}

/* line 6, ../sass/_booking_styles.scss */
.boking_widget_inline .stay_selection {
  margin-left: 22px;
}

/* line 10, ../sass/_booking_styles.scss */
div#wrapper_booking {
  position: absolute;
  margin: 0 auto;
  left: 0px;
  right: 0px;
  z-index: 23;
  bottom: 60px;
}

/* line 19, ../sass/_booking_styles.scss */
.boking_widget_inline .booking_form {
  background-color: inherit !important;
}

/* line 23, ../sass/_booking_styles.scss */
.boking_widget_inline {
  background-color: rgba(0, 0, 0, 0.75);
  padding: 1px 12px 17px;
  width: 1140px !important;
  box-sizing: border-box;
  height: auto;
}

/* line 31, ../sass/_booking_styles.scss */
.boking_widget_inline .selectric, .boking_widget_inline .date_box {
  background: white;
  border-radius: 0px;
}

/* line 36, ../sass/_booking_styles.scss */
.date_box .date_year, .selectric .label {
  color: gray;
}

/* line 40, ../sass/_booking_styles.scss */
.room_selector .label {
  color: #cdb480;
}

/* line 44, ../sass/_booking_styles.scss */
.boking_widget_inline .wrapper_booking_button {
  display: table;
}
/* line 47, ../sass/_booking_styles.scss */
.boking_widget_inline .wrapper_booking_button button {
  font-family: 'Roboto Slab';
  border-radius: 0px;
  font-size: 21px;
  width: 220px !important;
  background: #cdb480 url(/static_1/images/booking/flecha_motor_der.png) no-repeat;
  background-position-x: 250px;
  -ms-background-position-y: center;
  background-position-y: center;
  font-weight: 300;
  vertical-align: middle;
  margin-top: 15px !important;
  margin-bottom: 4px;
  width: 290px !important;
  line-height: 0;
}

/* line 65, ../sass/_booking_styles.scss */
.submit_button {
  cursor: pointer;
}

/* line 69, ../sass/_booking_styles.scss */
.boking_widget_inline .stay_selection .entry_date_wrapper label, .boking_widget_inline .stay_selection .departure_date_wrapper label, .boking_widget_inline .stay_selection .rooms_number_wrapper label {
  color: white;
  font-weight: 500;
}

/* line 74, ../sass/_booking_styles.scss */
.boking_widget_inline .promocode_input {
  border-radius: 0px;
  position: absolute;
  margin-top: 0px;
  top: 49px;
  left: 15px;
  box-shadow: 2px 2px 3px #666666;
  background: white;
  display: none;
  color: #cdb480;
}
/* line 85, ../sass/_booking_styles.scss */
.boking_widget_inline .promocode_input::-webkit-input-placeholder {
  color: #cdb480;
}
/* line 88, ../sass/_booking_styles.scss */
.boking_widget_inline .promocode_input:-moz-placeholder {
  color: #cdb480;
  opacity: 1;
}
/* line 92, ../sass/_booking_styles.scss */
.boking_widget_inline .promocode_input::-moz-placeholder {
  color: #cdb480;
  opacity: 1;
}
/* line 96, ../sass/_booking_styles.scss */
.boking_widget_inline .promocode_input:-ms-input-placeholder {
  color: #cdb480;
}

/* line 101, ../sass/_booking_styles.scss */
.boking_widget_inline .stay_selection .entry_date_wrapper label, .boking_widget_inline .stay_selection .departure_date_wrapper label, .boking_widget_inline .stay_selection .rooms_number_wrapper label, .boking_widget_inline .room .room_title, .boking_widget_inline .room .room_title, .boking_widget_inline .room .adults_selector label, .boking_widget_inline .room .children_selector label, .boking_widget_inline .room .babies_selector label {
  font-weight: lighter;
  color: white;
  font-family: 'Roboto';
  font-size: 11px;
}

/* line 108, ../sass/_booking_styles.scss */
.promocode_text {
  text-align: center;
  float: left;
  cursor: pointer;
  margin-top: 26px;
  font-family: roboto slab;
  font-size: 11px;
  color: white;
  font-weight: 400;
  margin-right: 25px;
}
/* line 118, ../sass/_booking_styles.scss */
.promocode_text strong {
  font-weight: inherit;
}

/* line 123, ../sass/_booking_styles.scss */
.wrapper_booking_button .promocode_input {
  display: none;
}

/* line 127, ../sass/_booking_styles.scss */
#data {
  position: relative;
}
/* line 129, ../sass/_booking_styles.scss */
#data .promocode_text {
  margin-right: 0px;
}
/* line 132, ../sass/_booking_styles.scss */
#data .promocode_input {
  position: absolute;
  left: 107px;
  box-shadow: 2px 2px 5px !important;
  top: 15px;
}
/* line 139, ../sass/_booking_styles.scss */
#data .wrapper_booking_button button {
  font-size: 13px;
  margin-top: 15px;
  background: #d84c1d;
}
/* line 145, ../sass/_booking_styles.scss */
#data .date_box, #data .selectric {
  background: #f5f5f5;
}
/* line 149, ../sass/_booking_styles.scss */
#data .booking_form {
  background: #C7C7C7;
  width: 270px;
}
/* line 154, ../sass/_booking_styles.scss */
#data .room .adults_selector {
  margin-right: 8px;
}
/* line 158, ../sass/_booking_styles.scss */
#data .promocode_text {
  margin-left: 0;
}

/* line 163, ../sass/_booking_styles.scss */
.stay_selection .entry_date_wrapper, .stay_selection .departure_date_wrapper {
  margin-right: 10px;
}

/* line 167, ../sass/_booking_styles.scss */
.boking_widget_inline .room_list_wrapper {
  margin-left: 15px;
}

/* line 171, ../sass/_booking_styles.scss */
.room .room_title, .room .adults_selector {
  margin-right: 5px;
}

/* line 175, ../sass/_booking_styles.scss */
.room_title {
  padding-right: 7px;
}

/* line 179, ../sass/_booking_styles.scss */
.room .adults_selector {
  margin-right: 10px;
}

/* line 183, ../sass/_booking_styles.scss */
.boking_widget_inline .room_list_wrapper {
  margin-right: 10px;
}

/* line 187, ../sass/_booking_styles.scss */
.promocode_text {
  margin-right: 20px;
  margin-left: 30px;
  margin-top: 27px;
  font-size: 12px;
  font-weight: lighter;
}

/* line 196, ../sass/_booking_styles.scss */
.ui-state-default {
  border: 1px solid white !important;
}

/* line 200, ../sass/_booking_styles.scss */
.ui-datepicker-title {
  color: white !important;
}

/* line 204, ../sass/_booking_styles.scss */
.ui-widget-header {
  background: #cdb480 !important;
}

/* line 208, ../sass/_booking_styles.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #cdb480 !important;
  color: white;
}

/* line 3, ../../../../sass/plugins/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 8, ../../../../sass/plugins/_mixins.scss */
.center_xy_before:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 13, ../../../../sass/plugins/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 17, ../../../../sass/plugins/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 21, ../../../../sass/plugins/_mixins.scss */
.center_y_before:before {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 25, ../../../../sass/plugins/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 30, ../../../../sass/plugins/_mixins.scss */
.fs:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
}

/* line 110, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:before, .icon-xcross:after {
  content: '';
  width: 100%;
  height: 2px;
  background: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 124, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -moz-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 61, ../../../../sass/plugins/_mixins.scss */
.display_flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* line 1, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown {
  margin: 40px 20px 20px;
  text-align: center;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  bottom: 180px;
  left: 48% !important;
  color: white;
}
/* line 9, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown div {
  display: inline-block;
  font-size: 16px;
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 0;
  padding-bottom: 0;
  text-transform: uppercase;
}
/* line 20, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .days {
  font-weight: 600;
}
/* line 23, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .date {
  display: block;
  font-size: 4.5rem;
}
/* line 28, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .title_format {
  font-weight: 600;
}

@media screen and (max-width: 800px) {
  /* line 35, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown {
    display: inline-block;
    width: 380px;
    top: 330px;
    left: 45% !important;
  }
  /* line 40, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown div {
    font-size: 10px;
  }
  /* line 43, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown .date {
    font-size: 2.5rem;
  }
}
/* line 4, ../sass/_rooms.scss */
.rooms_wrapper {
  margin-bottom: 60px;
  margin-top: 30px;
}
/* line 8, ../sass/_rooms.scss */
.rooms_wrapper .room_element {
  height: auto;
  margin-bottom: 20px;
  width: 100%;
  border-top: 5px solid #cdb480;
  border-radius: 5px 5px 0 0;
  display: table;
}
/* line 16, ../sass/_rooms.scss */
.rooms_wrapper .room_element h3.room_title {
  font-size: 24px;
  color: #cdb480;
  margin-bottom: 12px;
}
/* line 21, ../sass/_rooms.scss */
.rooms_wrapper .room_element h3.room_title span.capacity {
  font-weight: lighter;
  font-size: 16px;
  text-transform: capitalize;
  vertical-align: top;
  margin-top: 6px;
  display: inline-block;
}
/* line 29, ../sass/_rooms.scss */
.rooms_wrapper .room_element h3.room_title span.capacity .capacity_image {
  vertical-align: middle;
  padding-bottom: 4px;
}
/* line 36, ../sass/_rooms.scss */
.rooms_wrapper .room_element .room_description {
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
  color: gray;
  padding-bottom: 11px;
  margin-bottom: 33px;
}
/* line 44, ../sass/_rooms.scss */
.rooms_wrapper .room_element .room_description .hide_me {
  display: none;
}
/* line 48, ../sass/_rooms.scss */
.rooms_wrapper .room_element .service_elements {
  border-bottom: 1px solid #CECECE;
}
/* line 51, ../sass/_rooms.scss */
.rooms_wrapper .room_element .service_elements li {
  display: inline-block;
  padding-right: 20px;
  font-size: 13px;
  color: #cdb480;
  font-weight: lighter;
}
/* line 58, ../sass/_rooms.scss */
.rooms_wrapper .room_element .service_elements li img {
  vertical-align: middle;
  margin-right: 5px;
  -webkit-filter: grayscale(100%);
  /* Safari 6.0 - 9.0 */
  filter: grayscale(100%);
}
/* line 66, ../sass/_rooms.scss */
.rooms_wrapper .room_element .exceded, .rooms_wrapper .room_element .room_picture {
  width: 32%;
  min-height: 278px;
  float: left;
  position: relative;
  overflow: hidden;
}
/* line 73, ../sass/_rooms.scss */
.rooms_wrapper .room_element .exceded li, .rooms_wrapper .room_element .room_picture li {
  position: relative;
  overflow: hidden;
}
/* line 78, ../sass/_rooms.scss */
.rooms_wrapper .room_element .exceded .slides li, .rooms_wrapper .room_element .room_picture .slides li {
  height: 278px;
}
/* line 82, ../sass/_rooms.scss */
.rooms_wrapper .room_element .exceded img.room_image, .rooms_wrapper .room_element .room_picture img.room_image {
  min-height: 100%;
  position: absolute;
  left: -100%;
  top: -100%;
  right: -100%;
  bottom: -100%;
  margin: auto;
}
/* line 92, ../sass/_rooms.scss */
.rooms_wrapper .room_element .exceded .plus_image, .rooms_wrapper .room_element .room_picture .plus_image {
  position: absolute;
  text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.6);
  left: 20px;
  top: 20px;
  z-index: 1;
  color: white;
  background-color: #cdb480;
  border: 1px solid #cdb480;
  padding: 7px;
  border-radius: 5px;
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}
/* line 110, ../sass/_rooms.scss */
.rooms_wrapper .room_element:nth-child(even) .exceded {
  float: right;
}
/* line 114, ../sass/_rooms.scss */
.rooms_wrapper .room_element .flex-nav-prev {
  position: absolute !important;
  left: 0;
  top: 0;
  bottom: 0;
  height: 45px;
  margin: auto;
}
/* line 123, ../sass/_rooms.scss */
.rooms_wrapper .room_element .flex-nav-next {
  position: absolute !important;
  right: 0;
  top: 0;
  bottom: 0;
  height: 45px;
  margin: auto;
}
/* line 131, ../sass/_rooms.scss */
.rooms_wrapper .room_element .flex-nav-prev a, .rooms_wrapper .room_element .flex-nav-next a {
  display: block;
  color: white;
  background-color: #cdb480;
  border: 1px solid #cdb480;
  padding: 5px 10px;
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}
/* line 143, ../sass/_rooms.scss */
.rooms_wrapper .room_element .flex-nav-prev a:hover, .rooms_wrapper .room_element .flex-nav-next a:hover, .rooms_wrapper .room_element .plus_image:hover {
  display: block;
  color: #cdb480;
  border: 1px solid #cdb480;
  background-color: rgba(0, 0, 0, 0.3);
}
/* line 150, ../sass/_rooms.scss */
.rooms_wrapper .room_element .room_description_wrapper, .rooms_wrapper .room_element .room_content {
  background: #F8F8F8;
  float: right;
  width: 68%;
  padding: 25px 40px;
  min-height: 278px;
  box-sizing: border-box;
  position: relative;
}
/* line 160, ../sass/_rooms.scss */
.rooms_wrapper .room_element .room_description_wrapper .room_links .button-promotion, .rooms_wrapper .room_element .room_content .room_links .button-promotion {
  color: black;
  text-decoration: none;
  text-transform: uppercase;
  padding: 12px;
  float: right;
  border: 2.5px solid #cdb480;
}
/* line 167, ../sass/_rooms.scss */
.rooms_wrapper .room_element .room_description_wrapper .room_links .button-promotion:hover, .rooms_wrapper .room_element .room_content .room_links .button-promotion:hover {
  color: white;
  background-color: #cdb480;
}
/* line 176, ../sass/_rooms.scss */
.rooms_wrapper .room_element .service_elements .divide_0, .rooms_wrapper .room_element .service_elements .divide_1, .rooms_wrapper .room_element .service_elements .divide_2 {
  width: 32%;
  display: inline-table;
}
/* line 180, ../sass/_rooms.scss */
.rooms_wrapper .room_element .service_elements .divide_0 li, .rooms_wrapper .room_element .service_elements .divide_1 li, .rooms_wrapper .room_element .service_elements .divide_2 li {
  font-size: 13px;
  color: #d84c1d;
  font-weight: lighter;
}
/* line 185, ../sass/_rooms.scss */
.rooms_wrapper .room_element .service_elements .divide_0 li img, .rooms_wrapper .room_element .service_elements .divide_1 li img, .rooms_wrapper .room_element .service_elements .divide_2 li img {
  vertical-align: middle;
  margin-right: 5px;
  -webkit-filter: grayscale(100%);
  /* Safari 6.0 - 9.0 */
  filter: grayscale(100%);
}
/* line 195, ../sass/_rooms.scss */
.rooms_wrapper .room_element .room_buttons_wrapper {
  position: absolute;
  top: 17px;
  right: 40px;
}
/* line 200, ../sass/_rooms.scss */
.rooms_wrapper .room_element .room_buttons_wrapper img {
  vertical-align: middle;
  width: auto;
  display: none;
  height: 37px;
}
/* line 207, ../sass/_rooms.scss */
.rooms_wrapper .room_element .room_buttons_wrapper .room_book {
  color: white;
  padding: 8px;
  background: #cdb480;
  width: 117px;
  box-sizing: border-box;
  height: 37px;
  display: inline-block;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  float: right;
}
/* line 222, ../sass/_rooms.scss */
.rooms_wrapper .room_element .room_types {
  position: absolute;
  top: 16px;
  right: 27%;
}
/* line 227, ../sass/_rooms.scss */
.rooms_wrapper .room_element .room_types a {
  text-decoration: none;
}

/* line 4, ../sass/_offers.scss */
.offers_wrapper {
  margin-top: 50px;
  padding: 50px 0;
}
/* line 8, ../sass/_offers.scss */
.offers_wrapper .offer_element {
  display: inline-block;
  width: calc(50% - 10px);
  height: 375px;
  position: relative;
  overflow: hidden;
  margin-right: 10px;
  text-align: center;
}
/* line 18, ../sass/_offers.scss */
.offers_wrapper .offer_element:hover:before {
  opacity: .6;
}
/* line 23, ../sass/_offers.scss */
.offers_wrapper .offer_element:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: black;
  opacity: .3;
  z-index: 2;
  -webkit-transition: opacity 0.4s;
  -moz-transition: opacity 0.4s;
  -ms-transition: opacity 0.4s;
  -o-transition: opacity 0.4s;
  transition: opacity 0.4s;
}
/* line 32, ../sass/_offers.scss */
.offers_wrapper .offer_element:nth-child(even) {
  margin-right: 0;
}
/* line 36, ../sass/_offers.scss */
.offers_wrapper .offer_element .offer_content {
  z-index: 3;
}
/* line 39, ../sass/_offers.scss */
.offers_wrapper .offer_element .offer_content .offer_block {
  color: white;
}
/* line 42, ../sass/_offers.scss */
.offers_wrapper .offer_element .offer_content .offer_block .offer_title {
  font-size: 18px;
  text-transform: uppercase;
  margin-bottom: 30px;
  font-weight: lighter;
}
/* line 49, ../sass/_offers.scss */
.offers_wrapper .offer_element .offer_content .offer_block .offer_description {
  font-size: 14px;
}
/* line 53, ../sass/_offers.scss */
.offers_wrapper .offer_element .offer_content .offer_block .offer_links {
  margin-top: 20px;
}
/* line 56, ../sass/_offers.scss */
.offers_wrapper .offer_element .offer_content .offer_block .offer_links > div {
  display: inline-block;
  width: 100%;
  float: left;
  clear: both;
}
/* line 62, ../sass/_offers.scss */
.offers_wrapper .offer_element .offer_content .offer_block .offer_links > div a {
  display: inline-block;
  width: 200px;
  font-weight: lighter;
  padding: 10px 15px;
  text-transform: uppercase;
}
/* line 70, ../sass/_offers.scss */
.offers_wrapper .offer_element .offer_content .offer_block .offer_links > div.offer_link {
  margin-bottom: 5px;
}
/* line 73, ../sass/_offers.scss */
.offers_wrapper .offer_element .offer_content .offer_block .offer_links > div.offer_link a {
  border: 1px solid white;
  color: white;
  -webkit-transition: all 0.4s;
  -moz-transition: all 0.4s;
  -ms-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
}
/* line 78, ../sass/_offers.scss */
.offers_wrapper .offer_element .offer_content .offer_block .offer_links > div.offer_link a:hover {
  background: white;
  color: #cdb480;
}
/* line 86, ../sass/_offers.scss */
.offers_wrapper .offer_element .offer_content .offer_block .offer_links > div.offer_booking a {
  color: white;
  background: #dbcaa5;
  -webkit-transition: background 0.4s;
  -moz-transition: background 0.4s;
  -ms-transition: background 0.4s;
  -o-transition: background 0.4s;
  transition: background 0.4s;
}
/* line 91, ../sass/_offers.scss */
.offers_wrapper .offer_element .offer_content .offer_block .offer_links > div.offer_booking a:hover {
  background: #cdb480;
}

/*==== General ===*/
/* line 2, ../sass/_template_specific.scss */
body {
  font-family: "Open Sans", sans-serif;
}

/* line 6, ../sass/_template_specific.scss */
.fancybox-inner, .fancybox-outer {
  overflow: visible !important;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/
/* line 12, ../sass/_template_specific.scss */
.ui-state-default {
  border: 1px solid white !important;
}

/* line 16, ../sass/_template_specific.scss */
.ui-datepicker-title {
  color: white !important;
}

/* line 20, ../sass/_template_specific.scss */
.ui-widget-header {
  background: #cdb480 !important;
}

/* line 24, ../sass/_template_specific.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #cdb480 !important;
  color: white;
}

/*============ Header ============*/
/* line 31, ../sass/_template_specific.scss */
header {
  width: 100%;
  height: auto;
  color: #cdb480;
  overflow: visible;
  background: white;
  min-height: 70px;
}
/* line 39, ../sass/_template_specific.scss */
header #wrapper-header {
  position: relative;
  z-index: 22;
  height: auto;
}
/* line 45, ../sass/_template_specific.scss */
header a {
  color: #cdb480;
}

/* line 51, ../sass/_template_specific.scss */
#logoDiv {
  margin-top: 0px;
  width: auto;
  float: left;
  height: auto;
  overflow: hidden;
  background: white;
  position: absolute;
}
/* line 60, ../sass/_template_specific.scss */
#logoDiv img {
  background: none;
  height: 100%;
  width: auto;
  padding: 38px 17px 0px 17px;
  margin: 0px 2px 2px 2px;
}

/* line 69, ../sass/_template_specific.scss */
.middle-header {
  margin-right: 0px !important;
  color: #626262;
  float: right;
  width: 860px;
}

/* line 76, ../sass/_template_specific.scss */
.top-row, .bottom-row {
  overflow: auto;
}

/* line 80, ../sass/_template_specific.scss */
.bottom-row {
  margin-top: 7px;
}

/* line 85, ../sass/_template_specific.scss */
.text_official .official {
  font-weight: 500;
  display: block;
  float: right;
  padding-top: 2px;
  font-size: 16px;
}

/* line 94, ../sass/_template_specific.scss */
span.official {
  color: #cdb480;
  text-transform: uppercase;
  font-weight: 300;
}

/* line 150, ../sass/_template_specific.scss */
#lang {
  float: right;
  text-transform: uppercase;
  margin-right: 0px;
  font-size: 14px;
  font-weight: 400;
  background: #cdb480;
  padding: 0 10px;
  color: white;
  max-width: 60px;
}
/* line 160, ../sass/_template_specific.scss */
#lang .lang_selected {
  text-transform: uppercase;
  color: white;
  letter-spacing: 2px;
  font-size: 14px;
  padding: 5px 20px 5px 20px;
  position: relative;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
}
/* line 171, ../sass/_template_specific.scss */
#lang .lang_selected:before {
  content: "\f041";
  font-family: "FontAwesome", sans-serif;
  font-size: 16px;
  color: #cdb480;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 5px;
}
/* line 180, ../sass/_template_specific.scss */
#lang .lang_selected:after {
  content: "\f107";
  font-family: "FontAwesome", sans-serif;
  font-size: 8px;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 10px;
}
/* line 188, ../sass/_template_specific.scss */
#lang .lang_options_wrapper {
  display: none;
  position: absolute;
  width: 80px;
  right: 0;
  z-index: 101;
  background-color: #cdb480;
}
/* line 195, ../sass/_template_specific.scss */
#lang .lang_options_wrapper a {
  display: block;
  width: 100%;
  box-sizing: border-box;
  text-align: center;
  padding: 10px;
  border-top: 1px solid rgba(216, 76, 29, 0.3);
  color: white;
  font-weight: 100;
  text-decoration: none;
}
/* line 205, ../sass/_template_specific.scss */
#lang .lang_options_wrapper a img {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

/* line 214, ../sass/_template_specific.scss */
.web-oficial.interior {
  width: 1140px;
  margin: 0 auto;
  padding: 0px;
  font-size: 13px;
  background: #cdb480;
}
/* line 221, ../sass/_template_specific.scss */
.web-oficial.interior img {
  width: 20px;
  height: 20px;
  padding-top: 3px;
}
/* line 227, ../sass/_template_specific.scss */
.web-oficial.interior .tick_wrapper {
  padding-top: 4px;
}
/* line 229, ../sass/_template_specific.scss */
.web-oficial.interior .tick_wrapper span {
  color: white !important;
}

/* line 236, ../sass/_template_specific.scss */
.tick_wrapper {
  float: left;
  font-family: 'Source Sans Pro', sans-serif;
}

/* line 241, ../sass/_template_specific.scss */
.tick_wrapper img {
  width: auto;
  height: 23px;
  padding-top: 4px;
}

/* line 247, ../sass/_template_specific.scss */
.tick_center {
  display: table-caption;
  margin: 0 auto;
}

/* line 252, ../sass/_template_specific.scss */
.en .web-oficial {
  width: 245px;
}

/* line 256, ../sass/_template_specific.scss */
#social {
  width: auto;
  margin-top: 9px;
}
/* line 260, ../sass/_template_specific.scss */
#social img {
  width: 32px;
  height: 32px;
}
/* line 265, ../sass/_template_specific.scss */
#social a {
  background: white;
  color: black;
  display: inline-block;
  text-decoration: none;
  font-size: 16px;
  border-radius: 50%;
  padding: 8px;
  width: 20px;
  height: 20px;
  text-align: center;
}
/* line 277, ../sass/_template_specific.scss */
#social a:hover {
  opacity: 0.8;
}

/* line 283, ../sass/_template_specific.scss */
#top-sections {
  margin-top: 10px;
  text-align: right;
  margin-right: 0px !important;
  width: 640px;
  padding-left: 100px;
  font-size: 14px;
}
/* line 291, ../sass/_template_specific.scss */
#top-sections a span {
  text-transform: uppercase;
  font-weight: bold;
  color: #d84c1d;
  margin-left: 10px;
}
/* line 298, ../sass/_template_specific.scss */
#top-sections a:hover {
  color: #cdb480;
  transition: color 0.5s;
}
/* line 303, ../sass/_template_specific.scss */
#top-sections a {
  color: #626262;
  text-decoration: none;
  transition: all 0.5s;
}

/* line 311, ../sass/_template_specific.scss */
#main_menu {
  background: #cdb480;
  height: 40px;
}

/* line 316, ../sass/_template_specific.scss */
#main-sections {
  width: 850px;
  float: right;
}

/* line 321, ../sass/_template_specific.scss */
#mainMenuDiv {
  font-size: 15px;
  z-index: 99;
  height: 40px;
  box-sizing: border-box;
}

/* line 328, ../sass/_template_specific.scss */
#mainMenuDiv a {
  padding: 9px 0px 0px;
  text-decoration: none;
  color: white;
  display: inline-block;
  font-size: 13px;
  text-transform: uppercase;
  border-bottom: 2px solid #cdb480;
  font-weight: 700;
}
/* line 338, ../sass/_template_specific.scss */
#mainMenuDiv a.button_promotion span {
  border-bottom: 2px solid white;
}
/* line 341, ../sass/_template_specific.scss */
#mainMenuDiv a:hover:not(.button_promotion) {
  font-weight: bold;
}

/* line 346, ../sass/_template_specific.scss */
#mainMenuDiv a.button_promotion:hover {
  opacity: 0.8;
}

/* line 350, ../sass/_template_specific.scss */
#section-active a {
  font-weight: 700;
}

/* line 354, ../sass/_template_specific.scss */
#main-sections-inner ul {
  display: none;
}

/* line 358, ../sass/_template_specific.scss */
#main-sections-inner div:hover > ul {
  display: block;
}

/* line 362, ../sass/_template_specific.scss */
#main-sections-inner div ul {
  position: absolute;
}

/* line 366, ../sass/_template_specific.scss */
#main-sections-inner li ul {
  position: absolute;
}

/* line 370, ../sass/_template_specific.scss */
#main-sections-inner div li {
  float: none;
  display: block;
}

/* line 375, ../sass/_template_specific.scss */
#main-sections-inner {
  height: 40px;
  text-align: justify;
  -ms-text-justify: distribute-all-lines;
  text-justify: distribute-all-lines;
}

/* line 382, ../sass/_template_specific.scss */
#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

/* line 389, ../sass/_template_specific.scss */
#main-sections-inner > div {
  display: inline-block;
}

/* line 393, ../sass/_template_specific.scss */
.main-section-div-wrapper {
  width: 118px;
  text-align: center;
  vertical-align: middle;
}
/* line 397, ../sass/_template_specific.scss */
.main-section-div-wrapper a {
  line-height: 23px;
  font-size: 15px;
}

/* line 404, ../sass/_template_specific.scss */
span.separator {
  margin-left: 6px;
  margin-right: 2px;
}

/* line 409, ../sass/_template_specific.scss */
a.separator {
  margin-left: 3px;
}

/* line 413, ../sass/_template_specific.scss */
.book_menu {
  padding: 10px 22px;
  color: white;
  font-weight: bolder;
  font-size: 17px;
}

/*=============== Revolution Slider ============*/
/* line 421, ../sass/_template_specific.scss */
.tp-bullets .bullet {
  background: white !important;
  border-radius: 19px;
  width: 15px !important;
  height: 15px !important;
  margin: 4px !important;
}
/* line 427, ../sass/_template_specific.scss */
.tp-bullets .bullet.selected {
  background: rgba(255, 255, 255, 0.6) !important;
}
/* line 431, ../sass/_template_specific.scss */
.tp-bullets .bullet:hover {
  background: rgba(255, 255, 255, 0.6) !important;
}

/* line 436, ../sass/_template_specific.scss */
#slider_container {
  position: relative;
}

/* line 440, ../sass/_template_specific.scss */
.see_more_slider {
  position: absolute;
  top: 50px;
  right: 20%;
  z-index: 23;
  width: 475px;
  cursor: pointer;
  width: 495px;
}
/* line 449, ../sass/_template_specific.scss */
.see_more_slider .plus_slider {
  float: left;
}
/* line 453, ../sass/_template_specific.scss */
.see_more_slider p.default_title {
  background: rgba(0, 0, 0, 0.75);
  color: white;
  padding: 14px 11px 8px 70px;
  height: 22px;
  font-weight: lighter;
}
/* line 461, ../sass/_template_specific.scss */
.see_more_slider p.hidden_text {
  background: black;
  color: white;
  padding: 25px;
  width: 475px;
  font-weight: lighter;
  display: none;
  box-sizing: border-box;
}

/*======== Normal Content =======*/
/* line 473, ../sass/_template_specific.scss */
.normal_content_wrapper {
  margin-top: 73px;
}
/* line 476, ../sass/_template_specific.scss */
.normal_content_wrapper .normal_title {
  color: #cdb480;
  text-align: center;
  font-size: 28px;
  font-family: "Roboto Slab", serif;
}
/* line 482, ../sass/_template_specific.scss */
.normal_content_wrapper .normal_title strong {
  font-weight: bolder;
}
/* line 487, ../sass/_template_specific.scss */
.normal_content_wrapper .separator {
  display: table;
  margin: 16px auto 11px;
  width: 120px;
  border-top: 5px solid #cdb480;
  padding-bottom: 30px;
}
/* line 495, ../sass/_template_specific.scss */
.normal_content_wrapper .normal_description {
  font-family: "Open Sans", sans-serif;
  font-size: 13px;
  display: table;
  color: #757575;
  margin: auto auto 75px;
}
/* line 502, ../sass/_template_specific.scss */
.normal_content_wrapper .normal_description strong {
  font-weight: bolder;
}
/* line 507, ../sass/_template_specific.scss */
.normal_content_wrapper .left_block {
  width: 47%;
  float: left;
  text-align: justify;
}
/* line 513, ../sass/_template_specific.scss */
.normal_content_wrapper .right_block {
  width: 47%;
  float: right;
  text-align: justify;
}

/* line 520, ../sass/_template_specific.scss */
.content_access {
  text-align: center;
  margin: 40px auto;
  font-size: 16px;
  font-family: 'Merriweather', serif;
  color: grey;
}
/* line 527, ../sass/_template_specific.scss */
.content_access .separatos {
  display: table;
  margin: 16px auto 11px;
  width: 120px;
  border-top: 5px solid #cdb480;
  padding-bottom: 30px;
}
/* line 534, ../sass/_template_specific.scss */
.content_access .section-title {
  text-align: center;
  font-size: 28px;
}
/* line 538, ../sass/_template_specific.scss */
.content_access .section-title strong {
  font-weight: bolder;
}

/*===== Bottom carousel ====*/
/* line 545, ../sass/_template_specific.scss */
.footer_banners {
  display: block;
  margin: 0 auto;
  width: 1140px;
}
/* line 549, ../sass/_template_specific.scss */
.footer_banners .carousel_bottom_wrapper {
  position: relative;
  overflow: hidden;
  height: 435px;
  margin-bottom: 40px;
}
/* line 555, ../sass/_template_specific.scss */
.footer_banners .carousel_bottom_wrapper .carousel_bottom_background {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 559, ../sass/_template_specific.scss */
.footer_banners .carousel_bottom_wrapper .slides {
  height: 435px;
}
/* line 563, ../sass/_template_specific.scss */
.footer_banners .carousel_bottom_wrapper .black_overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  background: rgba(0, 0, 0, 0.35);
  min-height: 500%;
}
/* line 573, ../sass/_template_specific.scss */
.footer_banners .carousel_bottom_wrapper .carousel_element_list {
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  text-align: center;
}
/* line 582, ../sass/_template_specific.scss */
.footer_banners .carousel_bottom_wrapper .carousel_element_list a {
  text-decoration: none;
  z-index: 2;
  position: relative;
}
/* line 588, ../sass/_template_specific.scss */
.footer_banners .carousel_bottom_wrapper .carousel_element_list h3.carousel_title {
  font-family: 'Oswald', sans-serif;
  font-size: 29px;
  color: white;
  font-weight: 100;
  margin-bottom: 38px;
}
/* line 596, ../sass/_template_specific.scss */
.footer_banners .carousel_bottom_wrapper .carousel_element_list .carousel_description {
  font-size: 16px;
  line-height: 30px;
  color: white;
  width: 75%;
  margin: 0 auto;
}
/* line 605, ../sass/_template_specific.scss */
.footer_banners .carousel_bottom_wrapper .flex-prev, .footer_banners .carousel_bottom_wrapper .flex-next {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 3;
}
/* line 616, ../sass/_template_specific.scss */
.footer_banners .carousel_bottom_wrapper .flex-prev {
  left: 40px;
  -webkit-transform: translateY(-50%) rotate(180deg);
  -moz-transform: translateY(-50%) rotate(180deg);
  -ms-transform: translateY(-50%) rotate(180deg);
  -o-transform: translateY(-50%) rotate(180deg);
  transform: translateY(-50%) rotate(180deg);
}
/* line 625, ../sass/_template_specific.scss */
.footer_banners .carousel_bottom_wrapper .flex-next {
  right: 40px;
}
/* line 629, ../sass/_template_specific.scss */
.footer_banners .carousel_bottom_wrapper .carousel_element_button {
  display: table;
  margin: auto;
  border: 1px solid white;
  color: white;
  padding: 15px;
  text-transform: uppercase;
  margin-top: 35px;
  font-family: 'Oswald', sans-serif;
  font-weight: lighter;
  font-size: 20px;
}
/* line 641, ../sass/_template_specific.scss */
.footer_banners .carousel_bottom_wrapper .carousel_element_button img.right_arrow {
  vertical-align: top;
  margin-top: 10px;
  margin-left: 10px;
}

/* line 650, ../sass/_template_specific.scss */
.maps_footer {
  display: table;
  width: 100%;
  padding-top: 38px;
}
/* line 655, ../sass/_template_specific.scss */
.maps_footer h3.map_title {
  padding-bottom: 33px;
  text-align: center;
  font-size: 30px;
  color: #cdb480;
  font-weight: lighter;
  box-shadow: 0 4px 5px #A2A2A2;
  z-index: 2;
  position: relative;
}
/* line 665, ../sass/_template_specific.scss */
.maps_footer h3.map_title strong {
  font-weight: 700;
}

/* line 671, ../sass/_template_specific.scss */
.map_content {
  height: 380px;
}

/*====== Banners 3 Tropical =====*/
/* line 677, ../sass/_template_specific.scss */
.bannerx3-wrapper-tropical {
  overflow: hidden;
}
/* line 680, ../sass/_template_specific.scss */
.bannerx3-wrapper-tropical .block-banner {
  float: left;
  width: 380px;
  position: relative;
}
/* line 689, ../sass/_template_specific.scss */
.bannerx3-wrapper-tropical .block-banner .overlay,
.bannerx3-wrapper-tropical .block-banner .border {
  width: 380px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
/* line 698, ../sass/_template_specific.scss */
.bannerx3-wrapper-tropical .block-banner .overlay {
  background: rgba(20, 20, 20, 0.4);
  transition: all 0.3s ease;
  opacity: 1;
}
/* line 703, ../sass/_template_specific.scss */
.bannerx3-wrapper-tropical .block-banner:hover .overlay {
  opacity: 0;
}
/* line 706, ../sass/_template_specific.scss */
.bannerx3-wrapper-tropical .block-banner .border {
  transition: all 0.3s ease;
}
/* line 709, ../sass/_template_specific.scss */
.bannerx3-wrapper-tropical .block-banner:hover .border {
  box-shadow: inset 0 0 0 20px #cdb480;
}
/* line 712, ../sass/_template_specific.scss */
.bannerx3-wrapper-tropical .block-banner img {
  width: 380px;
  vertical-align: bottom;
}
/* line 716, ../sass/_template_specific.scss */
.bannerx3-wrapper-tropical .block-banner .caption {
  position: absolute;
  display: table;
  width: 50%;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}
/* line 726, ../sass/_template_specific.scss */
.bannerx3-wrapper-tropical .block-banner h4 {
  color: white;
  font-size: 20px;
  font-weight: 300;
  text-transform: uppercase;
  text-align: center;
  padding: 20px 10px;
  box-sizing: border-box;
}
/* line 735, ../sass/_template_specific.scss */
.bannerx3-wrapper-tropical .block-banner span {
  display: block;
  width: 100px;
  border-top: 4px solid white;
  margin: auto;
}

/*====== Carousel bottom Tropical =====*/
/* line 745, ../sass/_template_specific.scss */
.mini_gallery_wrapper {
  margin: 40px 0px;
}
/* line 748, ../sass/_template_specific.scss */
.mini_gallery_wrapper h3 {
  padding-bottom: 33px;
  text-align: center;
  font-size: 30px;
  color: #cdb480;
  font-weight: lighter;
  box-shadow: 0 4px 5px #A2A2A2;
  z-index: 2;
  position: relative;
}

/* line 760, ../sass/_template_specific.scss */
.flexslider {
  height: auto;
  width: 100%;
  position: relative;
}
/* line 767, ../sass/_template_specific.scss */
.flexslider li .text-bannerx2 img {
  height: 384px;
  max-width: none;
}

/* line 775, ../sass/_template_specific.scss */
.wrapper_filt .gallery_1 .video-li {
  height: 300px !important;
  width: 400px !important;
}

/*====== Banners 3 Vialmoura =====*/
/* line 782, ../sass/_template_specific.scss */
.bannerx3-wrapper-vialmoura {
  overflow: hidden;
  margin-bottom: 30px;
}
/* line 786, ../sass/_template_specific.scss */
.bannerx3-wrapper-vialmoura .block-banner {
  float: left;
  width: 33.33%;
  position: relative;
}
/* line 795, ../sass/_template_specific.scss */
.bannerx3-wrapper-vialmoura .block-banner .overlay,
.bannerx3-wrapper-vialmoura .block-banner .border {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
/* line 804, ../sass/_template_specific.scss */
.bannerx3-wrapper-vialmoura .block-banner .overlay {
  background: rgba(20, 20, 20, 0.4);
  transition: all 0.3s ease;
  opacity: 1;
}
/* line 809, ../sass/_template_specific.scss */
.bannerx3-wrapper-vialmoura .block-banner:hover .overlay {
  opacity: 0;
}
/* line 812, ../sass/_template_specific.scss */
.bannerx3-wrapper-vialmoura .block-banner .border {
  transition: all 0.3s ease;
}
/* line 815, ../sass/_template_specific.scss */
.bannerx3-wrapper-vialmoura .block-banner:hover .border {
  box-shadow: inset 0 0 0 20px #dbae2a;
}
/* line 818, ../sass/_template_specific.scss */
.bannerx3-wrapper-vialmoura .block-banner img {
  width: 100%;
  vertical-align: bottom;
}
/* line 822, ../sass/_template_specific.scss */
.bannerx3-wrapper-vialmoura .block-banner .caption {
  position: absolute;
  display: table;
  width: 50%;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}
/* line 832, ../sass/_template_specific.scss */
.bannerx3-wrapper-vialmoura .block-banner h4 {
  color: white;
  font-size: 30px;
  font-weight: 300;
  text-transform: uppercase;
  text-align: center;
  padding: 20px 10px;
  box-sizing: border-box;
}
/* line 841, ../sass/_template_specific.scss */
.bannerx3-wrapper-vialmoura .block-banner span {
  display: block;
  width: 100px;
  border-top: 4px solid white;
  margin: auto;
}

/*===== Footer ====*/
/* line 850, ../sass/_template_specific.scss */
footer {
  background: black;
  color: white;
  padding-top: 50px;
}
/* line 855, ../sass/_template_specific.scss */
footer .footer_column {
  color: #6D6D6D;
  font-size: 12px;
}
/* line 859, ../sass/_template_specific.scss */
footer .footer_column .footer_column_title {
  font-size: 18px;
  font-family: 'Roboto Slab';
  margin-bottom: 5px;
}
/* line 865, ../sass/_template_specific.scss */
footer .footer_column .image_logo {
  float: left;
}
/* line 869, ../sass/_template_specific.scss */
footer .footer_column .footer_column_description {
  line-height: 23px;
  font-family: "Open Sans", sans-serif;
  font-size: 13px;
}
/* line 876, ../sass/_template_specific.scss */
footer #newsletter {
  margin-top: 20px;
}
/* line 879, ../sass/_template_specific.scss */
footer #newsletter #title_newsletter, footer #newsletter #form-newsletter #suscEmailLabel {
  display: none !important;
}
/* line 883, ../sass/_template_specific.scss */
footer #newsletter input#suscEmail {
  background: #a2a2a2;
  border: 0;
  border-radius: 2px;
  width: 180px;
  height: 22px;
  float: left;
  padding-left: 15px;
  box-sizing: border-box;
}
/* line 894, ../sass/_template_specific.scss */
footer #newsletter button#newsletter-button {
  background: #cdb480;
  color: white;
  border: 0;
  margin-left: 9px;
  font-size: 12px;
  text-transform: uppercase;
  border-radius: 2px;
  padding: 4px 19px 5px;
  cursor: pointer;
}
/* line 905, ../sass/_template_specific.scss */
footer #newsletter button#newsletter-button:hover {
  opacity: 0.8;
}
/* line 910, ../sass/_template_specific.scss */
footer #newsletter .newsletter_checkbox a {
  color: #6D6D6D;
}

/* line 917, ../sass/_template_specific.scss */
.wrapper_footer_columns {
  margin-bottom: 55px;
}

/* line 921, ../sass/_template_specific.scss */
.social_likes {
  text-align: center;
  font-size: 11px;
}
/* line 925, ../sass/_template_specific.scss */
.social_likes div#facebook_like {
  width: 49.5%;
  float: left;
  text-align: right;
  margin-top: 2px;
}
/* line 932, ../sass/_template_specific.scss */
.social_likes div#google_plus_one {
  width: 49%;
  float: right;
  text-align: left;
}
/* line 938, ../sass/_template_specific.scss */
.social_likes span.copyright_text {
  display: block;
  margin-bottom: 6px;
  font-family: "Open Sans", sans-serif;
  color: #6D6D6D;
}

/* line 946, ../sass/_template_specific.scss */
.footer_links_business {
  color: #6D6D6D;
  text-align: center;
  font-size: 12px;
  margin-top: 4px;
}
/* line 952, ../sass/_template_specific.scss */
.footer_links_business a {
  color: #6D6D6D;
  text-decoration: none;
  font-family: "Open Sans", sans-serif;
}

/* line 959, ../sass/_template_specific.scss */
div#div-txt-copyright {
  text-align: center;
  font-size: 12px;
  color: #6D6D6D;
  font-family: "Open Sans", sans-serif;
}

/* line 966, ../sass/_template_specific.scss */
.full-copyright {
  padding-bottom: 15px;
}

/*============ Servicios =============*/
/* line 971, ../sass/_template_specific.scss */
.service-list {
  width: 750px;
  overflow: hidden;
  margin: auto;
}
/* line 976, ../sass/_template_specific.scss */
.service-list .left,
.service-list .right {
  width: 350px;
  float: left;
}
/* line 981, ../sass/_template_specific.scss */
.service-list .left {
  margin-right: 50px;
}

/* line 986, ../sass/_template_specific.scss */
.strong-list {
  margin-bottom: 5px;
  margin-top: 20px;
  font-weight: bold;
  font-size: 14px;
}

/*============ Golf =============*/
/* line 995, ../sass/_template_specific.scss */
.golf-blocks-wrapper {
  margin-bottom: 40px;
  font-family: "Open Sans", sans-serif;
  font-size: 14px;
}

/* line 1002, ../sass/_template_specific.scss */
.golf-block {
  overflow: hidden;
  clear: both;
  margin-bottom: 30px;
}

/* line 1008, ../sass/_template_specific.scss */
.golf-block .image-block {
  float: left;
  width: 300px;
}

/* line 1015, ../sass/_template_specific.scss */
.golf-block .golf-description h3 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

/*============ Habitaciones =============*/
/* line 1025, ../sass/_template_specific.scss */
.room_wrapper {
  margin-top: 75px;
  margin-bottom: 75px;
  display: table;
}

/* line 1031, ../sass/_template_specific.scss */
div#description-main-section.content_rooms {
  box-sizing: border-box;
  margin-top: 0px;
}

/* line 1036, ../sass/_template_specific.scss */
section#top_content {
  padding-top: 200px;
}

/* line 1040, ../sass/_template_specific.scss */
.rooms-description {
  background: rgba(245, 245, 245, 0.8);
  padding: 20px 20px 30px;
  color: grey;
  position: relative;
}
/* line 1046, ../sass/_template_specific.scss */
.rooms-description .destino {
  font-weight: 700;
  font-family: yanone, sans-serif;
}
/* line 1051, ../sass/_template_specific.scss */
.rooms-description .title-module {
  font-size: 23px;
  color: #cdb480;
  margin-bottom: 20px;
}

/* line 1059, ../sass/_template_specific.scss */
.description-rooms {
  font-weight: lighter;
  font-size: 14px;
  font-family: "Open Sans", sans-serif;
}

/* line 1065, ../sass/_template_specific.scss */
h4.title-module {
  font-size: 23px;
  color: #C5AD81;
  margin-top: 10px;
  margin-bottom: 5px;
}

/* line 1072, ../sass/_template_specific.scss */
.rooms {
  margin-bottom: 25px;
  position: relative;
}

/* line 1077, ../sass/_template_specific.scss */
.blockleft {
  margin-left: 0px;
}

/* line 1081, ../sass/_template_specific.scss */
.blockright {
  margin-right: 0px;
  margin-left: 30px;
}

/* line 1086, ../sass/_template_specific.scss */
.sub-description-rooms {
  margin: 10px 0 20px;
  font-weight: bold;
  font-size: 14px;
}

/* line 1092, ../sass/_template_specific.scss */
span.btn-corporate {
  text-transform: uppercase;
  color: white !important;
  background-color: #d84c1d;
  margin-right: 20px;
  margin-top: 20px;
}

/* line 1100, ../sass/_template_specific.scss */
span.btn-corporate {
  padding: 5px 12px;
  font-family: "Open Sans", sans-serif;
}

/* line 1105, ../sass/_template_specific.scss */
.btn-flecha {
  background-color: #cdb480;
  height: 22px;
  padding: 5px 10px;
  position: absolute;
  width: 10px;
  top: 16px;
  right: 15px;
  text-align: center;
}

/* line 1117, ../sass/_template_specific.scss */
.rooms .room_img {
  width: 100%;
}
/* line 1121, ../sass/_template_specific.scss */
.rooms .ico_cam_room {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 20;
}
/* line 1127, ../sass/_template_specific.scss */
.rooms span.btn-corporate {
  right: 90px;
}
/* line 1130, ../sass/_template_specific.scss */
.rooms .btn-flecha {
  width: 70px;
  cursor: pointer;
  font-family: "Open Sans", sans-serif;
}
/* line 1135, ../sass/_template_specific.scss */
.rooms .btn-flecha:hover {
  opacity: 0.8;
}

/* line 1141, ../sass/_template_specific.scss */
a.rooms-img {
  height: 220px;
  display: block;
  overflow: hidden;
  position: relative;
}

/* line 1148, ../sass/_template_specific.scss */
img.room_img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
}

/* line 1159, ../sass/_template_specific.scss */
a.btn_vermas_room {
  color: white !important;
  font-weight: 700;
  margin-left: 1px;
  text-align: center;
  text-decoration: none;
}

/* line 1167, ../sass/_template_specific.scss */
.room-links {
  margin-top: 20px;
}
/* line 1170, ../sass/_template_specific.scss */
.room-links a.button-promotion {
  color: white !important;
  text-decoration: none;
}
/* line 1175, ../sass/_template_specific.scss */
.room-links .btn-corporate:hover {
  opacity: 0.8;
}

/* line 1180, ../sass/_template_specific.scss */
.blockleft-room {
  margin-left: 0px;
  margin-right: 30px;
}

/* line 1185, ../sass/_template_specific.scss */
.blockright-room {
  margin-right: 0px;
}

/* line 1189, ../sass/_template_specific.scss */
.myFancyPopupRooms {
  margin-left: 20px;
  margin-right: 20px;
  width: 550px;
}
/* line 1194, ../sass/_template_specific.scss */
.myFancyPopupRooms .sub-description-rooms {
  font-weight: 700;
  font-family: yanone, sans-serif;
  color: #C5AD81;
  font-size: 18px;
  margin-top: 0;
}

/*======= Cycle Banners ======*/
/* line 1204, ../sass/_template_specific.scss */
.banners_cycle_wrapper {
  margin-bottom: 70px;
}
/* line 1207, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element {
  height: 270px;
  overflow: hidden;
  margin-bottom: 5px;
}
/* line 1212, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element .cycle_image_element {
  width: 35%;
  float: left;
  height: 270px;
}
/* line 1217, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element .cycle_image_element img {
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 1223, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element .cycle_text_wrapper {
  width: 65%;
  float: right;
  background: #F8F7F5;
  padding: 30px;
  text-align: left;
  font-weight: 100;
  box-sizing: border-box;
  color: black;
  height: 270px;
  line-height: 19px;
  font-family: "Open Sans", sans-serif;
  font-size: 13px;
  color: #757575;
}
/* line 1239, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element .cycle_text_wrapper h3.section_title {
  color: #C5AD81;
  font-size: 21px;
  padding-bottom: 14px;
  font-weight: 300;
  font-family: 'Roboto Slab', serif;
}
/* line 1248, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element .cycle_text_wrapper .exceded {
  height: 170px;
  overflow: hidden;
}
/* line 1255, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element.right .cycle_image_element {
  float: right;
}
/* line 1261, ../sass/_template_specific.scss */
.banners_cycle_wrapper a.see_more_section, .banners_cycle_wrapper a.see_more_video {
  background: #72162F;
  padding: 8px 15px;
  margin-top: 9px;
  display: inline-block;
  text-decoration: none;
  color: white;
  font-weight: 500;
}

/* line 1272, ../sass/_template_specific.scss */
.hidden_cycle {
  display: none;
  float: right;
  background: #F8F7F5;
  padding: 8px 30px;
  text-align: left;
  font-weight: 100;
  box-sizing: border-box;
  color: black;
  font-size: 14px;
  line-height: 19px;
}
/* line 1284, ../sass/_template_specific.scss */
.hidden_cycle h3.section_title {
  color: #C5AD81;
  font-size: 21px;
  padding-bottom: 14px;
  font-weight: 300;
}

/************************* SCAPES/OFERTAS ************************/
/* line 1293, ../sass/_template_specific.scss */
a.plus {
  padding: 7px 8px 9px !important;
}

/* line 1297, ../sass/_template_specific.scss */
a.play {
  padding: 10px 9px 5px !important;
}

/* line 1301, ../sass/_template_specific.scss */
.scapes-blocks {
  overflow: hidden;
  margin-top: 75px;
  margin-bottom: 75px;
}

/* line 1307, ../sass/_template_specific.scss */
.scapes-blocks .block {
  margin-bottom: 20px;
  width: 560px;
  float: left;
  height: 492px;
  overflow: hidden;
  position: relative;
}
/* line 1315, ../sass/_template_specific.scss */
.scapes-blocks .block a img {
  margin-bottom: -5px;
  width: 100%;
}
/* line 1320, ../sass/_template_specific.scss */
.scapes-blocks .block .description {
  padding: 20px;
  position: relative;
  background-color: rgba(245, 245, 245, 0.8);
  padding-right: 160px;
}
/* line 1326, ../sass/_template_specific.scss */
.scapes-blocks .block .description .title-module {
  font-size: 23px;
  color: #cdb480;
  font-weight: 700;
}
/* line 1331, ../sass/_template_specific.scss */
.scapes-blocks .block .description .title-module .offer-subtitle {
  font-weight: 300;
  font-size: 18px;
}
/* line 1337, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul {
  position: absolute;
  width: 115px;
  right: 0;
  top: 34px;
  text-align: right;
  padding-right: 10px;
}
/* line 1345, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li {
  display: inline;
}
/* line 1347, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a {
  background-color: #d84c1d;
  top: -1px;
  color: white;
  padding: 7px 10px 8px;
  right: 97px;
  text-align: center;
  height: 21px;
  text-decoration: none;
}
/* line 1356, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a:hover {
  opacity: 0.8;
}
/* line 1360, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a.plus {
  padding: 10px 7px 5px;
  margin-right: -75px;
  height: 20px;
  background: #cdb480;
  text-decoration: none;
}
/* line 1368, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a.play {
  padding: 10px 9px 5px;
  text-decoration: none;
}
/* line 1372, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a.play img {
  margin-top: 2px;
}
/* line 1379, ../sass/_template_specific.scss */
.scapes-blocks .block .description p {
  margin-bottom: 0;
}

/* line 1385, ../sass/_template_specific.scss */
.scapes-blocks .offer-image-container {
  wdith: 100%;
  height: 364px;
  overflow: hidden;
}

/* line 1391, ../sass/_template_specific.scss */
.scapes-blocks .row1 {
  margin-right: 10px;
}

/* line 1395, ../sass/_template_specific.scss */
.scapes-blocks .row2 {
  margin-left: 10px;
}

/* line 1399, ../sass/_template_specific.scss */
.scapes-bottom-content {
  background: #cdb480;
  padding: 20px;
}

/* line 1404, ../sass/_template_specific.scss */
.scapes-popup {
  padding: 19px;
}

/* line 1409, ../sass/_template_specific.scss */
.escapadas-popup h3 {
  color: #cdb480;
  margin-bottom: 20px;
}
/* line 1414, ../sass/_template_specific.scss */
.escapadas-popup h5 {
  color: #cdb480;
}

/* line 1419, ../sass/_template_specific.scss */
.oferta-reserva {
  margin-right: 10px;
}

/*======= Localizacion y Contacto ======*/
/* line 1426, ../sass/_template_specific.scss */
.page-localizacion #wrapper_content, .page-localizacion .how-to {
  background: rgba(252, 241, 235, 0.86);
  padding: 20px;
  margin-top: 200px;
  width: 1100px;
}
/* line 1433, ../sass/_template_specific.scss */
.page-localizacion .container12 .column6 {
  width: 530px;
}

/* line 1439, ../sass/_template_specific.scss */
.location-info-and-form-wrapper {
  margin-top: 75px;
  margin-bottom: 75px;
  background: rgba(226, 226, 226, 0.46);
  display: table;
  padding-top: 20px;
  padding-bottom: 20px;
}
/* line 1448, ../sass/_template_specific.scss */
.location-info-and-form-wrapper .form-contact span.title a {
  color: #757575;
  font-weight: 300;
  font-family: "Open Sans", sans-serif;
}
/* line 1455, ../sass/_template_specific.scss */
.location-info-and-form-wrapper label {
  display: inline;
  color: #757575;
  font-weight: 300;
  font-family: "Open Sans", sans-serif;
}

/* line 1463, ../sass/_template_specific.scss */
.location-info-and-form-wrapper h1 {
  font-weight: 700;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  padding-bottom: 6px;
  color: #C5AD81;
  font-family: "Open Sans", sans-serif;
  width: 95%;
}

/* line 1476, ../sass/_template_specific.scss */
.location-info strong {
  font-weight: bold;
}

/* line 1480, ../sass/_template_specific.scss */
#location-description-intro {
  margin-bottom: 30px;
}

/* line 1484, ../sass/_template_specific.scss */
.iframe-google-maps-wrapper {
  margin-top: 30px;
  width: 100%;
  margin-bottom: 30px;
}

/* line 1493, ../sass/_template_specific.scss */
.form-contact #title {
  display: none !important;
}

/* line 1497, ../sass/_template_specific.scss */
.form-contact #google-plus {
  display: none !important;
}

/* line 1501, ../sass/_template_specific.scss */
.form-contact .fb-like {
  display: none !important;
}

/* line 1509, ../sass/_template_specific.scss */
.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

/* line 1514, ../sass/_template_specific.scss */
.form-contact #contactContent .bordeInput {
  width: auto;
  margin-right: 5px;
}

/* line 1519, ../sass/_template_specific.scss */
.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: #757575;
  font-weight: 300;
  font-family: "Open Sans", sans-serif;
}

/* line 1528, ../sass/_template_specific.scss */
.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px !important;
  height: 30px;
  background-color: white;
  color: #5a5a5a;
}

/* line 1538, ../sass/_template_specific.scss */
.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px;
  background-color: white;
  color: #5a5a5a;
  margin-right: 35px;
}

/* line 1548, ../sass/_template_specific.scss */
.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 35px;
}

/* line 1553, ../sass/_template_specific.scss */
.form-contact #contact-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background: #C5AD81 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 28px;
  font-family: "Open Sans", sans-serif;
}

/* line 1569, ../sass/_template_specific.scss */
.form-contact #contact-button:hover {
  background-color: #cdb480 !important;
}

/* line 1573, ../sass/_template_specific.scss */
.location-info {
  font-weight: 300;
  padding-left: 20px;
  box-sizing: border-box;
  color: #757575;
  font-family: "Open Sans", sans-serif;
}
/* line 1580, ../sass/_template_specific.scss */
.location-info div * {
  font-size: 16px;
}

/* line 1586, ../sass/_template_specific.scss */
.how-to {
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;
}
/* line 1592, ../sass/_template_specific.scss */
.how-to h3 {
  font-size: 30px;
  margin-bottom: 20px;
  color: #cdb480;
}

/* line 1601, ../sass/_template_specific.scss */
form#my-bookings-form {
  margin-top: 30px;
}

/* line 1605, ../sass/_template_specific.scss */
#wrapper_services {
  display: none;
}

/* line 1609, ../sass/_template_specific.scss */
h3.section-title {
  font-family: "roboto slab";
  font-size: 36px;
  font-weight: 300;
  color: #d84c1d;
  margin-bottom: 5px;
}

/* line 1618, ../sass/_template_specific.scss */
.booking-content {
  text-align: center;
  margin-bottom: 20px;
}

/* line 1623, ../sass/_template_specific.scss */
#my-bookings-form-fields {
  text-align: center;
}
/* line 1626, ../sass/_template_specific.scss */
#my-bookings-form-fields label {
  display: block;
  color: #cdb480;
  text-transform: uppercase;
}
/* line 1631, ../sass/_template_specific.scss */
#my-bookings-form-fields input, #my-bookings-form-fields .bordeSelect {
  display: block;
  margin: 0 auto;
  margin-bottom: 10px;
  width: 250px;
  height: 15px;
  padding: 5px;
  text-align: center;
  color: black;
  border: none;
  background: #e6e6e6;
}
/* line 1644, ../sass/_template_specific.scss */
#my-bookings-form-fields .bordeSelect {
  -webkit-appearance: none;
  color: #d84c1d;
  width: 263px !important;
  border-radius: 0px !important;
  height: 25px !important;
  background: #e6e6e6 url(/img/checn/select_down.png) no-repeat 240px;
}
/* line 1652, ../sass/_template_specific.scss */
#my-bookings-form-fields #my-bookings-form-search-button {
  width: 260px;
  color: white;
  background-color: #cdb480;
  padding: 10px 0;
  border: none;
  font-size: 14px;
  cursor: pointer;
}
/* line 1661, ../sass/_template_specific.scss */
#my-bookings-form-fields #my-bookings-form-search-button:hover {
  background-color: #d84c1d;
}

/* line 1667, ../sass/_template_specific.scss */
button#cancelButton {
  width: 260px;
  color: white;
  background-color: #cdb480;
  padding: 10px 0;
  border: none;
  font-size: 14px;
  cursor: pointer;
}
/* line 1676, ../sass/_template_specific.scss */
button#cancelButton:hover {
  background-color: #d84c1d;
}

/* line 1681, ../sass/_template_specific.scss */
#cancel-button-container {
  text-align: center;
}
/* line 1684, ../sass/_template_specific.scss */
#cancel-button-container #cancelButton {
  width: 260px;
  color: white;
  background-color: #cdb480;
  padding: 10px 0;
  border: none;
  font-size: 14px;
  cursor: pointer;
  display: none;
  margin-left: 470px;
}
/* line 1695, ../sass/_template_specific.scss */
#cancel-button-container #cancelButton:hover {
  background-color: #d84c1d;
}

/* line 1701, ../sass/_template_specific.scss */
#reservation div.grid_12.alpha.my-bookings-booking-info {
  margin-left: 320px !important;
}

/* line 2, ../sass/_booking_widget_modal_5.scss */
.booking-data-popup .fancybox-outer {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
/* line 8, ../sass/_booking_widget_modal_5.scss */
.booking-data-popup div#wrapper_booking_fancybox {
  display: table;
  width: 100%;
  position: absolute;
  bottom: 0;
  top: 0;
  margin: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 20, ../sass/_booking_widget_modal_5.scss */
.booking-data-popup .adultos.numero_personas > label, .booking-data-popup .ninos.numero_personas > label, .booking-data-popup .bebes.numero_personas > label {
  display: none !important;
}

/* line 26, ../sass/_booking_widget_modal_5.scss */
div#data {
  background: rgba(205, 180, 128, 0.7);
}
/* line 29, ../sass/_booking_widget_modal_5.scss */
div#data .booking_title1, div#data .best_price {
  display: none;
}
/* line 33, ../sass/_booking_widget_modal_5.scss */
div#data div#booking_engine_title {
  display: block;
  float: none;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
}
/* line 40, ../sass/_booking_widget_modal_5.scss */
div#data #motor_reserva {
  width: 595px;
  margin: auto;
  display: table;
}
/* line 46, ../sass/_booking_widget_modal_5.scss */
div#data div#fecha_entrada, div#data div#fecha_salida {
  width: 290px;
  float: left;
  height: 125px;
}
/* line 52, ../sass/_booking_widget_modal_5.scss */
div#data div#fecha_salida {
  float: right;
  margin-left: 0 !important;
}
/* line 57, ../sass/_booking_widget_modal_5.scss */
div#data label#titulo_fecha_entrada, div#data label#titulo_fecha_salida {
  display: block;
  color: #999;
  width: 100% !important;
  text-align: center;
  text-transform: uppercase;
  font-size: 17px;
  font-weight: 500;
  font-family: 'Roboto', sans-serif;
  background: white;
  margin-bottom: 5px;
  padding: 9px 0;
}
/* line 71, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_fechas {
  width: 100%;
  margin-bottom: 15px;
}
/* line 76, ../sass/_booking_widget_modal_5.scss */
div#data .wrapper-old-web-support {
  display: none !important;
}
/* line 80, ../sass/_booking_widget_modal_5.scss */
div#data #fecha_entrada input, div#data #fecha_salida input {
  border: 0 !important;
  height: 84px !important;
  width: 100% !important;
  text-align: center !important;
  box-sizing: border-box !important;
  font-size: 31px !important;
  color: #4b4b4b !important;
  padding-right: 40px;
  border-radius: 0;
  background: white url(/static_1/images/booking_5/entry_date.png) no-repeat 85% center !important;
}
/* line 92, ../sass/_booking_widget_modal_5.scss */
div#data #fecha_entrada input::-webkit-input-placeholder, div#data #fecha_salida input::-webkit-input-placeholder {
  color: #4b4b4b !important;
}
/* line 97, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_habitaciones {
  margin: auto;
  width: 290px;
  float: left;
  height: 125px;
}
/* line 103, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_habitaciones label {
  display: block;
  color: #999;
  width: 100% !important;
  text-align: center;
  text-transform: uppercase;
  font-size: 17px;
  float: none;
  font-weight: bolder;
  font-family: 'Montserrat', sans-serif;
  background: white;
  margin-bottom: 2px;
  padding: 9px 0;
}
/* line 118, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_habitaciones select#selector_habitaciones {
  -webkit-border-radius: 0 !important;
  -moz-border-radius: 0 !important;
  border-radius: 0 !important;
  border: 0;
  width: 260px;
  float: left;
  height: 125px;
  background: white;
  -webkit-appearance: none;
}
/* line 129, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_habitaciones select#selector_habitaciones option {
  text-align: center;
}
/* line 134, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_habitaciones .selectric {
  height: 83px;
  border-radius: 0;
  margin-top: 0;
}
/* line 139, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_habitaciones .selectric p.label {
  color: #4b4b4b;
  text-align: center;
  box-sizing: border-box !important;
  font-size: 31px !important;
  padding-top: 22px;
}
/* line 147, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_habitaciones .selectric .button {
  background: transparent !important;
  right: 27px;
}
/* line 153, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_habitaciones .selectricItems li {
  color: #4b4b4b;
  text-align: center;
  box-sizing: border-box !important;
  font-size: 21px !important;
  padding: 12px 12px 10px;
}
/* line 162, ../sass/_booking_widget_modal_5.scss */
div#data .selectricWrapper {
  width: 100% !important;
}
/* line 166, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_opciones {
  float: right;
  margin-top: -125px;
}
/* line 170, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_opciones #hab1, div#data #contenedor_opciones #hab2, div#data #contenedor_opciones #hab3 {
  margin: auto;
  width: 290px;
  float: left;
  height: 125px;
}
/* line 177, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_opciones #hab1 {
  margin-left: 305px;
}
/* line 181, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_opciones #hab2, div#data #contenedor_opciones #hab3 {
  margin-top: 20px;
  display: block !important;
}
/* line 186, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_opciones #hab3 {
  float: right;
}
/* line 190, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_opciones label.numero_habitacion {
  color: #999;
  font-weight: 500;
  width: 100% !important;
  text-align: center;
  display: block !important;
  text-transform: uppercase;
  font-size: 17px;
  background: white;
  float: none;
  font-family: 'Roboto', sans-serif;
  margin-bottom: 2px;
  padding: 9px 0;
}
/* line 206, ../sass/_booking_widget_modal_5.scss */
div#data .adultos.numero_personas, div#data .ninos.numero_personas, div#data .bebes.numero_personas {
  margin: 0;
  position: relative;
  display: inline-block;
}
/* line 211, ../sass/_booking_widget_modal_5.scss */
div#data .adultos.numero_personas option, div#data .ninos.numero_personas option, div#data .bebes.numero_personas option {
  display: none;
}
/* line 216, ../sass/_booking_widget_modal_5.scss */
div#data .adultos.numero_personas {
  width: 142.25px;
  text-align: center;
  float: left;
  margin-right: 5.5px;
}
/* line 223, ../sass/_booking_widget_modal_5.scss */
div#data .ninos.numero_personas {
  width: 142.25px;
  text-align: center;
  float: left;
}
/* line 228, ../sass/_booking_widget_modal_5.scss */
div#data .ninos.numero_personas .selectricItems {
  left: -84px !important;
}
/* line 233, ../sass/_booking_widget_modal_5.scss */
div#data .bebes.numero_personas {
  width: 32%;
}
/* line 236, ../sass/_booking_widget_modal_5.scss */
div#data .bebes.numero_personas .selectricItems {
  left: -180px !important;
}
/* line 241, ../sass/_booking_widget_modal_5.scss */
div#data .ninos {
  float: left;
}
/* line 244, ../sass/_booking_widget_modal_5.scss */
div#data .ninos label#info_ninos {
  position: absolute;
  top: 20px;
  color: black;
  right: 0px;
  font-size: 9px !important;
  display: inline-block;
}
/* line 255, ../sass/_booking_widget_modal_5.scss */
div#data .selectricWrapper.selector_adultos .selectric, div#data .selectricWrapper.selector_ninos .selectric, div#data .selectricWrapper.selector_bebes .selectric {
  height: 83px;
  border-radius: 0;
  margin-top: 0;
}
/* line 261, ../sass/_booking_widget_modal_5.scss */
div#data .selectricWrapper.selector_adultos p.label, div#data .selectricWrapper.selector_ninos p.label, div#data .selectricWrapper.selector_bebes p.label {
  color: #4b4b4b;
  text-align: center;
  padding-right: 0 !important;
  box-sizing: border-box !important;
  padding-top: 23px;
  font-size: 18px !important;
}
/* line 270, ../sass/_booking_widget_modal_5.scss */
div#data .selectricWrapper.selector_adultos .button, div#data .selectricWrapper.selector_ninos .button, div#data .selectricWrapper.selector_bebes .button {
  background: transparent !important;
  width: 16px;
  height: 20px;
  top: 5px;
  right: 10px !important;
}
/* line 278, ../sass/_booking_widget_modal_5.scss */
div#data .selectricWrapper.selector_adultos .selectricItems li, div#data .selectricWrapper.selector_ninos .selectricItems li, div#data .selectricWrapper.selector_bebes .selectricItems li {
  color: #4b4b4b;
  text-align: center;
  box-sizing: border-box !important;
  font-size: 16px !important;
  padding: 6px 12px 4px;
}
/* line 287, ../sass/_booking_widget_modal_5.scss */
div#data fieldset#envio {
  width: 100%;
  margin-left: 0;
}
/* line 291, ../sass/_booking_widget_modal_5.scss */
div#data fieldset#envio input#promocode {
  float: left;
  width: 290px;
  border-radius: 0;
  border: 0;
  box-sizing: border-box;
  margin-top: 10px;
  height: 90px;
  text-align: center;
  background: rgba(255, 255, 255, 0.3);
  font-size: 31px !important;
  font-weight: 300;
  color: white;
}
/* line 305, ../sass/_booking_widget_modal_5.scss */
div#data fieldset#envio input#promocode::-webkit-input-placeholder {
  color: white;
  font-size: 18px;
  font-weight: 300;
  text-transform: uppercase;
}
/* line 313, ../sass/_booking_widget_modal_5.scss */
div#data fieldset#envio button#search-button {
  display: block;
  float: right;
  height: 90px;
  width: 290px;
  border-radius: 0;
  border: 0;
  box-sizing: border-box;
  margin-top: 10px;
  background: #cdb480;
  color: white;
  text-transform: uppercase;
  font-size: 27px !important;
  -webkit-transition: border-radius 0.6s;
  -moz-transition: border-radius 0.6s;
  -ms-transition: border-radius 0.6s;
  -o-transition: border-radius 0.6s;
  transition: border-radius 0.6s;
}
/* line 328, ../sass/_booking_widget_modal_5.scss */
div#data fieldset#envio button#search-button:hover {
  border-radius: 10px;
}
/* line 335, ../sass/_booking_widget_modal_5.scss */
div#data div#hab2 .disabled_overlay, div#data div#hab3 .disabled_overlay {
  display: none;
}
/* line 339, ../sass/_booking_widget_modal_5.scss */
div#data div#hab2.disabled, div#data div#hab3.disabled {
  opacity: 0.4;
  position: relative;
}
/* line 343, ../sass/_booking_widget_modal_5.scss */
div#data div#hab2.disabled .disabled_overlay, div#data div#hab3.disabled .disabled_overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: block;
}
/* line 355, ../sass/_booking_widget_modal_5.scss */
div#data #booking_engine_title #booking_title1, div#data #booking_engine_title #best_price {
  display: none;
}
/* line 359, ../sass/_booking_widget_modal_5.scss */
div#data #booking_engine_title h4#booking_title2 {
  color: white;
  margin-bottom: 25px;
  text-transform: uppercase;
  font-size: 22px;
  margin-top: 0;
}
/* line 369, ../sass/_booking_widget_modal_5.scss */
div#data #booking_engine_title #booking_title2:after {
  content: '';
  display: block;
  width: 70px;
  height: 1px;
  background: white;
  margin: 10px auto;
}
/* line 378, ../sass/_booking_widget_modal_5.scss */
div#data #booking_engine_title #booking_title2 span {
  font-weight: 300;
}
/* line 384, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_opciones div#hab2.disabled {
  display: none !important;
}
/* line 387, ../sass/_booking_widget_modal_5.scss */
div#data #contenedor_opciones div#hab2.disabled + #hab3 {
  display: none !important;
}
/* line 393, ../sass/_booking_widget_modal_5.scss */
div#data .selectricItems {
  width: 288px !important;
  top: 84% !important;
  left: 11px !important;
  z-index: 9999;
}
/* line 400, ../sass/_booking_widget_modal_5.scss */
div#data .destination_wrapper {
  width: 100%;
  margin-bottom: 15px;
  border-bottom: 0;
}
/* line 405, ../sass/_booking_widget_modal_5.scss */
div#data .destination_wrapper label {
  display: none;
}
/* line 410, ../sass/_booking_widget_modal_5.scss */
div#data .destination_wrapper .destination_field input {
  width: 100%;
  height: 55px;
  color: #d84c1d;
  padding-left: 55px;
  font-family: 'Roboto', sans-serif;
  font-weight: 500;
}
/* line 418, ../sass/_booking_widget_modal_5.scss */
div#data .destination_wrapper .destination_field input::-webkit-input-placeholder {
  color: #d84c1d;
  text-transform: uppercase;
  font-weight: bolder;
}
/* line 424, ../sass/_booking_widget_modal_5.scss */
div#data .destination_wrapper .destination_field input:-moz-placeholder {
  /* Firefox 18- */
  color: #d84c1d;
}
/* line 429, ../sass/_booking_widget_modal_5.scss */
div#data .destination_wrapper .destination_field input::-moz-placeholder {
  /* Firefox 19+ */
  color: #d84c1d;
}
/* line 434, ../sass/_booking_widget_modal_5.scss */
div#data .destination_wrapper .destination_field input:-ms-input-placeholder {
  color: #d84c1d;
}
/* line 441, ../sass/_booking_widget_modal_5.scss */
div#data #contador_noches {
  display: none;
}

/* line 447, ../sass/_booking_widget_modal_5.scss */
.booking-data-popup .fancybox-close {
  position: absolute;
  top: 30px;
  right: 30px;
  background: url(/img/gran2/close_button.png) no-repeat center;
  background: none;
}
/* line 454, ../sass/_booking_widget_modal_5.scss */
.booking-data-popup .fancybox-close:before {
  content: "x";
  color: white;
  font-size: 85px;
  line-height: 36px;
  font-family: 'Montserrat', sans-serif;
}

/* line 463, ../sass/_booking_widget_modal_5.scss */
.booking-data-popup .fancybox-outer {
  background: none;
}

/* line 467, ../sass/_booking_widget_modal_5.scss */
.contact_bottom_popup {
  display: block;
  width: 535px;
  margin: 20px auto;
  text-align: center;
  color: white;
}
/* line 474, ../sass/_booking_widget_modal_5.scss */
.contact_bottom_popup .phone_hotel_booking_popup, .contact_bottom_popup .email_hotel {
  display: inline-block;
  padding-left: 30px;
  line-height: 25px;
}
/* line 480, ../sass/_booking_widget_modal_5.scss */
.contact_bottom_popup .phone_hotel_booking_popup {
  margin-right: 10px;
  background: url(/img/gran2/booking_icos/phone_ico.png) no-repeat left center;
}
/* line 485, ../sass/_booking_widget_modal_5.scss */
.contact_bottom_popup .email_hotel {
  background: url(/img/gran2/booking_icos/mail_ico.png) no-repeat left center;
}

/* line 493, ../sass/_booking_widget_modal_5.scss */
.datepicker_wrapper_element .header_datepicker {
  background: #cdb480 !important;
}

/* line 25, ../sass/styles_tropical-sol.scss */
header,
#main_menu {
  background: #cdb480;
}

/* line 30, ../sass/styles_tropical-sol.scss */
#logoDiv {
  background: none;
}
/* line 33, ../sass/styles_tropical-sol.scss */
#logoDiv img {
  padding-top: 0px;
  margin-top: 10px;
}

/* line 39, ../sass/styles_tropical-sol.scss */
span.official {
  color: rgba(0, 0, 0, 0.5);
}

/* line 43, ../sass/styles_tropical-sol.scss */
#mainMenuDiv a {
  border-bottom: none;
}

/* line 47, ../sass/styles_tropical-sol.scss */
#lang {
  background: white;
}
/* line 50, ../sass/styles_tropical-sol.scss */
#lang .lang_selected {
  color: #cdb480;
}

/* line 55, ../sass/styles_tropical-sol.scss */
#lang a {
  color: #c09b74;
}

/* line 59, ../sass/styles_tropical-sol.scss */
.boking_widget_inline .wrapper_booking_button button {
  background: #d84c1d;
}

/* line 63, ../sass/styles_tropical-sol.scss */
.selectric .button {
  background: #cdb480 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
}

/* line 67, ../sass/styles_tropical-sol.scss */
.date_box .date_day {
  color: #cdb480;
}

/* line 71, ../sass/styles_tropical-sol.scss */
.normal_content_wrapper .normal_title {
  color: #787878;
}

/* line 75, ../sass/styles_tropical-sol.scss */
.normal_content_wrapper .separator {
  border-top: 5px solid #d84c1d;
}

/* line 79, ../sass/styles_tropical-sol.scss */
.content_access {
  text-align: center;
  margin: 40px auto;
}
/* line 83, ../sass/styles_tropical-sol.scss */
.content_access .separatos {
  border-top: 5px solid #d84c1d;
}

/* line 89, ../sass/styles_tropical-sol.scss */
.mini_gallery_wrapper h3 {
  color: #d84c1d;
}

/* line 93, ../sass/styles_tropical-sol.scss */
.maps_footer h3.map_title strong {
  font-weight: 100;
}

/* line 97, ../sass/styles_tropical-sol.scss */
footer #newsletter button#newsletter-button {
  background: #d84c1d;
}

/* line 101, ../sass/styles_tropical-sol.scss */
.offer_booking .button-promotion {
  background: #d84c1d !important;
  text-decoration: none;
}

/* line 107, ../sass/styles_tropical-sol.scss */
#social a {
  background: #d84c1d;
}

/* line 112, ../sass/styles_tropical-sol.scss */
#my-bookings-form-fields {
  text-align: center;
}
/* line 115, ../sass/styles_tropical-sol.scss */
#my-bookings-form-fields label {
  color: #cdb480;
}
/* line 119, ../sass/styles_tropical-sol.scss */
#my-bookings-form-fields .bordeSelect {
  color: #cdb480;
}
/* line 123, ../sass/styles_tropical-sol.scss */
#my-bookings-form-fields #my-bookings-form-search-button {
  background-color: #d84c1d;
}
/* line 126, ../sass/styles_tropical-sol.scss */
#my-bookings-form-fields #my-bookings-form-search-button:hover {
  background-color: #cdb480;
}

/* line 132, ../sass/styles_tropical-sol.scss */
#mainMenuDiv a {
  border-bottom: none;
  color: white;
  -webkit-transition: font-size 0.5s;
  -moz-transition: font-size 0.5s;
  -ms-transition: font-size 0.5s;
  -o-transition: font-size 0.5s;
  transition: font-size 0.5s;
}
/* line 137, ../sass/styles_tropical-sol.scss */
#mainMenuDiv a:hover {
  font-size: 15px;
}

/* line 142, ../sass/styles_tropical-sol.scss */
.submit_button.buttonsearch-ratecheck {
  -webkit-transition: background 0.3s;
  -moz-transition: background 0.3s;
  -ms-transition: background 0.3s;
  -o-transition: background 0.3s;
  transition: background 0.3s;
}
/* line 145, ../sass/styles_tropical-sol.scss */
.submit_button.buttonsearch-ratecheck:hover {
  background: #FF531D;
}

/* line 150, ../sass/styles_tropical-sol.scss */
.offer_links .offer_booking .button-promotion {
  -webkit-transition: background 0.3s;
  -moz-transition: background 0.3s;
  -ms-transition: background 0.3s;
  -o-transition: background 0.3s;
  transition: background 0.3s;
}
/* line 153, ../sass/styles_tropical-sol.scss */
.offer_links .offer_booking .button-promotion:hover {
  background: #FF531D;
}

/* line 160, ../sass/styles_tropical-sol.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: white !important;
}

/* line 164, ../sass/styles_tropical-sol.scss */
.datepicker_wrapper_element .header_datepicker {
  background: #d84c1d;
}

/* line 168, ../sass/styles_tropical-sol.scss */
.ui-datepicker .ui-datepicker-header {
  background: #cdb480 !important;
}

/* line 172, ../sass/styles_tropical-sol.scss */
.datepicker_wrapper_element .months_selector_container .cheapest_month_selector {
  background: #d84c1d;
}

/* line 177, ../sass/styles_tropical-sol.scss */
body .ui-datepicker th {
  font-weight: 300;
  font-size: 14px;
}
/* line 182, ../sass/styles_tropical-sol.scss */
body .ui-state-default, body .ui-widget-content .ui-state-default, body .ui-widget-header .ui-state-default {
  background: transparent !important;
  border: 0 !important;
  color: #646464 !important;
  font-weight: 400;
  font-family: Circular, "Helvetica Neue", Helvetica, Arial, sans-serif;
  text-align: center;
  font-size: 13px;
}
/* line 192, ../sass/styles_tropical-sol.scss */
body .ui-widget-content .ui-state-hover {
  border: 0;
  background: #cdb480 !important;
  color: white !important;
}
/* line 198, ../sass/styles_tropical-sol.scss */
body .ui-widget-content {
  border: 0;
  border-radius: 0;
  padding-bottom: 40px;
}
/* line 204, ../sass/styles_tropical-sol.scss */
body .ui-state-default {
  padding: 8px;
}
/* line 208, ../sass/styles_tropical-sol.scss */
body .ui-datepicker-start_date {
  opacity: 1 !important;
}
/* line 211, ../sass/styles_tropical-sol.scss */
body .ui-datepicker-start_date .ui-state-default {
  border: 0;
  background: #cdb480 !important;
  color: white !important;
}
/* line 218, ../sass/styles_tropical-sol.scss */
body .ui-datepicker-highlighted .ui-state-default {
  border: 0;
  background: rgba(40, 96, 144, 0.25) !important;
}
/* line 223, ../sass/styles_tropical-sol.scss */
body .ui-widget-content .ui-state-active {
  border: 0;
  background: #cdb480 !important;
  color: white !important;
}
/* line 229, ../sass/styles_tropical-sol.scss */
body span.ui-icon.ui-icon-circle-triangle-e, body span.ui-icon.ui-icon-circle-triangle-w {
  /* background: url(/static_1/images/mobile_img/renovation/flecha_der.png) no-repeat center;*/
  background: none;
  text-indent: 0;
  color: transparent;
  font-size: 0;
}
/* line 236, ../sass/styles_tropical-sol.scss */
body span.ui-icon.ui-icon-circle-triangle-e:before, body span.ui-icon.ui-icon-circle-triangle-w:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  content: '\f105';
  font-family: "FontAwesome", sans-serif;
  font-size: 20px;
  color: black;
}
/* line 246, ../sass/styles_tropical-sol.scss */
body span.ui-icon.ui-icon-circle-triangle-w:before {
  content: '\f104';
}
/* line 251, ../sass/styles_tropical-sol.scss */
body .ui-datepicker .ui-datepicker-next, body .ui-datepicker .ui-datepicker-prev {
  background: none;
}
/* line 255, ../sass/styles_tropical-sol.scss */
body .ui-datepicker-next.ui-state-hover, body .ui-datepicker-prev.ui-datepicker-prev-hover {
  border: 1px solid #636363;
}

/* line 262, ../sass/styles_tropical-sol.scss */
.datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-e:before, .datepicker_wrapper_element span.ui-icon.ui-icon-circle-triangle-w:before {
  display: none;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

/* line 275, ../sass/styles_tropical-sol.scss */
#top-sections a {
  margin-left: 10px;
}
/* line 277, ../sass/styles_tropical-sol.scss */
#top-sections a span, #top-sections a i {
  color: white;
}
/* line 282, ../sass/styles_tropical-sol.scss */
#top-sections a:first-of-type i:before {
  content: "\f278";
}
/* line 287, ../sass/styles_tropical-sol.scss */
#top-sections a:hover > span {
  opacity: 1;
  color: #d84c1d !important;
  transition: color 0.5s;
}

/* line 296, ../sass/styles_tropical-sol.scss */
.gallery_1 li {
  position: relative;
  height: 212px;
  width: 283px !important;
  overflow: hidden;
}
/* line 303, ../sass/styles_tropical-sol.scss */
.gallery_1 li .crop a img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  width: auto;
  height: auto;
  max-height: 190%;
}
/* line 308, ../sass/styles_tropical-sol.scss */
.gallery_1 li .crop a img:hover {
  transform: translate(-50%, -50%) scale(1.2);
}

/* line 316, ../sass/styles_tropical-sol.scss */
.map_title, room_title, location-info h1, .content_access .section-title, #my-bookings-form-fields label {
  font-family: "Roboto Slab", serif;
  color: #c09b74 !important;
}

/* line 321, ../sass/styles_tropical-sol.scss */
.content_access {
  font-family: "Open Sans", sans-serif;
}

@media only screen and (max-width: 1440px) {
  /* line 327, ../sass/styles_tropical-sol.scss */
  #calendar-app-root .price_calendar_wrapper {
    height: 650px;
    overflow-x: hidden;
    overflow-y: scroll;
  }
}
