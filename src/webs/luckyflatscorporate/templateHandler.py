# -*- coding: utf-8 -*-
import copy
from collections import OrderedDict
from copy import deepcopy
import os

from booking_process.utils.booking.normalizationUtils import normalizeFor<PERSON>lassName
from booking_process.constants.advance_configs_names import KEY_DOMAIN, NEWSLETTER_POPUP_THANKS, PUBLIC_CAPTCHA_KEY, \
	EMAIL_BOOKING
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.data_management.pictures_utils import getLogotypes, get_pictures_from_section_name, getPicturesForKey
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name, \
	get_section_from_section_spanish_name_with_properties
from booking_process.utils.development.dev_booking_utils import DEV_NAMESPACE, DEV
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_code
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.namespaces.namespace_utils import get_namespace
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html" % thisWeb

TEMPLATE_NAME = "luckyflatscorporate"
#Change this value in default.scss and in config.rb!!
base_web = TEMPLATE_NAME[:4]+TEMPLATE_NAME[-1:]


class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		params = {
			'base_web': base_web
		}

		if not user_agent_is_mobile():
			params.update(self.getDesktopData(sectionToUse, language))
			if DEV and DEV_NAMESPACE:
				params['namespace'] = DEV_NAMESPACE
			else:
				params['namespace'] = get_namespace()
		else:
			params_mobile = {
				"custom_element_home": self.getHtmlExtraBannersMobile(sectionToUse, language),
				'fontawesome5': True
			}

			apartment_blocks_params = {
				'apartment_blocks': self.get_apartments(language),
				'apartment_filters': self.get_apartments_filters(language),
				'aparment_section': get_section_from_section_spanish_name("_apartments_blocks", language),
				'is_mobile': True
			}
			language_dict = get_web_dictionary(language)
			apartment_blocks_params.update(language_dict)

			apartment_blocks_html = self.buildTemplate_2("banners/_apartments_blocks.html", apartment_blocks_params, False, TEMPLATE_NAME)
			params_mobile['custom_element_home'] += apartment_blocks_html

			params.update(params_mobile)

		return params

	def getDesktopData(self, section, language):
		section_type = ''
		section_name = ''
		if section:
			section_type = section['sectionType']
			section_name = section['sectionName'].lower()
		else:
			section = {}

		result_params_dict = {
			'scripts_to_render_desktop': self.scriptsToRenderDesktop(language, dict_params={"allow_messages": False}),
			'booking_engine_2': self.buildSearchEngine2(language),
			'newsletter': self.buildNewsletter2(language, check_newsletter=True, social=True, fontawesome5=True),
			'footer_columns': get_pictures_from_section_name("footer columns", language),
			'checkin_online': get_pictures_from_section_name("_checkin_online", language),
			'location_footer': get_section_from_section_spanish_name("_location_footer", language),
			'header_phone': self.getPicturesProperties(language, "_header_phone"),
			'language_selected': get_language_code(language)
		}

		email_header = get_config_property_value(EMAIL_BOOKING)
		if email_header:
			result_params_dict['email_header'] = email_header.split(";")

		logo_footer = getLogotypes("footer")
		if logo_footer:
			result_params_dict['logo_footer'] = logo_footer[0]

		if section.get('subtitle'):
			result_params_dict['content_subtitle'] = section

		automatic_content = {
			'Mis Reservas Corp': True,
			'Mis Reservas': True
		}

		if automatic_content.get(section_type):
			result_params_dict['content_access'] = True

		widget_options = get_section_from_section_spanish_name_with_properties('_widget_options', language)
		if widget_options and widget_options.get('resident_promocode'):
			result_params_dict['resident_promocode'] = widget_options.get('resident_promocode')

		result_params_dict.update(self.getDataSection(section, language))
		result_params_dict.update(self.getExtraBanners(section, language))

		return result_params_dict

	def getDataSection(self, section, language):
		result = {}
		section_type = section.get("sectionType")
		if section_type == "Inicio":
			result['home'] = True
			result['apartment_blocks'] = self.get_apartments(language)
			result['apartment_filters'] = self.get_apartments_filters(language)
			result['aparment_section'] = get_section_from_section_spanish_name("_apartments_blocks", language)
			result['revolution_slider'] = self.buildRevolutionSlider(language, specificTemplate="inc/_top_slider.html", jinja_template=TEMPLATE_NAME)

			advance_properties_inicio = self.getSectionAdvanceProperties(section, language)
			if advance_properties_inicio and advance_properties_inicio.get('custom_logotype'):
				result['custom_logotype'] = getLogotypes(advance_properties_inicio.get('custom_logotype'))

		elif section_type == "Ofertas":
			result['offers'] = self.getOffers(language)
			result['filter_hotels'] = self.get_each_aparment_in_offers(language)
			filter_type = self.get_each_type_hotel_in_offers(result['offers'])
			result['filter_type'] = filter_type

		elif section_type == "Galeria de Imagenes":
			gallery_pics = self.get_hotel_gallery(language)
			group = OrderedDict()

			for pic in gallery_pics:
				pic_title = pic.get('title', '')
				pic_title_clean = normalizeForClassName(pic_title)
				group.setdefault(pic_title, {'list_pics': []})
				group[pic_title]['list_pics'].append(pic)
				group[pic_title]['filter_class_name'] = pic_title_clean
				pic['class_name'] = pic_title_clean
				pic_properties = self.getSectionAdvanceProperties(pic, language)
				pic['video'] = pic_properties.get('video')
				pic['iframe'] = pic_properties.get('iframe')

			result['gallery_filter'] = group
			result['content_access'] = False

		elif section_type == u"Localización":
			result['form_contact'] = True
			result['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)
			result['iframe_map'] = get_section_from_section_spanish_name("iframe google maps", language).get("content", "")

		elif section_type == "Hotel Individual":
			section_name = section.get('sectionName')
			section_advance_properties = get_section_from_section_spanish_name_with_properties(section_name, language)
			result['hotel_namespace'] = section_advance_properties.get('namespace')
			result['only_adults'] = section_advance_properties.get('only_adults')

		return result

	def getExtraBanners(self, section, language):
		result = {}
		advance_properties = self.getSectionAdvanceProperties(section, language)

		if advance_properties.get('custom_section'):
			result['custom_section'] = True

		if advance_properties.get("cycle_banner"):
			banner_cycle_pics = self.getPicturesProperties(language, advance_properties.get("cycle_banner"))
			for pic in banner_cycle_pics:
				if pic.get("gallery"):
					pic["gallery"] = deepcopy(self.getPicturesProperties(language, pic["gallery"]))

			result['banner_cycle_pics'] = banner_cycle_pics

		if advance_properties.get("services_icon_block"):
			result['services_icon_block_section'] = get_section_from_section_spanish_name(advance_properties.get("services_icon_block"), language)
			result['services_icon_block'] = get_pictures_from_section_name(advance_properties.get("services_icon_block"), language)

		if advance_properties.get("banner_map"):
			banner_map = get_section_from_section_spanish_name(advance_properties.get("banner_map"), language)
			map_properties = self.getSectionAdvanceProperties(banner_map, language)
			banner_map.update(map_properties)
			if banner_map.get('custom_iframe'):
				result['custom_iframe_map'] = unescape(banner_map.get('custom_iframe'))
			else:
				result['iframe_map'] = get_section_from_section_spanish_name("Iframe google maps", language)
			result['banner_map'] = banner_map

		if advance_properties.get("full_slider"):
			result['full_slider'] = True

		if advance_properties.get("carousel_banner"):
			carousel_banners = self.getPicturesProperties(language, advance_properties['carousel_banner'], ["link_text"])
			carousel_section = get_section_from_section_spanish_name_with_properties(advance_properties['carousel_banner'], language)
			if carousel_section.get('custom_carousel_time'):
				result['carousel_time'] = carousel_section.get('custom_carousel_time')
			result['carousel_banners'] = carousel_banners

		if advance_properties.get("opinones_banner"):
			list_properties = ['country', 'date', 'icon', 'rating']
			opinions = self.getPicturesProperties(language, advance_properties['opinones_banner'], list_properties)
			opinion_section = get_section_from_section_spanish_name(advance_properties['opinones_banner'], language)

			result['opinions'] = opinions
			result['opinion_section'] = opinion_section

		if advance_properties.get("ventajas_banner"):
			ventajas_banner = self.getPicturesProperties(language, advance_properties['ventajas_banner'], ['icon'])
			result['ventajas_banner'] = ventajas_banner

		if advance_properties.get("faq_banner"):
			faq_options = get_pictures_from_section_name(advance_properties['faq_banner'], language)
			result['faq_options'] = faq_options

		if advance_properties.get("form_contact"):
			result['form_contact'] = True

		if advance_properties.get("bannerx5"):
			result['bannersx5'] = self.get_bannersx5(advance_properties['bannerx5'], language)

		if advance_properties.get("hide_newsletter"):
			result['hide_newsletter'] = True

		if advance_properties.get("iframe_map"):
			result['iframe_map'] = unescape(advance_properties['iframe_map'])

		if advance_properties.get("white_block"):
			result['white_block'] = get_section_from_section_spanish_name(advance_properties['white_block'], language)

		if advance_properties.get("apartments_blocks"):
			apartments = []
			all_services = self.getPicturesProperties(language, "_services_icons")

			for apartment in advance_properties['apartments_blocks'].split(";"):
				apartment_section = get_section_from_section_spanish_name_with_properties(apartment, language)
				apartment_services = apartment_section.get("services")

				if apartment_services:
					apartment_section['services_options'] = self.get_services(all_services, apartment_services)

				apartments.append(apartment_section)

			result['individual_aparments'] = apartments

		return result

	def get_bannersx5(self, section, language):
		bannersx5 = self.getPicturesProperties(language, section)
		bannerx5_top = {}
		bannerx5_bottom = {}
		bannerx5_columns = []

		for banner in bannersx5:
			if banner.get("type") == "column":
				bannerx5_columns.append(banner)

			elif banner.get("type") == "top":
				bannerx5_top = banner

			elif banner.get("type") == "bottom":
				bannerx5_bottom = banner

		bannersx5 = {
			"top": bannerx5_top,
			"bottom": bannerx5_bottom,
			"column": bannerx5_columns
		}

		return bannersx5

	def get_services(self, all_services, filter_services):
		services = []

		for service in all_services:
			for selected_service in filter_services:
				if service.get("filter") == selected_service:
					services.append(service)

					break

		return services

	def get_apartments(self, language):
		apartments = self.getPicturesProperties(language, "_apartments_blocks")

		for banner in apartments:
			if banner.get("gallery"):
				banner['gallery'] = get_pictures_from_section_name(banner['gallery'], language)

		return apartments

	def get_apartments_filters(self, language):
		apartments = self.get_apartments(language)

		apartment_filters = {}
		for apartment in apartments:
			if apartment.get("filter") and not apartment["filter"] in apartment_filters:
				apartment_filters[apartment['filter']] = {
					"filter_name": apartment['filter'].lower(),
					"filter_color": apartment.get("filter_color")
				}

		return apartment_filters

	def get_apartments_widget(self, language):
		apartments = self.getPicturesProperties(language, "_widget_options")

		for apartment in apartments:
			apartment['value'] = apartment['title']

		return apartments

	def getOffers(self, language):
		offers = []
		individual_offers = self.buildPromotionsInfo(language)
		for offer in individual_offers:
			offer = self.get_advance_propoerties_offer(offer, language)
			if offer.get("type"):
				offer['type_class'] = normalizeForClassName(offer.get("type"))
		offers.extend(individual_offers)
		return offers

	def get_each_aparment_in_offers(self, language):
		result = {}

		hotels = self.get_apartments_widget(language)

		for hotel in hotels:
			hotel_name = hotel.get("title")
			result[hotel_name] = {
				"hotel_name": hotel_name,
				"hotels": hotel.get("namespace")
			}

		return result

	def get_each_type_hotel_in_offers(self, offers):
		result = {}
		for offer in offers:
			if offer.get("type") and offer.get("hotel_list"):
				hotel_name = normalizeForClassName(offer['type'])
				hotel_list = offer["hotel_list"]
				if not result.get(hotel_name):
					result[hotel_name] = {
						"hotel_name": unescape(offer['type']),
						"offer_type": hotel_name,
						"hotels": ";".join(hotel_list)
					}

				else:
					result[hotel_name]['hotels'] += ";" + ";".join(hotel_list)

		return result

	def get_advance_propoerties_offer(self, offer, language):
		pictures_offer = getPicturesForKey(language, str(offer.get("offerKey")), [])
		if pictures_offer:
			advance_properties = self.getSectionAdvanceProperties(pictures_offer[0], language)
			if advance_properties.get("hotel_name"):
				offer['hotel_name'] = unescape(advance_properties["hotel_name"])
			if advance_properties.get("hotel_list"):
				offer['hotel_list'] = advance_properties["hotel_list"].split(";")
			if advance_properties.get("type"):
				offer['type'] = advance_properties['type']
			if offer.get('priority','').startswith("0"):
				offer['is_corpo'] = True

		return offer

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "650"

	def get_revolution_initializer(self):
		return True

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_7/_booking_widget.html', params, False)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		options['T_put_promocode'] = "PROMOCODE"
		options['namespace'] = ''
		options['booking_no_hide'] = True
		actual_namespace = get_namespace()
		general_dict = get_web_dictionary(language)

		if user_agent_is_mobile() and ('corpo' in get_namespace() or DEV):
			options['hotels_list'] = copy.deepcopy(self.get_hotels(language))
			for hotel in options['hotels_list']:
				hotel['value'] = hotel.get('title')
				hotel['name'] = hotel.get('title')
				hotel['id'] = hotel.get('namespace')
				hotel['url_booking'] = 'https://%s%s' % (hotel['id'], hotel['url_booking'])

			all_hotels_dict = {
				'value': general_dict.get('T_todos_hoteles_2'),
				'name': general_dict.get('T_todos_hoteles_2'),
				'id': actual_namespace,
				'class': actual_namespace,
				'namespace': actual_namespace,
				'app_ids': ';'.join(map(lambda x: x.get('namespace'), options['hotels_list'])),
				'url_booking': '%s/booking0' % get_config_property_value(KEY_DOMAIN)
			}

			options['hotels_list'] = [all_hotels_dict] + options['hotels_list']

			widget_options = get_section_from_section_spanish_name_with_properties('_widget_options', language)
			if widget_options and widget_options.get('resident_promocode'):
				general_dict['resident_promocode'] = widget_options.get('resident_promocode')
				options['custom_promocode_html'] = self.buildTemplate_2("_promocode_resident.html", general_dict, False,
																	TEMPLATE_NAME)
		elif 'corpo' in get_namespace() or DEV:
			mini_dict = dict(get_web_dictionary(language))
			mini_dict['hotels'] = self.get_hotels(language)
			options['applicationIds'] = ';'.join(map(lambda x: x.get('namespace'), mini_dict['hotels']))
			options['hotels_list_html'] = self.buildTemplate_2("_hotel_selector.html", mini_dict, False, TEMPLATE_NAME)

		return options


	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params, False)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		# args = {}
		# args['hotels'] = self.get_apartments_widget(language)
		# options['hotels_list_html'] = self.buildTemplate_2("banners/_hotel_selector_popup.html", args, False, TEMPLATE_NAME)
		return options

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		parent_data = super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate)
		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter_flexslider.html',
		}
		template = templateSectionsDict.get(sectionType, parent_data)
		return template

	def getParamsForSection(self, section, language):
		result = {}

		if section['sectionType'] == "Mis Reservas Corp":
			result = super(TemplateHandler, self).getParamsForSection(section, language)

			widget_apartments = self.get_apartments(language)
			for x in widget_apartments:
				if x.get("url_booking") and x.get("namespace"):
					build_url = x["url_booking"]
					split_url = build_url.split('/')
					only_host = split_url[0]
					x['domain'] = "https://" + x["namespace"] + only_host

				x['value'] = x.get("title")

			result['selectOptions'] = widget_apartments
			result['modify_reservation'] = True
			result['real_modify_reservation'] = True
			result['modify_reservation_content'] = True
			result['disable_content'] = True

		elif section['sectionType'] == "Mis Reservas":
			result = super(TemplateHandler, self).getParamsForSection(section, language)
			result['disable_content'] = True

		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def buildContentForSection(self, sectionFriendlyUrl, language,
							   sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
		currentSection = self.getSectionParams(sectionFriendlyUrl, language)

		if currentSection:
			if user_agent_is_mobile():
				html_to_render = self.getHtmlTypeSectionMobile(currentSection, language)

				if html_to_render:
					return html_to_render
				else:
					additionalParams['custom_elements'] = self.getHtmlExtraBannersMobile(currentSection, language)

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate,
																   additionalParams)

	def getHtmlTypeSectionMobile(self, section, language):
		section_type = section['sectionType']
		language_dict = get_web_dictionary(language)

		if section_type == "Habitaciones":
			return False

		elif section_type == "Ofertas":
			return False

	def getHtmlExtraBannersMobile(self, section, language):
		extra_banners = self.getExtraBanners(section, language)
		language_dict = get_web_dictionary(language)
		extra_banners.update(language_dict)
		extra_banners['is_mobile'] = True
		result = "<div class='section_content'>"

		result += self.buildTemplate_2('_main_content.html', extra_banners, False, TEMPLATE_NAME)

		result += "</div>"

		return result

	def buildNewsletter2(self, language, name=False, date=False, social=False, check_newsletter=False, background=True,fontawesome5=False):
		template_values = dict(get_web_dictionary(language))
		template_values['language_code'] = language.upper()
		template_values['language'] = get_language_code(language)
		template_values['name_in_form'] = name
		template_values['social'] = self.getSocialDictionary() if social else False
		template_values['background'] = background
		template_values['check_newsletter'] = check_newsletter
		template_values['newsletter_banner'] = get_section_from_section_spanish_name("_newsletter_banner", language)
		template_values['newsletter_custom_check'] = get_section_from_section_spanish_name("_newsletter_custom_check", language)
		template_values['fontawesome5'] = fontawesome5

		advance_newsletter = self.getSectionAdvanceProperties(template_values['newsletter_banner'], language)

		newsletter_thanks = get_config_property_value(NEWSLETTER_POPUP_THANKS)
		if newsletter_thanks:
			popup_thanks_section = get_section_from_section_spanish_name_with_properties(newsletter_thanks, language)
			template_values['newsletter_thanks'] = popup_thanks_section

		if advance_newsletter.get("email_placeholder"):
			template_values['T_introduce_email_placeholder'] = advance_newsletter.get("email_placeholder")

		if advance_newsletter.get("button_text"):
			template_values['T_enviar'] = advance_newsletter.get("button_text")

		content = self.buildTemplate_2('banners/_newsletter.html', template_values, False, TEMPLATE_NAME)

		return content

	def get_revolution_full_screen(self):
		return "on"

	def get_hotels(self, language):
		hotels = self.getPicturesProperties(language, "_hotel_selector")


		return hotels