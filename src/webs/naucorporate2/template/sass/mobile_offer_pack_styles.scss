//Base web (change too in template<PERSON>and<PERSON> and in config.rb)
$base_web: "nauil";
// colors definitions
$white: rgb(255,255,255);
$black: rgb(0,0,0);
$gray-1: rgb(90,90,90);
$gray-2: rgb(120,120,120);
$gray-3: rgb(190,190,190);
$gray-4: rgb(230,230,230);

// corporative colors definitions
$corporate_1: #897966;
$corporate_2: #199aa0;

// colors for booking widget
$booking_widget_color_1: $white;//body back ground & year input text color
$booking_widget_color_2: $corporate_1;//header background & input texts
$booking_widget_color_3: gray;//label texts
$booking_widget_color_4: gray;//not used, but must be defined


@mixin center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  -o-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
}

@mixin center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%,0%);
  -moz-transform: translate(-50%,0%);
  -ms-transform: translate(-50%,0%);
  -o-transform: translate(-50%,0%);
  transform: translate(-50%,0%);
}

@mixin center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%,-50%);
  -moz-transform: translate(0%,-50%);
  -ms-transform: translate(0%,-50%);
  -o-transform: translate(0%,-50%);
  transform: translate(0%,-50%);
}



/** Reservation button **/

.booking_general_button {
    margin-top: 10em;
    margin-bottom: 0.3em;
    padding: 2em;
    background: #ECEAEB;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/

.ui-state-default {
  border: 1px solid white !important;
}

#div-txt-copyright {
  color: gray;
  font-size: x-small;
}

.ui-datepicker-title {
  color: white !important;
}

.ui-widget-header {
  background: $corporate_1 !important;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $corporate_1 !important;
  color: white;
}

.cleardiv:before,
.cleardiv:after {
  content: " ";
  display: table;
}

.cleardiv:after {
  clear: both;
}

body {
  font-family: "Source Sans Pro", sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: $gray-1;

  strong {
    font-weight: bold;
  }

  em {
    font-style: italic;
  }

  i {
    font-style: italic;
  }

  sup {
    vertical-align: super;
    font-size: smaller;
  }

  small {
    font-size: smaller;
  }
}

img {
  vertical-align: bottom;
}

a {
  text-decoration: none;
}

h2.title-section {
  text-align: center;
  text-transform: uppercase;
  color: black;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 40px;
  background: url("/img/nauil/square.png?v=1") no-repeat center center;
  padding: 40px 0px;
}

.normal_content_wrapper .title-section {
  //display: none;
}

h3.incontent {
  color: #897966;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 10px;
  margin-top: 15px;
  font-size: 20px;
}

header {
  background: white;
}

header.ini-header {
  position: absolute;
  width: 100%;
  z-index: 333;
}

#logoDiv {
  padding: 20px 0px;
  margin-left: 0px;
}

.top-row {
  margin-bottom: 30px;
  padding-top: 20px;
}

#social,
#lang,
#top-sections,
.links-hidden {
  float: right;
  font-family: 'Droid Serif', serif;
  font-size: 14px;
  font-style: italic;
  a {
    color: $gray-1;
  }
  .separator {
    margin-left: 10px;
  }
}

#top-sections a {
  padding-left: 8px;
  line-height: 22px;

  &:hover,
  &#section-active {
    color: $corporate-1;
  }

  &:first-child {
    background: url("/img/nauil/bell.png") no-repeat left center;
    padding-left: 24px;
  }

  &:nth-of-type(2) {
    background: url("/img/nauil/location.png?v=2") no-repeat left center;
    padding-left: 24px;
  }
}

#social {
  padding-left: 30px;
  border-left: 1px solid $gray-3;
}

#lang {
  position: relative;
  color: $gray-1;
  font-weight: lighter;
  cursor: pointer;
  width: 115px;
  padding-left: 20px;
  border-left: 1px solid $gray-3;
  margin-left: 20px;
  line-height: 22px;

  &:hover {
    color: $corporate-1;
  }

  .lang_image {
    display: inline-block;
    vertical-align: middle;
  }

  span#selected-language {
    padding-left: 4px;
  }

  #language-selector-options {
    position: absolute;
    margin-top: 4px;
  }

  .arrow {
    display: inline-block;
    background: url(/img/nauil/arrow-down.png) no-repeat center center !important;
    float: right;
    width: 30px;
    height: 25px;
    margin-top: -2px;
    background-size: 17px !important;
    vertical-align: middle;
    margin-right: 6px;
  }

  #selected-language {

  }

  ul li {
    background: #ffffff;
    text-align: left;
    width: 80px;
    font-size: 11px;
    text-transform: uppercase;
    padding: 5px;
    cursor: pointer;
    display: block;
    border-bottom: 1px solid #EEE;
    color: #666;
    border-top: 1px solid #FFF;

    &:hover {
      border-bottom: 1px solid rgba(128, 128, 128, 0.33);
      background: #f0f0f0;
      width: 80px;
    }

    a {
      color: #666 !important;
      text-decoration: none !important;
    }
  }
}

.links-hidden {
  margin-right: 30px;
  line-height: 22px;
  position: relative;

  a:first-child {
    padding-left: 25px;
    background: url("/img/nauil/sobre-nosotros.png") no-repeat left center;
    cursor: pointer;
  }

  ul.hidden-links {
    display: none;
    position: absolute;
    background: white;
    padding: 10px;
    width: 100%;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    border: 2px solid lighten($corporate-1, 40%);

    a {
      background: none;
      padding-left: 10px;
    }
    a:hover {
      color: $corporate-1;
    }
  }
}

/*===== Menu =====*/

.bottom-row {
  clear: both;
}

#mainMenuDiv {
  background: $corporate-1;

  ul {
    padding: 20px;
    height: 20px;
    text-align: justify;
    justify-content: space-between;

    &:after {
      content: "";
      width: 100%;
      display: inline-block;
      height: 0;
    }

    .main-section-div-wrapper {
      display: inline-block;
      text-align: center;

      &:first-of-type {
        padding-left: 0;
      }

      &:last-of-type {
        padding-right: 0;
      }

      a {
        text-decoration: none;
        color: white;
        font-size: 13px;
        text-transform: uppercase;
        padding: 0px 5px 7px;
        border-bottom: 1px solid $corporate-1;
        font-weight: bold;

        &.button-promotion {
          color: white !important;
          background: $corporate_2;
          text-transform: uppercase;
          font-weight: bold;
          font-size: 11px;
          padding: 9px 7px;
        }
      }

      &:hover a {
        border-bottom: 1px solid white;
      }

      &#section-active a, {
        border-bottom: 1px solid white;
      }
    }

    .dot {
      display: inline-block;
      width: 4px;
      height: 4px;
      background: #f3f3f3;
      margin-left: -4px;
      margin-bottom: 3px;
    }
    .dot:last-child {
      display: none;
    }

  }
}

//******************** Slider ****************************//

#slider_container {
  position: relative;
}

#slider_inner_container {
  position: relative;

  .image-slider-fixed {
    height: 500px;
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-size: cover;
    position: relative;

    & > img {
      width: 100%;
      height: auto;
      position: fixed;
      top: 0px;
      z-index: -2;
      min-width: 1820px;
    }
  }
  .slogan-container {
    position: relative;
    height: 400px;
  }
  .slider_text {
    display: block;
    box-sizing: border-box;
    width: 100%;
    text-align: center;
    color: white;
    font-size: 57px;
    line-height: 70px;
    text-transform: uppercase;
    font-weight: lighter;
    text-shadow: 1px 0 8px #323232;
    position: absolute;
    top: 100px;
  }
}

.tp-bullets {
  bottom: 0px !important;
  opacity: 1 !important;
  z-index: 23 !important;
  width: 550px;
  padding: 10px;
  text-align: center;
}

.tp-bullets .bullet {
  background: url("/img/nauil/bullet_flexslider.png") no-repeat !important;
  background-position: center !important;
  display: inline-block !important;
  float: none !important;
}

.tp-bullets .bullet.selected {
  background: url("/img/nauil/bullet_flexslider_active.png") no-repeat center !important;
  background-size: cover;
}

.tp-leftarrow.default {
  background: url("/img/nauil/bx-left.png") no-Repeat center center;
  width: 45px;
  height: 45px;

  &:hover {
    background: rgba($corporate-1, .8) url("/img/nauil/bx-left.png") no-Repeat center center;
  }
}

.tp-rightarrow.default {
  background: url("/img/nauil/bx-right.png") no-Repeat 0 0;
  width: 45px;
  height: 45px;

  &:hover {
    background: rgba($corporate-1, .8) url("/img/nauil/bx-right.png") no-Repeat 0 0;
  }
}

//******************** Booking Engine ****************************//

//////BOOKING ENGINE

/* A few more changes in the new booking engine */

/** CALENDAR DATEPICKER**/

h4.booking_title_2 {
  display: block !important;
  font-size: 40px;
  line-height: 30px;
  margin-top: 8px;
}

h4.best_price {
  display: none;
}

h4.booking_title_custom {
  font-weight: 100;
  margin-bottom: -15px;
  text-transform: uppercase;
}

.ui-widget-header {
  background: $corporate_1;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $corporate_1;
  color: white;
}

.booking_widget {
  top: 157px;
  label {
    margin-left: 6px !important;
  }
}

.date_box {
  background-color: #edeef0;
}

.date_box .date_year {
  color: #808080;
}

.selectric {
  background-color: #edeef0;
}

.wrapper_booking_button .promocode_input {
  background-color: #edeef0;
  border-radius: 0px;
  width: 115px;
}

.promocode_input::-webkit-input-placeholder {
  color: transparent !important;
  &::before {
    content: "Promocode";
    color: #1b5360 !important;
    padding-left: 14px;
  }
}

.promocode_input::-moz-placeholder {
  color: #1b5360 !important;
}

.promocode_input:-moz-placeholder {
  color: #1b5360 !important;
}

.promocode_input:-ms-input-placeholder {
  color: #1b5360 !important;
}

.booking_widget.interior {
  top: 20px;
}

/*======== Ticks =======*/

.ticks-bar {
  background: lighten($corporate-1, 20%);
  color: white;
  overflow: hidden;
  text-align: center;
  font-family: 'Droid Serif', serif;

  .ticks-wrapper {
    position: relative;
  }

  .ticks {
    display: inline-block;
    font-size: 16px;
    font-weight: 200;
    margin: 0px 10px 0px 15px;
    line-height: 3;
    padding-left: 30px;
  }
  .tick_1 {
    background: url("/img/nauil/tick.png") no-repeat left;
  }
  .tick_2 {
    background: url("/img/nauil/tick.png") no-repeat left;
  }
  .tick_3 {
    background: url("/img/nauil/tick.png") no-repeat left;
  }
}

/*======== content =======*/

#content {
  background: white;
  padding-top: 40px;
}

.normal_content_wrapper {
  width: 930px;
  margin: 0 auto;

  p {
    margin-bottom: 20px;
  }
  .normal_description p > strong {
    font-weight: 600;
    color: black;
    font-size: 18px;
  }
  ul li {
    list-style-type: circle;
    margin-bottom: 4px;
  }
}
/*========  Destiny Banners =======*/

.destiny-wrapper {

  .block-destiny {
    width: 380px;
    height: 580px;
    float: left;
    overflow: hidden;
    position: relative;
    cursor: pointer;

    img {
      max-width: initial;
      -webkit-transition: all 0.6s ease;
      -moz-transition: all 0.6s ease;
      -ms-transition: all 0.6s ease;
      -o-transition: all 0.6s ease;
      transition: all 0.6s ease;
    }
    h3 {
      position: absolute;
      color: white;
      font-size: 30px;
      text-transform: uppercase;
      font-weight: 600;
      width: 100%;
      text-align: center;
      bottom: 230px;
      z-index: 3;
    }
  }
  .block-destiny:hover img {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1);
  }
}
/*======== Destinos =======*/

.block-destino {
  margin-bottom: 40px;

  .description {
    background: #f7f7f7;
    padding: 30px;
  }
  .description p {
    margin-bottom: 10px;
  }
  .description h2 {
    color: $corporate-1;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 10px;
    margin-top: 15px;
    font-size: 20px;
  }
  .description h3 {
    font-size: 18px;
    color: $corporate-1;
    margin-bottom: 8px;
    margin-top: 20px;
    text-transform: uppercase;
  }
  .description h4 {
    font-weight: bold;
    margin-bottom: 5px;
  }
  .show-destiny {
    text-align: center;
    margin-top: 20px;

    a {
      display: inline-block;
      padding: 8px 25px;
      background: $corporate-1;
      color: white;
      font-size: 18px;
    }
    a:hover {
      background: darken($corporate-1, 20%);
    }
  }

  .flex-3 {
    position: relative;

    .slides li {
      position: relative;
    }
    .slides li h4,
    .slides li h5 {
      position: absolute;
      color: white;
      left: 40px;
    }
    .slides li h4 {
      background: $corporate-1;
      padding: 15px 30px;
      font-size: 22px;
      font-family: 'Droid Serif', serif;
      top: 40px;
    }
    .slides li h5 {
      background: #010101;
      padding: 10px 20px;
      font-size: 18px;
      font-weight: 600;
      text-transform: uppercase;
      top: 95px;
    }
  }
  .flex-direction-nav {
    position: absolute;
    top: 50%;
    width: 100%;

    .flex-nav-prev {
      position: absolute;
      left: 20px;
    }
    .flex-nav-next {
      position: absolute;
      right: 20px;
    }

    .flex-prev {
      display: inline-block;
      background: url("/img/nauce/bx-left.png") no-repeat;
      color: transparent;
      width: 46px;
      height: 46px;
    }
    .flex-next {
      display: inline-block;
      background: url("/img/nauce/bx-right.png") no-repeat;
      color: transparent;
      width: 46px;
      height: 46px;
    }
  }
}

/*======== Main Banners =======*/

.main-banners {
  padding: 40px 0;
  max-width: 1900px;
  margin: 0 auto;
}

.first-line {

  .banner-1,
  .banner-2 {
    margin-left: 0px;
    margin-right: 0px;
    width: 50%;
    height: 360px;
    overflow: hidden;
    position: relative;

    img {
      position: absolute;
      left: -100%;
      right: -100%;
      top: -100%;
      bottom: -100%;
      margin: auto;
      min-height: 100%;
      min-width: 100%;
      max-width: initial;
    }
    .overlay {
      width: 100%;
      height: 360px;
      position: absolute;
      top: 0px;
      background: black;
      opacity: .25;
      transition: opacity 0.3s linear;
    }
    &:hover .overlay {
      opacity: 0;
    }
    .header {
      position: absolute;
      top: 40px;
      left: 30px;
      color: white;
    }
    h2 {
      text-transform: uppercase;
      font-size: 20px;
    }
    h4 {
      font-size: 28px;
      font-family: 'Droid Serif', serif;
    }
    .price {
      position: absolute;
      bottom: 30px;
      left: 30px;
      font-size: 20px;
      text-transform: uppercase;
      color: white;
    }
    .number {
      font-size: 60px;
      font-weight: 600;
      line-height: 52px;
    }
    .persona {
      font-size: 16px;
    }
  }
}

.second-line {

  .banner-1,
  .banner-2 {
    margin-left: 0px;
    margin-right: 0px;
    height: 360px;
    overflow: hidden;
    position: relative;
  }
  .banner-1 {
    width: 25%;
  }
  .banner-2 {
    width: 75%;
  }
  img {
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    max-width: initial;
  }
  .banner-1 .overlay {
    width: 100%;
  }
  .banner-2 .overlay {
    width: 100%;
  }
  .overlay {
    height: 360px;
    position: absolute;
    top: 0px;
    background: black;
    opacity: .25;
    transition: opacity 0.3s linear;
  }
  .banner-1, .banner-2 {

    &:hover .overlay {
      opacity: 0;
    }

  }

  .header {
    position: absolute;
    top: 40px;
    left: 30px;
    color: white;
  }
  h2 {
    text-transform: uppercase;
    font-size: 20px;
  }
  h4 {
    font-size: 28px;
    font-family: 'Droid Serif', serif;
  }
}

/*======== Full Banners =======*/

#full-banners {
  padding: 40px 0px;

  .full-banners-wrapper {
    background: black;
    img {
      min-width: 100%;
      min-height: 550px;
      max-width: none;
    }
  }

  .full-banner-1 {
    width: 45%;
  }
  .full-banner-2 {
    width: 55%;
  }
  .full-banner-1,
  .full-banner-2 {
    float: left;
    max-height: 550px;
    overflow: hidden;
    position: relative;
  }
  .title {
    position: absolute;
    background: $corporate-1;
    color: white;
    left: 0px;
    top: 220px;
    padding: 10px 30px;
    font-size: 20px;
    font-family: 'Droid Serif', serif;
  }
  .link {
    position: absolute;
    background: $gray-1 url("/img/nauil/arrow-link.png") no-repeat right center;
    color: white;
    top: 260px;
    padding: 8px 70px 8px 20px;

    &:hover {
      background: darken($corporate-1, 20%) url("/img/nauil/arrow-link.png") no-repeat right center;
    }
  }
  .description {
    position: absolute;
    bottom: 0px;
    background: rgba(255, 255, 255, 0.93);
    padding: 40px;
    width: 400px;
    height: 250px;
    font-size: 16px;
    right: 0px;

  }
}

/*========  news banners =======*/
.news-banners {
  padding-top: 60px;
  padding-bottom: 20px;
  background: white;
}

.news-row {
  background: #f3f1f2;
  padding-top: 20px;

  .block {
    width: 560px;
  }
  .block:first-child {
    margin-left: 0px;
  }
  .block:last-child {
    margin-right: 0px;
  }
  .block .image-box {
    height: 300px;
    width: 560px;
    overflow: hidden;
    position: relative;

    img {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
    }
  }
  .block .description-box {
    padding: 20px 15px;

    h3 {
      text-transform: uppercase;
      font-size: 20px;
      color: $gray-2;
      margin-bottom: 10px;
      font-weight: 600;
    }
    p {
      font-weight: 14px;
      font-weight: 300;
    }
    a {
      display: inline-block;
      background: white url("/img/nauil/arrow-link.png") no-repeat 90% center;
      padding: 6px 80px 6px 15px;
      margin-top: 20px;
      color: black;
      font-weight: 600;
    }
  }
}

/*========  Opinions Preview =======*/

.opinion-preview {
  padding: 40px;

  .see-more-opinions {
    float: right;
    color: $gray-1;
    text-transform: capitalize;
    padding-right: 80px;
    margin-bottom: 15px;
    background: url("/img/nauil/arrow-link.png") no-repeat 90% center;
  }
  .see-more-opinions:hover {
    color: $corporate-1;
  }

}

.opinion-table {
  border-top: 1px solid $gray-1;
  border-bottom: 1px solid $gray-1;
  padding: 5px 0px;
  display: table;
  clear: both;
}

.opinion-table .author {
  display: table-cell;
  vertical-align: middle;
  width: 150px;
  border-right: 1px solid $gray-1;
}

.opinion-table .opinion-content {
  width: 830px;
  padding: 20px;
  display: table-cell;
  vertical-align: middle;
  border-right: 1px solid $gray-1;
}

.opinion-table .value {
  width: 150px;
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  font-size: 44px;
  font-style: italic;
  color: rgb(42, 47, 106);
  font-weight: bold;
}

/*========  Banners Premios =======*/

#banners-premios {

  padding: 40px 0px;
  background: white;

  ul {
    text-align: center;
    justify-content: space-between;
    height: 160px;
    padding-top: 10px;
    box-sizing: border-box;
    margin-top: 0 !important;
  }
  ul:after {
    content: '';
    display: inline-block;
    width: 100%;
    height: 0;
  }
  li {
    display: inline-block;
    text-align: center;
    margin-right: 60px;
  }
  p {
    color: $gray-2;
    margin-top: 10px;
    font-size: 14px;
  }
}

/*================  Rooms ===============*/

.rooms-banner {
  text-align: left;
  margin: 60px 0px;

  .banner-icon {
    box-sizing: border-box;
    display: inline-block;
    text-align: center;
    padding: 0 20px;
    border-right: 1px solid $gray-1;
    height: 138px;
    vertical-align: middle;
  }
}

.wrapper-rooms {

  .room-block {
    height: 360px;
    margin-top: 30px;
    background: #f4f4f4;
  }
  .box-images {
    width: 540px;
    height: 360px;
    overflow: hidden;
    float: left;
  }
  .bx-viewport {
    height: 360px !important;

    .bxslider-rooms li {
      width: 540px !important;
    }
  }
  //Controls pager circles
  .bx-controls {
    position: relative;
    top: -30px;
  }
  .bx-pager {
    text-align: right;
    margin-right: 20px;
  }
  .bx-pager .bx-pager-item {
    display: inline-block;

    a {
      background: url("/img/nauil/bullet_flexslider.png") no-repeat center center;
      color: transparent;
      display: block;
      width: 15px;
      height: 15px;
      margin: 0 4px;
      outline: 0;
      -moz-border-radius: 9px;
      -webkit-border-radius: 9px;
      border-radius: 7px;
    }
    a.active {
      background: url("/img/nauil/bullet_flexslider_active.png") no-repeat center center;
    }
  }

  //Controls pager Next-Prev
  .bx-controls-direction {
    position: relative;
    top: -185px;

    .bx-prev {
      color: transparent;
      position: absolute;
      left: 20px;
      width: 46px;
      height: 46px;
      background: url("/img/nauil/bx-left.png") no-repeat;

      &:hover {
        background: rgba($corporate-1, .8) url("/img/nauil/bx-left.png") no-Repeat center center;
      }
    }
    .bx-next {
      color: transparent;
      position: absolute;
      right: 20px;
      width: 46px;
      height: 46px;
      background: url("/img/nauil/bx-right.png") no-repeat;

      &:hover {
        background: rgba($corporate-1, .8) url("/img/nauil/bx-right.png") no-Repeat center center;
      }
    }
  }

  .box-description {
    padding: 30px;
    width: 600px;
    height: 360px;
    box-sizing: border-box;
    float: right;
    position: relative;

    h3 {
      color: $corporate-1;
      font-family: 'Droid Serif', serif;
      font-size: 24px;
      margin-bottom: 20px;
      font-style: italic;
    }

    ul.service-preview {
      margin: 20px 0px;
      width: 350px;
    }
    ul.service-preview li {
      padding-left: 30px;

      &.room_ico {
        background: url("/img/nauil/bed.png") no-repeat left center;
        background-size: 28px;
      }

      &.person_ico {
        background: url('/img/nauil/persons_room.png') no-repeat left center;
        background-size: 28px;
      }
    }

    .price-room {
      width: 162px;
      position: absolute;
      right: 20px;
      bottom: 90px;
      display: none;

    }
    .price-room p {
      color: lighten($corporate-1, 20%);
      font-weight: 300;
    }
    .price-room .number {
      font-size: 34px;
      color: darken($corporate-1, 20%);
    }
    .price-room .night {
      font-size: 16px;
      color: darken($corporate-1, 20%);
      text-transform: uppercase;
      font-weight: 300;
    }
  }
  .booking-button {
    color: white;
    display: inline-block;
    background: #b57401;
    text-transform: uppercase;
    font-size: 22px;
    padding: 10px 25px;
    position: absolute;
    bottom: 30px;
    right: 30px;
    letter-spacing: 2px;

    &:hover {
      background: darken($corporate-1, 20%);
    }
  }
  .see-more-room {
    color: lighten($corporate-1, 10%);
    padding-left: 20px;
    padding-right: 25px;
    background: url("/img/nauil/arrow-down.png") no-repeat right 1px;
    font-weight: 300;
  }
  .see-more-room:hover {
    color: darken($corporate-1, 20%);
  }
  .see-less-room {
    color: $corporate-1;
    clear: both;
    display: block;
    text-align: center;
    width: 181px;
    margin: 0 auto;
    margin-top: 40px;
    background: url("/img/nauil/arrow-top.png") no-repeat right 1px;
  }
  .see-less-room:hover {
    color: darken($corporate-1, 20%);
  }
  .services-hidden {
    background: lighten($corporate-1, 40%);
    padding: 30px 0 30px 70px;
    box-sizing: border-box;

    .service-block {
      display: inline-block;
      text-align: left;
      width: 28%;
      padding-right: 5%;
      vertical-align: top;
    }
    .title-service {
      text-transform: uppercase;
      margin-bottom: 15px;
      color: darken($corporate-1, 20%);
    }
  }
  .more-number-rooms {
    text-align: center;
    margin-top: 20px;
    text-transform: uppercase;
    color: $corporate-1;

    p {
      width: auto;
      padding: 10px;
      cursor: pointer;
    }
  }
  .more-number-rooms:hover {
    color: darken($corporate-1, 20%);
  }
}

/*========  Promotions Page =======*/

.offers-filter {
  background: white;
  text-align: center;
  padding: 30px 0px 15px;

  li.inline {
    display: inline-block;
    padding: 10px 30px;
    background: white;
    color: #b7ac9e;
    text-transform: uppercase;
    font-size: 16px;
    font-weight: 600;
    margin: 0px 5px;
    cursor: pointer;
    text-align: center;
    border: 2px solid #897966;
  }
  li.all-promos {
    display: table;
    margin: 15px auto 0px;
    font-family: 'Droid Serif', serif;
    padding-bottom: 5px;
    border-bottom: 1px solid black;
    cursor: pointer;
  }

  li.inline:hover {
    color: white;
    background: lighten($corporate-1, 20%);
    //box-shadow: 3px 3px 1px white;
  }
  .filter-hover {
    color: white !important;
    background: lighten($corporate-1, 20%) !important;
    //box-shadow: 3px 3px 1px white !important;
  }
}

.promotions-wrapper {
  width: 1150px;
}

.promotions-wrapper .mix {
  display: none;
}

.block-promo {
  width: 373px;
  float: left;
  margin-right: 10px;
  margin-bottom: 10px;
  position: relative;
  padding-bottom: 20px;

  .image-box {
    height: 185px;
    position: relative;
    overflow: hidden;
  }
  .image-box img {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
  }
  .description-box {
    padding: 20px 20px 60px;
    border: 1px solid $gray-3;
  }
  h3 {
    color: $corporate-1;
    font-size: 18px;
    margin: 10px 0;
    letter-spacing: 2px;
  }
  .intro {
    color: black;
    font-weight: 700;
    font-size: 16px;
    margin-bottom: 20px;
  }
  .description {
    margin-bottom: 20px;
    font-size: 14px;
  }
  a {
    color: $corporate-1;
    font-size: 18px;
  }
  a:hover {
    color: darken($corporate-1, 20%);
    text-decoration: underline;
  }

  strong {
    display: block;
    color: #897966;
    font-size: 21px;
    font-weight: lighter;

    price {
      font-size: 32px;
      font-weight: 700;
      margin-left: 5px;
    }
  }

  hide {
    display: none;
  }

  a {
    display: block;
    margin-top: 25px;
  }

  a.button-promotion {
    position: absolute;
    bottom: 0;
    width: 100%;
    text-align: center;
    left: 0;
    padding: 12px 20px;
    box-sizing: border-box;
    color: white;
    background: #897966;
    text-transform: uppercase;
    font-size: 22px;
    font-weight: lighter;
    letter-spacing: 1px;
    margin-top: 0;
  }

  .see_more_offer {
    position: absolute;
    bottom: 0;
    width: 100%;
    text-align: center;
    left: 0;
    padding: 12px 20px;
    box-sizing: border-box;
    color: white;
    background: #897966;
    text-transform: uppercase;
    font-size: 22px;
    font-weight: lighter;
    letter-spacing: 1px;
    margin-top: 0;

    &:hover {
      color: white;
    }
  }
}

.promo-modal {
  h3 {
    text-transform: uppercase;
    color: $corporate-1;
    margin-bottom: 10px;
  }
  .intro {
    color: black;
    font-size: 17px;
    margin-bottom: 5px;
  }

  strong {
    display: block;
    color: #897966;
    font-size: 21px;
    font-weight: lighter;

    price {
      font-size: 32px;
      font-weight: 700;
      margin-left: 5px;
    }
  }

  hide a {
    margin-top: 30px;
    color: white;
    background: #897966;
    padding: 10px 30px;
    display: table;
    width: auto;
  }
}

//*************** Opiniones ****************//

.opinions .header, .opinions-total .header {
  background-color: $corporate-1;
  padding: 20px;
  position: relative;
  color: white;

  img {
    position: absolute;
    top: 20px;
    left: 20px;
  }

  h3 {
    margin-left: 52px;
    text-transform: uppercase;
    font-size: 22px;
  }
  p {
    font-size: 14px;
    margin-left: 52px;
    line-height: 14px;
    margin-top: 2px;
  }
}

.opinions-total {
  margin: 40px 0 40px 0;
  background: lighten($corporate-1, 40%);
}

.opinions {
  width: 379px !important;
  margin-left: 0px;
  margin-right: 0px;
}

.opinions .value, .opinions-total .value {
  text-align: center;
  padding: 14px 0;
  border-top: 2px solid white;
  font-size: 16px;
  text-transform: uppercase;
  color: $corporate-1;

  .media {
    font-size: 30px;
    font-family: nexabold;
    color: $corporate-1;
    margin-right: 10px;
  }
}

.opinions-button {
  position: absolute;
  right: 20px;
  top: 17px;
  display: block;
  background: white;
  color: $corporate-1;
  padding: 10px;
}

.opinions-button:hover {
  color: white;
  background: darken($corporate-1, 20%);
}

.opinions .coment, .opinions-total .coment {
  background: $corporate-2;

  padding: 15px 0;
  border-top: 1px solid white;
  position: relative;

  .plus-link {
    position: absolute;
    right: 15px;
    top: 12px;
    background-color: $corporate-1;
    padding: 8px 8px 0 !important;

    &:hover {
      background-color: $corporate-2;
    }
  }
  span {
    font-size: 12px;

    p {
      display: inline;
    }
  }
  .calification {
    color: white;
    background-color: $gray-2;
    padding: 10px 10px 9px;
    margin-right: 20px;
    margin-left: 15px;
    font-size: 14px;
  }
}

.opinions-total table {
  width: 100%;

  tr {
    background-color: lighten($corporate-1, 40%);;
    border-top: 2px solid white;
    color: $gray-1;

    .name {
      text-transform: uppercase;
      width: 250px;
      border-right: 2px solid white;
      padding: 20px;
    }
    .opinion-description {
      width: 800px;
      padding: 20px;
    }
    .calification {
      border-left: 2px solid white;
      vertical-align: middle;
      padding: 20px;

      span {
        color: white;
        background-color: #787878;
        padding: 10px 10px 9px;
        font-size: 14px;
      }
    }
    p {
      margin-bottom: 0;
    }
  }
}

.form-general {
  padding: 20px 20px 0;
  background: $white;
  margin-top: 20px;
  overflow: hidden;

  h3 {
    margin-bottom: 20px;
  }

  li {
    display: inline-block;
    width: 268px;
    margin-bottom: 10px;

    label {
      display: block;
      font-size: 12px;
      color: $gray-1;
    }
    input, textarea {
      border: none;
      width: 254px;
      padding: 10px 5px;
      background: $gray-4;
    }

    select {
      width: 265px;
      background-color: white;
      padding: 10px 5px;
      height: 35px;
      border: none;
      -webkit-appearance: none;
      border-radius: 0;
      background: $gray-4;
    }
    textarea {
      height: 13px;
      width: 524px;
    }

    #check_from, #check_till {
      background: white url("/img/holiy/calendar.png") no-repeat 235px;
    }
    #check_date, #end_date {
      background: white url("/img/holiy/calendar.png") no-repeat 100px;
    }
  }
  li.comment-box {
    width: 100%;
  }
  .short {
    width: 132px;

    input {
      width: 118px;
    }
  }
  a {
    color: white;
    margin-top: 10px;
    display: inline-block;

  }
  .btn-corporate {
    font-size: 14px;
    padding: 5px 10px 2px;
    border-radius: 3px;
    cursor: pointer;
    float: right;
    background: $corporate-2;
  }
  span a {
    color: $corporate-1;
    font-size: 12px;
  }
  .form-bottom {
    display: inline-block;
    width: 400px;

    p {
      margin-bottom: 0;
      line-height: 15px;
    }
    label.error {
      display: none !important;
    }
  }
  .last {
    margin-top: 38px;

    .form-bottom {
      margin-top: 10px;
    }
  }

  .double_li_form {

    width: 536px;
    input {
      width: 526px;
    }

  }

}

.form-general.form-opinion li {
  display: block !important;
}

.form-general.form-opinion .btn-corporate {
  float: none !important;
  margin-bottom: 20px;
}

/*===============  Location and Contact ==============*/

.map-location {
  margin-top: 100px;
  margin-bottom: 60px;
  position: relative;

  .left-band {
    position: absolute;
    width: 320px;
    background: #f7f7f7;
    height: 480px;
    top: 0px;
  }

  a {
    background: $corporate-1;
    color: white;
    padding: 3px 12px;
    position: absolute;
    right: 0px;
    bottom: 4px;
    font-size: 14px;
  }
  a:hover {
    background: darken($corporate-1, 20%);
  }
}

.contact-blocks-wrapper {

  &.first {
    border-right: 1px solid $gray-1;
    padding: 20px 60px 0 0;
    box-sizing: border-box;
    height: 275px;
  }

  &.second {
    box-sizing: border-box;
    padding: 20px 0px 0px 40px;
  }

  .contact-block {
    float: left;
    width: 339px;
    height: 110px;

    img {

    }
  }
  .contact-block:last-child {
    margin-top: -30px;
  }

  .block-contact {
    margin-bottom: 20px;
    margin-left: 43px;
    text-align: left;
  }

  h4 {
    color: $corporate-1;
    font-size: 16px;
    text-transform: uppercase;
    margin-bottom: 10px;
    font-weight: bold;
  }
  p {
    font-size: 14px;
  }
  .image-contact {
    float: left;
  }
}

.form-contact-location {
  box-sizing: border-box;
  background: lighten($corporate-1, 40%);
  padding: 40px;
  margin-top: 60px;
  clear: both;

  .title-form {
    text-align: center;
    margin-bottom: 20px;
  }
  h3 {
    text-align: center;
    text-transform: uppercase;
    margin-bottom: 20px;
    font-size: 20px;
  }
  h3 + span {
    display: inline-block;
    width: 60px;
    border-bottom: 5px solid $corporate-1;
  }
  .contInput {
    margin-bottom: 20px;
    display: inline-block;
  }
  .contInput.full {
    display: block;
    margin-bottom: 10px;
  }
  .small {
    margin-left: 40px;
  }
  label {
    display: block;
    text-transform: uppercase;
    margin-bottom: 3px;
  }
  .big input {
    width: 700px;
  }
  .small input {
    width: 316px;
  }
  input {
    height: 40px;
    border: none;
    border-bottom: 2px solid darken($corporate-1, 20%);
    padding: 10px;
    box-sizing: border-box;
  }
  textarea {
    width: 100%;
    padding: 0px;
    border: none;
    border-bottom: 2px solid darken($corporate-1, 20%);
    box-sizing: border-box;
    padding: 10px;
  }
  button {
    width: 150px;
    height: 40px;
    background: $corporate-1;
    color: white;
    text-transform: uppercase;
    font-size: 16px;
    border: none;
    outline: none;
    cursor: pointer;

    &:hover {
      background: darken($corporate-1, 20%);
    }
  }
  .required-fields {
    display: inline-block;
    float: right;
    font-style: italic;
  }
  .policy-terms input {
    height: 13px;

  }
  .policy-terms a {
    color: lighten($corporate-1, 10%);
  }
  .error {
    color: red;
    font-size: 14px;
    text-transform: lowercase;
  }
}

.left-band {
  .date, .time, .today_text {
    text-align: center;
    margin-top: 35px;
    font-size: 19px;
    font-weight: lighter;
  }

  .date {
    margin-top: 3px;
  }

  .time {
    font-weight: bolder;
    margin-top: 5px;
  }

  .today_text {
    text-transform: uppercase;
    font-size: 33px;
    font-weight: 600;
    margin-top: 50px;
  }

  img.weather-icon {
    display: block;
    margin: auto;
  }

  .temp {
    display: block;
    text-align: center;
    font-size: 40px;
    color: $corporate-1;
    margin-top: 10px;
  }
}

/*============= Mis Reservas  ===========*/

h3.section-title {
  text-align: center;
  text-transform: uppercase;
  color: black;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 40px;

  & + div {
    text-align: center;
    font-size: 16px;
    line-height: 22px;
    color: #646464;
  }
}

form#my-bookings-form {
  margin-top: 30px;
}

.booking-content {
  text-align: center;
  margin-bottom: 20px;
}

#my-bookings-form-fields {
  text-align: center;

  label {
    display: block;
    color: $corporate-1;
    text-transform: uppercase;
  }
  input, .bordeSelect {
    display: block;
    margin: 0 auto;
    margin-bottom: 10px;
    width: 250px;
    height: 15px;
    padding: 5px;
    text-align: center;
    color: black;
    border: none;
    background: $gray-4;
  }

  #my-bookings-form-search-button {
    width: 260px;
    color: white;
    background-color: $corporate-1;
    padding: 10px 0;
    border: none;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: darken($corporate-1, 20%);
    }
  }
}

button#cancelButton {
  width: 260px;
  color: white;
  background-color: $corporate-1;
  padding: 10px 0;
  border: none;
  font-size: 14px;
  cursor: pointer;

  &:hover {
    background-color: $corporate-2;
  }
}

#cancel-button-container {
  text-align: center;

  #cancelButton {
    width: 260px;
    color: white;
    background-color: $corporate-1;
    padding: 10px 0;
    border: none;
    font-size: 14px;
    cursor: pointer;
    display: none;
    margin-left: 470px;

    &:hover {
      background-color: $corporate-2;
    }
  }
}

#reservation div.grid_12.alpha.my-bookings-booking-info {
  margin-left: 320px !important;
}

.section-title + div {
  color: #918f8f;
  font-weight: 400;
  font-size: 14px;
  line-height: 26px;
  text-align: center;
  margin-top: 25px;
}

.content-access h3.section-title {
  color: $corporate_1;
  font-weight: lighter;
  font-size: 34px;
  text-transform: uppercase;
  margin-bottom: 29px;
  text-align: center;

  strong {
    font-weight: bolder;
  }
}

/*===============  Gallery Page ==============*/

.gallery-filter-wrapper {

  margin-top: 60px;

  .filter-list-images {
    text-align: center;
    margin-bottom: 60px;
  }
  .filter-list-images li {
    display: inline-block;
    padding: 5px 15px;
    background: white;
    color: lighten($corporate-1, 20%);
    text-transform: uppercase;
    font-size: 16px;
    font-weight: 600;
    margin: 0px 5px;
    cursor: pointer;
    text-align: center;
    border: 2px solid $corporate-1;
  }
  .filter-list-images li:hover,
  .filter-list-images li.active {
    background: lighten($corporate-1, 20%);
    color: white;
  }

}

.gallery-page-wrapper .mix {
  display: none;
}

.gallery-page-wrapper ul li {
  width: 285px;
  height: 190px;
  overflow: hidden;
  float: left;
  position: relative;

  img {
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    opacity: 1;
    transition: all 0.7s ease;
  }
  img:hover {
    opacity: .7;
  }
}

/*===============  Customiced ==============*/
//banners top
.bannerx3-wrapper {
  margin-top: 40px;
  margin-bottom: 20px;
  text-align: center;

  .bannerx3 {
    height: 170px;
  }

  h4 {
    font-size: 20px;
    font-family: "Droid Serif";
    color: $corporate-1;
    margin-bottom: 15px;
  }
  p {
    width: 250px;
    margin: 0 auto;
  }
}

//flexlider
.slider-section {
  position: relative;
  padding: 40px 0px;
  background: white;

  .flex-controlador {
    text-align: right;
    padding: 10px 0;

    li {
      display: inline-block;

      span {
        background: #897966;
        display: block;
        width: 10px;
        height: 10px;
        cursor: pointer;
        margin: 2px;
        -webkit-border-radius: 20px;
        -moz-border-radius: 20px;
        border-radius: 20px;
        position: relative;

        &.flex-active:before {
          content: '';
          padding: 7px;
          -webkit-border: 1px solid #897966;
          -moz-border: 1px solid #897966;
          border: 1px solid #897966;
          position: absolute;
          top: -3px;
          bottom: 0;
          left: -3px;
          right: 0;
          border-radius: 20px;
        }
      }
    }
  }
}

.flex-2 {
  height: 450px;
  position: relative;

  li {
    height: 450px;
    position: relative;
  }
  li img {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
  }

  .flex-direction-nav {
    position: absolute;
    top: 46%;
    width: 100%;

    .flex-nav-prev {
      position: absolute;
      left: -50px;
    }
    .flex-nav-next {
      position: absolute;
      right: -50px;
    }

    .flex-prev {
      display: inline-block;
      background: url("/img/nauil/flex-arrow-left.png") no-repeat;
      color: transparent;
      width: 33px;
      height: 56px;
    }
    .flex-next {
      display: inline-block;
      background: url("/img/nauil/flex-arrow-right.png?v=1") no-repeat;
      color: transparent;
      width: 33px;
      height: 56px;
    }
  }
}

.bg-dark {
  height: 300px;
  background: lighten($corporate-1, 40%);
  position: absolute;
  width: 100%;
  top: 50%;
  transform: translateY(-50%);
}

//blocks

.blocks-page-full, .blocksx4-page-full {
  background: white;
  padding: 40px 0px;
}
.blocks-page, .blocksx4-page {
  margin-top: 40px;

  .block-promo {
    width: 373px;
    float: left;
    margin-right: 10px;
    margin-bottom: 10px;
  }
  .block-promo .image-box {
    height: 185px;
    position: relative;
    overflow: hidden;
  }
  .block-promo .image-box img {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
  }
  .block-promo .description-box {
    height: auto;
    padding: 20px;
    border: 1px solid #bebebe;
  }
  .block-promo h3 {
    color: #897966;
    font-size: 18px;
    font-family: 'Droid Serif', serif;
    margin: 10px 0px;
  }
  .block-promo:nth-child(4n+4) {
    margin-right: 0px;
  }
}

.blocksx4-page {
  margin-top: 40px;
  .block-promo {
    width: calc(25% - 10px);
  }
  .block-promo .image-box {
    height: 137px;
  }
  .block-promo .image-box img {
    @include center_xy();
    bottom: auto;
    min-width: 100%;
    min-height: 100%;
    max-width: inherit;
  }
}

//Paralax

.paralax {
  height: 300px;
  background-repeat: no-repeat;
  background-size: cover;
  background-attachment: fixed;
}

.paralax-icons {

  .block {
    color: white;
    text-align: center;
    text-transform: uppercase;
    font-size: 20px;
    font-weight: bold;
    height: 300px;
    padding-top: 90px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
  .block h3 {
    margin-top: 10px;
    font-weight: 300;
  }

  img {
    width: 100px;
  }
}

/*========  Golf Individual =======*/

.table-golf {
  background: rgb(233, 232, 230);
  padding: 40px 0px;
  text-align: center;

  h2 {
    font-size: 26px;
    color: $corporate-1;
    font-weight: bold;
    margin-bottom: 40px;
  }

}

.contact-golf {

  background: white;

  .block-contact {
    float: left;
    width: 328px;
    height: 366px;
    box-sizing: border-box;
    padding: 40px;
    background: lighten($corporate-1, 40%);

    h3 {
      font-size: 26px;
      color: $corporate-1;
      margin-bottom: 20px;
      font-weight: bold;
    }
  }
  .map {
    float: left;
    width: 812px;
  }

}

.tarifas-golf, .membership_link {
  margin-bottom: 40px;
  margin-top: 20px;
  display: inline-block;
  margin-right: 30px;

  a {
    text-transform: uppercase;
    padding: 10px 30px;
    background: lighten($corporate-1, 20%);
    color: white;
    box-shadow: 5px 5px 1px $corporate-1;
  }
}

p.to_pdf {
  margin-top: 40px;
}

p.to_pdf a {
  text-transform: uppercase;
  padding: 10px 30px;
  background: lighten($corporate-1, 20%);
  color: white;
  box-shadow: 5px 5px 1px $corporate-1;
  margin-left: 10px;
}

.info-tecnica,
.services-golf {
  width: 450px;
  float: left;

  h3 {
    font-size: 24px;
    color: $corporate-1;
    margin-bottom: 30px;
  }
  p {
    margin-bottom: 10px;
  }
}

.info-tecnica {

  strong {
    font-weight: bold;
  }

  td {
    padding: 5px 10px 5px 20px;
  }

  .white {
    background: url("/img/nauil/tee-blanco.png") no-repeat left center;
  }
  .yellow {
    background: url("/img/nauil/tee-amarillo.png") no-repeat left center;
  }
  .red {
    background: url("/img/nauil/tee-rojo.png") no-repeat left center;
  }
  .blue {
    background: url("/img/nauil/tee-azul.png") no-repeat left center;
  }
  .first-td {
    padding: 5px 10px 5px 0px;
  }
}

/*========  Footer =======*/

footer {
  background: rgb(55, 56, 58);
  color: white;
  text-align: center;

  a {
    color: $gray-4;
    font-size: 14px;
  }

  .first-row {
    text-align: center;
    padding: 20px 0px;
    border-bottom: 1px solid $gray-3;
  }
}

.newsletter-label {
  display: inline-block;
  margin-right: 20px;
  font-weight: 300;
}

#newsletter {
  display: inline-block;

  h2 {
    display: none;
  }
  #suscEmailLabel {
    display: none !important;
  }
  #suscEmail {
    border: none;
    background: white;
    width: 240px;
    height: 30px;
    color: $gray-1;
    padding-left: 10px;
  }
  #newsletterButtonExternalDiv {
    float: right;

    button {
      border: none;
      background: $corporate-1 url("/img/nauil/arrow-mini-right.png?v=1") no-repeat center center;
      height: 32px;
      width: 42px;
      color: transparent;
      cursor: pointer;
      margin-right: 4px;
    }
    button:hover {
      background: darken($corporate-1, 20%) url("/img/nauil/arrow-mini-right.png?v=1") no-repeat center center;
      color: transparent;
    }
  }
}

.wrapper_footer_columns {
  margin-bottom: 40px;

  .footer_content {
    text-align: center;
    min-height: 240px;
    padding: 40px 20px 0px 20px;
    box-sizing: border-box;
    border-right: 1px solid $gray-3;

    #google_plus_one {
      margin-left: 20px;
    }

    div {
      width: 100%;
    }
  }
}

.footer_column {
  min-height: 240px;
  padding: 40px 20px 0px 20px;
  border-right: 1px solid $gray-3;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  text-align: left;

  .property-link {
    margin-top: 20px;

    a {
      text-transform: uppercase;
      font-weight: bold;
      font-size: 16px !important;
    }
    a:hover {
      color: $corporate-1;
    }
  }

  a {
    color: white;
    font-size: 12px;
  }
  .footer_column_title {
    text-transform: uppercase;
    color: $corporate-1;
    font-size: 18px;
    margin-bottom: 10px;
  }
}

.footer_column:last-child {
  border-right: 0px;
}

#facebook_like {
  margin-bottom: 10px;
}

#google_plus_one {
  margin-left: 40px;
  margin-bottom: 20px;
}

.footer-logos {
  background: white;
  margin-top: 20px;
  padding: 20px 0px;

  ul {
    text-align: justify;
    justify-content: space-between;
    height: 80px;
  }
  ul:after {
    content: '';
    display: inline-block;
    width: 100%;
    height: 0;
  }
  li {
    display: inline-block;
    text-align: center;
  }

}

/*======== Hidding Header =======*/

.hidden_top_menu {
  display: none;
  position: fixed !important;
  top: 0px;
  z-index: 401;
  width: 100%;
  background: white;
  box-shadow: 0 2px 6px -2px $corporate-1;

  #logoDiv {
    width: 200px !important;
    padding-top: 30px;
  }

  #main_menu {
    margin-top: 15px;
    width: 800px !important;
  }
  .deploy_booking {
    float: right;
    border: 0;
    background: #b57401;
    color: white;
    height: 100%;
    text-transform: uppercase;
    line-height: 40px;
    padding: 0 10px;
    position: relative;
    z-index: 3;
    cursor: pointer;
    font-size: 12px;
    outline: none;
    border-bottom: 10px solid white;
    border-top: 10px solid white;
    width: 100px !important;
    margin-top: 6px;
    margin-right: 0;
    height: 78px;
  }
  .deploy_booking:hover {
    background: darken($corporate-1, 20%);
  }

  #mainMenuDiv ul {
    padding: 20px 9px;
    .main-section-div-wrapper a {
      font-size: 12px;
      font-weight: 500;
    }
  }
}

.uk-table {
  width: 100%;
  margin-bottom: 25px;
}

* + .uk-table {
  margin-top: 25px;
}

.uk-table th,
.uk-table td {
  padding: 8px 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
}

.uk-table th {
  text-align: left;
}

.uk-table td {
  vertical-align: top;
}

.uk-table thead th {
  vertical-align: bottom;
}

.uk-table caption,
.uk-table tfoot {
  font-size: 13px;
  font-style: italic;

}

.uk-table caption {
  text-align: left;
  color: #999999;
}

.uk-table-middle,
.uk-table-middle td {
  vertical-align: middle !important;
}

.uk-table-striped tbody tr:nth-of-type(odd) {
  background: #f7f7f7;
}

.uk-table-condensed td {
  padding: 4px 2px;
}

.uk-table-hover tbody tr:hover {
  background: rgba(0, 0, 0, 0.03);
}

/*======= Specific hotel changes =======*/
body.nau-salema {
  #mainMenuDiv ul {
    padding: 20px 150px;
  }
}

/*===== Carousel Banners ======*/
.banner_carousel_element {
  margin-top: 50px;
}

.banners_carousel_wrapper {
  h2.banner_carousel_title {
    color: #897966;
    font-size: 23px;
    font-family: 'Droid Serif', serif;
    margin: 4px 0px;
  }

  .banner_carousel_description {
    font-size: 16px;
  }

  .banner_carousel_images {
    margin-top: 30px;
    position: relative;
    margin-bottom: 60px;

    .flex-direction-nav {
      position: absolute;
      top: 46%;
      width: 100%;

      .flex-nav-prev {
        position: absolute;
        left: -50px;
      }
      .flex-nav-next {
        position: absolute;
        right: -50px;
      }

      .flex-prev {
        display: inline-block;
        background: url("/img/#{$base_web}/flex-arrow-left.png") no-repeat;
        color: transparent;
        width: 33px;
        height: 56px;
      }
      .flex-next {
        display: inline-block;
        background: url("/img/#{$base_web}/flex-arrow-right.png?v=1") no-repeat;
        color: transparent;
        width: 33px;
        height: 56px;
      }
    }

  }

  .carousel_banner_image_element {
    padding: 0 5px;
    box-sizing: border-box;

    .text_carousel_image {
      h3 {
        color: #897966;
        font-size: 17px;
        font-family: 'Droid Serif', serif;
        margin: 14px 0px 7px;
      }
    }

    .carousel_image_top {
      width: 100%;
    }

    .exceded {
      height: 180px;
      overflow: hidden;

      img {
        min-height: 150px;
      }
    }

    .content_carousel_image {
      font-size: 16px;
      height: 103px;
      overflow: hidden;

      hide {
        display: none;
      }
    }
  }

  .text_carousel_image {
    display: table;
    box-sizing: border-box;
    width: 100%;
    padding: 0 20px 20px;
    background: #e2ded9;
  }

  a.myFancyPopupAuto {
    color: #897966;
    font-size: 14px;
    font-family: 'Droid Serif', serif;
    margin: 20px 0 0 !important;
    display: block;
  }
}

.hidden_carousel {
  .image_hidden_banner {
    width: 100%;
  }

  h3.title_hidden_banner {
    color: #897966;
    font-size: 24px;
    font-family: 'Droid Serif', serif;
    margin: 15px 0 15px;
    display: block;
  }

  .content_hidden_banner {
    font-size: 16px;
    clear: both;

    .new-intro {
      font-weight: bold;
      margin-bottom: 10px;
    }
    p {
      margin-bottom: 15px;
    }
  }
  .date_new {
    color: $corporate-1;
    font-style: italic;
    float: left;
    margin-bottom: 30px;
  }
  #shareSocialArea {
    width: 100%;
    float: right;
    margin-top: 0px;
    clear: initial;
  }
}

.flex-control-nav {
  text-align: right;
  padding: 10px 0;

  li {
    display: inline-block;

    a {
      background: #897966;
      display: block;
      width: 10px;
      height: 10px;
      cursor: pointer;
      margin: 4px;
      -webkit-border-radius: 20px;
      -moz-border-radius: 20px;
      border-radius: 20px;
      position: relative;
      text-indent: 9999px;

      &.flex-active:before {
        content: '';
        padding: 7px;
        -webkit-border: 1px solid #897966;
        -moz-border: 1px solid #897966;
        border: 1px solid #897966;
        position: absolute;
        top: -3px;
        bottom: 0;
        left: -3px;
        right: 0;
        border-radius: 20px;
      }
    }
  }
}

.white_background {
  background: white;
  overflow: hidden;

  .collapse_wrapper {
    width: 930px;

    .notas_block {
      margin-top: 20px;

      .notas_title {
        color: $corporate-1;
      }

      li {
        font-style: italic;
        list-style: initial;
        margin-left: 20px;
        color: black;
      }
    }
  }
}

/*====== Bottom banner contact =====*/
.bottom_banner_contact {
  padding-top: 50px;

  .photo_bottom_contact {
    padding-right: 20px;
    box-sizing: border-box;
  }
  .text_bottom_contact {
    font-size: 14px;
    padding-right: 20px;
    box-sizing: border-box;

    b {
      display: block;
      color: #897966;
      font-size: 18px;
      font-family: 'Droid Serif', serif;
      margin: 10px 0;
    }
  }

  .icon_text_bottom_contact {
    font-size: 14px;
    width: 200px;
  }

  img.icon_bottom_contact {
    padding-right: 11px;
    display: table;
  }

  .list_bottom_contact {
    margin-top:30px;
    table {
      tr:nth-child(2) td, tr:nth-child(5) td {
        padding-bottom: 10px;
      }
    }

    .title_bottom_contact {
      display: block;
      color: #897966;
      font-size: 18px;
      font-family: 'Droid Serif', serif;
      margin: 10px 0;
    }
  }
}

/*====== Price banners block ======*/
.prices_blocks_wrapper {
  padding-top: 50px;

  .price_element {
    width: 282px;
    display: inline-block;
    padding: 0 5px;
    box-sizing: border-box;
    vertical-align: top;

    &:first-of-type {
      padding-left: 0;
    }

    &:last-of-type {
      padding-right: 0;
    }

    .exceded {
      height: 185px;
      overflow: hidden;

      img.prices_block_image {
        width: 100%;
      }
    }

    .price_block_text {
      background: #f9f5f1;
      display: table;
      padding: 10px 20px;
      width: 100%;
      box-sizing: border-box;
      position: relative;
      padding-bottom: 50px;

      .price_element_text {
        position: absolute;
        bottom: 40px;
        width: 300px;
      }

      .price_block_description {
        color: black;
        font-weight: 600;
        font-size: 14px;
        padding-right: 34px;
        margin-bottom: 15px;

        hide {
          display: none;
        }
      }

      .price_element_text {
        big {
          color: #897966;
          font-size: 29px;
          font-weight: bolder;
          border-right: 1px solid;
          padding-right: 5px;
          margin-right: 5px;
          float: left;
          padding-top: 2px;
        }

        text {
          display: block;
          color: #a7927a;
        }

        small {
          color: #353535;
          font-weight: 600;
        }
      }
    }

    h3.price_block_title {
      color: #897966;
      font-size: 17px;
      font-family: 'Droid Serif', serif;
      margin: 7px 0px;
    }

    a.see_more_prices_block {
      margin-top: 10px;
      display: block;
      margin-bottom: 10px;
      text-decoration: none;
      color: #897966;
      font-size: 13px;
      text-transform: uppercase;
      position: absolute;
      bottom: 10px;
      left: 22px;
    }
  }
}

.hide_price_popup {
  color: #5a5a5a;

  h3.price_block_title {
    color: #897966;
    font-size: 22px;
    font-family: 'Droid Serif', serif;
    margin: 7px 0;
  }

  .price_block_description {
    font-size: 16px;
  }
}

/*===== Restaurant Blocks =======*/
.restaurant_blocks.white_background {
  padding-bottom: 50px;

  .restaurant_full_width_background {
    background: #e2ded9;
    padding: 20px 0;

    &:last-of-type {
      margin-bottom: 0;
    }
  }

  .restaurant_wrapper {
    .exceded {
      float: left;
      width: 65%;
      height: 450px;
      overflow: hidden;

      img {
        width: auto;
        max-width: none;
      }
    }
  }

  .content_restaurant {
    width: 35%;
    float: right;
    background: white;
    box-sizing: border-box;
    padding: 20px 20px 60px;
    position: relative;
    height: auto;

    h3.restaurant_title {
      color: #897966;
      font-size: 18px;
      font-family: 'Droid Serif', serif;
      margin: 10px 0;
    }

    .description_restaurant {
      font-family: "Source sans pro", sans-serif;
      text-align: justify;
      font-size: 16px;
      line-height: 19px;
      margin-bottom: 15px;
    }

    .button_restaurant {
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: #897966;
      text-align: center;
      color: white;
      padding: 10px;
      box-sizing: border-box;
      font-weight: 100;
      text-transform: uppercase;
      cursor: pointer;
    }
  }

  .restaurant_properties_wrapper {
    margin-bottom: 0;

    h3.restaurant_property_title {
      color: #897966;
      font-size: 18px;
      font-family: 'Droid Serif', serif;
      margin: 20px 0;
    }

    .restaurant_property_text {
      font-family: 'Source sans pro', sans-serif;
      text-align: justify;
      font-size: 16px;
      line-height: 19px;
      margin-bottom: 15px;
    }

    .restaurant_property_element {
      border: 1px solid lightgray;
      border-top: 0;
      border-left: 0;
      display: table;
      width: 100%;

      .exceded {
        float: left;
        width: 33%;
        margin-right: 30px;
        display: inline-block;

        .restaurant_image_property {
          width: 100%;
        }
      }

      .restaurant_property_content {
        display: inline-block;
        width: 33%;
      }
    }

    .restaurant_property_time {
      display: inline-block;
      margin-left: 32px;
    }
  }

  .time_text {
    font-family: 'Source sans pro', sans-serif;
    text-align: justify;
    font-size: 16px;
    line-height: 17px;
    margin-bottom: 15px;
  }

  .time_icon {
    margin-right: 10px;
    vertical-align: middle;
    width: 13px;
  }

  .ico_service_description {
    font-family: 'Source sans pro', sans-serif;
    font-size: 15px;
  }
}

/*======= Bar Blocks ======*/
.bars_blocks_wrapper {
  padding-bottom: 50px;

  .bar_block_element {
    width: 364px;
    display: inline-block;
    margin: 0 10px;
    vertical-align: top;

    &:nth-of-type(1), &:nth-of-type(4), &:nth-of-type(7) {
      margin-left: 0;
    }

    &:nth-of-type(3), &:nth-of-type(6), &:nth-of-type(9) {
      margin-right: 0;
    }

    .bar_content_text {
      background: #e2ded9;
      min-height: 200px;
      padding: 15px 30px;
      position: relative;
    }

    .bar_image {
      width: 100%;
    }

    h3.bar_title {
      color: #897966;
      font-size: 18px;
      font-family: 'Droid Serif', serif;
      margin-bottom: 10px;
    }

    .bar_description {
      text-align: justify;
      font-size: 16px;
      line-height: 17px;
      margin-bottom: 15px;

      time {
        display: block;
        margin-top: 20px;
        padding-left: 17px;
        padding-right: 17px;
        background: url(/img/nauil/time_icon.png) no-repeat left center;
        -webkit-background-size: 12px;
        background-size: 12px;
        position: absolute;
        bottom: 15px;
      }
    }
  }
}

.bars_blocks_superior_text {
  margin-bottom: 20px;
  h2.title {
    color: #897966;
    font-size: 21px;
    font-family: 'Droid Serif', serif;
    margin: 20px 0;
  }

  .content_text {
    text-align: justify;
    font-size: 16px;
    line-height: 17px;
    margin-bottom: 15px;
  }
}

.bar_block_element.unique_element {
  width: 100%;

  .exceded {
    float: left;
    width: 33.4%;
  }

  .bar_content_text {
    float: right;
    width: 66.6%;
    box-sizing: border-box;
    height: 190px;
    min-height: auto;
  }

  .bar_description {
    div {
      display: inline-block;
      width: 64%;
      margin-right: 40px;
    }

    time {
      vertical-align: top;
      display: inline-block;
      margin-top: 0;
      width: 175px;
    }
  }
}

.collapse_element_wrapper {
  h3 {
    color: #897966;
    font-family: 'Droid Serif', serif;
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 20px;
    margin-top: 35px;
  }

  img.collapse_ico {
    display: inline-block;
  }

  h4.collapse_title {
    display: inline-block;
    font-size: 17px;
    margin-left: 10px;
    width: 860px;

    .drinks {
      //font-size: 12px;
    }
  }

  .collapse_image {
    display: inline-block;
    vertical-align: middle;
  }

  .collapse_option {
    border-top: 1px solid #e8e8e8;
    padding: 10px;
    //cursor: pointer;

    &.gray {
      background: #f3f3f3;
    }

    &:last-of-type {
      border-bottom: 1px solid #E8E8E8;
    }
  }

  .description_ico {
    margin-top: 20px;
    font-family: 'Droid Serif', serif;
    text-align: justify;
    font-size: 13px;
    line-height: 19px;
    margin-bottom: 15px;
    padding: 0 40px;
  }
}

.specially_collapse_text {
  text-align: center;
  background: #897966;
  margin: 25px;
  color: white;
  padding: 10px 0;
  font-size: 23px;
  font-family: 'Droid Serif', serif;
}

/*====== Bottom banner contact =====*/
.bottom_banner_contact {
  padding-top: 50px;

  .text_bottom_contact {
    font-size: 16px;
    padding-right: 20px;
    box-sizing: border-box;

    b {
      display: block;
      color: #897966;
      font-size: 20px;
      font-family: 'Droid Serif', serif;
      margin: 10px 0;
    }
  }

  td {
    vertical-align: top;
  }

  .icon_text_bottom_contact {
    font-size: 16px;
    width: 200px;
  }

  img.icon_bottom_contact {
    padding-right: 11px;
    display: table;
    margin: auto;
  }

  .list_bottom_contact {

    table {
      tr:nth-child(2) td, tr:nth-child(5) td {
        padding-bottom: 10px;
      }
    }

    .title_bottom_contact {
      display: block;
      color: #897966;
      font-size: 20px;
      font-family: 'Droid Serif', serif;
      margin: 10px 0;
    }
  }
}

/*====== Share sections ====*/
div#shareSocialArea {
  margin-top: 25px;

  .share_text {
    line-height: 20px;
  }

  span.at-icon-wrapper {
    vertical-align: middle;
  }
}

div#shareSocialArea {
  margin-top: 15px;
  border-top: 1px solid;
  padding-top: 10px;
  width: 200px;
  float: right;
  clear: both;
}

/*===== See more button home ========*/
.normal_description.home_section {
  //overflow: hidden;
  //height: auto;

  .hide {
    display: none;
  }
}

.see_more_button_home {
  display: block;
  width: 180px;
  height: 35px;
  background: #897966 url("/static_1/images/booking/flecha_motor.png") no-repeat center;
  background-size: 20px;
  margin: 40px auto 0;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }

  &.active {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
}

/*===== Datepicker =====*/
.ui-datepicker {
  border: 0;
  border-radius: 0;
  font-family: 'Droid Serif', serif;

  .ui-datepicker-header {
    border-radius: 0;
    border: 0;
  }
}

/*====== News carousel ======*/

.news_events_wrapper {
  background: #e7e4df;
  padding: 50px;
  overflow: hidden;
}

.news_carousel_wrapper {
  h2.banner_carousel_title {
    color: #897966;
    font-size: 21px;
    font-family: 'Droid Serif', serif;
    margin: 4px 0px;
  }

  .banner_carousel_description {
    font-size: 14px;
  }

  .banner_carousel_images {
    margin-top: 30px;
    position: relative;
    margin-bottom: 60px;

    .flex-direction-nav {
      position: absolute;
      top: 46%;
      width: 100%;

      .flex-nav-prev {
        position: absolute;
        left: -50px;
      }
      .flex-nav-next {
        position: absolute;
        right: -50px;
      }

      .flex-prev {
        display: inline-block;
        background: url("/img/#{$base_web}/flex-arrow-left.png") no-repeat;
        color: transparent;
        width: 33px;
        height: 56px;
      }
      .flex-next {
        display: inline-block;
        background: url("/img/#{$base_web}/flex-arrow-right.png?v=1") no-repeat;
        color: transparent;
        width: 33px;
        height: 56px;
      }
    }
  }

  .carousel_banner_image_element {
    padding: 0 5px;
    box-sizing: border-box;

    .text_carousel_image {
      h3 {
        color: #897966;
        font-size: 17px;
        font-family: 'Droid Serif', serif;
        margin: 14px 0px 7px;
      }
    }

    .carousel_image_top {
      width: 100%;
    }

    .exceded {
      height: 150px;
      overflow: hidden;

      img {
        min-height: 150px;
      }
    }

    .content_carousel_image {
      font-size: 16px;
      height: 100px;
      overflow: hidden;

      .hide_more {
        display: none;
      }
    }
  }

  .text_carousel_image {
    display: table;
    padding: 0 20px 50px;
    background: white;
    position: relative;

    .date_new {
      color: #897966;
      font-style: italic;
    }
  }

  a.myFancyPopupNews {
    color: #897966;
    font-size: 14px;
    font-family: 'Droid Serif', serif;
    margin: 20px 0 0 !important;
    display: block;
    position: absolute;
    left: 20px;
    bottom: 20px;

    &:first-letter {
      text-transform: uppercase;
    }
  }
}

/*====== Flexslider control nav =====*/
.flex-control-nav {
  text-align: right;
  padding: 10px 0;

  li {
    display: inline-block;

    a {
      background: #897966;
      display: block;
      width: 10px;
      height: 10px;
      cursor: pointer;
      margin: 4px;
      -webkit-border-radius: 20px;
      -moz-border-radius: 20px;
      border-radius: 20px;
      position: relative;
      text-indent: 9999px;

      &.flex-active:before {
        content: '';
        padding: 7px;
        -webkit-border: 1px solid #897966;
        -moz-border: 1px solid #897966;
        border: 1px solid #897966;
        position: absolute;
        top: -3px;
        bottom: 0;
        left: -3px;
        right: 0;
        border-radius: 20px;
      }
    }
  }
}

/*====== News carousel =====*/
.news_carousel_wrapper {
  position: relative;
  margin-bottom: 30px;

  li {
    .exceded {
      height: 450px;
      position: relative;

      img {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
      }
    }
  }

  .flex-direction-nav {
    position: absolute;
    top: 46%;
    width: 100%;

    .flex-nav-prev {
      position: absolute;
      left: -50px;
    }

    .flex-nav-next {
      position: absolute;
      right: -50px;
    }

    .flex-prev {
      display: inline-block;
      background: url("/img/nauil/flex-arrow-left.png") no-repeat;
      color: transparent;
      width: 33px;
      height: 56px;
    }
    .flex-next {
      display: inline-block;
      background: url("/img/nauil/flex-arrow-right.png?v=1") no-repeat;
      color: transparent;
      width: 33px;
      height: 56px;
    }
  }
}

.news_carousel_wrapper {
  .flex-control-nav {
    text-align: center;
  }
}

/*======= Events Blocks ======*/
.event_block_element {
  display: block;
  width: 100%;
  margin-bottom: 5px;
  height: 185px;
  overflow: hidden;

  .exceded {
    width: 40%;
    position: relative;
    float: left;

    .day_wrapper {
      position: absolute;
      top: -2px;
      background: url('/img/nauce/events_background.png') no-repeat;
      background-size: 90%;
      text-align: left;
      bottom: 0;
      width: 35%;
      padding-top: 67px;
      box-sizing: border-box;
      color: white;
      font-size: 26px;
      font-weight: lighter;
      line-height: 35px;
      height: 187px;
      padding-left: 40px;

      b {
        display: block;
        font-size: 50px;
        font-weight: 500;
      }
    }
  }
  .event_block_description {
    width: 60%;
    float: right;
    background: white;
    padding: 20px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    height: 185px;

    a.myFancyPopupNews {
      position: absolute;
      bottom: 20px;
      right: 40px;
      color: #897966;

      &:first-letter {
        text-transform: uppercase;
      }
    }

    .event_block_text {
      location {
        margin-bottom: 100px;
        display: block;
        padding-left: 20px;
        font-size: 15px;
        background: url("/img/nauce/location/location.png") no-repeat;
      }

      time {
        color: #897966;
        font-weight: lighter;
        margin-bottom: 15px;
        display: block;
        font-size: 16px;
      }
    }
  }

  h4.event_block_title {
    color: #897966;
    font-size: 23px;
    font-family: 'Droid Serif', serif;
    font-weight: bolder;
  }
}

.hide_event_text {
  display: none;

  img.image_hidden_banner {
    width: 100%;
  }

  h3.title_hidden_banner {
    color: #897966;
    font-size: 27px;
    font-family: 'Droid Serif', serif;
    margin: 15px 0 10px;
    display: block;
    text-transform: uppercase;
  }

  time {
    margin-bottom: 10px;
    display: block;
    color: #897966;
    font-weight: lighter;
  }

  location {
    display: block;
    padding-left: 20px;
    font-size: 15px;
    background: url("/img/nauce/location/location.png") no-repeat;
    margin-bottom: 15px;
  }
  #shareSocialArea {
    float: right;
  }
}

.events_slide {
  position: absolute;
  left: 0;
  top: auto;
  bottom: 565px;

  &:first-of-type {
    top: 0;
    bottom: auto;
  }
}

.events_blocks_wrapper {
  height: 565px;
  overflow: hidden;
  position: relative;
}

.directional_navigator_events {
  text-align: center;
  margin-top: 25px;

  .up_arrow, .down_arrow {
    display: inline-block;
    height: 25px;
    width: 35px;
    background: url(/img/nauil/arrow-down.png) no-repeat center;
    background-size: 40px;

    &.disabled {
      opacity: 0.5;
    }

    &:not(.disabled) {
      cursor: pointer;
    }
  }

  .up_arrow {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
}

.banners-section {
  background: white;
}

.full-row {
  background: lighten($corporate-1, 40%);
  padding: 40px 0px;

  .block {
    width: 277px;
    float: left;
    margin-right: 10px;
  }
  .block:nth-child(4n) {
    margin-right: 0px;
  }
  .block:nth-child(n+5) {
    margin-top: 10px;
  }
  .image-box {
    height: 150px;
    position: relative;
    overflow: hidden;
  }
  .image-box img {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
  }
  .description-box {
    padding: 20px;
    border: 1px solid $gray-3;
    background: white;
    min-height: 250px;
    position: relative
  }
  h3 {
    color: $corporate-1;
    font-size: 18px;
    font-family: 'Droid Serif', serif;
    margin: 10px 0px;
  }
  .intro {
    color: black;
    font-weight: 700;
    font-size: 16px;
    margin-bottom: 20px;
  }
  .description {
    margin-bottom: 20px;
    font-size: 14px;
  }
  a {
    color: $corporate-1;
    position: absolute;
    bottom: 20px;
    left: 20px;
    text-transform: capitalize;
  }
  a:hover {
    color: darken($corporate-1, 20%);
    text-decoration: underline;
  }
}

/*=== Salgados palace ===*/
body.nau-salgados-palace {
  .hidden_top_menu #mainMenuDiv ul .main-section-div-wrapper a {
    font-size: 10px;
  }
}

// Popup Golf
.fancy-golf {
  table {
    width: 100%;
  }
  table, th, td {
    border: 1px #b7ac9e;
    border-collapse: collapse;
  }
  th, td {
    padding: 5px;
    text-align: left;
  }
  td.preco {
    padding: 5px;

  }
  tr.tfoot {
    padding-top: 60px;
    text-align: left;
    border-top-color: #b7ac9e;
    border-top-style: solid;
  }
  td.tfoot {
    text-align: left;
  }
  table#t01 tr:nth-child(even) {
    background-color: #eee;
  }
  table#t01 tr:nth-child(odd) {
    background-color: #fff;
  }
  table#t01 th {
    background-color: #b7ac9e;
    color: white;
  }
}

.promo-modal {
  #shareSocialArea {
    position: absolute;
    right: 0;
    bottom: 0;
    margin-top: 0;
  }
}

.info4Share {
  display: none;
}

/*=== New Offer Section ===*/

.line{
  height: 1px;
  color: $corporate-1;
  background-color: $corporate-1;
  margin-bottom: 20px;
}

.offers_blocks_wrapper {
  display: inline-block;
  width: 100%;
  margin-top: 10px;

  .offer_element {
    width: calc(100% );
    height: 300px;
    float: left;
    margin-right: 37px;
    position: relative;
    overflow: hidden;
    margin-bottom: 20px;
    padding-bottom:20px;


    &:last-child, &:nth-child(3n) {
      margin-right: 0;
    }

    .tag_offer {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 12;
      color: black;
      padding: 10px;
      font-size: 15px;
      line-height:15px;
    }


    .offer_image {
      img {
        @include center_xy;
        max-width: none;
        min-height: 100%;
        min-width: 100%;
      }
    }

    .offer_content {
      opacity: 1;
      transition: opacity .7s;
      height: 100%;


      .overlay_black {
        position: absolute;
        width: 100%;
        top: 0;
        bottom: 0;
        background: rgba(black, .6);
      }

      .center_block {
        @include center_y;
        width: 100%;
        text-align: center;
        color: white;

        .offer_title {
          text-transform: uppercase;
          font-size: 26px;
          line-height: 26px;
          font-family: "Droid Serif";
          margin-bottom: 10px;
        }

        .offer_description {
          font-weight: lighter;
          font-size: 14px;
          line-height: 14px;

          .promocode_offer_Text {
            margin-top: 10px;
            display: block;
          }

          .promocode_offer {
            color: red;
            font-style: italic;
            font-weight: bold;
            font-family: "Droid Serif";
          }
        }
      }

      .see_more {
        position: absolute;
        font-size: 15px;
        line-height: 15px;
        bottom: 0;
        width: 100%;
        background: $corporate_1;
        color: white;
        padding: 5px 0;
        text-align: center;
      }
    }
  }
}

/*=== Pack Offer ===*/
.pack_offer_wrapper {
  display: inline-block;
  width: 100%;

  .pack_title_section {
    color: $corporate_1;
    font-size: 30px;
    line-height: 30px;
    text-transform: uppercase;
    margin: 20px auto;
    text-align: center;
  }

  .pack_description_section {
    margin-bottom: 20px;
    font-size: 14px;
    line-height: 20px;

    .highlight {
      color: $corporate_1;
    }

    .table_offer {
      display: inline-block;
      width: 100%;
      margin-top: 20px;

      .left_offer {
        float: left;
        width: 100%;
        box-sizing: border-box;
        padding-right: 30px;
      }

      .right_offer {
        float: right;
        width: 100%;
        box-sizing: border-box;
        padding-right: 30px;

        .offer_phone {
          box-sizing: border-box;
          padding-left: 40px;
          background: url(/img/#{$base_web}/phone.png) no-repeat;
          background-size: contain;
        }

        .offer_email {
          box-sizing: border-box;
          padding-left: 40px;
          background: url(/img/#{$base_web}/email.png) no-repeat;
          background-size: contain;
        }
      }
    }
  }

  .button_promotion {
    background: #b57401!important;

    &:hover {
      background: #4f453a!important;
    }
  }

  .button_promotion, .personalized_link {
    cursor: pointer;
    background: $corporate_1;
    border-radius: 0;
    position: relative;
    top: 0;
    display: block;
    vertical-align: top;
    height: 49px;
    min-width: 200px !important;
    text-align: center;
    font-size: 22px;
    color: white;
    box-sizing: border-box;
    padding: 7px 0;
    margin-top: 30px;
    float: left;
    text-transform: uppercase;

    &:hover {
      background: #b57401;
    }
  }

  .personalized_link{
    margin-left: 10px;
  }

  .filter_pack {
    display: inline-block;
    width: 100%;
    margin-top: 20px;
    padding-bottom: 20px;
    border-bottom: 2px solid $corporate_1;
    font-size: 16px;

    .filter_element {
      display: inline-block;
      vertical-align: middle;
      font-style: italic;
      font-weight: bold;
      cursor: pointer;

      &.selected {
        color: $corporate_1;
      }
    }

    .barra {
      display: inline-block;
      background: $corporate_1;
      height: 15px;
      margin: 0 5px;
      width: 2px;
      vertical-align: middle;
    }
  }

  .pack_content_wraper {
    display: inline-block;
    width: 100%;

    .pack_element {
      display: inline-block;
      width: 100%;
      border-bottom: 2px solid $corporate_1;
      padding: 40px 0;

      &:first-child {
        border-top: 2px solid $corporate_1;
      }

      .pack_image {
        position: relative;
        height: 200px;
        width: 200px;
        display: block;
        margin: 10px auto;
        overflow: hidden;

        img {
          @include center_xy;
          min-width: 100%;
          min-height: 100%;
          max-width: none;
        }
      }

      .pack_content {
        float: right;
        width: 100%;
        display: inline-block;

        .pack_hotel_name {
          font-size: 24px;
          line-height: 24px;
        }
        .pack_stars {
          font-size: 24px;
          line-height: 24px;
          color: $corporate_1;
        }
        .pack_location {
          font-weight: 100;
          font-size: 14px;
          line-height: 24px;
        }
        .pack_title {
          background-color: #EFEFEF;
          color: #4B4B4B;
          text-align: center;
          padding: 10px;
          font-size: 24px;
          clear: both;
          line-height: 24px;
        }

        .pack_description {
          margin-top: 20px;
          font-size: 14px;
          line-height: 20px;
        }

        .see_more {
          display: block;
          padding: 10px;
          text-align: center;
          text-transform: uppercase;
          font-size: 14px;
          line-height: 20px;
          box-sizing: border-box;
          color: white;
          background: $corporate_1;
          margin-top: 20px;
        }
      }
    }
  }
}

a.button_link {
  background: #b57401 !important;
  color: white;
  padding: 10px 30px;
  display: table;
}

/*=== Suggestion Slider ===*/
.offers_packs_carousel {
  .title_carousel {
    font-size: 32px;
    text-align: center;
    font-family: "Droid Serif";
    margin: 25px 0;
  }

  .carousel_content {
    position: relative;

    .prev_arrow {
      @include center_y;
      left: -50px;
    }

    .next_arrow {
      @include center_y;
      right: -50px;
    }
  }

  .suggestion_element {
    position: relative;
    height: 380px;

    .suggestion_image {
      position: relative;
      height: 100%;
      overflow: hidden;

      img {
        @include center_xy;
        max-width: none;
        min-width: 100%;
        min-height: 100%;
      }
    }

    .suggestion_content {
      opacity: 1;
      transition: opacity .7s;
      height: 100%;

      &:hover {
        opacity: 1;
      }

      .overlay_black {
        position: absolute;
        width: 100%;
        top: 0;
        bottom: 0;
        background: rgba(black, .6);
      }

      .center_block {
        @include center_y;
        width: 100%;
        text-align: center;
        color: white;

        .suggestion_title {
          text-transform: uppercase;
          font-size: 26px;
          font-family: "Droid Serif";
          margin-bottom: 10px;
          line-height: 1.2;
        }

        .suggestion_description {
          font-weight: lighter;
          font-size: 14px;
          line-height: 1.2;

          .promocode_offer_Text {
            margin-top: 10px;
            display: block;
          }

          .promocode_offer {
            color: red;
            font-style: italic;
            font-weight: bold;
            font-family: "Droid Serif";
          }
        }
      }

      .see_more {
        position: absolute;
        bottom: 0;
        width: 100%;
        background: $corporate_1;
        color: white;
        padding: 15px 0;
        font-size: 15px;
        text-align: center;
      }
    }
  }
}