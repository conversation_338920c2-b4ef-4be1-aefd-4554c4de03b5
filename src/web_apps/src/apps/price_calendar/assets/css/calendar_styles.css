@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&display=swap");
/* line 16, ../sass/calendar_styles.scss */
body.development {
  padding-bottom: 80px;
}

/* line 20, ../sass/calendar_styles.scss */
.price_calendar_wrapper {
  font-family: "Montserrat", sans-serif;
  color: var(--text_color);
  width: 1140px;
  margin: 0 auto 55px auto;
  box-shadow: 0 2px 7px #00000022;
  border-radius: 8px;
  line-height: initial;
  background-color: white;
}
/* line 5, ../../../../../../../../../../../Library/Ruby/Gems/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
.price_calendar_wrapper html, .price_calendar_wrapper body, .price_calendar_wrapper div, .price_calendar_wrapper span, .price_calendar_wrapper applet, .price_calendar_wrapper object, .price_calendar_wrapper iframe,
.price_calendar_wrapper h1, .price_calendar_wrapper h2, .price_calendar_wrapper h3, .price_calendar_wrapper h4, .price_calendar_wrapper h5, .price_calendar_wrapper h6, .price_calendar_wrapper p, .price_calendar_wrapper blockquote, .price_calendar_wrapper pre,
.price_calendar_wrapper a, .price_calendar_wrapper abbr, .price_calendar_wrapper acronym, .price_calendar_wrapper address, .price_calendar_wrapper big, .price_calendar_wrapper cite, .price_calendar_wrapper code,
.price_calendar_wrapper del, .price_calendar_wrapper dfn, .price_calendar_wrapper em, .price_calendar_wrapper img, .price_calendar_wrapper ins, .price_calendar_wrapper kbd, .price_calendar_wrapper q, .price_calendar_wrapper s, .price_calendar_wrapper samp,
.price_calendar_wrapper small, .price_calendar_wrapper strike, .price_calendar_wrapper strong, .price_calendar_wrapper sub, .price_calendar_wrapper sup, .price_calendar_wrapper tt, .price_calendar_wrapper var,
.price_calendar_wrapper b, .price_calendar_wrapper u, .price_calendar_wrapper i, .price_calendar_wrapper center,
.price_calendar_wrapper dl, .price_calendar_wrapper dt, .price_calendar_wrapper dd, .price_calendar_wrapper ol, .price_calendar_wrapper ul, .price_calendar_wrapper li,
.price_calendar_wrapper fieldset, .price_calendar_wrapper form, .price_calendar_wrapper label, .price_calendar_wrapper legend,
.price_calendar_wrapper table, .price_calendar_wrapper caption, .price_calendar_wrapper tbody, .price_calendar_wrapper tfoot, .price_calendar_wrapper thead, .price_calendar_wrapper tr, .price_calendar_wrapper th, .price_calendar_wrapper td,
.price_calendar_wrapper article, .price_calendar_wrapper aside, .price_calendar_wrapper canvas, .price_calendar_wrapper details, .price_calendar_wrapper embed,
.price_calendar_wrapper figure, .price_calendar_wrapper figcaption, .price_calendar_wrapper footer, .price_calendar_wrapper header, .price_calendar_wrapper hgroup,
.price_calendar_wrapper menu, .price_calendar_wrapper nav, .price_calendar_wrapper output, .price_calendar_wrapper ruby, .price_calendar_wrapper section, .price_calendar_wrapper summary,
.price_calendar_wrapper time, .price_calendar_wrapper mark, .price_calendar_wrapper audio, .price_calendar_wrapper video {
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
  font-size: 100%;
  vertical-align: baseline;
}
/* line 22, ../../../../../../../../../../../Library/Ruby/Gems/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
.price_calendar_wrapper html {
  line-height: 1;
}
/* line 24, ../../../../../../../../../../../Library/Ruby/Gems/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
.price_calendar_wrapper ol, .price_calendar_wrapper ul {
  list-style: none;
}
/* line 26, ../../../../../../../../../../../Library/Ruby/Gems/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
.price_calendar_wrapper table {
  border-collapse: collapse;
  border-spacing: 0;
}
/* line 28, ../../../../../../../../../../../Library/Ruby/Gems/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
.price_calendar_wrapper caption, .price_calendar_wrapper th, .price_calendar_wrapper td {
  text-align: left;
  font-weight: normal;
  vertical-align: middle;
}
/* line 30, ../../../../../../../../../../../Library/Ruby/Gems/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
.price_calendar_wrapper q, .price_calendar_wrapper blockquote {
  quotes: none;
}
/* line 103, ../../../../../../../../../../../Library/Ruby/Gems/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
.price_calendar_wrapper q:before, .price_calendar_wrapper q:after, .price_calendar_wrapper blockquote:before, .price_calendar_wrapper blockquote:after {
  content: "";
  content: none;
}
/* line 32, ../../../../../../../../../../../Library/Ruby/Gems/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
.price_calendar_wrapper a img {
  border: none;
}
/* line 116, ../../../../../../../../../../../Library/Ruby/Gems/2.6.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
.price_calendar_wrapper article, .price_calendar_wrapper aside, .price_calendar_wrapper details, .price_calendar_wrapper figcaption, .price_calendar_wrapper figure, .price_calendar_wrapper footer, .price_calendar_wrapper header, .price_calendar_wrapper hgroup, .price_calendar_wrapper main, .price_calendar_wrapper menu, .price_calendar_wrapper nav, .price_calendar_wrapper section, .price_calendar_wrapper summary {
  display: block;
}
/* line 32, ../sass/calendar_styles.scss */
.fancybox-inner .price_calendar_wrapper {
  margin: 0;
}
/* line 36, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container {
  padding: 40px 50px;
}
/* line 39, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 25px;
  padding-right: 90px;
}
/* line 45, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .title #lottie_animation_calendar {
  width: 32px;
  display: inline-block;
  position: relative;
  top: 7px;
  margin-right: 8px;
}
/* line 52, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .title #lottie_animation_calendar * {
  fill: var(--text_color);
  stroke: var(--text_color);
}
/* line 59, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .description {
  font-size: 16px;
  font-weight: 400;
  padding-right: 90px;
}
/* line 66, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .description {
  margin-bottom: 35px;
}
/* line 70, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper {
  position: relative;
  max-width: 900px;
  margin: 0 auto;
  -moz-transition: -moz-transform, 0.4s;
  -o-transition: -o-transform, 0.4s;
  -webkit-transition: -webkit-transform, 0.4s;
  transition: transform, 0.4s;
}
/* line 76, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper .date_suggestion {
  width: 213px;
  border: 1px solid var(--main_color);
  text-align: center;
  display: inline-block;
  padding: 16px 0;
  -moz-transition: background-color, 0.4s;
  -o-transition: background-color, 0.4s;
  -webkit-transition: background-color, 0.4s;
  transition: background-color, 0.4s;
  cursor: pointer;
}
/* line 85, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper .date_suggestion .suggestion {
  font-size: 20px;
  font-weight: bold;
  padding-bottom: 10px;
}
/* line 91, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper .date_suggestion .nights {
  font-size: 18px;
  font-weight: 500;
}
/* line 96, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper .date_suggestion .price {
  font-size: 20px;
  font-weight: 600;
  text-transform: capitalize;
}
/* line 102, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper .date_suggestion:hover {
  background-color: #33333322;
}
/* line 106, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper .slider_navs {
  position: absolute;
  display: flex;
  justify-content: space-between;
  width: 100%;
  position: absolute;
  top: 50%;
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -webkit-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}
/* line 113, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper .slider_navs .slider_nav {
  border: 1px solid var(--main_color);
  border-radius: 5px;
  width: 38px;
  height: 38px;
  display: flex;
  position: relative;
  cursor: pointer;
}
/* line 122, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper .slider_navs .slider_nav.slider_next {
  left: 50px;
}
/* line 126, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper .slider_navs .slider_nav.slider_prev {
  right: 50px;
}
/* line 130, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper .slider_navs .slider_nav.disabled {
  opacity: 0;
  cursor: auto;
}
/* line 135, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper .slider_navs .slider_nav svg {
  width: 45%;
  position: absolute;
  top: 50%;
  left: 50%;
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 142, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper.beggining {
  transform: translateX(-50px);
}
/* line 146, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper.no_slide {
  transform: none;
}
/* line 149, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper.no_slide .swiper-wrapper {
  display: flex;
  justify-content: space-around;
}
/* line 153, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper.no_slide .swiper-wrapper .swiper-slide {
  width: auto !important;
}
/* line 158, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .date_suggestions_wrapper .slider_wrapper.no_slide .slider_navs {
  display: none;
}
/* line 165, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper {
  display: flex;
  justify-content: space-between;
  margin-top: 40px;
  position: relative;
}
/* line 171, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .calendar_prices_loader {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffffb3;
  z-index: 5;
  opacity: 0;
  pointer-events: none;
  transition: all 0.2s;
}
/* line 184, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .calendar_prices_loader.active {
  opacity: 1;
  pointer-events: initial;
}
/* line 191, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper {
  display: flex;
  justify-content: center;
  position: relative;
  margin-bottom: 10px;
}
/* line 197, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .change_month_btn {
  display: inline-block;
  transform: rotate(-90deg);
  position: absolute;
  top: 12px;
  cursor: pointer;
}
/* line 204, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .change_month_btn svg {
  height: 7px;
  width: auto;
}
/* line 209, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .change_month_btn:not(.main) {
  right: 30px;
}
/* line 213, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .change_month_btn.main {
  transform: rotate(90deg);
  left: 30px;
}
/* line 219, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .change_month_btn.disabled svg * {
  fill: #c7c9cd;
}
/* line 21, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper {
  position: relative;
  display: inline-block;
  z-index: 4;
  height: 30px;
}
/* line 27, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper .selector_wrapper {
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 0 0 11px 11px;
  width: 100%;
  background-color: white;
  font-size: 16px;
  font-weight: 400;
  line-height: 30px;
  min-width: 173px;
  width: max-content;
  height: initial;
  box-shadow: 0px 6px 6px #00000029;
}
/* line 42, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector_label {
  text-align: center;
  border-radius: 6px;
  padding: 7px 16px;
  border: 1px solid transparent;
  padding: 3px 16px;
  cursor: pointer;
}
/* line 50, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector_label svg {
  height: 7px;
  width: auto;
  margin-left: 10px;
  margin-bottom: 2px;
  transform: rotate(180deg);
}
/* line 59, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector {
  padding: 10px;
  max-height: 260px;
  overflow-y: auto;
  color: #8d8d8d;
}
/* line 65, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector .option {
  cursor: pointer;
}
/* line 68, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector .option:hover {
  color: var(--text_color);
}
/* line 73, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector .legend_wrapper {
  width: 146px;
  font-size: 11px;
  font-weight: 300;
  font-style: italic;
  line-height: 14px;
  border-top: 0.5px solid #f5f5f5;
  margin-top: 10px;
  padding-top: 8px;
}
/* line 83, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector .legend_wrapper br {
  display: block;
  width: 10px;
}
/* line 92, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper.hide .selector_wrapper {
  height: 100%;
  box-shadow: none;
}
/* line 96, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper.hide .selector_wrapper .selector_label {
  border-color: #ebebeb;
  box-shadow: none;
}
/* line 100, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper.hide .selector_wrapper .selector_label svg {
  transform: rotate(0deg);
}
/* line 105, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper.hide .selector_wrapper .selector {
  opacity: 0;
  pointer-events: none;
}
/* line 117, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper {
  position: relative;
  display: inline-block;
  z-index: 1;
}
/* line 122, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper.show {
  z-index: 4;
}
/* line 126, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper .selector_wrapper {
  font-size: 16px;
  font-weight: 500;
  line-height: 30px;
  border-radius: 11px;
  min-width: 173px;
  position: absolute;
  left: 50%;
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -webkit-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}
/* line 134, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector_label {
  font-size: 24px;
  font-weight: bold;
  box-shadow: none;
}
/* line 139, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector_label svg {
  display: inline;
  margin-bottom: 4px;
}
/* line 144, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector_label .selector {
  padding: 10px 20px;
}
/* line 152, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper.hide .selector_wrapper .selector_label {
  border-color: transparent;
}
/* line 230, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper {
  text-align: center;
  border-spacing: 5px;
  border-collapse: separate;
  margin: 0 auto;
}
/* line 238, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper thead tr td {
  font-size: 20px;
  font-weight: 600;
  color: #aaaaaa;
  text-align: center;
  padding: 1px;
}
/* line 249, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper {
  padding: 1px;
  position: relative;
  width: 61px;
  height: 55px;
  border-radius: 8px;
  text-align: center;
  color: var(--color_day_dispo);
}
/* line 258, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .dash {
  width: 9px;
  height: 10px;
  border-bottom: 1px solid #adadad;
  color: transparent;
  display: block;
  margin: 0 auto;
}
/* line 267, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .min_stay {
  position: relative;
  font-size: 11px;
  width: 100%;
  padding-bottom: 1px;
  z-index: 3;
}
/* line 274, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .min_stay:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background-color: var(--main_color);
  opacity: 0.12;
}
/* line 286, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .day {
  font-size: 20px;
  font-weight: bold;
  position: relative;
  top: 1px;
  z-index: 3;
  line-height: 22px;
}
/* line 295, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .price {
  position: relative;
  font-size: 11px;
  width: max-content;
  margin: 0 auto;
  z-index: 3;
  bottom: 1px;
}
/* line 305, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .high_occupancy::after {
  content: "";
  position: absolute;
  width: 7px;
  height: 7px;
  background-color: var(--color_day_high_occupancy);
  border: 1px solid white;
  display: block;
  z-index: 2;
  border-radius: 50%;
  left: calc(50% - (7px/2));
}
/* line 319, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .close_to_arrival_tooltip {
  position: fixed;
  padding: 12px;
  border: 2px solid var(--main_color);
  background: white;
  z-index: 10;
  font-size: 14px;
  font-style: italic;
  color: #9b9b9b;
  text-align: left;
  width: 201px;
  border-radius: 15px;
  transform: translate(-34px, 28px);
}
/* line 333, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .close_to_arrival_tooltip:before, .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .close_to_arrival_tooltip:after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 25px;
  margin-left: -5px;
  border-width: 0 14px 14px 14px;
  border-style: solid;
  border-color: transparent transparent var(--main_color) transparent;
}
/* line 345, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .close_to_arrival_tooltip:after {
  border-color: transparent transparent white transparent;
  border-width: 0 12px 12px 12px;
  left: 27px;
  bottom: calc(100% - 1px);
}
/* line 353, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .day_tooltip {
  position: absolute;
  width: 160px;
  font-size: 14px;
  font-style: italic;
  font-weight: 300;
  border: 2px solid var(--main_color);
  border-radius: 8px;
  background-color: white;
  color: var(--text_color);
  padding: 10px;
  text-align: left;
  left: 50%;
  bottom: calc(100% + 15px);
  transform: translateX(-50%);
  opacity: 0;
  pointer-events: none;
  z-index: 4;
}
/* line 372, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .day_tooltip:has(:not(:only-child)) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 30px;
  width: fit-content;
}
/* line 379, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .day_tooltip:has(:not(:only-child)) > * {
  width: max-content;
  max-width: 250px;
}
/* line 383, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .day_tooltip:has(:not(:only-child)) > *.nights_number_tooltip {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 20px;
  position: relative;
}
/* line 390, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .day_tooltip:has(:not(:only-child)) > *.nights_number_tooltip::after {
  content: "";
  display: block;
  width: 1px;
  height: 100%;
  background: var(--text_color);
  position: absolute;
  right: -15px;
}
/* line 403, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .day_tooltip.nights_number_tooltip {
  display: flex;
  align-items: center;
  gap: 5px;
  width: auto;
  padding: 10px 20px;
  text-align: center;
  white-space: nowrap;
  font-size: 20px;
}
/* line 414, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .day_tooltip:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translate(-35%, 65%) rotate(135deg);
  width: 10px;
  height: 10px;
  background: white;
  border-top: 2px solid var(--main_color);
  border-right: 2px solid var(--main_color);
}
/* line 428, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.disabled {
  color: #adadad;
}
/* line 431, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.disabled .day {
  position: relative;
}
/* line 434, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.disabled .day:after {
  content: '';
  position: absolute;
  top: 10px;
  height: 1px;
  width: 24px;
  background-color: #adadad;
  position: absolute;
  top: 50%;
  left: 50%;
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 446, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.disabled .price {
  position: relative;
}
/* line 449, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.disabled .price:after {
  content: '';
  position: absolute;
  top: 10px;
  height: 1px;
  width: 100%;
  background-color: #adadad;
  position: absolute;
  top: 50%;
  left: 50%;
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 463, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.hover:after, .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.selected:after {
  content: '';
  width: calc(100% + 5px);
  height: 100%;
  background-color: var(--main_color);
  opacity: 0.13;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
/* line 476, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.hover.last_hover:after, .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.selected.last_hover:after {
  border-radius: 0 8px 8px 0;
}
/* line 482, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.selected {
  color: var(--text_color_selected) !important;
}
/* line 485, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.selected .day {
  color: var(--text_color_selected);
}
/* line 489, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.selected:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: var(--main_color);
  color: var(--text_color_selected);
  z-index: 2;
  border-radius: 8px;
}
/* line 503, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.selected.start_date:after {
  border-radius: 8px;
  width: 100%;
}
/* line 509, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.selected.start_date + .hover:after {
  left: -9px;
  width: calc(100% + 14px);
}
/* line 517, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.selected:not(.start_date):after {
  border-radius: 0 8px 8px 0;
  width: 100%;
}
/* line 523, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.selected .day:after {
  background-color: var(--text_color_selected);
}
/* line 528, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.selected .high_occupancy::after {
  background-color: var(--color_day_high_occupancy);
}
/* line 535, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.has_min_stay {
  color: var(--color_day_min_stay);
}
/* line 538, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.has_min_stay:before {
  content: '';
  width: 100%;
  height: 105%;
  position: absolute;
  top: 50%;
  left: 50%;
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  border: 3px solid var(--main_color);
  opacity: 0.12;
  border-radius: 8px;
  z-index: 1;
  box-sizing: border-box;
}
/* line 551, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.has_min_stay.selected .min_stay, .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.has_min_stay.selected:before {
  border-color: var(--main_color);
  opacity: 1;
  z-index: 2;
}
/* line 561, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.last_day_available .last_day_text {
  position: relative;
}
/* line 564, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.last_day_available .last_day_text:before {
  content: "";
  position: absolute;
  top: -37px;
  left: 16px;
  width: 0;
  height: 0;
  border-top: 20px solid #E4AC51;
  border-left: 20px solid transparent;
  border-radius: 4px;
}
/* line 578, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.last_day_available:hover .day_tooltip {
  border: 2px solid #E4AC51;
  opacity: 1;
}
/* line 582, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.last_day_available:hover .day_tooltip:after {
  border-top: 2px solid #E4AC51;
  border-right: 2px solid #E4AC51;
}
/* line 591, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.closed .no_dispo_text {
  text-transform: uppercase;
  font-weight: 800;
  font-size: 8px;
}
/* line 598, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper:not(.empty) {
  cursor: pointer;
}
/* line 603, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper:last-of-type:after {
  width: 100%;
}
/* line 612, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper:nth-child(6n).selected.start_date + .hover:after {
  width: 108%;
  left: -8px;
}
/* line 622, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper:hover .day_tooltip.nights_number_tooltip {
  opacity: 1;
}
/* line 629, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.disabled_min_stay:hover .day_tooltip.min_stay_tooltip,
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.disabled_min_stay:hover .day_tooltip.unselectable_day_tooltip, .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.disabled:hover .day_tooltip.min_stay_tooltip,
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.disabled:hover .day_tooltip.unselectable_day_tooltip {
  opacity: 1;
}
/* line 635, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.loading_prices {
  color: var(--text_color) !important;
  position: relative;
}
/* line 639, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.loading_prices .price,
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.loading_prices .no_dispo_text,
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.loading_prices .min_stay,
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.loading_prices .day_tooltip,
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.loading_prices .day:after {
  display: none;
}
/* line 647, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.loading_prices:before {
  border: none;
}
/* line 651, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.loading_prices .price_loader {
  z-index: 3;
}
/* line 654, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.loading_prices .price_loader:after {
  content: '';
  position: absolute;
  left: 50%;
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -webkit-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  bottom: 10px;
  background: var(--text_color);
  height: 2px;
  width: 30%;
}
/* line 665, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.highlighted {
  position: relative;
}
/* line 668, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.highlighted .highlight_underline {
  position: absolute;
  bottom: -10px;
  left: -15px;
  right: -14px;
  height: 1px;
  background-color: var(--text_color);
  z-index: 3;
}
/* line 680, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.highlighted.range_start .highlight_underline {
  left: 10%;
  right: -5%;
  height: 6px;
  background: transparent;
  border-left: 1px solid var(--text_color);
  border-bottom: 1px solid var(--text_color);
  border-radius: 0 0 0 10px;
}
/* line 692, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.highlighted.range_end .highlight_underline {
  right: 10%;
  left: -5%;
  height: 6px;
  background: transparent;
  border-right: 1px solid var(--text_color);
  border-bottom: 1px solid var(--text_color);
  border-radius: 0 0 10px 0;
}
/* line 703, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.cheapest_price {
  color: var(--color_cheapest_price);
}
/* line 706, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.cheapest_price .price {
  font-weight: 400;
}
/* line 712, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr.week-row {
  height: 65px;
}
/* line 716, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr.highlight-ranges {
  height: 90px;
}
/* line 720, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr.highlight-ranges td.day_wrapper:before, .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr.highlight-ranges td.day_wrapper:after {
  height: 80%;
  top: 15%;
}
/* line 726, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr.highlight-ranges td.day_wrapper.has_min_stay:before {
  height: 75%;
  top: 55%;
}
/* line 738, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month-container {
  position: relative;
  padding-bottom: 20px;
}
/* line 743, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .range-label {
  position: absolute;
  text-align: center;
  font-size: 12px;
  color: var(--text_color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  pointer-events: none;
  line-height: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
/* line 757, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .range-label .range-text {
  background-color: white;
  padding: 0 4px;
  position: relative;
  z-index: 2;
  white-space: nowrap;
}
/* line 766, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .calendar_wrapper .month_wrapper {
  position: relative;
  z-index: 1;
}
/* line 772, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper {
  margin-top: 10px;
}
/* line 775, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top {
  padding-bottom: 16px;
}
/* line 778, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 25px;
}
/* line 784, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel {
  display: flex;
  align-items: center;
}
/* line 788, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .currency_selector_wrapper {
  min-width: 175px;
}
/* line 791, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .currency_selector_wrapper .options {
  max-height: 100px;
  overflow-y: auto;
}
/* line 248, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper {
  margin: 0 10px;
  display: flex;
  align-items: center;
}
/* line 253, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper span {
  font: normal normal normal 16px/22px Open Sans;
}
/* line 257, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .info-icon-wrapper {
  position: relative;
  display: inline-block;
  margin-left: 10px;
  margin-right: 5px;
  cursor: pointer;
}
/* line 264, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .info-icon-wrapper svg {
  fill: white;
  width: 20px;
  height: 16px;
  margin-bottom: -2px;
}
/* line 272, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .on_request_tooltip {
  transition: max-height 0.3s ease;
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--main_color);
  color: white;
  border-radius: 10px;
  font-size: 13px;
  width: 370px;
  z-index: 10;
  margin-bottom: 5px;
  color: white;
  max-height: 200px;
}
/* line 288, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .on_request_tooltip.is_mobile {
  width: 250px;
  left: 0;
  transform: translateX(-85%);
  margin-bottom: 10px;
}
/* line 294, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .on_request_tooltip.is_mobile.show:after {
  left: unset;
  right: 10%;
}
/* line 300, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .on_request_tooltip.hide {
  max-height: 0;
  overflow: hidden;
}
/* line 305, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .on_request_tooltip .tooltip_title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
}
/* line 310, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .on_request_tooltip .tooltip_inner_wrapper {
  padding: 25px 30px;
}
/* line 315, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .on_request_tooltip.show:after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--main_color) transparent transparent transparent;
}
/* line 326, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .switch {
  position: relative;
  width: 68px;
  border: 1px solid var(--main_color);
  height: 33px;
  border-radius: 100px;
  margin-left: 10px;
  cursor: pointer;
}
/* line 335, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .switch .switch_knob {
  font-family: "Font Awesome 6 Pro";
  position: absolute;
  top: 3px;
  left: auto;
  right: 37px;
  width: 27px;
  height: 27px;
  box-sizing: border-box;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  line-height: 0.7;
  padding: 11px 4px;
  background-color: var(--main_color);
  border-radius: 50%;
  transition: 0.3s ease all, left 0.3s cubic-bezier(0.18, 0.89, 0.35, 1.15);
}
/* line 354, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .switch .switch_knob .fa-check {
  display: none;
}
/* line 360, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .switch.active .switch_knob {
  right: 3px;
}
/* line 363, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .switch.active .switch_knob .fa-check {
  display: block;
}
/* line 367, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .right_panel .on_request_wrapper .switch.active .switch_knob .fa-xmark {
  display: none;
}
/* line 21, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper,
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper {
  position: relative;
  display: inline-block;
  z-index: 4;
  height: 30px;
}
/* line 27, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper,
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper {
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 0 0 11px 11px;
  width: 100%;
  background-color: white;
  font-size: 16px;
  font-weight: 400;
  line-height: 30px;
  min-width: 173px;
  width: max-content;
  height: initial;
  box-shadow: 0px 6px 6px #00000029;
}
/* line 42, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper .selector_label,
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper .selector_label {
  text-align: center;
  border-radius: 6px;
  padding: 7px 16px;
  border: 1px solid transparent;
  padding: 3px 16px;
  cursor: pointer;
}
/* line 50, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper .selector_label svg,
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper .selector_label svg {
  height: 7px;
  width: auto;
  margin-left: 10px;
  margin-bottom: 2px;
  transform: rotate(180deg);
}
/* line 59, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper .selector,
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper .selector {
  padding: 10px;
  max-height: 260px;
  overflow-y: auto;
  color: #8d8d8d;
}
/* line 65, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper .selector .option,
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper .selector .option {
  cursor: pointer;
}
/* line 68, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper .selector .option:hover,
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper .selector .option:hover {
  color: var(--text_color);
}
/* line 73, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper .selector .legend_wrapper,
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper .selector .legend_wrapper {
  width: 146px;
  font-size: 11px;
  font-weight: 300;
  font-style: italic;
  line-height: 14px;
  border-top: 0.5px solid #f5f5f5;
  margin-top: 10px;
  padding-top: 8px;
}
/* line 83, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper .selector .legend_wrapper br,
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper .selector .legend_wrapper br {
  display: block;
  width: 10px;
}
/* line 92, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper.hide .selector_wrapper,
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper.hide .selector_wrapper {
  height: 100%;
  box-shadow: none;
}
/* line 96, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper.hide .selector_wrapper .selector_label,
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper.hide .selector_wrapper .selector_label {
  border-color: #ebebeb;
  box-shadow: none;
}
/* line 100, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper.hide .selector_wrapper .selector_label svg,
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper.hide .selector_wrapper .selector_label svg {
  transform: rotate(0deg);
}
/* line 105, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper.hide .selector_wrapper .selector,
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper.hide .selector_wrapper .selector {
  opacity: 0;
  pointer-events: none;
}
/* line 806, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper .selector {
  max-height: 175px;
}
/* line 812, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
/* line 818, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper {
  display: flex;
  font-size: 14px;
  color: #8d8d8d;
}
/* line 823, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper .legend {
  display: flex;
  align-items: center;
  margin-right: 15px;
}
/* line 828, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper .legend .icon {
  width: 24px;
  height: 24px;
  display: inline-block;
  position: relative;
  margin-right: 10px;
}
/* line 835, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper .legend .icon.dispo {
  background-color: var(--color_day_dispo);
}
/* line 839, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper .legend .icon.no_dispo {
  background-color: var(--color_day_no_dispo);
}
/* line 843, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper .legend .icon.min_stay {
  background-color: var(--color_day_min_stay);
}
/* line 847, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper .legend .icon.high_occupancy {
  background-color: var(--color_day_high_occupancy);
}
/* line 851, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper .legend .icon.cheapest_price {
  background-color: var(--color_cheapest_price);
}
/* line 858, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper.default .legend {
  margin-right: 30px;
}
/* line 862, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper.default .legend .icon.dispo {
  background-color: var(--main_color);
}
/* line 866, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper.default .legend .icon.no_dispo {
  border: 1px solid #c7c9cd;
  background: none;
}
/* line 870, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper.default .legend .icon.no_dispo:after {
  content: '';
  width: 15px;
  height: 1px;
  background: #c7c9cd;
  position: absolute;
  top: 50%;
  left: 50%;
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 879, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper.default .legend .icon.min_stay, .price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper.default .legend .icon.high_occupancy, .price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper.default .legend .icon.cheapest_price {
  background: none;
}
/* line 887, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .notice_info {
  font-size: 14px;
  font-style: italic;
  color: #8d8d8d;
  margin-top: 10px;
}
/* line 894, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .left_wrapper .notice_info_chart {
  font-size: 14px;
  font-style: italic;
  width: 724px;
  align-self: flex-start;
}
/* line 21, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .right_wrapper .selector_full_wrapper {
  position: relative;
  display: inline-block;
  z-index: 4;
  height: 30px;
}
/* line 27, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .right_wrapper .selector_full_wrapper .selector_wrapper {
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 0 0 11px 11px;
  width: 100%;
  background-color: white;
  font-size: 16px;
  font-weight: 400;
  line-height: 30px;
  min-width: 173px;
  width: max-content;
  height: initial;
  box-shadow: 0px 6px 6px #00000029;
}
/* line 42, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .right_wrapper .selector_full_wrapper .selector_wrapper .selector_label {
  text-align: center;
  border-radius: 6px;
  padding: 7px 16px;
  border: 1px solid transparent;
  padding: 3px 16px;
  cursor: pointer;
}
/* line 50, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .right_wrapper .selector_full_wrapper .selector_wrapper .selector_label svg {
  height: 7px;
  width: auto;
  margin-left: 10px;
  margin-bottom: 2px;
  transform: rotate(180deg);
}
/* line 59, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .right_wrapper .selector_full_wrapper .selector_wrapper .selector {
  padding: 10px;
  max-height: 260px;
  overflow-y: auto;
  color: #8d8d8d;
}
/* line 65, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .right_wrapper .selector_full_wrapper .selector_wrapper .selector .option {
  cursor: pointer;
}
/* line 68, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .right_wrapper .selector_full_wrapper .selector_wrapper .selector .option:hover {
  color: var(--text_color);
}
/* line 73, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .right_wrapper .selector_full_wrapper .selector_wrapper .selector .legend_wrapper {
  width: 146px;
  font-size: 11px;
  font-weight: 300;
  font-style: italic;
  line-height: 14px;
  border-top: 0.5px solid #f5f5f5;
  margin-top: 10px;
  padding-top: 8px;
}
/* line 83, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .right_wrapper .selector_full_wrapper .selector_wrapper .selector .legend_wrapper br {
  display: block;
  width: 10px;
}
/* line 92, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .right_wrapper .selector_full_wrapper.hide .selector_wrapper {
  height: 100%;
  box-shadow: none;
}
/* line 96, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .right_wrapper .selector_full_wrapper.hide .selector_wrapper .selector_label {
  border-color: #ebebeb;
  box-shadow: none;
}
/* line 100, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .right_wrapper .selector_full_wrapper.hide .selector_wrapper .selector_label svg {
  transform: rotate(0deg);
}
/* line 105, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .right_wrapper .selector_full_wrapper.hide .selector_wrapper .selector {
  opacity: 0;
  pointer-events: none;
}
/* line 905, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper .right_wrapper .toggle_chart {
  font-size: 16px;
  color: #707070;
  text-decoration: underline;
  margin-top: 15px;
  cursor: pointer;
}
/* line 916, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom {
  border-top: 1px solid #eeeeee;
  padding-top: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/* line 923, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .left_wrapper {
  display: flex;
  align-items: center;
}
/* line 927, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .left_wrapper .amount_item {
  font-size: 16px;
  color: #bebebe;
  margin-right: 25px;
}
/* line 932, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .left_wrapper .amount_item .amount {
  color: var(--text_color);
  font-size: 18px;
  font-weight: bold;
  margin-left: 5px;
}
/* line 939, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .left_wrapper .amount_item:last-child {
  text-transform: capitalize;
  margin-right: 0;
}
/* line 946, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper {
  display: flex;
  align-items: center;
}
/* line 950, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .clear_dates {
  font-size: 16px;
  color: #707070;
  text-decoration: underline;
  margin-right: 25px;
  cursor: pointer;
}
/* line 958, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .btn_booking {
  width: 320px;
  height: 52px;
  background-color: white;
  color: var(--booking_btn_color);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  border: 1px solid var(--booking_btn_color);
  overflow: hidden;
  position: relative;
  cursor: pointer;
}
/* line 973, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .btn_booking svg {
  fill: white;
  position: absolute;
  left: -20%;
  width: 35px;
  height: 35px;
  z-index: 2;
  -moz-transition: all, 0.4s;
  -o-transition: all, 0.4s;
  -webkit-transition: all, 0.4s;
  transition: all, 0.4s;
}
/* line 983, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .btn_booking .text_wrapper {
  z-index: 2;
  transition: transform 0.4s;
}
/* line 987, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .btn_booking .text_wrapper:lang(ru) {
  font-size: 16px;
}
/* line 992, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .btn_booking:after {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 100%;
  background-color: var(--booking_btn_color);
  z-index: 1;
  -moz-transition: all, 0.3s;
  -o-transition: all, 0.3s;
  -webkit-transition: all, 0.3s;
  transition: all, 0.3s;
}
/* line 1004, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .btn_booking.active {
  color: var(--text_color_selected);
}
/* line 1007, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .btn_booking.active svg {
  left: 4%;
}
/* line 1010, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .btn_booking.active svg * {
  fill: var(--text_color_selected);
}
/* line 1015, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .btn_booking.active .text_wrapper {
  transform: translateX(8%);
}
/* line 1019, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .btn_booking.active:after {
  right: 0;
}
/* line 248, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper {
  margin: 0 10px;
  display: flex;
  align-items: center;
}
/* line 253, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper span {
  font: normal normal normal 16px/22px Open Sans;
}
/* line 257, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .info-icon-wrapper {
  position: relative;
  display: inline-block;
  margin-left: 10px;
  margin-right: 5px;
  cursor: pointer;
}
/* line 264, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .info-icon-wrapper svg {
  fill: white;
  width: 20px;
  height: 16px;
  margin-bottom: -2px;
}
/* line 272, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .on_request_tooltip {
  transition: max-height 0.3s ease;
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--main_color);
  color: white;
  border-radius: 10px;
  font-size: 13px;
  width: 370px;
  z-index: 10;
  margin-bottom: 5px;
  color: white;
  max-height: 200px;
}
/* line 288, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .on_request_tooltip.is_mobile {
  width: 250px;
  left: 0;
  transform: translateX(-85%);
  margin-bottom: 10px;
}
/* line 294, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .on_request_tooltip.is_mobile.show:after {
  left: unset;
  right: 10%;
}
/* line 300, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .on_request_tooltip.hide {
  max-height: 0;
  overflow: hidden;
}
/* line 305, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .on_request_tooltip .tooltip_title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
}
/* line 310, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .on_request_tooltip .tooltip_inner_wrapper {
  padding: 25px 30px;
}
/* line 315, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .on_request_tooltip.show:after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--main_color) transparent transparent transparent;
}
/* line 326, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .switch {
  position: relative;
  width: 68px;
  border: 1px solid var(--main_color);
  height: 33px;
  border-radius: 100px;
  margin-left: 10px;
  cursor: pointer;
}
/* line 335, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .switch .switch_knob {
  font-family: "Font Awesome 6 Pro";
  position: absolute;
  top: 3px;
  left: auto;
  right: 37px;
  width: 27px;
  height: 27px;
  box-sizing: border-box;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  line-height: 0.7;
  padding: 11px 4px;
  background-color: var(--main_color);
  border-radius: 50%;
  transition: 0.3s ease all, left 0.3s cubic-bezier(0.18, 0.89, 0.35, 1.15);
}
/* line 354, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .switch .switch_knob .fa-check {
  display: none;
}
/* line 360, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .switch.active .switch_knob {
  right: 3px;
}
/* line 363, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .switch.active .switch_knob .fa-check {
  display: block;
}
/* line 367, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .switch.active .switch_knob .fa-xmark {
  display: none;
}
/* line 1028, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper {
  margin: 10px 0;
}
/* line 1031, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper span {
  order: 2;
}
/* line 1035, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .on_request_wrapper .switch {
  margin-right: 10px;
  margin-left: 0;
}
/* line 1042, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper.chart_opened {
  margin-top: 0;
}
/* line 1045, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .bottom_wrapper.chart_opened .selector_full_wrapper {
  margin-left: 26px;
}
/* line 21, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper {
  position: relative;
  display: inline-block;
  z-index: 4;
  height: 30px;
}
/* line 27, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper .selector_wrapper {
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 0 0 11px 11px;
  width: 100%;
  background-color: white;
  font-size: 16px;
  font-weight: 400;
  line-height: 30px;
  min-width: 173px;
  width: max-content;
  height: initial;
  box-shadow: 0px 6px 6px #00000029;
}
/* line 42, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector_label {
  text-align: center;
  border-radius: 6px;
  padding: 7px 16px;
  border: 1px solid transparent;
  padding: 3px 16px;
  cursor: pointer;
}
/* line 50, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector_label svg {
  height: 7px;
  width: auto;
  margin-left: 10px;
  margin-bottom: 2px;
  transform: rotate(180deg);
}
/* line 59, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector {
  padding: 10px;
  max-height: 260px;
  overflow-y: auto;
  color: #8d8d8d;
}
/* line 65, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector .option {
  cursor: pointer;
}
/* line 68, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector .option:hover {
  color: var(--text_color);
}
/* line 73, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector .legend_wrapper {
  width: 146px;
  font-size: 11px;
  font-weight: 300;
  font-style: italic;
  line-height: 14px;
  border-top: 0.5px solid #f5f5f5;
  margin-top: 10px;
  padding-top: 8px;
}
/* line 83, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector .legend_wrapper br {
  display: block;
  width: 10px;
}
/* line 92, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper.hide .selector_wrapper {
  height: 100%;
  box-shadow: none;
}
/* line 96, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper.hide .selector_wrapper .selector_label {
  border-color: #ebebeb;
  box-shadow: none;
}
/* line 100, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper.hide .selector_wrapper .selector_label svg {
  transform: rotate(0deg);
}
/* line 105, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper.hide .selector_wrapper .selector {
  opacity: 0;
  pointer-events: none;
}
/* line 117, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper {
  position: relative;
  display: inline-block;
  z-index: 1;
}
/* line 122, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper.show {
  z-index: 4;
}
/* line 126, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper .selector_wrapper {
  font-size: 16px;
  font-weight: 500;
  line-height: 30px;
  border-radius: 11px;
  min-width: 173px;
  position: absolute;
  left: 50%;
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -webkit-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}
/* line 134, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector_label {
  font-size: 24px;
  font-weight: bold;
  box-shadow: none;
}
/* line 139, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector_label svg {
  display: inline;
  margin-bottom: 4px;
}
/* line 144, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector_label .selector {
  padding: 10px 20px;
}
/* line 152, ../sass/mixins.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper.hide .selector_wrapper .selector_label {
  border-color: transparent;
}
/* line 1056, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .chart_wrapper .month_selector .selector_full_wrapper .selector_wrapper {
  transform: none;
}
/* line 1062, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .chart_wrapper .recharts-wrapper {
  margin: 50px 20px 0 55px;
}
/* line 1065, ../sass/calendar_styles.scss */
.price_calendar_wrapper .full_container .chart_wrapper .recharts-wrapper svg {
  overflow: visible;
}
/* line 1072, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile {
  width: 100%;
  box-shadow: none;
}
/* line 1076, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container {
  padding: 40px 0 30px 0;
}
/* line 1079, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .title {
  font-size: 15px;
  margin-bottom: 10px;
  padding: 0 25px;
}
/* line 1085, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .date_suggestions_wrapper {
  position: relative;
  padding: 0 25px;
}
/* line 1089, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .date_suggestions_wrapper .description {
  font-size: 14px;
  padding: 0;
  margin-bottom: 25px;
}
/* line 1095, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .date_suggestions_wrapper .slider_wrapper {
  width: 95%;
  transform: none;
}
/* line 1099, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .date_suggestions_wrapper .slider_wrapper .date_suggestion {
  width: 100px;
  padding: 10px 0;
}
/* line 1103, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .date_suggestions_wrapper .slider_wrapper .date_suggestion .suggestion {
  font-size: 12px;
}
/* line 1107, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .date_suggestions_wrapper .slider_wrapper .date_suggestion .nights {
  font-size: 12px;
  display: none;
}
/* line 1112, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .date_suggestions_wrapper .slider_wrapper .date_suggestion .price {
  font-size: 11px;
}
/* line 1118, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .date_suggestions_wrapper .slider_wrapper .slider_navs .slider_nav {
  border: none;
  width: 16px;
  height: 16px;
}
/* line 1123, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .date_suggestions_wrapper .slider_wrapper .slider_navs .slider_nav svg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
/* line 1129, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .date_suggestions_wrapper .slider_wrapper .slider_navs .slider_nav.slider_prev {
  right: 20px;
}
/* line 1133, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .date_suggestions_wrapper .slider_wrapper .slider_navs .slider_nav.slider_next {
  left: 20px;
}
/* line 1137, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .date_suggestions_wrapper .slider_wrapper .slider_navs .slider_nav.disabled {
  opacity: 0.2;
}
/* line 1144, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .date_suggestions_wrapper:after {
  content: '';
  width: 100%;
  height: 1px;
  background-color: #e5e5e5;
  position: absolute;
  bottom: -40px;
  left: 0;
}
/* line 1155, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper {
  display: block;
  position: relative;
  margin-top: 65px;
}
/* line 1160, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .month_full_wrapper {
  margin-bottom: 20px;
}
/* line 1163, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper {
  position: initial;
  margin-bottom: 15px;
}
/* line 1168, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .change_month_btn.main {
  left: 15px;
}
/* line 1172, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .change_month_btn:not(.main) {
  right: 15px;
}
/* line 1179, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper .selector_label {
  font-size: 20px;
}
/* line 1186, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper {
  border-spacing: 0 5px;
}
/* line 1191, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper thead tr td {
  font-size: 16px;
}
/* line 1199, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper {
  width: 52px;
  height: 51px;
}
/* line 1203, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .day {
  font-size: 18px;
}
/* line 1207, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .no_dispo_text {
  font-size: 6px;
}
/* line 1211, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .day_tooltip {
  display: none;
}
/* line 1215, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.selected.start_date + .hover:after {
  width: calc(100% + 9px);
}
/* line 1219, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.hover:after {
  width: 100%;
}
/* line 1225, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.last_day_available .last_day_text:before {
  left: 9px;
}
/* line 1236, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .full_container .calendar_wrapper .range-label {
  font-size: 10px;
}
/* line 1243, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper {
  padding: 0 25px;
}
/* line 1247, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .top .info_currency_wrapper {
  display: block;
}
/* line 1251, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper {
  display: block;
}
/* line 1254, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper .legend {
  margin-top: 10px;
  font-size: 14px;
}
/* line 1258, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper .legend .icon {
  width: 20px;
  height: 20px;
  margin-right: 15px;
}
/* line 1265, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .top .info_currency_wrapper .left_wrapper .legends_wrapper .legend:not(:first-child) .icon {
  width: 19px;
  height: 19px;
}
/* line 1273, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .top .info_currency_wrapper .left_wrapper .notice_info {
  margin-top: 30px;
}
/* line 1280, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom {
  display: block;
  border: none;
}
/* line 1285, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .left_wrapper .amount_item, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .left_wrapper .confirm_dates_button_wrapper, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom:has(.confirm_dates_button_wrapper) .amount_item, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom:has(.confirm_dates_button_wrapper) .confirm_dates_button_wrapper {
  margin-right: 20px;
}
/* line 1288, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .left_wrapper .amount_item:last-child, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .left_wrapper .confirm_dates_button_wrapper:last-child, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom:has(.confirm_dates_button_wrapper) .amount_item:last-child, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom:has(.confirm_dates_button_wrapper) .confirm_dates_button_wrapper:last-child {
  position: fixed;
  width: 100%;
  left: 0vw;
  bottom: 0vh;
  box-shadow: 0px -3px 6px #5f5f5f19;
  padding: 20px 20px;
  background-color: white;
  z-index: 6;
}
/* line 1298, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .left_wrapper .amount_item:last-child .amount, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .left_wrapper .confirm_dates_button_wrapper:last-child .amount, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom:has(.confirm_dates_button_wrapper) .amount_item:last-child .amount, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom:has(.confirm_dates_button_wrapper) .confirm_dates_button_wrapper:last-child .amount {
  font-weight: bold;
}
/* line 1302, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .left_wrapper .amount_item:last-child .confirm_dates_button, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .left_wrapper .confirm_dates_button_wrapper:last-child .confirm_dates_button, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom:has(.confirm_dates_button_wrapper) .amount_item:last-child .confirm_dates_button, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom:has(.confirm_dates_button_wrapper) .confirm_dates_button_wrapper:last-child .confirm_dates_button {
  width: 100%;
  height: 52px;
  color: white;
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  background-color: var(--booking_btn_color);
  border: 1px solid var(--booking_btn_color);
  overflow: hidden;
  cursor: pointer;
}
/* line 1314, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .left_wrapper .amount_item:last-child .confirm_dates_button.disabled, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .left_wrapper .confirm_dates_button_wrapper:last-child .confirm_dates_button.disabled, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom:has(.confirm_dates_button_wrapper) .amount_item:last-child .confirm_dates_button.disabled, .price_calendar_wrapper.is_mobile .bottom_wrapper .bottom:has(.confirm_dates_button_wrapper) .confirm_dates_button_wrapper:last-child .confirm_dates_button.disabled {
  color: var(--booking_btn_color);
  background-color: white;
  cursor: default;
}
/* line 1325, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .right_wrapper .clear_dates {
  margin-top: 20px;
}
/* line 1329, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .right_wrapper .btn_booking {
  position: fixed;
  width: 140px;
  height: 40px;
  right: 5vw;
  bottom: 1.5vh;
  font-size: 20px;
  z-index: 6;
}
/* line 1340, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper {
  display: block;
  margin-top: 20px;
}
/* line 161, ../sass/mixins.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper,
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper {
  position: relative;
  display: block;
  z-index: 4;
  width: 100%;
  height: 55px;
  margin-bottom: 10px;
}
/* line 169, ../sass/mixins.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper,
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper {
  overflow: hidden;
  width: 100%;
  background-color: white;
  font-weight: 400;
  line-height: 30px;
  font-size: 16px;
  border-radius: 0;
  position: absolute;
  left: 0;
  top: 0;
  box-shadow: 0px 6px 6px #00000029;
}
/* line 182, ../sass/mixins.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper .selector_label,
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper .selector_label {
  text-align: center;
  border: 1px solid var(--main_color);
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
}
/* line 191, ../sass/mixins.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper .selector_label svg,
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper .selector_label svg {
  margin-right: 15px;
}
/* line 194, ../sass/mixins.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper .selector_label svg *,
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper .selector_label svg * {
  fill: var(--main_color);
}
/* line 200, ../sass/mixins.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper .selector,
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper .selector {
  padding: 10px;
  max-height: 170px;
  overflow-y: auto;
  color: #8d8d8d;
  font-size: 16px;
}
/* line 207, ../sass/mixins.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper .selector .option,
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper .selector .option {
  cursor: pointer;
  color: var(--text_color);
}
/* line 212, ../sass/mixins.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper .selector_wrapper .selector .legend_wrapper,
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper .selector_wrapper .selector .legend_wrapper {
  font-size: 11px;
  font-weight: 300;
  font-style: italic;
  line-height: 14px;
  border-top: 0.5px solid #f5f5f5;
  margin-top: 10px;
  padding-top: 8px;
}
/* line 225, ../sass/mixins.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper.hide .selector_wrapper,
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper.hide .selector_wrapper {
  height: 100%;
  box-shadow: none;
}
/* line 229, ../sass/mixins.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper.hide .selector_wrapper .selector_label,
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper.hide .selector_wrapper .selector_label {
  border: 1px solid var(--main_color);
  box-shadow: none;
}
/* line 233, ../sass/mixins.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper.hide .selector_wrapper .selector_label svg,
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper.hide .selector_wrapper .selector_label svg {
  transform: rotate(0deg);
}
/* line 238, ../sass/mixins.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .filters_selector_wrapper .selector_full_wrapper.hide .selector_wrapper .selector,
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .currency_selector_wrapper .selector_full_wrapper.hide .selector_wrapper .selector {
  opacity: 0;
  pointer-events: none;
}
/* line 1349, ../sass/calendar_styles.scss */
.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .selectors_wrapper .currency_selector_wrapper {
  position: relative;
  z-index: 5;
}

/* This fires as soon as the element enters the DOM*/
/* line 1360, ../sass/calendar_styles.scss */
.list-transition-enter {
  opacity: 0;
}

/* This is where we can add the transition*/
/* line 1364, ../sass/calendar_styles.scss */
.list-transition-enter-active {
  opacity: 1;
  transition: all 600ms;
}

/* This fires as soon as the this.state.showList is false */
/* fires as element leaves the DOM*/
