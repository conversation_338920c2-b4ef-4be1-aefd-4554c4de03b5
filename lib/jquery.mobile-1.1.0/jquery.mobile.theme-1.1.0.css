/*
* jQuery Mobile Framework 1.1.0 db342b1f315c282692791aa870455901fdb46a55
* http://jquerymobile.com
*
* Copyright 2011 (c) jQuery Project
* Dual licensed under the MIT or GPL Version 2 licenses.
* http://jquery.org/license
*
*/
/* Swatches */

/* A
-----------------------------------------------------------------------------------------------------------*/

.ui-bar-a {
	border: 1px solid 		#333 /*{a-bar-border}*/;
	background: 			#111111 /*{a-bar-background-color}*/;
	color: 					#ffffff /*{a-bar-color}*/;
	font-weight: bold;
	text-shadow: 0 /*{a-bar-shadow-x}*/ -1px /*{a-bar-shadow-y}*/ 1px /*{a-bar-shadow-radius}*/ #000000 /*{a-bar-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #3c3c3c /*{a-bar-background-start}*/), to( #111 /*{a-bar-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #3c3c3c /*{a-bar-background-start}*/, #111 /*{a-bar-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #3c3c3c /*{a-bar-background-start}*/, #111 /*{a-bar-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #3c3c3c /*{a-bar-background-start}*/, #111 /*{a-bar-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #3c3c3c /*{a-bar-background-start}*/, #111 /*{a-bar-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #3c3c3c /*{a-bar-background-start}*/, #111 /*{a-bar-background-end}*/);
}
.ui-bar-a, 
.ui-bar-a input, 
.ui-bar-a select, 
.ui-bar-a textarea, 
.ui-bar-a button {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
}
.ui-bar-a .ui-link-inherit {
	color: #fff /*{a-bar-color}*/;
}

.ui-bar-a .ui-link {
	color: #7cc4e7 /*{a-bar-link-color}*/;
	font-weight: bold;
}

.ui-bar-a .ui-link:hover {
	color: #2489CE /*{a-bar-link-hover}*/;
}

.ui-bar-a .ui-link:active {
	color: #2489CE /*{a-bar-link-active}*/;
}

.ui-bar-a .ui-link:visited {
    color: #2489CE /*{a-bar-link-visited}*/;
}
.ui-body-a,
.ui-overlay-a {
	border: 1px solid 		#444 /*{a-body-border}*/;
	background: 			#222 /*{a-body-background-color}*/;
	color: 					#fff /*{a-body-color}*/;
	text-shadow: 0 /*{a-body-shadow-x}*/ 1px /*{a-body-shadow-y}*/ 1px /*{a-body-shadow-radius}*/ #111 /*{a-body-shadow-color}*/;
	font-weight: normal;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #444 /*{a-body-background-start}*/), to( #222 /*{a-body-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #444 /*{a-body-background-start}*/, #222 /*{a-body-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #444 /*{a-body-background-start}*/, #222 /*{a-body-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #444 /*{a-body-background-start}*/, #222 /*{a-body-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #444 /*{a-body-background-start}*/, #222 /*{a-body-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #444 /*{a-body-background-start}*/, #222 /*{a-body-background-end}*/);	
}
.ui-overlay-a {
	background-image: none;
	border-width: 0;
}
.ui-body-a,
.ui-body-a input,
.ui-body-a select,
.ui-body-a textarea,
.ui-body-a button {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
}
.ui-body-a .ui-link-inherit {
	color: 	#fff /*{a-body-color}*/;
}

.ui-body-a .ui-link {
	color: #2489CE /*{a-body-link-color}*/;
	font-weight: bold;
}

.ui-body-a .ui-link:hover {
	color: #2489CE /*{a-body-link-hover}*/;
}

.ui-body-a .ui-link:active {
	color: #2489CE /*{a-body-link-active}*/;
}

.ui-body-a .ui-link:visited {
    color: #2489CE /*{a-body-link-visited}*/;
}

.ui-btn-up-a {
	border: 1px solid 		#111 /*{a-bup-border}*/;
	background: 			#333 /*{a-bup-background-color}*/;
	font-weight: bold;
	color: 					#fff /*{a-bup-color}*/;
	text-shadow: 0 /*{a-bup-shadow-x}*/ 1px /*{a-bup-shadow-y}*/ 1px /*{a-bup-shadow-radius}*/ #111 /*{a-bup-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #444444 /*{a-bup-background-start}*/), to( #2d2d2d /*{a-bup-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #444444 /*{a-bup-background-start}*/, #2d2d2d /*{a-bup-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #444444 /*{a-bup-background-start}*/, #2d2d2d /*{a-bup-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #444444 /*{a-bup-background-start}*/, #2d2d2d /*{a-bup-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #444444 /*{a-bup-background-start}*/, #2d2d2d /*{a-bup-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #444444 /*{a-bup-background-start}*/, #2d2d2d /*{a-bup-background-end}*/);
}
.ui-btn-up-a a.ui-link-inherit {
	color: 					#fff /*{a-bup-color}*/;
}
.ui-btn-hover-a {
	border: 1px solid 		#000 /*{a-bhover-border}*/;
	background: 			#444444 /*{a-bhover-background-color}*/;
	font-weight: bold;
	color: 					#fff /*{a-bhover-color}*/;
	text-shadow: 0 /*{a-bhover-shadow-x}*/ 1px /*{a-bhover-shadow-y}*/ 1px /*{a-bhover-shadow-radius}*/ #111 /*{a-bhover-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #555555 /*{a-bhover-background-start}*/), to( #383838 /*{a-bhover-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #555555 /*{a-bhover-background-start}*/, #383838 /*{a-bhover-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #555555 /*{a-bhover-background-start}*/, #383838 /*{a-bhover-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #555555 /*{a-bhover-background-start}*/, #383838 /*{a-bhover-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #555555 /*{a-bhover-background-start}*/, #383838 /*{a-bhover-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #555555 /*{a-bhover-background-start}*/, #383838 /*{a-bhover-background-end}*/);
}
.ui-btn-hover-a a.ui-link-inherit {
	color: 					#fff /*{a-bhover-color}*/;
}
.ui-btn-down-a {
	border: 1px solid 		#000 /*{a-bdown-border}*/;
	background: 			#222 /*{a-bdown-background-color}*/;
	font-weight: bold;
	color: 					#fff /*{a-bdown-color}*/;
	text-shadow: 0 /*{a-bdown-shadow-x}*/ 1px /*{a-bdown-shadow-y}*/ 1px /*{a-bdown-shadow-radius}*/ #111 /*{a-bdown-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #202020 /*{a-bdown-background-start}*/), to( #2c2c2c /*{a-bdown-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #202020 /*{a-bdown-background-start}*/, #2c2c2c /*{a-bdown-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #202020 /*{a-bdown-background-start}*/, #2c2c2c /*{a-bdown-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #202020 /*{a-bdown-background-start}*/, #2c2c2c /*{a-bdown-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #202020 /*{a-bdown-background-start}*/, #2c2c2c /*{a-bdown-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #202020 /*{a-bdown-background-start}*/, #2c2c2c /*{a-bdown-background-end}*/);
}
.ui-btn-down-a a.ui-link-inherit {
	color: 					#fff /*{a-bdown-color}*/;
}
.ui-btn-up-a,
.ui-btn-hover-a,
.ui-btn-down-a {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
	text-decoration: none;
}


/* B
-----------------------------------------------------------------------------------------------------------*/
.ui-bar-b {
	border: 1px solid 		#456f9a /*{b-bar-border}*/;
	background: 			#5e87b0 /*{b-bar-background-color}*/;
	color: 					#fff /*{b-bar-color}*/;
	font-weight: bold;
	text-shadow: 0 /*{b-bar-shadow-x}*/ 1px /*{b-bar-shadow-y}*/ 1px /*{b-bar-shadow-radius}*/ #3e6790 /*{b-bar-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #6facd5 /*{b-bar-background-start}*/), to( #497bae /*{b-bar-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #6facd5 /*{b-bar-background-start}*/, #497bae /*{b-bar-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #6facd5 /*{b-bar-background-start}*/, #497bae /*{b-bar-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #6facd5 /*{b-bar-background-start}*/, #497bae /*{b-bar-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #6facd5 /*{b-bar-background-start}*/, #497bae /*{b-bar-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #6facd5 /*{b-bar-background-start}*/, #497bae /*{b-bar-background-end}*/);
}
.ui-bar-b,
.ui-bar-b input,
.ui-bar-b select,
.ui-bar-b textarea,
.ui-bar-b button {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
}
.ui-bar-b .ui-link-inherit {
	color: 	#fff /*{b-bar-color}*/;
}
.ui-bar-b .ui-link {
	color: #ddf0f8 /*{b-bar-link-color}*/;
	font-weight: bold;
}

.ui-bar-b .ui-link:hover {
	color: #ddf0f8 /*{b-bar-link-hover}*/;
}

.ui-bar-b .ui-link:active {
	color: #ddf0f8 /*{b-bar-link-active}*/;
}

.ui-bar-b .ui-link:visited {
    color: #ddf0f8 /*{b-bar-link-visited}*/;
}
.ui-body-b,
.ui-overlay-b {
	border: 1px solid 		#999 /*{b-body-border}*/;
	background: 			#f3f3f3 /*{b-body-background-color}*/;
	color: 					#222222 /*{b-body-color}*/;
	text-shadow: 0 /*{b-body-shadow-x}*/ 1px /*{b-body-shadow-y}*/ 0 /*{b-body-shadow-radius}*/ #fff /*{b-body-shadow-color}*/;
	font-weight: normal;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #ddd /*{b-body-background-start}*/), to( #ccc /*{b-body-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #ddd /*{b-body-background-start}*/, #ccc /*{b-body-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #ddd /*{b-body-background-start}*/, #ccc /*{b-body-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #ddd /*{b-body-background-start}*/, #ccc /*{b-body-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #ddd /*{b-body-background-start}*/, #ccc /*{b-body-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #ddd /*{b-body-background-start}*/, #ccc /*{b-body-background-end}*/);
}
.ui-overlay-b {
	background-image: none;
	border-width: 0;
}
.ui-body-b,
.ui-body-b input,
.ui-body-b select,
.ui-body-b textarea,
.ui-body-b button {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
}
.ui-body-b .ui-link-inherit {
	color: 	#333333 /*{b-body-color}*/;
}

.ui-body-b .ui-link {
	color: #2489CE /*{b-body-link-color}*/;
	font-weight: bold;
}

.ui-body-b .ui-link:hover {
	color: #2489CE /*{b-body-link-hover}*/;
}

.ui-body-b .ui-link:active {
	color: #2489CE /*{b-body-link-active}*/;
}

.ui-body-b .ui-link:visited {
    color: #2489CE /*{b-body-link-visited}*/;
}

.ui-btn-up-b {
	border: 1px solid 		#044062 /*{b-bup-border}*/;
	background: 			#396b9e /*{b-bup-background-color}*/;
	font-weight: bold;
	color: 					#fff /*{b-bup-color}*/;
	text-shadow: 0 /*{b-bup-shadow-x}*/ 1px /*{b-bup-shadow-y}*/ 1px /*{b-bup-shadow-radius}*/ #194b7e /*{b-bup-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #5f9cc5 /*{b-bup-background-start}*/), to( #396b9e /*{b-bup-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #5f9cc5 /*{b-bup-background-start}*/, #396b9e /*{b-bup-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #5f9cc5 /*{b-bup-background-start}*/, #396b9e /*{b-bup-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #5f9cc5 /*{b-bup-background-start}*/, #396b9e /*{b-bup-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #5f9cc5 /*{b-bup-background-start}*/, #396b9e /*{b-bup-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #5f9cc5 /*{b-bup-background-start}*/, #396b9e /*{b-bup-background-end}*/);
}
.ui-btn-up-b a.ui-link-inherit {
	color: 					#fff /*{b-bup-color}*/;
}
.ui-btn-hover-b {
	border: 1px solid 		#00415e /*{b-bhover-border}*/;
	background: 			#4b88b6 /*{b-bhover-background-color}*/;
	font-weight: bold;
	color: 					#fff /*{b-bhover-color}*/;
	text-shadow: 0 /*{b-bhover-shadow-x}*/ 1px /*{b-bhover-shadow-y}*/ 1px /*{b-bhover-shadow-radius}*/ #194b7e /*{b-bhover-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #6facd5 /*{b-bhover-background-start}*/), to( #4272a4 /*{b-bhover-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #6facd5 /*{b-bhover-background-start}*/, #4272a4 /*{b-bhover-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #6facd5 /*{b-bhover-background-start}*/, #4272a4 /*{b-bhover-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #6facd5 /*{b-bhover-background-start}*/, #4272a4 /*{b-bhover-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #6facd5 /*{b-bhover-background-start}*/, #4272a4 /*{b-bhover-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #6facd5 /*{b-bhover-background-start}*/, #4272a4 /*{b-bhover-background-end}*/);
}
.ui-btn-hover-b a.ui-link-inherit {
	color: 					#fff /*{b-bhover-color}*/;
}
.ui-btn-down-b {
	border: 1px solid 		#225377 /*{b-bdown-border}*/;
	background: 			#4e89c5 /*{b-bdown-background-color}*/;
	font-weight: bold;
	color: 					#fff /*{b-bdown-color}*/;
	text-shadow: 0 /*{b-bdown-shadow-x}*/ 1px /*{b-bdown-shadow-y}*/ 1px /*{b-bdown-shadow-radius}*/ #194b7e /*{b-bdown-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #295b8e /*{b-bdown-background-start}*/), to( #3e79b5 /*{b-bdown-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #295b8e /*{b-bdown-background-start}*/, #3e79b5 /*{b-bdown-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #295b8e /*{b-bdown-background-start}*/, #3e79b5 /*{b-bdown-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #295b8e /*{b-bdown-background-start}*/, #3e79b5 /*{b-bdown-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #295b8e /*{b-bdown-background-start}*/, #3e79b5 /*{b-bdown-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #295b8e /*{b-bdown-background-start}*/, #3e79b5 /*{b-bdown-background-end}*/);
}
.ui-btn-down-b a.ui-link-inherit {
	color: 					#fff /*{b-bdown-color}*/;
}
.ui-btn-up-b,
.ui-btn-hover-b,
.ui-btn-down-b {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
	text-decoration: none;
}


/* C
-----------------------------------------------------------------------------------------------------------*/

.ui-bar-c {
	border: 1px solid 		#B3B3B3 /*{c-bar-border}*/;
	background: 			#eeeeee /*{c-bar-background-color}*/;
	color: 					#3E3E3E /*{c-bar-color}*/;
	font-weight: bold;
	text-shadow: 0 /*{c-bar-shadow-x}*/ 1px /*{c-bar-shadow-y}*/ 1px /*{c-bar-shadow-radius}*/ 	#fff /*{c-bar-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #f0f0f0 /*{c-bar-background-start}*/), to( #ddd /*{c-bar-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #f0f0f0 /*{c-bar-background-start}*/, #ddd /*{c-bar-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #f0f0f0 /*{c-bar-background-start}*/, #ddd /*{c-bar-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #f0f0f0 /*{c-bar-background-start}*/, #ddd /*{c-bar-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #f0f0f0 /*{c-bar-background-start}*/, #ddd /*{c-bar-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #f0f0f0 /*{c-bar-background-start}*/, #ddd /*{c-bar-background-end}*/);
}

.ui-bar-c .ui-link-inherit {
	color: 	#3E3E3E /*{c-bar-color}*/;
}
.ui-bar-c .ui-link {
	color: #7cc4e7 /*{c-bar-link-color}*/;
	font-weight: bold;
}

.ui-bar-c .ui-link:hover {
	color: #2489CE /*{c-bar-link-hover}*/;
}

.ui-bar-c .ui-link:active {
	color: #2489CE /*{c-bar-link-active}*/;
}

.ui-bar-c .ui-link:visited {
    color: #2489CE /*{c-bar-link-visited}*/;
}

.ui-bar-c,
.ui-bar-c input,
.ui-bar-c select,
.ui-bar-c textarea,
.ui-bar-c button {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
}
.ui-body-c,
.ui-overlay-c {
	border: 1px solid 		#aaa /*{c-body-border}*/;
	color: 					#333333 /*{c-body-color}*/;
	text-shadow: 0 /*{c-body-shadow-x}*/ 1px /*{c-body-shadow-y}*/ 0 /*{c-body-shadow-radius}*/ #fff /*{c-body-shadow-color}*/;
	background: 			#f9f9f9 /*{c-body-background-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #f9f9f9 /*{c-body-background-start}*/), to( #eeeeee /*{c-body-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #f9f9f9 /*{c-body-background-start}*/, #eeeeee /*{c-body-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #f9f9f9 /*{c-body-background-start}*/, #eeeeee /*{c-body-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #f9f9f9 /*{c-body-background-start}*/, #eeeeee /*{c-body-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #f9f9f9 /*{c-body-background-start}*/, #eeeeee /*{c-body-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #f9f9f9 /*{c-body-background-start}*/, #eeeeee /*{c-body-background-end}*/);
}
.ui-overlay-c {
	background-image: none;
	border-width: 0;
}
.ui-body-c,
.ui-body-c input,
.ui-body-c select,
.ui-body-c textarea,
.ui-body-c button {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
}

.ui-body-c .ui-link-inherit {
	color: 	#333333 /*{c-body-color}*/;
}

.ui-body-c .ui-link {
	color: #2489CE /*{c-body-link-color}*/;
	font-weight: bold;
}

.ui-body-c .ui-link:hover {
	color: #2489CE /*{c-body-link-hover}*/;
}

.ui-body-c .ui-link:active {
	color: #2489CE /*{c-body-link-active}*/;
}

.ui-body-c .ui-link:visited {
    color: #2489CE /*{c-body-link-visited}*/;
}

.ui-btn-up-c {
	border: 1px solid 		#ccc /*{c-bup-border}*/;
	background: 			#eee /*{c-bup-background-color}*/;
	font-weight: bold;
	color: 					#222 /*{c-bup-color}*/;
	text-shadow: 0 /*{c-bup-shadow-x}*/ 1px /*{c-bup-shadow-y}*/ 0 /*{c-bup-shadow-radius}*/ #ffffff /*{c-bup-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #ffffff /*{c-bup-background-start}*/), to( #f1f1f1 /*{c-bup-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #ffffff /*{c-bup-background-start}*/, #f1f1f1 /*{c-bup-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #ffffff /*{c-bup-background-start}*/, #f1f1f1 /*{c-bup-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #ffffff /*{c-bup-background-start}*/, #f1f1f1 /*{c-bup-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #ffffff /*{c-bup-background-start}*/, #f1f1f1 /*{c-bup-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #ffffff /*{c-bup-background-start}*/, #f1f1f1 /*{c-bup-background-end}*/);
}
.ui-btn-up-c a.ui-link-inherit {
	color: 					#2F3E46 /*{c-bup-color}*/;
}

.ui-btn-hover-c {
	border: 1px solid 		#bbb /*{c-bhover-border}*/;
	background: 			#dfdfdf /*{c-bhover-background-color}*/;
	font-weight: bold;
	color: 					#222 /*{c-bhover-color}*/;
	text-shadow: 0 /*{c-bhover-shadow-x}*/ 1px /*{c-bhover-shadow-y}*/ 0 /*{c-bhover-shadow-radius}*/ #ffffff /*{c-bhover-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #f6f6f6 /*{c-bhover-background-start}*/), to( #e0e0e0 /*{c-bhover-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #f9f9f9 /*{c-bhover-background-start}*/, #e0e0e0 /*{c-bhover-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #f6f6f6 /*{c-bhover-background-start}*/, #e0e0e0 /*{c-bhover-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #f6f6f6 /*{c-bhover-background-start}*/, #e0e0e0 /*{c-bhover-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #f6f6f6 /*{c-bhover-background-start}*/, #e0e0e0 /*{c-bhover-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #f6f6f6 /*{c-bhover-background-start}*/, #e0e0e0 /*{c-bhover-background-end}*/);
}
.ui-btn-hover-c a.ui-link-inherit {
	color: 					#2F3E46 /*{c-bhover-color}*/;
}
.ui-btn-down-c {
	border: 1px solid 		#bbb /*{c-bdown-border}*/;
	background: 			#d6d6d6 /*{c-bdown-background-color}*/;
	font-weight: bold;
	color: 					#222 /*{c-bdown-color}*/;
	text-shadow: 0 /*{c-bdown-shadow-x}*/ 1px /*{c-bdown-shadow-y}*/ 0 /*{c-bdown-shadow-radius}*/ #ffffff /*{c-bdown-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #d0d0d0 /*{c-bdown-background-start}*/), to( #dfdfdf /*{c-bdown-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #d0d0d0 /*{c-bdown-background-start}*/, #dfdfdf /*{c-bdown-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #d0d0d0 /*{c-bdown-background-start}*/, #dfdfdf /*{c-bdown-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #d0d0d0 /*{c-bdown-background-start}*/, #dfdfdf /*{c-bdown-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #d0d0d0 /*{c-bdown-background-start}*/, #dfdfdf /*{c-bdown-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #d0d0d0 /*{c-bdown-background-start}*/, #dfdfdf /*{c-bdown-background-end}*/);
}
.ui-btn-down-c a.ui-link-inherit {
	color: 					#2F3E46 /*{c-bdown-color}*/;
}
.ui-btn-up-c,
.ui-btn-hover-c,
.ui-btn-down-c {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
	text-decoration: none;
}


/* D
-----------------------------------------------------------------------------------------------------------*/

.ui-bar-d {
	border: 1px solid 		#bbb /*{d-bar-border}*/;
	background: 			#bbb /*{d-bar-background-color}*/;
	color: 					#333 /*{d-bar-color}*/;
	text-shadow: 0 /*{d-bar-shadow-x}*/ 1px /*{d-bar-shadow-y}*/ 0 /*{d-bar-shadow-radius}*/ #eee /*{d-bar-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #ddd /*{d-bar-background-start}*/), to( #bbb /*{d-bar-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #ddd /*{d-bar-background-start}*/, #bbb /*{d-bar-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #ddd /*{d-bar-background-start}*/, #bbb /*{d-bar-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #ddd /*{d-bar-background-start}*/, #bbb /*{d-bar-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #ddd /*{d-bar-background-start}*/, #bbb /*{d-bar-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #ddd /*{d-bar-background-start}*/, #bbb /*{d-bar-background-end}*/);
}
.ui-bar-d,
.ui-bar-d input,
.ui-bar-d select,
.ui-bar-d textarea,
.ui-bar-d button {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
}

.ui-bar-d .ui-link-inherit {
	color: 	#333333 /*{d-bar-color}*/;
}
.ui-bar-d .ui-link {
	color: #2489CE /*{d-bar-link-color}*/;
	font-weight: bold;
}

.ui-bar-d .ui-link:hover {
	color: #2489CE /*{d-bar-link-hover}*/;
}

.ui-bar-d .ui-link:active {
	color: #2489CE /*{d-bar-link-active}*/;
}

.ui-bar-d .ui-link:visited {
    color: #2489CE /*{d-bar-link-visited}*/;
}

.ui-body-d,
.ui-overlay-d {
	border: 1px solid 		#bbb /*{d-body-border}*/;
	color: 					#333333 /*{d-body-color}*/;
	text-shadow: 0 /*{d-body-shadow-x}*/ 1px /*{d-body-shadow-y}*/ 0 /*{d-body-shadow-radius}*/ 	#fff /*{d-body-shadow-color}*/;
	background: 			#ffffff /*{d-body-background-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #fff), to( #fff /*{d-body-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #fff /*{d-body-background-start}*/, #fff /*{d-body-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #fff /*{d-body-background-start}*/, #fff /*{d-body-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #fff /*{d-body-background-start}*/, #fff /*{d-body-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #fff /*{d-body-background-start}*/, #fff /*{d-body-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #fff /*{d-body-background-start}*/, #fff /*{d-body-background-end}*/);
}
.ui-overlay-d {
	background-image: none;
	border-width: 0;
}
.ui-body-d,
.ui-body-d input,
.ui-body-d select,
.ui-body-d textarea,
.ui-body-d button {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
}

.ui-body-d .ui-link-inherit {
	color: 	#333333 /*{d-body-color}*/;
}

.ui-body-d .ui-link {
	color: #2489CE /*{d-body-link-color}*/;
	font-weight: bold;
}

.ui-body-d .ui-link:hover {
	color: #2489CE /*{d-body-link-hover}*/;
}

.ui-body-d .ui-link:active {
	color: #2489CE /*{d-body-link-active}*/;
}

.ui-body-d .ui-link:visited {
    color: #2489CE /*{d-body-link-visited}*/;
}

.ui-btn-up-d {
	border: 1px solid 		#bbb /*{d-bup-border}*/;
	background: 			#fff /*{d-bup-background-color}*/;
	font-weight: bold;
	color: 					#333 /*{d-bup-color}*/;
	text-shadow: 0 /*{d-bup-shadow-x}*/ 1px /*{d-bup-shadow-y}*/ 0 /*{d-bup-shadow-radius}*/ #fff /*{d-bup-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #fafafa), to( #f6f6f6 /*{d-bup-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #fafafa /*{d-bup-background-start}*/, #f6f6f6 /*{d-bup-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #fafafa /*{d-bup-background-start}*/, #f6f6f6 /*{d-bup-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #fafafa /*{d-bup-background-start}*/, #f6f6f6 /*{d-bup-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #fafafa /*{d-bup-background-start}*/, #f6f6f6 /*{d-bup-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #fafafa /*{d-bup-background-start}*/, #f6f6f6 /*{d-bup-background-end}*/);
}
.ui-btn-up-d a.ui-link-inherit {
	color: 					#333 /*{d-bup-color}*/;
}
.ui-btn-hover-d {
	border: 1px solid 		#aaa /*{d-bhover-border}*/;
	background: 			#eeeeee /*{d-bhover-background-color}*/;
	font-weight: bold;
	color: 					#333 /*{d-bhover-color}*/;
	cursor: pointer;
	text-shadow: 0 /*{d-bhover-shadow-x}*/ 1px /*{d-bhover-shadow-y}*/ 0 /*{d-bhover-shadow-radius}*/ 	#fff /*{d-bhover-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #eee), to( #fff /*{d-bhover-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #eee /*{d-bhover-background-start}*/, #fff /*{d-bhover-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #eee /*{d-bhover-background-start}*/, #fff /*{d-bhover-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #eee /*{d-bhover-background-start}*/, #fff /*{d-bhover-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #eee /*{d-bhover-background-start}*/, #fff /*{d-bhover-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #eee /*{d-bhover-background-start}*/, #fff /*{d-bhover-background-end}*/);
}
.ui-btn-hover-d a.ui-link-inherit {
	color: 					#333 /*{d-bhover-color}*/;
}
.ui-btn-down-d {
	border: 1px solid 		#aaa /*{d-bdown-border}*/;
	background: 			#eee /*{d-bdown-background-color}*/;
	font-weight: bold;
	color: 					#333 /*{d-bdown-color}*/;
	text-shadow: 0 /*{d-bdown-shadow-x}*/ 1px /*{d-bdown-shadow-y}*/ 0 /*{d-bdown-shadow-radius}*/ 	#ffffff /*{d-bdown-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #e5e5e5 /*{d-bdown-background-start}*/), to( #f2f2f2 /*{d-bdown-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #e5e5e5 /*{d-bdown-background-start}*/, #f2f2f2 /*{d-bdown-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #e5e5e5 /*{d-bdown-background-start}*/, #f2f2f2 /*{d-bdown-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #e5e5e5 /*{d-bdown-background-start}*/, #f2f2f2 /*{d-bdown-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #e5e5e5 /*{d-bdown-background-start}*/, #f2f2f2 /*{d-bdown-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #e5e5e5 /*{d-bdown-background-start}*/, #f2f2f2 /*{d-bdown-background-end}*/);
}
.ui-btn-down-d a.ui-link-inherit {
	color: 					#333 /*{d-bdown-color}*/;
}
.ui-btn-up-d,
.ui-btn-hover-d,
.ui-btn-down-d {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
	text-decoration: none;
}


/* E
-----------------------------------------------------------------------------------------------------------*/

.ui-bar-e {
	border: 1px solid 		#F7C942 /*{e-bar-border}*/;
	background: 			#fadb4e /*{e-bar-background-color}*/;
	color: 					#333 /*{e-bar-color}*/;
	text-shadow: 0 /*{e-bar-shadow-x}*/ 1px /*{e-bar-shadow-y}*/ 0 /*{e-bar-shadow-radius}*/ 	#fff /*{e-bar-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #fceda7 /*{e-bar-background-start}*/), to( #fbef7e /*{e-bar-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #fceda7 /*{e-bar-background-start}*/, #fbef7e /*{e-bar-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #fceda7 /*{e-bar-background-start}*/, #fbef7e /*{e-bar-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #fceda7 /*{e-bar-background-start}*/, #fbef7e /*{e-bar-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #fceda7 /*{e-bar-background-start}*/, #fbef7e /*{e-bar-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #fceda7 /*{e-bar-background-start}*/, #fbef7e /*{e-bar-background-end}*/);
}
.ui-bar-e,
.ui-bar-e input,
.ui-bar-e select,
.ui-bar-e textarea,
.ui-bar-e button {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
}
.ui-bar-e .ui-link-inherit {
	color: 	#333333 /*{e-bar-color}*/;
}
.ui-bar-e .ui-link {
	color: #2489CE /*{e-bar-link-color}*/;
	font-weight: bold;
}

.ui-bar-e .ui-link:hover {
	color: #2489CE /*{e-bar-link-hover}*/;
}

.ui-bar-e .ui-link:active {
	color: #2489CE /*{e-bar-link-active}*/;
}

.ui-bar-e .ui-link:visited {
    color: #2489CE /*{e-bar-link-visited}*/;
}

.ui-body-e,
.ui-overlay-e {
	border: 1px solid 		#F7C942 /*{e-body-border}*/;
	color: 					#222222 /*{e-body-color}*/;
	text-shadow: 0 /*{e-body-shadow-x}*/ 1px /*{e-body-shadow-y}*/ 0 /*{e-body-shadow-radius}*/ 	#fff /*{e-body-shadow-color}*/;
	background: 			#fff9df /*{e-body-background-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #fffadf /*{e-body-background-start}*/), to( #fff3a5 /*{e-body-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #fffadf /*{e-body-background-start}*/, #fff3a5 /*{e-body-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #fffadf /*{e-body-background-start}*/, #fff3a5 /*{e-body-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #fffadf /*{e-body-background-start}*/, #fff3a5 /*{e-body-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #fffadf /*{e-body-background-start}*/, #fff3a5 /*{e-body-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #fffadf /*{e-body-background-start}*/, #fff3a5 /*{e-body-background-end}*/);
}
.ui-overlay-e {
	background-image: none;
	border-width: 0;
}
.ui-body-e,
.ui-body-e input,
.ui-body-e select,
.ui-body-e textarea,
.ui-body-e button {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
}
.ui-body-e .ui-link-inherit {
	color: 	#333333 /*{e-body-color}*/;
}

.ui-body-e .ui-link {
	color: #2489CE /*{e-body-link-color}*/;
	font-weight: bold;
}

.ui-body-e .ui-link:hover {
	color: #2489CE /*{e-body-link-hover}*/;
}

.ui-body-e .ui-link:active {
	color: #2489CE /*{e-body-link-active}*/;
}

.ui-body-e .ui-link:visited {
    color: #2489CE /*{e-body-link-visited}*/;
}

.ui-btn-up-e {
	border: 1px solid 		#F4C63f /*{e-bup-border}*/;
	background: 			#fadb4e /*{e-bup-background-color}*/;
	font-weight: bold;
	color: 					#222 /*{e-bup-color}*/;
	text-shadow: 0 /*{e-bup-shadow-x}*/ 1px /*{e-bup-shadow-y}*/ 0 /*{e-bup-shadow-radius}*/ 	#fff /*{e-bup-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #ffefaa /*{e-bup-background-start}*/), to( #ffe155 /*{e-bup-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #ffefaa /*{e-bup-background-start}*/, #ffe155 /*{e-bup-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #ffefaa /*{e-bup-background-start}*/, #ffe155 /*{e-bup-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #ffefaa /*{e-bup-background-start}*/, #ffe155 /*{e-bup-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #ffefaa /*{e-bup-background-start}*/, #ffe155 /*{e-bup-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #ffefaa /*{e-bup-background-start}*/, #ffe155 /*{e-bup-background-end}*/);
}
.ui-btn-up-e a.ui-link-inherit {
	color: 					#222 /*{e-bup-color}*/;
}
.ui-btn-hover-e {
	border: 1px solid 		#F2C43d /*{e-bhover-border}*/;
	background: 			#fbe26f /*{e-bhover-background-color}*/;
	font-weight: bold;
	color: 					#111 /*{e-bhover-color}*/;
	text-shadow: 0 /*{e-bhover-shadow-x}*/ 1px /*{e-bhover-shadow-y}*/ 0 /*{e-bhover-shadow-radius}*/ 	#fff /*{e-bhover-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #fff5ba /*{e-bhover-background-start}*/), to( #fbdd52 /*{e-bhover-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #fff5ba /*{e-bhover-background-start}*/, #fbdd52 /*{e-bhover-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #fff5ba /*{e-bhover-background-start}*/, #fbdd52 /*{e-bhover-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #fff5ba /*{e-bhover-background-start}*/, #fbdd52 /*{e-bhover-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #fff5ba /*{e-bhover-background-start}*/, #fbdd52 /*{e-bhover-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #fff5ba /*{e-bhover-background-start}*/, #fbdd52 /*{e-bhover-background-end}*/);
}

.ui-btn-hover-e a.ui-link-inherit {
	color: 					#333 /*{e-bhover-color}*/;
}
.ui-btn-down-e {
	border: 1px solid 		#F2C43d /*{e-bdown-border}*/;
	background: 			#fceda7 /*{e-bdown-background-color}*/;
	font-weight: bold;
	color: 					#111 /*{e-bdown-color}*/;
	text-shadow: 0 /*{e-bdown-shadow-x}*/ 1px /*{e-bdown-shadow-y}*/ 0 /*{e-bdown-shadow-radius}*/ 	#ffffff /*{e-bdown-shadow-color}*/;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #f8d94c /*{e-bdown-background-start}*/), to( #fadb4e /*{e-bdown-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #f8d94c /*{e-bdown-background-start}*/, #fadb4e /*{e-bdown-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #f8d94c /*{e-bdown-background-start}*/, #fadb4e /*{e-bdown-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #f8d94c /*{e-bdown-background-start}*/, #fadb4e /*{e-bdown-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #f8d94c /*{e-bdown-background-start}*/, #fadb4e /*{e-bdown-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #f8d94c /*{e-bdown-background-start}*/, #fadb4e /*{e-bdown-background-end}*/);
}
.ui-btn-down-e a.ui-link-inherit {
	color: 					#333 /*{e-bdown-color}*/;
}
.ui-btn-up-e,
.ui-btn-hover-e,
.ui-btn-down-e {
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
	text-decoration: none;
}

/* Structure */

/* links within "buttons" 
-----------------------------------------------------------------------------------------------------------*/

a.ui-link-inherit {
	text-decoration: none !important;
}


/* Active class used as the "on" state across all themes
-----------------------------------------------------------------------------------------------------------*/
.ui-btn-active {
	border: 1px solid 		#2373a5 /*{global-active-border}*/;
	background: 			#5393c5 /*{global-active-background-color}*/;
	font-weight: bold;
	color: 					#fff /*{global-active-color}*/;
	cursor: pointer;
	text-shadow: 0 /*{global-active-shadow-x}*/ 1px /*{global-active-shadow-y}*/ 1px /*{global-active-shadow-radius}*/ #3373a5 /*{global-active-shadow-color}*/;
	text-decoration: none;
	background-image: -webkit-gradient(linear, left top, left bottom, from( #5393c5 /*{global-active-background-start}*/), to( #6facd5 /*{global-active-background-end}*/)); /* Saf4+, Chrome */
	background-image: -webkit-linear-gradient( #5393c5 /*{global-active-background-start}*/, #6facd5 /*{global-active-background-end}*/); /* Chrome 10+, Saf5.1+ */
	background-image:    -moz-linear-gradient( #5393c5 /*{global-active-background-start}*/, #6facd5 /*{global-active-background-end}*/); /* FF3.6 */
	background-image:     -ms-linear-gradient( #5393c5 /*{global-active-background-start}*/, #6facd5 /*{global-active-background-end}*/); /* IE10 */
	background-image:      -o-linear-gradient( #5393c5 /*{global-active-background-start}*/, #6facd5 /*{global-active-background-end}*/); /* Opera 11.10+ */
	background-image:         linear-gradient( #5393c5 /*{global-active-background-start}*/, #6facd5 /*{global-active-background-end}*/);
	font-family: Helvetica, Arial, sans-serif /*{global-font-family}*/;
}
.ui-btn-active a.ui-link-inherit {
	color: 					#fff /*{global-active-color}*/;
}


/* button inner top highlight
-----------------------------------------------------------------------------------------------------------*/

.ui-btn-inner {
	border-top: 1px solid 	#fff;
	border-color: 			rgba(255,255,255,.3);
}


/* corner rounding classes
-----------------------------------------------------------------------------------------------------------*/

.ui-corner-tl {
	-moz-border-radius-topleft: 		.6em /*{global-radii-blocks}*/;
	-webkit-border-top-left-radius: 	.6em /*{global-radii-blocks}*/;
	border-top-left-radius: 			.6em /*{global-radii-blocks}*/;
}
.ui-corner-tr {
	-moz-border-radius-topright: 		.6em /*{global-radii-blocks}*/;
	-webkit-border-top-right-radius: 	.6em /*{global-radii-blocks}*/;
	border-top-right-radius: 			.6em /*{global-radii-blocks}*/;
}
.ui-corner-bl {
	-moz-border-radius-bottomleft: 		.6em /*{global-radii-blocks}*/;
	-webkit-border-bottom-left-radius: 	.6em /*{global-radii-blocks}*/;
	border-bottom-left-radius: 			.6em /*{global-radii-blocks}*/;
}
.ui-corner-br {
	-moz-border-radius-bottomright: 	.6em /*{global-radii-blocks}*/;
	-webkit-border-bottom-right-radius: .6em /*{global-radii-blocks}*/;
	border-bottom-right-radius: 		.6em /*{global-radii-blocks}*/;
}
.ui-corner-top {
	-moz-border-radius-topleft: 		.6em /*{global-radii-blocks}*/;
	-webkit-border-top-left-radius: 	.6em /*{global-radii-blocks}*/;
	border-top-left-radius: 			.6em /*{global-radii-blocks}*/;
	-moz-border-radius-topright: 		.6em /*{global-radii-blocks}*/;
	-webkit-border-top-right-radius: 	.6em /*{global-radii-blocks}*/;
	border-top-right-radius: 			.6em /*{global-radii-blocks}*/;
}
.ui-corner-bottom {
	-moz-border-radius-bottomleft: 		.6em /*{global-radii-blocks}*/;
	-webkit-border-bottom-left-radius: 	.6em /*{global-radii-blocks}*/;
	border-bottom-left-radius: 			.6em /*{global-radii-blocks}*/;
	-moz-border-radius-bottomright: 	.6em /*{global-radii-blocks}*/;
	-webkit-border-bottom-right-radius: .6em /*{global-radii-blocks}*/;
	border-bottom-right-radius: 		.6em /*{global-radii-blocks}*/;
	}
.ui-corner-right {
	-moz-border-radius-topright: 		.6em /*{global-radii-blocks}*/;
	-webkit-border-top-right-radius: 	.6em /*{global-radii-blocks}*/;
	border-top-right-radius: 			.6em /*{global-radii-blocks}*/;
	-moz-border-radius-bottomright: 	.6em /*{global-radii-blocks}*/;
	-webkit-border-bottom-right-radius: .6em /*{global-radii-blocks}*/;
	border-bottom-right-radius: 		.6em /*{global-radii-blocks}*/;
}
.ui-corner-left {
	-moz-border-radius-topleft: 		.6em /*{global-radii-blocks}*/;
	-webkit-border-top-left-radius: 	.6em /*{global-radii-blocks}*/;
	border-top-left-radius: 			.6em /*{global-radii-blocks}*/;
	-moz-border-radius-bottomleft: 		.6em /*{global-radii-blocks}*/;
	-webkit-border-bottom-left-radius: 	.6em /*{global-radii-blocks}*/;
	border-bottom-left-radius: 			.6em /*{global-radii-blocks}*/;
}
.ui-corner-all {
	-moz-border-radius: 				.6em /*{global-radii-blocks}*/;
	-webkit-border-radius: 				.6em /*{global-radii-blocks}*/;
	border-radius: 						.6em /*{global-radii-blocks}*/;
}
.ui-corner-none {
	-moz-border-radius: 				   0;
	-webkit-border-radius: 				   0;
	border-radius: 						   0;
}

/* Form field separator
-----------------------------------------------------------------------------------------------------------*/
.ui-br {
	border-bottom: rgb(130,130,130);
	border-bottom: rgba(130,130,130,.3);
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

/* Interaction cues
-----------------------------------------------------------------------------------------------------------*/
.ui-disabled {
	opacity: 							.3;
}
.ui-disabled,
.ui-disabled a {
	cursor: default !important;
	pointer-events: none;
}
.ui-disabled .ui-btn-text {
	-ms-filter:"progid:DXImageTransform.Microsoft.Alpha(opacity=30)";
	filter: alpha(opacity=30);
	zoom: 1;
}

/* Icons
-----------------------------------------------------------------------------------------------------------*/

.ui-icon,
.ui-icon-searchfield:after {
	background: 						#666 /*{global-icon-color}*/;
	background: 						rgba(0,0,0,.4) /*{global-icon-disc}*/;
	background-image: url(images/icons-18-white.png) /*{global-icon-set}*/;
	background-repeat: no-repeat;
	-moz-border-radius: 				9px;
	-webkit-border-radius: 				9px;
	border-radius: 						9px;
}


/* Alt icon color
-----------------------------------------------------------------------------------------------------------*/

.ui-icon-alt {
	background: 						#fff;
	background: 						rgba(255,255,255,.3);
	background-image: url(images/icons-18-black.png);
	background-repeat: no-repeat;
}

/* HD/"retina" sprite
-----------------------------------------------------------------------------------------------------------*/

@media only screen and (-webkit-min-device-pixel-ratio: 1.5),
       only screen and (min--moz-device-pixel-ratio: 1.5),
       only screen and (min-resolution: 240dpi) {
	
	.ui-icon-plus, .ui-icon-minus, .ui-icon-delete, .ui-icon-arrow-r,
	.ui-icon-arrow-l, .ui-icon-arrow-u, .ui-icon-arrow-d, .ui-icon-check,
	.ui-icon-gear, .ui-icon-refresh, .ui-icon-forward, .ui-icon-back,
	.ui-icon-grid, .ui-icon-star, .ui-icon-alert, .ui-icon-info, .ui-icon-home, .ui-icon-search, .ui-icon-searchfield:after, 
	.ui-icon-checkbox-off, .ui-icon-checkbox-on, .ui-icon-radio-off, .ui-icon-radio-on {
		background-image: url(images/icons-36-white.png);
		-moz-background-size: 776px 18px;
		-o-background-size: 776px 18px;
		-webkit-background-size: 776px 18px;
		background-size: 776px 18px;
	}
	.ui-icon-alt {
		background-image: url(images/icons-36-black.png);
	}
}

/* plus minus */
.ui-icon-plus {
	background-position: 	-0 50%;
}
.ui-icon-minus {
	background-position: 	-36px 50%;
}

/* delete/close */
.ui-icon-delete {
	background-position: 	-72px 50%;
}

/* arrows */
.ui-icon-arrow-r {
	background-position: 	-108px 50%;
}
.ui-icon-arrow-l {
	background-position: 	-144px 50%;
}
.ui-icon-arrow-u {
	background-position: 	-180px 50%;
}
.ui-icon-arrow-d {
	background-position: 	-216px 50%;
}

/* misc */
.ui-icon-check {
	background-position: 	-252px 50%;
}
.ui-icon-gear {
	background-position: 	-288px 50%;
}
.ui-icon-refresh {
	background-position: 	-324px 50%;
}
.ui-icon-forward {
	background-position: 	-360px 50%;
}
.ui-icon-back {
	background-position: 	-396px 50%;
}
.ui-icon-grid {
	background-position: 	-432px 50%;
}
.ui-icon-star {
	background-position: 	-468px 50%;
}
.ui-icon-alert {
	background-position: 	-504px 50%;
}
.ui-icon-info {
	background-position: 	-540px 50%;
}
.ui-icon-home {
	background-position: 	-576px 50%;
}
.ui-icon-search,
.ui-icon-searchfield:after {
	background-position: 	-612px 50%;
}
.ui-icon-checkbox-off {
	background-position: 	-684px 50%;
}
.ui-icon-checkbox-on {
	background-position: 	-648px 50%;
}
.ui-icon-radio-off {
	background-position: 	-756px 50%;
}
.ui-icon-radio-on {
	background-position: 	-720px 50%;
}


/* checks,radios */
.ui-checkbox .ui-icon {
	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
	border-radius: 3px;
}
.ui-icon-checkbox-off,
.ui-icon-radio-off {
	background-color: transparent;	
}
.ui-checkbox-on .ui-icon,
.ui-radio-on .ui-icon {
	background-color: #4596ce /*{global-active-background-color}*/; /* NOTE: this hex should match the active state color. It's repeated here for cascade */
}

/* loading icon */
.ui-icon-loading {
	background: url(images/ajax-loader.gif);
	background-size: 46px 46px;
}


/* Button corner classes
-----------------------------------------------------------------------------------------------------------*/

.ui-btn-corner-tl {
	-moz-border-radius-topleft: 		1em /*{global-radii-buttons}*/;
	-webkit-border-top-left-radius: 	1em /*{global-radii-buttons}*/;
	border-top-left-radius: 			1em /*{global-radii-buttons}*/;
}
.ui-btn-corner-tr {
	-moz-border-radius-topright: 		1em /*{global-radii-buttons}*/;
	-webkit-border-top-right-radius: 	1em /*{global-radii-buttons}*/;
	border-top-right-radius: 			1em /*{global-radii-buttons}*/;
}
.ui-btn-corner-bl {
	-moz-border-radius-bottomleft: 		1em /*{global-radii-buttons}*/;
	-webkit-border-bottom-left-radius: 	1em /*{global-radii-buttons}*/;
	border-bottom-left-radius: 			1em /*{global-radii-buttons}*/;
}
.ui-btn-corner-br {
	-moz-border-radius-bottomright: 	1em /*{global-radii-buttons}*/;
	-webkit-border-bottom-right-radius: 1em /*{global-radii-buttons}*/;
	border-bottom-right-radius: 		1em /*{global-radii-buttons}*/;
}
.ui-btn-corner-top {
	-moz-border-radius-topleft: 		1em /*{global-radii-buttons}*/;
	-webkit-border-top-left-radius: 	1em /*{global-radii-buttons}*/;
	border-top-left-radius: 			1em /*{global-radii-buttons}*/;
	-moz-border-radius-topright: 		1em /*{global-radii-buttons}*/;
	-webkit-border-top-right-radius: 	1em /*{global-radii-buttons}*/;
	border-top-right-radius: 			1em /*{global-radii-buttons}*/;
}
.ui-btn-corner-bottom {
	-moz-border-radius-bottomleft: 		1em /*{global-radii-buttons}*/;
	-webkit-border-bottom-left-radius: 	1em /*{global-radii-buttons}*/;
	border-bottom-left-radius: 			1em /*{global-radii-buttons}*/;
	-moz-border-radius-bottomright: 	1em /*{global-radii-buttons}*/;
	-webkit-border-bottom-right-radius: 1em /*{global-radii-buttons}*/;
	border-bottom-right-radius: 		1em /*{global-radii-buttons}*/;
}
.ui-btn-corner-right {
	 -moz-border-radius-topright: 		1em /*{global-radii-buttons}*/;
	-webkit-border-top-right-radius: 	1em /*{global-radii-buttons}*/;
	border-top-right-radius: 			1em /*{global-radii-buttons}*/;
	-moz-border-radius-bottomright: 	1em /*{global-radii-buttons}*/;
	-webkit-border-bottom-right-radius: 1em /*{global-radii-buttons}*/;
	border-bottom-right-radius: 		1em /*{global-radii-buttons}*/;
}
.ui-btn-corner-left {
	-moz-border-radius-topleft: 		1em /*{global-radii-buttons}*/;
	-webkit-border-top-left-radius: 	1em /*{global-radii-buttons}*/;
	border-top-left-radius: 			1em /*{global-radii-buttons}*/;
	-moz-border-radius-bottomleft: 		1em /*{global-radii-buttons}*/;
	-webkit-border-bottom-left-radius: 	1em /*{global-radii-buttons}*/;
	border-bottom-left-radius: 			1em /*{global-radii-buttons}*/;
}
.ui-btn-corner-all {
	-moz-border-radius: 				1em /*{global-radii-buttons}*/;
	-webkit-border-radius: 				1em /*{global-radii-buttons}*/;
	border-radius: 						1em /*{global-radii-buttons}*/;
}

/* radius clip workaround for cleaning up corner trapping */
.ui-corner-tl,
.ui-corner-tr,
.ui-corner-bl, 
.ui-corner-br,
.ui-corner-top,
.ui-corner-bottom, 
.ui-corner-right,
.ui-corner-left,
.ui-corner-all,
.ui-btn-corner-tl,
.ui-btn-corner-tr,
.ui-btn-corner-bl, 
.ui-btn-corner-br,
.ui-btn-corner-top,
.ui-btn-corner-bottom, 
.ui-btn-corner-right,
.ui-btn-corner-left,
.ui-btn-corner-all {
  -webkit-background-clip: padding-box;
     -moz-background-clip: padding;
          background-clip: padding-box;
}

/* Overlay / modal
-----------------------------------------------------------------------------------------------------------*/

.ui-overlay {
	background: #666;
	opacity: .5;
	filter: Alpha(Opacity=50);
	position: absolute;
	width: 100%;
	height: 100%;
}
.ui-overlay-shadow {
	-moz-box-shadow: 0px 0px 12px 			rgba(0,0,0,.6);
	-webkit-box-shadow: 0px 0px 12px 		rgba(0,0,0,.6);
	box-shadow: 0px 0px 12px 				rgba(0,0,0,.6);
}
.ui-shadow {
	-moz-box-shadow: 0px 1px 4px /*{global-box-shadow-size}*/ 			rgba(0,0,0,.3) /*{global-box-shadow-color}*/;
	-webkit-box-shadow: 0px 1px 4px /*{global-box-shadow-size}*/ 		rgba(0,0,0,.3) /*{global-box-shadow-color}*/;
	box-shadow: 0px 1px 4px /*{global-box-shadow-size}*/ 				rgba(0,0,0,.3) /*{global-box-shadow-color}*/;
}
.ui-bar-a .ui-shadow,
.ui-bar-b .ui-shadow ,
.ui-bar-c .ui-shadow  {
	-moz-box-shadow: 0px 1px 0 				rgba(255,255,255,.3);
	-webkit-box-shadow: 0px 1px 0 			rgba(255,255,255,.3);
	box-shadow: 0px 1px 0 					rgba(255,255,255,.3);
}
.ui-shadow-inset {
	-moz-box-shadow: inset 0px 1px 4px 		rgba(0,0,0,.2);
	-webkit-box-shadow: inset 0px 1px 4px 	rgba(0,0,0,.2);
	box-shadow: inset 0px 1px 4px 			rgba(0,0,0,.2);
}
.ui-icon-shadow {
	-moz-box-shadow: 0px 1px 0 				rgba(255,255,255,.4) /*{global-icon-shadow}*/;
	-webkit-box-shadow: 0px 1px 0 			rgba(255,255,255,.4) /*{global-icon-shadow}*/;
	box-shadow: 0px 1px 0 					rgba(255,255,255,.4) /*{global-icon-shadow}*/;
}

/* Focus state - set here for specificity (note: these classes are added by JavaScript)
-----------------------------------------------------------------------------------------------------------*/

.ui-btn:focus {
	outline: 0;
}

.ui-focus,
.ui-btn:focus {
	-moz-box-shadow: 0px 0px 12px 		#387bbe /*{global-active-background-color}*/;
	-webkit-box-shadow: 0px 0px 12px 	#387bbe /*{global-active-background-color}*/;
	box-shadow: 0px 0px 12px 			#387bbe /*{global-active-background-color}*/;
}

/* unset box shadow in browsers that don't do it right
-----------------------------------------------------------------------------------------------------------*/

.ui-mobile-nosupport-boxshadow * {
	-moz-box-shadow: none !important;
	-webkit-box-shadow: none !important;
	box-shadow: none !important;
}

/* ...and bring back focus */
.ui-mobile-nosupport-boxshadow .ui-focus,
.ui-mobile-nosupport-boxshadow .ui-btn:focus {
	outline-width: 1px;
	outline-style: dotted;
}
