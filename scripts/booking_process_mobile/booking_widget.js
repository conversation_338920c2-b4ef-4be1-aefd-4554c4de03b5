var previous_calendar_info = "";
show_current_at_pos = "";


$(window).on("load", function () {

    datePickerWidgetBooking.init();

    $(".row.ocupation .input_wrapper").click(function () {
        $(".occupancy_popup").slideDown("slow");
    });

    $(".occupancy_popup .close").click(function () {
        $(".occupancy_popup").slideUp("slow");
    });

    $(".double_button_wrapper .modify_search").click(function () {
        if ($(this).hasClass("bottom_modify_search")) {
            $("html, body").animate({
                scrollTop: 0
            }, 1000, open_widget);
        } else {
            open_widget();
        }

        if ($(".modify_search.flex_calendar").length) {
            open_flexible_calendar();
        }
    });

    $(".booking_widget_wrapper .close_button").click(function (e) {
        e.preventDefault();
        close_widget();
    });

    $(".booking_widget_wrapper .to_booking0").click(function(){
        const form_widget = $(".booking_widget_wrapper .paraty-booking-form");
        const hotels = $(this).data("hotels");
        const input_hotels = $("<input name='applicationIds' type='hidden'>");
        const input_order = $("<input name='keep_order' type='hidden' value='true'>");
        const input_redirection = $("<input name='redirection_from_no_availability_pop_up' type='hidden' value='true'>")

        input_hotels.val(hotels);
        form_widget.attr("action", form_widget.attr("action").replace("booking1", "booking0"));
        form_widget.append(input_hotels, input_order, input_redirection);
        form_widget.find("#namespace").remove();
        form_widget.find(".submit_button").click();
      });

    if ($('.booking_widget_wrapper .hotel_select').length) {
        $('.booking_widget_wrapper .hotel_select .hotel_selector_option').on('click', function () {
            select_hotel($(this));
        });

        // Preselect current hotel
         let hotel_namespace = $('.booking_widget_wrapper input[name="namespace"]').val() || '',
             target_hotel_element = $(`.booking_widget_wrapper .hotel_select .hotel_selector_option[data-namespace="${hotel_namespace}"]`);
         if (target_hotel_element.length) {
             select_hotel(target_hotel_element);
         }
    }

    $(".paraty-booking-form, .room_booking_form, .additional_service_form, #supplements_form_post, .packages_form").on("submit", function (e) {
        const data = $(this).serializeArray();

        if($('#booking2_upselling').length && !data.some((elem)=>elem.value)){
            const message = $('#booking2_upselling').val() || $.i18n._('T_no_servicio');
            alert(message);
            e.preventDefault();
            return;
        }

        showLoading();
    });

    $(".double_button_wrapper.parent .show_calendar, .dates_flexible, .open_availability_calendar").click(function () {
        open_flexible_calendar($(this));

        typeof gtag !== 'undefined' && gtag("event", "select_content", {
            content_type: "price_calendar",
            item_id: "btn_show_price_calendar"
        });
    });

    $(".booking_widget_wrapper .counter_box .control").click(function () {
        var control_box = $(this).parent(),
            room_content = $(this).closest(".room_resume_item"),
            target_input = control_box.find(".input_occupancy"),
            target_html = control_box.find(".quantity"),
            min_value = control_box.data("min"),
            max_value = control_box.data("max"),
            current_value = control_box.attr("data-current");

        if ($(this).hasClass("add")) {
            current_value = parseInt(current_value) + 1;

            if (current_value > max_value) {
                current_value = max_value;
            }
        } else if ($(this).hasClass("subtract")) {
            current_value = parseInt(current_value) - 1;

            if (current_value < min_value) {
                current_value = min_value;
            }
        } else {
            return false;
        }

        if (control_box.hasClass("room_box")) {
            showing_rooms(current_value);
        }

        control_box.attr("data-current", current_value);
        target_input.val(current_value);
        target_html.text(current_value);

        if (control_box.hasClass("kid_box") || control_box.hasClass("baby_box")) {
            check_kids_ages(control_box, false);
        }

        setOccupancyString();
        check_if_limit(control_box);
    });

    $(".age_options_wrapper .age_option").click(function () {
        var room_wrapper = $(this).closest(".room_resume_item"),
            is_baby = !!$(this).closest(".babies_age_select").length,
            age_selects = (is_baby)?room_wrapper.find(".babies_age_select"):room_wrapper.find(".kids_age_select"),
            room = $(this).data("room"),
            age_input = (is_baby)?$("#agesBaby" + room):$("#agesKid" + room),
            age_list = [];

        $(this).addClass("active").siblings(".age_option").removeClass("active");

        age_selects.each(function () {
            if ($(this).is(':visible') && $(this).find(".age_option.active").length) {
                age_list.push($(this).find(".age_option.active").data("age"));
            } else {
                let min_kids_age = room_wrapper.find(".kids_age_select")?.find(".age_option:first")?.data("age") ?? '2';
                age_list.push((is_baby) ? "0" : min_kids_age);
            }
        });

        age_input.val(age_list.join(";"));
    });
    set_search_params();
    var input_hidden_name = $("input[name=hidden_name]"),
        all_packages = $(".packages_content .package_element");
    if (input_hidden_name.length) {
        var hidden_name_class = input_hidden_name.val();
        if (all_packages.length > 1) {
            $(`.${hidden_name_class}`).siblings().remove();
            $(".packages_content").trigger('refresh.owl.carousel');
        }
    }

    $("body").on("click", ".previous_month_selector, .next_month_selector", function () {
        previous_calendar_info = availability_data;
        var button = $(".show_calendar.btn.btn_icon");

        if($(this).closest(".flexible_dates_wrapper")) {
            button = $(this).closest(".dates_popup").find(".dates_flexible");
        }

        var previous_text = button.html();
        button.html('<i class="far fa-circle-notch fa-spin fa-1x fa-fw"></i>');
        wait_loading_calendar(button, previous_text);
    });
});

$(window).on('pageshow', function (e) {
    if ('persisted' in e.originalEvent) {
        if (e.originalEvent.persisted) {
            $(".loading_animation_popup").hide(); // Close loading popup when page is loaded from cache
        }
    } else {
        setTimeout(function () {
            $(".loading_animation_popup").fadeOut();
        }, 7500);
    }
});

$(function () {
    $(document).on('click', '.button-promotion', function (e) {
       e.preventDefault();
       let booking_widget = $('.paraty-booking-form'),
           direct_search_url = check_smart_datas(booking_widget, $(this));

       if (direct_search_url) {
           window.location.href = direct_search_url;
       } else {
           booking_widget.find('.submit_button').trigger('click');
       }

    });

    $(document).on("open_chat_conversation", function(e){
        $("#qt-button-frame").contents().find("#qt-button-root button").trigger("click");
    });
});

function showLoading() {
    const loadingPopup = $(".loading_animation_popup");

    if (!loadingPopup.length) return;

    replaceDateVariables(loadingPopup);

    const specialLoadingTextElem = $('input[name="special_loading_text"]');
    if(specialLoadingTextElem.length && specialLoadingTextElem.val()){
        loadingPopup.find('.loading_text').html(specialLoadingTextElem.val());
    }

    if (iOS()) {
        loadingPopup.show();
    } else {
        loadingPopup.fadeIn();
    }
}

function select_hotel(hotel_element) {
    let hotel_name = hotel_element.find('.hotel_name').text().trim(),
        hotel_url = hotel_element.attr('data-url-booking'),
        hotel_namespace = hotel_element.attr('data-namespace'),
        form_wrapper = hotel_element.closest(".paraty-booking-form"),
        hotel_title = form_wrapper.find(".hotel_select .input_title"),
        only_adults = !!hotel_element.attr('data-only-adults');

    if (only_adults) {
        form_wrapper.find('.occupancy_popup .room_resume_item').addClass('adults_only')
            .find('input[name^="childrenRoom"], input[name^="babiesRoom"]').attr('disabled', 'disabled');
    } else {
        form_wrapper.find('.occupancy_popup .room_resume_item').removeClass('adults_only')
            .find('input[name^="childrenRoom"], input[name^="babiesRoom"]').removeAttr('disabled');
    }

    if (hotel_namespace && hotel_url) {
        form_wrapper.attr("action", hotel_url);
        form_wrapper.find("#namespace").val(hotel_namespace);
        hotel_title.text(hotel_name);
        hotel_element.addClass('selected');
        hotel_element.siblings().removeClass('selected');
        update_calendar_v2_namespace(hotel_namespace);
    }

    setOccupancyString();
}

function check_smart_datas(booking_widget, button) {
    let smart_promocode = button.attr("data-smartpromocode") || button.attr("data-promocode"),
        smart_hidden_promocode = button.attr("data-smarthiddenpromocode"),
        smart_start_date = button.attr("data-smartdateini"),
        smart_end_date = button.attr("data-smartdatefin"),
        direct_search_path = window.direct_search_data?.path,
        direct_search_params = window.direct_search_data?.params;

    if (smart_promocode) {
        booking_widget.find('input[name=promocode]').val(smart_promocode);
        if (direct_search_params) {
            direct_search_params.promocode = smart_promocode;
        }
    }
    if (smart_hidden_promocode) {
        booking_widget.append(`<input type="hidden" name="hidden_default_promocode" value=${smart_hidden_promocode}>`);
        if (direct_search_params) {
            direct_search_params.hidden_default_promocode = smart_hidden_promocode;
        }
    }

    if (smart_start_date) {
        booking_widget.find('input[name=startDate]').val(smart_start_date);
        if (direct_search_params) {
            direct_search_params.startDate = smart_start_date;
        }

        if (!smart_end_date) {
            let parsed_start_date = $.datepicker.parseDate("dd/mm/yy", smart_start_date);
            parsed_start_date.setDate(parsed_start_date.getDate() + 1);
            let end_date = $.datepicker.formatDate("dd/mm/yy", parsed_start_date);
            booking_widget.find('input[name=endDate]').val(end_date);
            if (direct_search_params) {
                direct_search_params.endDate = end_date;
            }
        }
    }

    if (smart_end_date) {
        booking_widget.find('input[name=endDate]').val(smart_end_date);
        if (direct_search_params) {
            direct_search_params.endDate = smart_end_date;
        }
    }

    if (direct_search_path && direct_search_params) {
        let target_url = direct_search_path;

        if (target_url.includes('?')) {
            target_url += '&';
        } else {
            target_url += '?';
        }

        target_url += new URLSearchParams(direct_search_params).toString();
        return target_url;
    }
}

function check_kids_ages(wrapper, onStart) {
    var actual_value = wrapper.find("input.input_occupancy").val();
    var is_baby = wrapper.hasClass('baby_box');
    var room_wrapper = wrapper.closest('.room_resume_item');
    var actual_room_index = room_wrapper.attr('room_index');
    var age_selects = room_wrapper.find('.kids_age_select');
    var ages_label = room_wrapper.find('.kids_ages_label');

    if (is_baby) {
        age_selects = room_wrapper.find('.babies_age_select');
        ages_label = room_wrapper.find('.babies_ages_label');
    }

    if (parseInt(actual_value) > 0) {
        ages_label.show();
    } else {
        ages_label.hide();
    }

    age_selects.each(function (index) {
        if (index < parseInt(actual_value)) {
            $(this).show();
            if (!$(this).find(".age_option").hasClass("active") && !onStart) {
                $(this).find(".age_option").first().trigger("click");
            }
        } else {
            $(this).hide();
        }
    });

    fill_age(wrapper, actual_room_index);
}

function fill_age(wrapper, room_index) {
    var ages_values;
    var is_baby = wrapper.hasClass('baby_box');
    if (is_baby) {
        ages_values = $("#agesBaby" + room_index).val();
    } else {
        ages_values = $("#agesKid" + room_index).val();
    }

    if (!ages_values){
        return;
    }

    var splitted_ages_values = ages_values.split(";");

    var iteration_count = 1;
    splitted_ages_values.forEach(function(n) {
        if (n) {
            if (is_baby) {
                $(".age_option[data-age=" + n + "][data-baby=" + iteration_count + "][data-room=" + room_index + "]").addClass('active');
            } else {
                $(".age_option[data-age=" + n + "][data-kid=" + iteration_count + "][data-room=" + room_index + "]").addClass('active');
            }
            iteration_count++;
        }
    });
}

function wait_loading_calendar(button, previous_text) {
    setTimeout(function() {
        if(previous_calendar_info === availability_data) {
            wait_loading_calendar(button, previous_text);
        } else {
            console.log("Calendar loaded");
            button.html(previous_text);
        }
    }, 500);
}

var datePickerWidgetBooking = function () {
    return {
        config: {
            custom_week_day_start: '1',
            period_closed: [],
            show_selected_nights: false,
            show_confirm_dates_button: false,
        },

        init: function () {
            if ($("#show_selected_nights").val()) {
                datePickerWidgetBooking.config.show_selected_nights = true;
                const nightsSelected = $('input#internalNumNights').val();
                datePickerWidgetBooking.set_nights_selected(nightsSelected);
            }
            if ($("#show_confirm_dates_button").val()) datePickerWidgetBooking.config.show_confirm_dates_button = true;

            if ($(".start_datepicker").length) {
                datePickerWidgetBooking.prepare_datepickers();
            }
        },

        format_date: function (date_text, target_element) {
            var _monthNames = $.datepicker._defaults.monthNamesShort;

            if (typeof(date_text) != 'string') {
                var target_day = date_text.getDate(),
                    target_month = date_text.getMonth(),
                    target_year = date_text.getFullYear();
            } else {
                var splitted_dates = date_text.split("/"),
                    target_day = splitted_dates[0],
                    target_month = parseInt(splitted_dates[1] - 1),
                    target_year = splitted_dates[2];
            }

            var formatted_date = [target_day, _monthNames[target_month], target_year].join(" ");
            target_element.html(formatted_date);
        },

        prepare_datepickers: function () {
            const today = !isNaN(parseInt($('#customDayClosingHour')?.val())) ?
                    new Date((new Date()).getTime() - (parseInt($('#customDayClosingHour')?.val()) * 60) * 60000) :
                    new Date();

            var customNumberOfMonths = $(".start_datepicker").attr("data-months-count"),
                start_date = HotelDateTime($("#internalStartDate").val()),
                end_date = HotelDateTime($("#internalEndDate").val()),
                start_month_offset = get_month_difference(today, start_date),
                end_month_offset = get_month_difference(start_date, end_date);

            $(".start_datepicker").datepicker({
                numberOfMonths: typeof(customNumberOfMonths) !== 'undefined' ? parseInt(customNumberOfMonths) : 12,
                minDate: today,
                defaultDate: start_date,
                showCurrentAtPos: start_month_offset,
                firstDay: this.config.custom_week_day_start,
                dayNamesMin: $.datepicker._defaults.dayNames.map(function(x){
                    var shorted = x.slice(0,2);
                    if (shorted.indexOf('&') > -1) {
                        shorted = x.slice(0,x.indexOf(';'));
                    }
                    return shorted;
                }),
                beforeShowDay: function(date) {
                    var show_day = true,
                        string_date = $.datepicker.formatDate("yy-mm-dd", date);

                    if(datePickerWidgetBooking.config.period_closed) {
                        datePickerWidgetBooking.config.period_closed.forEach(function(period) {
                            var last_close = period.last_close,
                                available_days = period.available_days,
                                number_day = date.getDay();

                            if(last_close) {
                                if (string_date >= last_close) {
                                    show_day = false;
                                }
                            } else {
                                if(string_date >= period.close && string_date < period.open) {
                                    show_day = available_days && available_days.indexOf(number_day.toString()) > -1 ? true : false;
                                }
                            }
                        });
                    }

                    return [show_day];
                },
                onSelect: function(dateText, ins){
                    if (datePickerWidgetBooking.config.show_confirm_dates_button) {
                        $(".confirm_dates_button").attr("disabled", true).addClass("disabled");
                    }

                    datePickerWidgetBooking.format_date(dateText, $(".entry_date_wrapper .entry_date"));
                    $("input[name='startDate']").val(dateText);
                    start_date_selection = dateText;
                    var splitted_date = dateText.split("/");
                    var current_start_day = new Date(splitted_date[2], splitted_date[1] - 1, splitted_date[0]);
                    var next_day = new Date(splitted_date[2], splitted_date[1] - 1, splitted_date[0]);
                    next_day.setDate(next_day.getDate() + 1);

                    $(".end_datepicker").datepicker("option", "minDate", next_day);
                    var limit_days_search = HotelDateTime(next_day);
                    limit_days_search.setDate(limit_days_search.getDate() + 118);
                    $(".end_datepicker").datepicker("option", "maxDate", limit_days_search);
                    $(".end_datepicker").datepicker("setDate", next_day);
                    if($("input[name='endDate']").val()) {
                        $(".end_datepicker").datepicker("option", "showCurrentAtPos", get_month_difference(current_start_day, next_day));
                    }
                    datePickerWidgetBooking.format_date(next_day, $(".departure_date_wrapper .departure_date"));
                    datePickerWidgetBooking.open_departure_datepicker();

                    //Fix for issue with selection of months that goes to past
                    ins.drawMonth += $(".start_datepicker").datepicker("option", "showCurrentAtPos");
                }
            });
            datePickerWidgetBooking.format_date(new Date(), $(".entry_date_wrapper .entry_date"));

            // Closed Hotel Today
            if ($("input[name=closed_hotel_open_date]").length) {
                start_date = HotelDateTime($("input[name=closed_hotel_open_date]").val());
                $(".start_datepicker").datepicker("option", "minDate", start_date);
            }

            let start_date_value = $(".start_datepicker").val();
            if (!start_date_value) {
                let today_time = new Date(),
                    values_to_join = [today_time.getDate(), today_time.getMonth() + 1, new Date().getFullYear()];

                start_date_value = values_to_join.join('/');
            }

            var tomorrow = $.datepicker.parseDate("dd/mm/yy", start_date_value);
            tomorrow.setDate(tomorrow.getDate() + 1);

            var search_days_limit = $.datepicker.parseDate("dd/mm/yy", start_date_value);
            search_days_limit.setDate(search_days_limit.getDate() + 119);

            start_date_selection = start_date_value;
            $(".end_datepicker").datepicker({
                numberOfMonths: typeof(customNumberOfMonths) !== 'undefined' ? parseInt(customNumberOfMonths) : 12,
                minDate: tomorrow,
                maxDate: search_days_limit,
                defaultDate: end_date,
                dateFormat: "dd/mm/yy",
                showCurrentAtPos: end_month_offset,
                firstDay: this.config.custom_week_day_start,
                dayNamesMin: $.datepicker._defaults.dayNames.map(function(x){
                    var shorted = x.slice(0,2);
                    if (shorted.indexOf('&') > -1) {
                        shorted = x.slice(0,x.indexOf(';'));
                    }
                    return shorted;
                }),
                onSelect: function (dateText, inst) {
                    datePickerWidgetBooking.format_date(dateText, $(".departure_date_wrapper .departure_date"));
                    $("input[name='endDate']").val(dateText);

                    const start_date_selected = $.datepicker.parseDate("dd/mm/yy", $("input[name='startDate']").val());
                    const end_date_selected = $.datepicker.parseDate("dd/mm/yy", dateText);

                    if(show_current_at_pos > 0) {
                        const start_date_month = start_date_selected.getMonth();
                        const end_date_month = end_date_selected.getMonth();

                        show_current_at_pos = Math.abs(end_date_month - start_date_month);
                        inst.drawMonth = end_date_month;
                        $(".end_datepicker").datepicker("option", "showCurrentAtPos", show_current_at_pos);
                    }

                    if (datePickerWidgetBooking.config.show_selected_nights) {
                        const nights_selected = (end_date_selected - start_date_selected) / (24 * 60 * 60 * 1000);
                        datePickerWidgetBooking.set_nights_selected(nights_selected);
                    }

                    if (datePickerWidgetBooking.config.show_confirm_dates_button) {
                        $(".confirm_dates_button").attr("disabled", false).removeClass("disabled");
                        return;
                    }

                    datePickerWidgetBooking.close_departure_datepicker();
                },
                beforeShowDay: function (date) {
                    var show_date = $.datepicker.formatDate("dd/mm/yy", date),
                        show_day = true;

                    if(datePickerWidgetBooking.config.period_closed) {
                        datePickerWidgetBooking.config.period_closed.forEach(function(period) {

                            try {
                                var last_close = period.last_close,
                                    available_days = period.available_days,
                                    number_day = date.getDay(),
                                    period_close = period.close,
                                    period_open = period.open;
                            } catch (e) {}

                            if(last_close) {
                                last_close = $.datepicker.parseDate("yy-mm-dd", last_close);
                                last_close.setDate(last_close.getDate() + 1);
                                if (date >= last_close) {
                                    show_day = false;
                                }
                            } else {
                                period_close = $.datepicker.parseDate("yy-mm-dd", period_close);
                                period_open = $.datepicker.parseDate("yy-mm-dd", period_open);

                                period_close.setDate(period_close.getDate() + 1);

                                if(date >= period_close && date < period_open) {
                                    show_day = available_days && available_days.indexOf(number_day.toString()) > -1 ? true : false;
                                }
                            }
                        });
                    }

                    var splitted_date = start_date_selection.split("/"),
                        start_datetime = new Date(splitted_date[2], splitted_date[1] - 1, splitted_date[0]);

                    var splitted_show_date = show_date.split("/"),
                        show_datetime = new Date(splitted_show_date[2], splitted_show_date[1] - 1, splitted_show_date[0]);

                    var actual_selected_date = $(".end_datepicker").val().split("/"),
                        actual_selected_datetime = new Date(actual_selected_date[2], actual_selected_date[1] - 1, actual_selected_date[0]);

                    if (show_datetime.getTime()  == start_datetime.getTime()) {
                        return [show_day, 'start_date_selection', ''];
                    }

                    if (show_datetime > start_datetime && show_datetime < actual_selected_datetime) {
                        return [show_day, 'highlight_day', ''];
                    }

                    return [show_day, '', ''];
                }
            });
            datePickerWidgetBooking.format_date(tomorrow, $(".departure_date_wrapper .departure_date"));

            $(".start_datepicker").addClass('notranslate');
            $(".end_datepicker").addClass('notranslate');

            let open_price_calendar = $('.double_button_wrapper.parent .modify_search').data("price-calendar-by-default")

            $(".entry_date_wrapper").click(function () {
              if (open_price_calendar) {
                $(".double_button_wrapper.parent .show_calendar, .dates_flexible, .open_availability_calendar").click();
              } else {
                datePickerWidgetBooking.open_entry_datepicker();
              }
            });

            $(".departure_date_wrapper").click(function () {
              if (open_price_calendar) {
                $(".double_button_wrapper.parent .show_calendar, .dates_flexible, .open_availability_calendar"
                ).click();
              } else {
                datePickerWidgetBooking.open_departure_datepicker();
              }
            });

            $(".dates_popup .close_popup, .bottom_buttons_wrapper .close_button, .buttons_bottom .accept_button:not(.disabled)").click(function(){
                $(this).closest(".dates_popup").removeClass('active');
                $(".flexible_dates_wrapper, .dates_flexible").removeClass('active');
                $('#calendar_content_wrapper').append($('#calendar-app-root'));
                $(".normal_calendar_button").addClass('active');
                $("body").removeClass("fixed_screen");
            });

            $("body").on("click", ".bottom_buttons_wrapper .close_button", function () {
                $(this).closest(".dates_popup").removeClass('active');
                $(".flexible_dates_wrapper, .dates_flexible").removeClass('active');
                $('#calendar_content_wrapper').append($('#calendar-app-root'));
                $(".normal_calendar_button").addClass('active');
                $("body").removeClass("fixed_screen");
            });

            if (datePickerWidgetBooking.config.show_selected_nights) {
                $(window).on("nightsSelectedChange", function(event) {
                    datePickerWidgetBooking.set_nights_selected(event.detail);
                });
            }

            if (datePickerWidgetBooking.config.show_confirm_dates_button) {
                $(".confirm_dates_button").click(function (e) {
                    e.preventDefault();
                    datePickerWidgetBooking.close_departure_datepicker();
                });
            }

            $(".dates_flexible.dates_button").click(function(){
                datePickerWidgetBooking.show_flexible_days($(this));
            });

            $(".normal_calendar_button.dates_button").click(function(){
                datePickerWidgetBooking.show_default_calendar($(this));
            });
        },

        open_entry_datepicker: function () {
            var entry_popup = $("#entry_date_popup"),
                departure_popup = $("#departure_date_popup");

            $("body").addClass("fixed_screen");

            entry_popup.addClass('active');
            departure_popup.removeClass('active');
            $(".booking_widget_wrapper").removeClass('from_btn');
        },

        open_departure_datepicker: function () {
            var entry_popup = $("#entry_date_popup"),
                departure_popup = $("#departure_date_popup");

            $("body").addClass("fixed_screen");

            entry_popup.removeClass('active');
            departure_popup.addClass('active');
            $(".booking_widget_wrapper").removeClass('from_btn');
        },

        close_departure_datepicker: function () {
            var entry_popup = $("#entry_date_popup"),
                departure_popup = $("#departure_date_popup");

            $("body").removeClass("fixed_screen");

            entry_popup.removeClass('active');
            departure_popup.removeClass('active');
            $('#calendar_content_wrapper').append($('#calendar-app-root'));
            $(".booking_widget_wrapper").removeClass('from_btn');
        },

        show_flexible_days: function(clicked_button) {

            if (clicked_button.closest("#departure_date_popup").length > 0) {
                var real_wrapper_popup = $("#entry_date_popup.dates_popup"),
                    closest_wrapper = clicked_button.closest(".dates_popup");
                real_wrapper_popup.addClass("active");
                clicked_button.closest("#departure_date_popup").removeClass("active");
                closest_wrapper.find(".dates_button").removeClass('active');
                real_wrapper_popup.find(".dates_flexible.dates_button").addClass('active');
                real_wrapper_popup.find(".flexible_dates_wrapper").addClass('active');
            } else {
                var wrapper_popup = clicked_button.closest(".dates_popup");
                wrapper_popup.find(".dates_button").removeClass('active');
                clicked_button.addClass('active');
                wrapper_popup.find(".flexible_dates_wrapper").addClass('active');
            }
        },
        show_default_calendar: function(clicked_button) {
            var wrapper_popup = clicked_button.closest(".dates_popup");
            wrapper_popup.find(".dates_button").removeClass('active');
            clicked_button.addClass('active');
            wrapper_popup.find(".flexible_dates_wrapper").removeClass('active');
            $(".booking_widget_wrapper").removeClass('from_btn');
        },
        set_nights_selected: function(nights) {
            if (!nights || nights < 1 || !datePickerWidgetBooking.config.show_selected_nights) return;
            $(".dates_popup .nights_selected_wrapper .num_nights_selected").text(nights);
        }
    }
}();

function showing_rooms(index) {
    var max_rooms = parseInt($(".counter_box.room_box").data("max"));

    for(var i=1; i <= max_rooms; i++) {
        if(i <= index) {
            $(".room_resume_item.room_element_" + i).slideDown();
        } else {
            $(".room_resume_item.room_element_" + i).slideUp();
        }
    }
}

function setOccupancyString() {
    var room_box = $(".counter_box.room_box"),
        adult_box = $(".counter_box.adult_box"),
        kid_box = $(".counter_box.kid_box"),
        pet_box = $(".counter_box.pet_box"),
        baby_box = $(".counter_box.baby_box"),
        merge_kids_number = $("input[name='merge_kids_number']").val(),
        adults_only = $(".booking_widget_wrapper .occupancy_popup .room_resume_item").first().hasClass('adults_only');

    var room_amount = parseInt(room_box.attr("data-current")),
        adult_amount = 0,
        kid_amount = 0,
        pet_amount = 0,
        baby_amount = 0;

    adult_box.each(function (index) {
        if (index >= room_amount) {
            return false;
        }

        adult_amount += parseInt($(this).attr("data-current"));
    });

    if (!adults_only) {
        kid_box.each(function (index) {
            if (index >= room_amount) {
                return false;
            }

            kid_amount += parseInt($(this).attr("data-current"));
        });

        baby_box.each(function (index) {
            if (index >= room_amount) {
                return false;
            }

            baby_amount += parseInt($(this).attr("data-current"));
        });
    }

    pet_box.each(function (index) {
        if (index >= room_amount) {
            return false;
        }

        pet_amount += parseInt($(this).attr("data-current"));
    });

    if ($.i18n._("T_habitacion") === "T_habitacion") {
        $.i18n.load(messages); //Randomly at this point, may is not loaded needed translates
    }

    var occupancy_string = "";
    const hide_rooms = $(".booking_widget_wrapper input[name=hide_rooms]");
    if (!hide_rooms.length) {
        occupancy_string += room_amount + " ";
        occupancy_string += room_amount === 1 ? $.i18n._("T_habitacion") : $.i18n._("T_habitaciones");
        occupancy_string += ", ";
    }

    occupancy_string += adult_amount + " ";
    occupancy_string += adult_amount === 1 ? $.i18n._("T_adulto") : $.i18n._("T_adultos");

    if (merge_kids_number) {
        let total_kids = kid_amount;
        if (baby_amount > 0) {
            total_kids += baby_amount;
        }

        if (total_kids > 0) {
            occupancy_string += ", " + total_kids + " ";
            occupancy_string += total_kids === 1 ? $.i18n._("T_nino") : $.i18n._("T_ninos");
        }
    } else {
        if (kid_amount > 0) {
            occupancy_string += ", " + kid_amount + " ";
            occupancy_string += kid_amount === 1 ? $.i18n._("T_nino") : $.i18n._("T_ninos");
        }

        if (baby_amount > 0) {
            occupancy_string += ", " + baby_amount + " ";
            occupancy_string += baby_amount === 1 ? $.i18n._("T_bebe") : $.i18n._("T_bebes");
        }
    }

    if (pet_amount > 0) {
        occupancy_string += ", " + pet_amount + " ";
        occupancy_string += pet_amount === 1 ? $.i18n._("T_mascota") : $.i18n._("T_mascotas");
    }
    $(".row.ocupation .ocupation_detail p").text(occupancy_string);
}

function set_search_params() {
   let startDateVal = $("#internalStartDate").val(),
       endDateVal = $("#internalEndDate").val();

    if(startDateVal && endDateVal) {
        let splitted_start = startDateVal.split("-"),
            splitted_end = endDateVal.split("-");

        let start_date = new Date(splitted_start[0], splitted_start[1] - 1, splitted_start[2]),
            end_date = new Date(splitted_end[0], splitted_end[1] - 1, splitted_end[2]);

        var num_room = $("#internalNumRooms"),
            promocode = $("#internalPromocode"),
            hidden_promocode = $("#internalHiddenPromocode"),
            occupancy_option = $(".internalSearchOccupancy");

        datePickerWidgetBooking.format_date(start_date, $(".entry_date_wrapper .entry_date"));
        $(".entry_input").val($.datepicker.formatDate("dd/mm/yy", start_date));
        datePickerWidgetBooking.format_date(end_date, $(".departure_date_wrapper .departure_date"));
        $(".departure_input").val($.datepicker.formatDate("dd/mm/yy", end_date));

        showing_rooms(parseInt(num_room.val()));
        $("input[name=numRooms]").val(num_room.val());
        $(".counter_box.room_box").attr("data-current", num_room.val());
        $(".counter_box.room_box .quantity").text(num_room.val());
        $(".row.promocode input, input.promocode_text").val(promocode.val());

        if ($("#internalHiddenName").length) {
            var hidden_name_val = $("#internalHiddenName").val();
            $(".paraty-booking-form").each(function(){
                $(this).append($(`<input type='hidden' name='hidden_name' value='${hidden_name_val}'>`));
            });
        }
        if(hidden_promocode.length){
            $(".paraty-booking-form input[name='hidden_default_promocode']").val(hidden_promocode.val());
        }

        occupancy_option.each(function () {
            var data = $(this).data(),
                value = $(this).val(),
                room_element = $(".room_element_" + data['counter']);

            if (data['name'] === "adult") {
                $("input[name=adultsRoom" + data['counter'] + "]").val(value);
                room_element.find(".adult_box").attr("data-current", value);
                room_element.find(".adult_box .quantity").text(value);
            } else if (data['name'] === "kid") {
                $("input[name=childrenRoom" + data['counter'] + "]").val(value);
                room_element.find(".kid_box").attr("data-current", value);
                room_element.find(".kid_box .quantity").text(value);
            } else if (data['name'] === "baby") {
                $("input[name=babiesRoom" + data['counter'] + "]").val(value);
                room_element.find(".baby_box").attr("data-current", value);
                room_element.find(".baby_box .quantity").text(value);
            } else if (data['name'] === "pet") {
                $("input[name=petsRoom" + data['counter'] + "]").val(value);
                room_element.find(".pet_box").attr("data-current", value);
                room_element.find(".pet_box .quantity").text(value);
            }
        });
        setOccupancyString();
        $(".counter_box").each(function () {
            check_if_limit($(this));
        });

         $(".kid_box, .baby_box").each(function () {
            check_kids_ages($(this), true);
        });
    } else {
        console.log("date not found");
    }
}

function check_if_limit(element) {
    var min_value = element.data("min"),
        max_value = element.data("max"),
        current_value = parseInt(element.attr("data-current"));

    if (current_value === min_value) {
        element.find(".subtract").addClass("limit");
    } else if (current_value === max_value) {
        element.find(".add").addClass("limit");
    } else {
        element.find(".limit").removeClass("limit");
    }
}

function open_widget() {
    $(".double_button_wrapper.parent").slideUp("slow").promise().done(function () {
        $(".search-resume-wrapper").slideUp("slow").promise().done(function () {
            $(".booking_widget_wrapper").slideDown("slow");
        });
    });
}

function close_widget() {
    $(".booking_widget_wrapper").slideUp("slow").promise().done(function () {
        $(".search-resume-wrapper").slideDown("slow").promise().done(function () {
            $(".double_button_wrapper.parent").slideDown("slow");
        });
    });
}

function open_calendar_price_loader_popup() {
    if (typeof lottie === 'undefined' ||
        typeof loader_animation === 'undefined' ||
        !$('.container_popup_calendar').length ||
        window.location.href.includes('localhost')) {
        return;
    }

    const container_booking_popup = $('.container_popup_calendar');
    const animation_container = container_booking_popup.find('#lottie_animation');

    let params = {
        container: $('#lottie_animation')[0],
        renderer: 'svg',
        loop: true,
        autoplay: true,
        animationData: loader_animation
    };

    let anim = lottie.loadAnimation(params);

    setTimeout(function () {
        params['container'] = $('#lottie_animation_calendar')[0];
        params['loop'] = false;

        let anim_calendar = lottie.loadAnimation(params);

        anim_calendar.addEventListener('complete', function() {
            anim_calendar.stop();
            setTimeout(() => {
                anim_calendar.play();
            }, 5000);
        });
    }, 5000);

    //Open popup
    container_booking_popup.fadeIn();

    setTimeout(function () {
        //Close popup
        container_booking_popup.fadeOut();
    }, 5000);
}

function open_flexible_calendar(element_clicked) {
    var calendar_wrapper = $("#calendar_price"),
        entry_popup = $("#entry_date_popup"),
        btn_calendar = $(".btn_calendar"),
        namespace = $(element_clicked).attr('namespace') ? $(element_clicked).attr('namespace') : null,
        container_flexible = entry_popup.find(".flexible_dates_wrapper");

    if (container_flexible.length) {
        calendar_wrapper = container_flexible;
        $(".booking_widget_wrapper").addClass('from_btn').slideDown();
        $(".dates_flexible").addClass('active');
        $(".normal_calendar_button").removeClass('active');
        container_flexible.addClass('active');
    }
    $(".double_button_wrapper.parent").hide();

    $("body").addClass("fixed_screen");

    entry_popup.addClass('active');

    if ($(element_clicked).attr('namespace')) {
        calendar_specific_namespace_query = $(element_clicked).attr('namespace');
        customized_action_target = $(element_clicked).closest(".hotel_element").find(".hotel_submit").attr('data-url');
    } else {
        calendar_specific_namespace_query = undefined;
        customized_action_target = undefined;
    }

    let is_price_calendar_v2 = $('#price_calendar_v2').length;
    if (is_price_calendar_v2) {
        let flexible_calendar = $('#flexible_entry_dates');

        if ($(element_clicked).attr('namespace') && typeof window.calendar_data !== 'undefined') {
            window.calendar_data.update_namespace_calendar(calendar_specific_namespace_query);
            if(element_clicked.attr('target-url')){
                window.calendar_data.baseUrl = element_clicked.attr('target-url');
            }
        }
        flexible_calendar.append($('#calendar-app-root'));

        let prices_updated = flexible_calendar.hasClass('prices_loaded');

         if (!prices_updated) {
            const event = new CustomEvent('calendarShowed.price_calendar_v2');
            document.dispatchEvent(event);
            flexible_calendar.addClass('prices_loaded');
         }
    } else {
        flexible_calendar_search(calendar_wrapper, btn_calendar, namespace);
    }
}

function update_calendar_v2_namespace(namespace) {
    const isCalendarV2 = $("#calendar-app-root") && $("#calendar-app-root").length;
    if (isCalendarV2) {
        document.dispatchEvent(new CustomEvent('updateNamespace.price_calendar_v2', {
            detail: {namespace: namespace}
        }));
    }
};

function flexible_calendar_search(calendar_wrapper, button, namespace) {
    var calendar_search_url = $("#calendar_search_url").val();

    if (calendar_search_url.indexOf(namespace) > -1) {
        if (calendar_wrapper.html() !== "") {
            calendar_wrapper.slideDown();
            return false;
        }
    } else  {
        calendar_wrapper.slideDown();
    }

    if (namespace) {
        calendar_wrapper.html("");
        $("#calendar_search_url").val(calendar_search_global + "&namespace=r__" + namespace)
        calendar_search_url = calendar_search_global;
        calendar_search_url = calendar_search_url.replace(/&namespace=.*&/gm, '&');
        calendar_search_url = calendar_search_url + "&namespace=r__" + namespace;
    } else {
        calendar_wrapper.html("");
        var calendar_seach_main = "";
        if (window.calendar_search_global !== undefined) {
            calendar_seach_main = calendar_search_global;
        } else {
            calendar_seach_main = calendar_search_url;
        }
        $("#calendar_search_url").val(calendar_seach_main);
        calendar_search_url = calendar_seach_main;
    }

    var previous_text = button.html();
    button.html('<i class="far fa-circle-notch fa-spin fa-1x fa-fw"></i>');

    // Replace # to url encode, used for promocodes with #
    calendar_search_url = calendar_search_url.replace(/#/g, "%23");

    $.ajax(calendar_search_url).done(function (data) {
        calendar_wrapper.html(data);
        build_months_selector(2);
        calendar_wrapper.slideDown();
        button.html(previous_text);
        if (!$(".flexible_dates_wrapper.active .bottom_buttons_wrapper").length) {
            $('.flexible_dates_wrapper.active .calendars-section .buttons-section').append('<div class="bottom_buttons_wrapper popup_helper_wrapper"><div class="close_button message_element">' + $.i18n._("entry_date_select") + '</div></div>').promise().done(function () {
                $("#entry_date_popup .buttons-section .button").detach().appendTo('.bottom_buttons_wrapper');
            });
        }
    });
}

function get_month_difference(date1, date2) {
    if (date1 && date2) {
        return Math.abs((date2.getFullYear() - date1.getFullYear()) * 12 + (date2.getMonth() - date1.getMonth()));
    } else {
        return 0
    }
}

function price_calendar_callback(date, isStartDateSelection) {

    const date_formated = $.datepicker.formatDate("dd/mm/yy", date);

    if (isStartDateSelection) {
        $("input[name=startDate]").val(date_formated);
        $("input[name=startDate]").trigger("change");
    } else {
        $("input[name=endDate]").val(date_formated);
        $("input[name=endDate]").trigger("change");
    }

    replaceDateVariables($(".loading_animation_popup"));
}

let is_price_calendar_v2 = $("#price_calendar_v2").length;
if (is_price_calendar_v2 && typeof window.calendar_data !== "undefined") {
    window.calendar_data.change_date_callback = price_calendar_callback;
    const thousands_separator = $("#thousands_separator").val();
    const decimal_separator = $("#decimal_separator").val();
    if (thousands_separator) {
        window.calendar_data.thousands_separator = thousands_separator;
    }
    if (decimal_separator) {
        window.calendar_data.decimal_separator = decimal_separator;
    }
}

function replaceDateVariables(element) {

    if(!element.length) {
        return;
    }

    const startDate = $("input[name=startDate]").val();
    const endDate = $("input[name=endDate]").val();

    let elementContentEdited = element.html()
    elementContentEdited = elementContentEdited.replace("@@start_date@@", startDate)
    elementContentEdited = elementContentEdited.replace("@@end_date@@", endDate)

    element.html(elementContentEdited);

}

//Reject injections by js in promocode input when invalid characters are detected
(function ($) {
    let originalVal = $.fn.val;
    $.fn.val = function (value) {
        if (arguments.length > 0 && value !=='') {
            if (this.attr("name") === "promocode" && typeof value === "string" && !/^[a-zA-Z0-9.#&_-]+$/.test(value)) {
                console.warn("Invalid input detected. Value rejected for promocode input.");
                return this;
            }
        }
        return originalVal.apply(this, arguments);
    };
})(jQuery);