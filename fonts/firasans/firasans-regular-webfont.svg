<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="fira_sansregular" horiz-adv-x="1024" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="542" />
<glyph unicode="&#xfb01;" horiz-adv-x="1177" d="M20 934v145h173v142q0 144 92 229.5t280 85.5q154 0 299 -74l-65 -131q-105 51 -228 51q-108 0 -149 -38.5t-41 -120.5v-144h602v-1079h-188v934h-414v-934h-188v934h-173z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1212" d="M20 934v145h173v148q0 140 91 224.5t265 84.5q122 0 250 -41l186 25v-1303q0 -44 16 -65t52 -21q39 0 69 12l49 -131q-75 -37 -159 -37q-103 0 -159 61t-56 177v1137q-112 34 -236 34q-100 0 -140 -37t-40 -118v-150h262l-20 -145h-242v-934h-188v934h-173z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode="&#xd;" horiz-adv-x="542" />
<glyph unicode=" "  horiz-adv-x="542" />
<glyph unicode="&#x09;" horiz-adv-x="542" />
<glyph unicode="&#xa0;" horiz-adv-x="542" />
<glyph unicode="!" horiz-adv-x="493" d="M104 119q0 59 41 100t101 41q61 0 102 -41t41 -100q0 -60 -41 -102t-102 -42q-60 0 -101 42t-41 102zM139 1411h209l-20 -940h-166z" />
<glyph unicode="&#x22;" horiz-adv-x="817" d="M123 1411h203l-31 -537h-141zM492 1411h202l-30 -537h-142z" />
<glyph unicode="#" horiz-adv-x="1060" d="M31 334v141h151l58 440h-134v144h152l41 311h160l-41 -311h315l41 311h160l-41 -311h137v-144h-156l-57 -440h137v-141h-157l-43 -334h-160l43 334h-315l-43 -334h-160l43 334h-131zM342 475h315l58 440h-316z" />
<glyph unicode="$" horiz-adv-x="1087" d="M51 154l111 120q146 -143 352 -143q128 0 211.5 63t83.5 181q0 100 -61.5 155t-235.5 109q-210 65 -308 154t-98 241q0 143 101 239.5t264 115.5v299h164v-302q99 -10 177.5 -48t150.5 -105l-109 -119q-131 123 -307 123q-108 0 -177 -50.5t-69 -144.5q0 -60 23.5 -99 t87.5 -73.5t188 -72.5q75 -23 127.5 -44t109 -56t90.5 -76t56.5 -102.5t22.5 -137.5q0 -155 -98 -261.5t-273 -135.5v-301h-164v294q-251 11 -420 177z" />
<glyph unicode="%" horiz-adv-x="1691" d="M92 1044q0 157 93.5 252t238.5 95q147 0 240.5 -94.5t93.5 -252.5q0 -157 -93.5 -251.5t-240.5 -94.5q-145 0 -238.5 94.5t-93.5 251.5zM258 1044q0 -43 8 -79.5t25.5 -69t51.5 -51.5t81 -19q166 0 166 219q0 43 -7.5 79.5t-25 69.5t-51.5 52t-82 19q-90 0 -128 -62 t-38 -158zM307 23l948 1433l131 -86l-948 -1433zM934 322q0 157 93.5 251.5t240.5 94.5t239 -94t92 -252t-92 -252.5t-239 -94.5t-240.5 94.5t-93.5 252.5zM1102 322q0 -43 8 -79.5t25.5 -69.5t51.5 -52t81 -19q166 0 166 220q0 43 -7.5 79t-25 68.5t-51.5 51t-82 18.5 q-166 0 -166 -217z" />
<glyph unicode="&#x26;" horiz-adv-x="1492" d="M166 358q0 126 72 222t213 186q-103 100 -151 177t-48 175q0 139 96.5 228.5t269.5 89.5q166 0 270.5 -88.5t104.5 -223.5q0 -63 -22.5 -118.5t-65 -101.5t-89.5 -81t-111 -73l360 -340q81 165 123 346l176 -51q-73 -241 -182 -404l229 -215l-135 -111l-199 193 q-178 -193 -452 -193q-204 0 -331.5 105t-127.5 278zM358 365q0 -112 79 -176t208 -64q180 0 324 147l-410 392q-103 -67 -152 -138.5t-49 -160.5zM436 1120q0 -73 39 -132t123 -138q105 64 158 125.5t53 138.5q0 86 -50.5 134t-137.5 48t-136 -49.5t-49 -126.5z" />
<glyph unicode="'" horiz-adv-x="448" d="M123 1411h203l-31 -537h-141z" />
<glyph unicode="(" horiz-adv-x="663" d="M82 717q0 138 22.5 263t48 208.5t86.5 194t94.5 161t116.5 168.5q3 4 4.5 6.5t4 6t4.5 6.5l119 -82q-62 -92 -103 -160t-82.5 -157.5t-65 -175.5t-38 -198t-14.5 -241q0 -160 20 -289.5t64 -242t92.5 -198.5t126.5 -202l-119 -82q-146 207 -201 304q-142 251 -171 534 q-9 85 -9 176z" />
<glyph unicode=")" horiz-adv-x="663" d="M82 -215q78 116 126.5 202t92.5 198.5t64 242t20 289.5q0 129 -14.5 241t-38 198t-65 175.5t-82.5 157.5t-103 160l119 82q146 -207 201 -304q142 -251 171 -534q9 -85 9 -176q0 -138 -22.5 -263t-48 -208.5t-86.5 -194t-94.5 -161t-116.5 -168.5q-9 -13 -13 -19z" />
<glyph unicode="*" horiz-adv-x="899" d="M41 1149l55 168l291 -125l-31 321h185l-31 -323l291 129l57 -170l-313 -68l213 -237l-148 -109l-161 275l-160 -275l-148 107l211 239z" />
<glyph unicode="+" horiz-adv-x="1021" d="M127 600v156h297v307h172v-307h299v-156h-299v-305h-172v305h-297z" />
<glyph unicode="," horiz-adv-x="491" d="M78 -340l82 340q-58 45 -58 119q0 59 41 100t101 41q61 0 102 -41t41 -100q0 -65 -37 -146l-137 -313h-135z" />
<glyph unicode="-" horiz-adv-x="825" d="M123 561v160h579v-160h-579z" />
<glyph unicode="." horiz-adv-x="491" d="M102 119q0 59 41 100t101 41q61 0 102 -41t41 -100q0 -60 -41 -102t-102 -42q-60 0 -101 42t-41 102z" />
<glyph unicode="/" horiz-adv-x="1064" d="M215 -174l475 1827l160 -37l-473 -1829z" />
<glyph unicode="0" horiz-adv-x="1142" d="M113 684q0 345 115 526t343 181t343.5 -182t115.5 -525q0 -345 -115.5 -527t-343.5 -182t-343 182t-115 527zM309 684q0 -296 65 -427.5t197 -131.5q131 0 197 132t66 427q0 293 -66 425t-197 132q-132 0 -197 -131.5t-65 -425.5z" />
<glyph unicode="1" horiz-adv-x="886" d="M72 1108l424 262h166v-1370h-189v1169l-319 -194z" />
<glyph unicode="2" horiz-adv-x="1013" d="M51 1184q168 207 416 207q186 0 299 -106t113 -269q0 -55 -11.5 -107t-30 -100.5t-57 -106.5t-78.5 -109.5t-108.5 -126.5t-133 -141t-165.5 -167h616l-22 -158h-809v150q85 89 135 141.5t112.5 119.5t96.5 105.5t76 88.5t63 80t45.5 69t35 66t21.5 60.5t14 63.5t3 64 q0 106 -62 166.5t-163 60.5q-84 0 -148 -35t-135 -115z" />
<glyph unicode="3" horiz-adv-x="1021" d="M31 160l112 104q129 -135 293 -135q128 0 202.5 73t74.5 195q0 134 -70 190t-203 56h-102l22 145h70q107 0 177.5 63.5t70.5 178.5q0 96 -62 152.5t-165 56.5q-81 0 -147 -29t-136 -94l-98 113q174 162 391 162q188 0 296.5 -101.5t108.5 -247.5q0 -125 -72.5 -205.5 t-193.5 -109.5q136 -13 222.5 -98t86.5 -234q0 -183 -128 -301.5t-339 -118.5q-257 0 -411 185z" />
<glyph unicode="4" horiz-adv-x="1089" d="M82 338v135l412 918l157 -66l-370 -838h389l16 369h164v-369h178v-149h-178v-338h-182v338h-586z" />
<glyph unicode="5" horiz-adv-x="1026" d="M53 150l111 108q66 -66 136 -97.5t157 -31.5q130 0 205 82t75 231q0 150 -67.5 217.5t-182.5 67.5q-94 0 -194 -47h-148v690h742l-27 -147h-534v-404q103 53 219 53q176 0 282.5 -116.5t106.5 -317.5q0 -205 -131 -334t-344 -129q-236 0 -406 175z" />
<glyph unicode="6" horiz-adv-x="1091" d="M113 643q0 224 61.5 392.5t179 262t277.5 93.5q155 0 282 -84l-73 -125q-95 59 -211 59q-147 0 -233.5 -141t-94.5 -379q128 180 330 180q77 0 144.5 -27.5t120.5 -80t83.5 -137.5t30.5 -191q0 -219 -125.5 -354.5t-311.5 -135.5q-122 0 -212 48.5t-143.5 139t-79 209.5 t-25.5 271zM303 563q6 -226 70.5 -332t199.5 -106q119 0 182.5 90t63.5 244q0 293 -221 293q-89 0 -164.5 -50.5t-130.5 -138.5z" />
<glyph unicode="7" horiz-adv-x="909" d="M51 1217v153h797v-141l-512 -1249l-172 57l493 1180h-606z" />
<glyph unicode="8" horiz-adv-x="1128" d="M92 362q0 127 69 213t201 144q-212 107 -212 319q0 111 61 193t153.5 121t200.5 39q79 0 152 -22t132.5 -63.5t95.5 -109.5t36 -152q0 -102 -57 -175.5t-176 -133.5q288 -116 288 -368q0 -172 -133 -282t-342 -110q-210 0 -339.5 108.5t-129.5 278.5zM289 362 q0 -112 73 -174.5t201 -62.5t202.5 66t74.5 174t-65 169t-234 119l-52 19q-102 -50 -151 -124.5t-49 -185.5zM334 1036q0 -52 16.5 -90t52.5 -66t77.5 -47t107.5 -40l35 -13q93 53 133.5 112t40.5 146q0 97 -60.5 154t-171.5 57q-107 0 -169 -56t-62 -157z" />
<glyph unicode="9" horiz-adv-x="1075" d="M92 938q0 139 60.5 243.5t159 157t218.5 52.5q216 0 329.5 -142.5t113.5 -390.5q0 -142 -18.5 -253.5t-59 -200.5t-97.5 -155.5t-142 -121t-182.5 -94.5t-229.5 -78l-43 141q274 78 420.5 211.5t156.5 354.5q-47 -73 -127 -118.5t-180 -45.5q-164 0 -271.5 120 t-107.5 320zM283 934q0 -143 61.5 -216t165.5 -73q161 0 272 170q4 227 -56.5 326.5t-190.5 99.5q-120 0 -186 -80t-66 -227z" />
<glyph unicode=":" horiz-adv-x="491" d="M102 119q0 59 41 100t101 41q61 0 102 -41t41 -100q0 -60 -41 -102t-102 -42q-60 0 -101 42t-41 102zM102 872q0 59 41 100.5t101 41.5q61 0 102 -41.5t41 -100.5q0 -60 -41 -101.5t-102 -41.5q-60 0 -101 41.5t-41 101.5z" />
<glyph unicode=";" horiz-adv-x="491" d="M78 -340l82 340q-58 45 -58 119q0 59 41 100t101 41q61 0 102 -41t41 -100q0 -65 -37 -146l-137 -313h-135zM102 872q0 59 41 100.5t101 41.5q61 0 102 -41.5t41 -100.5q0 -60 -41 -101.5t-102 -41.5q-60 0 -101 41.5t-41 101.5z" />
<glyph unicode="&#x3c;" d="M102 586v192l752 350l68 -155l-670 -291l670 -295l-68 -151z" />
<glyph unicode="=" d="M127 401v158h770v-158h-770zM127 797v157h770v-157h-770z" />
<glyph unicode="&#x3e;" d="M102 387l670 295l-670 291l68 155l752 -350v-192l-752 -350z" />
<glyph unicode="?" horiz-adv-x="940" d="M61 1233q173 203 426 203q94 0 170 -27t123.5 -72t73 -102t25.5 -119q0 -66 -20.5 -119t-53 -87.5t-72 -64t-79.5 -56.5t-72.5 -56.5t-53 -72.5t-20.5 -97v-92h-186v102q0 70 20 125.5t51.5 92t70 66t77 54.5t70 51t51.5 61.5t20 80.5q0 84 -55.5 130t-151.5 46 q-164 0 -285 -147zM279 119q0 59 40.5 100t100.5 41q61 0 102 -41t41 -100q0 -60 -41 -102t-102 -42q-60 0 -100.5 42t-40.5 102z" />
<glyph unicode="@" horiz-adv-x="2088" d="M143 492q0 159 43 303.5t125.5 260.5t195.5 201.5t259.5 132t310.5 46.5q166 0 308 -43t244 -118t174.5 -177t107.5 -221t35 -248q0 -128 -25 -237t-73 -193.5t-126.5 -132.5t-179.5 -48q-61 0 -109.5 19.5t-78.5 52t-48 67t-26 72.5q-39 -89 -111.5 -147.5t-183.5 -58.5 q-163 0 -258.5 123t-95.5 327q0 258 123 396.5t323 138.5q167 0 322 -82v-531q0 -127 34.5 -179t104.5 -52q217 0 217 461q0 303 -175.5 479t-502.5 176q-170 0 -310 -59.5t-234.5 -164.5t-146.5 -250t-52 -314q0 -170 49.5 -312t142.5 -244.5t234 -159.5t317 -57 q194 0 393 70l52 -145q-115 -40 -216.5 -60t-230.5 -20q-274 0 -485 113t-329 324.5t-118 490.5zM815 473q0 -154 49 -233.5t144 -79.5q152 0 221 176v506q-68 30 -146 30q-268 0 -268 -399z" />
<glyph unicode="A" horiz-adv-x="1173" d="M12 0l453 1411h246l450 -1411h-205l-106 354h-535l-106 -354h-197zM362 512h441l-219 737z" />
<glyph unicode="B" horiz-adv-x="1245" d="M205 0v1411h334q268 0 410.5 -88t142.5 -268q0 -123 -77 -204.5t-188 -105.5q68 -12 121 -33t102 -59t76 -102t27 -150q0 -401 -559 -401h-389zM399 156h195q84 0 143 10t109.5 35.5t76 75t25.5 124.5q0 72 -25 124.5t-70 82t-100 43t-122 13.5h-232v-508zM399 813h213 q124 0 202.5 59t78.5 168q0 64 -22 107t-67.5 67t-103.5 33.5t-141 9.5h-160v-444z" />
<glyph unicode="C" horiz-adv-x="1146" d="M113 707q0 174 46.5 314.5t126.5 230t185.5 137t226.5 47.5q126 0 213.5 -33.5t178.5 -106.5l-107 -125q-128 105 -272 105q-84 0 -152 -30t-123.5 -94t-86 -177t-30.5 -268q0 -149 29.5 -260.5t83.5 -177.5t123 -98t154 -32q90 0 157.5 30.5t143.5 86.5l96 -123 q-66 -68 -169 -113t-235 -45q-264 0 -426.5 192.5t-162.5 539.5z" />
<glyph unicode="D" horiz-adv-x="1318" d="M205 0v1411h295q82 0 152.5 -7.5t144.5 -28.5t132.5 -54t111.5 -87.5t88.5 -126t56 -171.5t20.5 -223q0 -120 -21 -220.5t-57 -172.5t-87.5 -128.5t-108.5 -92t-124.5 -58.5t-131.5 -32t-133 -9h-338zM399 154h158q72 0 131.5 13t119 50.5t100 97.5t66 161.5t25.5 236.5 q0 100 -14 180t-37 136t-58 97.5t-72.5 65.5t-86 38.5t-92 19.5t-96.5 5h-144v-1101z" />
<glyph unicode="E" horiz-adv-x="1095" d="M205 0v1411h764l-23 -156h-547v-460h475v-156h-475v-483h586v-156h-780z" />
<glyph unicode="F" horiz-adv-x="1005" d="M205 0v1411h749l-22 -156h-533v-481h461v-153h-461v-621h-194z" />
<glyph unicode="G" horiz-adv-x="1292" d="M113 707q0 173 49.5 313.5t134.5 230t194.5 137.5t233.5 48q133 0 230 -38t194 -118l-115 -119q-75 61 -145.5 88t-163.5 27q-80 0 -150 -31.5t-128.5 -96t-93 -178t-34.5 -263.5q0 -297 97.5 -434.5t288.5 -137.5q154 0 268 64v426h-250l-23 157h465v-682 q-219 -125 -460 -125q-279 0 -435.5 186.5t-156.5 545.5z" />
<glyph unicode="H" horiz-adv-x="1392" d="M205 0v1411h194v-590h594v590h195v-1411h-195v662h-594v-662h-194z" />
<glyph unicode="I" horiz-adv-x="604" d="M205 0v1411h194v-1411h-194z" />
<glyph unicode="J" horiz-adv-x="624" d="M10 -139q136 54 181 126.5t45 217.5v1206h194v-1214q0 -108 -25 -188t-75.5 -135t-114 -90.5t-154.5 -64.5z" />
<glyph unicode="K" horiz-adv-x="1206" d="M205 0v1411h194v-1411h-194zM410 754l536 657h221l-534 -645l573 -766h-239z" />
<glyph unicode="L" horiz-adv-x="1019" d="M205 0v1411h194v-1241h580l-23 -170h-751z" />
<glyph unicode="M" horiz-adv-x="1593" d="M127 0l117 1411h262l297 -1026l282 1026h265l116 -1411h-188l-49 637q-32 391 -35 575l-305 -1052h-182l-322 1054q0 -242 -27 -591l-47 -623h-184z" />
<glyph unicode="N" horiz-adv-x="1398" d="M205 0v1411h254l579 -1200q-6 53 -16 186.5t-10 243.5v770h182v-1411h-260l-574 1198q27 -316 27 -551v-647h-182z" />
<glyph unicode="O" horiz-adv-x="1417" d="M113 702q0 343 163.5 538.5t432.5 195.5q272 0 434 -191t162 -540q0 -344 -162.5 -537t-433.5 -193t-433.5 190t-162.5 537zM319 702q0 -293 104 -430t286 -137q389 0 389 570q0 571 -389 571q-185 0 -287.5 -139.5t-102.5 -434.5z" />
<glyph unicode="P" horiz-adv-x="1189" d="M205 0v1411h370q261 0 405 -109t144 -327q0 -118 -41 -207.5t-116 -143t-172 -80t-217 -26.5h-179v-518h-194zM399 672h170q83 0 143.5 13.5t108.5 46.5t72.5 93t24.5 148q0 287 -345 287h-174v-588z" />
<glyph unicode="Q" horiz-adv-x="1415" d="M113 702q0 343 163.5 538.5t432.5 195.5q272 0 434 -191t162 -540q0 -169 -40.5 -293t-109.5 -201t-174 -131q202 0 383 -127l-127 -164q-122 109 -233 150t-299 41q-268 0 -430 188t-162 534zM319 702q0 -293 104 -430t286 -137q389 0 389 570q0 571 -389 571 q-185 0 -287.5 -139.5t-102.5 -434.5z" />
<glyph unicode="R" horiz-adv-x="1239" d="M205 0v1411h370q265 0 401 -100.5t136 -298.5q0 -148 -75.5 -240.5t-229.5 -138.5l381 -633h-232l-338 598h-219v-598h-194zM399 748h197q153 0 231 62.5t78 201.5q0 131 -79 189.5t-253 58.5h-174v-512z" />
<glyph unicode="S" horiz-adv-x="1116" d="M51 158l107 118q86 -70 173.5 -105.5t196.5 -35.5q132 0 217.5 65t85.5 187q0 103 -63 160t-242 112q-216 66 -316.5 158t-100.5 250q0 163 124.5 266t319.5 103q140 0 241 -40t195 -124l-106 -119q-152 123 -322 123q-111 0 -182.5 -52t-71.5 -149q0 -62 24.5 -102.5 t90.5 -76.5t194 -75q77 -24 131 -45.5t112.5 -57.5t93.5 -78t58 -105.5t23 -141.5q0 -188 -135 -303t-369 -115q-293 0 -479 183z" />
<glyph unicode="T" horiz-adv-x="1058" d="M31 1245v166h1007l-20 -166h-391v-1245h-195v1245h-401z" />
<glyph unicode="U" horiz-adv-x="1355" d="M184 453v958h195v-944q0 -328 297 -328q152 0 225.5 81.5t73.5 246.5v944h196v-958q0 -215 -131 -346.5t-364 -131.5q-236 0 -364 130.5t-128 347.5z" />
<glyph unicode="V" horiz-adv-x="1138" d="M12 1411h209l354 -1200l355 1200h196l-450 -1411h-209z" />
<glyph unicode="W" horiz-adv-x="1691" d="M51 1411h191l227 -1241l272 1241h207l279 -1241l235 1241h178l-286 -1411h-250l-260 1182l-262 -1182h-244z" />
<glyph unicode="X" horiz-adv-x="1105" d="M10 0l424 752l-387 659h221l285 -530l287 530h209l-383 -649l430 -762h-222l-325 625l-330 -625h-209z" />
<glyph unicode="Y" horiz-adv-x="1126" d="M10 1411h215l344 -698l344 698h203l-454 -868v-543h-197v541z" />
<glyph unicode="Z" horiz-adv-x="1069" d="M61 0v156l705 1091h-631v164h842v-158l-698 -1087h698l-23 -166h-893z" />
<glyph unicode="[" horiz-adv-x="659" d="M133 -238v1909h424v-158h-246v-1595h246v-156h-424z" />
<glyph unicode="\" horiz-adv-x="1064" d="M215 1616l160 37l475 -1827l-162 -39z" />
<glyph unicode="]" horiz-adv-x="659" d="M102 -82h246v1595h-246v158h424v-1909h-424v156z" />
<glyph unicode="^" horiz-adv-x="1105" d="M82 1079l387 641h168l387 -641h-201l-272 477l-270 -477h-199z" />
<glyph unicode="_" horiz-adv-x="1064" d="M35 -129h995v-162h-995v162z" />
<glyph unicode="`" horiz-adv-x="614" d="M61 1483l84 157l408 -233l-57 -100z" />
<glyph unicode="a" horiz-adv-x="1114" d="M92 301q0 172 124 264t351 92h166v80q0 114 -55 163.5t-170 49.5q-120 0 -291 -57l-47 137q201 74 373 74q191 0 285 -93t94 -266v-493q0 -66 22.5 -97.5t67.5 -48.5l-43 -131q-84 9 -135.5 47t-75.5 115q-109 -162 -322 -162q-159 0 -251.5 89.5t-92.5 236.5zM293 311 q0 -194 186 -194q163 0 254 168v247h-141q-299 0 -299 -221z" />
<glyph unicode="b" horiz-adv-x="1216" d="M195 0v1513l188 23v-592q118 160 305 160q185 0 293.5 -148t108.5 -415q0 -256 -114.5 -411t-309.5 -155q-169 0 -287 144l-19 -119h-165zM383 270q42 -66 105 -105.5t133 -39.5q124 0 195 101.5t71 314.5q0 220 -65.5 316.5t-186.5 96.5q-145 0 -252 -168v-516z" />
<glyph unicode="c" horiz-adv-x="978" d="M117 530q0 260 126.5 417t344.5 157q191 0 330 -117l-91 -119q-112 80 -231 80q-131 0 -204 -103.5t-73 -309.5q0 -400 277 -400q64 0 116 19.5t119 62.5l87 -123q-146 -119 -330 -119q-219 0 -345 147.5t-126 407.5z" />
<glyph unicode="d" horiz-adv-x="1224" d="M127 535q0 165 50.5 293.5t147.5 202t226 73.5q165 0 291 -133v565l188 -23v-1513h-166l-18 150q-53 -83 -133 -129t-178 -46q-193 0 -300.5 152t-107.5 408zM330 539q0 -414 245 -414q86 0 147.5 39.5t119.5 120.5v528q-111 141 -250 141q-124 0 -193 -104.5t-69 -310.5 z" />
<glyph unicode="e" horiz-adv-x="1116" d="M117 528q0 256 121 416t327 160q215 0 330 -141t115 -392q0 -46 -4 -96h-689q13 -179 90 -262.5t201 -83.5q79 0 144.5 22t136.5 72l82 -112q-172 -136 -377 -136q-225 0 -351 147.5t-126 405.5zM317 614h506v13q0 160 -64 243.5t-190 83.5q-232 0 -252 -340z" />
<glyph unicode="f" horiz-adv-x="686" d="M20 934v145h193v148q0 139 89 224t249 85q130 0 258 -55l-59 -138q-93 41 -189 41q-87 0 -123.5 -37.5t-36.5 -117.5v-150h263l-21 -145h-242v-934h-188v934h-193z" />
<glyph unicode="g" horiz-adv-x="1064" d="M51 -109h170q0 -65 23 -102t88 -57.5t182 -20.5q169 0 242 41.5t73 126.5q0 75 -57 114.5t-160 39.5h-170q-137 0 -207.5 58.5t-70.5 146.5q0 112 119 190q-94 50 -138.5 122.5t-44.5 176.5q0 165 118 271t298 106q26 -1 50 -1q65 0 119 5q73 5 134 21.5t95 29.5t96 41 l55 -170q-102 -33 -315 -33q190 -85 190 -272q0 -163 -111 -265t-301 -102q-70 0 -137 21q-53 -39 -53 -100q0 -89 141 -89h172q164 0 266.5 -84.5t102.5 -214.5q0 -158 -128.5 -242.5t-377.5 -84.5q-174 0 -276.5 37t-144.5 107t-42 183zM293 727q0 -110 59.5 -177 t167.5 -67q110 0 169 64.5t59 181.5q0 240 -232 240q-106 0 -164.5 -66.5t-58.5 -175.5z" />
<glyph unicode="h" horiz-adv-x="1200" d="M195 0v1511l188 21v-602q128 174 319 174q147 0 230.5 -88.5t83.5 -241.5v-774h-189v748q0 115 -44.5 161.5t-127.5 46.5t-148.5 -48.5t-123.5 -139.5v-768h-188z" />
<glyph unicode="i" horiz-adv-x="577" d="M156 1468q0 56 36.5 92.5t94.5 36.5q59 0 96 -36.5t37 -92.5q0 -55 -37 -91t-96 -36q-58 0 -94.5 36t-36.5 91zM195 0v1079h188v-1079h-188z" />
<glyph unicode="j" horiz-adv-x="573" d="M-18 -297q60 28 93.5 51t62.5 61.5t40.5 95.5t11.5 142v1026h189v-1013q0 -113 -20 -191.5t-66.5 -135.5t-104 -94t-151.5 -79zM154 1468q0 56 36.5 92.5t94.5 36.5q59 0 96 -36.5t37 -92.5q0 -55 -37 -91t-96 -36q-58 0 -94.5 36t-36.5 91z" />
<glyph unicode="k" horiz-adv-x="1048" d="M195 0v1513l188 23v-1536h-188zM395 590l398 489h211l-398 -477l443 -602h-226z" />
<glyph unicode="l" horiz-adv-x="600" d="M184 213v1300l189 23v-1319q0 -44 15.5 -65t51.5 -21q40 0 70 12l49 -131q-66 -37 -149 -37q-104 0 -165 62t-61 176z" />
<glyph unicode="m" horiz-adv-x="1755" d="M195 0v1079h161l17 -157q120 182 311 182q99 0 170 -51t102 -144q130 195 322 195q133 0 213 -89.5t80 -240.5v-774h-189v748q0 208 -151 208q-81 0 -136 -46t-118 -144v-766h-189v748q0 208 -151 208q-81 0 -137 -46.5t-117 -143.5v-766h-188z" />
<glyph unicode="n" horiz-adv-x="1200" d="M195 0v1079h161l17 -159q57 86 141.5 135t187.5 49q149 0 231.5 -87.5t82.5 -242.5v-774h-189v748q0 115 -43 161.5t-127 46.5q-86 0 -151 -48.5t-123 -141.5v-766h-188z" />
<glyph unicode="o" horiz-adv-x="1196" d="M117 539q0 255 128.5 410t354.5 155q227 0 353 -151.5t126 -411.5q0 -255 -128 -410.5t-353 -155.5q-228 0 -354.5 152t-126.5 412zM319 539q0 -412 279 -412t279 414q0 411 -277 411q-281 0 -281 -413z" />
<glyph unicode="p" horiz-adv-x="1216" d="M195 -436v1515h161l15 -145q59 82 142.5 126t176.5 44q207 0 303.5 -148t96.5 -415q0 -254 -110.5 -410t-313.5 -156q-181 0 -283 123v-512zM383 260q86 -131 238 -131q266 0 266 412q0 413 -244 413q-149 0 -260 -168v-526z" />
<glyph unicode="q" horiz-adv-x="1224" d="M127 535q0 165 50.5 293.5t147.5 202t226 73.5q171 0 303 -146l14 121h162v-1515l-188 22v557q-54 -80 -132.5 -124t-174.5 -44q-193 0 -300.5 152t-107.5 408zM330 539q0 -414 245 -414q86 0 147.5 39.5t119.5 120.5v528q-111 141 -250 141q-124 0 -193 -104.5 t-69 -310.5z" />
<glyph unicode="r" horiz-adv-x="790" d="M195 0v1079h161l19 -219q42 121 116 182.5t173 61.5q57 0 106 -12l-35 -185q-54 13 -94 13q-100 0 -162 -73.5t-96 -230.5v-616h-188z" />
<glyph unicode="s" horiz-adv-x="956" d="M51 117l101 114q136 -104 303 -104q106 0 168.5 44t62.5 120q0 80 -53 121t-209 82q-172 45 -249 121t-77 192q0 129 110.5 213t278.5 84q206 0 367 -123l-80 -119q-145 92 -280 92q-92 0 -145.5 -38t-53.5 -103q0 -66 50 -103t181 -71q180 -45 269.5 -123t89.5 -213 q0 -108 -62 -184t-156 -110t-208 -34q-245 0 -408 142z" />
<glyph unicode="t" horiz-adv-x="739" d="M18 934v145h189v244l188 23v-267h256l-20 -145h-236v-643q0 -83 30 -121.5t99 -38.5q65 0 144 43l71 -125q-109 -74 -241 -74q-138 0 -214.5 79.5t-76.5 228.5v651h-189z" />
<glyph unicode="u" horiz-adv-x="1191" d="M184 305v774h189v-753q0 -109 39 -156t127 -47q157 0 270 186v770h188v-1079h-161l-15 168q-62 -100 -142 -146.5t-194 -46.5q-142 0 -221.5 86.5t-79.5 243.5z" />
<glyph unicode="v" horiz-adv-x="1007" d="M20 1079h205l283 -911l280 911h199l-366 -1079h-228z" />
<glyph unicode="w" horiz-adv-x="1468" d="M41 1079h188l195 -948l213 948h211l203 -948l196 948h180l-245 -1079h-256l-189 909l-194 -909h-250z" />
<glyph unicode="x" horiz-adv-x="993" d="M10 0l377 569l-334 510h221l226 -395l227 395h213l-332 -501l375 -578h-227l-264 457l-269 -457h-213z" />
<glyph unicode="y" horiz-adv-x="1007" d="M20 1079h201l289 -942l283 942h194l-360 -1085q-30 -91 -69 -160t-97.5 -127.5t-142.5 -94.5t-193 -48l-21 147q81 14 136.5 36.5t94 60t62.5 80.5t49 112h-63z" />
<glyph unicode="z" horiz-adv-x="894" d="M51 0v141l561 779h-514v159h729v-143l-563 -778h570l-23 -158h-760z" />
<glyph unicode="{" horiz-adv-x="659" d="M51 637v156q80 0 112 36.5t32 122.5v455q0 163 84 234t288 71v-145q-104 0 -149 -32t-45 -108v-489q0 -44 -7 -77.5t-19 -56t-32.5 -40t-42 -28t-53.5 -21.5q55 -19 84.5 -38.5t49.5 -64t20 -116.5v-490q0 -76 45 -107.5t149 -31.5v-146q-204 0 -288 71t-84 235v454 q0 86 -31.5 121t-112.5 35z" />
<glyph unicode="|" horiz-adv-x="825" d="M328 -209v1862h170v-1862h-170z" />
<glyph unicode="}" horiz-adv-x="659" d="M92 -133q104 0 149.5 31.5t45.5 107.5v490q0 55 9.5 92.5t31.5 61.5t46.5 37.5t65.5 27.5q-40 14 -65 28.5t-46.5 39t-31.5 62.5t-10 93v489q0 76 -45.5 108t-149.5 32v145q204 0 288.5 -71t84.5 -234v-455q0 -86 31.5 -122.5t111.5 -36.5v-156q-81 0 -112 -35t-31 -121 v-454q0 -164 -84.5 -235t-288.5 -71v146z" />
<glyph unicode="~" horiz-adv-x="999" d="M72 588q101 198 280 198q51 0 101 -19.5t82 -42.5t69.5 -42.5t67.5 -19.5q82 0 143 98l113 -62q-95 -186 -275 -186q-53 0 -104 20t-83 43.5t-68 43.5t-64 20q-79 0 -148 -111z" />
<glyph unicode="&#xa1;" horiz-adv-x="493" d="M104 870q0 60 41.5 102t102.5 42q60 0 100.5 -42t40.5 -102q0 -59 -40.5 -100t-100.5 -41q-61 0 -102.5 41t-41.5 100zM145 -414l21 920h166l22 -920h-209z" />
<glyph unicode="&#xa2;" horiz-adv-x="978" d="M117 530q0 236 105 389t290 179v303h162v-303q131 -17 244 -111l-91 -119q-112 80 -231 80q-131 0 -204 -103.5t-73 -309.5q0 -400 277 -400q64 0 116 19.5t119 62.5l87 -123q-115 -92 -244 -112v-297h-164v297q-185 25 -289 169t-104 379z" />
<glyph unicode="&#xa3;" horiz-adv-x="1064" d="M82 0v150q104 38 136 90.5t32 179.5v239h-135v123h135v228q0 169 102 275t285 106q225 0 367 -170l-123 -95q-50 58 -105.5 84.5t-130.5 26.5q-99 0 -154 -58t-55 -167v-230h426v-123h-426v-241q0 -104 -26.5 -162t-98.5 -98h656l-23 -158h-862z" />
<glyph unicode="&#xa4;" horiz-adv-x="1146" d="M59 293l160 160q-74 110 -74 245q0 132 72 244l-158 164l113 113l160 -164q101 69 237 69q142 0 248 -67l162 162l108 -113l-155 -158q76 -109 76 -250q0 -140 -76 -252l155 -153l-112 -113l-160 156q-109 -66 -246 -66q-132 0 -241 72l-160 -162zM326 700 q0 -133 64 -206.5t185 -73.5q123 0 187.5 73t64.5 207q0 133 -64.5 207t-187.5 74q-120 0 -184.5 -74.5t-64.5 -206.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1097" d="M10 1370h209l336 -604l334 604h198l-391 -661h232v-127h-283v-185h283v-125h-283v-272h-192v272h-285v125h285v185h-285v127h233z" />
<glyph unicode="&#xa6;" horiz-adv-x="825" d="M328 -209v746h170v-746h-170zM328 907v746h170v-746h-170z" />
<glyph unicode="&#xa7;" horiz-adv-x="1091" d="M143 637q0 74 42.5 139t115.5 109q-108 73 -108 207q0 137 106 215.5t276 78.5q201 0 351 -110l-72 -119q-131 84 -281 84q-93 0 -143.5 -35.5t-50.5 -99.5q0 -71 49 -112.5t201 -92.5q169 -58 244 -125t75 -176q0 -142 -155 -246q110 -76 110 -200q0 -136 -111 -217.5 t-288 -81.5q-199 0 -344 102l71 125q119 -80 279 -80q89 0 147 35t58 98q0 72 -48.5 110t-205.5 89q-85 28 -141 54.5t-98 61.5t-60.5 80.5t-18.5 106.5zM326 662q0 -73 39.5 -110.5t154.5 -74.5q81 -27 162 -61q86 80 86 157q0 72 -44 110.5t-177 86.5q-60 21 -137 55 q-84 -81 -84 -163z" />
<glyph unicode="&#xa8;" horiz-adv-x="788" d="M61 1456q0 50 34 83.5t85 33.5q50 0 83.5 -33.5t33.5 -83.5t-33.5 -83.5t-83.5 -33.5q-51 0 -85 33.5t-34 83.5zM492 1456q0 50 33.5 83.5t82.5 33.5q51 0 85 -33.5t34 -83.5t-34 -83.5t-85 -33.5q-49 0 -82.5 33.5t-33.5 83.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1658" d="M188 889q0 184 86.5 332.5t233 229.5t323.5 81q131 0 249 -48t203.5 -131.5t136 -204t50.5 -259.5q0 -138 -50.5 -258t-136 -203.5t-203.5 -131.5t-249 -48q-177 0 -323.5 81t-233 228.5t-86.5 331.5zM313 889q0 -235 147 -384t371 -149q223 0 370 149.5t147 383.5 q0 236 -147 386t-370 150q-224 0 -371 -150t-147 -386zM502 891q0 183 96 286t238 103q74 0 129 -20.5t110 -61.5l-71 -96q-76 55 -160 55t-135.5 -65.5t-51.5 -200.5q0 -129 50.5 -194.5t136.5 -65.5q90 0 172 63l65 -98q-105 -94 -243 -94q-152 0 -244 102t-92 287z" />
<glyph unicode="&#xaa;" d="M133 805q0 131 101 200.5t288 69.5h119v49q0 80 -40.5 113t-129.5 33q-101 0 -231 -43l-45 127q159 57 307 57q321 0 321 -276v-349q0 -48 16.5 -70.5t51.5 -33.5l-39 -125q-72 8 -116 35.5t-66 85.5q-84 -123 -258 -123q-129 0 -204 69t-75 181zM145 0v158h770v-158 h-770zM326 815q0 -60 34.5 -92.5t98.5 -32.5q115 0 182 109v164h-98q-217 0 -217 -148z" />
<glyph unicode="&#xab;" horiz-adv-x="1177" d="M113 522v129l358 445l113 -78l-277 -430l277 -430l-113 -78zM594 522v129l358 445l113 -78l-277 -430l277 -430l-113 -78z" />
<glyph unicode="&#xac;" d="M127 582v157h770v-450h-170v293h-600z" />
<glyph unicode="&#xad;" horiz-adv-x="825" d="M123 561v160h579v-160h-579z" />
<glyph unicode="&#xae;" horiz-adv-x="1312" d="M137 1010q0 143 68 264t187 191.5t261 70.5q146 0 266.5 -70.5t188.5 -191t68 -264.5q0 -108 -40.5 -204.5t-109.5 -166.5t-166.5 -111t-206.5 -41q-106 0 -202 41t-164.5 111t-109 167t-40.5 204zM250 1010q0 -184 114 -303t289 -119q181 0 295.5 118.5t114.5 303.5 t-114.5 305.5t-295.5 120.5q-175 0 -289 -120.5t-114 -305.5zM467 735v565h160q250 0 250 -172q0 -59 -36.5 -99t-97.5 -58l152 -236h-137l-125 217h-49v-217h-117zM584 1042h61q111 0 111 86q0 82 -115 82h-57v-168z" />
<glyph unicode="&#xaf;" horiz-adv-x="681" d="M61 1366v141h560v-141h-560z" />
<glyph unicode="&#xb0;" horiz-adv-x="1071" d="M113 1108q0 101 49 177.5t124.5 113.5t162.5 37q137 0 236 -89.5t99 -240.5t-99 -238.5t-236 -87.5q-87 0 -162.5 37t-124.5 113.5t-49 177.5zM270 1108q0 -97 52 -149t127 -52q76 0 127 51.5t51 147.5t-51.5 148.5t-126.5 52.5t-127 -51t-52 -148z" />
<glyph unicode="&#xb1;" d="M127 0v158h770v-158h-770zM129 647v156h297v307h172v-307h299v-156h-299v-305h-172v305h-297z" />
<glyph unicode="&#xb2;" horiz-adv-x="819" d="M113 1389q114 139 286 139q132 0 208.5 -69.5t76.5 -176.5q0 -100 -76 -200t-291 -296h388l-17 -127h-551v119q105 102 159.5 156t106.5 110.5t72.5 90t32.5 67t12 68.5q0 62 -36.5 96.5t-96.5 34.5q-53 0 -92.5 -21t-81.5 -69z" />
<glyph unicode="&#xb3;" horiz-adv-x="819" d="M106 764l93 86q78 -84 184 -84q75 0 119.5 39t44.5 106q0 74 -42 106t-122 32h-70l19 114h47q65 0 106 34t41 95q0 54 -36.5 84.5t-98.5 30.5q-95 0 -180 -76l-82 90q119 107 274 107q132 0 206.5 -61.5t74.5 -153.5q0 -77 -47.5 -128.5t-128.5 -72.5q90 -8 147.5 -61 t57.5 -148q0 -112 -87.5 -187t-234.5 -75q-177 0 -285 123z" />
<glyph unicode="&#xb4;" horiz-adv-x="614" d="M61 1407l408 233l84 -157l-434 -176z" />
<glyph unicode="&#xb5;" horiz-adv-x="1204" d="M195 -436v1515h188v-760q0 -182 162 -182q163 0 264 197v745h188v-710q0 -205 54 -369l-177 -25q-17 37 -27 72.5t-13.5 59t-8.5 67.5v2q-49 -85 -121.5 -143t-160.5 -58q-71 0 -117 24t-80 79q35 -106 35 -275v-217z" />
<glyph unicode="&#xb6;" horiz-adv-x="1503" d="M184 995q0 198 135 307t369 109h529v-1825l-170 -26v1702h-234v-1676l-170 -26v1018q-118 6 -207 39.5t-143.5 90t-81.5 128.5t-27 159z" />
<glyph unicode="&#xb7;" horiz-adv-x="491" d="M102 637q0 59 41 100t101 41q61 0 102 -41t41 -100q0 -60 -41 -101.5t-102 -41.5q-60 0 -101 41.5t-41 101.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="563" d="M61 -444l52 106q64 -33 129 -33q46 0 74 20t28 62q0 86 -182 86l28 236h121v-148q98 -8 144.5 -56.5t46.5 -117.5q0 -97 -72 -151t-182 -54q-121 0 -187 50z" />
<glyph unicode="&#xb9;" horiz-adv-x="819" d="M133 1343l287 170h141v-854h-158v693l-200 -117z" />
<glyph unicode="&#xba;" d="M115 983q0 195 106 311.5t293 116.5t291 -114t104 -314q0 -194 -105.5 -311t-291.5 -117q-188 0 -292.5 114.5t-104.5 313.5zM127 0v158h770v-158h-770zM309 983q0 -287 203 -287t203 287t-201 287q-205 0 -205 -287z" />
<glyph unicode="&#xbb;" horiz-adv-x="1159" d="M113 158l276 430l-276 430l112 78l359 -445v-129l-359 -442zM575 158l277 430l-277 430l113 78l359 -445v-129l-359 -442z" />
<glyph unicode="&#xbc;" horiz-adv-x="1908" d="M133 1241l287 170h141v-854h-158v692l-200 -116zM477 -100l834 1636l121 -59l-832 -1637zM1176 197v110l262 561l135 -51l-232 -498h218l14 216h139v-216h109v-122h-109v-197h-156v197h-380z" />
<glyph unicode="&#xbd;" horiz-adv-x="1908" d="M133 1241l287 170h141v-854h-158v692l-200 -116zM477 -100l834 1636l121 -59l-832 -1637zM1202 729q114 139 287 139q132 0 208.5 -69t76.5 -176q0 -100 -76 -200t-291 -296h387l-16 -127h-551v119q106 102 159.5 155.5t106 110.5t73 90t32.5 66.5t12 68.5 q0 62 -36.5 96.5t-96.5 34.5q-53 0 -92.5 -21t-81.5 -69z" />
<glyph unicode="&#xbe;" horiz-adv-x="1908" d="M106 662l93 86q78 -84 184 -84q75 0 119.5 39t44.5 106q0 73 -41.5 105t-122.5 32h-70l19 115h47q65 0 106 34t41 95q0 54 -36.5 84.5t-98.5 30.5q-95 0 -180 -76l-82 90q118 106 274 106q132 0 206.5 -61.5t74.5 -153.5q0 -77 -47.5 -128t-128.5 -72q90 -8 147.5 -61 t57.5 -148q0 -112 -87.5 -187t-234.5 -75q-177 0 -285 123zM477 -100l834 1636l121 -59l-832 -1637zM1176 197v110l262 561l135 -51l-232 -498h218l14 216h139v-216h109v-122h-109v-197h-156v197h-380z" />
<glyph unicode="&#xbf;" horiz-adv-x="940" d="M61 -121q0 66 20.5 119t53 87.5t72 63.5t79.5 55t72.5 54.5t53 70t20.5 93.5v84h186v-94q0 -68 -20 -122t-51.5 -89t-70 -64t-77 -53.5t-70 -50.5t-51.5 -61.5t-20 -80.5q0 -84 55.5 -130t151.5 -46q163 0 285 148l129 -101q-173 -202 -426 -202q-94 0 -170 26.5 t-123.5 71.5t-73 102t-25.5 119zM377 870q0 60 41 102t102 42q60 0 101 -42t41 -102q0 -59 -41 -100t-101 -41q-61 0 -102 41t-41 100z" />
<glyph unicode="&#xc0;" horiz-adv-x="1173" d="M12 0l453 1411h246l450 -1411h-205l-106 354h-535l-106 -354h-197zM322 1708l79 158l412 -219l-49 -101zM362 512h441l-219 737z" />
<glyph unicode="&#xc1;" horiz-adv-x="1173" d="M12 0l453 1411h246l450 -1411h-205l-106 354h-535l-106 -354h-197zM342 1647l410 219l82 -158l-443 -162zM362 512h441l-219 737z" />
<glyph unicode="&#xc2;" horiz-adv-x="1173" d="M12 0l453 1411h246l450 -1411h-205l-106 354h-535l-106 -354h-197zM246 1647l278 225h125l277 -225l-80 -93l-260 166l-258 -166zM362 512h441l-219 737z" />
<glyph unicode="&#xc3;" horiz-adv-x="1173" d="M12 0l453 1411h246l450 -1411h-205l-106 354h-535l-106 -354h-197zM246 1663q85 180 223 180q39 0 75 -15.5t59 -33.5t51 -33.5t53 -15.5q30 0 54.5 21.5t53.5 68.5l115 -57q-80 -176 -223 -176q-46 0 -90 24.5t-81 49t-67 24.5q-34 0 -58.5 -22.5t-50.5 -71.5zM362 512 h441l-219 737z" />
<glyph unicode="&#xc4;" horiz-adv-x="1173" d="M12 0l453 1411h246l450 -1411h-205l-106 354h-535l-106 -354h-197zM254 1714q0 50 34 83.5t85 33.5q49 0 82.5 -33.5t33.5 -83.5t-33.5 -83.5t-82.5 -33.5q-51 0 -85 33.5t-34 83.5zM362 512h441l-219 737zM684 1714q0 50 34 83.5t83 33.5q51 0 85 -33.5t34 -83.5 t-34 -83.5t-85 -33.5q-49 0 -83 33.5t-34 83.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1173" d="M12 0l453 1411h246l450 -1411h-205l-106 354h-535l-106 -354h-197zM354 1753q0 92 66 154.5t168 62.5t167.5 -62.5t65.5 -154.5t-65.5 -154.5t-167.5 -62.5t-168 62.5t-66 154.5zM362 512h441l-219 737zM479 1753q0 -54 29 -84.5t80 -30.5q50 0 79 30.5t29 84.5 t-28.5 84.5t-79.5 30.5t-80 -30.5t-29 -84.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1671" d="M-25 0l562 1411h940l-23 -156h-633l115 -462h522v-156h-483l121 -481h465v-156h-623l-82 354h-549l-133 -354h-199zM367 512h452l-172 743z" />
<glyph unicode="&#xc7;" horiz-adv-x="1146" d="M113 707q0 174 46.5 314.5t126.5 230t185.5 137t226.5 47.5q126 0 213.5 -33.5t178.5 -106.5l-107 -125q-128 105 -272 105q-84 0 -152 -30t-123.5 -94t-86 -177t-30.5 -268q0 -149 29.5 -260.5t83.5 -177.5t123 -98t154 -32q90 0 157.5 30.5t143.5 86.5l96 -123 q-131 -135 -336 -153v-95q98 -8 144.5 -56.5t46.5 -117.5q0 -97 -72 -151t-182 -54q-121 0 -187 50l51 106q64 -33 129 -33q46 0 74.5 20t28.5 62q0 86 -182 86l22 180q-240 22 -385 213t-145 517z" />
<glyph unicode="&#xc8;" horiz-adv-x="1095" d="M205 0v1411h764l-23 -156h-547v-460h475v-156h-475v-483h586v-156h-780zM328 1708l80 158l411 -219l-49 -101z" />
<glyph unicode="&#xc9;" horiz-adv-x="1095" d="M205 0v1411h764l-23 -156h-547v-460h475v-156h-475v-483h586v-156h-780zM348 1647l410 219l82 -158l-443 -162z" />
<glyph unicode="&#xca;" horiz-adv-x="1095" d="M205 0v1411h764l-23 -156h-547v-460h475v-156h-475v-483h586v-156h-780zM252 1647l278 225h125l277 -225l-80 -93l-260 166l-258 -166z" />
<glyph unicode="&#xcb;" horiz-adv-x="1095" d="M205 0v1411h764l-23 -156h-547v-460h475v-156h-475v-483h586v-156h-780zM260 1714q0 50 34 83.5t85 33.5q49 0 83 -33.5t34 -83.5t-34 -83.5t-83 -33.5q-51 0 -85 33.5t-34 83.5zM690 1714q0 50 34 83.5t83 33.5q51 0 85 -33.5t34 -83.5t-34 -83.5t-85 -33.5 q-49 0 -83 33.5t-34 83.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="604" d="M35 1708l80 158l411 -219l-49 -101zM205 0v1411h194v-1411h-194z" />
<glyph unicode="&#xcd;" horiz-adv-x="604" d="M55 1647l410 219l82 -158l-443 -162zM205 0v1411h194v-1411h-194z" />
<glyph unicode="&#xce;" horiz-adv-x="604" d="M-41 1647l279 225h124l277 -225l-80 -93l-260 166l-258 -166zM205 0v1411h194v-1411h-194z" />
<glyph unicode="&#xcf;" horiz-adv-x="604" d="M-33 1714q0 50 34 83.5t85 33.5q50 0 83.5 -33.5t33.5 -83.5t-33.5 -83.5t-83.5 -33.5q-51 0 -85 33.5t-34 83.5zM205 0v1411h194v-1411h-194zM397 1714q0 50 33.5 83.5t83.5 33.5q51 0 85 -33.5t34 -83.5t-34 -83.5t-85 -33.5q-50 0 -83.5 33.5t-33.5 83.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1343" d="M41 651v144h188v616h295q82 0 153 -7.5t144.5 -28.5t132.5 -54t112 -87.5t88.5 -126t56 -171.5t20.5 -223q0 -120 -21 -220.5t-57 -172.5t-87.5 -128.5t-109 -92t-125 -58.5t-131.5 -32t-133 -9h-338v651h-188zM424 154h158q72 0 131.5 13t119 50.5t100 97.5t66 161.5 t25.5 236.5q0 100 -14 180t-37 136t-58.5 97.5t-73 65.5t-86 38.5t-92 19.5t-96.5 5h-143v-460h321v-144h-321v-497z" />
<glyph unicode="&#xd1;" horiz-adv-x="1398" d="M205 0v1411h254l579 -1200q-6 53 -16 186.5t-10 243.5v770h182v-1411h-260l-574 1198q27 -316 27 -551v-647h-182zM381 1663q85 180 223 180q39 0 75 -15.5t58.5 -33.5t51 -33.5t53.5 -15.5q30 0 54.5 21.5t53.5 68.5l115 -57q-80 -176 -223 -176q-46 0 -90 24.5t-81 49 t-67 24.5q-33 0 -57 -22.5t-51 -71.5z" />
<glyph unicode="&#xd2;" horiz-adv-x="1417" d="M113 702q0 343 163.5 538.5t432.5 195.5q272 0 434 -191t162 -540q0 -344 -162.5 -537t-433.5 -193t-433.5 190t-162.5 537zM319 702q0 -293 104 -430t286 -137q389 0 389 570q0 571 -389 571q-185 0 -287.5 -139.5t-102.5 -434.5zM442 1708l80 158l412 -219l-49 -101z " />
<glyph unicode="&#xd3;" horiz-adv-x="1417" d="M113 702q0 343 163.5 538.5t432.5 195.5q272 0 434 -191t162 -540q0 -344 -162.5 -537t-433.5 -193t-433.5 190t-162.5 537zM319 702q0 -293 104 -430t286 -137q389 0 389 570q0 571 -389 571q-185 0 -287.5 -139.5t-102.5 -434.5zM463 1647l409 219l82 -158l-442 -162z " />
<glyph unicode="&#xd4;" horiz-adv-x="1417" d="M113 702q0 343 163.5 538.5t432.5 195.5q272 0 434 -191t162 -540q0 -344 -162.5 -537t-433.5 -193t-433.5 190t-162.5 537zM319 702q0 -293 104 -430t286 -137q389 0 389 570q0 571 -389 571q-185 0 -287.5 -139.5t-102.5 -434.5zM367 1647l278 225h125l277 -225 l-80 -93l-260 166l-258 -166z" />
<glyph unicode="&#xd5;" horiz-adv-x="1417" d="M113 702q0 343 163.5 538.5t432.5 195.5q272 0 434 -191t162 -540q0 -344 -162.5 -537t-433.5 -193t-433.5 190t-162.5 537zM319 702q0 -293 104 -430t286 -137q389 0 389 570q0 571 -389 571q-185 0 -287.5 -139.5t-102.5 -434.5zM367 1663q85 180 223 180 q49 0 92 -24.5t78.5 -49t66.5 -24.5q30 0 55 21.5t54 68.5l115 -57q-80 -176 -224 -176q-37 0 -72.5 15.5t-59 33.5t-52.5 33.5t-53 15.5q-34 0 -58.5 -22.5t-50.5 -71.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1417" d="M113 702q0 343 163.5 538.5t432.5 195.5q272 0 434 -191t162 -540q0 -344 -162.5 -537t-433.5 -193t-433.5 190t-162.5 537zM319 702q0 -293 104 -430t286 -137q389 0 389 570q0 571 -389 571q-185 0 -287.5 -139.5t-102.5 -434.5zM375 1714q0 50 34 83.5t85 33.5 q49 0 82.5 -33.5t33.5 -83.5t-33.5 -83.5t-82.5 -33.5q-51 0 -85 33.5t-34 83.5zM805 1714q0 50 33.5 83.5t83.5 33.5q51 0 84.5 -33.5t33.5 -83.5t-33.5 -83.5t-84.5 -33.5q-50 0 -83.5 33.5t-33.5 83.5z" />
<glyph unicode="&#xd7;" d="M147 379l246 246l-246 254l115 116l246 -256l254 256l115 -116l-246 -246l246 -254l-115 -115l-246 254l-254 -254z" />
<glyph unicode="&#xd8;" horiz-adv-x="1417" d="M113 702q0 343 163.5 538.5t432.5 195.5q71 0 135 -15l76 256l161 -43l-86 -268q148 -80 229 -248t81 -413q0 -344 -162.5 -537t-433.5 -193q-71 0 -138 15l-71 -250l-162 43l82 262q-146 81 -226.5 248.5t-80.5 408.5zM319 702q0 -377 168 -503l324 1067q-55 10 -102 10 q-185 0 -287.5 -139.5t-102.5 -434.5zM606 145q52 -10 103 -10q389 0 389 570q0 391 -172 512z" />
<glyph unicode="&#xd9;" horiz-adv-x="1355" d="M184 453v958h195v-944q0 -328 297 -328q152 0 225.5 81.5t73.5 246.5v944h196v-958q0 -215 -131 -346.5t-364 -131.5q-236 0 -364 130.5t-128 347.5zM412 1708l80 158l411 -219l-49 -101z" />
<glyph unicode="&#xda;" horiz-adv-x="1355" d="M184 453v958h195v-944q0 -328 297 -328q152 0 225.5 81.5t73.5 246.5v944h196v-958q0 -215 -131 -346.5t-364 -131.5q-236 0 -364 130.5t-128 347.5zM432 1647l410 219l82 -158l-443 -162z" />
<glyph unicode="&#xdb;" horiz-adv-x="1355" d="M184 453v958h195v-944q0 -328 297 -328q152 0 225.5 81.5t73.5 246.5v944h196v-958q0 -215 -131 -346.5t-364 -131.5q-236 0 -364 130.5t-128 347.5zM336 1647l278 225h125l277 -225l-80 -93l-260 166l-258 -166z" />
<glyph unicode="&#xdc;" horiz-adv-x="1355" d="M184 453v958h195v-944q0 -328 297 -328q152 0 225.5 81.5t73.5 246.5v944h196v-958q0 -215 -131 -346.5t-364 -131.5q-236 0 -364 130.5t-128 347.5zM344 1714q0 50 34 83.5t85 33.5q49 0 83 -33.5t34 -83.5t-34 -83.5t-83 -33.5q-51 0 -85 33.5t-34 83.5zM774 1714 q0 50 34 83.5t83 33.5q51 0 85 -33.5t34 -83.5t-34 -83.5t-85 -33.5q-49 0 -83 33.5t-34 83.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1126" d="M10 1411h215l344 -698l344 698h203l-454 -868v-543h-197v541zM317 1647l410 219l82 -158l-442 -162z" />
<glyph unicode="&#xde;" horiz-adv-x="1189" d="M205 0v1411h194v-242h176q262 0 405.5 -110.5t143.5 -333.5q0 -121 -41 -212t-116 -145.5t-172 -81t-217 -26.5h-179v-260h-194zM399 414h170q83 0 143.5 14t108.5 47.5t72.5 95t24.5 152.5q0 157 -89.5 226t-255.5 69h-174v-604z" />
<glyph unicode="&#xdf;" horiz-adv-x="1214" d="M195 0v1104q0 205 107.5 318.5t303.5 113.5q168 0 268.5 -83.5t100.5 -209.5q0 -61 -23.5 -109.5t-56.5 -79t-66.5 -57t-57 -57t-23.5 -66.5q0 -34 21 -65t55 -56t75 -52t82.5 -61t75.5 -74.5t55 -101t21 -132.5q0 -107 -52 -189.5t-134.5 -125t-178.5 -42.5 q-121 0 -209 48l55 131q56 -27 136 -27q86 0 138 53t52 154q0 55 -20.5 100.5t-53 78t-72.5 62t-79.5 58t-72 59.5t-53 74t-20.5 94q0 54 23.5 96t56.5 69t66 53.5t56.5 65.5t23.5 89q0 75 -54.5 115.5t-136.5 40.5q-221 0 -221 -285v-1104h-188z" />
<glyph unicode="&#xe0;" horiz-adv-x="1114" d="M92 301q0 172 124 264t351 92h166v80q0 114 -55 163.5t-170 49.5q-120 0 -291 -57l-47 137q201 74 373 74q191 0 285 -93t94 -266v-493q0 -66 22.5 -97.5t67.5 -48.5l-43 -131q-84 9 -135.5 47t-75.5 115q-109 -162 -322 -162q-159 0 -251.5 89.5t-92.5 236.5zM293 311 q0 -194 186 -194q163 0 254 168v247h-141q-299 0 -299 -221zM309 1483l84 157l408 -233l-58 -100z" />
<glyph unicode="&#xe1;" horiz-adv-x="1114" d="M92 301q0 172 124 264t351 92h166v80q0 114 -55 163.5t-170 49.5q-120 0 -291 -57l-47 137q201 74 373 74q191 0 285 -93t94 -266v-493q0 -66 22.5 -97.5t67.5 -48.5l-43 -131q-84 9 -135.5 47t-75.5 115q-109 -162 -322 -162q-159 0 -251.5 89.5t-92.5 236.5zM289 1407 l407 233l84 -157l-434 -176zM293 311q0 -194 186 -194q163 0 254 168v247h-141q-299 0 -299 -221z" />
<glyph unicode="&#xe2;" horiz-adv-x="1114" d="M92 301q0 172 124 264t351 92h166v80q0 114 -55 163.5t-170 49.5q-120 0 -291 -57l-47 137q201 74 373 74q191 0 285 -93t94 -266v-493q0 -66 22.5 -97.5t67.5 -48.5l-43 -131q-84 9 -135.5 47t-75.5 115q-109 -162 -322 -162q-159 0 -251.5 89.5t-92.5 236.5zM195 1391 l276 231h125l278 -231l-81 -93l-258 172l-261 -172zM293 311q0 -194 186 -194q163 0 254 168v247h-141q-299 0 -299 -221z" />
<glyph unicode="&#xe3;" horiz-adv-x="1114" d="M92 301q0 172 124 264t351 92h166v80q0 114 -55 163.5t-170 49.5q-120 0 -291 -57l-47 137q201 74 373 74q191 0 285 -93t94 -266v-493q0 -66 22.5 -97.5t67.5 -48.5l-43 -131q-84 9 -135.5 47t-75.5 115q-109 -162 -322 -162q-159 0 -251.5 89.5t-92.5 236.5zM193 1405 q85 180 223 180q49 0 92 -24.5t78.5 -49t66.5 -24.5q30 0 55 21.5t54 68.5l115 -57q-80 -177 -224 -177q-37 0 -72.5 15.5t-59 34t-52.5 34t-53 15.5q-34 0 -58.5 -22.5t-50.5 -71.5zM293 311q0 -194 186 -194q163 0 254 168v247h-141q-299 0 -299 -221z" />
<glyph unicode="&#xe4;" horiz-adv-x="1114" d="M92 301q0 172 124 264t351 92h166v80q0 114 -55 163.5t-170 49.5q-120 0 -291 -57l-47 137q201 74 373 74q191 0 285 -93t94 -266v-493q0 -66 22.5 -97.5t67.5 -48.5l-43 -131q-84 9 -135.5 47t-75.5 115q-109 -162 -322 -162q-159 0 -251.5 89.5t-92.5 236.5zM201 1456 q0 50 33.5 83.5t84.5 33.5q49 0 83 -33.5t34 -83.5t-34 -83.5t-83 -33.5q-51 0 -84.5 33.5t-33.5 83.5zM293 311q0 -194 186 -194q163 0 254 168v247h-141q-299 0 -299 -221zM631 1456q0 50 33.5 83.5t83.5 33.5q51 0 84.5 -33.5t33.5 -83.5t-33.5 -83.5t-84.5 -33.5 q-50 0 -83.5 33.5t-33.5 83.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1114" d="M92 301q0 172 124 264t351 92h166v80q0 114 -55 163.5t-170 49.5q-120 0 -291 -57l-47 137q201 74 373 74q191 0 285 -93t94 -266v-493q0 -66 22.5 -97.5t67.5 -48.5l-43 -131q-84 9 -135.5 47t-75.5 115q-109 -162 -322 -162q-159 0 -251.5 89.5t-92.5 236.5zM293 311 q0 -194 186 -194q163 0 254 168v247h-141q-299 0 -299 -221zM301 1495q0 92 66 154.5t168 62.5t167.5 -62.5t65.5 -154.5t-65.5 -154.5t-167.5 -62.5t-168 62.5t-66 154.5zM426 1495q0 -54 29 -84.5t80 -30.5q50 0 79 30.5t29 84.5t-28.5 84.5t-79.5 30.5t-80 -30.5 t-29 -84.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1738" d="M92 301q0 172 126.5 264t354.5 92h166v80q0 114 -55 163.5t-170 49.5q-120 0 -291 -57l-47 137q201 74 373 74q233 0 321 -172q117 172 326 172q210 0 323 -141.5t113 -391.5q0 -46 -4 -96h-688q13 -179 90 -262.5t201 -83.5q79 0 144 22t136 72l82 -112 q-172 -136 -376 -136q-129 0 -228.5 51.5t-161.5 147.5q-73 -107 -165.5 -153t-219.5 -46q-161 0 -255.5 90t-94.5 236zM299 311q0 -194 186 -194q87 0 155 45t130 143q-31 102 -31 221v6h-141q-299 0 -299 -221zM940 614h506v13q0 160 -64 243.5t-190 83.5 q-232 0 -252 -340z" />
<glyph unicode="&#xe7;" horiz-adv-x="978" d="M117 530q0 260 126.5 417t344.5 157q191 0 330 -117l-91 -119q-112 80 -231 80q-131 0 -204 -103.5t-73 -309.5q0 -400 277 -400q64 0 116 19.5t119 62.5l87 -123q-129 -105 -287 -117v-92q98 -8 144 -56.5t46 -117.5q0 -97 -72 -151t-182 -54q-120 0 -186 50l51 106 q64 -33 129 -33q46 0 74.5 20t28.5 62q0 86 -183 86l23 185q-183 28 -285 171.5t-102 376.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1116" d="M117 528q0 256 121 416t327 160q215 0 330 -141t115 -392q0 -46 -4 -96h-689q13 -179 90 -262.5t201 -83.5q79 0 144.5 22t136.5 72l82 -112q-172 -136 -377 -136q-225 0 -351 147.5t-126 405.5zM317 614h506v13q0 160 -64 243.5t-190 83.5q-232 0 -252 -340zM346 1483 l84 157l408 -233l-58 -100z" />
<glyph unicode="&#xe9;" horiz-adv-x="1116" d="M117 528q0 256 121 416t327 160q215 0 330 -141t115 -392q0 -46 -4 -96h-689q13 -179 90 -262.5t201 -83.5q79 0 144.5 22t136.5 72l82 -112q-172 -136 -377 -136q-225 0 -351 147.5t-126 405.5zM317 614h506v13q0 160 -64 243.5t-190 83.5q-232 0 -252 -340zM326 1407 l407 233l84 -157l-434 -176z" />
<glyph unicode="&#xea;" horiz-adv-x="1116" d="M117 528q0 256 121 416t327 160q215 0 330 -141t115 -392q0 -46 -4 -96h-689q13 -179 90 -262.5t201 -83.5q79 0 144.5 22t136.5 72l82 -112q-172 -136 -377 -136q-225 0 -351 147.5t-126 405.5zM231 1391l277 231h125l278 -231l-82 -93l-258 172l-260 -172zM317 614h506 v13q0 160 -64 243.5t-190 83.5q-232 0 -252 -340z" />
<glyph unicode="&#xeb;" horiz-adv-x="1116" d="M117 528q0 256 121 416t327 160q215 0 330 -141t115 -392q0 -46 -4 -96h-689q13 -179 90 -262.5t201 -83.5q79 0 144.5 22t136.5 72l82 -112q-172 -136 -377 -136q-225 0 -351 147.5t-126 405.5zM238 1456q0 50 33.5 83.5t84.5 33.5q50 0 83.5 -33.5t33.5 -83.5 t-33.5 -83.5t-83.5 -33.5q-51 0 -84.5 33.5t-33.5 83.5zM317 614h506v13q0 160 -64 243.5t-190 83.5q-232 0 -252 -340zM668 1456q0 50 33.5 83.5t82.5 33.5q51 0 85 -33.5t34 -83.5t-34 -83.5t-85 -33.5q-49 0 -82.5 33.5t-33.5 83.5z" />
<glyph unicode="&#xec;" horiz-adv-x="577" d="M63 1483l84 157l408 -233l-57 -100zM195 0v1079h188v-1079h-188z" />
<glyph unicode="&#xed;" horiz-adv-x="577" d="M43 1407l408 233l84 -157l-435 -176zM195 0v1079h188v-1079h-188z" />
<glyph unicode="&#xee;" horiz-adv-x="577" d="M-51 1391l276 231h125l279 -231l-82 -93l-258 172l-260 -172zM195 0v1079h188v-1079h-188z" />
<glyph unicode="&#xef;" horiz-adv-x="577" d="M-45 1456q0 50 34 83.5t85 33.5q49 0 82.5 -33.5t33.5 -83.5t-33.5 -83.5t-82.5 -33.5q-51 0 -85 33.5t-34 83.5zM195 0v1079h188v-1079h-188zM385 1456q0 50 33.5 83.5t83.5 33.5q51 0 85 -33.5t34 -83.5t-34 -83.5t-85 -33.5q-50 0 -83.5 33.5t-33.5 83.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1167" d="M117 481q0 93 25.5 179t74 157t129.5 113.5t182 42.5q186 0 301 -137q-25 129 -83 227.5t-160 179.5l-148 -143l-129 65l152 154q-110 51 -232 74l41 143q177 -35 305 -98l144 151l110 -96l-129 -133q185 -141 262.5 -336t77.5 -481q0 -257 -130.5 -412.5t-340.5 -155.5 q-127 0 -228.5 58t-162.5 174.5t-61 273.5zM309 489q0 -176 69.5 -270t188.5 -94q128 0 205.5 109t77.5 311q0 43 -4 121q-107 159 -289 159q-248 0 -248 -336z" />
<glyph unicode="&#xf1;" horiz-adv-x="1200" d="M195 0v1079h161l17 -159q57 86 141.5 135t187.5 49q149 0 231.5 -87.5t82.5 -242.5v-774h-189v748q0 115 -43 161.5t-127 46.5q-86 0 -151 -48.5t-123 -141.5v-766h-188zM268 1405q38 81 95 130.5t129 49.5q49 0 92 -24.5t78.5 -49t66.5 -24.5q30 0 55 21.5t54 68.5 l114 -57q-80 -177 -223 -177q-37 0 -72.5 15.5t-59 34t-52.5 34t-53 15.5q-34 0 -58.5 -22.5t-50.5 -71.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="1196" d="M117 539q0 255 128.5 410t354.5 155q227 0 353 -151.5t126 -411.5q0 -255 -128 -410.5t-353 -155.5q-228 0 -354.5 152t-126.5 412zM319 539q0 -412 279 -412t279 414q0 411 -277 411q-281 0 -281 -413zM373 1483l84 157l407 -233l-57 -100z" />
<glyph unicode="&#xf3;" horiz-adv-x="1196" d="M117 539q0 255 128.5 410t354.5 155q227 0 353 -151.5t126 -411.5q0 -255 -128 -410.5t-353 -155.5q-228 0 -354.5 152t-126.5 412zM319 539q0 -412 279 -412t279 414q0 411 -277 411q-281 0 -281 -413zM352 1407l408 233l84 -157l-434 -176z" />
<glyph unicode="&#xf4;" horiz-adv-x="1196" d="M117 539q0 255 128.5 410t354.5 155q227 0 353 -151.5t126 -411.5q0 -255 -128 -410.5t-353 -155.5q-228 0 -354.5 152t-126.5 412zM258 1391l277 231h124l279 -231l-82 -93l-258 172l-260 -172zM319 539q0 -412 279 -412t279 414q0 411 -277 411q-281 0 -281 -413z" />
<glyph unicode="&#xf5;" horiz-adv-x="1196" d="M117 539q0 255 128.5 410t354.5 155q227 0 353 -151.5t126 -411.5q0 -255 -128 -410.5t-353 -155.5q-228 0 -354.5 152t-126.5 412zM256 1405q85 180 223 180q39 0 75 -15.5t58.5 -33.5t51 -33.5t53.5 -15.5q30 0 54.5 21.5t53.5 68.5l115 -57q-80 -177 -223 -177 q-37 0 -73 15.5t-59.5 34t-52.5 34t-53 15.5q-33 0 -57 -22.5t-51 -71.5zM319 539q0 -412 279 -412t279 414q0 411 -277 411q-281 0 -281 -413z" />
<glyph unicode="&#xf6;" horiz-adv-x="1196" d="M117 539q0 255 128.5 410t354.5 155q227 0 353 -151.5t126 -411.5q0 -255 -128 -410.5t-353 -155.5q-228 0 -354.5 152t-126.5 412zM264 1456q0 50 34 83.5t85 33.5q49 0 83 -33.5t34 -83.5t-34 -83.5t-83 -33.5q-51 0 -85 33.5t-34 83.5zM319 539q0 -412 279 -412 t279 414q0 411 -277 411q-281 0 -281 -413zM694 1456q0 50 34 83.5t83 33.5q51 0 85 -33.5t34 -83.5t-34 -83.5t-85 -33.5q-49 0 -83 33.5t-34 83.5z" />
<glyph unicode="&#xf7;" d="M127 602v158h770v-158h-770zM371 215q0 59 40.5 100t100.5 41q61 0 102 -41t41 -100q0 -60 -41 -101.5t-102 -41.5q-60 0 -100.5 41.5t-40.5 101.5zM371 1151q0 59 40.5 100t100.5 41q61 0 102 -41t41 -100q0 -60 -41 -101.5t-102 -41.5q-60 0 -100.5 41.5t-40.5 101.5z " />
<glyph unicode="&#xf8;" horiz-adv-x="1196" d="M117 539q0 255 128.5 410t354.5 155q50 0 105 -10l79 249l154 -45l-86 -258q110 -67 168.5 -195.5t58.5 -303.5q0 -255 -128 -410.5t-353 -155.5q-44 0 -104 11l-80 -252l-154 45l86 260q-110 68 -169.5 197t-59.5 303zM319 539q0 -264 99 -357l254 764q-34 6 -72 6 q-281 0 -281 -413zM528 133q30 -6 70 -6q279 0 279 414q0 263 -97 354z" />
<glyph unicode="&#xf9;" horiz-adv-x="1191" d="M184 305v774h189v-753q0 -109 39 -156t127 -47q157 0 270 186v770h188v-1079h-161l-15 168q-62 -100 -142 -146.5t-194 -46.5q-142 0 -221.5 86.5t-79.5 243.5zM367 1483l84 157l407 -233l-57 -100z" />
<glyph unicode="&#xfa;" horiz-adv-x="1191" d="M184 305v774h189v-753q0 -109 39 -156t127 -47q157 0 270 186v770h188v-1079h-161l-15 168q-62 -100 -142 -146.5t-194 -46.5q-142 0 -221.5 86.5t-79.5 243.5zM346 1407l408 233l84 -157l-435 -176z" />
<glyph unicode="&#xfb;" horiz-adv-x="1191" d="M184 305v774h189v-753q0 -109 39 -156t127 -47q157 0 270 186v770h188v-1079h-161l-15 168q-62 -100 -142 -146.5t-194 -46.5q-142 0 -221.5 86.5t-79.5 243.5zM252 1391l276 231h125l279 -231l-82 -93l-258 172l-260 -172z" />
<glyph unicode="&#xfc;" horiz-adv-x="1191" d="M184 305v774h189v-753q0 -109 39 -156t127 -47q157 0 270 186v770h188v-1079h-161l-15 168q-62 -100 -142 -146.5t-194 -46.5q-142 0 -221.5 86.5t-79.5 243.5zM258 1456q0 50 34 83.5t85 33.5q49 0 83 -33.5t34 -83.5t-34 -83.5t-83 -33.5q-51 0 -85 33.5t-34 83.5z M688 1456q0 50 34 83.5t83 33.5q51 0 85 -33.5t34 -83.5t-34 -83.5t-85 -33.5q-49 0 -83 33.5t-34 83.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1007" d="M20 1079h201l289 -942l283 942h194l-360 -1085q-30 -91 -69 -160t-97.5 -127.5t-142.5 -94.5t-193 -48l-21 147q81 14 136.5 36.5t94 60t62.5 80.5t49 112h-63zM258 1407l408 233l84 -157l-435 -176z" />
<glyph unicode="&#xfe;" horiz-adv-x="1216" d="M195 -436v1949l188 23v-588q58 76 137.5 116t169.5 40q207 0 303.5 -148t96.5 -415q0 -254 -110.5 -410t-313.5 -156q-181 0 -283 123v-504zM383 260q86 -131 238 -131q266 0 266 412q0 413 -244 413q-149 0 -260 -168v-526z" />
<glyph unicode="&#xff;" horiz-adv-x="1007" d="M20 1079h201l289 -942l283 942h194l-360 -1085q-30 -91 -69 -160t-97.5 -127.5t-142.5 -94.5t-193 -48l-21 147q81 14 136.5 36.5t94 60t62.5 80.5t49 112h-63zM170 1456q0 50 34 83.5t85 33.5q49 0 83 -33.5t34 -83.5t-34 -83.5t-83 -33.5q-51 0 -85 33.5t-34 83.5z M600 1456q0 50 34 83.5t83 33.5q51 0 85 -33.5t34 -83.5t-34 -83.5t-85 -33.5q-49 0 -83 33.5t-34 83.5z" />
<glyph unicode="&#x152;" horiz-adv-x="1837" d="M113 702q0 171 43 310.5t119 231.5t180.5 142t226.5 50q78 0 158 -25h870l-22 -156h-611q162 -162 183 -460h356v-156h-354q-19 -312 -199 -483h663v-156h-895q-73 -25 -149 -25q-123 0 -226.5 47.5t-180 138t-119.5 229t-43 312.5zM319 702q0 -149 28 -260t78.5 -177 t116 -98t146.5 -32q172 0 270.5 138t98.5 432q0 293 -97.5 432t-271.5 139q-170 0 -269.5 -140.5t-99.5 -433.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1875" d="M117 539q0 255 128.5 410t354.5 155q259 0 379 -217q122 217 356 217q209 0 321.5 -141.5t112.5 -391.5q0 -46 -4 -96h-688q13 -179 90 -262.5t201 -83.5q79 0 144.5 22t136.5 72l82 -112q-172 -136 -377 -136q-255 0 -379 224q-128 -224 -377 -224q-228 0 -354.5 152 t-126.5 412zM319 539q0 -412 279 -412t279 414q0 411 -277 411q-281 0 -281 -413zM1077 614h506v13q0 159 -62 243t-182 84q-242 0 -262 -340z" />
<glyph unicode="&#x178;" horiz-adv-x="1126" d="M10 1411h215l344 -698l344 698h203l-454 -868v-543h-197v541zM229 1714q0 50 34 83.5t85 33.5q50 0 83.5 -33.5t33.5 -83.5t-33.5 -83.5t-83.5 -33.5q-51 0 -85 33.5t-34 83.5zM659 1714q0 50 33.5 83.5t83.5 33.5q51 0 85 -33.5t34 -83.5t-34 -83.5t-85 -33.5 q-50 0 -83.5 33.5t-33.5 83.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="802" d="M61 1391l277 231h125l278 -231l-82 -93l-258 172l-260 -172z" />
<glyph unicode="&#x2dc;" horiz-adv-x="806" d="M61 1405q38 81 95 130.5t129 49.5q49 0 92 -24.5t78.5 -49t66.5 -24.5q30 0 55 21.5t54 68.5l114 -57q-80 -177 -223 -177q-37 0 -72.5 15.5t-59 34t-52.5 34t-53 15.5q-34 0 -58.5 -22.5t-50.5 -71.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="985" />
<glyph unicode="&#x2001;" horiz-adv-x="1970" />
<glyph unicode="&#x2002;" horiz-adv-x="985" />
<glyph unicode="&#x2003;" horiz-adv-x="1970" />
<glyph unicode="&#x2004;" horiz-adv-x="656" />
<glyph unicode="&#x2005;" horiz-adv-x="492" />
<glyph unicode="&#x2006;" horiz-adv-x="328" />
<glyph unicode="&#x2007;" horiz-adv-x="328" />
<glyph unicode="&#x2008;" horiz-adv-x="246" />
<glyph unicode="&#x2009;" horiz-adv-x="394" />
<glyph unicode="&#x200a;" horiz-adv-x="109" />
<glyph unicode="&#x2010;" horiz-adv-x="825" d="M123 561v160h579v-160h-579z" />
<glyph unicode="&#x2011;" horiz-adv-x="825" d="M123 561v160h579v-160h-579z" />
<glyph unicode="&#x2012;" horiz-adv-x="825" d="M123 561v160h579v-160h-579z" />
<glyph unicode="&#x2013;" horiz-adv-x="1064" d="M66 561v160h933v-160h-933z" />
<glyph unicode="&#x2014;" horiz-adv-x="1617" d="M66 561v160h1486v-160h-1486z" />
<glyph unicode="&#x2018;" horiz-adv-x="466" d="M90 1133q0 44 35 118l137 291h123l-82 -307q53 -41 53 -102q0 -54 -39.5 -91.5t-93.5 -37.5q-56 0 -94.5 37.5t-38.5 91.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="466" d="M82 1004l82 307q-53 41 -53 102q0 54 39.5 91.5t93.5 37.5q56 0 94.5 -37.5t38.5 -91.5q0 -45 -35 -119l-137 -290h-123z" />
<glyph unicode="&#x201a;" horiz-adv-x="466" d="M82 -319l82 307q-53 41 -53 102q0 54 39.5 91.5t93.5 37.5q56 0 94.5 -37.5t38.5 -91.5q0 -45 -35 -119l-137 -290h-123z" />
<glyph unicode="&#x201c;" horiz-adv-x="831" d="M90 1133q0 44 35 118l137 291h123l-82 -307q53 -41 53 -102q0 -54 -39.5 -91.5t-93.5 -37.5q-56 0 -94.5 37.5t-38.5 91.5zM455 1133q0 46 34 118l138 291h123l-82 -307q53 -41 53 -102q0 -54 -39.5 -91.5t-93.5 -37.5q-56 0 -94.5 37.5t-38.5 91.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="831" d="M82 1004l82 307q-53 41 -53 102q0 54 39.5 91.5t93.5 37.5q56 0 94.5 -37.5t38.5 -91.5q0 -45 -35 -119l-137 -290h-123zM446 1004l82 307q-53 41 -53 102q0 54 39.5 91.5t93.5 37.5q56 0 94.5 -37.5t38.5 -91.5q0 -47 -34 -119l-138 -290h-123z" />
<glyph unicode="&#x201e;" horiz-adv-x="831" d="M82 -319l82 307q-53 41 -53 102q0 54 39.5 91.5t93.5 37.5q56 0 94.5 -37.5t38.5 -91.5q0 -45 -35 -119l-137 -290h-123zM446 -319l82 307q-53 41 -53 102q0 54 39.5 91.5t93.5 37.5q56 0 94.5 -37.5t38.5 -91.5q0 -47 -34 -119l-138 -290h-123z" />
<glyph unicode="&#x2022;" horiz-adv-x="663" d="M102 700q0 98 66 164t164 66t163.5 -66.5t65.5 -165.5q0 -98 -65.5 -163.5t-163.5 -65.5t-164 66.5t-66 164.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1511" d="M102 119q0 59 41 100t101 41q61 0 102 -41t41 -100q0 -60 -41 -102t-102 -42q-60 0 -101 42t-41 102zM612 119q0 59 41 100t101 41q61 0 102 -41t41 -100q0 -60 -41 -102t-102 -42q-60 0 -101 42t-41 102zM1122 119q0 59 41 100t101 41q61 0 102 -41t41 -100 q0 -60 -41 -102t-102 -42q-60 0 -101 42t-41 102z" />
<glyph unicode="&#x202f;" horiz-adv-x="394" />
<glyph unicode="&#x2039;" horiz-adv-x="696" d="M113 522v129l358 445l113 -78l-277 -430l277 -430l-113 -78z" />
<glyph unicode="&#x203a;" horiz-adv-x="696" d="M113 158l276 430l-276 430l112 78l359 -445v-129l-359 -442z" />
<glyph unicode="&#x205f;" horiz-adv-x="492" />
<glyph unicode="&#x20ac;" horiz-adv-x="1179" d="M51 473l31 125h143v184h-174l31 127h151q49 230 189 356t334 126q201 0 372 -115l-73 -129q-77 47 -142.5 67.5t-148.5 20.5q-253 0 -326 -326h523l-31 -127h-504v-184h465l-29 -125h-430q26 -179 108.5 -259.5t235.5 -80.5q80 0 146.5 22t140.5 62v-164 q-159 -78 -315 -78q-212 0 -344.5 127.5t-172.5 370.5h-180z" />
<glyph unicode="&#x2122;" horiz-adv-x="1806" d="M72 1280v131h690l-25 -131h-248v-670h-157v670h-260zM807 610l33 801h215l186 -559l170 559h221l31 -801h-154l-18 432q-5 150 4 234h-2l-184 -582h-138l-202 578h-2q8 -90 4 -230l-17 -432h-147z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1075" d="M0 0v1075h1075v-1075h-1075z" />
<glyph unicode="&#xfb03;" horiz-adv-x="1761" d="M20 934v145h173v127q0 140 91 225t265 85q85 0 154 -16t157 -56q98 92 289 92q154 0 299 -74l-66 -131q-105 51 -227 51q-108 0 -149 -38.5t-41 -120.5v-144h602v-1079h-189v934h-413v-934h-189v934h-395v-934h-188v934h-173zM381 1079h395v142q0 53 12 100 q-114 43 -227 43q-100 0 -140 -37.5t-40 -118.5v-129z" />
<glyph unicode="&#xfb04;" horiz-adv-x="1796" d="M20 934v145h173v127q0 140 91 225t265 85q85 0 154 -16t157 -56q95 92 273 92q121 0 249 -41l187 25v-1303q0 -44 15.5 -65t51.5 -21q40 0 70 12l49 -131q-75 -37 -160 -37q-103 0 -159 61t-56 177v1137q-112 34 -235 34q-100 0 -140 -37t-40 -118v-150h262l-21 -145 h-241v-934h-189v934h-395v-934h-188v934h-173zM381 1079h395v148q0 51 12 94q-114 43 -227 43q-100 0 -140 -37.5t-40 -118.5v-129z" />
<hkern u1="C" u2="&#x2d;" k="123" />
<hkern u1="Q" u2="&#x7d;" k="-27" />
<hkern u1="Q" u2="]" k="-27" />
<hkern u1="Q" u2="&#x29;" k="-8" />
<hkern u1="_" u2="y" k="20" />
<hkern u1="f" u2="&#xf0;" k="4" />
<hkern u1="&#xc7;" u2="&#x2d;" k="123" />
<hkern u1="&#xde;" u2="&#xf0;" k="31" />
<hkern g1="ampersand" 	g2="asterisk,registered" 	k="82" />
<hkern g1="ampersand" 	g2="at" 	k="41" />
<hkern g1="ampersand" 	g2="backslash" 	k="205" />
<hkern g1="ampersand" 	g2="bullet" 	k="41" />
<hkern g1="ampersand" 	g2="four" 	k="41" />
<hkern g1="ampersand" 	g2="nine" 	k="41" />
<hkern g1="ampersand" 	g2="one" 	k="141" />
<hkern g1="ampersand" 	g2="percent" 	k="82" />
<hkern g1="ampersand" 	g2="question" 	k="164" />
<hkern g1="ampersand" 	g2="seven" 	k="61" />
<hkern g1="ampersand" 	g2="slash" 	k="82" />
<hkern g1="ampersand" 	g2="trademark" 	k="123" />
<hkern g1="ampersand" 	g2="yen" 	k="102" />
<hkern g1="at" 	g2="asterisk,registered" 	k="61" />
<hkern g1="at" 	g2="backslash" 	k="102" />
<hkern g1="at" 	g2="nine" 	k="20" />
<hkern g1="at" 	g2="one" 	k="61" />
<hkern g1="at" 	g2="question" 	k="82" />
<hkern g1="at" 	g2="seven" 	k="61" />
<hkern g1="at" 	g2="trademark" 	k="61" />
<hkern g1="at" 	g2="yen" 	k="61" />
<hkern g1="at" 	g2="three" 	k="41" />
<hkern g1="at" 	g2="two" 	k="41" />
<hkern g1="at" 	g2="underscore" 	k="102" />
<hkern g1="at" 	g2="ampersand" 	k="41" />
<hkern g1="at" 	g2="guillemotright,guilsinglright" 	k="61" />
<hkern g1="copyright" 	g2="slash" 	k="102" />
<hkern g1="copyright" 	g2="underscore" 	k="205" />
<hkern g1="degree" 	g2="asterisk,registered" 	k="143" />
<hkern g1="degree" 	g2="backslash" 	k="184" />
<hkern g1="degree" 	g2="question" 	k="205" />
<hkern g1="degree" 	g2="slash" 	k="348" />
<hkern g1="degree" 	g2="underscore" 	k="205" />
<hkern g1="degree" 	g2="exclam" 	k="143" />
<hkern g1="less,equal,greater,multiply" 	g2="nine" 	k="20" />
<hkern g1="less,equal,greater,multiply" 	g2="one" 	k="41" />
<hkern g1="less,equal,greater,multiply" 	g2="question" 	k="82" />
<hkern g1="less,equal,greater,multiply" 	g2="yen" 	k="41" />
<hkern g1="less,equal,greater,multiply" 	g2="three" 	k="72" />
<hkern g1="Euro" 	g2="asterisk,registered" 	k="-20" />
<hkern g1="Euro" 	g2="at" 	k="41" />
<hkern g1="Euro" 	g2="bullet" 	k="82" />
<hkern g1="Euro" 	g2="four" 	k="72" />
<hkern g1="Euro" 	g2="nine" 	k="35" />
<hkern g1="Euro" 	g2="one" 	k="61" />
<hkern g1="Euro" 	g2="eight" 	k="31" />
<hkern g1="Euro" 	g2="Euro" 	k="41" />
<hkern g1="Euro" 	g2="two" 	k="20" />
<hkern g1="Euro" 	g2="ampersand" 	k="61" />
<hkern g1="Euro" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="Euro" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="Euro" 	g2="less,equal,greater,multiply" 	k="61" />
<hkern g1="Euro" 	g2="six" 	k="41" />
<hkern g1="Euro" 	g2="zero" 	k="41" />
<hkern g1="percent" 	g2="asterisk,registered" 	k="102" />
<hkern g1="percent" 	g2="backslash" 	k="123" />
<hkern g1="percent" 	g2="one" 	k="123" />
<hkern g1="percent" 	g2="question" 	k="123" />
<hkern g1="percent" 	g2="trademark" 	k="123" />
<hkern g1="percent" 	g2="yen" 	k="61" />
<hkern g1="section" 	g2="four" 	k="41" />
<hkern g1="section" 	g2="one" 	k="82" />
<hkern g1="section" 	g2="seven" 	k="61" />
<hkern g1="section" 	g2="two" 	k="20" />
<hkern g1="sterling" 	g2="at" 	k="41" />
<hkern g1="sterling" 	g2="backslash" 	k="102" />
<hkern g1="sterling" 	g2="bullet" 	k="82" />
<hkern g1="sterling" 	g2="four" 	k="72" />
<hkern g1="sterling" 	g2="nine" 	k="41" />
<hkern g1="sterling" 	g2="one" 	k="82" />
<hkern g1="sterling" 	g2="yen" 	k="20" />
<hkern g1="sterling" 	g2="eight" 	k="41" />
<hkern g1="sterling" 	g2="Euro" 	k="61" />
<hkern g1="sterling" 	g2="ampersand" 	k="61" />
<hkern g1="sterling" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="sterling" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="sterling" 	g2="less,equal,greater,multiply" 	k="41" />
<hkern g1="sterling" 	g2="six" 	k="61" />
<hkern g1="sterling" 	g2="zero" 	k="61" />
<hkern g1="trademark" 	g2="underscore" 	k="205" />
<hkern g1="yen" 	g2="asterisk,registered" 	k="-8" />
<hkern g1="yen" 	g2="at" 	k="61" />
<hkern g1="yen" 	g2="bullet" 	k="41" />
<hkern g1="yen" 	g2="four" 	k="41" />
<hkern g1="yen" 	g2="nine" 	k="31" />
<hkern g1="yen" 	g2="one" 	k="61" />
<hkern g1="yen" 	g2="question" 	k="20" />
<hkern g1="yen" 	g2="slash" 	k="82" />
<hkern g1="yen" 	g2="eight" 	k="20" />
<hkern g1="yen" 	g2="Euro" 	k="41" />
<hkern g1="yen" 	g2="sterling" 	k="61" />
<hkern g1="yen" 	g2="three" 	k="31" />
<hkern g1="yen" 	g2="two" 	k="31" />
<hkern g1="yen" 	g2="underscore" 	k="123" />
<hkern g1="yen" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-8" />
<hkern g1="yen" 	g2="ampersand" 	k="41" />
<hkern g1="yen" 	g2="guillemotright,guilsinglright" 	k="82" />
<hkern g1="yen" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="yen" 	g2="exclam" 	k="-41" />
<hkern g1="yen" 	g2="less,equal,greater,multiply" 	k="41" />
<hkern g1="yen" 	g2="six" 	k="61" />
<hkern g1="yen" 	g2="bracketright" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="asterisk,registered" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="at" 	k="61" />
<hkern g1="asterisk,registered" 	g2="exclam" 	k="-18" />
<hkern g1="asterisk,registered" 	g2="four" 	k="61" />
<hkern g1="asterisk,registered" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="asterisk,registered" 	g2="nine" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="numbersign" 	k="41" />
<hkern g1="asterisk,registered" 	g2="one" 	k="-18" />
<hkern g1="asterisk,registered" 	g2="questiondown" 	k="164" />
<hkern g1="asterisk,registered" 	g2="seven" 	k="-18" />
<hkern g1="asterisk,registered" 	g2="slash" 	k="164" />
<hkern g1="asterisk,registered" 	g2="sterling" 	k="41" />
<hkern g1="asterisk,registered" 	g2="three" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="two" 	k="-18" />
<hkern g1="asterisk,registered" 	g2="underscore" 	k="205" />
<hkern g1="asterisk,registered" 	g2="yen" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-18" />
<hkern g1="backslash" 	g2="asterisk,registered" 	k="164" />
<hkern g1="backslash" 	g2="at" 	k="61" />
<hkern g1="backslash" 	g2="four" 	k="41" />
<hkern g1="backslash" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="backslash" 	g2="nine" 	k="82" />
<hkern g1="backslash" 	g2="one" 	k="100" />
<hkern g1="backslash" 	g2="seven" 	k="82" />
<hkern g1="backslash" 	g2="three" 	k="61" />
<hkern g1="backslash" 	g2="yen" 	k="82" />
<hkern g1="backslash" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="143" />
<hkern g1="backslash" 	g2="ampersand" 	k="123" />
<hkern g1="backslash" 	g2="backslash" 	k="246" />
<hkern g1="backslash" 	g2="bullet" 	k="61" />
<hkern g1="backslash" 	g2="copyright" 	k="102" />
<hkern g1="backslash" 	g2="degree" 	k="184" />
<hkern g1="backslash" 	g2="Euro" 	k="41" />
<hkern g1="backslash" 	g2="percent" 	k="123" />
<hkern g1="backslash" 	g2="question" 	k="164" />
<hkern g1="backslash" 	g2="six" 	k="41" />
<hkern g1="backslash" 	g2="trademark" 	k="164" />
<hkern g1="braceleft" 	g2="questiondown" 	k="-8" />
<hkern g1="braceleft" 	g2="underscore" 	k="-18" />
<hkern g1="bracketleft" 	g2="four" 	k="20" />
<hkern g1="bracketleft" 	g2="one" 	k="59" />
<hkern g1="bracketleft" 	g2="questiondown" 	k="-8" />
<hkern g1="bracketleft" 	g2="underscore" 	k="-18" />
<hkern g1="bracketleft" 	g2="yen" 	k="-8" />
<hkern g1="bullet" 	g2="one" 	k="115" />
<hkern g1="bullet" 	g2="questiondown" 	k="123" />
<hkern g1="bullet" 	g2="seven" 	k="123" />
<hkern g1="bullet" 	g2="slash" 	k="61" />
<hkern g1="bullet" 	g2="three" 	k="102" />
<hkern g1="bullet" 	g2="two" 	k="102" />
<hkern g1="bullet" 	g2="yen" 	k="41" />
<hkern g1="bullet" 	g2="ampersand" 	k="41" />
<hkern g1="bullet" 	g2="backslash" 	k="61" />
<hkern g1="bullet" 	g2="question" 	k="82" />
<hkern g1="bullet" 	g2="trademark" 	k="61" />
<hkern g1="bullet" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="exclam" 	g2="asterisk,registered" 	k="-18" />
<hkern g1="exclam" 	g2="question" 	k="41" />
<hkern g1="exclamdown" 	g2="one" 	k="61" />
<hkern g1="exclamdown" 	g2="seven" 	k="61" />
<hkern g1="exclamdown" 	g2="backslash" 	k="102" />
<hkern g1="exclamdown" 	g2="question" 	k="61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="at" 	k="61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="four" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="guillemotleft,guilsinglleft" 	k="74" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="one" 	k="61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="seven" 	k="61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="three" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="two" 	k="31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="yen" 	k="82" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="backslash" 	k="82" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="bullet" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="trademark" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="asterisk,registered" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="nine" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="one" 	k="102" />
<hkern g1="guillemotright,guilsinglright" 	g2="questiondown" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="seven" 	k="102" />
<hkern g1="guillemotright,guilsinglright" 	g2="slash" 	k="61" />
<hkern g1="guillemotright,guilsinglright" 	g2="three" 	k="92" />
<hkern g1="guillemotright,guilsinglright" 	g2="two" 	k="92" />
<hkern g1="guillemotright,guilsinglright" 	g2="underscore" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="yen" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="backslash" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="question" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="trademark" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="guillemotright,guilsinglright" 	k="240" />
<hkern g1="numbersign" 	g2="asterisk,registered" 	k="-41" />
<hkern g1="numbersign" 	g2="seven" 	k="-8" />
<hkern g1="numbersign" 	g2="three" 	k="16" />
<hkern g1="numbersign" 	g2="two" 	k="16" />
<hkern g1="numbersign" 	g2="underscore" 	k="164" />
<hkern g1="numbersign" 	g2="ampersand" 	k="51" />
<hkern g1="numbersign" 	g2="backslash" 	k="41" />
<hkern g1="numbersign" 	g2="bullet" 	k="41" />
<hkern g1="numbersign" 	g2="question" 	k="41" />
<hkern g1="parenleft" 	g2="four" 	k="20" />
<hkern g1="parenleft" 	g2="one" 	k="41" />
<hkern g1="parenleft" 	g2="questiondown" 	k="-18" />
<hkern g1="parenleft" 	g2="underscore" 	k="-18" />
<hkern g1="question" 	g2="asterisk,registered" 	k="-18" />
<hkern g1="question" 	g2="four" 	k="31" />
<hkern g1="question" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="question" 	g2="numbersign" 	k="61" />
<hkern g1="question" 	g2="questiondown" 	k="205" />
<hkern g1="question" 	g2="slash" 	k="164" />
<hkern g1="question" 	g2="underscore" 	k="205" />
<hkern g1="question" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-18" />
<hkern g1="question" 	g2="ampersand" 	k="82" />
<hkern g1="question" 	g2="question" 	k="74" />
<hkern g1="question" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="questiondown" 	g2="asterisk,registered" 	k="82" />
<hkern g1="questiondown" 	g2="at" 	k="61" />
<hkern g1="questiondown" 	g2="exclam" 	k="61" />
<hkern g1="questiondown" 	g2="four" 	k="102" />
<hkern g1="questiondown" 	g2="guillemotleft,guilsinglleft" 	k="102" />
<hkern g1="questiondown" 	g2="nine" 	k="41" />
<hkern g1="questiondown" 	g2="numbersign" 	k="41" />
<hkern g1="questiondown" 	g2="one" 	k="164" />
<hkern g1="questiondown" 	g2="questiondown" 	k="74" />
<hkern g1="questiondown" 	g2="seven" 	k="82" />
<hkern g1="questiondown" 	g2="three" 	k="41" />
<hkern g1="questiondown" 	g2="two" 	k="41" />
<hkern g1="questiondown" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="123" />
<hkern g1="questiondown" 	g2="ampersand" 	k="61" />
<hkern g1="questiondown" 	g2="backslash" 	k="205" />
<hkern g1="questiondown" 	g2="bullet" 	k="61" />
<hkern g1="questiondown" 	g2="Euro" 	k="82" />
<hkern g1="questiondown" 	g2="percent" 	k="82" />
<hkern g1="questiondown" 	g2="question" 	k="205" />
<hkern g1="questiondown" 	g2="six" 	k="41" />
<hkern g1="questiondown" 	g2="trademark" 	k="102" />
<hkern g1="questiondown" 	g2="guillemotright,guilsinglright" 	k="59" />
<hkern g1="questiondown" 	g2="eight" 	k="41" />
<hkern g1="questiondown" 	g2="exclamdown" 	k="41" />
<hkern g1="questiondown" 	g2="zero" 	k="41" />
<hkern g1="slash" 	g2="at" 	k="102" />
<hkern g1="slash" 	g2="four" 	k="102" />
<hkern g1="slash" 	g2="guillemotleft,guilsinglleft" 	k="123" />
<hkern g1="slash" 	g2="nine" 	k="82" />
<hkern g1="slash" 	g2="numbersign" 	k="41" />
<hkern g1="slash" 	g2="one" 	k="123" />
<hkern g1="slash" 	g2="questiondown" 	k="205" />
<hkern g1="slash" 	g2="slash" 	k="246" />
<hkern g1="slash" 	g2="sterling" 	k="102" />
<hkern g1="slash" 	g2="three" 	k="61" />
<hkern g1="slash" 	g2="two" 	k="82" />
<hkern g1="slash" 	g2="underscore" 	k="246" />
<hkern g1="slash" 	g2="ampersand" 	k="102" />
<hkern g1="slash" 	g2="bullet" 	k="61" />
<hkern g1="slash" 	g2="Euro" 	k="61" />
<hkern g1="slash" 	g2="percent" 	k="41" />
<hkern g1="slash" 	g2="question" 	k="123" />
<hkern g1="slash" 	g2="six" 	k="61" />
<hkern g1="slash" 	g2="guillemotright,guilsinglright" 	k="82" />
<hkern g1="slash" 	g2="eight" 	k="51" />
<hkern g1="slash" 	g2="zero" 	k="20" />
<hkern g1="underscore" 	g2="asterisk,registered" 	k="205" />
<hkern g1="underscore" 	g2="at" 	k="61" />
<hkern g1="underscore" 	g2="four" 	k="246" />
<hkern g1="underscore" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="underscore" 	g2="one" 	k="205" />
<hkern g1="underscore" 	g2="questiondown" 	k="-8" />
<hkern g1="underscore" 	g2="underscore" 	k="20" />
<hkern g1="underscore" 	g2="yen" 	k="123" />
<hkern g1="underscore" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="164" />
<hkern g1="underscore" 	g2="ampersand" 	k="61" />
<hkern g1="underscore" 	g2="backslash" 	k="246" />
<hkern g1="underscore" 	g2="copyright" 	k="205" />
<hkern g1="underscore" 	g2="Euro" 	k="164" />
<hkern g1="underscore" 	g2="percent" 	k="164" />
<hkern g1="underscore" 	g2="question" 	k="164" />
<hkern g1="underscore" 	g2="six" 	k="61" />
<hkern g1="underscore" 	g2="trademark" 	k="205" />
<hkern g1="underscore" 	g2="eight" 	k="41" />
<hkern g1="underscore" 	g2="zero" 	k="61" />
<hkern g1="underscore" 	g2="braceright" 	k="-18" />
<hkern g1="underscore" 	g2="bracketright" 	k="-18" />
<hkern g1="underscore" 	g2="parenright" 	k="-18" />
<hkern g1="eight" 	g2="backslash" 	k="51" />
<hkern g1="eight" 	g2="Euro" 	k="10" />
<hkern g1="eight" 	g2="nine" 	k="35" />
<hkern g1="eight" 	g2="one" 	k="61" />
<hkern g1="eight" 	g2="question" 	k="41" />
<hkern g1="eight" 	g2="seven" 	k="31" />
<hkern g1="eight" 	g2="trademark" 	k="41" />
<hkern g1="eight" 	g2="two" 	k="20" />
<hkern g1="eight" 	g2="underscore" 	k="41" />
<hkern g1="eight" 	g2="yen" 	k="20" />
<hkern g1="four" 	g2="backslash" 	k="102" />
<hkern g1="four" 	g2="nine" 	k="41" />
<hkern g1="four" 	g2="one" 	k="119" />
<hkern g1="four" 	g2="question" 	k="102" />
<hkern g1="four" 	g2="seven" 	k="49" />
<hkern g1="four" 	g2="trademark" 	k="82" />
<hkern g1="four" 	g2="two" 	k="41" />
<hkern g1="four" 	g2="yen" 	k="41" />
<hkern g1="four" 	g2="percent" 	k="82" />
<hkern g1="four" 	g2="asterisk,registered" 	k="82" />
<hkern g1="four" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="four" 	g2="slash" 	k="41" />
<hkern g1="four" 	g2="three" 	k="35" />
<hkern g1="nine" 	g2="backslash" 	k="82" />
<hkern g1="nine" 	g2="one" 	k="41" />
<hkern g1="nine" 	g2="question" 	k="41" />
<hkern g1="nine" 	g2="seven" 	k="31" />
<hkern g1="nine" 	g2="trademark" 	k="20" />
<hkern g1="nine" 	g2="two" 	k="45" />
<hkern g1="nine" 	g2="underscore" 	k="205" />
<hkern g1="nine" 	g2="yen" 	k="20" />
<hkern g1="nine" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-20" />
<hkern g1="nine" 	g2="slash" 	k="82" />
<hkern g1="nine" 	g2="three" 	k="41" />
<hkern g1="nine" 	g2="four" 	k="20" />
<hkern g1="one" 	g2="one" 	k="29" />
<hkern g1="seven" 	g2="Euro" 	k="61" />
<hkern g1="seven" 	g2="nine" 	k="37" />
<hkern g1="seven" 	g2="one" 	k="37" />
<hkern g1="seven" 	g2="trademark" 	k="-41" />
<hkern g1="seven" 	g2="two" 	k="31" />
<hkern g1="seven" 	g2="underscore" 	k="205" />
<hkern g1="seven" 	g2="bullet" 	k="51" />
<hkern g1="seven" 	g2="degree" 	k="-20" />
<hkern g1="seven" 	g2="eight" 	k="41" />
<hkern g1="seven" 	g2="guillemotleft,guilsinglleft" 	k="102" />
<hkern g1="seven" 	g2="percent" 	k="-20" />
<hkern g1="seven" 	g2="six" 	k="47" />
<hkern g1="seven" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-18" />
<hkern g1="seven" 	g2="asterisk,registered" 	k="-18" />
<hkern g1="seven" 	g2="guillemotright,guilsinglright" 	k="61" />
<hkern g1="seven" 	g2="slash" 	k="82" />
<hkern g1="seven" 	g2="three" 	k="20" />
<hkern g1="seven" 	g2="exclam" 	k="-41" />
<hkern g1="seven" 	g2="four" 	k="82" />
<hkern g1="seven" 	g2="ampersand" 	k="41" />
<hkern g1="seven" 	g2="at" 	k="41" />
<hkern g1="seven" 	g2="numbersign" 	k="41" />
<hkern g1="seven" 	g2="sterling" 	k="61" />
<hkern g1="seven" 	g2="zero" 	k="25" />
<hkern g1="six" 	g2="backslash" 	k="61" />
<hkern g1="six" 	g2="nine" 	k="31" />
<hkern g1="six" 	g2="one" 	k="100" />
<hkern g1="six" 	g2="question" 	k="61" />
<hkern g1="six" 	g2="seven" 	k="51" />
<hkern g1="six" 	g2="trademark" 	k="61" />
<hkern g1="six" 	g2="two" 	k="25" />
<hkern g1="six" 	g2="underscore" 	k="41" />
<hkern g1="six" 	g2="yen" 	k="61" />
<hkern g1="six" 	g2="degree" 	k="20" />
<hkern g1="six" 	g2="asterisk,registered" 	k="41" />
<hkern g1="six" 	g2="slash" 	k="41" />
<hkern g1="six" 	g2="three" 	k="20" />
<hkern g1="three" 	g2="backslash" 	k="41" />
<hkern g1="three" 	g2="nine" 	k="25" />
<hkern g1="three" 	g2="one" 	k="51" />
<hkern g1="three" 	g2="question" 	k="20" />
<hkern g1="three" 	g2="seven" 	k="10" />
<hkern g1="three" 	g2="two" 	k="20" />
<hkern g1="three" 	g2="underscore" 	k="41" />
<hkern g1="three" 	g2="yen" 	k="25" />
<hkern g1="three" 	g2="slash" 	k="41" />
<hkern g1="three" 	g2="four" 	k="10" />
<hkern g1="two" 	g2="backslash" 	k="41" />
<hkern g1="two" 	g2="Euro" 	k="31" />
<hkern g1="two" 	g2="nine" 	k="20" />
<hkern g1="two" 	g2="one" 	k="51" />
<hkern g1="two" 	g2="question" 	k="20" />
<hkern g1="two" 	g2="seven" 	k="20" />
<hkern g1="two" 	g2="yen" 	k="31" />
<hkern g1="two" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="two" 	g2="three" 	k="10" />
<hkern g1="two" 	g2="four" 	k="55" />
<hkern g1="two" 	g2="sterling" 	k="20" />
<hkern g1="zero" 	g2="backslash" 	k="20" />
<hkern g1="zero" 	g2="nine" 	k="20" />
<hkern g1="zero" 	g2="one" 	k="51" />
<hkern g1="zero" 	g2="question" 	k="41" />
<hkern g1="zero" 	g2="seven" 	k="41" />
<hkern g1="zero" 	g2="trademark" 	k="41" />
<hkern g1="zero" 	g2="two" 	k="25" />
<hkern g1="zero" 	g2="underscore" 	k="61" />
<hkern g1="zero" 	g2="three" 	k="20" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="backslash" 	k="164" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="Euro" 	k="-8" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="one" 	k="123" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="question" 	k="82" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="seven" 	k="41" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="trademark" 	k="41" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="underscore" 	k="41" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="yen" 	k="31" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="eight" 	k="-8" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="percent" 	k="61" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="asterisk,registered" 	k="61" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="three" 	k="-18" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="four" 	k="-18" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="nine" 	k="-8" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="one" 	k="-8" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="seven" 	k="-8" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="two" 	k="-8" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="underscore" 	k="164" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="yen" 	k="-8" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="asterisk,registered" 	k="-18" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="slash" 	k="123" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="questiondown" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="100" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk,registered" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="braceright" 	k="-18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="bracketright" 	k="-18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="bullet" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="colon,semicolon" 	k="-8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="exclam" 	k="-4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="four" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="nine" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="one" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="seven" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="two" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="35" />
<hkern g1="B" 	g2="T" 	k="41" />
<hkern g1="B" 	g2="V" 	k="31" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="49" />
<hkern g1="B" 	g2="asterisk,registered" 	k="20" />
<hkern g1="B" 	g2="backslash" 	k="41" />
<hkern g1="B" 	g2="four" 	k="20" />
<hkern g1="B" 	g2="nine" 	k="41" />
<hkern g1="B" 	g2="one" 	k="82" />
<hkern g1="B" 	g2="question" 	k="41" />
<hkern g1="B" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="B" 	g2="seven" 	k="61" />
<hkern g1="B" 	g2="trademark" 	k="41" />
<hkern g1="B" 	g2="two" 	k="31" />
<hkern g1="B" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="B" 	g2="AE" 	k="20" />
<hkern g1="B" 	g2="J" 	k="33" />
<hkern g1="B" 	g2="dollar,S" 	k="41" />
<hkern g1="B" 	g2="W" 	k="8" />
<hkern g1="B" 	g2="X" 	k="23" />
<hkern g1="B" 	g2="Z" 	k="31" />
<hkern g1="B" 	g2="five" 	k="20" />
<hkern g1="B" 	g2="underscore" 	k="41" />
<hkern g1="B" 	g2="x" 	k="12" />
<hkern g1="C,Ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="35" />
<hkern g1="C,Ccedilla" 	g2="backslash" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="bullet" 	k="123" />
<hkern g1="C,Ccedilla" 	g2="four" 	k="102" />
<hkern g1="C,Ccedilla" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="164" />
<hkern g1="C,Ccedilla" 	g2="nine" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="one" 	k="111" />
<hkern g1="C,Ccedilla" 	g2="seven" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="dollar,S" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="8" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="five" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="x" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="v,y,yacute,ydieresis" 	k="102" />
<hkern g1="C,Ccedilla" 	g2="M" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="ampersand" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="at" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="eight" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="less,equal,greater,multiply" 	k="113" />
<hkern g1="C,Ccedilla" 	g2="f,uniFB01,uniFB02" 	k="45" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="C,Ccedilla" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="j" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="l" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="m,n,p,r,mu,ntilde" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="s" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="six" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="35" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="z" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="zero" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="asterisk,registered" 	k="-8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="backslash" 	k="61" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="bullet" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="four" 	k="61" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="nine" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="one" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="J" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="dollar,S" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Z" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v,y,yacute,ydieresis" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="at" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="eight" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="l" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="m,n,p,r,mu,ntilde" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="six" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="zero" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-8" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="35" />
<hkern g1="F" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="F" 	g2="bracketright" 	k="-8" />
<hkern g1="F" 	g2="colon,semicolon" 	k="41" />
<hkern g1="F" 	g2="exclam" 	k="-4" />
<hkern g1="F" 	g2="four" 	k="82" />
<hkern g1="F" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="F" 	g2="nine" 	k="41" />
<hkern g1="F" 	g2="one" 	k="61" />
<hkern g1="F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="F" 	g2="two" 	k="20" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="F" 	g2="AE" 	k="143" />
<hkern g1="F" 	g2="J" 	k="18" />
<hkern g1="F" 	g2="dollar,S" 	k="41" />
<hkern g1="F" 	g2="Z" 	k="41" />
<hkern g1="F" 	g2="five" 	k="41" />
<hkern g1="F" 	g2="underscore" 	k="205" />
<hkern g1="F" 	g2="x" 	k="123" />
<hkern g1="F" 	g2="slash" 	k="102" />
<hkern g1="F" 	g2="v,y,yacute,ydieresis" 	k="72" />
<hkern g1="F" 	g2="M" 	k="39" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="123" />
<hkern g1="F" 	g2="ampersand" 	k="61" />
<hkern g1="F" 	g2="at" 	k="41" />
<hkern g1="F" 	g2="eight" 	k="51" />
<hkern g1="F" 	g2="less,equal,greater,multiply" 	k="41" />
<hkern g1="F" 	g2="f,uniFB01,uniFB02" 	k="82" />
<hkern g1="F" 	g2="g" 	k="92" />
<hkern g1="F" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="82" />
<hkern g1="F" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="F" 	g2="j" 	k="66" />
<hkern g1="F" 	g2="l" 	k="20" />
<hkern g1="F" 	g2="m,n,p,r,mu,ntilde" 	k="72" />
<hkern g1="F" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="72" />
<hkern g1="F" 	g2="s" 	k="92" />
<hkern g1="F" 	g2="six" 	k="31" />
<hkern g1="F" 	g2="t" 	k="29" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="76" />
<hkern g1="F" 	g2="w" 	k="51" />
<hkern g1="F" 	g2="z" 	k="102" />
<hkern g1="F" 	g2="zero" 	k="20" />
<hkern g1="F" 	g2="i" 	k="72" />
<hkern g1="F" 	g2="numbersign" 	k="61" />
<hkern g1="G" 	g2="T" 	k="39" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="49" />
<hkern g1="G" 	g2="backslash" 	k="31" />
<hkern g1="G" 	g2="nine" 	k="20" />
<hkern g1="G" 	g2="one" 	k="59" />
<hkern g1="G" 	g2="question" 	k="20" />
<hkern g1="G" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="G" 	g2="seven" 	k="35" />
<hkern g1="G" 	g2="trademark" 	k="20" />
<hkern g1="G" 	g2="two" 	k="20" />
<hkern g1="G" 	g2="J" 	k="8" />
<hkern g1="G" 	g2="X" 	k="8" />
<hkern g1="G" 	g2="Z" 	k="10" />
<hkern g1="H,I,N,Igrave,Ntilde" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-18" />
<hkern g1="Iacute,Icircumflex,Idieresis" 	g2="backslash" 	k="-18" />
<hkern g1="Iacute,Icircumflex,Idieresis" 	g2="braceright" 	k="-76" />
<hkern g1="Iacute,Icircumflex,Idieresis" 	g2="bracketright" 	k="-76" />
<hkern g1="Iacute,Icircumflex,Idieresis" 	g2="parenright" 	k="-88" />
<hkern g1="J" 	g2="underscore" 	k="41" />
<hkern g1="J" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-18" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="92" />
<hkern g1="L" 	g2="T" 	k="170" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="51" />
<hkern g1="L" 	g2="V" 	k="160" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="184" />
<hkern g1="L" 	g2="asterisk,registered" 	k="164" />
<hkern g1="L" 	g2="backslash" 	k="223" />
<hkern g1="L" 	g2="bullet" 	k="164" />
<hkern g1="L" 	g2="four" 	k="84" />
<hkern g1="L" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="205" />
<hkern g1="L" 	g2="nine" 	k="123" />
<hkern g1="L" 	g2="one" 	k="246" />
<hkern g1="L" 	g2="question" 	k="164" />
<hkern g1="L" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="205" />
<hkern g1="L" 	g2="seven" 	k="68" />
<hkern g1="L" 	g2="trademark" 	k="205" />
<hkern g1="L" 	g2="two" 	k="31" />
<hkern g1="L" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="205" />
<hkern g1="L" 	g2="J" 	k="39" />
<hkern g1="L" 	g2="dollar,S" 	k="31" />
<hkern g1="L" 	g2="W" 	k="82" />
<hkern g1="L" 	g2="five" 	k="20" />
<hkern g1="L" 	g2="slash" 	k="41" />
<hkern g1="L" 	g2="v,y,yacute,ydieresis" 	k="141" />
<hkern g1="L" 	g2="M" 	k="20" />
<hkern g1="L" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="L" 	g2="ampersand" 	k="41" />
<hkern g1="L" 	g2="eight" 	k="20" />
<hkern g1="L" 	g2="less,equal,greater,multiply" 	k="61" />
<hkern g1="L" 	g2="f,uniFB01,uniFB02" 	k="59" />
<hkern g1="L" 	g2="g" 	k="51" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="123" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="L" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="L" 	g2="j" 	k="31" />
<hkern g1="L" 	g2="l" 	k="25" />
<hkern g1="L" 	g2="m,n,p,r,mu,ntilde" 	k="20" />
<hkern g1="L" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="L" 	g2="s" 	k="31" />
<hkern g1="L" 	g2="six" 	k="61" />
<hkern g1="L" 	g2="t" 	k="49" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="L" 	g2="w" 	k="90" />
<hkern g1="L" 	g2="zero" 	k="61" />
<hkern g1="L" 	g2="igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="L" 	g2="copyright" 	k="164" />
<hkern g1="M" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="M" 	g2="T" 	k="29" />
<hkern g1="M" 	g2="V" 	k="8" />
<hkern g1="M" 	g2="Y,Yacute,Ydieresis" 	k="33" />
<hkern g1="M" 	g2="four" 	k="20" />
<hkern g1="M" 	g2="nine" 	k="10" />
<hkern g1="M" 	g2="one" 	k="29" />
<hkern g1="M" 	g2="question" 	k="20" />
<hkern g1="M" 	g2="seven" 	k="20" />
<hkern g1="M" 	g2="trademark" 	k="41" />
<hkern g1="M" 	g2="J" 	k="8" />
<hkern g1="M" 	g2="dollar,S" 	k="31" />
<hkern g1="M" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="72" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="63" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="one" 	k="25" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="seven" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="two" 	k="51" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="18" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="29" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="51" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="underscore" 	k="82" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="three" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="M" 	k="14" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="20" />
<hkern g1="P" 	g2="T" 	k="51" />
<hkern g1="P" 	g2="V" 	k="41" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="76" />
<hkern g1="P" 	g2="backslash" 	k="102" />
<hkern g1="P" 	g2="colon,semicolon" 	k="10" />
<hkern g1="P" 	g2="four" 	k="92" />
<hkern g1="P" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="31" />
<hkern g1="P" 	g2="nine" 	k="10" />
<hkern g1="P" 	g2="one" 	k="29" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="246" />
<hkern g1="P" 	g2="question" 	k="82" />
<hkern g1="P" 	g2="seven" 	k="41" />
<hkern g1="P" 	g2="trademark" 	k="20" />
<hkern g1="P" 	g2="two" 	k="51" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="123" />
<hkern g1="P" 	g2="AE" 	k="162" />
<hkern g1="P" 	g2="J" 	k="59" />
<hkern g1="P" 	g2="dollar,S" 	k="41" />
<hkern g1="P" 	g2="W" 	k="20" />
<hkern g1="P" 	g2="X" 	k="63" />
<hkern g1="P" 	g2="Z" 	k="76" />
<hkern g1="P" 	g2="five" 	k="31" />
<hkern g1="P" 	g2="underscore" 	k="205" />
<hkern g1="P" 	g2="x" 	k="31" />
<hkern g1="P" 	g2="slash" 	k="102" />
<hkern g1="P" 	g2="three" 	k="41" />
<hkern g1="P" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="P" 	g2="M" 	k="61" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="113" />
<hkern g1="P" 	g2="ampersand" 	k="80" />
<hkern g1="P" 	g2="at" 	k="61" />
<hkern g1="P" 	g2="eight" 	k="41" />
<hkern g1="P" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="P" 	g2="g" 	k="55" />
<hkern g1="P" 	g2="b,h,k,germandbls,thorn" 	k="41" />
<hkern g1="P" 	g2="j" 	k="41" />
<hkern g1="P" 	g2="l" 	k="20" />
<hkern g1="P" 	g2="m,n,p,r,mu,ntilde" 	k="41" />
<hkern g1="P" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="P" 	g2="s" 	k="68" />
<hkern g1="P" 	g2="six" 	k="20" />
<hkern g1="P" 	g2="t" 	k="10" />
<hkern g1="P" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="55" />
<hkern g1="P" 	g2="w" 	k="10" />
<hkern g1="P" 	g2="z" 	k="61" />
<hkern g1="P" 	g2="numbersign" 	k="61" />
<hkern g1="R" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="R" 	g2="T" 	k="41" />
<hkern g1="R" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="R" 	g2="V" 	k="14" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="53" />
<hkern g1="R" 	g2="backslash" 	k="41" />
<hkern g1="R" 	g2="braceright" 	k="-8" />
<hkern g1="R" 	g2="bracketright" 	k="-18" />
<hkern g1="R" 	g2="bullet" 	k="41" />
<hkern g1="R" 	g2="four" 	k="41" />
<hkern g1="R" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="49" />
<hkern g1="R" 	g2="nine" 	k="10" />
<hkern g1="R" 	g2="one" 	k="29" />
<hkern g1="R" 	g2="question" 	k="41" />
<hkern g1="R" 	g2="seven" 	k="31" />
<hkern g1="R" 	g2="trademark" 	k="20" />
<hkern g1="R" 	g2="J" 	k="20" />
<hkern g1="R" 	g2="dollar,S" 	k="14" />
<hkern g1="R" 	g2="Z" 	k="14" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="R" 	g2="ampersand" 	k="20" />
<hkern g1="R" 	g2="g" 	k="20" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="R" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="dollar,S" 	g2="T" 	k="45" />
<hkern g1="dollar,S" 	g2="V" 	k="41" />
<hkern g1="dollar,S" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="dollar,S" 	g2="backslash" 	k="61" />
<hkern g1="dollar,S" 	g2="nine" 	k="35" />
<hkern g1="dollar,S" 	g2="one" 	k="59" />
<hkern g1="dollar,S" 	g2="question" 	k="41" />
<hkern g1="dollar,S" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="dollar,S" 	g2="seven" 	k="41" />
<hkern g1="dollar,S" 	g2="trademark" 	k="31" />
<hkern g1="dollar,S" 	g2="two" 	k="20" />
<hkern g1="dollar,S" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="41" />
<hkern g1="dollar,S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="dollar,S" 	g2="AE" 	k="20" />
<hkern g1="dollar,S" 	g2="J" 	k="8" />
<hkern g1="dollar,S" 	g2="dollar,S" 	k="25" />
<hkern g1="dollar,S" 	g2="W" 	k="20" />
<hkern g1="dollar,S" 	g2="X" 	k="14" />
<hkern g1="dollar,S" 	g2="Z" 	k="31" />
<hkern g1="dollar,S" 	g2="underscore" 	k="61" />
<hkern g1="dollar,S" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="dollar,S" 	g2="M" 	k="20" />
<hkern g1="dollar,S" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="dollar,S" 	g2="z" 	k="20" />
<hkern g1="dollar,S" 	g2="yen" 	k="20" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="72" />
<hkern g1="T" 	g2="asterisk,registered" 	k="-8" />
<hkern g1="T" 	g2="backslash" 	k="61" />
<hkern g1="T" 	g2="bracketright" 	k="-18" />
<hkern g1="T" 	g2="bullet" 	k="102" />
<hkern g1="T" 	g2="colon,semicolon" 	k="82" />
<hkern g1="T" 	g2="exclam" 	k="-8" />
<hkern g1="T" 	g2="four" 	k="102" />
<hkern g1="T" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="102" />
<hkern g1="T" 	g2="nine" 	k="41" />
<hkern g1="T" 	g2="one" 	k="41" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="123" />
<hkern g1="T" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="T" 	g2="AE" 	k="152" />
<hkern g1="T" 	g2="J" 	k="29" />
<hkern g1="T" 	g2="dollar,S" 	k="41" />
<hkern g1="T" 	g2="Z" 	k="41" />
<hkern g1="T" 	g2="five" 	k="41" />
<hkern g1="T" 	g2="underscore" 	k="102" />
<hkern g1="T" 	g2="x" 	k="70" />
<hkern g1="T" 	g2="slash" 	k="102" />
<hkern g1="T" 	g2="v,y,yacute,ydieresis" 	k="82" />
<hkern g1="T" 	g2="M" 	k="41" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="102" />
<hkern g1="T" 	g2="ampersand" 	k="82" />
<hkern g1="T" 	g2="at" 	k="102" />
<hkern g1="T" 	g2="eight" 	k="41" />
<hkern g1="T" 	g2="f,uniFB01,uniFB02" 	k="82" />
<hkern g1="T" 	g2="g" 	k="102" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="T" 	g2="j" 	k="61" />
<hkern g1="T" 	g2="l" 	k="20" />
<hkern g1="T" 	g2="m,n,p,r,mu,ntilde" 	k="61" />
<hkern g1="T" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="T" 	g2="s" 	k="102" />
<hkern g1="T" 	g2="six" 	k="82" />
<hkern g1="T" 	g2="t" 	k="82" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="82" />
<hkern g1="T" 	g2="w" 	k="61" />
<hkern g1="T" 	g2="z" 	k="82" />
<hkern g1="T" 	g2="zero" 	k="61" />
<hkern g1="T" 	g2="numbersign" 	k="102" />
<hkern g1="Thorn" 	g2="T" 	k="82" />
<hkern g1="Thorn" 	g2="V" 	k="49" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ydieresis" 	k="100" />
<hkern g1="Thorn" 	g2="backslash" 	k="82" />
<hkern g1="Thorn" 	g2="four" 	k="31" />
<hkern g1="Thorn" 	g2="nine" 	k="20" />
<hkern g1="Thorn" 	g2="one" 	k="70" />
<hkern g1="Thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="Thorn" 	g2="question" 	k="70" />
<hkern g1="Thorn" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="Thorn" 	g2="seven" 	k="102" />
<hkern g1="Thorn" 	g2="trademark" 	k="61" />
<hkern g1="Thorn" 	g2="two" 	k="61" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="Thorn" 	g2="AE" 	k="102" />
<hkern g1="Thorn" 	g2="J" 	k="39" />
<hkern g1="Thorn" 	g2="dollar,S" 	k="41" />
<hkern g1="Thorn" 	g2="W" 	k="29" />
<hkern g1="Thorn" 	g2="X" 	k="78" />
<hkern g1="Thorn" 	g2="Z" 	k="74" />
<hkern g1="Thorn" 	g2="five" 	k="20" />
<hkern g1="Thorn" 	g2="underscore" 	k="205" />
<hkern g1="Thorn" 	g2="x" 	k="20" />
<hkern g1="Thorn" 	g2="slash" 	k="82" />
<hkern g1="Thorn" 	g2="three" 	k="82" />
<hkern g1="Thorn" 	g2="M" 	k="29" />
<hkern g1="Thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="Thorn" 	g2="ampersand" 	k="41" />
<hkern g1="Thorn" 	g2="eight" 	k="31" />
<hkern g1="Thorn" 	g2="g" 	k="20" />
<hkern g1="Thorn" 	g2="m,n,p,r,mu,ntilde" 	k="41" />
<hkern g1="Thorn" 	g2="s" 	k="41" />
<hkern g1="Thorn" 	g2="z" 	k="41" />
<hkern g1="Thorn" 	g2="numbersign" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="8" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="dollar,S" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="X" 	k="8" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Z" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="underscore" 	k="61" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-18" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="V" 	g2="asterisk,registered" 	k="-8" />
<hkern g1="V" 	g2="bracketright" 	k="-27" />
<hkern g1="V" 	g2="bullet" 	k="20" />
<hkern g1="V" 	g2="colon,semicolon" 	k="20" />
<hkern g1="V" 	g2="exclam" 	k="-20" />
<hkern g1="V" 	g2="four" 	k="41" />
<hkern g1="V" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="51" />
<hkern g1="V" 	g2="nine" 	k="10" />
<hkern g1="V" 	g2="one" 	k="41" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="V" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="V" 	g2="trademark" 	k="-20" />
<hkern g1="V" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-20" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="V" 	g2="AE" 	k="59" />
<hkern g1="V" 	g2="J" 	k="8" />
<hkern g1="V" 	g2="dollar,S" 	k="31" />
<hkern g1="V" 	g2="Z" 	k="41" />
<hkern g1="V" 	g2="five" 	k="20" />
<hkern g1="V" 	g2="underscore" 	k="102" />
<hkern g1="V" 	g2="x" 	k="35" />
<hkern g1="V" 	g2="slash" 	k="61" />
<hkern g1="V" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="V" 	g2="M" 	k="14" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="V" 	g2="ampersand" 	k="41" />
<hkern g1="V" 	g2="at" 	k="31" />
<hkern g1="V" 	g2="eight" 	k="20" />
<hkern g1="V" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="V" 	g2="g" 	k="61" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="V" 	g2="j" 	k="14" />
<hkern g1="V" 	g2="m,n,p,r,mu,ntilde" 	k="20" />
<hkern g1="V" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="V" 	g2="s" 	k="41" />
<hkern g1="V" 	g2="six" 	k="10" />
<hkern g1="V" 	g2="t" 	k="20" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="V" 	g2="w" 	k="14" />
<hkern g1="V" 	g2="z" 	k="39" />
<hkern g1="V" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-20" />
<hkern g1="V" 	g2="numbersign" 	k="20" />
<hkern g1="W" 	g2="asterisk,registered" 	k="-8" />
<hkern g1="W" 	g2="bracketright" 	k="-8" />
<hkern g1="W" 	g2="four" 	k="20" />
<hkern g1="W" 	g2="one" 	k="41" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="W" 	g2="trademark" 	k="-20" />
<hkern g1="W" 	g2="J" 	k="8" />
<hkern g1="W" 	g2="dollar,S" 	k="20" />
<hkern g1="W" 	g2="Z" 	k="41" />
<hkern g1="W" 	g2="five" 	k="20" />
<hkern g1="W" 	g2="underscore" 	k="123" />
<hkern g1="W" 	g2="x" 	k="20" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="61" />
<hkern g1="W" 	g2="ampersand" 	k="20" />
<hkern g1="W" 	g2="at" 	k="20" />
<hkern g1="W" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="W" 	g2="g" 	k="20" />
<hkern g1="W" 	g2="m,n,p,r,mu,ntilde" 	k="10" />
<hkern g1="W" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="W" 	g2="s" 	k="20" />
<hkern g1="W" 	g2="t" 	k="10" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="W" 	g2="z" 	k="25" />
<hkern g1="W" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-29" />
<hkern g1="K,X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="K,X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="K,X" 	g2="backslash" 	k="41" />
<hkern g1="K,X" 	g2="braceright" 	k="-8" />
<hkern g1="K,X" 	g2="bracketright" 	k="-18" />
<hkern g1="K,X" 	g2="bullet" 	k="49" />
<hkern g1="K,X" 	g2="four" 	k="61" />
<hkern g1="K,X" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="70" />
<hkern g1="K,X" 	g2="nine" 	k="20" />
<hkern g1="K,X" 	g2="one" 	k="80" />
<hkern g1="K,X" 	g2="question" 	k="61" />
<hkern g1="K,X" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="18" />
<hkern g1="K,X" 	g2="J" 	k="8" />
<hkern g1="K,X" 	g2="dollar,S" 	k="20" />
<hkern g1="K,X" 	g2="v,y,yacute,ydieresis" 	k="18" />
<hkern g1="K,X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="K,X" 	g2="ampersand" 	k="20" />
<hkern g1="K,X" 	g2="at" 	k="61" />
<hkern g1="K,X" 	g2="eight" 	k="18" />
<hkern g1="K,X" 	g2="less,equal,greater,multiply" 	k="61" />
<hkern g1="K,X" 	g2="f,uniFB01,uniFB02" 	k="29" />
<hkern g1="K,X" 	g2="g" 	k="31" />
<hkern g1="K,X" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="K,X" 	g2="j" 	k="20" />
<hkern g1="K,X" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="K,X" 	g2="six" 	k="41" />
<hkern g1="K,X" 	g2="t" 	k="18" />
<hkern g1="K,X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="18" />
<hkern g1="K,X" 	g2="w" 	k="8" />
<hkern g1="K,X" 	g2="zero" 	k="8" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="63" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bracketright" 	k="-18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bullet" 	k="80" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="111" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="four" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="111" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="nine" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="one" 	k="59" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="182" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="two" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="129" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="dollar,S" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="five" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="underscore" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="three" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v,y,yacute,ydieresis" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="M" 	k="33" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="162" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="eight" 	k="59" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,uniFB01,uniFB02" 	k="59" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="j" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="l" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,mu,ntilde" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="121" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="111" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="six" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="49" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="111" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="zero" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="numbersign" 	k="102" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="Z" 	g2="T" 	k="10" />
<hkern g1="Z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="Z" 	g2="asterisk,registered" 	k="-20" />
<hkern g1="Z" 	g2="backslash" 	k="82" />
<hkern g1="Z" 	g2="bullet" 	k="82" />
<hkern g1="Z" 	g2="four" 	k="61" />
<hkern g1="Z" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="113" />
<hkern g1="Z" 	g2="nine" 	k="41" />
<hkern g1="Z" 	g2="one" 	k="41" />
<hkern g1="Z" 	g2="two" 	k="20" />
<hkern g1="Z" 	g2="J" 	k="18" />
<hkern g1="Z" 	g2="dollar,S" 	k="31" />
<hkern g1="Z" 	g2="Z" 	k="14" />
<hkern g1="Z" 	g2="five" 	k="31" />
<hkern g1="Z" 	g2="x" 	k="41" />
<hkern g1="Z" 	g2="v,y,yacute,ydieresis" 	k="31" />
<hkern g1="Z" 	g2="M" 	k="20" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="Z" 	g2="ampersand" 	k="41" />
<hkern g1="Z" 	g2="at" 	k="41" />
<hkern g1="Z" 	g2="eight" 	k="31" />
<hkern g1="Z" 	g2="less,equal,greater,multiply" 	k="61" />
<hkern g1="Z" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="Z" 	g2="g" 	k="51" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="102" />
<hkern g1="Z" 	g2="guillemotright,guilsinglright" 	k="61" />
<hkern g1="Z" 	g2="j" 	k="31" />
<hkern g1="Z" 	g2="l" 	k="31" />
<hkern g1="Z" 	g2="m,n,p,r,mu,ntilde" 	k="31" />
<hkern g1="Z" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="Z" 	g2="s" 	k="31" />
<hkern g1="Z" 	g2="six" 	k="51" />
<hkern g1="Z" 	g2="t" 	k="41" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="Z" 	g2="w" 	k="20" />
<hkern g1="Z" 	g2="z" 	k="31" />
<hkern g1="Z" 	g2="zero" 	k="20" />
<hkern g1="Z" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-8" />
<hkern g1="Z" 	g2="numbersign" 	k="20" />
<hkern g1="five" 	g2="J" 	k="8" />
<hkern g1="five" 	g2="dollar,S" 	k="31" />
<hkern g1="five" 	g2="T" 	k="82" />
<hkern g1="five" 	g2="V" 	k="61" />
<hkern g1="five" 	g2="W" 	k="41" />
<hkern g1="five" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="five" 	g2="Z" 	k="31" />
<hkern g1="five" 	g2="asterisk,registered" 	k="41" />
<hkern g1="five" 	g2="backslash" 	k="61" />
<hkern g1="five" 	g2="degree" 	k="20" />
<hkern g1="five" 	g2="five" 	k="10" />
<hkern g1="five" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-18" />
<hkern g1="five" 	g2="nine" 	k="31" />
<hkern g1="five" 	g2="one" 	k="104" />
<hkern g1="five" 	g2="percent" 	k="41" />
<hkern g1="five" 	g2="question" 	k="41" />
<hkern g1="five" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="61" />
<hkern g1="five" 	g2="s" 	k="20" />
<hkern g1="five" 	g2="seven" 	k="49" />
<hkern g1="five" 	g2="slash" 	k="41" />
<hkern g1="five" 	g2="t" 	k="14" />
<hkern g1="five" 	g2="three" 	k="14" />
<hkern g1="five" 	g2="trademark" 	k="41" />
<hkern g1="five" 	g2="two" 	k="25" />
<hkern g1="five" 	g2="underscore" 	k="41" />
<hkern g1="five" 	g2="v,y,yacute,ydieresis" 	k="18" />
<hkern g1="five" 	g2="w" 	k="4" />
<hkern g1="five" 	g2="x" 	k="18" />
<hkern g1="five" 	g2="yen" 	k="51" />
<hkern g1="five" 	g2="z" 	k="14" />
<hkern g1="five" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="20" />
<hkern g1="j" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-29" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="asterisk,registered" 	k="20" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="backslash" 	k="102" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="five" 	k="20" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="nine" 	k="25" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="one" 	k="102" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="question" 	k="82" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="seven" 	k="82" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="three" 	k="41" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="trademark" 	k="61" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="two" 	k="41" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="underscore" 	k="41" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="v,y,yacute,ydieresis" 	k="8" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="x" 	k="18" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="z" 	k="14" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="10" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="J" 	k="41" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="T" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="V" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="Z" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="asterisk,registered" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-88" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="s" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="seven" 	k="-18" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="slash" 	k="205" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="t" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="underscore" 	k="205" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="yen" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="z" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="205" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="AE" 	k="123" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="at" 	k="41" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="braceright" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="bracketright" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="eight" 	k="31" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="four" 	k="82" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="numbersign" 	k="61" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="questiondown" 	k="205" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="six" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="sterling" 	k="61" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="zero" 	k="20" />
<hkern g1="z" 	g2="backslash" 	k="61" />
<hkern g1="z" 	g2="one" 	k="20" />
<hkern g1="z" 	g2="question" 	k="61" />
<hkern g1="z" 	g2="s" 	k="14" />
<hkern g1="z" 	g2="seven" 	k="20" />
<hkern g1="z" 	g2="trademark" 	k="41" />
<hkern g1="z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="z" 	g2="at" 	k="20" />
<hkern g1="z" 	g2="four" 	k="51" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="z" 	g2="numbersign" 	k="8" />
<hkern g1="z" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="z" 	g2="six" 	k="20" />
<hkern g1="z" 	g2="ampersand" 	k="41" />
<hkern g1="z" 	g2="bullet" 	k="10" />
<hkern g1="z" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="z" 	g2="g" 	k="20" />
<hkern g1="z" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="72" />
<hkern g1="z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="ampersand" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk,registered" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="backslash" 	k="82" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="g" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="l" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="one" 	k="102" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="61" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="seven" 	k="61" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="trademark" 	k="61" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v,y,yacute,ydieresis" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="j" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="s" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="ampersand" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="backslash" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="bullet" 	k="51" />
<hkern g1="c,cent,ccedilla" 	g2="eight" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="less,equal,greater,multiply" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="four" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="g" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="88" />
<hkern g1="c,cent,ccedilla" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="one" 	k="27" />
<hkern g1="c,cent,ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="c,cent,ccedilla" 	g2="question" 	k="72" />
<hkern g1="c,cent,ccedilla" 	g2="seven" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="trademark" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="zero" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-20" />
<hkern g1="c,cent,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="at" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="colon,semicolon" 	k="-8" />
<hkern g1="c,cent,ccedilla" 	g2="yen" 	k="41" />
<hkern g1="d" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-27" />
<hkern g1="f" 	g2="ampersand" 	k="20" />
<hkern g1="f" 	g2="asterisk,registered" 	k="-102" />
<hkern g1="f" 	g2="backslash" 	k="-18" />
<hkern g1="f" 	g2="copyright" 	k="-61" />
<hkern g1="f" 	g2="eight" 	k="-10" />
<hkern g1="f" 	g2="four" 	k="27" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="f" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="41" />
<hkern g1="f" 	g2="nine" 	k="-41" />
<hkern g1="f" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="f" 	g2="one" 	k="-41" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="f" 	g2="question" 	k="-102" />
<hkern g1="f" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-143" />
<hkern g1="f" 	g2="seven" 	k="-123" />
<hkern g1="f" 	g2="trademark" 	k="-123" />
<hkern g1="f" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-61" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="45" />
<hkern g1="f" 	g2="braceright" 	k="-74" />
<hkern g1="f" 	g2="bracketright" 	k="-74" />
<hkern g1="f" 	g2="slash" 	k="49" />
<hkern g1="f" 	g2="s" 	k="10" />
<hkern g1="f" 	g2="two" 	k="-41" />
<hkern g1="f" 	g2="underscore" 	k="61" />
<hkern g1="f" 	g2="at" 	k="31" />
<hkern g1="f" 	g2="colon,semicolon" 	k="-12" />
<hkern g1="f" 	g2="z" 	k="20" />
<hkern g1="f" 	g2="five" 	k="-41" />
<hkern g1="f" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-123" />
<hkern g1="f" 	g2="exclam" 	k="-98" />
<hkern g1="f" 	g2="parenright" 	k="-164" />
<hkern g1="f" 	g2="bar,brokenbar" 	k="-8" />
<hkern g1="f" 	g2="percent" 	k="-41" />
<hkern g1="f" 	g2="three" 	k="-49" />
<hkern g1="g" 	g2="backslash" 	k="41" />
<hkern g1="g" 	g2="four" 	k="31" />
<hkern g1="g" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="g" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="g" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="g" 	g2="question" 	k="41" />
<hkern g1="g" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="g" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="39" />
<hkern g1="g" 	g2="s" 	k="14" />
<hkern g1="g" 	g2="underscore" 	k="-8" />
<hkern g1="g" 	g2="z" 	k="8" />
<hkern g1="germandbls" 	g2="asterisk,registered" 	k="82" />
<hkern g1="germandbls" 	g2="backslash" 	k="61" />
<hkern g1="germandbls" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="germandbls" 	g2="g" 	k="20" />
<hkern g1="germandbls" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="germandbls" 	g2="nine" 	k="51" />
<hkern g1="germandbls" 	g2="one" 	k="102" />
<hkern g1="germandbls" 	g2="question" 	k="61" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="72" />
<hkern g1="germandbls" 	g2="seven" 	k="61" />
<hkern g1="germandbls" 	g2="t" 	k="20" />
<hkern g1="germandbls" 	g2="trademark" 	k="61" />
<hkern g1="germandbls" 	g2="v,y,yacute,ydieresis" 	k="25" />
<hkern g1="germandbls" 	g2="w" 	k="4" />
<hkern g1="germandbls" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="41" />
<hkern g1="germandbls" 	g2="underscore" 	k="41" />
<hkern g1="germandbls" 	g2="x" 	k="25" />
<hkern g1="i" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-29" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="asterisk,registered" 	k="-68" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="backslash" 	k="-47" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="l" 	k="-18" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="question" 	k="-27" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-55" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="seven" 	k="-47" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="trademark" 	k="-115" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-27" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="braceright" 	k="-96" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="bracketright" 	k="-96" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="j" 	k="-29" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="five" 	k="-18" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="exclam" 	k="-76" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="parenright" 	k="-96" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="b,h,k,germandbls,thorn" 	k="-27" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="i" 	k="-29" />
<hkern g1="l,uniFB02" 	g2="asterisk,registered" 	k="20" />
<hkern g1="l,uniFB02" 	g2="backslash" 	k="41" />
<hkern g1="l,uniFB02" 	g2="bullet" 	k="20" />
<hkern g1="l,uniFB02" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="l,uniFB02" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="l,uniFB02" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="l,uniFB02" 	g2="one" 	k="49" />
<hkern g1="l,uniFB02" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-8" />
<hkern g1="l,uniFB02" 	g2="question" 	k="31" />
<hkern g1="l,uniFB02" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="31" />
<hkern g1="l,uniFB02" 	g2="seven" 	k="29" />
<hkern g1="l,uniFB02" 	g2="trademark" 	k="31" />
<hkern g1="l,uniFB02" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="l,uniFB02" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="31" />
<hkern g1="l,uniFB02" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="l,uniFB02" 	g2="braceright" 	k="-8" />
<hkern g1="l,uniFB02" 	g2="bracketright" 	k="-8" />
<hkern g1="l,uniFB02" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="l,uniFB02" 	g2="z" 	k="-20" />
<hkern g1="l,uniFB02" 	g2="exclam" 	k="-8" />
<hkern g1="h,m,n,ntilde" 	g2="backslash" 	k="72" />
<hkern g1="h,m,n,ntilde" 	g2="one" 	k="61" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="61" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="seven" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="v,y,yacute,ydieresis" 	k="14" />
<hkern g1="h,m,n,ntilde" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="q" 	g2="backslash" 	k="51" />
<hkern g1="q" 	g2="question" 	k="41" />
<hkern g1="q" 	g2="trademark" 	k="20" />
<hkern g1="q" 	g2="j" 	k="-18" />
<hkern g1="r" 	g2="ampersand" 	k="82" />
<hkern g1="r" 	g2="asterisk,registered" 	k="-39" />
<hkern g1="r" 	g2="backslash" 	k="102" />
<hkern g1="r" 	g2="bullet" 	k="10" />
<hkern g1="r" 	g2="eight" 	k="20" />
<hkern g1="r" 	g2="four" 	k="61" />
<hkern g1="r" 	g2="g" 	k="31" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="r" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="68" />
<hkern g1="r" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="r" 	g2="one" 	k="-20" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="r" 	g2="question" 	k="61" />
<hkern g1="r" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-8" />
<hkern g1="r" 	g2="trademark" 	k="41" />
<hkern g1="r" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-41" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="72" />
<hkern g1="r" 	g2="slash" 	k="123" />
<hkern g1="r" 	g2="j" 	k="20" />
<hkern g1="r" 	g2="s" 	k="31" />
<hkern g1="r" 	g2="two" 	k="10" />
<hkern g1="r" 	g2="underscore" 	k="164" />
<hkern g1="r" 	g2="at" 	k="31" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="r" 	g2="z" 	k="20" />
<hkern g1="r" 	g2="five" 	k="20" />
<hkern g1="r" 	g2="three" 	k="41" />
<hkern g1="r" 	g2="numbersign" 	k="41" />
<hkern g1="s" 	g2="backslash" 	k="82" />
<hkern g1="s" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="s" 	g2="nine" 	k="20" />
<hkern g1="s" 	g2="one" 	k="61" />
<hkern g1="s" 	g2="question" 	k="61" />
<hkern g1="s" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="31" />
<hkern g1="s" 	g2="seven" 	k="51" />
<hkern g1="s" 	g2="trademark" 	k="41" />
<hkern g1="s" 	g2="v,y,yacute,ydieresis" 	k="35" />
<hkern g1="s" 	g2="w" 	k="14" />
<hkern g1="s" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="20" />
<hkern g1="s" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="s" 	g2="s" 	k="20" />
<hkern g1="s" 	g2="two" 	k="20" />
<hkern g1="s" 	g2="underscore" 	k="41" />
<hkern g1="s" 	g2="x" 	k="20" />
<hkern g1="s" 	g2="five" 	k="20" />
<hkern g1="t" 	g2="backslash" 	k="82" />
<hkern g1="t" 	g2="bullet" 	k="41" />
<hkern g1="t" 	g2="four" 	k="41" />
<hkern g1="t" 	g2="g" 	k="31" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="t" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="82" />
<hkern g1="t" 	g2="l" 	k="20" />
<hkern g1="t" 	g2="nine" 	k="10" />
<hkern g1="t" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="t" 	g2="one" 	k="41" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-8" />
<hkern g1="t" 	g2="question" 	k="61" />
<hkern g1="t" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="31" />
<hkern g1="t" 	g2="seven" 	k="41" />
<hkern g1="t" 	g2="six" 	k="20" />
<hkern g1="t" 	g2="trademark" 	k="41" />
<hkern g1="t" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="t" 	g2="zero" 	k="20" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="t" 	g2="bracketright" 	k="-20" />
<hkern g1="t" 	g2="at" 	k="20" />
<hkern g1="t" 	g2="colon,semicolon" 	k="-8" />
<hkern g1="t" 	g2="exclam" 	k="-8" />
<hkern g1="t" 	g2="numbersign" 	k="20" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="backslash" 	k="51" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="one" 	k="41" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="question" 	k="41" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="seven" 	k="41" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="trademark" 	k="31" />
<hkern g1="v,y,yacute,ydieresis" 	g2="ampersand" 	k="29" />
<hkern g1="v,y,yacute,ydieresis" 	g2="asterisk,registered" 	k="-18" />
<hkern g1="v,y,yacute,ydieresis" 	g2="backslash" 	k="41" />
<hkern g1="v,y,yacute,ydieresis" 	g2="four" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="g" 	k="4" />
<hkern g1="v,y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="v,y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="v,y,yacute,ydieresis" 	g2="question" 	k="61" />
<hkern g1="v,y,yacute,ydieresis" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-18" />
<hkern g1="v,y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="33" />
<hkern g1="v,y,yacute,ydieresis" 	g2="s" 	k="14" />
<hkern g1="v,y,yacute,ydieresis" 	g2="two" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="underscore" 	k="61" />
<hkern g1="v,y,yacute,ydieresis" 	g2="colon,semicolon" 	k="-8" />
<hkern g1="v,y,yacute,ydieresis" 	g2="exclam" 	k="-18" />
<hkern g1="v,y,yacute,ydieresis" 	g2="three" 	k="29" />
<hkern g1="w" 	g2="asterisk,registered" 	k="-18" />
<hkern g1="w" 	g2="backslash" 	k="41" />
<hkern g1="w" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="w" 	g2="question" 	k="41" />
<hkern g1="w" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-8" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="w" 	g2="underscore" 	k="41" />
<hkern g1="w" 	g2="colon,semicolon" 	k="-8" />
<hkern g1="w" 	g2="exclam" 	k="-8" />
<hkern g1="w" 	g2="three" 	k="20" />
<hkern g1="k,x" 	g2="ampersand" 	k="41" />
<hkern g1="k,x" 	g2="asterisk,registered" 	k="-8" />
<hkern g1="k,x" 	g2="backslash" 	k="41" />
<hkern g1="k,x" 	g2="bullet" 	k="41" />
<hkern g1="k,x" 	g2="less,equal,greater,multiply" 	k="31" />
<hkern g1="k,x" 	g2="four" 	k="31" />
<hkern g1="k,x" 	g2="g" 	k="14" />
<hkern g1="k,x" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="k,x" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="72" />
<hkern g1="k,x" 	g2="nine" 	k="20" />
<hkern g1="k,x" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="k,x" 	g2="one" 	k="31" />
<hkern g1="k,x" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-8" />
<hkern g1="k,x" 	g2="question" 	k="51" />
<hkern g1="k,x" 	g2="six" 	k="20" />
<hkern g1="k,x" 	g2="trademark" 	k="41" />
<hkern g1="k,x" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="k,x" 	g2="zero" 	k="8" />
<hkern g1="k,x" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-8" />
<hkern g1="k,x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="k,x" 	g2="braceright" 	k="-18" />
<hkern g1="k,x" 	g2="bracketright" 	k="-18" />
<hkern g1="k,x" 	g2="s" 	k="10" />
<hkern g1="k,x" 	g2="at" 	k="20" />
<hkern g1="k,x" 	g2="colon,semicolon" 	k="-8" />
<hkern g1="k,x" 	g2="five" 	k="20" />
<hkern g1="k,x" 	g2="exclam" 	k="-8" />
<hkern g1="ampersand" 	g2="J" 	k="12" />
<hkern g1="ampersand" 	g2="T" 	k="162" />
<hkern g1="ampersand" 	g2="V" 	k="80" />
<hkern g1="ampersand" 	g2="W" 	k="29" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="141" />
<hkern g1="ampersand" 	g2="Z" 	k="20" />
<hkern g1="ampersand" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="ampersand" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="ampersand" 	g2="five" 	k="41" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="164" />
<hkern g1="ampersand" 	g2="v,y,yacute,ydieresis" 	k="41" />
<hkern g1="ampersand" 	g2="w" 	k="41" />
<hkern g1="at" 	g2="T" 	k="102" />
<hkern g1="at" 	g2="V" 	k="31" />
<hkern g1="at" 	g2="W" 	k="20" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="at" 	g2="Z" 	k="41" />
<hkern g1="at" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="41" />
<hkern g1="at" 	g2="AE" 	k="41" />
<hkern g1="at" 	g2="X" 	k="61" />
<hkern g1="at" 	g2="s" 	k="20" />
<hkern g1="at" 	g2="x" 	k="20" />
<hkern g1="at" 	g2="z" 	k="20" />
<hkern g1="degree" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="164" />
<hkern g1="less,equal,greater,multiply" 	g2="J" 	k="12" />
<hkern g1="less,equal,greater,multiply" 	g2="Z" 	k="51" />
<hkern g1="less,equal,greater,multiply" 	g2="X" 	k="61" />
<hkern g1="less,equal,greater,multiply" 	g2="x" 	k="31" />
<hkern g1="percent" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="123" />
<hkern g1="sterling" 	g2="five" 	k="31" />
<hkern g1="sterling" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="yen" 	g2="five" 	k="20" />
<hkern g1="yen" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="yen" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="yen" 	g2="dollar,S" 	k="41" />
<hkern g1="yen" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="asterisk,registered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="asterisk,registered" 	g2="AE" 	k="102" />
<hkern g1="asterisk,registered" 	g2="T" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="V" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="W" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="asterisk,registered" 	g2="f,uniFB01,uniFB02" 	k="-18" />
<hkern g1="asterisk,registered" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-68" />
<hkern g1="asterisk,registered" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="asterisk,registered" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="asterisk,registered" 	g2="t" 	k="-18" />
<hkern g1="asterisk,registered" 	g2="v,y,yacute,ydieresis" 	k="-18" />
<hkern g1="asterisk,registered" 	g2="w" 	k="-18" />
<hkern g1="asterisk,registered" 	g2="x" 	k="-8" />
<hkern g1="backslash" 	g2="T" 	k="102" />
<hkern g1="backslash" 	g2="V" 	k="61" />
<hkern g1="backslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="backslash" 	g2="f,uniFB01,uniFB02" 	k="61" />
<hkern g1="backslash" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="205" />
<hkern g1="backslash" 	g2="t" 	k="41" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="backslash" 	g2="Z" 	k="41" />
<hkern g1="backslash" 	g2="j" 	k="-41" />
<hkern g1="braceleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-18" />
<hkern g1="braceleft" 	g2="AE" 	k="-18" />
<hkern g1="braceleft" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-96" />
<hkern g1="braceleft" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="braceleft" 	g2="x" 	k="-18" />
<hkern g1="braceleft" 	g2="j" 	k="-55" />
<hkern g1="braceleft" 	g2="Iacute,Icircumflex,Idieresis" 	k="-76" />
<hkern g1="braceleft" 	g2="J" 	k="-18" />
<hkern g1="braceleft" 	g2="X" 	k="-8" />
<hkern g1="bracketleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-18" />
<hkern g1="bracketleft" 	g2="AE" 	k="-27" />
<hkern g1="bracketleft" 	g2="T" 	k="-8" />
<hkern g1="bracketleft" 	g2="V" 	k="-27" />
<hkern g1="bracketleft" 	g2="W" 	k="-8" />
<hkern g1="bracketleft" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-96" />
<hkern g1="bracketleft" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="bracketleft" 	g2="x" 	k="-18" />
<hkern g1="bracketleft" 	g2="Y,Yacute,Ydieresis" 	k="-18" />
<hkern g1="bracketleft" 	g2="j" 	k="-66" />
<hkern g1="bracketleft" 	g2="Iacute,Icircumflex,Idieresis" 	k="-76" />
<hkern g1="bracketleft" 	g2="J" 	k="-18" />
<hkern g1="bracketleft" 	g2="X" 	k="-18" />
<hkern g1="bullet" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="bullet" 	g2="T" 	k="102" />
<hkern g1="bullet" 	g2="V" 	k="20" />
<hkern g1="bullet" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="bullet" 	g2="x" 	k="41" />
<hkern g1="bullet" 	g2="Y,Yacute,Ydieresis" 	k="80" />
<hkern g1="bullet" 	g2="Z" 	k="82" />
<hkern g1="bullet" 	g2="X" 	k="39" />
<hkern g1="bullet" 	g2="dollar,S" 	k="51" />
<hkern g1="bullet" 	g2="five" 	k="20" />
<hkern g1="bullet" 	g2="z" 	k="10" />
<hkern g1="exclam" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-4" />
<hkern g1="exclamdown" 	g2="T" 	k="61" />
<hkern g1="exclamdown" 	g2="V" 	k="41" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="exclamdown" 	g2="j" 	k="-55" />
<hkern g1="exclamdown" 	g2="J" 	k="-18" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Z" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="dollar,S" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="five" 	k="41" />
<hkern g1="numbersign" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="numbersign" 	g2="AE" 	k="49" />
<hkern g1="numbersign" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="numbersign" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-59" />
<hkern g1="numbersign" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="numbersign" 	g2="v,y,yacute,ydieresis" 	k="-20" />
<hkern g1="numbersign" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="numbersign" 	g2="Z" 	k="31" />
<hkern g1="numbersign" 	g2="j" 	k="20" />
<hkern g1="numbersign" 	g2="X" 	k="20" />
<hkern g1="numbersign" 	g2="dollar,S" 	k="31" />
<hkern g1="numbersign" 	g2="z" 	k="20" />
<hkern g1="numbersign" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="numbersign" 	g2="g" 	k="20" />
<hkern g1="numbersign" 	g2="m,n,p,r,mu,ntilde" 	k="10" />
<hkern g1="numbersign" 	g2="s" 	k="31" />
<hkern g1="numbersign" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="parenleft" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-96" />
<hkern g1="parenleft" 	g2="j" 	k="-74" />
<hkern g1="parenleft" 	g2="Iacute,Icircumflex,Idieresis" 	k="-88" />
<hkern g1="parenleft" 	g2="J" 	k="-41" />
<hkern g1="question" 	g2="v,y,yacute,ydieresis" 	k="-18" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="questiondown" 	g2="AE" 	k="41" />
<hkern g1="questiondown" 	g2="T" 	k="164" />
<hkern g1="questiondown" 	g2="V" 	k="82" />
<hkern g1="questiondown" 	g2="W" 	k="61" />
<hkern g1="questiondown" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="49" />
<hkern g1="questiondown" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="questiondown" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="questiondown" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="102" />
<hkern g1="questiondown" 	g2="t" 	k="61" />
<hkern g1="questiondown" 	g2="v,y,yacute,ydieresis" 	k="82" />
<hkern g1="questiondown" 	g2="w" 	k="41" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="182" />
<hkern g1="questiondown" 	g2="Z" 	k="31" />
<hkern g1="questiondown" 	g2="j" 	k="-27" />
<hkern g1="questiondown" 	g2="X" 	k="41" />
<hkern g1="questiondown" 	g2="dollar,S" 	k="61" />
<hkern g1="questiondown" 	g2="five" 	k="41" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="questiondown" 	g2="m,n,p,r,mu,ntilde" 	k="61" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="slash" 	g2="AE" 	k="82" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="102" />
<hkern g1="slash" 	g2="f,uniFB01,uniFB02" 	k="61" />
<hkern g1="slash" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="slash" 	g2="t" 	k="61" />
<hkern g1="slash" 	g2="v,y,yacute,ydieresis" 	k="41" />
<hkern g1="slash" 	g2="w" 	k="41" />
<hkern g1="slash" 	g2="x" 	k="61" />
<hkern g1="slash" 	g2="Z" 	k="41" />
<hkern g1="slash" 	g2="Iacute,Icircumflex,Idieresis" 	k="-18" />
<hkern g1="slash" 	g2="X" 	k="41" />
<hkern g1="slash" 	g2="five" 	k="41" />
<hkern g1="slash" 	g2="z" 	k="61" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="slash" 	g2="g" 	k="61" />
<hkern g1="slash" 	g2="m,n,p,r,mu,ntilde" 	k="61" />
<hkern g1="slash" 	g2="s" 	k="61" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="61" />
<hkern g1="underscore" 	g2="T" 	k="102" />
<hkern g1="underscore" 	g2="V" 	k="102" />
<hkern g1="underscore" 	g2="W" 	k="123" />
<hkern g1="underscore" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="underscore" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="underscore" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="underscore" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="205" />
<hkern g1="underscore" 	g2="t" 	k="41" />
<hkern g1="underscore" 	g2="v,y,yacute,ydieresis" 	k="61" />
<hkern g1="underscore" 	g2="w" 	k="41" />
<hkern g1="underscore" 	g2="Y,Yacute,Ydieresis" 	k="164" />
<hkern g1="underscore" 	g2="j" 	k="-74" />
<hkern g1="underscore" 	g2="J" 	k="-49" />
<hkern g1="underscore" 	g2="dollar,S" 	k="41" />
<hkern g1="underscore" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="82" />
<hkern g1="underscore" 	g2="g" 	k="-18" />
<hkern g1="underscore" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="underscore" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="61" />
<hkern g1="underscore" 	g2="l" 	k="20" />
<hkern g1="eight" 	g2="J" 	k="20" />
<hkern g1="eight" 	g2="dollar,S" 	k="20" />
<hkern g1="eight" 	g2="T" 	k="41" />
<hkern g1="eight" 	g2="V" 	k="20" />
<hkern g1="eight" 	g2="X" 	k="18" />
<hkern g1="eight" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="eight" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="31" />
<hkern g1="eight" 	g2="v,y,yacute,ydieresis" 	k="14" />
<hkern g1="four" 	g2="J" 	k="8" />
<hkern g1="four" 	g2="dollar,S" 	k="41" />
<hkern g1="four" 	g2="T" 	k="102" />
<hkern g1="four" 	g2="V" 	k="41" />
<hkern g1="four" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="four" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="102" />
<hkern g1="four" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="four" 	g2="W" 	k="20" />
<hkern g1="four" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="four" 	g2="five" 	k="20" />
<hkern g1="four" 	g2="x" 	k="20" />
<hkern g1="nine" 	g2="J" 	k="20" />
<hkern g1="nine" 	g2="dollar,S" 	k="20" />
<hkern g1="nine" 	g2="T" 	k="41" />
<hkern g1="nine" 	g2="V" 	k="10" />
<hkern g1="nine" 	g2="X" 	k="8" />
<hkern g1="nine" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="nine" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="nine" 	g2="AE" 	k="41" />
<hkern g1="nine" 	g2="Z" 	k="41" />
<hkern g1="nine" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="one" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-41" />
<hkern g1="seven" 	g2="J" 	k="41" />
<hkern g1="seven" 	g2="dollar,S" 	k="41" />
<hkern g1="seven" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-18" />
<hkern g1="seven" 	g2="five" 	k="31" />
<hkern g1="seven" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="seven" 	g2="AE" 	k="102" />
<hkern g1="seven" 	g2="Z" 	k="31" />
<hkern g1="seven" 	g2="s" 	k="51" />
<hkern g1="seven" 	g2="z" 	k="41" />
<hkern g1="seven" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="61" />
<hkern g1="seven" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-47" />
<hkern g1="seven" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="seven" 	g2="g" 	k="51" />
<hkern g1="seven" 	g2="m,n,p,r,mu,ntilde" 	k="20" />
<hkern g1="seven" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="seven" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="six" 	g2="J" 	k="20" />
<hkern g1="six" 	g2="T" 	k="102" />
<hkern g1="six" 	g2="V" 	k="41" />
<hkern g1="six" 	g2="X" 	k="20" />
<hkern g1="six" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="six" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="41" />
<hkern g1="six" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="six" 	g2="five" 	k="10" />
<hkern g1="six" 	g2="x" 	k="20" />
<hkern g1="three" 	g2="T" 	k="51" />
<hkern g1="three" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="two" 	g2="J" 	k="31" />
<hkern g1="two" 	g2="T" 	k="41" />
<hkern g1="two" 	g2="V" 	k="20" />
<hkern g1="two" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="two" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="two" 	g2="W" 	k="10" />
<hkern g1="two" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="two" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="two" 	g2="g" 	k="41" />
<hkern g1="two" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="zero" 	g2="T" 	k="61" />
<hkern g1="zero" 	g2="X" 	k="8" />
<hkern g1="zero" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="zero" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="zero" 	g2="x" 	k="8" />
<hkern g1="zero" 	g2="Z" 	k="20" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="T" 	k="123" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="V" 	k="61" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="Y,Yacute,Ydieresis" 	k="141" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="102" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="five" 	k="-8" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="V" 	k="-20" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="X" 	k="18" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="v,y,yacute,ydieresis" 	k="-18" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="x" 	k="-8" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="35" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="AE" 	k="82" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-27" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="t" 	k="-8" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="w" 	k="-8" />
<hkern g1="at" 	g2="colon,semicolon" 	k="31" />
<hkern g1="at" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="copyright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="degree" 	g2="colon,semicolon" 	k="225" />
<hkern g1="degree" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="184" />
<hkern g1="degree" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="410" />
<hkern g1="Euro" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="82" />
<hkern g1="sterling" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="61" />
<hkern g1="trademark" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="yen" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="41" />
<hkern g1="yen" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="asterisk,registered" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="205" />
<hkern g1="backslash" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="123" />
<hkern g1="bracketleft" 	g2="colon,semicolon" 	k="-8" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-8" />
<hkern g1="colon,semicolon" 	g2="AE" 	k="-41" />
<hkern g1="colon,semicolon" 	g2="T" 	k="82" />
<hkern g1="colon,semicolon" 	g2="V" 	k="20" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="111" />
<hkern g1="colon,semicolon" 	g2="Z" 	k="-8" />
<hkern g1="colon,semicolon" 	g2="backslash" 	k="102" />
<hkern g1="colon,semicolon" 	g2="bracketright" 	k="-8" />
<hkern g1="colon,semicolon" 	g2="Euro" 	k="-8" />
<hkern g1="colon,semicolon" 	g2="f,uniFB01,uniFB02" 	k="-8" />
<hkern g1="colon,semicolon" 	g2="five" 	k="-8" />
<hkern g1="colon,semicolon" 	g2="four" 	k="10" />
<hkern g1="colon,semicolon" 	g2="one" 	k="82" />
<hkern g1="colon,semicolon" 	g2="question" 	k="59" />
<hkern g1="colon,semicolon" 	g2="s" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="51" />
<hkern g1="colon,semicolon" 	g2="t" 	k="-8" />
<hkern g1="colon,semicolon" 	g2="three" 	k="-18" />
<hkern g1="colon,semicolon" 	g2="v,y,yacute,ydieresis" 	k="-8" />
<hkern g1="colon,semicolon" 	g2="w" 	k="-8" />
<hkern g1="colon,semicolon" 	g2="x" 	k="-8" />
<hkern g1="colon,semicolon" 	g2="z" 	k="-8" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="102" />
<hkern g1="guillemotright,guilsinglright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="AE" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="T" 	k="102" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="V" 	k="51" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="111" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="Z" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="backslash" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="f,uniFB01,uniFB02" 	k="31" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="five" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="four" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="one" 	k="143" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="question" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="seven" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="t" 	k="10" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="three" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="x" 	k="72" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="z" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="J" 	k="18" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="dollar,S" 	k="82" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="X" 	k="70" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="ampersand" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="guillemotright,guilsinglright" 	k="102" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="j" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="nine" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="percent" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="questiondown" 	k="164" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="slash" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="trademark" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="two" 	k="125" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="underscore" 	k="164" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="yen" 	k="41" />
<hkern g1="numbersign" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="numbersign" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="102" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="AE" 	k="-18" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="123" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="102" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="182" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Z" 	k="-8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="backslash" 	k="287" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="bracketright" 	k="-8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Euro" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="f,uniFB01,uniFB02" 	k="29" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="five" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="four" 	k="68" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="one" 	k="205" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="question" 	k="102" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="s" 	k="-8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="t" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="three" 	k="-18" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v,y,yacute,ydieresis" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="x" 	k="-8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="z" 	k="-8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="ampersand" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="nine" 	k="51" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="percent" 	k="205" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="trademark" 	k="164" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="yen" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk,registered" 	k="205" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="copyright" 	k="164" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="g" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="205" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="six" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="zero" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="102" />
<hkern g1="question" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="205" />
<hkern g1="question" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="102" />
<hkern g1="questiondown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="123" />
<hkern g1="questiondown" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="123" />
<hkern g1="questiondown" 	g2="colon,semicolon" 	k="61" />
<hkern g1="slash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="287" />
<hkern g1="slash" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="123" />
<hkern g1="slash" 	g2="colon,semicolon" 	k="102" />
<hkern g1="underscore" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="164" />
<hkern g1="four" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="nine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="113" />
<hkern g1="seven" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="78" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="51" />
<hkern g1="seven" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="184" />
<hkern g1="six" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="two" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="53" />
<hkern g1="zero" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="25" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
</font>
</defs></svg> 