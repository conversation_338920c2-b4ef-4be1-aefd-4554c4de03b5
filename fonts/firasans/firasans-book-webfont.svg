<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="fira_sansbook" horiz-adv-x="1189" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="548" />
<glyph unicode="&#xfb01;" horiz-adv-x="1171" d="M27 944v133h172v150q0 141 89.5 223t268.5 82q156 0 301 -74l-61 -121q-112 56 -234 56q-107 0 -149.5 -39.5t-42.5 -126.5v-150h602v-1077h-172v944h-430v-944h-172v944h-172z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1208" d="M27 944v133h172v154q0 138 89 219.5t259 81.5q125 0 256 -41l172 22v-1304q0 -46 16.5 -68t55.5 -22t69 12l45 -121q-72 -35 -151 -35q-99 0 -153 59t-54 173v1149q-118 37 -246 37q-101 0 -143.5 -38t-42.5 -122v-156h266l-19 -133h-247v-944h-172v944h-172z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode="&#xd;" horiz-adv-x="548" />
<glyph unicode=" "  horiz-adv-x="548" />
<glyph unicode="&#x09;" horiz-adv-x="548" />
<glyph unicode="&#xa0;" horiz-adv-x="548" />
<glyph unicode="!" horiz-adv-x="493" d="M115 109q0 54 38 92.5t93 38.5q56 0 94.5 -38t38.5 -93q0 -56 -38 -95t-95 -39q-55 0 -93 39t-38 95zM150 1409h188l-16 -944h-154z" />
<glyph unicode="&#x22;" horiz-adv-x="798" d="M129 1409h184l-28 -530h-129zM487 1409h185l-29 -530h-129z" />
<glyph unicode="#" horiz-adv-x="1054" d="M31 342v131h151l60 445h-136v133h154l41 317h148l-41 -317h329l41 317h148l-41 -317h139v-133h-158l-59 -445h141v-131h-157l-46 -342h-147l45 342h-330l-45 -342h-147l45 342h-135zM330 473h329l60 445h-330z" />
<glyph unicode="$" horiz-adv-x="1085" d="M57 152l103 110q150 -143 354 -143q132 0 217.5 66.5t85.5 187.5q0 106 -63 163t-240 111q-210 64 -305.5 151.5t-95.5 237.5q0 141 100.5 238t261.5 112v300h150v-300q101 -12 178.5 -49t150.5 -104l-100 -109q-131 123 -309 123q-113 0 -183.5 -53t-70.5 -150 q0 -93 61 -145t242 -106q75 -23 129 -45.5t108.5 -57t88 -75.5t54.5 -101t21 -135q0 -157 -97.5 -263t-272.5 -132v-299h-150v290q-249 14 -418 177z" />
<glyph unicode="%" horiz-adv-x="1675" d="M96 1047q0 156 91.5 249t234.5 93q145 0 236.5 -93t91.5 -249t-92 -250.5t-236 -94.5q-143 0 -234.5 94.5t-91.5 250.5zM248 1047q0 -97 40.5 -161.5t133.5 -64.5t132.5 63.5t39.5 162.5q0 45 -8 82.5t-27 71t-54 52.5t-83 19q-49 0 -84 -18.5t-54 -51.5t-27.5 -71 t-8.5 -84zM305 18l948 1434l121 -78l-948 -1433zM926 319q0 156 91 249.5t236 93.5q144 0 235 -93.5t91 -249.5t-91 -250t-235 -94q-143 0 -235 94t-92 250zM1081 319q0 -45 8.5 -83t27.5 -71t53.5 -52t82.5 -19q174 0 174 225q0 45 -8.5 82.5t-27 70.5t-54 52t-84.5 19 q-92 0 -132 -63t-40 -161z" />
<glyph unicode="&#x26;" horiz-adv-x="1488" d="M170 356q0 127 73 222.5t216 185.5q-106 101 -154.5 177.5t-48.5 174.5q0 138 97 228t265 90q162 0 264.5 -88t102.5 -222q0 -63 -22 -117.5t-65 -100.5t-91.5 -82t-116.5 -76l375 -353q84 174 125 357l162 -45q-70 -235 -181 -412l236 -219l-125 -101l-207 199 q-177 -199 -452 -199q-200 0 -326.5 104.5t-126.5 276.5zM346 362q0 -115 82 -182t213 -67q190 0 334 157l-418 400q-108 -69 -159.5 -142.5t-51.5 -165.5zM426 1118q0 -73 38.5 -132.5t127.5 -143.5q112 66 167.5 129.5t55.5 142.5q0 90 -52.5 140.5t-141.5 50.5 q-91 0 -143 -52.5t-52 -134.5z" />
<glyph unicode="'" horiz-adv-x="442" d="M129 1409h184l-28 -530h-129z" />
<glyph unicode="(" horiz-adv-x="663" d="M88 717q0 79 7.5 153.5t16.5 134.5t30.5 126.5t36.5 110.5t48.5 107t51 96t60.5 96.5t61.5 90t66.5 94.5l108 -73q-63 -93 -103.5 -160.5t-83 -158t-66.5 -176.5t-39 -198.5t-15 -242.5q0 -129 15 -241.5t39 -199t66.5 -176.5t83 -158t103.5 -161l-108 -74 q-48 67 -67 94.5t-61 90.5t-60.5 96.5t-51.5 96t-48 107t-36.5 110.5t-30.5 126.5t-16.5 135t-7.5 153.5z" />
<glyph unicode=")" horiz-adv-x="663" d="M88 -219q63 93 103.5 161t83 158t66.5 176.5t39 199t15 241.5q0 130 -15 242.5t-39 198.5t-66.5 176.5t-83 158t-103.5 160.5l109 73q48 -67 67 -94.5t61 -90t60.5 -96.5t51 -96t48 -106.5t36.5 -110.5t30.5 -126.5t16.5 -135t7 -153.5t-7 -153.5t-16.5 -135 t-30.5 -126.5t-36.5 -111t-48 -106.5t-51.5 -96t-60 -96.5t-61 -90.5t-67 -94.5z" />
<glyph unicode="*" horiz-adv-x="897" d="M47 1159l51 154l293 -125l-26 323h167l-26 -321l291 125l53 -156l-311 -72l209 -239l-136 -100l-163 274l-164 -274l-135 98l208 241z" />
<glyph unicode="+" horiz-adv-x="1019" d="M131 606v144h299v307h158v-307h301v-144h-301v-307h-158v307h-299z" />
<glyph unicode="," horiz-adv-x="475" d="M78 -334l82 332q-56 42 -56 111q0 54 38.5 92.5t93.5 38.5q58 0 95.5 -38t37.5 -93q0 -61 -35 -138l-133 -305h-123z" />
<glyph unicode="-" horiz-adv-x="823" d="M127 567v148h571v-148h-571z" />
<glyph unicode="." horiz-adv-x="475" d="M104 109q0 54 38.5 92.5t93.5 38.5q58 0 95.5 -38t37.5 -93q0 -56 -37.5 -95t-95.5 -39q-55 0 -93.5 39t-38.5 95z" />
<glyph unicode="/" horiz-adv-x="1064" d="M221 -174l475 1825l148 -35l-473 -1827z" />
<glyph unicode="0" horiz-adv-x="1138" d="M119 684q0 345 113 525t337 180t337.5 -180.5t113.5 -526.5q0 -344 -113.5 -525.5t-337.5 -181.5t-337 181.5t-113 527.5zM297 684q0 -300 67.5 -435.5t204.5 -135.5q136 0 204.5 136t68.5 433q0 298 -68.5 433.5t-204.5 135.5q-137 0 -204.5 -134.5t-67.5 -432.5z" />
<glyph unicode="1" horiz-adv-x="876" d="M76 1108l420 260h151v-1368h-172v1184l-323 -199z" />
<glyph unicode="2" horiz-adv-x="1005" d="M55 1186q167 203 410 203q183 0 293 -105t110 -266q0 -56 -11 -108.5t-29.5 -101t-57.5 -107.5t-78.5 -111t-110 -129t-134.5 -143.5t-168 -172.5h622l-20 -145h-799v137q106 111 167 176t132 143.5t107.5 123t78 100t59.5 90.5t35 78.5t22 80.5t5 81q0 111 -63.5 174 t-167.5 63q-87 0 -153 -36t-136 -115z" />
<glyph unicode="3" horiz-adv-x="1015" d="M33 158l104 96q131 -137 297 -137q132 0 208.5 75t76.5 201q0 139 -71.5 197.5t-207.5 58.5h-100l20 133h70q109 0 181.5 65.5t72.5 184.5q0 101 -63.5 159t-171.5 58q-81 0 -148.5 -29t-138.5 -94l-90 103q174 160 387 160q185 0 292 -100.5t107 -246.5q0 -127 -73 -207 t-195 -108q136 -11 222.5 -97.5t86.5 -236.5q0 -183 -125.5 -300.5t-333.5 -117.5q-252 0 -407 183z" />
<glyph unicode="4" horiz-adv-x="1083" d="M84 342v125l414 922l143 -60l-375 -848h408l16 375h150v-375h178v-139h-178v-342h-168v342h-588z" />
<glyph unicode="5" horiz-adv-x="1019" d="M57 147l103 101q66 -67 136.5 -99t160.5 -32q133 0 210.5 85.5t77.5 239.5q0 153 -70.5 223t-189.5 70q-101 0 -200 -51h-135v684h729l-25 -135h-541v-418q107 55 224 55q175 0 281 -116t106 -316q0 -206 -128 -334.5t-339 -128.5q-231 0 -400 172z" />
<glyph unicode="6" horiz-adv-x="1087" d="M119 637q0 344 137 548t373 204q151 0 276 -82l-67 -115q-95 59 -211 59q-152 0 -241 -146t-95 -392q130 184 334 184q77 0 143.5 -27t118.5 -79.5t82 -136.5t30 -191q0 -220 -122 -354t-306 -134q-97 0 -173.5 32t-128.5 89.5t-86 141t-49 182.5t-15 217zM293 565 q6 -230 72 -341t206 -111q123 0 188.5 93.5t65.5 252.5q0 301 -229 301q-92 0 -169.5 -52t-133.5 -143z" />
<glyph unicode="7" horiz-adv-x="905" d="M53 1227v141h789v-129l-518 -1257l-158 51l502 1194h-615z" />
<glyph unicode="8" horiz-adv-x="1122" d="M96 360q0 129 70.5 216t206.5 145q-113 55 -166 133t-53 186q0 82 34.5 149.5t92 110.5t130 66t150.5 23q106 0 197 -37t152 -117.5t61 -190.5q0 -102 -57.5 -176t-178.5 -135q145 -61 218 -151t73 -217q0 -172 -131 -281t-338 -109q-208 0 -334.5 107.5t-126.5 277.5z M276 360q0 -116 75.5 -180.5t207.5 -64.5t209.5 66.5t77.5 180.5q0 111 -65 173t-234 122l-55 19q-111 -50 -163.5 -125.5t-52.5 -190.5zM324 1038q0 -98 59.5 -151.5t191.5 -98.5l41 -14q98 55 141.5 115.5t43.5 148.5q0 102 -63.5 162t-176.5 60q-109 0 -173 -59t-64 -163 z" />
<glyph unicode="9" horiz-adv-x="1071" d="M96 938q0 207 123.5 329t308.5 122q215 0 326 -143t111 -394q0 -167 -28.5 -294.5t-82 -220.5t-144 -164.5t-200 -121t-264.5 -94.5l-41 129q277 79 424 217t157 367q-50 -76 -131.5 -123t-181.5 -47q-163 0 -270 119.5t-107 318.5zM270 934q0 -146 65 -221.5t173 -75.5 q168 0 280 178q4 230 -58 333t-198 103q-125 0 -193.5 -82.5t-68.5 -234.5z" />
<glyph unicode=":" horiz-adv-x="475" d="M104 109q0 54 38.5 92.5t93.5 38.5q58 0 95.5 -38t37.5 -93q0 -56 -37.5 -95t-95.5 -39q-55 0 -93.5 39t-38.5 95zM104 872q0 54 38.5 93t93.5 39q58 0 95.5 -38.5t37.5 -93.5q0 -56 -37.5 -94.5t-95.5 -38.5q-55 0 -93.5 38.5t-38.5 94.5z" />
<glyph unicode=";" horiz-adv-x="475" d="M78 -334l82 332q-56 42 -56 111q0 54 38.5 92.5t93.5 38.5q58 0 95.5 -38t37.5 -93q0 -61 -35 -138l-133 -305h-123zM104 872q0 54 38.5 93t93.5 39q58 0 95.5 -38.5t37.5 -93.5q0 -56 -37.5 -94.5t-95.5 -38.5q-55 0 -93.5 38.5t-38.5 94.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="1021" d="M109 596v172l743 350l61 -143l-665 -293l665 -297l-61 -139z" />
<glyph unicode="=" horiz-adv-x="1019" d="M131 412v145h760v-145h-760zM131 799v145h760v-145h-760z" />
<glyph unicode="&#x3e;" horiz-adv-x="1021" d="M109 385l665 297l-665 293l63 143l741 -350v-172l-741 -350z" />
<glyph unicode="?" horiz-adv-x="933" d="M68 1233q171 201 417 201q179 0 281 -92.5t102 -223.5q0 -66 -20.5 -118t-53.5 -86.5t-72.5 -63.5t-79 -56.5t-72.5 -57.5t-53.5 -74.5t-20.5 -100.5v-96h-170v106q0 70 20 126t52 92t70.5 66t77 55.5t70.5 51.5t52 63t20 83q0 86 -57 134t-156 48q-167 0 -291 -147z M285 109q0 54 38 92.5t93 38.5q58 0 95.5 -38t37.5 -93q0 -56 -37.5 -95t-95.5 -39q-55 0 -93 39t-38 95z" />
<glyph unicode="@" horiz-adv-x="2088" d="M150 492q0 158 43 302t124.5 260t193.5 201.5t257.5 132t308.5 46.5q206 0 372 -65.5t271.5 -177.5t162 -256t56.5 -306q0 -178 -44 -313.5t-134.5 -215.5t-218.5 -80q-112 0 -176 63t-82 150q-96 -208 -297 -208q-161 0 -256.5 122t-95.5 326q0 257 122 394t320 137 q160 0 316 -80v-531q0 -128 37.5 -183.5t109.5 -55.5q225 0 225 471q0 199 -77.5 347.5t-234 233t-376.5 84.5q-171 0 -313 -60.5t-238 -167t-149 -253t-53 -317.5q0 -231 88 -406.5t259.5 -275t405.5 -99.5q197 0 391 68l48 -133q-115 -39 -214.5 -58.5t-226.5 -19.5 q-271 0 -480.5 112t-327 322.5t-117.5 489.5zM803 473q0 -157 53 -239t152 -82q156 0 229 184v510q-74 33 -156 33q-137 0 -207.5 -103.5t-70.5 -302.5z" />
<glyph unicode="A" horiz-adv-x="1169" d="M16 0l457 1409h225l455 -1409h-188l-109 360h-549l-110 -360h-181zM350 506h461l-229 756z" />
<glyph unicode="B" horiz-adv-x="1241" d="M211 0v1409h328q263 0 402.5 -87.5t139.5 -266.5q0 -125 -75 -206t-187 -104q68 -11 122 -32.5t102.5 -60t75 -103t26.5 -150.5q0 -399 -547 -399h-387zM387 143h211q85 0 146 11t111 39t75.5 79.5t25.5 126.5q0 141 -89 206t-232 65h-248v-527zM387 807h229 q126 0 204.5 61.5t78.5 173.5q0 126 -85 176t-257 50h-170v-461z" />
<glyph unicode="C" horiz-adv-x="1142" d="M119 705q0 175 45.5 315.5t125.5 229.5t184.5 136.5t225.5 47.5q123 0 207.5 -33t175.5 -105l-96 -114q-130 104 -276 104q-69 0 -127.5 -18.5t-110 -62t-88 -109t-57.5 -165.5t-21 -226q0 -150 31 -263.5t86 -181t126.5 -100.5t158.5 -33q91 0 159 30t144 87l88 -113 q-156 -156 -398 -156q-127 0 -233.5 48t-184.5 139t-121.5 230t-43.5 313z" />
<glyph unicode="D" horiz-adv-x="1312" d="M211 0v1409h291q80 0 149.5 -7.5t141.5 -28.5t130 -54t110 -87t87 -125.5t55.5 -171.5t20.5 -222q0 -120 -21 -221t-56.5 -173.5t-86.5 -128.5t-107.5 -91.5t-123.5 -58t-130.5 -31.5t-131.5 -9h-328zM387 141h166q73 0 134 13.5t122 52t103 100.5t68 165.5t26 240.5 q0 101 -14 183t-38 139t-60 99t-74.5 67t-88 40t-94 20t-98.5 5h-152v-1125z" />
<glyph unicode="E" horiz-adv-x="1085" d="M211 0v1409h752l-21 -143h-555v-475h481v-144h-481v-504h590v-143h-766z" />
<glyph unicode="F" horiz-adv-x="1001" d="M211 0v1409h737l-20 -143h-541v-496h467v-141h-467v-629h-176z" />
<glyph unicode="G" horiz-adv-x="1286" d="M119 707q0 173 49.5 313.5t133.5 229.5t192 136.5t229 47.5q130 0 225.5 -37t190.5 -115l-105 -111q-75 61 -146 88t-165 27q-81 0 -153 -32t-132 -98t-95.5 -181.5t-35.5 -267.5q0 -584 400 -584q156 0 274 65v443h-256l-20 145h452v-678q-212 -123 -450 -123 q-276 0 -432 187t-156 545z" />
<glyph unicode="H" horiz-adv-x="1388" d="M211 0v1409h176v-594h612v594h179v-1409h-179v668h-612v-668h-176z" />
<glyph unicode="I" horiz-adv-x="598" d="M211 0v1409h176v-1409h-176z" />
<glyph unicode="J" horiz-adv-x="618" d="M14 -143q136 55 181 128t45 218v1206h178v-1214q0 -105 -23 -182t-71.5 -130.5t-110 -89t-152.5 -65.5z" />
<glyph unicode="K" horiz-adv-x="1185" d="M211 0v1409h176v-1409h-176zM397 754l549 655h203l-547 -645l584 -764h-219z" />
<glyph unicode="L" horiz-adv-x="1009" d="M211 0v1409h176v-1253h582l-21 -156h-737z" />
<glyph unicode="M" horiz-adv-x="1589" d="M131 0l119 1409h242l309 -1042l295 1042h241l123 -1409h-172l-53 645q-36 400 -39 584l-317 -1067h-166l-332 1069q-3 -249 -33 -596l-49 -635h-168z" />
<glyph unicode="N" horiz-adv-x="1394" d="M211 0v1409h231l598 -1216q-22 215 -22 428v788h166v-1409h-238l-594 1214q25 -283 25 -534v-680h-166z" />
<glyph unicode="O" horiz-adv-x="1417" d="M119 700q0 343 162 538.5t428 195.5q268 0 428.5 -191.5t160.5 -540.5q0 -343 -160 -535t-429 -192t-429.5 189.5t-160.5 535.5zM307 700q0 -295 107 -436t295 -141q401 0 401 579q0 584 -401 584q-190 0 -296 -143t-106 -443z" />
<glyph unicode="P" horiz-adv-x="1183" d="M211 0v1409h360q258 0 399.5 -108t141.5 -324q0 -95 -26.5 -170.5t-74.5 -128t-116 -87.5t-148.5 -51t-175.5 -16h-184v-524h-176zM387 666h178q85 0 147.5 14.5t112 48.5t74.5 95.5t25 150.5q0 156 -92 225.5t-263 69.5h-182v-604z" />
<glyph unicode="Q" horiz-adv-x="1415" d="M119 700q0 343 162 538.5t428 195.5q268 0 428.5 -191.5t160.5 -540.5q0 -172 -41.5 -298.5t-112 -203.5t-175.5 -128q214 0 387 -129l-117 -150q-120 107 -232.5 147t-301.5 40q-266 0 -426 187.5t-160 532.5zM307 700q0 -295 107 -436t295 -141q401 0 401 579 q0 584 -401 584q-190 0 -296 -143t-106 -443z" />
<glyph unicode="R" horiz-adv-x="1230" d="M211 0v1409h362q261 0 395 -100t134 -295q0 -148 -77 -240t-230 -137l381 -637h-211l-344 606h-234v-606h-176zM387 743h207q157 0 238 64.5t81 206.5q0 134 -81 195t-261 61h-184v-527z" />
<glyph unicode="S" horiz-adv-x="1114" d="M57 156l99 110q85 -71 173 -107t199 -36q136 0 224 68.5t88 193.5q0 109 -65.5 168.5t-246.5 114.5q-216 66 -314.5 156.5t-98.5 246.5q0 159 122 261t314 102q139 0 238 -40t190 -122l-96 -109q-152 123 -324 123q-117 0 -189.5 -54.5t-72.5 -154.5q0 -96 63 -150.5 t250 -111.5q77 -23 132.5 -45.5t112 -58t91 -77.5t56.5 -104t22 -139q0 -187 -132.5 -301.5t-361.5 -114.5q-286 0 -473 181z" />
<glyph unicode="T" horiz-adv-x="1048" d="M31 1257v152h997l-20 -152h-396v-1257h-176v1257h-405z" />
<glyph unicode="U" horiz-adv-x="1351" d="M190 449v960h177v-946q0 -336 307 -336q157 0 232 84t75 252v946h180v-960q0 -213 -128 -343.5t-359 -130.5q-233 0 -358.5 130t-125.5 344z" />
<glyph unicode="V" horiz-adv-x="1134" d="M16 1409h193l362 -1219l365 1219h180l-450 -1409h-193z" />
<glyph unicode="W" horiz-adv-x="1683" d="M53 1409h176l236 -1251l280 1251h191l287 -1251l243 1251h164l-293 -1409h-227l-270 1202l-273 -1202h-221z" />
<glyph unicode="X" horiz-adv-x="1093" d="M12 0l426 752l-387 657h201l295 -539l293 539h190l-381 -647l432 -762h-202l-336 637l-338 -637h-193z" />
<glyph unicode="Y" horiz-adv-x="1114" d="M14 1409h197l352 -709l350 709h187l-453 -864v-545h-180v543z" />
<glyph unicode="Z" horiz-adv-x="1067" d="M63 0v143l717 1117h-641v149h834v-143l-711 -1114h707l-21 -152h-885z" />
<glyph unicode="[" horiz-adv-x="657" d="M143 -236v1903h404v-143h-242v-1618h242v-142h-404z" />
<glyph unicode="\" horiz-adv-x="1064" d="M221 1616l148 35l475 -1825l-150 -37z" />
<glyph unicode="]" horiz-adv-x="657" d="M111 -94h241v1618h-241v143h403v-1903h-403v142z" />
<glyph unicode="^" horiz-adv-x="1101" d="M90 1079l385 637h152l385 -637h-185l-278 486l-277 -486h-182z" />
<glyph unicode="_" horiz-adv-x="1064" d="M41 -133h983v-148h-983v148z" />
<glyph unicode="`" horiz-adv-x="602" d="M61 1485l78 147l402 -229l-54 -92z" />
<glyph unicode="a" horiz-adv-x="1110" d="M98 301q0 171 122 261.5t345 90.5h176v86q0 118 -56 170t-175 52q-120 0 -291 -60l-43 127q195 74 365 74q188 0 280 -91.5t92 -262.5v-500q0 -70 22 -103t69 -49l-41 -121q-82 11 -131 49t-70 117q-110 -166 -324 -166q-158 0 -249 89.5t-91 236.5zM281 309 q0 -99 50 -151t146 -52q172 0 264 177v256h-153q-307 0 -307 -230z" />
<glyph unicode="b" horiz-adv-x="1210" d="M201 0v1511l172 21v-592q120 162 309 162q185 0 291 -147.5t106 -413.5q0 -257 -112.5 -411.5t-307.5 -154.5q-167 0 -290 146l-19 -121h-149zM373 262q44 -68 109 -108.5t139 -40.5q128 0 201 105.5t73 322.5q0 424 -260 424q-153 0 -262 -172v-531z" />
<glyph unicode="c" horiz-adv-x="974" d="M121 530q0 260 126 416t341 156q186 0 323 -115l-84 -108q-107 79 -233 79q-136 0 -212.5 -107.5t-76.5 -318.5q0 -208 76 -308.5t213 -100.5q66 0 119.5 20t117.5 64l80 -113q-140 -119 -323 -119q-216 0 -341.5 147.5t-125.5 407.5z" />
<glyph unicode="d" horiz-adv-x="1218" d="M131 535q0 252 113.5 409.5t308.5 157.5q165 0 293 -135v565l172 -21v-1511h-150l-18 152q-53 -82 -133 -129.5t-180 -47.5q-192 0 -299 151.5t-107 408.5zM315 537q0 -212 66 -318t190 -106q90 0 154 42t121 128v536q-53 71 -114.5 108.5t-141.5 37.5 q-129 0 -202 -108.5t-73 -319.5z" />
<glyph unicode="e" horiz-adv-x="1112" d="M121 528q0 257 119.5 415.5t322.5 158.5q213 0 325.5 -140t112.5 -389q0 -48 -4 -94h-694q13 -187 92 -274.5t209 -87.5q80 0 145.5 22.5t137.5 73.5l76 -102q-168 -136 -371 -136q-222 0 -346.5 147.5t-124.5 405.5zM303 606h528v17q0 165 -66 253.5t-198 88.5 q-117 0 -185.5 -88t-78.5 -271z" />
<glyph unicode="f" horiz-adv-x="679" d="M27 944v133h190v154q0 136 86.5 218.5t241.5 82.5q131 0 256 -55l-56 -125q-93 41 -190 41q-89 0 -127.5 -38.5t-38.5 -121.5v-156h266l-18 -133h-248v-944h-172v944h-190z" />
<glyph unicode="g" horiz-adv-x="1056" d="M55 -111h156q0 -68 25 -106.5t91.5 -59t186.5 -20.5q173 0 247.5 44t74.5 132q0 78 -59 119t-165 41h-170q-134 0 -204 57.5t-70 143.5q0 113 117 188q-93 50 -137 123t-44 178q0 164 115.5 268.5t294.5 104.5q24 -1 47 -1q100 0 177 11q94 13 140.5 29t122.5 51l52 -158 q-94 -30 -312 -33q189 -83 189 -276q0 -161 -109.5 -262t-296.5 -101q-73 0 -137 21q-59 -40 -59 -107q0 -92 147 -92h172q162 0 262.5 -82.5t100.5 -210.5q0 -157 -125.5 -240t-370.5 -83q-253 0 -356 76.5t-103 244.5zM281 729q0 -116 62.5 -184t174.5 -68 q115 0 175.5 65.5t60.5 186.5t-60.5 184.5t-179.5 63.5q-112 0 -172.5 -68.5t-60.5 -179.5z" />
<glyph unicode="h" horiz-adv-x="1198" d="M201 0v1509l172 21v-606q131 178 327 178q145 0 227.5 -88.5t82.5 -241.5v-772h-172v748q0 119 -46 169t-135 50q-87 0 -155.5 -51t-128.5 -146v-770h-172z" />
<glyph unicode="i" horiz-adv-x="571" d="M164 1466q0 52 34 85.5t87 33.5q55 0 89 -33.5t34 -85.5t-34 -85t-89 -33q-54 0 -87.5 33.5t-33.5 84.5zM201 0v1077h172v-1077h-172z" />
<glyph unicode="j" horiz-adv-x="569" d="M-10 -305q48 23 76.5 41t56.5 45.5t42.5 62t23 85.5t8.5 120v1028h172v-1018q0 -89 -14 -157t-38 -116t-67.5 -87.5t-90.5 -67.5t-120 -61zM162 1466q0 52 34 85.5t87 33.5q55 0 89 -33.5t34 -85.5t-34 -85t-89 -33q-54 0 -87.5 33.5t-33.5 84.5z" />
<glyph unicode="k" horiz-adv-x="1036" d="M201 0v1511l172 21v-1532h-172zM385 588l410 489h192l-409 -477l456 -600h-207z" />
<glyph unicode="l" horiz-adv-x="595" d="M190 205v1306l172 21v-1323q0 -90 72 -90q40 0 70 12l45 -121q-68 -35 -143 -35q-100 0 -158 59.5t-58 170.5z" />
<glyph unicode="m" horiz-adv-x="1753" d="M201 0v1077h147l14 -162q128 187 316 187q100 0 170 -53t100 -148q132 201 326 201q132 0 210.5 -89t78.5 -241v-772h-172v748q0 219 -158 219q-44 0 -81 -13.5t-69.5 -42t-58 -61.5t-57.5 -82v-768h-172v748q0 219 -158 219q-83 0 -142.5 -49.5t-121.5 -149.5v-768h-172 z" />
<glyph unicode="n" horiz-adv-x="1198" d="M201 0v1077h147l14 -162q135 187 338 187q147 0 228.5 -87.5t81.5 -242.5v-772h-172v748q0 119 -45.5 169t-133.5 50q-90 0 -158 -51t-128 -148v-768h-172z" />
<glyph unicode="o" d="M121 537q0 257 126 411t349 154q225 0 349 -150t124 -411q0 -257 -126 -411.5t-349 -154.5t-348 151.5t-125 410.5zM305 537q0 -211 74 -316.5t215 -105.5q142 0 216.5 106t74.5 320q0 211 -74 316.5t-215 105.5t-216 -106.5t-75 -319.5z" />
<glyph unicode="p" horiz-adv-x="1210" d="M201 -432v1509h147l14 -149q59 83 142 128.5t178 45.5q205 0 301 -146.5t96 -414.5q0 -167 -46.5 -293t-142.5 -199.5t-231 -73.5q-183 0 -286 127v-514zM373 254q91 -137 248 -137q274 0 274 424t-254 424q-155 0 -268 -172v-539z" />
<glyph unicode="q" horiz-adv-x="1218" d="M131 535q0 252 113.5 409.5t308.5 157.5q172 0 305 -148l12 123h148v-1509l-172 20v555q-54 -79 -133 -123.5t-176 -44.5q-192 0 -299 151.5t-107 408.5zM315 537q0 -212 66.5 -318t191.5 -106q89 0 152.5 42t120.5 128v536q-53 71 -114.5 108.5t-141.5 37.5 q-129 0 -202 -108.5t-73 -319.5z" />
<glyph unicode="r" horiz-adv-x="784" d="M201 0v1077h147l17 -225q86 250 290 250q61 0 107 -12l-31 -168q-49 12 -94 12q-102 0 -164.5 -73t-99.5 -230v-631h-172z" />
<glyph unicode="s" horiz-adv-x="954" d="M57 117l93 104q141 -106 307 -106q108 0 173.5 46.5t65.5 127.5q0 84 -53.5 126t-214.5 85q-171 45 -247.5 119.5t-76.5 189.5q0 127 108 210t273 83q200 0 361 -123l-74 -109q-75 49 -140.5 72t-139.5 23q-95 0 -152 -41.5t-57 -110.5q0 -68 51.5 -106t187.5 -74 q180 -47 267.5 -123t87.5 -211q0 -105 -59.5 -180t-152 -109.5t-204.5 -34.5q-241 0 -404 142z" />
<glyph unicode="t" horiz-adv-x="729" d="M23 944v133h186v248l172 18v-266h260l-18 -133h-242v-659q0 -86 32 -126t103 -40q73 0 150 45l65 -117q-107 -72 -237 -72q-135 0 -210 77.5t-75 223.5v668h-186z" />
<glyph unicode="u" d="M190 305v772h172v-753q0 -114 42 -162.5t135 -48.5q164 0 280 192v772h172v-1077h-147l-15 174q-62 -101 -142.5 -150t-194.5 -49q-143 0 -222.5 86t-79.5 244z" />
<glyph unicode="v" horiz-adv-x="997" d="M23 1077h188l293 -923l289 923h182l-369 -1077h-209z" />
<glyph unicode="w" horiz-adv-x="1460" d="M43 1077h174l203 -956l221 956h193l213 -956l204 956h164l-248 -1077h-235l-199 922l-205 -922h-229z" />
<glyph unicode="x" horiz-adv-x="980" d="M12 0l377 569l-332 508h201l236 -403l235 403h195l-332 -499l377 -578h-209l-275 467l-278 -467h-195z" />
<glyph unicode="y" horiz-adv-x="995" d="M23 1077h184l297 -952l291 952h178l-363 -1083q-30 -91 -68 -159t-94 -125.5t-136.5 -92.5t-186.5 -49l-21 135q81 15 136.5 38.5t93.5 62t62 82.5t50 114h-59z" />
<glyph unicode="z" horiz-adv-x="888" d="M53 0v129l570 803h-523v145h719v-131l-571 -801h575l-20 -145h-750z" />
<glyph unicode="{" horiz-adv-x="661" d="M61 643v143q79 0 112.5 36.5t33.5 121.5v471q0 157 80 225t272 68v-133q-102 0 -146 -32.5t-44 -110.5v-498q0 -71 -19.5 -115.5t-50 -65.5t-84.5 -38q82 -26 118 -70t36 -147v-496q0 -78 44 -110.5t146 -32.5v-133q-192 0 -272 67.5t-80 224.5v471q0 83 -33.5 118.5 t-112.5 35.5z" />
<glyph unicode="|" horiz-adv-x="827" d="M336 -209v1860h156v-1860h-156z" />
<glyph unicode="}" horiz-adv-x="661" d="M102 -141q101 0 146 33t45 110v496q0 71 19 115t49.5 64.5t84.5 37.5q-54 17 -84.5 38t-49.5 65.5t-19 115.5v498q0 77 -45 110t-146 33v133q192 0 272.5 -68t80.5 -225v-471q0 -85 33 -121.5t112 -36.5v-143q-79 0 -112 -35t-33 -119v-471q0 -157 -80.5 -224.5 t-272.5 -67.5v133z" />
<glyph unicode="~" horiz-adv-x="991" d="M80 592q99 188 268 188q50 0 99 -19t81 -42.5t70 -42.5t68 -19q79 0 143 97l102 -58q-93 -178 -262 -178q-51 0 -101 19.5t-82.5 43t-69 43t-64.5 19.5q-78 0 -148 -106z" />
<glyph unicode="&#xa1;" horiz-adv-x="493" d="M115 872q0 56 38 94t95 38q55 0 93 -38t38 -94t-37.5 -93.5t-93.5 -37.5q-57 0 -95 37.5t-38 93.5zM156 -412l16 920h154l18 -920h-188z" />
<glyph unicode="&#xa2;" horiz-adv-x="974" d="M121 530q0 237 106 389.5t291 178.5v301h148v-301q129 -17 245 -111l-84 -108q-107 79 -233 79q-136 0 -212.5 -107.5t-76.5 -318.5q0 -208 76 -308.5t213 -100.5q66 0 119.5 20t117.5 64l80 -113q-107 -91 -245 -112v-295h-150v293q-186 25 -290.5 169.5t-104.5 380.5z " />
<glyph unicode="&#xa3;" horiz-adv-x="1058" d="M88 0v137q101 40 134.5 92.5t33.5 173.5v261h-135v114h135v236q0 167 100 271t279 104q219 0 358 -166l-112 -86q-50 58 -106.5 84t-133.5 26q-103 0 -159 -60.5t-56 -172.5v-236h428v-114h-428v-263q0 -103 -27 -160t-100 -96h659l-20 -145h-850z" />
<glyph unicode="&#xa4;" horiz-adv-x="1146" d="M70 291l159 160q-75 107 -75 247q0 137 73 246l-157 164l102 102l160 -163q104 71 237 71q145 0 246 -69l162 161l100 -102l-157 -158q77 -109 77 -252q0 -146 -77 -252l157 -155l-102 -105l-162 158q-104 -68 -244 -68q-130 0 -239 72l-160 -162zM319 700 q0 -135 66.5 -210.5t189.5 -75.5q126 0 192.5 75.5t66.5 210.5q0 134 -66.5 209.5t-192.5 75.5q-123 0 -189.5 -75.5t-66.5 -209.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1085" d="M14 1368h191l344 -612l340 612h182l-393 -663h235v-117h-282v-191h282v-116h-282v-281h-176v281h-283v116h283v191h-283v117h234z" />
<glyph unicode="&#xa6;" horiz-adv-x="827" d="M336 -209v746h156v-746h-156zM336 907v744h156v-744h-156z" />
<glyph unicode="&#xa7;" horiz-adv-x="1091" d="M150 639q0 75 42.5 140t116.5 110q-55 39 -81.5 88.5t-26.5 118.5q0 134 103.5 211t268.5 77q194 0 345 -106l-68 -111q-131 84 -279 84q-95 0 -147.5 -38.5t-52.5 -104.5q0 -74 49.5 -116t204.5 -93q169 -58 243 -125t74 -176q0 -144 -156 -246q109 -80 109 -202 q0 -133 -108.5 -213t-280.5 -80q-196 0 -342 100l67 115q119 -80 281 -80q93 0 152 38t59 103q0 74 -48.5 113.5t-205.5 91.5q-172 55 -245.5 120t-73.5 181zM317 662q0 -76 41.5 -114.5t163.5 -78.5q87 -28 160 -61q94 81 94 167q0 72 -44.5 113t-180.5 90q-59 20 -141 56 q-93 -81 -93 -172z" />
<glyph unicode="&#xa8;" horiz-adv-x="761" d="M61 1452q0 46 32 77.5t79 31.5q46 0 77.5 -31.5t31.5 -77.5t-31.5 -77.5t-77.5 -31.5q-47 0 -79 31.5t-32 77.5zM481 1452q0 46 31.5 77.5t77.5 31.5q47 0 78.5 -31.5t31.5 -77.5t-31.5 -77.5t-78.5 -31.5q-46 0 -77.5 31.5t-31.5 77.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1658" d="M197 889q0 183 84.5 329.5t229 227t320.5 80.5q130 0 246 -47.5t201 -130t134.5 -202t49.5 -257.5q0 -182 -84.5 -328t-228.5 -226.5t-318 -80.5q-176 0 -320.5 80.5t-229 226.5t-84.5 328zM313 889q0 -235 147 -384t371 -149q223 0 370 149.5t147 383.5 q0 237 -146.5 386.5t-370.5 149.5t-371 -150t-147 -386zM508 891q0 182 94.5 283.5t231.5 101.5q72 0 125.5 -20t107.5 -60l-66 -90q-76 55 -159 55q-84 0 -137.5 -65.5t-53.5 -204.5q0 -131 52 -197.5t139 -66.5q91 0 172 61l61 -90q-103 -92 -237 -92q-148 0 -239 101.5 t-91 283.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="1024" d="M139 805q0 130 100 199t283 69h127v53q0 83 -41 117.5t-133 34.5q-103 0 -231 -43l-41 117q152 57 299 57q315 0 315 -272v-355q0 -51 16.5 -74t51.5 -36l-37 -113q-68 8 -109.5 35t-62.5 84q-88 -121 -260 -121q-128 0 -202.5 68t-74.5 180zM152 0v145h759v-145h-759z M315 813q0 -63 37 -97t105 -34q121 0 192 113v174h-108q-226 0 -226 -156z" />
<glyph unicode="&#xab;" horiz-adv-x="1167" d="M121 528v117l352 442l105 -69l-279 -430l279 -428l-105 -72zM592 528v117l352 442l105 -69l-279 -430l279 -428l-105 -72z" />
<glyph unicode="&#xac;" horiz-adv-x="1024" d="M131 588v145h762v-432h-156v287h-606z" />
<glyph unicode="&#xad;" horiz-adv-x="823" d="M127 567v148h571v-148h-571z" />
<glyph unicode="&#xae;" horiz-adv-x="1312" d="M143 1012q0 141 67.5 260t185 188.5t257.5 69.5q144 0 262.5 -69.5t186 -188t67.5 -260.5t-67.5 -260t-186 -187t-262.5 -69q-105 0 -199.5 40.5t-162.5 109t-108 164t-40 202.5zM248 1012q0 -184 115.5 -303t289.5 -119q179 0 294.5 119t115.5 303t-115.5 304 t-294.5 120q-174 0 -289.5 -120.5t-115.5 -303.5zM471 737v563h156q124 0 184.5 -42t60.5 -128q0 -59 -36 -98t-97 -57l152 -238h-129l-127 219h-55v-219h-109zM580 1042h67q113 0 113 88q0 45 -29 64.5t-90 19.5h-61v-172z" />
<glyph unicode="&#xaf;" horiz-adv-x="665" d="M61 1370v131h543v-131h-543z" />
<glyph unicode="&#xb0;" horiz-adv-x="1069" d="M119 1110q0 99 48.5 174.5t122.5 112.5t159 37q87 0 161 -37.5t121 -113t47 -175.5q0 -148 -97 -235t-232 -87q-85 0 -159 37t-122.5 112.5t-48.5 174.5zM264 1110q0 -98 54 -151.5t131 -53.5q79 0 131.5 53t52.5 150q0 98 -52.5 151.5t-131.5 53.5q-77 0 -131 -53 t-54 -150z" />
<glyph unicode="&#xb1;" horiz-adv-x="1019" d="M131 0v145h760v-145h-760zM133 647v144h299v307h158v-307h301v-144h-301v-307h-158v307h-299z" />
<glyph unicode="&#xb2;" horiz-adv-x="819" d="M119 1389q112 137 280 137q129 0 204 -68t75 -174q0 -104 -75.5 -204t-295.5 -302h393l-16 -119h-543v111q88 85 137.5 135t99.5 102.5t73 83t43 64.5t26 60t6 57q0 65 -37 100t-100 35q-55 0 -95.5 -21t-82.5 -69z" />
<glyph unicode="&#xb3;" horiz-adv-x="819" d="M113 762l84 80q81 -84 188 -84q78 0 124 40.5t46 110.5q0 144 -170 144h-68l19 106h45q67 0 110.5 35t43.5 100q0 56 -38.5 87.5t-103.5 31.5q-97 0 -182 -76l-76 84q117 105 271 105q128 0 201 -61t73 -152q0 -79 -47.5 -130t-128.5 -71q90 -8 146.5 -61.5t56.5 -149.5 q0 -112 -86 -186t-230 -74q-173 0 -278 121z" />
<glyph unicode="&#xb4;" horiz-adv-x="602" d="M61 1403l402 229l78 -147l-426 -174z" />
<glyph unicode="&#xb5;" horiz-adv-x="1202" d="M201 -432v1509h172v-760q0 -192 170 -192q168 0 274 203v749h172v-710q0 -206 55 -369l-161 -21q-24 55 -33 97t-16 110q-52 -89 -126 -149t-167 -60q-73 0 -121 26t-82 83q33 -112 33 -272v-224z" />
<glyph unicode="&#xb6;" horiz-adv-x="1490" d="M190 997q0 196 132.5 304t363.5 108h512v-1821l-156 -24v1708h-239v-1684l-156 -24v1020q-233 10 -345 122.5t-112 290.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="475" d="M104 641q0 54 38.5 92.5t93.5 38.5q58 0 95.5 -38t37.5 -93q0 -56 -37.5 -94.5t-95.5 -38.5q-55 0 -93.5 38.5t-38.5 94.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="548" d="M61 -438l48 98q64 -33 129 -33q46 0 75 21t29 63q0 88 -182 88l26 234h111v-152q98 -8 144 -54.5t46 -115.5q0 -94 -69.5 -146t-175.5 -52q-116 0 -181 49z" />
<glyph unicode="&#xb9;" horiz-adv-x="819" d="M141 1343l283 168h129v-852h-145v703l-203 -119z" />
<glyph unicode="&#xba;" horiz-adv-x="1024" d="M121 983q0 194 104.5 310t288.5 116t286.5 -113t102.5 -313q0 -194 -104 -310t-287 -116q-185 0 -288 113.5t-103 312.5zM133 0v145h760v-145h-760zM299 983q0 -297 213 -297t213 297q0 295 -211 295q-215 0 -215 -295z" />
<glyph unicode="&#xbb;" horiz-adv-x="1148" d="M121 160l276 428l-276 430l102 69l355 -442v-117l-355 -440zM573 160l277 428l-277 430l103 69l354 -442v-117l-354 -440z" />
<glyph unicode="&#xbc;" horiz-adv-x="1900" d="M141 1241l283 168h129v-852h-145v703l-203 -119zM477 -102l834 1636l112 -53l-833 -1637zM1171 201v102l265 563l125 -47l-236 -506h231l13 222h129v-222h110v-112h-110v-201h-144v201h-383z" />
<glyph unicode="&#xbd;" horiz-adv-x="1900" d="M141 1241l283 168h129v-852h-145v703l-203 -119zM477 -102l834 1636l112 -53l-833 -1637zM1200 729q112 137 281 137q129 0 203.5 -67.5t74.5 -173.5q0 -104 -75.5 -204.5t-294.5 -301.5h393l-17 -119h-542v111q246 239 315.5 327t69.5 174q0 65 -37.5 100.5t-100.5 35.5 q-54 0 -95 -21.5t-83 -69.5z" />
<glyph unicode="&#xbe;" horiz-adv-x="1900" d="M113 659l84 80q81 -84 188 -84q78 0 124 41t46 111q0 143 -170 143h-68l19 107h45q67 0 110.5 35t43.5 100q0 56 -38.5 87.5t-103.5 31.5q-97 0 -182 -76l-76 84q116 104 271 104q128 0 201 -61t73 -152q0 -79 -47.5 -129.5t-128.5 -70.5q90 -8 146.5 -61.5t56.5 -149.5 q0 -112 -86 -186t-230 -74q-173 0 -278 120zM477 -102l834 1636l112 -53l-833 -1637zM1171 201v102l265 563l125 -47l-236 -506h231l13 222h129v-222h110v-112h-110v-201h-144v201h-383z" />
<glyph unicode="&#xbf;" horiz-adv-x="933" d="M68 -123q0 66 20.5 118t53 86t72 63t79 55.5t72 55.5t53 70.5t20.5 94.5v88h170v-96q0 -67 -20 -121t-52 -89t-70.5 -64t-77 -54t-70.5 -51t-52 -63t-20 -83q0 -84 57.5 -132t155.5 -48q166 0 291 148l118 -91q-173 -200 -419 -200q-91 0 -164.5 26t-120 70t-71.5 99.5 t-25 117.5zM385 872q0 56 38 94t95 38q55 0 93 -38t38 -94t-37.5 -93.5t-93.5 -37.5q-57 0 -95 37.5t-38 93.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1169" d="M16 0l457 1409h225l455 -1409h-188l-109 360h-549l-110 -360h-181zM328 1708l73 147l406 -215l-45 -92zM350 506h461l-229 756z" />
<glyph unicode="&#xc1;" horiz-adv-x="1169" d="M16 0l457 1409h225l455 -1409h-188l-109 360h-549l-110 -360h-181zM346 1640l404 215l75 -147l-434 -160zM350 506h461l-229 756z" />
<glyph unicode="&#xc2;" horiz-adv-x="1169" d="M16 0l457 1409h225l455 -1409h-188l-109 360h-549l-110 -360h-181zM252 1640l276 222h113l272 -222l-73 -84l-256 166l-256 -166zM350 506h461l-229 756z" />
<glyph unicode="&#xc3;" horiz-adv-x="1169" d="M16 0l457 1409h225l455 -1409h-188l-109 360h-549l-110 -360h-181zM252 1665q83 170 215 170q47 0 90.5 -24t80 -48t67.5 -24q30 0 55 21t55 69l105 -53q-77 -170 -215 -170q-45 0 -89 24.5t-81.5 49t-67.5 24.5q-34 0 -58.5 -21.5t-52.5 -70.5zM350 506h461l-229 756z " />
<glyph unicode="&#xc4;" horiz-adv-x="1169" d="M16 0l457 1409h225l455 -1409h-188l-109 360h-549l-110 -360h-181zM266 1710q0 46 32 77.5t79 31.5q46 0 77 -31.5t31 -77.5t-31 -77t-77 -31q-47 0 -79 31t-32 77zM350 506h461l-229 756zM686 1710q0 46 31.5 77.5t77.5 31.5q47 0 78.5 -31.5t31.5 -77.5t-31.5 -77 t-78.5 -31q-46 0 -77.5 31t-31.5 77z" />
<glyph unicode="&#xc5;" horiz-adv-x="1169" d="M16 0l457 1409h225l455 -1409h-188l-109 360h-549l-110 -360h-181zM350 506h461l-229 756zM360 1751q0 90 63.5 150.5t162.5 60.5t163 -60.5t64 -150.5t-64 -150.5t-163 -60.5t-162.5 60.5t-63.5 150.5zM477 1751q0 -54 29 -84.5t80 -30.5t80.5 30.5t29.5 84.5 q0 56 -29 86.5t-81 30.5q-51 0 -80 -31t-29 -86z" />
<glyph unicode="&#xc6;" horiz-adv-x="1654" d="M-18 0l557 1409h923l-20 -143h-641l121 -478h522v-143h-488l127 -502h465v-143h-608l-86 360h-555l-137 -360h-180zM352 504h469l-180 764z" />
<glyph unicode="&#xc7;" horiz-adv-x="1142" d="M119 705q0 175 45.5 315.5t125.5 229.5t184.5 136.5t225.5 47.5q123 0 207.5 -33t175.5 -105l-96 -114q-130 104 -276 104q-69 0 -127.5 -18.5t-110 -62t-88 -109t-57.5 -165.5t-21 -226q0 -150 31 -263.5t86 -181t126.5 -100.5t158.5 -33q91 0 159 30t144 87l88 -113 q-136 -136 -340 -154v-96q98 -8 144 -54.5t46 -115.5q0 -94 -69.5 -146t-175.5 -52q-116 0 -181 49l47 98q64 -33 129 -33q46 0 75.5 21t29.5 63q0 88 -182 88l20 178q-237 22 -380.5 212.5t-143.5 515.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1085" d="M211 0v1409h752l-21 -143h-555v-475h481v-144h-481v-504h590v-143h-766zM336 1708l74 147l405 -215l-45 -92z" />
<glyph unicode="&#xc9;" horiz-adv-x="1085" d="M211 0v1409h752l-21 -143h-555v-475h481v-144h-481v-504h590v-143h-766zM354 1640l404 215l76 -147l-435 -160z" />
<glyph unicode="&#xca;" horiz-adv-x="1085" d="M211 0v1409h752l-21 -143h-555v-475h481v-144h-481v-504h590v-143h-766zM260 1640l277 222h112l273 -222l-74 -84l-256 166l-256 -166z" />
<glyph unicode="&#xcb;" horiz-adv-x="1085" d="M211 0v1409h752l-21 -143h-555v-475h481v-144h-481v-504h590v-143h-766zM274 1710q0 46 32 77.5t79 31.5q46 0 77.5 -31.5t31.5 -77.5t-31.5 -77t-77.5 -31q-47 0 -79 31t-32 77zM694 1710q0 46 31.5 77.5t77.5 31.5q47 0 78.5 -31.5t31.5 -77.5t-31.5 -77t-78.5 -31 q-46 0 -77.5 31t-31.5 77z" />
<glyph unicode="&#xcc;" horiz-adv-x="598" d="M39 1708l74 147l405 -215l-45 -92zM211 0v1409h176v-1409h-176z" />
<glyph unicode="&#xcd;" horiz-adv-x="598" d="M57 1640l404 215l76 -147l-435 -160zM211 0v1409h176v-1409h-176z" />
<glyph unicode="&#xce;" horiz-adv-x="598" d="M-37 1640l277 222h112l273 -222l-74 -84l-256 166l-256 -166zM211 0v1409h176v-1409h-176z" />
<glyph unicode="&#xcf;" horiz-adv-x="598" d="M-23 1710q0 46 32 77.5t79 31.5q46 0 77.5 -31.5t31.5 -77.5t-31.5 -77t-77.5 -31q-47 0 -79 31t-32 77zM211 0v1409h176v-1409h-176zM397 1710q0 46 31.5 77.5t77.5 31.5q47 0 78.5 -31.5t31.5 -77.5t-31.5 -77t-78.5 -31q-46 0 -77.5 31t-31.5 77z" />
<glyph unicode="&#xd0;" horiz-adv-x="1333" d="M43 655v133h188v621h291q80 0 149.5 -7.5t142 -28.5t130.5 -54t110 -87t87 -125.5t55.5 -171.5t20.5 -222q0 -120 -21 -221t-56.5 -173.5t-87 -128.5t-108 -91.5t-123.5 -58t-130.5 -31.5t-131.5 -9h-328v655h-188zM408 141h165q73 0 134 13.5t122 52t103 100.5t68 165.5 t26 240.5q0 101 -14 183t-38 139t-60 99t-74.5 67t-88 40t-94 20t-98.5 5h-151v-478h331v-133h-331v-514z" />
<glyph unicode="&#xd1;" horiz-adv-x="1394" d="M211 0v1409h231l598 -1216q-22 215 -22 428v788h166v-1409h-238l-594 1214q25 -283 25 -534v-680h-166zM385 1665q83 170 215 170q47 0 90.5 -24t80 -48t67.5 -24q30 0 55 21t55 69l105 -53q-77 -170 -215 -170q-45 0 -89 24.5t-81.5 49t-67.5 24.5q-34 0 -58.5 -21.5 t-52.5 -70.5z" />
<glyph unicode="&#xd2;" horiz-adv-x="1417" d="M119 700q0 343 162 538.5t428 195.5q268 0 428.5 -191.5t160.5 -540.5q0 -343 -160 -535t-429 -192t-429.5 189.5t-160.5 535.5zM307 700q0 -295 107 -436t295 -141q401 0 401 579q0 584 -401 584q-190 0 -296 -143t-106 -443zM451 1708l73 147l406 -215l-45 -92z" />
<glyph unicode="&#xd3;" horiz-adv-x="1417" d="M119 700q0 343 162 538.5t428 195.5q268 0 428.5 -191.5t160.5 -540.5q0 -343 -160 -535t-429 -192t-429.5 189.5t-160.5 535.5zM307 700q0 -295 107 -436t295 -141q401 0 401 579q0 584 -401 584q-190 0 -296 -143t-106 -443zM469 1640l403 215l76 -147l-434 -160z" />
<glyph unicode="&#xd4;" horiz-adv-x="1417" d="M119 700q0 343 162 538.5t428 195.5q268 0 428.5 -191.5t160.5 -540.5q0 -343 -160 -535t-429 -192t-429.5 189.5t-160.5 535.5zM307 700q0 -295 107 -436t295 -141q401 0 401 579q0 584 -401 584q-190 0 -296 -143t-106 -443zM375 1640l276 222h113l272 -222l-73 -84 l-256 166l-256 -166z" />
<glyph unicode="&#xd5;" horiz-adv-x="1417" d="M119 700q0 343 162 538.5t428 195.5q268 0 428.5 -191.5t160.5 -540.5q0 -343 -160 -535t-429 -192t-429.5 189.5t-160.5 535.5zM307 700q0 -295 107 -436t295 -141q401 0 401 579q0 584 -401 584q-190 0 -296 -143t-106 -443zM375 1665q83 170 215 170q37 0 73 -15 t59 -33t51.5 -33t53.5 -15q30 0 55.5 21.5t55.5 68.5l104 -53q-77 -170 -215 -170q-45 0 -88.5 24.5t-81 49t-67.5 24.5q-34 0 -58.5 -21.5t-52.5 -70.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1417" d="M119 700q0 343 162 538.5t428 195.5q268 0 428.5 -191.5t160.5 -540.5q0 -343 -160 -535t-429 -192t-429.5 189.5t-160.5 535.5zM307 700q0 -295 107 -436t295 -141q401 0 401 579q0 584 -401 584q-190 0 -296 -143t-106 -443zM389 1710q0 46 32 77.5t79 31.5 q46 0 77 -31.5t31 -77.5t-31 -77t-77 -31q-47 0 -79 31t-32 77zM809 1710q0 46 31.5 77.5t77.5 31.5q47 0 78.5 -31.5t31.5 -77.5t-31.5 -77t-78.5 -31q-46 0 -77.5 31t-31.5 77z" />
<glyph unicode="&#xd7;" horiz-adv-x="1019" d="M154 377l247 248l-247 256l106 108l246 -260l256 260l104 -108l-248 -248l248 -256l-104 -107l-248 258l-254 -258z" />
<glyph unicode="&#xd8;" horiz-adv-x="1417" d="M119 700q0 343 162 538.5t428 195.5q73 0 137 -15l78 254l147 -41l-84 -264q148 -80 229.5 -250t81.5 -416q0 -343 -160 -535t-429 -192q-75 0 -142 15l-71 -246l-150 39l80 260q-146 80 -226.5 247t-80.5 410zM307 700q0 -390 182 -516l328 1090q-52 12 -108 12 q-190 0 -296 -143t-106 -443zM596 135q56 -12 113 -12q401 0 401 579q0 405 -186 527z" />
<glyph unicode="&#xd9;" horiz-adv-x="1351" d="M190 449v960h177v-946q0 -336 307 -336q157 0 232 84t75 252v946h180v-960q0 -213 -128 -343.5t-359 -130.5q-233 0 -358.5 130t-125.5 344zM418 1708l74 147l405 -215l-45 -92z" />
<glyph unicode="&#xda;" horiz-adv-x="1351" d="M190 449v960h177v-946q0 -336 307 -336q157 0 232 84t75 252v946h180v-960q0 -213 -128 -343.5t-359 -130.5q-233 0 -358.5 130t-125.5 344zM436 1640l404 215l75 -147l-434 -160z" />
<glyph unicode="&#xdb;" horiz-adv-x="1351" d="M190 449v960h177v-946q0 -336 307 -336q157 0 232 84t75 252v946h180v-960q0 -213 -128 -343.5t-359 -130.5q-233 0 -358.5 130t-125.5 344zM342 1640l276 222h113l273 -222l-74 -84l-256 166l-256 -166z" />
<glyph unicode="&#xdc;" horiz-adv-x="1351" d="M190 449v960h177v-946q0 -336 307 -336q157 0 232 84t75 252v946h180v-960q0 -213 -128 -343.5t-359 -130.5q-233 0 -358.5 130t-125.5 344zM356 1710q0 46 32 77.5t79 31.5q46 0 77 -31.5t31 -77.5t-31 -77t-77 -31q-47 0 -79 31t-32 77zM776 1710q0 46 31.5 77.5 t77.5 31.5q47 0 78.5 -31.5t31.5 -77.5t-31.5 -77t-78.5 -31q-46 0 -77.5 31t-31.5 77z" />
<glyph unicode="&#xdd;" horiz-adv-x="1114" d="M14 1409h197l352 -709l350 709h187l-453 -864v-545h-180v543zM317 1640l404 215l76 -147l-435 -160z" />
<glyph unicode="&#xde;" horiz-adv-x="1183" d="M211 0v1409h176v-244h184q259 0 400 -109t141 -331q0 -121 -40.5 -211t-114.5 -144t-170.5 -80t-215.5 -26h-184v-264h-176zM387 406h178q85 0 147.5 14.5t112 49t74.5 98t25 155.5q0 162 -91.5 232.5t-263.5 70.5h-182v-620z" />
<glyph unicode="&#xdf;" horiz-adv-x="1208" d="M201 0v1104q0 204 106 316t297 112q164 0 263.5 -82t99.5 -207q0 -61 -24 -109.5t-58 -79t-68 -57t-58 -58t-24 -67.5q0 -34 21.5 -65t55.5 -56t75 -52t82.5 -61t75.5 -75t55.5 -101.5t21.5 -133.5q0 -158 -104 -255.5t-252 -97.5q-119 0 -207 48l51 120q62 -28 140 -28 q89 0 143.5 55.5t54.5 159.5q0 57 -20.5 104.5t-54 80.5t-73.5 63t-79.5 58.5t-73 59t-54 72.5t-20.5 92q0 53 23.5 94.5t57 69t67 54.5t57 66.5t23.5 90.5q0 77 -56 119.5t-141 42.5q-111 0 -171 -72.5t-60 -220.5v-1104h-172z" />
<glyph unicode="&#xe0;" horiz-adv-x="1110" d="M98 301q0 171 122 261.5t345 90.5h176v86q0 118 -56 170t-175 52q-120 0 -291 -60l-43 127q195 74 365 74q188 0 280 -91.5t92 -262.5v-500q0 -70 22 -103t69 -49l-41 -121q-82 11 -131 49t-70 117q-110 -166 -324 -166q-158 0 -249 89.5t-91 236.5zM281 309 q0 -99 50 -151t146 -52q172 0 264 177v256h-153q-307 0 -307 -230zM311 1485l78 147l402 -229l-54 -92z" />
<glyph unicode="&#xe1;" horiz-adv-x="1110" d="M98 301q0 171 122 261.5t345 90.5h176v86q0 118 -56 170t-175 52q-120 0 -291 -60l-43 127q195 74 365 74q188 0 280 -91.5t92 -262.5v-500q0 -70 22 -103t69 -49l-41 -121q-82 11 -131 49t-70 117q-110 -166 -324 -166q-158 0 -249 89.5t-91 236.5zM281 309 q0 -99 50 -151t146 -52q172 0 264 177v256h-153q-307 0 -307 -230zM291 1403l401 229l78 -147l-426 -174z" />
<glyph unicode="&#xe2;" horiz-adv-x="1110" d="M98 301q0 171 122 261.5t345 90.5h176v86q0 118 -56 170t-175 52q-120 0 -291 -60l-43 127q195 74 365 74q188 0 280 -91.5t92 -262.5v-500q0 -70 22 -103t69 -49l-41 -121q-82 11 -131 49t-70 117q-110 -166 -324 -166q-158 0 -249 89.5t-91 236.5zM201 1389l274 227 h113l274 -227l-74 -86l-256 172l-258 -172zM281 309q0 -99 50 -151t146 -52q172 0 264 177v256h-153q-307 0 -307 -230z" />
<glyph unicode="&#xe3;" horiz-adv-x="1110" d="M98 301q0 171 122 261.5t345 90.5h176v86q0 118 -56 170t-175 52q-120 0 -291 -60l-43 127q195 74 365 74q188 0 280 -91.5t92 -262.5v-500q0 -70 22 -103t69 -49l-41 -121q-82 11 -131 49t-70 117q-110 -166 -324 -166q-158 0 -249 89.5t-91 236.5zM199 1407 q83 170 215 170q37 0 73 -15t59 -33t51.5 -33t53.5 -15q30 0 55.5 21.5t55.5 68.5l104 -53q-77 -170 -215 -170q-45 0 -88.5 24.5t-81 49t-67.5 24.5q-34 0 -58.5 -21.5t-52.5 -70.5zM281 309q0 -99 50 -151t146 -52q172 0 264 177v256h-153q-307 0 -307 -230z" />
<glyph unicode="&#xe4;" horiz-adv-x="1110" d="M98 301q0 171 122 261.5t345 90.5h176v86q0 118 -56 170t-175 52q-120 0 -291 -60l-43 127q195 74 365 74q188 0 280 -91.5t92 -262.5v-500q0 -70 22 -103t69 -49l-41 -121q-82 11 -131 49t-70 117q-110 -166 -324 -166q-158 0 -249 89.5t-91 236.5zM213 1452 q0 46 32 77.5t79 31.5q46 0 77 -31.5t31 -77.5t-31 -77.5t-77 -31.5q-47 0 -79 31.5t-32 77.5zM281 309q0 -99 50 -151t146 -52q172 0 264 177v256h-153q-307 0 -307 -230zM633 1452q0 46 31 77.5t77 31.5q47 0 79 -31.5t32 -77.5t-32 -77.5t-79 -31.5q-46 0 -77 31.5 t-31 77.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1110" d="M98 301q0 171 122 261.5t345 90.5h176v86q0 118 -56 170t-175 52q-120 0 -291 -60l-43 127q195 74 365 74q188 0 280 -91.5t92 -262.5v-500q0 -70 22 -103t69 -49l-41 -121q-82 11 -131 49t-70 117q-110 -166 -324 -166q-158 0 -249 89.5t-91 236.5zM281 309 q0 -99 50 -151t146 -52q172 0 264 177v256h-153q-307 0 -307 -230zM307 1493q0 90 63 150.5t162 60.5t163.5 -60.5t64.5 -150.5t-64.5 -150.5t-163.5 -60.5t-162 60.5t-63 150.5zM424 1493q0 -54 28.5 -84.5t79.5 -30.5t81 30.5t30 84.5q0 55 -29 86t-82 31q-51 0 -79.5 -31 t-28.5 -86z" />
<glyph unicode="&#xe6;" horiz-adv-x="1736" d="M98 301q0 170 125 262t348 92h174v84q0 118 -55 170t-174 52q-123 0 -291 -60l-45 127q198 74 365 74q239 0 325 -180q113 180 328 180q207 0 318.5 -140.5t111.5 -388.5q0 -48 -4 -94h-694q11 -187 91 -274.5t210 -87.5q80 0 145 22.5t137 73.5l76 -102 q-172 -136 -370 -136q-130 0 -229 52t-159 151q-77 -108 -169.5 -155.5t-217.5 -47.5q-159 0 -252.5 90t-93.5 236zM287 309q0 -99 49.5 -151t144.5 -52q91 0 161.5 47t133.5 148q-31 105 -31 225v13h-151q-307 0 -307 -230zM930 606h528v17q0 165 -66 253.5t-198 88.5 q-117 0 -185.5 -88t-78.5 -271z" />
<glyph unicode="&#xe7;" horiz-adv-x="974" d="M121 530q0 260 126 416t341 156q186 0 323 -115l-84 -108q-107 79 -233 79q-136 0 -212.5 -107.5t-76.5 -318.5q0 -208 76 -308.5t213 -100.5q66 0 119.5 20t117.5 64l80 -113q-127 -108 -284 -117v-96q98 -8 144 -54.5t46 -115.5q0 -94 -70 -146t-176 -52 q-115 0 -180 49l47 98q64 -33 129 -33q46 0 75.5 21t29.5 63q0 88 -183 88l21 183q-183 25 -286 169t-103 379z" />
<glyph unicode="&#xe8;" horiz-adv-x="1112" d="M121 528q0 257 119.5 415.5t322.5 158.5q213 0 325.5 -140t112.5 -389q0 -48 -4 -94h-694q13 -187 92 -274.5t209 -87.5q80 0 145.5 22.5t137.5 73.5l76 -102q-168 -136 -371 -136q-222 0 -346.5 147.5t-124.5 405.5zM303 606h528v17q0 165 -66 253.5t-198 88.5 q-117 0 -185.5 -88t-78.5 -271zM348 1485l78 147l401 -229l-53 -92z" />
<glyph unicode="&#xe9;" horiz-adv-x="1112" d="M121 528q0 257 119.5 415.5t322.5 158.5q213 0 325.5 -140t112.5 -389q0 -48 -4 -94h-694q13 -187 92 -274.5t209 -87.5q80 0 145.5 22.5t137.5 73.5l76 -102q-168 -136 -371 -136q-222 0 -346.5 147.5t-124.5 405.5zM303 606h528v17q0 165 -66 253.5t-198 88.5 q-117 0 -185.5 -88t-78.5 -271zM328 1403l401 229l78 -147l-426 -174z" />
<glyph unicode="&#xea;" horiz-adv-x="1112" d="M121 528q0 257 119.5 415.5t322.5 158.5q213 0 325.5 -140t112.5 -389q0 -48 -4 -94h-694q13 -187 92 -274.5t209 -87.5q80 0 145.5 22.5t137.5 73.5l76 -102q-168 -136 -371 -136q-222 0 -346.5 147.5t-124.5 405.5zM238 1389l274 227h113l274 -227l-74 -86l-256 172 l-258 -172zM303 606h528v17q0 165 -66 253.5t-198 88.5q-117 0 -185.5 -88t-78.5 -271z" />
<glyph unicode="&#xeb;" horiz-adv-x="1112" d="M121 528q0 257 119.5 415.5t322.5 158.5q213 0 325.5 -140t112.5 -389q0 -48 -4 -94h-694q13 -187 92 -274.5t209 -87.5q80 0 145.5 22.5t137.5 73.5l76 -102q-168 -136 -371 -136q-222 0 -346.5 147.5t-124.5 405.5zM250 1452q0 46 31.5 77.5t78.5 31.5q46 0 77.5 -31.5 t31.5 -77.5t-31.5 -77.5t-77.5 -31.5q-47 0 -78.5 31.5t-31.5 77.5zM303 606h528v17q0 165 -66 253.5t-198 88.5q-117 0 -185.5 -88t-78.5 -271zM670 1452q0 46 31 77.5t77 31.5q47 0 79 -31.5t32 -77.5t-32 -77.5t-79 -31.5q-46 0 -77 31.5t-31 77.5z" />
<glyph unicode="&#xec;" horiz-adv-x="571" d="M66 1485l77 147l402 -229l-53 -92zM201 0v1077h172v-1077h-172z" />
<glyph unicode="&#xed;" horiz-adv-x="571" d="M45 1403l401 229l78 -147l-426 -174zM201 0v1077h172v-1077h-172z" />
<glyph unicode="&#xee;" horiz-adv-x="571" d="M-45 1389l274 227h113l274 -227l-73 -86l-256 172l-258 -172zM201 0v1077h172v-1077h-172z" />
<glyph unicode="&#xef;" horiz-adv-x="571" d="M-33 1452q0 46 32 77.5t79 31.5q46 0 77 -31.5t31 -77.5t-31 -77.5t-77 -31.5q-47 0 -79 31.5t-32 77.5zM201 0v1077h172v-1077h-172zM387 1452q0 46 31.5 77.5t77.5 31.5q47 0 78.5 -31.5t31.5 -77.5t-31.5 -77.5t-78.5 -31.5q-46 0 -77.5 31.5t-31.5 77.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1161" d="M121 479q0 94 25.5 180t74 156t128 112t179.5 42q188 0 310 -144q-25 135 -85.5 239.5t-164.5 188.5l-152 -147l-119 59l156 158q-109 55 -235 78l36 133q173 -35 304 -102l143 151l102 -88l-131 -135q182 -138 260 -331.5t78 -479.5q0 -263 -127.5 -418.5t-335.5 -155.5 q-196 0 -321 135t-125 369zM297 487q0 -180 72 -277t196 -97q133 0 212 114t79 326q0 45 -4 115q-113 166 -297 166q-71 0 -122.5 -27.5t-80 -77t-42 -109.5t-13.5 -133z" />
<glyph unicode="&#xf1;" horiz-adv-x="1198" d="M201 0v1077h147l14 -162q135 187 338 187q147 0 228.5 -87.5t81.5 -242.5v-772h-172v748q0 119 -45.5 169t-133.5 50q-90 0 -158 -51t-128 -148v-768h-172zM276 1407q83 170 216 170q37 0 73 -15t59 -33t51.5 -33t53.5 -15q30 0 55.5 21.5t55.5 68.5l104 -53 q-77 -170 -215 -170q-45 0 -88.5 24.5t-81 49t-67.5 24.5q-34 0 -58.5 -21.5t-52.5 -70.5z" />
<glyph unicode="&#xf2;" d="M121 537q0 257 126 411t349 154q225 0 349 -150t124 -411q0 -257 -126 -411.5t-349 -154.5t-348 151.5t-125 410.5zM305 537q0 -211 74 -316.5t215 -105.5q142 0 216.5 106t74.5 320q0 211 -74 316.5t-215 105.5t-216 -106.5t-75 -319.5zM375 1485l78 147l401 -229 l-53 -92z" />
<glyph unicode="&#xf3;" d="M121 537q0 257 126 411t349 154q225 0 349 -150t124 -411q0 -257 -126 -411.5t-349 -154.5t-348 151.5t-125 410.5zM305 537q0 -211 74 -316.5t215 -105.5q142 0 216.5 106t74.5 320q0 211 -74 316.5t-215 105.5t-216 -106.5t-75 -319.5zM354 1403l402 229l78 -147 l-426 -174z" />
<glyph unicode="&#xf4;" d="M121 537q0 257 126 411t349 154q225 0 349 -150t124 -411q0 -257 -126 -411.5t-349 -154.5t-348 151.5t-125 410.5zM264 1389l275 227h112l275 -227l-74 -86l-256 172l-258 -172zM305 537q0 -211 74 -316.5t215 -105.5q142 0 216.5 106t74.5 320q0 211 -74 316.5 t-215 105.5t-216 -106.5t-75 -319.5z" />
<glyph unicode="&#xf5;" d="M121 537q0 257 126 411t349 154q225 0 349 -150t124 -411q0 -257 -126 -411.5t-349 -154.5t-348 151.5t-125 410.5zM262 1407q83 170 215 170q47 0 90.5 -24t80 -48t67.5 -24q30 0 55 21t55 69l105 -53q-77 -170 -215 -170q-45 0 -89 24.5t-81.5 49t-67.5 24.5 q-34 0 -58 -21.5t-52 -70.5zM305 537q0 -211 74 -316.5t215 -105.5q142 0 216.5 106t74.5 320q0 211 -74 316.5t-215 105.5t-216 -106.5t-75 -319.5z" />
<glyph unicode="&#xf6;" d="M121 537q0 257 126 411t349 154q225 0 349 -150t124 -411q0 -257 -126 -411.5t-349 -154.5t-348 151.5t-125 410.5zM276 1452q0 46 32 77.5t79 31.5q46 0 77.5 -31.5t31.5 -77.5t-31.5 -77.5t-77.5 -31.5q-47 0 -79 31.5t-32 77.5zM305 537q0 -211 74 -316.5t215 -105.5 q142 0 216.5 106t74.5 320q0 211 -74 316.5t-215 105.5t-216 -106.5t-75 -319.5zM696 1452q0 46 31.5 77.5t77.5 31.5q47 0 78.5 -31.5t31.5 -77.5t-31.5 -77.5t-78.5 -31.5q-46 0 -77.5 31.5t-31.5 77.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1019" d="M131 608v146h760v-146h-760zM379 219q0 54 38 92.5t93 38.5q58 0 95.5 -38t37.5 -93q0 -56 -37.5 -94.5t-95.5 -38.5q-55 0 -93 38.5t-38 94.5zM379 1145q0 54 38 92.5t93 38.5q58 0 95.5 -38t37.5 -93q0 -56 -37.5 -94.5t-95.5 -38.5q-55 0 -93 38.5t-38 94.5z" />
<glyph unicode="&#xf8;" d="M121 537q0 257 126 411t349 154q53 0 111 -10l79 247l142 -41l-86 -258q110 -66 168.5 -194t58.5 -305q0 -257 -126 -411.5t-349 -154.5q-47 0 -111 11l-80 -250l-139 41l86 260q-111 66 -170 195t-59 305zM305 537q0 -278 111 -369l260 786q-37 9 -80 9 q-141 0 -216 -106.5t-75 -319.5zM516 121q38 -6 78 -6q142 0 216.5 106t74.5 320q0 277 -109 366z" />
<glyph unicode="&#xf9;" d="M190 305v772h172v-753q0 -114 42 -162.5t135 -48.5q164 0 280 192v772h172v-1077h-147l-15 174q-62 -101 -142.5 -150t-194.5 -49q-143 0 -222.5 86t-79.5 244zM371 1485l78 147l401 -229l-53 -92z" />
<glyph unicode="&#xfa;" d="M190 305v772h172v-753q0 -114 42 -162.5t135 -48.5q164 0 280 192v772h172v-1077h-147l-15 174q-62 -101 -142.5 -150t-194.5 -49q-143 0 -222.5 86t-79.5 244zM350 1403l402 229l77 -147l-426 -174z" />
<glyph unicode="&#xfb;" d="M190 305v772h172v-753q0 -114 42 -162.5t135 -48.5q164 0 280 192v772h172v-1077h-147l-15 174q-62 -101 -142.5 -150t-194.5 -49q-143 0 -222.5 86t-79.5 244zM260 1389l275 227h112l275 -227l-74 -86l-256 172l-258 -172z" />
<glyph unicode="&#xfc;" d="M190 305v772h172v-753q0 -114 42 -162.5t135 -48.5q164 0 280 192v772h172v-1077h-147l-15 174q-62 -101 -142.5 -150t-194.5 -49q-143 0 -222.5 86t-79.5 244zM272 1452q0 46 32 77.5t79 31.5q46 0 77.5 -31.5t31.5 -77.5t-31.5 -77.5t-77.5 -31.5q-47 0 -79 31.5 t-32 77.5zM692 1452q0 46 31.5 77.5t77.5 31.5q47 0 78.5 -31.5t31.5 -77.5t-31.5 -77.5t-78.5 -31.5q-46 0 -77.5 31.5t-31.5 77.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="995" d="M23 1077h184l297 -952l291 952h178l-363 -1083q-30 -91 -68 -159t-94 -125.5t-136.5 -92.5t-186.5 -49l-21 135q81 15 136.5 38.5t93.5 62t62 82.5t50 114h-59zM256 1403l401 229l78 -147l-426 -174z" />
<glyph unicode="&#xfe;" horiz-adv-x="1210" d="M201 -432v1943l172 21v-588q121 158 309 158q205 0 301 -146.5t96 -414.5q0 -167 -46.5 -293t-142.5 -199.5t-231 -73.5q-183 0 -286 127v-505zM373 254q91 -137 248 -137q274 0 274 424t-254 424q-155 0 -268 -172v-539z" />
<glyph unicode="&#xff;" horiz-adv-x="995" d="M23 1077h184l297 -952l291 952h178l-363 -1083q-30 -91 -68 -159t-94 -125.5t-136.5 -92.5t-186.5 -49l-21 135q81 15 136.5 38.5t93.5 62t62 82.5t50 114h-59zM178 1452q0 46 32 77.5t79 31.5q46 0 77 -31.5t31 -77.5t-31 -77.5t-77 -31.5q-47 0 -79 31.5t-32 77.5z M598 1452q0 46 31.5 77.5t77.5 31.5q47 0 78.5 -31.5t31.5 -77.5t-31.5 -77.5t-78.5 -31.5q-46 0 -77.5 31.5t-31.5 77.5z" />
<glyph unicode="&#x152;" horiz-adv-x="1824" d="M119 700q0 171 42.5 310.5t118 231.5t178.5 142t224 50q79 0 156 -25h864l-21 -143h-620q172 -159 194 -475h353v-144h-353q-8 -161 -60 -290t-144 -214h665v-143h-885q-73 -25 -149 -25q-122 0 -224.5 47.5t-178 137.5t-118 228.5t-42.5 311.5zM307 700q0 -293 104 -435 t277 -142q178 0 279.5 142.5t101.5 441.5q0 295 -101.5 437t-279.5 142q-175 0 -278 -144.5t-103 -441.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1875" d="M121 537q0 257 126 411t349 154q262 0 383 -228q120 228 358 228q207 0 318.5 -140.5t111.5 -388.5q0 -48 -4 -94h-694q11 -187 91 -274.5t210 -87.5q80 0 145.5 22.5t135.5 73.5l75 -102q-168 -136 -370 -136q-258 0 -383 232q-123 -232 -379 -232q-223 0 -348 151.5 t-125 410.5zM305 537q0 -211 74 -316.5t215 -105.5q142 0 216.5 106t74.5 320q0 211 -74 316.5t-215 105.5t-216 -106.5t-75 -319.5zM1069 606h528v17q0 164 -65.5 253t-190.5 89q-252 0 -272 -359z" />
<glyph unicode="&#x178;" horiz-adv-x="1114" d="M14 1409h197l352 -709l350 709h187l-453 -864v-545h-180v543zM238 1710q0 46 31.5 77.5t78.5 31.5q46 0 77.5 -31.5t31.5 -77.5t-31.5 -77t-77.5 -31q-47 0 -78.5 31t-31.5 77zM657 1710q0 46 31.5 77.5t77.5 31.5q47 0 79 -31.5t32 -77.5t-32 -77t-79 -31 q-46 0 -77.5 31t-31.5 77z" />
<glyph unicode="&#x2c6;" horiz-adv-x="784" d="M61 1389l275 227h113l274 -227l-74 -86l-256 172l-258 -172z" />
<glyph unicode="&#x2dc;" horiz-adv-x="790" d="M61 1407q83 170 215 170q47 0 90.5 -24t80 -48t67.5 -24q30 0 55.5 21.5t55.5 68.5l104 -53q-77 -170 -215 -170q-45 0 -89 24.5t-81.5 49t-67.5 24.5q-34 0 -58 -21.5t-52 -70.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="981" />
<glyph unicode="&#x2001;" horiz-adv-x="1962" />
<glyph unicode="&#x2002;" horiz-adv-x="981" />
<glyph unicode="&#x2003;" horiz-adv-x="1962" />
<glyph unicode="&#x2004;" horiz-adv-x="654" />
<glyph unicode="&#x2005;" horiz-adv-x="490" />
<glyph unicode="&#x2006;" horiz-adv-x="327" />
<glyph unicode="&#x2007;" horiz-adv-x="327" />
<glyph unicode="&#x2008;" horiz-adv-x="245" />
<glyph unicode="&#x2009;" horiz-adv-x="392" />
<glyph unicode="&#x200a;" horiz-adv-x="109" />
<glyph unicode="&#x2010;" horiz-adv-x="823" d="M127 567v148h571v-148h-571z" />
<glyph unicode="&#x2011;" horiz-adv-x="823" d="M127 567v148h571v-148h-571z" />
<glyph unicode="&#x2012;" horiz-adv-x="823" d="M127 567v148h571v-148h-571z" />
<glyph unicode="&#x2013;" horiz-adv-x="1064" d="M70 567v148h925v-148h-925z" />
<glyph unicode="&#x2014;" horiz-adv-x="1613" d="M70 567v148h1476v-148h-1476z" />
<glyph unicode="&#x2018;" horiz-adv-x="462" d="M98 1141q0 39 33 112l133 283h113l-80 -299q49 -37 49 -96q0 -50 -36 -85.5t-87 -35.5q-52 0 -88.5 35.5t-36.5 85.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="462" d="M86 1022l80 299q-49 37 -49 96q0 50 36 85.5t87 35.5q52 0 88.5 -35.5t36.5 -85.5q0 -39 -33 -112l-133 -283h-113z" />
<glyph unicode="&#x201a;" horiz-adv-x="462" d="M86 -311l80 299q-49 37 -49 96q0 50 36 85.5t87 35.5q52 0 88.5 -35.5t36.5 -85.5q0 -40 -33 -113l-133 -282h-113z" />
<glyph unicode="&#x201c;" horiz-adv-x="815" d="M98 1141q0 39 33 112l133 283h113l-80 -299q49 -37 49 -96q0 -50 -36 -85.5t-87 -35.5q-52 0 -88.5 35.5t-36.5 85.5zM451 1141q0 42 32 112l133 283h113l-80 -299q49 -37 49 -96q0 -50 -36 -85.5t-87 -35.5q-52 0 -88 35.5t-36 85.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="815" d="M86 1022l80 299q-49 37 -49 96q0 50 36 85.5t87 35.5q52 0 88.5 -35.5t36.5 -85.5q0 -39 -33 -112l-133 -283h-113zM438 1022l80 299q-49 37 -49 96q0 50 36 85.5t87 35.5q52 0 88.5 -35.5t36.5 -85.5q0 -39 -33 -112l-133 -283h-113z" />
<glyph unicode="&#x201e;" horiz-adv-x="815" d="M86 -311l80 299q-49 37 -49 96q0 50 36 85.5t87 35.5q52 0 88.5 -35.5t36.5 -85.5q0 -40 -33 -113l-133 -282h-113zM438 -311l80 299q-49 37 -49 96q0 50 36 85.5t87 35.5q52 0 88.5 -35.5t36.5 -85.5q0 -40 -33 -113l-133 -282h-113z" />
<glyph unicode="&#x2022;" horiz-adv-x="645" d="M113 702q0 91 60 151t151 60t151 -61t60 -152t-60 -151t-151 -60t-151 61t-60 152z" />
<glyph unicode="&#x2026;" horiz-adv-x="1458" d="M104 109q0 54 38.5 92.5t93.5 38.5q58 0 95.5 -38t37.5 -93q0 -56 -37.5 -95t-95.5 -39q-55 0 -93.5 39t-38.5 95zM596 109q0 54 38 92.5t93 38.5q58 0 95.5 -38t37.5 -93q0 -56 -37.5 -95t-95.5 -39q-55 0 -93 39t-38 95zM1087 109q0 54 38.5 92.5t93.5 38.5 q58 0 95.5 -38t37.5 -93q0 -56 -37.5 -95t-95.5 -39q-55 0 -93.5 39t-38.5 95z" />
<glyph unicode="&#x202f;" horiz-adv-x="392" />
<glyph unicode="&#x2039;" horiz-adv-x="698" d="M121 528v117l352 442l105 -69l-279 -430l279 -428l-105 -72z" />
<glyph unicode="&#x203a;" horiz-adv-x="698" d="M121 160l276 428l-276 430l102 69l355 -442v-117l-355 -440z" />
<glyph unicode="&#x205f;" horiz-adv-x="490" />
<glyph unicode="&#x20ac;" horiz-adv-x="1177" d="M57 479l29 115h143v190h-172l29 119h152q47 231 185.5 358.5t330.5 127.5q196 0 366 -113l-67 -119q-78 47 -142.5 67.5t-148.5 20.5q-128 0 -215.5 -84t-124.5 -258h530l-28 -119h-512v-190h473l-27 -115h-440q26 -187 111.5 -272.5t242.5 -85.5q80 0 144.5 21.5 t142.5 62.5v-150q-157 -80 -314 -80q-211 0 -341.5 128.5t-167.5 375.5h-179z" />
<glyph unicode="&#x2122;" horiz-adv-x="1798" d="M74 1288v121h680l-23 -121h-250v-678h-145v678h-262zM805 610l33 799h198l195 -567l182 567h201l33 -799h-142l-18 426q-5 147 2 246h-2l-193 -586h-127l-211 584h-2q8 -108 2 -244l-16 -426h-135z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1075" d="M0 0v1075h1075v-1075h-1075z" />
<glyph unicode="&#xfb03;" horiz-adv-x="1755" d="M27 944v133h172v133q0 138 89 219.5t259 81.5q85 0 154.5 -15.5t160.5 -55.5q92 92 279 92q156 0 301 -74l-62 -121q-112 56 -233 56q-107 0 -150 -40t-43 -126v-150h602v-1077h-172v944h-430v-944h-172v944h-411v-944h-172v944h-172zM371 1077h411v150q0 53 15 100 q-120 45 -240 45q-101 0 -143.5 -38t-42.5 -122v-135z" />
<glyph unicode="&#xfb04;" horiz-adv-x="1792" d="M27 944v133h172v133q0 138 89 219.5t259 81.5q85 0 154.5 -15.5t158.5 -55.5q89 92 270 92q125 0 256 -41l173 22v-1304q0 -46 16 -68t55 -22q40 0 70 12l45 -121q-72 -35 -152 -35q-99 0 -153 59t-54 173v1149q-118 37 -245 37q-101 0 -144 -38.5t-43 -121.5v-156h267 l-19 -133h-248v-944h-172v944h-411v-944h-172v944h-172zM371 1077h411v154q0 49 13 96q-117 45 -238 45q-101 0 -143.5 -38t-42.5 -122v-135z" />
<hkern u1="C" u2="&#x2d;" k="123" />
<hkern u1="Q" u2="&#x7d;" k="-23" />
<hkern u1="Q" u2="]" k="-23" />
<hkern u1="Q" u2="&#x29;" k="-6" />
<hkern u1="_" u2="y" k="20" />
<hkern u1="f" u2="&#xf0;" k="4" />
<hkern u1="&#xc7;" u2="&#x2d;" k="123" />
<hkern u1="&#xde;" u2="&#xf0;" k="31" />
<hkern g1="ampersand" 	g2="asterisk,registered" 	k="82" />
<hkern g1="ampersand" 	g2="at" 	k="41" />
<hkern g1="ampersand" 	g2="backslash" 	k="205" />
<hkern g1="ampersand" 	g2="bullet" 	k="41" />
<hkern g1="ampersand" 	g2="four" 	k="41" />
<hkern g1="ampersand" 	g2="nine" 	k="41" />
<hkern g1="ampersand" 	g2="one" 	k="139" />
<hkern g1="ampersand" 	g2="percent" 	k="82" />
<hkern g1="ampersand" 	g2="question" 	k="164" />
<hkern g1="ampersand" 	g2="seven" 	k="61" />
<hkern g1="ampersand" 	g2="slash" 	k="82" />
<hkern g1="ampersand" 	g2="trademark" 	k="123" />
<hkern g1="ampersand" 	g2="yen" 	k="102" />
<hkern g1="at" 	g2="asterisk,registered" 	k="61" />
<hkern g1="at" 	g2="backslash" 	k="102" />
<hkern g1="at" 	g2="nine" 	k="20" />
<hkern g1="at" 	g2="one" 	k="61" />
<hkern g1="at" 	g2="question" 	k="82" />
<hkern g1="at" 	g2="seven" 	k="61" />
<hkern g1="at" 	g2="trademark" 	k="61" />
<hkern g1="at" 	g2="yen" 	k="61" />
<hkern g1="at" 	g2="three" 	k="41" />
<hkern g1="at" 	g2="two" 	k="41" />
<hkern g1="at" 	g2="underscore" 	k="102" />
<hkern g1="at" 	g2="ampersand" 	k="41" />
<hkern g1="at" 	g2="guillemotright,guilsinglright" 	k="61" />
<hkern g1="copyright" 	g2="slash" 	k="102" />
<hkern g1="copyright" 	g2="underscore" 	k="205" />
<hkern g1="degree" 	g2="asterisk,registered" 	k="145" />
<hkern g1="degree" 	g2="backslash" 	k="186" />
<hkern g1="degree" 	g2="question" 	k="205" />
<hkern g1="degree" 	g2="slash" 	k="350" />
<hkern g1="degree" 	g2="underscore" 	k="205" />
<hkern g1="degree" 	g2="exclam" 	k="145" />
<hkern g1="less,equal,greater,multiply" 	g2="nine" 	k="20" />
<hkern g1="less,equal,greater,multiply" 	g2="one" 	k="41" />
<hkern g1="less,equal,greater,multiply" 	g2="question" 	k="82" />
<hkern g1="less,equal,greater,multiply" 	g2="yen" 	k="41" />
<hkern g1="less,equal,greater,multiply" 	g2="three" 	k="74" />
<hkern g1="Euro" 	g2="asterisk,registered" 	k="-20" />
<hkern g1="Euro" 	g2="at" 	k="41" />
<hkern g1="Euro" 	g2="bullet" 	k="82" />
<hkern g1="Euro" 	g2="four" 	k="74" />
<hkern g1="Euro" 	g2="nine" 	k="35" />
<hkern g1="Euro" 	g2="one" 	k="61" />
<hkern g1="Euro" 	g2="eight" 	k="31" />
<hkern g1="Euro" 	g2="Euro" 	k="41" />
<hkern g1="Euro" 	g2="two" 	k="20" />
<hkern g1="Euro" 	g2="ampersand" 	k="61" />
<hkern g1="Euro" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="Euro" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="Euro" 	g2="less,equal,greater,multiply" 	k="61" />
<hkern g1="Euro" 	g2="six" 	k="41" />
<hkern g1="Euro" 	g2="zero" 	k="41" />
<hkern g1="percent" 	g2="asterisk,registered" 	k="102" />
<hkern g1="percent" 	g2="backslash" 	k="123" />
<hkern g1="percent" 	g2="one" 	k="123" />
<hkern g1="percent" 	g2="question" 	k="123" />
<hkern g1="percent" 	g2="trademark" 	k="123" />
<hkern g1="percent" 	g2="yen" 	k="61" />
<hkern g1="section" 	g2="four" 	k="41" />
<hkern g1="section" 	g2="one" 	k="82" />
<hkern g1="section" 	g2="seven" 	k="61" />
<hkern g1="section" 	g2="two" 	k="20" />
<hkern g1="sterling" 	g2="at" 	k="41" />
<hkern g1="sterling" 	g2="backslash" 	k="102" />
<hkern g1="sterling" 	g2="bullet" 	k="82" />
<hkern g1="sterling" 	g2="four" 	k="74" />
<hkern g1="sterling" 	g2="nine" 	k="41" />
<hkern g1="sterling" 	g2="one" 	k="82" />
<hkern g1="sterling" 	g2="yen" 	k="20" />
<hkern g1="sterling" 	g2="eight" 	k="41" />
<hkern g1="sterling" 	g2="Euro" 	k="61" />
<hkern g1="sterling" 	g2="ampersand" 	k="61" />
<hkern g1="sterling" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="sterling" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="sterling" 	g2="less,equal,greater,multiply" 	k="41" />
<hkern g1="sterling" 	g2="six" 	k="61" />
<hkern g1="sterling" 	g2="zero" 	k="61" />
<hkern g1="trademark" 	g2="underscore" 	k="205" />
<hkern g1="yen" 	g2="asterisk,registered" 	k="-6" />
<hkern g1="yen" 	g2="at" 	k="61" />
<hkern g1="yen" 	g2="bullet" 	k="41" />
<hkern g1="yen" 	g2="four" 	k="41" />
<hkern g1="yen" 	g2="nine" 	k="31" />
<hkern g1="yen" 	g2="one" 	k="61" />
<hkern g1="yen" 	g2="question" 	k="20" />
<hkern g1="yen" 	g2="slash" 	k="82" />
<hkern g1="yen" 	g2="eight" 	k="20" />
<hkern g1="yen" 	g2="Euro" 	k="41" />
<hkern g1="yen" 	g2="sterling" 	k="61" />
<hkern g1="yen" 	g2="three" 	k="31" />
<hkern g1="yen" 	g2="two" 	k="31" />
<hkern g1="yen" 	g2="underscore" 	k="123" />
<hkern g1="yen" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-6" />
<hkern g1="yen" 	g2="ampersand" 	k="41" />
<hkern g1="yen" 	g2="guillemotright,guilsinglright" 	k="82" />
<hkern g1="yen" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="yen" 	g2="exclam" 	k="-41" />
<hkern g1="yen" 	g2="less,equal,greater,multiply" 	k="41" />
<hkern g1="yen" 	g2="six" 	k="61" />
<hkern g1="yen" 	g2="bracketright" 	k="-6" />
<hkern g1="asterisk,registered" 	g2="asterisk,registered" 	k="-6" />
<hkern g1="asterisk,registered" 	g2="at" 	k="61" />
<hkern g1="asterisk,registered" 	g2="exclam" 	k="-16" />
<hkern g1="asterisk,registered" 	g2="four" 	k="61" />
<hkern g1="asterisk,registered" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="asterisk,registered" 	g2="nine" 	k="-6" />
<hkern g1="asterisk,registered" 	g2="numbersign" 	k="41" />
<hkern g1="asterisk,registered" 	g2="one" 	k="-16" />
<hkern g1="asterisk,registered" 	g2="questiondown" 	k="164" />
<hkern g1="asterisk,registered" 	g2="seven" 	k="-16" />
<hkern g1="asterisk,registered" 	g2="slash" 	k="164" />
<hkern g1="asterisk,registered" 	g2="sterling" 	k="41" />
<hkern g1="asterisk,registered" 	g2="three" 	k="-6" />
<hkern g1="asterisk,registered" 	g2="two" 	k="-16" />
<hkern g1="asterisk,registered" 	g2="underscore" 	k="205" />
<hkern g1="asterisk,registered" 	g2="yen" 	k="-6" />
<hkern g1="asterisk,registered" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-16" />
<hkern g1="backslash" 	g2="asterisk,registered" 	k="164" />
<hkern g1="backslash" 	g2="at" 	k="61" />
<hkern g1="backslash" 	g2="four" 	k="41" />
<hkern g1="backslash" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="backslash" 	g2="nine" 	k="82" />
<hkern g1="backslash" 	g2="one" 	k="98" />
<hkern g1="backslash" 	g2="seven" 	k="82" />
<hkern g1="backslash" 	g2="three" 	k="61" />
<hkern g1="backslash" 	g2="yen" 	k="82" />
<hkern g1="backslash" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="143" />
<hkern g1="backslash" 	g2="ampersand" 	k="123" />
<hkern g1="backslash" 	g2="backslash" 	k="246" />
<hkern g1="backslash" 	g2="bullet" 	k="61" />
<hkern g1="backslash" 	g2="copyright" 	k="102" />
<hkern g1="backslash" 	g2="degree" 	k="186" />
<hkern g1="backslash" 	g2="Euro" 	k="41" />
<hkern g1="backslash" 	g2="percent" 	k="123" />
<hkern g1="backslash" 	g2="question" 	k="164" />
<hkern g1="backslash" 	g2="six" 	k="41" />
<hkern g1="backslash" 	g2="trademark" 	k="164" />
<hkern g1="braceleft" 	g2="questiondown" 	k="-6" />
<hkern g1="braceleft" 	g2="underscore" 	k="-16" />
<hkern g1="bracketleft" 	g2="four" 	k="20" />
<hkern g1="bracketleft" 	g2="one" 	k="57" />
<hkern g1="bracketleft" 	g2="questiondown" 	k="-6" />
<hkern g1="bracketleft" 	g2="underscore" 	k="-16" />
<hkern g1="bracketleft" 	g2="yen" 	k="-6" />
<hkern g1="bullet" 	g2="one" 	k="119" />
<hkern g1="bullet" 	g2="questiondown" 	k="123" />
<hkern g1="bullet" 	g2="seven" 	k="123" />
<hkern g1="bullet" 	g2="slash" 	k="61" />
<hkern g1="bullet" 	g2="three" 	k="102" />
<hkern g1="bullet" 	g2="two" 	k="104" />
<hkern g1="bullet" 	g2="yen" 	k="41" />
<hkern g1="bullet" 	g2="ampersand" 	k="41" />
<hkern g1="bullet" 	g2="backslash" 	k="61" />
<hkern g1="bullet" 	g2="question" 	k="82" />
<hkern g1="bullet" 	g2="trademark" 	k="61" />
<hkern g1="bullet" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="exclam" 	g2="asterisk,registered" 	k="-16" />
<hkern g1="exclam" 	g2="question" 	k="41" />
<hkern g1="exclamdown" 	g2="one" 	k="61" />
<hkern g1="exclamdown" 	g2="seven" 	k="61" />
<hkern g1="exclamdown" 	g2="backslash" 	k="102" />
<hkern g1="exclamdown" 	g2="question" 	k="61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="at" 	k="61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="four" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="guillemotleft,guilsinglleft" 	k="63" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="one" 	k="63" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="seven" 	k="63" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="three" 	k="23" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="two" 	k="33" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="yen" 	k="82" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="backslash" 	k="82" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="bullet" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="trademark" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="asterisk,registered" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="nine" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="one" 	k="104" />
<hkern g1="guillemotright,guilsinglright" 	g2="questiondown" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="seven" 	k="104" />
<hkern g1="guillemotright,guilsinglright" 	g2="slash" 	k="61" />
<hkern g1="guillemotright,guilsinglright" 	g2="three" 	k="94" />
<hkern g1="guillemotright,guilsinglright" 	g2="two" 	k="94" />
<hkern g1="guillemotright,guilsinglright" 	g2="underscore" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="yen" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="backslash" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="question" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="trademark" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="guillemotright,guilsinglright" 	k="248" />
<hkern g1="numbersign" 	g2="asterisk,registered" 	k="-41" />
<hkern g1="numbersign" 	g2="seven" 	k="-6" />
<hkern g1="numbersign" 	g2="three" 	k="18" />
<hkern g1="numbersign" 	g2="two" 	k="18" />
<hkern g1="numbersign" 	g2="underscore" 	k="164" />
<hkern g1="numbersign" 	g2="ampersand" 	k="53" />
<hkern g1="numbersign" 	g2="backslash" 	k="41" />
<hkern g1="numbersign" 	g2="bullet" 	k="41" />
<hkern g1="numbersign" 	g2="question" 	k="41" />
<hkern g1="parenleft" 	g2="four" 	k="20" />
<hkern g1="parenleft" 	g2="one" 	k="41" />
<hkern g1="parenleft" 	g2="questiondown" 	k="-16" />
<hkern g1="parenleft" 	g2="underscore" 	k="-16" />
<hkern g1="question" 	g2="asterisk,registered" 	k="-16" />
<hkern g1="question" 	g2="four" 	k="31" />
<hkern g1="question" 	g2="guillemotleft,guilsinglleft" 	k="53" />
<hkern g1="question" 	g2="numbersign" 	k="61" />
<hkern g1="question" 	g2="questiondown" 	k="205" />
<hkern g1="question" 	g2="slash" 	k="164" />
<hkern g1="question" 	g2="underscore" 	k="205" />
<hkern g1="question" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-16" />
<hkern g1="question" 	g2="ampersand" 	k="82" />
<hkern g1="question" 	g2="question" 	k="78" />
<hkern g1="question" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="questiondown" 	g2="asterisk,registered" 	k="82" />
<hkern g1="questiondown" 	g2="at" 	k="61" />
<hkern g1="questiondown" 	g2="exclam" 	k="61" />
<hkern g1="questiondown" 	g2="four" 	k="102" />
<hkern g1="questiondown" 	g2="guillemotleft,guilsinglleft" 	k="104" />
<hkern g1="questiondown" 	g2="nine" 	k="41" />
<hkern g1="questiondown" 	g2="numbersign" 	k="41" />
<hkern g1="questiondown" 	g2="one" 	k="164" />
<hkern g1="questiondown" 	g2="questiondown" 	k="78" />
<hkern g1="questiondown" 	g2="seven" 	k="82" />
<hkern g1="questiondown" 	g2="three" 	k="41" />
<hkern g1="questiondown" 	g2="two" 	k="41" />
<hkern g1="questiondown" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="123" />
<hkern g1="questiondown" 	g2="ampersand" 	k="61" />
<hkern g1="questiondown" 	g2="backslash" 	k="205" />
<hkern g1="questiondown" 	g2="bullet" 	k="61" />
<hkern g1="questiondown" 	g2="Euro" 	k="82" />
<hkern g1="questiondown" 	g2="percent" 	k="82" />
<hkern g1="questiondown" 	g2="question" 	k="205" />
<hkern g1="questiondown" 	g2="six" 	k="41" />
<hkern g1="questiondown" 	g2="trademark" 	k="102" />
<hkern g1="questiondown" 	g2="guillemotright,guilsinglright" 	k="57" />
<hkern g1="questiondown" 	g2="eight" 	k="41" />
<hkern g1="questiondown" 	g2="exclamdown" 	k="41" />
<hkern g1="questiondown" 	g2="zero" 	k="41" />
<hkern g1="slash" 	g2="at" 	k="102" />
<hkern g1="slash" 	g2="four" 	k="102" />
<hkern g1="slash" 	g2="guillemotleft,guilsinglleft" 	k="123" />
<hkern g1="slash" 	g2="nine" 	k="82" />
<hkern g1="slash" 	g2="numbersign" 	k="41" />
<hkern g1="slash" 	g2="one" 	k="123" />
<hkern g1="slash" 	g2="questiondown" 	k="205" />
<hkern g1="slash" 	g2="slash" 	k="246" />
<hkern g1="slash" 	g2="sterling" 	k="102" />
<hkern g1="slash" 	g2="three" 	k="61" />
<hkern g1="slash" 	g2="two" 	k="82" />
<hkern g1="slash" 	g2="underscore" 	k="246" />
<hkern g1="slash" 	g2="ampersand" 	k="102" />
<hkern g1="slash" 	g2="bullet" 	k="61" />
<hkern g1="slash" 	g2="Euro" 	k="61" />
<hkern g1="slash" 	g2="percent" 	k="41" />
<hkern g1="slash" 	g2="question" 	k="123" />
<hkern g1="slash" 	g2="six" 	k="61" />
<hkern g1="slash" 	g2="guillemotright,guilsinglright" 	k="82" />
<hkern g1="slash" 	g2="eight" 	k="51" />
<hkern g1="slash" 	g2="zero" 	k="20" />
<hkern g1="underscore" 	g2="asterisk,registered" 	k="205" />
<hkern g1="underscore" 	g2="at" 	k="61" />
<hkern g1="underscore" 	g2="four" 	k="246" />
<hkern g1="underscore" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="underscore" 	g2="one" 	k="205" />
<hkern g1="underscore" 	g2="questiondown" 	k="-6" />
<hkern g1="underscore" 	g2="underscore" 	k="20" />
<hkern g1="underscore" 	g2="yen" 	k="123" />
<hkern g1="underscore" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="164" />
<hkern g1="underscore" 	g2="ampersand" 	k="61" />
<hkern g1="underscore" 	g2="backslash" 	k="246" />
<hkern g1="underscore" 	g2="copyright" 	k="205" />
<hkern g1="underscore" 	g2="Euro" 	k="164" />
<hkern g1="underscore" 	g2="percent" 	k="164" />
<hkern g1="underscore" 	g2="question" 	k="164" />
<hkern g1="underscore" 	g2="six" 	k="61" />
<hkern g1="underscore" 	g2="trademark" 	k="205" />
<hkern g1="underscore" 	g2="eight" 	k="41" />
<hkern g1="underscore" 	g2="zero" 	k="61" />
<hkern g1="underscore" 	g2="braceright" 	k="-16" />
<hkern g1="underscore" 	g2="bracketright" 	k="-16" />
<hkern g1="underscore" 	g2="parenright" 	k="-16" />
<hkern g1="eight" 	g2="backslash" 	k="51" />
<hkern g1="eight" 	g2="Euro" 	k="10" />
<hkern g1="eight" 	g2="nine" 	k="35" />
<hkern g1="eight" 	g2="one" 	k="61" />
<hkern g1="eight" 	g2="question" 	k="41" />
<hkern g1="eight" 	g2="seven" 	k="31" />
<hkern g1="eight" 	g2="trademark" 	k="41" />
<hkern g1="eight" 	g2="two" 	k="20" />
<hkern g1="eight" 	g2="underscore" 	k="41" />
<hkern g1="eight" 	g2="yen" 	k="20" />
<hkern g1="four" 	g2="backslash" 	k="102" />
<hkern g1="four" 	g2="nine" 	k="41" />
<hkern g1="four" 	g2="one" 	k="115" />
<hkern g1="four" 	g2="question" 	k="102" />
<hkern g1="four" 	g2="seven" 	k="47" />
<hkern g1="four" 	g2="trademark" 	k="82" />
<hkern g1="four" 	g2="two" 	k="41" />
<hkern g1="four" 	g2="yen" 	k="41" />
<hkern g1="four" 	g2="percent" 	k="82" />
<hkern g1="four" 	g2="asterisk,registered" 	k="82" />
<hkern g1="four" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="four" 	g2="slash" 	k="41" />
<hkern g1="four" 	g2="three" 	k="35" />
<hkern g1="nine" 	g2="backslash" 	k="82" />
<hkern g1="nine" 	g2="one" 	k="41" />
<hkern g1="nine" 	g2="question" 	k="41" />
<hkern g1="nine" 	g2="seven" 	k="33" />
<hkern g1="nine" 	g2="trademark" 	k="20" />
<hkern g1="nine" 	g2="two" 	k="45" />
<hkern g1="nine" 	g2="underscore" 	k="205" />
<hkern g1="nine" 	g2="yen" 	k="20" />
<hkern g1="nine" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-20" />
<hkern g1="nine" 	g2="slash" 	k="82" />
<hkern g1="nine" 	g2="three" 	k="41" />
<hkern g1="nine" 	g2="four" 	k="20" />
<hkern g1="one" 	g2="one" 	k="27" />
<hkern g1="seven" 	g2="Euro" 	k="63" />
<hkern g1="seven" 	g2="nine" 	k="39" />
<hkern g1="seven" 	g2="one" 	k="39" />
<hkern g1="seven" 	g2="trademark" 	k="-41" />
<hkern g1="seven" 	g2="two" 	k="33" />
<hkern g1="seven" 	g2="underscore" 	k="205" />
<hkern g1="seven" 	g2="bullet" 	k="53" />
<hkern g1="seven" 	g2="degree" 	k="-20" />
<hkern g1="seven" 	g2="eight" 	k="43" />
<hkern g1="seven" 	g2="guillemotleft,guilsinglleft" 	k="104" />
<hkern g1="seven" 	g2="percent" 	k="-20" />
<hkern g1="seven" 	g2="six" 	k="49" />
<hkern g1="seven" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-16" />
<hkern g1="seven" 	g2="asterisk,registered" 	k="-16" />
<hkern g1="seven" 	g2="guillemotright,guilsinglright" 	k="63" />
<hkern g1="seven" 	g2="slash" 	k="82" />
<hkern g1="seven" 	g2="three" 	k="23" />
<hkern g1="seven" 	g2="exclam" 	k="-41" />
<hkern g1="seven" 	g2="four" 	k="82" />
<hkern g1="seven" 	g2="ampersand" 	k="41" />
<hkern g1="seven" 	g2="at" 	k="41" />
<hkern g1="seven" 	g2="numbersign" 	k="41" />
<hkern g1="seven" 	g2="sterling" 	k="61" />
<hkern g1="seven" 	g2="zero" 	k="25" />
<hkern g1="six" 	g2="backslash" 	k="61" />
<hkern g1="six" 	g2="nine" 	k="31" />
<hkern g1="six" 	g2="one" 	k="98" />
<hkern g1="six" 	g2="question" 	k="61" />
<hkern g1="six" 	g2="seven" 	k="51" />
<hkern g1="six" 	g2="trademark" 	k="61" />
<hkern g1="six" 	g2="two" 	k="25" />
<hkern g1="six" 	g2="underscore" 	k="41" />
<hkern g1="six" 	g2="yen" 	k="61" />
<hkern g1="six" 	g2="degree" 	k="20" />
<hkern g1="six" 	g2="asterisk,registered" 	k="41" />
<hkern g1="six" 	g2="slash" 	k="41" />
<hkern g1="six" 	g2="three" 	k="20" />
<hkern g1="three" 	g2="backslash" 	k="41" />
<hkern g1="three" 	g2="nine" 	k="25" />
<hkern g1="three" 	g2="one" 	k="53" />
<hkern g1="three" 	g2="question" 	k="20" />
<hkern g1="three" 	g2="seven" 	k="10" />
<hkern g1="three" 	g2="two" 	k="20" />
<hkern g1="three" 	g2="underscore" 	k="41" />
<hkern g1="three" 	g2="yen" 	k="25" />
<hkern g1="three" 	g2="slash" 	k="41" />
<hkern g1="three" 	g2="four" 	k="12" />
<hkern g1="two" 	g2="backslash" 	k="41" />
<hkern g1="two" 	g2="Euro" 	k="31" />
<hkern g1="two" 	g2="nine" 	k="20" />
<hkern g1="two" 	g2="one" 	k="53" />
<hkern g1="two" 	g2="question" 	k="20" />
<hkern g1="two" 	g2="seven" 	k="20" />
<hkern g1="two" 	g2="yen" 	k="31" />
<hkern g1="two" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="two" 	g2="three" 	k="10" />
<hkern g1="two" 	g2="four" 	k="55" />
<hkern g1="two" 	g2="sterling" 	k="20" />
<hkern g1="zero" 	g2="backslash" 	k="20" />
<hkern g1="zero" 	g2="nine" 	k="20" />
<hkern g1="zero" 	g2="one" 	k="53" />
<hkern g1="zero" 	g2="question" 	k="41" />
<hkern g1="zero" 	g2="seven" 	k="41" />
<hkern g1="zero" 	g2="trademark" 	k="41" />
<hkern g1="zero" 	g2="two" 	k="25" />
<hkern g1="zero" 	g2="underscore" 	k="61" />
<hkern g1="zero" 	g2="three" 	k="20" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="backslash" 	k="164" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="Euro" 	k="-6" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="one" 	k="123" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="question" 	k="82" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="seven" 	k="41" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="trademark" 	k="41" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="underscore" 	k="41" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="yen" 	k="31" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="eight" 	k="-6" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="percent" 	k="61" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="asterisk,registered" 	k="61" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="three" 	k="-16" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="four" 	k="-16" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="nine" 	k="-6" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="one" 	k="-6" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="seven" 	k="-6" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="two" 	k="-6" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="underscore" 	k="164" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="yen" 	k="-6" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="asterisk,registered" 	k="-16" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="slash" 	k="123" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="questiondown" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="98" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk,registered" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="63" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="braceright" 	k="-16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="bracketright" 	k="-16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="bullet" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="exclam" 	k="-4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="four" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="nine" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="one" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="seven" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="two" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="35" />
<hkern g1="B" 	g2="T" 	k="41" />
<hkern g1="B" 	g2="V" 	k="31" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="47" />
<hkern g1="B" 	g2="asterisk,registered" 	k="20" />
<hkern g1="B" 	g2="backslash" 	k="41" />
<hkern g1="B" 	g2="four" 	k="20" />
<hkern g1="B" 	g2="nine" 	k="41" />
<hkern g1="B" 	g2="one" 	k="82" />
<hkern g1="B" 	g2="question" 	k="41" />
<hkern g1="B" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="B" 	g2="seven" 	k="61" />
<hkern g1="B" 	g2="trademark" 	k="41" />
<hkern g1="B" 	g2="two" 	k="31" />
<hkern g1="B" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="B" 	g2="AE" 	k="20" />
<hkern g1="B" 	g2="J" 	k="31" />
<hkern g1="B" 	g2="dollar,S" 	k="41" />
<hkern g1="B" 	g2="W" 	k="6" />
<hkern g1="B" 	g2="X" 	k="20" />
<hkern g1="B" 	g2="Z" 	k="31" />
<hkern g1="B" 	g2="five" 	k="20" />
<hkern g1="B" 	g2="underscore" 	k="41" />
<hkern g1="B" 	g2="x" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="35" />
<hkern g1="C,Ccedilla" 	g2="backslash" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="bullet" 	k="123" />
<hkern g1="C,Ccedilla" 	g2="four" 	k="104" />
<hkern g1="C,Ccedilla" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="164" />
<hkern g1="C,Ccedilla" 	g2="nine" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="one" 	k="109" />
<hkern g1="C,Ccedilla" 	g2="seven" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="16" />
<hkern g1="C,Ccedilla" 	g2="dollar,S" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="6" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="five" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="x" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="v,y,yacute,ydieresis" 	k="102" />
<hkern g1="C,Ccedilla" 	g2="M" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="ampersand" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="at" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="eight" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="less,equal,greater,multiply" 	k="115" />
<hkern g1="C,Ccedilla" 	g2="f,uniFB01,uniFB02" 	k="45" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="C,Ccedilla" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="j" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="l" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="m,n,p,r,mu,ntilde" 	k="33" />
<hkern g1="C,Ccedilla" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="s" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="six" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="35" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="z" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="zero" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="asterisk,registered" 	k="-6" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="backslash" 	k="61" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="bullet" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="four" 	k="63" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="nine" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="one" 	k="47" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="J" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="dollar,S" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Z" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v,y,yacute,ydieresis" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="at" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="eight" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="l" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="m,n,p,r,mu,ntilde" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="six" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="zero" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-6" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="35" />
<hkern g1="F" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="F" 	g2="bracketright" 	k="-6" />
<hkern g1="F" 	g2="colon,semicolon" 	k="41" />
<hkern g1="F" 	g2="exclam" 	k="-4" />
<hkern g1="F" 	g2="four" 	k="82" />
<hkern g1="F" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="F" 	g2="nine" 	k="41" />
<hkern g1="F" 	g2="one" 	k="61" />
<hkern g1="F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="F" 	g2="two" 	k="20" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="F" 	g2="AE" 	k="143" />
<hkern g1="F" 	g2="J" 	k="16" />
<hkern g1="F" 	g2="dollar,S" 	k="41" />
<hkern g1="F" 	g2="Z" 	k="41" />
<hkern g1="F" 	g2="five" 	k="41" />
<hkern g1="F" 	g2="underscore" 	k="205" />
<hkern g1="F" 	g2="x" 	k="123" />
<hkern g1="F" 	g2="slash" 	k="102" />
<hkern g1="F" 	g2="v,y,yacute,ydieresis" 	k="72" />
<hkern g1="F" 	g2="M" 	k="37" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="123" />
<hkern g1="F" 	g2="ampersand" 	k="61" />
<hkern g1="F" 	g2="at" 	k="41" />
<hkern g1="F" 	g2="eight" 	k="51" />
<hkern g1="F" 	g2="less,equal,greater,multiply" 	k="43" />
<hkern g1="F" 	g2="f,uniFB01,uniFB02" 	k="82" />
<hkern g1="F" 	g2="g" 	k="92" />
<hkern g1="F" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="82" />
<hkern g1="F" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="F" 	g2="j" 	k="66" />
<hkern g1="F" 	g2="l" 	k="20" />
<hkern g1="F" 	g2="m,n,p,r,mu,ntilde" 	k="72" />
<hkern g1="F" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="72" />
<hkern g1="F" 	g2="s" 	k="92" />
<hkern g1="F" 	g2="six" 	k="31" />
<hkern g1="F" 	g2="t" 	k="27" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="76" />
<hkern g1="F" 	g2="w" 	k="51" />
<hkern g1="F" 	g2="z" 	k="102" />
<hkern g1="F" 	g2="zero" 	k="20" />
<hkern g1="F" 	g2="i" 	k="72" />
<hkern g1="F" 	g2="numbersign" 	k="61" />
<hkern g1="G" 	g2="T" 	k="37" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="47" />
<hkern g1="G" 	g2="backslash" 	k="31" />
<hkern g1="G" 	g2="nine" 	k="20" />
<hkern g1="G" 	g2="one" 	k="57" />
<hkern g1="G" 	g2="question" 	k="20" />
<hkern g1="G" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="G" 	g2="seven" 	k="35" />
<hkern g1="G" 	g2="trademark" 	k="20" />
<hkern g1="G" 	g2="two" 	k="20" />
<hkern g1="G" 	g2="J" 	k="6" />
<hkern g1="G" 	g2="X" 	k="6" />
<hkern g1="G" 	g2="Z" 	k="10" />
<hkern g1="H,I,N,Igrave,Ntilde" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-16" />
<hkern g1="Iacute,Icircumflex,Idieresis" 	g2="backslash" 	k="-16" />
<hkern g1="Iacute,Icircumflex,Idieresis" 	g2="braceright" 	k="-70" />
<hkern g1="Iacute,Icircumflex,Idieresis" 	g2="bracketright" 	k="-70" />
<hkern g1="Iacute,Icircumflex,Idieresis" 	g2="parenright" 	k="-82" />
<hkern g1="J" 	g2="underscore" 	k="41" />
<hkern g1="J" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-16" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="94" />
<hkern g1="L" 	g2="T" 	k="166" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="51" />
<hkern g1="L" 	g2="V" 	k="156" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="184" />
<hkern g1="L" 	g2="asterisk,registered" 	k="164" />
<hkern g1="L" 	g2="backslash" 	k="221" />
<hkern g1="L" 	g2="bullet" 	k="164" />
<hkern g1="L" 	g2="four" 	k="88" />
<hkern g1="L" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="205" />
<hkern g1="L" 	g2="nine" 	k="123" />
<hkern g1="L" 	g2="one" 	k="246" />
<hkern g1="L" 	g2="question" 	k="164" />
<hkern g1="L" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="205" />
<hkern g1="L" 	g2="seven" 	k="63" />
<hkern g1="L" 	g2="trademark" 	k="205" />
<hkern g1="L" 	g2="two" 	k="33" />
<hkern g1="L" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="205" />
<hkern g1="L" 	g2="J" 	k="37" />
<hkern g1="L" 	g2="dollar,S" 	k="33" />
<hkern g1="L" 	g2="W" 	k="82" />
<hkern g1="L" 	g2="five" 	k="20" />
<hkern g1="L" 	g2="slash" 	k="41" />
<hkern g1="L" 	g2="v,y,yacute,ydieresis" 	k="139" />
<hkern g1="L" 	g2="M" 	k="20" />
<hkern g1="L" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="L" 	g2="ampersand" 	k="41" />
<hkern g1="L" 	g2="eight" 	k="20" />
<hkern g1="L" 	g2="less,equal,greater,multiply" 	k="61" />
<hkern g1="L" 	g2="f,uniFB01,uniFB02" 	k="57" />
<hkern g1="L" 	g2="g" 	k="51" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="123" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="L" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="L" 	g2="j" 	k="31" />
<hkern g1="L" 	g2="l" 	k="25" />
<hkern g1="L" 	g2="m,n,p,r,mu,ntilde" 	k="20" />
<hkern g1="L" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="L" 	g2="s" 	k="33" />
<hkern g1="L" 	g2="six" 	k="61" />
<hkern g1="L" 	g2="t" 	k="47" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="L" 	g2="w" 	k="88" />
<hkern g1="L" 	g2="zero" 	k="61" />
<hkern g1="L" 	g2="igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="L" 	g2="copyright" 	k="164" />
<hkern g1="M" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="M" 	g2="T" 	k="27" />
<hkern g1="M" 	g2="V" 	k="6" />
<hkern g1="M" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="M" 	g2="four" 	k="20" />
<hkern g1="M" 	g2="nine" 	k="10" />
<hkern g1="M" 	g2="one" 	k="27" />
<hkern g1="M" 	g2="question" 	k="20" />
<hkern g1="M" 	g2="seven" 	k="20" />
<hkern g1="M" 	g2="trademark" 	k="41" />
<hkern g1="M" 	g2="J" 	k="6" />
<hkern g1="M" 	g2="dollar,S" 	k="31" />
<hkern g1="M" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="72" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="one" 	k="25" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="seven" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="two" 	k="51" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="16" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="27" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="51" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="underscore" 	k="82" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="three" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="M" 	k="14" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="20" />
<hkern g1="P" 	g2="T" 	k="51" />
<hkern g1="P" 	g2="V" 	k="41" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="76" />
<hkern g1="P" 	g2="backslash" 	k="102" />
<hkern g1="P" 	g2="colon,semicolon" 	k="10" />
<hkern g1="P" 	g2="four" 	k="94" />
<hkern g1="P" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="33" />
<hkern g1="P" 	g2="nine" 	k="10" />
<hkern g1="P" 	g2="one" 	k="27" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="246" />
<hkern g1="P" 	g2="question" 	k="82" />
<hkern g1="P" 	g2="seven" 	k="41" />
<hkern g1="P" 	g2="trademark" 	k="20" />
<hkern g1="P" 	g2="two" 	k="53" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="123" />
<hkern g1="P" 	g2="AE" 	k="160" />
<hkern g1="P" 	g2="J" 	k="57" />
<hkern g1="P" 	g2="dollar,S" 	k="43" />
<hkern g1="P" 	g2="W" 	k="20" />
<hkern g1="P" 	g2="X" 	k="61" />
<hkern g1="P" 	g2="Z" 	k="76" />
<hkern g1="P" 	g2="five" 	k="31" />
<hkern g1="P" 	g2="underscore" 	k="205" />
<hkern g1="P" 	g2="x" 	k="31" />
<hkern g1="P" 	g2="slash" 	k="102" />
<hkern g1="P" 	g2="three" 	k="41" />
<hkern g1="P" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="P" 	g2="M" 	k="61" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="115" />
<hkern g1="P" 	g2="ampersand" 	k="78" />
<hkern g1="P" 	g2="at" 	k="61" />
<hkern g1="P" 	g2="eight" 	k="41" />
<hkern g1="P" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="P" 	g2="g" 	k="55" />
<hkern g1="P" 	g2="b,h,k,germandbls,thorn" 	k="41" />
<hkern g1="P" 	g2="j" 	k="41" />
<hkern g1="P" 	g2="l" 	k="20" />
<hkern g1="P" 	g2="m,n,p,r,mu,ntilde" 	k="41" />
<hkern g1="P" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="P" 	g2="s" 	k="70" />
<hkern g1="P" 	g2="six" 	k="20" />
<hkern g1="P" 	g2="t" 	k="10" />
<hkern g1="P" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="55" />
<hkern g1="P" 	g2="w" 	k="10" />
<hkern g1="P" 	g2="z" 	k="61" />
<hkern g1="P" 	g2="numbersign" 	k="61" />
<hkern g1="R" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="R" 	g2="T" 	k="41" />
<hkern g1="R" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="R" 	g2="V" 	k="14" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="R" 	g2="backslash" 	k="41" />
<hkern g1="R" 	g2="braceright" 	k="-6" />
<hkern g1="R" 	g2="bracketright" 	k="-16" />
<hkern g1="R" 	g2="bullet" 	k="41" />
<hkern g1="R" 	g2="four" 	k="41" />
<hkern g1="R" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="47" />
<hkern g1="R" 	g2="nine" 	k="10" />
<hkern g1="R" 	g2="one" 	k="27" />
<hkern g1="R" 	g2="question" 	k="41" />
<hkern g1="R" 	g2="seven" 	k="31" />
<hkern g1="R" 	g2="trademark" 	k="20" />
<hkern g1="R" 	g2="J" 	k="20" />
<hkern g1="R" 	g2="dollar,S" 	k="14" />
<hkern g1="R" 	g2="Z" 	k="14" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="R" 	g2="ampersand" 	k="20" />
<hkern g1="R" 	g2="g" 	k="20" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="R" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="dollar,S" 	g2="T" 	k="45" />
<hkern g1="dollar,S" 	g2="V" 	k="41" />
<hkern g1="dollar,S" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="dollar,S" 	g2="backslash" 	k="61" />
<hkern g1="dollar,S" 	g2="nine" 	k="35" />
<hkern g1="dollar,S" 	g2="one" 	k="57" />
<hkern g1="dollar,S" 	g2="question" 	k="41" />
<hkern g1="dollar,S" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="dollar,S" 	g2="seven" 	k="41" />
<hkern g1="dollar,S" 	g2="trademark" 	k="31" />
<hkern g1="dollar,S" 	g2="two" 	k="20" />
<hkern g1="dollar,S" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="41" />
<hkern g1="dollar,S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="dollar,S" 	g2="AE" 	k="20" />
<hkern g1="dollar,S" 	g2="J" 	k="6" />
<hkern g1="dollar,S" 	g2="dollar,S" 	k="25" />
<hkern g1="dollar,S" 	g2="W" 	k="20" />
<hkern g1="dollar,S" 	g2="X" 	k="14" />
<hkern g1="dollar,S" 	g2="Z" 	k="31" />
<hkern g1="dollar,S" 	g2="underscore" 	k="61" />
<hkern g1="dollar,S" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="dollar,S" 	g2="M" 	k="20" />
<hkern g1="dollar,S" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="dollar,S" 	g2="z" 	k="20" />
<hkern g1="dollar,S" 	g2="yen" 	k="20" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="72" />
<hkern g1="T" 	g2="asterisk,registered" 	k="-6" />
<hkern g1="T" 	g2="backslash" 	k="61" />
<hkern g1="T" 	g2="bracketright" 	k="-16" />
<hkern g1="T" 	g2="bullet" 	k="102" />
<hkern g1="T" 	g2="colon,semicolon" 	k="82" />
<hkern g1="T" 	g2="exclam" 	k="-6" />
<hkern g1="T" 	g2="four" 	k="102" />
<hkern g1="T" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="102" />
<hkern g1="T" 	g2="nine" 	k="41" />
<hkern g1="T" 	g2="one" 	k="41" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="123" />
<hkern g1="T" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="T" 	g2="AE" 	k="150" />
<hkern g1="T" 	g2="J" 	k="27" />
<hkern g1="T" 	g2="dollar,S" 	k="41" />
<hkern g1="T" 	g2="Z" 	k="41" />
<hkern g1="T" 	g2="five" 	k="41" />
<hkern g1="T" 	g2="underscore" 	k="102" />
<hkern g1="T" 	g2="x" 	k="68" />
<hkern g1="T" 	g2="slash" 	k="102" />
<hkern g1="T" 	g2="v,y,yacute,ydieresis" 	k="82" />
<hkern g1="T" 	g2="M" 	k="41" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="102" />
<hkern g1="T" 	g2="ampersand" 	k="82" />
<hkern g1="T" 	g2="at" 	k="102" />
<hkern g1="T" 	g2="eight" 	k="41" />
<hkern g1="T" 	g2="f,uniFB01,uniFB02" 	k="82" />
<hkern g1="T" 	g2="g" 	k="102" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="T" 	g2="j" 	k="61" />
<hkern g1="T" 	g2="l" 	k="20" />
<hkern g1="T" 	g2="m,n,p,r,mu,ntilde" 	k="61" />
<hkern g1="T" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="T" 	g2="s" 	k="102" />
<hkern g1="T" 	g2="six" 	k="82" />
<hkern g1="T" 	g2="t" 	k="82" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="82" />
<hkern g1="T" 	g2="w" 	k="61" />
<hkern g1="T" 	g2="z" 	k="82" />
<hkern g1="T" 	g2="zero" 	k="61" />
<hkern g1="T" 	g2="numbersign" 	k="102" />
<hkern g1="Thorn" 	g2="T" 	k="82" />
<hkern g1="Thorn" 	g2="V" 	k="47" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ydieresis" 	k="98" />
<hkern g1="Thorn" 	g2="backslash" 	k="82" />
<hkern g1="Thorn" 	g2="four" 	k="33" />
<hkern g1="Thorn" 	g2="nine" 	k="20" />
<hkern g1="Thorn" 	g2="one" 	k="68" />
<hkern g1="Thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="Thorn" 	g2="question" 	k="68" />
<hkern g1="Thorn" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="Thorn" 	g2="seven" 	k="102" />
<hkern g1="Thorn" 	g2="trademark" 	k="61" />
<hkern g1="Thorn" 	g2="two" 	k="61" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="Thorn" 	g2="AE" 	k="102" />
<hkern g1="Thorn" 	g2="J" 	k="37" />
<hkern g1="Thorn" 	g2="dollar,S" 	k="41" />
<hkern g1="Thorn" 	g2="W" 	k="27" />
<hkern g1="Thorn" 	g2="X" 	k="74" />
<hkern g1="Thorn" 	g2="Z" 	k="72" />
<hkern g1="Thorn" 	g2="five" 	k="20" />
<hkern g1="Thorn" 	g2="underscore" 	k="205" />
<hkern g1="Thorn" 	g2="x" 	k="20" />
<hkern g1="Thorn" 	g2="slash" 	k="82" />
<hkern g1="Thorn" 	g2="three" 	k="82" />
<hkern g1="Thorn" 	g2="M" 	k="27" />
<hkern g1="Thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="Thorn" 	g2="ampersand" 	k="41" />
<hkern g1="Thorn" 	g2="eight" 	k="31" />
<hkern g1="Thorn" 	g2="g" 	k="20" />
<hkern g1="Thorn" 	g2="m,n,p,r,mu,ntilde" 	k="41" />
<hkern g1="Thorn" 	g2="s" 	k="43" />
<hkern g1="Thorn" 	g2="z" 	k="41" />
<hkern g1="Thorn" 	g2="numbersign" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="6" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="dollar,S" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="X" 	k="6" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Z" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="underscore" 	k="61" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-16" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="V" 	g2="asterisk,registered" 	k="-6" />
<hkern g1="V" 	g2="bracketright" 	k="-23" />
<hkern g1="V" 	g2="bullet" 	k="20" />
<hkern g1="V" 	g2="colon,semicolon" 	k="20" />
<hkern g1="V" 	g2="exclam" 	k="-20" />
<hkern g1="V" 	g2="four" 	k="41" />
<hkern g1="V" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="53" />
<hkern g1="V" 	g2="nine" 	k="10" />
<hkern g1="V" 	g2="one" 	k="41" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="V" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="V" 	g2="trademark" 	k="-20" />
<hkern g1="V" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-20" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="27" />
<hkern g1="V" 	g2="AE" 	k="57" />
<hkern g1="V" 	g2="J" 	k="6" />
<hkern g1="V" 	g2="dollar,S" 	k="31" />
<hkern g1="V" 	g2="Z" 	k="41" />
<hkern g1="V" 	g2="five" 	k="20" />
<hkern g1="V" 	g2="underscore" 	k="102" />
<hkern g1="V" 	g2="x" 	k="35" />
<hkern g1="V" 	g2="slash" 	k="63" />
<hkern g1="V" 	g2="v,y,yacute,ydieresis" 	k="23" />
<hkern g1="V" 	g2="M" 	k="14" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="V" 	g2="ampersand" 	k="41" />
<hkern g1="V" 	g2="at" 	k="31" />
<hkern g1="V" 	g2="eight" 	k="20" />
<hkern g1="V" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="V" 	g2="g" 	k="61" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="V" 	g2="j" 	k="14" />
<hkern g1="V" 	g2="m,n,p,r,mu,ntilde" 	k="20" />
<hkern g1="V" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="V" 	g2="s" 	k="41" />
<hkern g1="V" 	g2="six" 	k="10" />
<hkern g1="V" 	g2="t" 	k="20" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="V" 	g2="w" 	k="14" />
<hkern g1="V" 	g2="z" 	k="37" />
<hkern g1="V" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-20" />
<hkern g1="V" 	g2="numbersign" 	k="20" />
<hkern g1="W" 	g2="asterisk,registered" 	k="-6" />
<hkern g1="W" 	g2="bracketright" 	k="-6" />
<hkern g1="W" 	g2="four" 	k="20" />
<hkern g1="W" 	g2="one" 	k="41" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="W" 	g2="trademark" 	k="-20" />
<hkern g1="W" 	g2="J" 	k="6" />
<hkern g1="W" 	g2="dollar,S" 	k="20" />
<hkern g1="W" 	g2="Z" 	k="41" />
<hkern g1="W" 	g2="five" 	k="20" />
<hkern g1="W" 	g2="underscore" 	k="123" />
<hkern g1="W" 	g2="x" 	k="20" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="61" />
<hkern g1="W" 	g2="ampersand" 	k="20" />
<hkern g1="W" 	g2="at" 	k="20" />
<hkern g1="W" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="W" 	g2="g" 	k="20" />
<hkern g1="W" 	g2="m,n,p,r,mu,ntilde" 	k="10" />
<hkern g1="W" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="W" 	g2="s" 	k="20" />
<hkern g1="W" 	g2="t" 	k="10" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="W" 	g2="z" 	k="25" />
<hkern g1="W" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-27" />
<hkern g1="K,X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="27" />
<hkern g1="K,X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="K,X" 	g2="backslash" 	k="41" />
<hkern g1="K,X" 	g2="braceright" 	k="-6" />
<hkern g1="K,X" 	g2="bracketright" 	k="-16" />
<hkern g1="K,X" 	g2="bullet" 	k="47" />
<hkern g1="K,X" 	g2="four" 	k="61" />
<hkern g1="K,X" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="68" />
<hkern g1="K,X" 	g2="nine" 	k="20" />
<hkern g1="K,X" 	g2="one" 	k="78" />
<hkern g1="K,X" 	g2="question" 	k="61" />
<hkern g1="K,X" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="16" />
<hkern g1="K,X" 	g2="J" 	k="6" />
<hkern g1="K,X" 	g2="dollar,S" 	k="20" />
<hkern g1="K,X" 	g2="v,y,yacute,ydieresis" 	k="16" />
<hkern g1="K,X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="K,X" 	g2="ampersand" 	k="20" />
<hkern g1="K,X" 	g2="at" 	k="61" />
<hkern g1="K,X" 	g2="eight" 	k="16" />
<hkern g1="K,X" 	g2="less,equal,greater,multiply" 	k="61" />
<hkern g1="K,X" 	g2="f,uniFB01,uniFB02" 	k="27" />
<hkern g1="K,X" 	g2="g" 	k="31" />
<hkern g1="K,X" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="K,X" 	g2="j" 	k="20" />
<hkern g1="K,X" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="K,X" 	g2="six" 	k="41" />
<hkern g1="K,X" 	g2="t" 	k="16" />
<hkern g1="K,X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="K,X" 	g2="w" 	k="6" />
<hkern g1="K,X" 	g2="zero" 	k="6" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bracketright" 	k="-16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bullet" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="109" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="four" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="109" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="nine" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="one" 	k="57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="180" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="two" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="98" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="125" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="dollar,S" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="five" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="underscore" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="98" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="three" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v,y,yacute,ydieresis" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="M" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="160" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="94" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="eight" 	k="57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,uniFB01,uniFB02" 	k="57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="j" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="l" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,mu,ntilde" 	k="98" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="119" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="109" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="six" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="98" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="109" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="zero" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="numbersign" 	k="102" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="Z" 	g2="T" 	k="10" />
<hkern g1="Z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="Z" 	g2="asterisk,registered" 	k="-20" />
<hkern g1="Z" 	g2="backslash" 	k="82" />
<hkern g1="Z" 	g2="bullet" 	k="82" />
<hkern g1="Z" 	g2="four" 	k="61" />
<hkern g1="Z" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="115" />
<hkern g1="Z" 	g2="nine" 	k="43" />
<hkern g1="Z" 	g2="one" 	k="41" />
<hkern g1="Z" 	g2="two" 	k="20" />
<hkern g1="Z" 	g2="J" 	k="16" />
<hkern g1="Z" 	g2="dollar,S" 	k="31" />
<hkern g1="Z" 	g2="Z" 	k="14" />
<hkern g1="Z" 	g2="five" 	k="31" />
<hkern g1="Z" 	g2="x" 	k="41" />
<hkern g1="Z" 	g2="v,y,yacute,ydieresis" 	k="31" />
<hkern g1="Z" 	g2="M" 	k="20" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="33" />
<hkern g1="Z" 	g2="ampersand" 	k="41" />
<hkern g1="Z" 	g2="at" 	k="41" />
<hkern g1="Z" 	g2="eight" 	k="31" />
<hkern g1="Z" 	g2="less,equal,greater,multiply" 	k="63" />
<hkern g1="Z" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="Z" 	g2="g" 	k="51" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="104" />
<hkern g1="Z" 	g2="guillemotright,guilsinglright" 	k="63" />
<hkern g1="Z" 	g2="j" 	k="33" />
<hkern g1="Z" 	g2="l" 	k="31" />
<hkern g1="Z" 	g2="m,n,p,r,mu,ntilde" 	k="33" />
<hkern g1="Z" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="Z" 	g2="s" 	k="33" />
<hkern g1="Z" 	g2="six" 	k="51" />
<hkern g1="Z" 	g2="t" 	k="41" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="43" />
<hkern g1="Z" 	g2="w" 	k="20" />
<hkern g1="Z" 	g2="z" 	k="31" />
<hkern g1="Z" 	g2="zero" 	k="20" />
<hkern g1="Z" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-6" />
<hkern g1="Z" 	g2="numbersign" 	k="20" />
<hkern g1="five" 	g2="J" 	k="6" />
<hkern g1="five" 	g2="dollar,S" 	k="31" />
<hkern g1="five" 	g2="T" 	k="82" />
<hkern g1="five" 	g2="V" 	k="61" />
<hkern g1="five" 	g2="W" 	k="41" />
<hkern g1="five" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="five" 	g2="Z" 	k="31" />
<hkern g1="five" 	g2="asterisk,registered" 	k="41" />
<hkern g1="five" 	g2="backslash" 	k="61" />
<hkern g1="five" 	g2="degree" 	k="20" />
<hkern g1="five" 	g2="five" 	k="10" />
<hkern g1="five" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-16" />
<hkern g1="five" 	g2="nine" 	k="31" />
<hkern g1="five" 	g2="one" 	k="102" />
<hkern g1="five" 	g2="percent" 	k="41" />
<hkern g1="five" 	g2="question" 	k="41" />
<hkern g1="five" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="61" />
<hkern g1="five" 	g2="s" 	k="20" />
<hkern g1="five" 	g2="seven" 	k="47" />
<hkern g1="five" 	g2="slash" 	k="41" />
<hkern g1="five" 	g2="t" 	k="14" />
<hkern g1="five" 	g2="three" 	k="14" />
<hkern g1="five" 	g2="trademark" 	k="41" />
<hkern g1="five" 	g2="two" 	k="25" />
<hkern g1="five" 	g2="underscore" 	k="41" />
<hkern g1="five" 	g2="v,y,yacute,ydieresis" 	k="16" />
<hkern g1="five" 	g2="w" 	k="4" />
<hkern g1="five" 	g2="x" 	k="16" />
<hkern g1="five" 	g2="yen" 	k="51" />
<hkern g1="five" 	g2="z" 	k="14" />
<hkern g1="five" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="20" />
<hkern g1="j" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-27" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="asterisk,registered" 	k="20" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="backslash" 	k="102" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="five" 	k="20" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="nine" 	k="25" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="one" 	k="102" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="question" 	k="82" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="seven" 	k="82" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="three" 	k="41" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="trademark" 	k="61" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="two" 	k="41" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="underscore" 	k="41" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="v,y,yacute,ydieresis" 	k="6" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="x" 	k="16" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="z" 	k="14" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="10" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="J" 	k="41" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="T" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="V" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="Z" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="asterisk,registered" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-84" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="s" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="seven" 	k="-16" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="slash" 	k="205" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="t" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="underscore" 	k="205" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="yen" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="z" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="205" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="AE" 	k="123" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="at" 	k="41" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="braceright" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="bracketright" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="eight" 	k="31" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="four" 	k="82" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="numbersign" 	k="61" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="questiondown" 	k="205" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="six" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="sterling" 	k="61" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="zero" 	k="20" />
<hkern g1="z" 	g2="backslash" 	k="61" />
<hkern g1="z" 	g2="one" 	k="20" />
<hkern g1="z" 	g2="question" 	k="61" />
<hkern g1="z" 	g2="s" 	k="14" />
<hkern g1="z" 	g2="seven" 	k="20" />
<hkern g1="z" 	g2="trademark" 	k="41" />
<hkern g1="z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="z" 	g2="at" 	k="20" />
<hkern g1="z" 	g2="four" 	k="53" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="z" 	g2="numbersign" 	k="6" />
<hkern g1="z" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="z" 	g2="six" 	k="20" />
<hkern g1="z" 	g2="ampersand" 	k="41" />
<hkern g1="z" 	g2="bullet" 	k="10" />
<hkern g1="z" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="z" 	g2="g" 	k="20" />
<hkern g1="z" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="74" />
<hkern g1="z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="ampersand" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk,registered" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="backslash" 	k="82" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="g" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="23" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="l" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="one" 	k="102" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="61" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="seven" 	k="61" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="trademark" 	k="61" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v,y,yacute,ydieresis" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="j" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="s" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="ampersand" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="backslash" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="bullet" 	k="51" />
<hkern g1="c,cent,ccedilla" 	g2="eight" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="less,equal,greater,multiply" 	k="43" />
<hkern g1="c,cent,ccedilla" 	g2="four" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="g" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="90" />
<hkern g1="c,cent,ccedilla" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="c,cent,ccedilla" 	g2="one" 	k="29" />
<hkern g1="c,cent,ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="c,cent,ccedilla" 	g2="question" 	k="72" />
<hkern g1="c,cent,ccedilla" 	g2="seven" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="trademark" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="zero" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-20" />
<hkern g1="c,cent,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="at" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="c,cent,ccedilla" 	g2="yen" 	k="41" />
<hkern g1="d" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-23" />
<hkern g1="f" 	g2="ampersand" 	k="20" />
<hkern g1="f" 	g2="asterisk,registered" 	k="-102" />
<hkern g1="f" 	g2="backslash" 	k="-16" />
<hkern g1="f" 	g2="copyright" 	k="-61" />
<hkern g1="f" 	g2="eight" 	k="-10" />
<hkern g1="f" 	g2="four" 	k="29" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="f" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="41" />
<hkern g1="f" 	g2="nine" 	k="-41" />
<hkern g1="f" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="f" 	g2="one" 	k="-41" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="f" 	g2="question" 	k="-102" />
<hkern g1="f" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-143" />
<hkern g1="f" 	g2="seven" 	k="-123" />
<hkern g1="f" 	g2="trademark" 	k="-123" />
<hkern g1="f" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-61" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="45" />
<hkern g1="f" 	g2="braceright" 	k="-63" />
<hkern g1="f" 	g2="bracketright" 	k="-63" />
<hkern g1="f" 	g2="slash" 	k="47" />
<hkern g1="f" 	g2="s" 	k="10" />
<hkern g1="f" 	g2="two" 	k="-41" />
<hkern g1="f" 	g2="underscore" 	k="61" />
<hkern g1="f" 	g2="at" 	k="31" />
<hkern g1="f" 	g2="colon,semicolon" 	k="-10" />
<hkern g1="f" 	g2="z" 	k="20" />
<hkern g1="f" 	g2="five" 	k="-41" />
<hkern g1="f" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-123" />
<hkern g1="f" 	g2="exclam" 	k="-94" />
<hkern g1="f" 	g2="parenright" 	k="-164" />
<hkern g1="f" 	g2="bar,brokenbar" 	k="-6" />
<hkern g1="f" 	g2="percent" 	k="-41" />
<hkern g1="f" 	g2="three" 	k="-47" />
<hkern g1="g" 	g2="backslash" 	k="41" />
<hkern g1="g" 	g2="four" 	k="31" />
<hkern g1="g" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="g" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="g" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="g" 	g2="question" 	k="41" />
<hkern g1="g" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="g" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="37" />
<hkern g1="g" 	g2="s" 	k="14" />
<hkern g1="g" 	g2="underscore" 	k="-6" />
<hkern g1="g" 	g2="z" 	k="6" />
<hkern g1="germandbls" 	g2="asterisk,registered" 	k="82" />
<hkern g1="germandbls" 	g2="backslash" 	k="61" />
<hkern g1="germandbls" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="germandbls" 	g2="g" 	k="20" />
<hkern g1="germandbls" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="germandbls" 	g2="nine" 	k="51" />
<hkern g1="germandbls" 	g2="one" 	k="102" />
<hkern g1="germandbls" 	g2="question" 	k="61" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="72" />
<hkern g1="germandbls" 	g2="seven" 	k="61" />
<hkern g1="germandbls" 	g2="t" 	k="20" />
<hkern g1="germandbls" 	g2="trademark" 	k="61" />
<hkern g1="germandbls" 	g2="v,y,yacute,ydieresis" 	k="25" />
<hkern g1="germandbls" 	g2="w" 	k="4" />
<hkern g1="germandbls" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="41" />
<hkern g1="germandbls" 	g2="underscore" 	k="41" />
<hkern g1="germandbls" 	g2="x" 	k="25" />
<hkern g1="i" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-27" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="asterisk,registered" 	k="-61" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="backslash" 	k="-41" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="l" 	k="-16" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="question" 	k="-23" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-49" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="seven" 	k="-43" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="trademark" 	k="-104" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-23" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="braceright" 	k="-90" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="bracketright" 	k="-90" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="j" 	k="-27" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="five" 	k="-16" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="exclam" 	k="-70" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="parenright" 	k="-90" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="b,h,k,germandbls,thorn" 	k="-23" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="i" 	k="-27" />
<hkern g1="l,uniFB02" 	g2="asterisk,registered" 	k="20" />
<hkern g1="l,uniFB02" 	g2="backslash" 	k="41" />
<hkern g1="l,uniFB02" 	g2="bullet" 	k="20" />
<hkern g1="l,uniFB02" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="l,uniFB02" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="l,uniFB02" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="l,uniFB02" 	g2="one" 	k="47" />
<hkern g1="l,uniFB02" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-6" />
<hkern g1="l,uniFB02" 	g2="question" 	k="31" />
<hkern g1="l,uniFB02" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="31" />
<hkern g1="l,uniFB02" 	g2="seven" 	k="27" />
<hkern g1="l,uniFB02" 	g2="trademark" 	k="31" />
<hkern g1="l,uniFB02" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="l,uniFB02" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="31" />
<hkern g1="l,uniFB02" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="l,uniFB02" 	g2="braceright" 	k="-6" />
<hkern g1="l,uniFB02" 	g2="bracketright" 	k="-6" />
<hkern g1="l,uniFB02" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="l,uniFB02" 	g2="z" 	k="-20" />
<hkern g1="l,uniFB02" 	g2="exclam" 	k="-6" />
<hkern g1="h,m,n,ntilde" 	g2="backslash" 	k="74" />
<hkern g1="h,m,n,ntilde" 	g2="one" 	k="61" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="61" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="seven" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="v,y,yacute,ydieresis" 	k="14" />
<hkern g1="h,m,n,ntilde" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="q" 	g2="backslash" 	k="53" />
<hkern g1="q" 	g2="question" 	k="41" />
<hkern g1="q" 	g2="trademark" 	k="20" />
<hkern g1="q" 	g2="j" 	k="-16" />
<hkern g1="r" 	g2="ampersand" 	k="82" />
<hkern g1="r" 	g2="asterisk,registered" 	k="-37" />
<hkern g1="r" 	g2="backslash" 	k="102" />
<hkern g1="r" 	g2="bullet" 	k="10" />
<hkern g1="r" 	g2="eight" 	k="20" />
<hkern g1="r" 	g2="four" 	k="61" />
<hkern g1="r" 	g2="g" 	k="33" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="63" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="23" />
<hkern g1="r" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="70" />
<hkern g1="r" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="r" 	g2="one" 	k="-20" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="r" 	g2="question" 	k="61" />
<hkern g1="r" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-6" />
<hkern g1="r" 	g2="trademark" 	k="41" />
<hkern g1="r" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-41" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="72" />
<hkern g1="r" 	g2="slash" 	k="123" />
<hkern g1="r" 	g2="j" 	k="20" />
<hkern g1="r" 	g2="s" 	k="33" />
<hkern g1="r" 	g2="two" 	k="10" />
<hkern g1="r" 	g2="underscore" 	k="164" />
<hkern g1="r" 	g2="at" 	k="31" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="r" 	g2="z" 	k="20" />
<hkern g1="r" 	g2="five" 	k="20" />
<hkern g1="r" 	g2="three" 	k="41" />
<hkern g1="r" 	g2="numbersign" 	k="41" />
<hkern g1="s" 	g2="backslash" 	k="84" />
<hkern g1="s" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="s" 	g2="nine" 	k="20" />
<hkern g1="s" 	g2="one" 	k="61" />
<hkern g1="s" 	g2="question" 	k="61" />
<hkern g1="s" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="31" />
<hkern g1="s" 	g2="seven" 	k="51" />
<hkern g1="s" 	g2="trademark" 	k="41" />
<hkern g1="s" 	g2="v,y,yacute,ydieresis" 	k="35" />
<hkern g1="s" 	g2="w" 	k="14" />
<hkern g1="s" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="20" />
<hkern g1="s" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="s" 	g2="s" 	k="23" />
<hkern g1="s" 	g2="two" 	k="20" />
<hkern g1="s" 	g2="underscore" 	k="41" />
<hkern g1="s" 	g2="x" 	k="20" />
<hkern g1="s" 	g2="five" 	k="20" />
<hkern g1="t" 	g2="backslash" 	k="82" />
<hkern g1="t" 	g2="bullet" 	k="41" />
<hkern g1="t" 	g2="four" 	k="41" />
<hkern g1="t" 	g2="g" 	k="31" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="t" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="82" />
<hkern g1="t" 	g2="l" 	k="20" />
<hkern g1="t" 	g2="nine" 	k="10" />
<hkern g1="t" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="t" 	g2="one" 	k="41" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-6" />
<hkern g1="t" 	g2="question" 	k="61" />
<hkern g1="t" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="31" />
<hkern g1="t" 	g2="seven" 	k="41" />
<hkern g1="t" 	g2="six" 	k="20" />
<hkern g1="t" 	g2="trademark" 	k="41" />
<hkern g1="t" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="t" 	g2="zero" 	k="20" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="t" 	g2="bracketright" 	k="-20" />
<hkern g1="t" 	g2="at" 	k="20" />
<hkern g1="t" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="t" 	g2="exclam" 	k="-6" />
<hkern g1="t" 	g2="numbersign" 	k="20" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="backslash" 	k="53" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="one" 	k="41" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="question" 	k="41" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="seven" 	k="41" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="trademark" 	k="31" />
<hkern g1="v,y,yacute,ydieresis" 	g2="ampersand" 	k="27" />
<hkern g1="v,y,yacute,ydieresis" 	g2="asterisk,registered" 	k="-16" />
<hkern g1="v,y,yacute,ydieresis" 	g2="backslash" 	k="41" />
<hkern g1="v,y,yacute,ydieresis" 	g2="four" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="g" 	k="4" />
<hkern g1="v,y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="6" />
<hkern g1="v,y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="v,y,yacute,ydieresis" 	g2="question" 	k="61" />
<hkern g1="v,y,yacute,ydieresis" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-16" />
<hkern g1="v,y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="v,y,yacute,ydieresis" 	g2="s" 	k="14" />
<hkern g1="v,y,yacute,ydieresis" 	g2="two" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="underscore" 	k="61" />
<hkern g1="v,y,yacute,ydieresis" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="v,y,yacute,ydieresis" 	g2="exclam" 	k="-16" />
<hkern g1="v,y,yacute,ydieresis" 	g2="three" 	k="27" />
<hkern g1="w" 	g2="asterisk,registered" 	k="-16" />
<hkern g1="w" 	g2="backslash" 	k="41" />
<hkern g1="w" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="w" 	g2="question" 	k="41" />
<hkern g1="w" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-6" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="w" 	g2="underscore" 	k="41" />
<hkern g1="w" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="w" 	g2="exclam" 	k="-6" />
<hkern g1="w" 	g2="three" 	k="20" />
<hkern g1="k,x" 	g2="ampersand" 	k="41" />
<hkern g1="k,x" 	g2="asterisk,registered" 	k="-6" />
<hkern g1="k,x" 	g2="backslash" 	k="41" />
<hkern g1="k,x" 	g2="bullet" 	k="43" />
<hkern g1="k,x" 	g2="less,equal,greater,multiply" 	k="31" />
<hkern g1="k,x" 	g2="four" 	k="31" />
<hkern g1="k,x" 	g2="g" 	k="14" />
<hkern g1="k,x" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="k,x" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="72" />
<hkern g1="k,x" 	g2="nine" 	k="20" />
<hkern g1="k,x" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="k,x" 	g2="one" 	k="33" />
<hkern g1="k,x" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-6" />
<hkern g1="k,x" 	g2="question" 	k="51" />
<hkern g1="k,x" 	g2="six" 	k="20" />
<hkern g1="k,x" 	g2="trademark" 	k="41" />
<hkern g1="k,x" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="k,x" 	g2="zero" 	k="6" />
<hkern g1="k,x" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-6" />
<hkern g1="k,x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="k,x" 	g2="braceright" 	k="-16" />
<hkern g1="k,x" 	g2="bracketright" 	k="-16" />
<hkern g1="k,x" 	g2="s" 	k="10" />
<hkern g1="k,x" 	g2="at" 	k="20" />
<hkern g1="k,x" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="k,x" 	g2="five" 	k="20" />
<hkern g1="k,x" 	g2="exclam" 	k="-6" />
<hkern g1="ampersand" 	g2="J" 	k="10" />
<hkern g1="ampersand" 	g2="T" 	k="162" />
<hkern g1="ampersand" 	g2="V" 	k="78" />
<hkern g1="ampersand" 	g2="W" 	k="27" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="139" />
<hkern g1="ampersand" 	g2="Z" 	k="20" />
<hkern g1="ampersand" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="ampersand" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="ampersand" 	g2="five" 	k="41" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="164" />
<hkern g1="ampersand" 	g2="v,y,yacute,ydieresis" 	k="41" />
<hkern g1="ampersand" 	g2="w" 	k="41" />
<hkern g1="at" 	g2="T" 	k="102" />
<hkern g1="at" 	g2="V" 	k="31" />
<hkern g1="at" 	g2="W" 	k="20" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="94" />
<hkern g1="at" 	g2="Z" 	k="41" />
<hkern g1="at" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="41" />
<hkern g1="at" 	g2="AE" 	k="41" />
<hkern g1="at" 	g2="X" 	k="61" />
<hkern g1="at" 	g2="s" 	k="20" />
<hkern g1="at" 	g2="x" 	k="20" />
<hkern g1="at" 	g2="z" 	k="20" />
<hkern g1="degree" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="164" />
<hkern g1="less,equal,greater,multiply" 	g2="J" 	k="10" />
<hkern g1="less,equal,greater,multiply" 	g2="Z" 	k="53" />
<hkern g1="less,equal,greater,multiply" 	g2="X" 	k="61" />
<hkern g1="less,equal,greater,multiply" 	g2="x" 	k="31" />
<hkern g1="percent" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="123" />
<hkern g1="sterling" 	g2="five" 	k="31" />
<hkern g1="sterling" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="yen" 	g2="five" 	k="20" />
<hkern g1="yen" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="yen" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="yen" 	g2="dollar,S" 	k="41" />
<hkern g1="yen" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="asterisk,registered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="asterisk,registered" 	g2="AE" 	k="102" />
<hkern g1="asterisk,registered" 	g2="T" 	k="-6" />
<hkern g1="asterisk,registered" 	g2="V" 	k="-6" />
<hkern g1="asterisk,registered" 	g2="W" 	k="-6" />
<hkern g1="asterisk,registered" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="asterisk,registered" 	g2="f,uniFB01,uniFB02" 	k="-16" />
<hkern g1="asterisk,registered" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-61" />
<hkern g1="asterisk,registered" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="asterisk,registered" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="asterisk,registered" 	g2="t" 	k="-16" />
<hkern g1="asterisk,registered" 	g2="v,y,yacute,ydieresis" 	k="-16" />
<hkern g1="asterisk,registered" 	g2="w" 	k="-16" />
<hkern g1="asterisk,registered" 	g2="x" 	k="-6" />
<hkern g1="backslash" 	g2="T" 	k="102" />
<hkern g1="backslash" 	g2="V" 	k="63" />
<hkern g1="backslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="backslash" 	g2="f,uniFB01,uniFB02" 	k="61" />
<hkern g1="backslash" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="205" />
<hkern g1="backslash" 	g2="t" 	k="41" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="backslash" 	g2="Z" 	k="41" />
<hkern g1="backslash" 	g2="j" 	k="-41" />
<hkern g1="braceleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-16" />
<hkern g1="braceleft" 	g2="AE" 	k="-16" />
<hkern g1="braceleft" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-90" />
<hkern g1="braceleft" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="braceleft" 	g2="x" 	k="-16" />
<hkern g1="braceleft" 	g2="j" 	k="-49" />
<hkern g1="braceleft" 	g2="Iacute,Icircumflex,Idieresis" 	k="-70" />
<hkern g1="braceleft" 	g2="J" 	k="-16" />
<hkern g1="braceleft" 	g2="X" 	k="-6" />
<hkern g1="bracketleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-16" />
<hkern g1="bracketleft" 	g2="AE" 	k="-23" />
<hkern g1="bracketleft" 	g2="T" 	k="-6" />
<hkern g1="bracketleft" 	g2="V" 	k="-23" />
<hkern g1="bracketleft" 	g2="W" 	k="-6" />
<hkern g1="bracketleft" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-90" />
<hkern g1="bracketleft" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="bracketleft" 	g2="x" 	k="-16" />
<hkern g1="bracketleft" 	g2="Y,Yacute,Ydieresis" 	k="-16" />
<hkern g1="bracketleft" 	g2="j" 	k="-57" />
<hkern g1="bracketleft" 	g2="Iacute,Icircumflex,Idieresis" 	k="-70" />
<hkern g1="bracketleft" 	g2="J" 	k="-16" />
<hkern g1="bracketleft" 	g2="X" 	k="-16" />
<hkern g1="bullet" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="bullet" 	g2="T" 	k="102" />
<hkern g1="bullet" 	g2="V" 	k="20" />
<hkern g1="bullet" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="bullet" 	g2="x" 	k="43" />
<hkern g1="bullet" 	g2="Y,Yacute,Ydieresis" 	k="78" />
<hkern g1="bullet" 	g2="Z" 	k="82" />
<hkern g1="bullet" 	g2="X" 	k="37" />
<hkern g1="bullet" 	g2="dollar,S" 	k="51" />
<hkern g1="bullet" 	g2="five" 	k="20" />
<hkern g1="bullet" 	g2="z" 	k="10" />
<hkern g1="exclam" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-4" />
<hkern g1="exclamdown" 	g2="T" 	k="63" />
<hkern g1="exclamdown" 	g2="V" 	k="41" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="exclamdown" 	g2="j" 	k="-49" />
<hkern g1="exclamdown" 	g2="J" 	k="-16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Z" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="dollar,S" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="five" 	k="41" />
<hkern g1="numbersign" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="numbersign" 	g2="AE" 	k="47" />
<hkern g1="numbersign" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="numbersign" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-57" />
<hkern g1="numbersign" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="numbersign" 	g2="v,y,yacute,ydieresis" 	k="-20" />
<hkern g1="numbersign" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="numbersign" 	g2="Z" 	k="31" />
<hkern g1="numbersign" 	g2="j" 	k="20" />
<hkern g1="numbersign" 	g2="X" 	k="20" />
<hkern g1="numbersign" 	g2="dollar,S" 	k="31" />
<hkern g1="numbersign" 	g2="z" 	k="20" />
<hkern g1="numbersign" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="numbersign" 	g2="g" 	k="20" />
<hkern g1="numbersign" 	g2="m,n,p,r,mu,ntilde" 	k="10" />
<hkern g1="numbersign" 	g2="s" 	k="31" />
<hkern g1="numbersign" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="parenleft" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-90" />
<hkern g1="parenleft" 	g2="j" 	k="-63" />
<hkern g1="parenleft" 	g2="Iacute,Icircumflex,Idieresis" 	k="-82" />
<hkern g1="parenleft" 	g2="J" 	k="-41" />
<hkern g1="question" 	g2="v,y,yacute,ydieresis" 	k="-16" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="questiondown" 	g2="AE" 	k="41" />
<hkern g1="questiondown" 	g2="T" 	k="164" />
<hkern g1="questiondown" 	g2="V" 	k="82" />
<hkern g1="questiondown" 	g2="W" 	k="61" />
<hkern g1="questiondown" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="47" />
<hkern g1="questiondown" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="questiondown" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="questiondown" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="102" />
<hkern g1="questiondown" 	g2="t" 	k="61" />
<hkern g1="questiondown" 	g2="v,y,yacute,ydieresis" 	k="82" />
<hkern g1="questiondown" 	g2="w" 	k="41" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="180" />
<hkern g1="questiondown" 	g2="Z" 	k="31" />
<hkern g1="questiondown" 	g2="j" 	k="-23" />
<hkern g1="questiondown" 	g2="X" 	k="41" />
<hkern g1="questiondown" 	g2="dollar,S" 	k="61" />
<hkern g1="questiondown" 	g2="five" 	k="41" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="questiondown" 	g2="m,n,p,r,mu,ntilde" 	k="61" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="63" />
<hkern g1="slash" 	g2="AE" 	k="82" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="102" />
<hkern g1="slash" 	g2="f,uniFB01,uniFB02" 	k="61" />
<hkern g1="slash" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="slash" 	g2="t" 	k="61" />
<hkern g1="slash" 	g2="v,y,yacute,ydieresis" 	k="41" />
<hkern g1="slash" 	g2="w" 	k="41" />
<hkern g1="slash" 	g2="x" 	k="61" />
<hkern g1="slash" 	g2="Z" 	k="41" />
<hkern g1="slash" 	g2="Iacute,Icircumflex,Idieresis" 	k="-16" />
<hkern g1="slash" 	g2="X" 	k="41" />
<hkern g1="slash" 	g2="five" 	k="41" />
<hkern g1="slash" 	g2="z" 	k="61" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="slash" 	g2="g" 	k="61" />
<hkern g1="slash" 	g2="m,n,p,r,mu,ntilde" 	k="61" />
<hkern g1="slash" 	g2="s" 	k="61" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="61" />
<hkern g1="underscore" 	g2="T" 	k="102" />
<hkern g1="underscore" 	g2="V" 	k="102" />
<hkern g1="underscore" 	g2="W" 	k="123" />
<hkern g1="underscore" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="underscore" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="underscore" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="underscore" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="205" />
<hkern g1="underscore" 	g2="t" 	k="41" />
<hkern g1="underscore" 	g2="v,y,yacute,ydieresis" 	k="61" />
<hkern g1="underscore" 	g2="w" 	k="41" />
<hkern g1="underscore" 	g2="Y,Yacute,Ydieresis" 	k="164" />
<hkern g1="underscore" 	g2="j" 	k="-63" />
<hkern g1="underscore" 	g2="J" 	k="-47" />
<hkern g1="underscore" 	g2="dollar,S" 	k="41" />
<hkern g1="underscore" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="82" />
<hkern g1="underscore" 	g2="g" 	k="-16" />
<hkern g1="underscore" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="underscore" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="61" />
<hkern g1="underscore" 	g2="l" 	k="20" />
<hkern g1="eight" 	g2="J" 	k="20" />
<hkern g1="eight" 	g2="dollar,S" 	k="20" />
<hkern g1="eight" 	g2="T" 	k="41" />
<hkern g1="eight" 	g2="V" 	k="20" />
<hkern g1="eight" 	g2="X" 	k="16" />
<hkern g1="eight" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="eight" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="31" />
<hkern g1="eight" 	g2="v,y,yacute,ydieresis" 	k="14" />
<hkern g1="four" 	g2="J" 	k="6" />
<hkern g1="four" 	g2="dollar,S" 	k="41" />
<hkern g1="four" 	g2="T" 	k="102" />
<hkern g1="four" 	g2="V" 	k="41" />
<hkern g1="four" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="four" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="102" />
<hkern g1="four" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="four" 	g2="W" 	k="20" />
<hkern g1="four" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="four" 	g2="five" 	k="20" />
<hkern g1="four" 	g2="x" 	k="20" />
<hkern g1="nine" 	g2="J" 	k="20" />
<hkern g1="nine" 	g2="dollar,S" 	k="20" />
<hkern g1="nine" 	g2="T" 	k="41" />
<hkern g1="nine" 	g2="V" 	k="10" />
<hkern g1="nine" 	g2="X" 	k="6" />
<hkern g1="nine" 	g2="Y,Yacute,Ydieresis" 	k="68" />
<hkern g1="nine" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="nine" 	g2="AE" 	k="41" />
<hkern g1="nine" 	g2="Z" 	k="41" />
<hkern g1="nine" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="one" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-41" />
<hkern g1="seven" 	g2="J" 	k="41" />
<hkern g1="seven" 	g2="dollar,S" 	k="41" />
<hkern g1="seven" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-16" />
<hkern g1="seven" 	g2="five" 	k="33" />
<hkern g1="seven" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="seven" 	g2="AE" 	k="102" />
<hkern g1="seven" 	g2="Z" 	k="31" />
<hkern g1="seven" 	g2="s" 	k="51" />
<hkern g1="seven" 	g2="z" 	k="41" />
<hkern g1="seven" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="61" />
<hkern g1="seven" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-43" />
<hkern g1="seven" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="seven" 	g2="g" 	k="51" />
<hkern g1="seven" 	g2="m,n,p,r,mu,ntilde" 	k="20" />
<hkern g1="seven" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="seven" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="six" 	g2="J" 	k="20" />
<hkern g1="six" 	g2="T" 	k="102" />
<hkern g1="six" 	g2="V" 	k="41" />
<hkern g1="six" 	g2="X" 	k="20" />
<hkern g1="six" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="six" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="41" />
<hkern g1="six" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="six" 	g2="five" 	k="10" />
<hkern g1="six" 	g2="x" 	k="20" />
<hkern g1="three" 	g2="T" 	k="53" />
<hkern g1="three" 	g2="Y,Yacute,Ydieresis" 	k="53" />
<hkern g1="two" 	g2="J" 	k="31" />
<hkern g1="two" 	g2="T" 	k="41" />
<hkern g1="two" 	g2="V" 	k="20" />
<hkern g1="two" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="two" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="two" 	g2="W" 	k="10" />
<hkern g1="two" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="two" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="two" 	g2="g" 	k="41" />
<hkern g1="two" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="zero" 	g2="T" 	k="61" />
<hkern g1="zero" 	g2="X" 	k="6" />
<hkern g1="zero" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="zero" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="zero" 	g2="x" 	k="6" />
<hkern g1="zero" 	g2="Z" 	k="20" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="T" 	k="123" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="V" 	k="61" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="Y,Yacute,Ydieresis" 	k="139" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="102" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="five" 	k="-6" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="V" 	k="-20" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="X" 	k="16" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="v,y,yacute,ydieresis" 	k="-16" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="x" 	k="-6" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="35" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="AE" 	k="82" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="53" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-23" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="t" 	k="-6" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="w" 	k="-6" />
<hkern g1="at" 	g2="colon,semicolon" 	k="31" />
<hkern g1="at" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="copyright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="degree" 	g2="colon,semicolon" 	k="227" />
<hkern g1="degree" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="184" />
<hkern g1="degree" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="410" />
<hkern g1="Euro" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="82" />
<hkern g1="sterling" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="61" />
<hkern g1="trademark" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="yen" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="41" />
<hkern g1="yen" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="asterisk,registered" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="205" />
<hkern g1="backslash" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="123" />
<hkern g1="bracketleft" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="AE" 	k="-41" />
<hkern g1="colon,semicolon" 	g2="T" 	k="82" />
<hkern g1="colon,semicolon" 	g2="V" 	k="20" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="109" />
<hkern g1="colon,semicolon" 	g2="Z" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="backslash" 	k="102" />
<hkern g1="colon,semicolon" 	g2="bracketright" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="Euro" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="f,uniFB01,uniFB02" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="five" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="four" 	k="10" />
<hkern g1="colon,semicolon" 	g2="one" 	k="84" />
<hkern g1="colon,semicolon" 	g2="question" 	k="57" />
<hkern g1="colon,semicolon" 	g2="s" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="51" />
<hkern g1="colon,semicolon" 	g2="t" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="three" 	k="-16" />
<hkern g1="colon,semicolon" 	g2="v,y,yacute,ydieresis" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="w" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="x" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="z" 	k="-6" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="102" />
<hkern g1="guillemotright,guilsinglright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="AE" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="T" 	k="102" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="V" 	k="53" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="109" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="Z" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="backslash" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="f,uniFB01,uniFB02" 	k="31" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="five" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="four" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="one" 	k="143" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="question" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="seven" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="t" 	k="10" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="three" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="x" 	k="72" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="z" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="J" 	k="16" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="dollar,S" 	k="84" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="X" 	k="68" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="ampersand" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="guillemotright,guilsinglright" 	k="102" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="j" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="nine" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="percent" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="questiondown" 	k="164" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="slash" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="trademark" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="two" 	k="129" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="underscore" 	k="164" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="yen" 	k="41" />
<hkern g1="numbersign" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="numbersign" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="102" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-6" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="AE" 	k="-16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="123" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="102" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="180" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Z" 	k="-6" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="backslash" 	k="287" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="bracketright" 	k="-6" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Euro" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="f,uniFB01,uniFB02" 	k="27" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="five" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="four" 	k="72" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="one" 	k="205" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="question" 	k="102" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="s" 	k="-6" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="t" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="three" 	k="-16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v,y,yacute,ydieresis" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="x" 	k="-6" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="z" 	k="-6" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="ampersand" 	k="33" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="nine" 	k="53" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="percent" 	k="205" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="trademark" 	k="164" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="yen" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk,registered" 	k="205" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="copyright" 	k="164" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="g" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="205" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="six" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="zero" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="102" />
<hkern g1="question" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="205" />
<hkern g1="question" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="104" />
<hkern g1="questiondown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="123" />
<hkern g1="questiondown" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="123" />
<hkern g1="questiondown" 	g2="colon,semicolon" 	k="61" />
<hkern g1="slash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="287" />
<hkern g1="slash" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="123" />
<hkern g1="slash" 	g2="colon,semicolon" 	k="102" />
<hkern g1="underscore" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="164" />
<hkern g1="four" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="nine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="115" />
<hkern g1="seven" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="82" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="51" />
<hkern g1="seven" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="186" />
<hkern g1="six" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="two" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="57" />
<hkern g1="zero" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="25" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
</font>
</defs></svg> 