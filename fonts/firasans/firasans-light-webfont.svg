<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="fira_sanslight" horiz-adv-x="1097" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="571" />
<glyph unicode="&#xfb01;" horiz-adv-x="1153" d="M43 975v96h176v174q0 132 80.5 204.5t232.5 72.5q160 0 302 -76l-43 -86q-129 61 -254 61q-104 0 -151.5 -41.5t-47.5 -140.5v-168h598v-1071h-119v975h-479v-975h-119v975h-176z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1193" d="M43 975v96h176v176q0 128 83 201.5t237 73.5q134 0 280 -47l119 16v-1307q0 -53 20.5 -78.5t67.5 -25.5q40 0 72 14l32 -88q-67 -29 -126 -29q-88 0 -136.5 51.5t-48.5 155.5v1194q-142 45 -272 45q-108 0 -158.5 -42.5t-50.5 -137.5v-172h283l-15 -96h-268v-975h-119 v975h-176z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode="&#xd;" horiz-adv-x="571" />
<glyph unicode=" "  horiz-adv-x="571" />
<glyph unicode="&#x09;" horiz-adv-x="571" />
<glyph unicode="&#xa0;" horiz-adv-x="571" />
<glyph unicode="!" horiz-adv-x="491" d="M147 76q0 40 29 68t70 28q42 0 70 -28t28 -68q0 -42 -28 -70.5t-70 -28.5q-41 0 -70 29t-29 70zM180 1403h127l-8 -957h-109z" />
<glyph unicode="&#x22;" horiz-adv-x="741" d="M145 1403h127l-18 -514h-90zM469 1403h127l-18 -514h-91z" />
<glyph unicode="#" horiz-adv-x="1030" d="M31 369v94h159l62 463h-146v96h158l45 340h107l-45 -340h374l46 340h106l-45 -340h147v-96h-159l-62 -463h146v-94h-158l-49 -369h-107l49 369h-374l-49 -369h-107l49 369h-147zM297 463h375l61 463h-375z" />
<glyph unicode="$" horiz-adv-x="1077" d="M80 147l72 76q153 -143 360 -143q148 0 240 77t92 212q0 121 -69 184t-255 121q-208 66 -297.5 149t-89.5 224q0 137 97.5 229t256.5 104v297h105v-297q104 -9 181 -45.5t151 -103.5l-72 -76q-71 67 -146 96t-171 29q-125 0 -202 -61.5t-77 -167.5q0 -104 66 -162 t249 -113q101 -32 167 -63t121.5 -77t81.5 -110.5t26 -152.5q0 -158 -101.5 -263.5t-273.5 -127.5v-291h-105v286q-239 8 -407 170z" />
<glyph unicode="%" horiz-adv-x="1624" d="M109 1051q0 152 84 242.5t223 90.5q136 0 220.5 -90t84.5 -241q0 -152 -83.5 -243t-221.5 -91q-136 0 -221.5 90.5t-85.5 241.5zM219 1051q0 -106 48 -176t149 -70q52 0 91 20t61 55.5t32 78t10 94.5q0 106 -46.5 175.5t-147.5 69.5q-102 0 -149.5 -69.5t-47.5 -177.5z M301 8l948 1432l88 -56l-950 -1433zM903 309q0 153 83 243.5t222 90.5q137 0 222.5 -90t85.5 -240q0 -153 -84 -244.5t-224 -91.5q-135 0 -220 90.5t-85 241.5zM1014 309q0 -106 47 -176t147 -70q103 0 150 70t47 180q0 67 -17.5 119.5t-63.5 88.5t-116 36 q-100 0 -147 -69.5t-47 -178.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1478" d="M184 350q0 131 74.5 227t220.5 181q-112 107 -159.5 181.5t-47.5 174.5q0 135 94.5 223t249.5 88q150 0 245 -85t95 -218q0 -117 -77 -202.5t-236 -174.5l420 -393q90 185 135 389l113 -32q-65 -247 -174 -428l254 -234l-88 -70l-230 216q-88 -104 -198 -160t-261 -56 q-186 0 -308 103.5t-122 269.5zM307 354q0 -125 89.5 -201.5t228.5 -76.5q122 0 210.5 46.5t163.5 137.5l-448 432q-124 -74 -184 -154.5t-60 -183.5zM391 1116q0 -77 37.5 -139.5t144.5 -165.5q138 75 202.5 146.5t64.5 160.5q0 100 -63 157.5t-161 57.5q-103 0 -164 -62.5 t-61 -154.5z" />
<glyph unicode="'" horiz-adv-x="417" d="M145 1403h127l-18 -514h-90z" />
<glyph unicode="(" horiz-adv-x="663" d="M111 717q0 100 11 192t26 164.5t45 149.5t55 130.5t70 125.5t75 115.5t84 119.5l76 -49q-64 -96 -104 -161t-86 -159.5t-71 -180.5t-42 -201.5t-17 -245.5q0 -160 22.5 -293t71.5 -250.5t97 -201t129 -203.5l-76 -50q-54 75 -84 119t-74.5 116t-70 125.5t-55.5 131 t-45.5 150t-26 164.5t-10.5 192z" />
<glyph unicode=")" horiz-adv-x="663" d="M111 -231q64 95 104 161t85.5 160t70.5 180.5t42 201.5t17 245t-17 245.5t-42 202t-70.5 180t-85.5 159.5t-104 161l75 49q54 -76 84 -119t75 -116t70 -125.5t55.5 -130.5t45.5 -150t26 -164t11 -192t-11 -192t-26 -164.5t-45.5 -150t-55.5 -130.5t-70 -125.5t-75 -116 t-84 -119.5z" />
<glyph unicode="*" horiz-adv-x="892" d="M70 1192l34 106l302 -116l-19 325h117l-17 -325l299 116l37 -106l-311 -82l203 -250l-94 -69l-175 270l-174 -270l-94 67l203 250z" />
<glyph unicode="+" horiz-adv-x="1009" d="M141 627v102h308v311h112v-311h307v-102h-307v-312h-112v312h-308z" />
<glyph unicode="," horiz-adv-x="423" d="M78 -309l82 301q-47 26 -47 84q0 40 28.5 68t69.5 28q43 0 70.5 -27.5t27.5 -68.5q0 -45 -30 -113l-115 -272h-86z" />
<glyph unicode="-" horiz-adv-x="821" d="M139 590v102h543v-102h-543z" />
<glyph unicode="." horiz-adv-x="423" d="M113 76q0 40 28.5 68t69.5 28q43 0 70.5 -27.5t27.5 -68.5q0 -42 -27.5 -70.5t-70.5 -28.5q-41 0 -69.5 29t-28.5 70z" />
<glyph unicode="/" horiz-adv-x="1064" d="M244 -178l475 1823l102 -27l-475 -1823z" />
<glyph unicode="0" horiz-adv-x="1126" d="M135 682q0 346 108.5 524t319.5 178t319.5 -178.5t108.5 -525.5q0 -345 -109 -524t-319 -179q-211 0 -319.5 179.5t-108.5 525.5zM258 682q0 -606 305 -606q150 0 226.5 147t76.5 457q0 606 -303 606q-305 0 -305 -604z" />
<glyph unicode="1" horiz-adv-x="845" d="M88 1110l408 252h102v-1362h-119v1231l-336 -209z" />
<glyph unicode="2" horiz-adv-x="980" d="M68 1190q83 99 177 146.5t216 47.5q172 0 273.5 -100.5t101.5 -257.5q0 -51 -6.5 -96.5t-25.5 -95t-39.5 -91t-63 -100.5t-80 -106.5t-107.5 -124.5t-128.5 -140t-158.5 -168h645l-16 -104h-768v100q123 131 195.5 209.5t148.5 165t115.5 140t77.5 111t53.5 102.5t25 92 t9.5 100q0 121 -70.5 192.5t-187.5 71.5q-95 0 -167 -38.5t-138 -117.5z" />
<glyph unicode="3" horiz-adv-x="995" d="M41 154l76 67q70 -76 145 -109.5t166 -33.5q148 0 231.5 83.5t83.5 221.5q0 287 -307 287h-90l16 96h66q55 0 104.5 17t89.5 49t63.5 85.5t23.5 120.5q0 113 -72 179.5t-191 66.5q-87 0 -158.5 -30t-144.5 -97l-63 74q159 153 373 153q173 0 273.5 -96.5t100.5 -243.5 q0 -133 -74 -215.5t-194 -103.5q134 -6 220.5 -95.5t86.5 -244.5q0 -180 -119 -294t-315 -114q-237 0 -391 177z" />
<glyph unicode="4" horiz-adv-x="1060" d="M92 358v88l416 938l100 -41l-389 -886h469l14 399h103v-399h184v-99h-184v-358h-117v358h-596z" />
<glyph unicode="5" horiz-adv-x="997" d="M70 141l73 72q66 -69 140 -102t172 -33q145 0 229 97.5t84 264.5q0 164 -77 245t-208 81q-65 0 -115 -15.5t-112 -50.5h-94v662h686l-19 -98h-553v-465q112 63 234 63q176 0 279.5 -113.5t103.5 -310.5q0 -204 -119.5 -332.5t-318.5 -128.5q-227 0 -385 164z" />
<glyph unicode="6" horiz-adv-x="1073" d="M135 614q0 357 132 563.5t360 206.5q138 0 254 -79l-52 -82q-97 63 -204 63q-111 0 -194 -77.5t-127 -212t-48 -310.5q138 201 350 201q157 0 260 -110t103 -318q0 -220 -113.5 -351t-288.5 -131q-115 0 -200.5 48.5t-135 136.5t-73 200.5t-23.5 251.5zM256 563 q6 -237 80 -362t231 -125q134 0 207.5 104t73.5 275q0 170 -70 251.5t-188 81.5q-102 0 -187.5 -60t-146.5 -165z" />
<glyph unicode="7" horiz-adv-x="894" d="M61 1262v100h760v-92l-536 -1286l-111 36l524 1242h-637z" />
<glyph unicode="8" horiz-adv-x="1101" d="M109 352q0 263 292 379q-122 55 -177.5 128t-55.5 185q0 79 32 145t86 107.5t122 64.5t143 23t143 -21.5t122.5 -62t87 -106.5t32.5 -147q0 -107 -59.5 -183.5t-186.5 -142.5q157 -63 230 -148.5t73 -218.5q0 -169 -124 -273t-322 -104t-318 104t-120 271zM233 352 q0 -131 86 -202.5t230 -71.5q145 0 232 73t87 203q0 120 -67 185.5t-236 128.5l-69 26q-136 -54 -199.5 -136.5t-63.5 -205.5zM287 1042q0 -98 56 -155t196 -109l63 -22q114 61 164.5 126.5t50.5 159.5q0 117 -72.5 182.5t-193.5 65.5q-117 0 -190.5 -65.5t-73.5 -182.5z " />
<glyph unicode="9" horiz-adv-x="1060" d="M109 938q0 204 117 325t296 121q207 0 310.5 -144t103.5 -404q0 -160 -28.5 -285t-81.5 -216.5t-139.5 -162t-189.5 -119.5t-245 -92l-31 92q138 43 240 97t181 130t122.5 180.5t50.5 237.5q-130 -190 -336 -190q-161 0 -265.5 117t-104.5 313zM231 936q0 -158 72.5 -244 t196.5 -86q97 0 172.5 52t142.5 153q2 244 -67.5 359.5t-221.5 115.5q-138 0 -216.5 -91.5t-78.5 -258.5z" />
<glyph unicode=":" horiz-adv-x="423" d="M113 76q0 40 28.5 68t69.5 28q43 0 70.5 -27.5t27.5 -68.5q0 -42 -27.5 -70.5t-70.5 -28.5q-41 0 -69.5 29t-28.5 70zM113 874q0 40 28.5 68.5t69.5 28.5q43 0 70.5 -28t27.5 -69q0 -42 -27 -70t-71 -28q-41 0 -69.5 28.5t-28.5 69.5z" />
<glyph unicode=";" horiz-adv-x="423" d="M78 -309l82 301q-47 26 -47 84q0 40 28.5 68t69.5 28q43 0 70.5 -27.5t27.5 -68.5q0 -45 -30 -113l-115 -272h-86zM113 874q0 40 28.5 68.5t69.5 28.5q43 0 70.5 -28t27.5 -69q0 -42 -27 -70t-71 -28q-41 0 -69.5 28.5t-28.5 69.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="1017" d="M131 627v110l713 346l43 -100l-651 -301l651 -307l-43 -99z" />
<glyph unicode="=" horiz-adv-x="1009" d="M141 446v105h727v-105h-727zM141 805v104h727v-104h-727z" />
<glyph unicode="&#x3e;" horiz-adv-x="1017" d="M131 375l649 307l-649 301l43 100l713 -346v-110l-713 -351z" />
<glyph unicode="?" horiz-adv-x="915" d="M84 1237q84 96 179.5 142t215.5 46q164 0 259.5 -86.5t95.5 -212.5q0 -63 -20.5 -114t-54 -84.5t-73.5 -62.5t-79.5 -58t-73 -61t-54 -79.5t-20.5 -107.5v-113h-117v121q0 70 20 125.5t52.5 91.5t71.5 66t78.5 56.5t72 54.5t52.5 68.5t20 90.5q0 93 -64 148t-172 55 q-91 0 -164 -36.5t-143 -115.5zM307 76q0 40 28.5 68t70.5 28t70 -28t28 -68q0 -42 -28 -70.5t-70 -28.5t-70.5 28.5t-28.5 70.5z" />
<glyph unicode="@" horiz-adv-x="2088" d="M174 489q0 196 65.5 368t182.5 297.5t286.5 198t368.5 72.5q201 0 363 -64.5t264.5 -175t156.5 -252.5t54 -302q0 -295 -102 -450.5t-269 -155.5q-103 0 -166.5 63.5t-83.5 161.5q-94 -221 -303 -221q-152 0 -248 118.5t-96 327.5q0 128 33 228t91.5 162t135.5 94t168 32 q154 0 297 -73v-529q0 -268 170 -268q55 0 98.5 26t79 83t55 158t19.5 239q0 150 -47.5 276.5t-136 220t-225.5 146.5t-308 53q-233 0 -412 -110t-274.5 -299t-95.5 -425q0 -243 95 -426t272 -282.5t413 -99.5q200 0 387 69l33 -96q-106 -37 -200.5 -55.5t-221.5 -18.5 q-196 0 -361.5 63.5t-284.5 179.5t-186 287t-67 379zM766 475q0 -171 64 -261.5t174 -90.5q51 0 95.5 19.5t75.5 52.5t52.5 69t34.5 76v518q-84 41 -183 41q-150 0 -231.5 -110t-81.5 -314z" />
<glyph unicode="A" horiz-adv-x="1157" d="M33 0l469 1403h153l467 -1403h-129l-123 383h-589l-123 -383h-125zM315 487h523l-260 816z" />
<glyph unicode="B" horiz-adv-x="1224" d="M227 0v1403h314q244 0 376 -87.5t132 -262.5q0 -130 -71 -209.5t-187 -100.5q67 -7 122.5 -29.5t102 -63t72.5 -107.5t26 -154q0 -195 -133 -292t-371 -97h-383zM350 102h260q377 0 377 287q0 78 -26 136.5t-73 93.5t-107.5 52t-133.5 17h-297v-586zM350 786h283 q130 0 210.5 68.5t80.5 189.5q0 136 -93.5 196t-277.5 60h-203v-514z" />
<glyph unicode="C" horiz-adv-x="1126" d="M135 702q0 175 44.5 314.5t122.5 227.5t180 134.5t220 46.5t199 -31.5t162 -97.5l-66 -82q-73 57 -137.5 82t-148.5 25q-95 0 -173.5 -35t-141 -106.5t-97.5 -193.5t-35 -284q0 -155 34 -275t94.5 -193.5t139.5 -110.5t173 -37q100 0 169.5 29.5t141.5 87.5l63 -80 q-153 -146 -379 -146q-122 0 -225 47.5t-179 137t-118.5 228t-42.5 312.5z" />
<glyph unicode="D" horiz-adv-x="1294" d="M227 0v1403h279q75 0 139.5 -7.5t132.5 -28t122.5 -53.5t104 -87t82.5 -124.5t52.5 -169.5t19.5 -220t-20 -222.5t-54 -174t-83.5 -128.5t-103.5 -91t-118.5 -57.5t-125 -31t-126.5 -8.5h-301zM350 102h187q493 0 493 611q0 107 -15.5 194t-41.5 147.5t-65.5 105.5 t-81.5 71t-96 42t-102 21.5t-106 5.5h-172v-1198z" />
<glyph unicode="E" horiz-adv-x="1048" d="M227 0v1403h713l-14 -103h-576v-522h498v-102h-498v-574h604v-102h-727z" />
<glyph unicode="F" horiz-adv-x="985" d="M227 0v1403h701l-15 -103h-563v-546h486v-103h-486v-651h-123z" />
<glyph unicode="G" horiz-adv-x="1265" d="M135 707q0 175 49 315t131.5 226t186.5 131.5t219 45.5q118 0 206.5 -33.5t178.5 -105.5l-74 -78q-75 60 -147 86.5t-164 26.5q-70 0 -134 -20.5t-123.5 -67.5t-103.5 -116.5t-70 -175t-26 -234.5q0 -314 118.5 -469.5t332.5 -155.5q162 0 295 74v497h-275l-14 103h409 v-664q-200 -115 -415 -115q-268 0 -424 187.5t-156 542.5z" />
<glyph unicode="H" horiz-adv-x="1372" d="M227 0v1403h123v-612h672v612h123v-1403h-123v686h-672v-686h-123z" />
<glyph unicode="I" horiz-adv-x="577" d="M227 0v1403h123v-1403h-123z" />
<glyph unicode="J" horiz-adv-x="595" d="M27 -154q137 61 183 132.5t46 216.5v1208h123v-1215q0 -182 -71 -273t-249 -159z" />
<glyph unicode="K" horiz-adv-x="1120" d="M227 0v1403h123v-1403h-123zM358 752l586 651h141l-581 -645l616 -758h-149z" />
<glyph unicode="L" horiz-adv-x="976" d="M227 0v1403h123v-1292h586l-16 -111h-693z" />
<glyph unicode="M" horiz-adv-x="1576" d="M141 0l133 1403h170l349 -1092l335 1092h172l136 -1403h-119l-64 672q-38 372 -53 610l-352 -1116h-117l-364 1116q-3 -67 -48 -606l-61 -676h-117z" />
<glyph unicode="N" horiz-adv-x="1378" d="M227 0v1403h158l666 -1268q-17 251 -17 416v852h117v-1403h-162l-661 1268q16 -256 16 -484v-784h-117z" />
<glyph unicode="O" horiz-adv-x="1415" d="M135 698q0 341 157.5 534t414.5 193q262 0 417.5 -188.5t155.5 -536.5q0 -342 -154.5 -532.5t-418.5 -190.5q-260 0 -416 189t-156 532zM264 698q0 -305 120 -460.5t323 -155.5q211 0 327.5 154.5t116.5 463.5q0 311 -117.5 466t-326.5 155q-205 0 -324 -156.5 t-119 -466.5z" />
<glyph unicode="P" horiz-adv-x="1167" d="M227 0v1403h330q251 0 384.5 -103.5t133.5 -316.5q0 -115 -38.5 -200.5t-109.5 -136t-164.5 -75t-211.5 -24.5h-201v-547h-123zM350 647h201q92 0 161 16.5t123.5 53.5t82.5 103.5t28 160.5q0 172 -99 247t-292 75h-205v-656z" />
<glyph unicode="Q" horiz-adv-x="1413" d="M135 698q0 341 157.5 534t414.5 193q262 0 417.5 -188.5t155.5 -536.5q0 -521 -354 -651q240 -3 403 -135l-82 -104q-112 99 -230 135.5t-310 36.5q-260 0 -416 187t-156 529zM264 698q0 -305 120 -460.5t323 -155.5q211 0 327.5 154.5t116.5 463.5q0 311 -117.5 466 t-326.5 155q-205 0 -324 -156.5t-119 -466.5z" />
<glyph unicode="R" horiz-adv-x="1208" d="M227 0v1403h340q500 0 500 -381q0 -152 -79.5 -241.5t-233.5 -129.5l385 -651h-146l-362 629h-281v-629h-123zM350 729h234q352 0 352 293q0 143 -88 212t-285 69h-213v-574z" />
<glyph unicode="S" horiz-adv-x="1103" d="M80 154l70 77q85 -74 172.5 -111.5t203.5 -37.5q151 0 246.5 79.5t95.5 217.5q0 125 -71 191t-262 126q-216 66 -308 151.5t-92 233.5q0 150 114.5 247t297.5 97q130 0 222 -38t181 -117l-71 -78q-79 68 -155.5 98.5t-172.5 30.5q-130 0 -210.5 -64.5t-80.5 -173.5 q0 -107 69.5 -167t258.5 -117q103 -32 171 -64t124.5 -79.5t83 -114.5t26.5 -158q0 -181 -127 -293.5t-338 -112.5q-261 0 -448 177z" />
<glyph unicode="T" horiz-adv-x="1013" d="M31 1294v109h962l-14 -109h-412v-1294h-121v1294h-415z" />
<glyph unicode="U" horiz-adv-x="1335" d="M207 438v965h123v-954q0 -176 83 -270.5t255 -94.5t254 95t82 270v954h124v-965q0 -210 -118 -335.5t-342 -125.5q-226 0 -343.5 125.5t-117.5 335.5z" />
<glyph unicode="V" horiz-adv-x="1118" d="M33 1403h133l395 -1276l397 1276h127l-456 -1403h-137z" />
<glyph unicode="W" horiz-adv-x="1658" d="M61 1403h125l267 -1288l307 1288h137l313 -1288l269 1288h116l-307 -1403h-153l-308 1266l-307 -1266h-149z" />
<glyph unicode="X" horiz-adv-x="1052" d="M20 0l429 754l-386 649h140l319 -567l320 567h133l-381 -643l438 -760h-139l-373 674l-366 -674h-134z" />
<glyph unicode="Y" horiz-adv-x="1071" d="M27 1403h137l377 -746l370 746h131l-444 -854v-549h-123v547z" />
<glyph unicode="Z" horiz-adv-x="1056" d="M72 0v102l751 1194h-673v107h808v-100l-745 -1194h733l-16 -109h-858z" />
<glyph unicode="[" horiz-adv-x="649" d="M172 -229v1882h342v-101h-229v-1683h229v-98h-342z" />
<glyph unicode="\" horiz-adv-x="1064" d="M244 1620l102 25l475 -1823l-104 -27z" />
<glyph unicode="]" horiz-adv-x="649" d="M135 -131h230v1683h-230v101h342v-1882h-342v98z" />
<glyph unicode="^" horiz-adv-x="1087" d="M115 1079l379 625h100l377 -625h-133l-295 508l-293 -508h-135z" />
<glyph unicode="_" horiz-adv-x="1064" d="M59 -145h949v-105h-949v105z" />
<glyph unicode="`" horiz-adv-x="561" d="M61 1495l54 107l385 -211l-37 -70z" />
<glyph unicode="a" d="M115 297q0 167 118 256.5t326 89.5h205v105q0 129 -58 187t-190 58q-125 0 -291 -61l-30 90q182 72 337 72q351 0 351 -340v-516q0 -84 21.5 -121t68.5 -56l-27 -84q-73 14 -113.5 56t-52.5 125q-112 -181 -334 -181q-153 0 -242 88t-89 232zM242 303q0 -110 59.5 -169.5 t165.5 -59.5q191 0 297 188v295h-186q-163 0 -249.5 -64t-86.5 -190z" />
<glyph unicode="b" horiz-adv-x="1189" d="M217 0v1507l119 15v-600q128 172 321 172q186 0 289 -144t103 -409q0 -260 -108 -412t-300 -152q-96 0 -174.5 43t-132.5 115l-12 -135h-105zM336 248q114 -172 282 -172q145 0 224.5 117.5t79.5 347.5q0 454 -289 454q-66 0 -123 -26t-95.5 -63.5t-78.5 -94.5v-563z" />
<glyph unicode="c" horiz-adv-x="960" d="M131 528q0 258 123.5 412t329.5 154q92 0 164 -25.5t139 -83.5l-60 -78q-107 84 -237 84q-151 0 -241.5 -118t-90.5 -343q0 -223 89 -335.5t241 -112.5q128 0 248 90l57 -80q-134 -115 -307 -115q-208 0 -331.5 146.5t-123.5 404.5z" />
<glyph unicode="d" horiz-adv-x="1198" d="M141 530q0 251 113 407.5t307 156.5q184 0 301 -146v574l119 -15v-1507h-102l-15 164q-111 -187 -321 -187q-187 0 -294.5 148.5t-107.5 404.5zM268 532q0 -221 78 -338.5t215 -117.5q102 0 172.5 49t128.5 149v560q-113 161 -280 161q-144 0 -230 -121 q-85 -120 -84 -337v-5z" />
<glyph unicode="e" d="M131 526q0 258 116 413t312 155q205 0 311.5 -138.5t106.5 -380.5q0 -46 -4 -86h-715q10 -209 99.5 -310t234.5 -101q84 0 149 25t140 81l55 -75q-161 -132 -350 -132q-213 0 -334 145.5t-121 403.5zM258 582h602v30q0 180 -74.5 281.5t-222.5 101.5q-133 0 -214.5 -102 t-90.5 -311z" />
<glyph unicode="f" horiz-adv-x="657" d="M43 975v96h186v176q0 127 79 201t218 74q125 0 250 -58l-41 -88q-106 47 -203 47q-92 0 -138 -43t-46 -137v-172h283l-17 -96h-266v-975h-119v975h-186z" />
<glyph unicode="g" horiz-adv-x="1030" d="M68 -113h110q2 -115 74.5 -162t261.5 -47q181 0 262.5 52t81.5 151q0 86 -63.5 132t-178.5 46h-174q-127 0 -195.5 54t-68.5 135q0 108 117 182q-92 47 -136 120.5t-44 180.5q0 162 108.5 262.5t284.5 100.5h46q106 0 182 8q93 9 134.5 21.5t106.5 39.5l37 -114 q-104 -28 -297 -31q182 -85 182 -291q0 -159 -104 -255.5t-283 -96.5q-80 0 -139 20q-82 -47 -82 -123q0 -110 170 -110h174q157 0 250.5 -76.5t93.5 -198.5q0 -147 -117 -227t-348 -80q-240 0 -343 72.5t-103 234.5zM238 731q0 -128 73 -199t197 -71q130 0 200 70t70 198 q0 129 -68 200.5t-202 71.5q-132 0 -201 -73.5t-69 -196.5z" />
<glyph unicode="h" horiz-adv-x="1196" d="M217 0v1507l119 15v-629q143 201 354 201q143 0 221 -87t78 -241v-766h-119v750q0 247 -202 247q-102 0 -181 -57t-151 -164v-776h-119z" />
<glyph unicode="i" horiz-adv-x="552" d="M186 1456q0 36 24.5 61t63.5 25q40 0 65.5 -25t25.5 -61t-25.5 -61t-65.5 -25q-39 0 -63.5 25t-24.5 61zM217 0v1071h119v-1071h-119z" />
<glyph unicode="j" horiz-adv-x="552" d="M12 -330q59 29 92.5 53.5t62 65t39.5 99.5t11 147v1036h119v-1032q0 -71 -7.5 -126.5t-24.5 -99t-39 -75.5t-57 -60t-71.5 -48.5t-89.5 -45.5zM186 1456q0 36 24.5 61t63.5 25q40 0 65.5 -25t25.5 -61t-25.5 -61t-65.5 -25q-39 0 -63.5 25t-24.5 61z" />
<glyph unicode="k" horiz-adv-x="999" d="M217 0v1507l119 15v-1522h-119zM348 586l451 485h137l-451 -479l498 -592h-143z" />
<glyph unicode="l" horiz-adv-x="581" d="M207 180v1327l119 15v-1338q0 -104 86 -104q41 0 73 14l33 -88q-60 -29 -123 -29q-87 0 -137.5 51.5t-50.5 151.5z" />
<glyph unicode="m" horiz-adv-x="1746" d="M217 0v1071h102l11 -182q63 96 146.5 150.5t182.5 54.5q102 0 172 -56.5t95 -156.5q63 99 148.5 156t187.5 57q129 0 203.5 -88t74.5 -240v-766h-119v750q0 247 -182 247q-91 0 -162.5 -57t-138.5 -164v-776h-119v750q0 247 -182 247q-91 0 -163.5 -57t-137.5 -164v-776 h-119z" />
<glyph unicode="n" horiz-adv-x="1196" d="M217 0v1071h102l13 -182q140 205 358 205q145 0 222 -85.5t77 -242.5v-766h-119v750q0 129 -50.5 188t-151.5 59q-102 0 -181 -57t-151 -164v-776h-119z" />
<glyph unicode="o" horiz-adv-x="1169" d="M131 535q0 258 122 408.5t333 150.5q214 0 333 -146.5t119 -408.5q0 -260 -121 -411t-333 -151q-213 0 -333 149.5t-120 408.5zM258 535q0 -223 85 -340t241 -117t242.5 117t86.5 344q0 223 -85 338.5t-242 115.5q-155 0 -241.5 -116t-86.5 -342z" />
<glyph unicode="p" horiz-adv-x="1189" d="M217 -420v1491h102l11 -156q60 84 144.5 131.5t184.5 47.5q194 0 292 -144t98 -409q0 -259 -106 -411.5t-302 -152.5q-188 0 -305 148v-528zM336 242q108 -164 282 -164q149 0 226.5 117t77.5 346q0 454 -285 454q-95 0 -166.5 -48.5t-134.5 -135.5v-569z" />
<glyph unicode="q" horiz-adv-x="1198" d="M141 530q0 252 113.5 408t308.5 156q184 0 307 -152l9 129h102v-1491l-119 17v559q-111 -179 -319 -179q-187 0 -294.5 148.5t-107.5 404.5zM268 532q0 -221 78 -338.5t217 -117.5q102 0 172 49t127 147v564q-111 159 -278 159q-146 0 -232 -121q-84 -120 -84 -337v-5z " />
<glyph unicode="r" horiz-adv-x="763" d="M217 0v1071h102l11 -240q43 133 117 198t186 65q59 0 106 -13l-22 -112q-47 12 -94 12q-110 0 -176.5 -78.5t-110.5 -247.5v-655h-119z" />
<glyph unicode="s" horiz-adv-x="950" d="M80 115l65 75q140 -112 316 -112q119 0 191.5 54t72.5 151q0 96 -57.5 143.5t-225.5 93.5q-170 44 -244.5 112.5t-74.5 180.5q0 122 101.5 201.5t254.5 79.5q189 0 342 -119l-55 -78q-73 51 -138.5 74.5t-144.5 23.5q-107 0 -172 -48.5t-65 -131.5q0 -78 56.5 -121 t203.5 -82q182 -48 263 -119.5t81 -203.5q0 -97 -52.5 -168.5t-139.5 -107.5t-195 -36q-220 0 -383 138z" />
<glyph unicode="t" horiz-adv-x="698" d="M37 975v96h180v256l119 14v-270h276l-14 -96h-262v-709q0 -94 38.5 -140t119.5 -46q83 0 163 47l48 -82q-104 -68 -226 -68q-123 0 -192.5 72.5t-69.5 210.5v715h-180z" />
<glyph unicode="u" horiz-adv-x="1185" d="M207 305v766h119v-754q0 -125 52.5 -183t158.5 -58q185 0 313 219v776h119v-1071h-103l-6 190q-127 -213 -350 -213q-145 0 -224 85t-79 243z" />
<glyph unicode="v" horiz-adv-x="962" d="M31 1071h131l325 -967l318 967h127l-371 -1071h-147z" />
<glyph unicode="w" horiz-adv-x="1429" d="M51 1071h123l229 -979l250 979h135l246 -981l228 981h116l-262 -1071h-160l-237 961l-242 -961h-158z" />
<glyph unicode="x" horiz-adv-x="940" d="M20 0l377 569l-327 502h139l262 -428l264 428h135l-329 -498l377 -573h-144l-309 498l-307 -498h-138z" />
<glyph unicode="y" horiz-adv-x="958" d="M31 1071h129l323 -983l318 983h125l-367 -1075q-32 -93 -66 -157t-84.5 -117.5t-120.5 -87t-167 -52.5l-12 92q80 20 133 45.5t90.5 68t60.5 88.5t51 124h-41z" />
<glyph unicode="z" horiz-adv-x="864" d="M57 0v92l598 877h-549v102h689v-92l-598 -877h594l-13 -102h-721z" />
<glyph unicode="{" horiz-adv-x="667" d="M90 664v102q79 0 117.5 35.5t38.5 116.5v522q0 134 70 195t221 61v-92q-96 0 -137.5 -38t-41.5 -122v-520q0 -100 -36 -143.5t-119 -65.5q83 -22 119 -65t36 -144v-518q0 -84 41.5 -122t137.5 -38v-92q-151 0 -221 61t-70 195v522q0 81 -38.5 115.5t-117.5 34.5z" />
<glyph unicode="|" horiz-adv-x="833" d="M360 -205v1852h111v-1852h-111z" />
<glyph unicode="}" horiz-adv-x="667" d="M131 -172q96 0 137 38t41 122v518q0 101 36.5 144t119.5 65q-83 22 -119.5 65.5t-36.5 143.5v520q0 84 -41 122t-137 38v92q150 0 220.5 -61t70.5 -195v-522q0 -81 38.5 -116.5t117.5 -35.5v-102q-79 0 -117.5 -34.5t-38.5 -115.5v-522q0 -134 -70.5 -195t-220.5 -61v92z " />
<glyph unicode="~" horiz-adv-x="966" d="M104 604q46 73 97.5 114.5t130.5 41.5q45 0 92 -18.5t80 -41t72.5 -41t70.5 -18.5q45 0 75.5 22.5t63.5 67.5l74 -41q-42 -70 -93 -109.5t-128 -39.5q-56 0 -115 29.5t-111 59t-89 29.5q-46 0 -78 -24.5t-66 -71.5z" />
<glyph unicode="&#xa1;" horiz-adv-x="491" d="M147 874q0 42 28.5 70.5t70.5 28.5t70 -28.5t28 -70.5q0 -40 -28 -68t-70 -28t-70.5 28t-28.5 68zM184 -403l11 913h106l10 -913h-127z" />
<glyph unicode="&#xa2;" horiz-adv-x="960" d="M131 528q0 242 109.5 393.5t294.5 170.5v301h102v-301q140 -12 250 -107l-60 -78q-107 84 -237 84q-151 0 -241.5 -118t-90.5 -343q0 -223 89 -335.5t241 -112.5q128 0 248 90l57 -80q-115 -98 -256 -112v-287h-105v287q-186 19 -293.5 163t-107.5 385z" />
<glyph unicode="&#xa3;" horiz-adv-x="1042" d="M104 0v98q99 44 134.5 94.5t35.5 155.5v330h-133v88h133v258q0 158 92.5 259t260.5 101q103 0 183 -37t148 -116l-79 -64q-53 61 -110 89t-138 28q-115 0 -176.5 -70.5t-61.5 -191.5v-256h438v-88h-438v-328q0 -100 -30.5 -153.5t-106.5 -92.5h672l-15 -104h-809z" />
<glyph unicode="&#xa4;" horiz-adv-x="1146" d="M100 283l166 161q-82 108 -82 254q0 143 80 250l-164 164l74 74l164 -164q99 74 235 74q137 0 238 -72l164 162l72 -74l-160 -160q82 -108 82 -254q0 -151 -84 -258l162 -157l-74 -74l-162 162q-101 -72 -238 -72q-136 0 -235 76l-166 -166zM299 698q0 -139 71.5 -221 t204.5 -82q136 0 207.5 82t71.5 221t-71.5 221t-207.5 82q-133 0 -204.5 -82t-71.5 -221z" />
<glyph unicode="&#xa5;" horiz-adv-x="1048" d="M27 1362h135l366 -639l365 639h129l-399 -670h241v-88h-278v-211h278v-88h-278v-305h-121v305h-279v88h279v211h-279v88h240z" />
<glyph unicode="&#xa6;" horiz-adv-x="833" d="M360 -205v742h111v-742h-111zM360 907v740h111v-740h-111z" />
<glyph unicode="&#xa7;" horiz-adv-x="1089" d="M172 649q0 149 162 254q-59 38 -85 86t-26 117q0 125 96.5 200.5t245.5 75.5q100 0 175.5 -26t148.5 -74l-49 -80q-68 43 -132 62.5t-143 19.5q-102 0 -162.5 -48.5t-60.5 -123.5q0 -78 52 -122t214 -99q169 -57 240.5 -123t71.5 -176q0 -145 -158 -250q110 -71 110 -203 q0 -127 -100 -202.5t-258 -75.5q-182 0 -334 96l51 84q125 -82 287 -82q106 0 170 47t64 123q0 79 -48.5 121.5t-205.5 97.5q-181 60 -253.5 122.5t-72.5 178.5zM289 662q0 -84 48 -127.5t195 -94.5q100 -34 154 -59q117 87 117 197q0 55 -18 90t-70.5 67.5t-153.5 67.5 q-81 27 -153 61q-119 -91 -119 -202z" />
<glyph unicode="&#xa8;" horiz-adv-x="673" d="M61 1438q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57zM449 1438q0 34 23 58t58 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58 23t-23 57z" />
<glyph unicode="&#xa9;" horiz-adv-x="1658" d="M221 891q0 177 80.5 318.5t219 219.5t308.5 78q127 0 239.5 -46t193.5 -126t128.5 -195.5t47.5 -248.5q0 -177 -80.5 -318.5t-219.5 -220t-309 -78.5t-308.5 78t-219 219.5t-80.5 319.5zM313 891q0 -239 146 -388t372 -149q224 0 369.5 150.5t145.5 386.5 q0 238 -145.5 387t-369.5 149q-226 0 -372 -150t-146 -386zM530 893q0 178 88.5 273.5t212.5 95.5q117 0 211 -72l-49 -68q-70 52 -157 52q-83 0 -142 -67t-59 -214q0 -142 55.5 -212.5t147.5 -70.5q87 0 172 60l45 -68q-97 -82 -221 -82q-137 0 -220.5 98.5t-83.5 274.5z " />
<glyph unicode="&#xaa;" horiz-adv-x="1024" d="M162 803q0 127 96 195.5t264 68.5h156v70q0 92 -44 132t-147 40q-101 0 -231 -45l-31 86q145 53 275 53q152 0 223.5 -64.5t71.5 -193.5v-377q0 -62 16 -88t51 -39l-24 -80q-61 11 -95.5 41t-46.5 88q-92 -129 -266 -129q-125 0 -196.5 66t-71.5 176zM164 0v104h727v-104 h-727zM285 809q0 -76 44.5 -116t123.5 -40q148 0 225 135v197h-139q-254 0 -254 -176z" />
<glyph unicode="&#xab;" horiz-adv-x="1136" d="M145 549v80l336 436l74 -51l-281 -424l281 -426l-74 -49zM582 549v80l336 436l73 -51l-280 -424l280 -426l-73 -49z" />
<glyph unicode="&#xac;" horiz-adv-x="1021" d="M141 606v105h740v-367h-113v262h-627z" />
<glyph unicode="&#xad;" horiz-adv-x="821" d="M139 590v102h543v-102h-543z" />
<glyph unicode="&#xae;" horiz-adv-x="1308" d="M164 1014q0 134 64.5 248t177.5 180.5t247 66.5q210 0 352 -141t142 -350q0 -205 -143 -348.5t-351 -143.5q-135 0 -247.5 64t-177 176t-64.5 248zM246 1014q0 -179 117 -296.5t290 -117.5q175 0 293.5 120t118.5 298q0 181 -117 298.5t-295 117.5q-172 0 -289.5 -121 t-117.5 -299zM483 745v558h142q233 0 233 -166q0 -116 -131 -150l152 -242h-103l-131 228h-76v-228h-86zM569 1044h80q121 0 121 93q0 52 -32.5 73t-100.5 21h-68v-187z" />
<glyph unicode="&#xaf;" horiz-adv-x="616" d="M61 1384v97h494v-97h-494z" />
<glyph unicode="&#xb0;" horiz-adv-x="1067" d="M141 1118q0 138 92 222.5t216 84.5q130 0 219.5 -84t89.5 -225q0 -138 -90 -222.5t-219 -84.5q-124 0 -216 84t-92 225zM246 1118q0 -101 58 -160t145 -59q91 0 146.5 58t55.5 159q0 102 -55.5 160.5t-146.5 58.5q-87 0 -145 -58t-58 -159z" />
<glyph unicode="&#xb1;" horiz-adv-x="1009" d="M141 0v104h727v-104h-727zM141 647v103h308v311h112v-311h307v-103h-307v-311h-112v311h-308z" />
<glyph unicode="&#xb2;" horiz-adv-x="819" d="M135 1395q109 127 268 127q117 0 186.5 -65.5t69.5 -166.5q0 -48 -13.5 -91.5t-36 -84t-74.5 -99.5t-105.5 -113.5t-153.5 -151.5h408l-14 -88h-516v83q253 251 325 346.5t72 190.5q0 72 -42 112t-114 40q-108 0 -188 -93z" />
<glyph unicode="&#xb3;" horiz-adv-x="819" d="M129 760l64 57q83 -86 194 -86q92 0 142.5 47t50.5 125q0 164 -187 164h-61l14 80h41q72 0 122 39t50 114q0 65 -43.5 102.5t-114.5 37.5q-55 0 -99.5 -18t-94.5 -60l-55 63q116 99 256 99q118 0 188 -59.5t70 -153.5q0 -80 -48.5 -132t-128.5 -67q87 -5 143 -60t56 -151 q0 -112 -80.5 -184t-214.5 -72q-158 0 -264 115z" />
<glyph unicode="&#xb4;" horiz-adv-x="561" d="M61 1391l385 211l54 -107l-400 -174z" />
<glyph unicode="&#xb5;" horiz-adv-x="1198" d="M217 -416v1487h119v-764q0 -117 55.5 -171t145.5 -54q193 0 311 242v747h119v-709q0 -206 55 -366l-109 -14q-25 61 -36 109t-17 118q-128 -232 -323 -232q-159 0 -224 129q23 -124 23 -270v-239z" />
<glyph unicode="&#xb6;" horiz-adv-x="1449" d="M207 1004q0 191 125 295t350 104h459v-1806l-111 -19v1727h-262v-1708l-111 -19v1024q-233 9 -341.5 118.5t-108.5 283.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="423" d="M113 653q0 40 28.5 68.5t69.5 28.5q43 0 70.5 -28t27.5 -69q0 -42 -27 -70t-71 -28q-41 0 -69.5 28.5t-28.5 69.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="501" d="M61 -422l37 74q57 -31 125 -31q50 0 81.5 22.5t31.5 69.5q0 90 -182 90l20 230h78v-164q96 -7 142 -48.5t46 -107.5q0 -84 -61.5 -131t-153.5 -47q-100 0 -164 43z" />
<glyph unicode="&#xb9;" horiz-adv-x="819" d="M166 1348l272 161h90v-847h-104v735l-211 -123z" />
<glyph unicode="&#xba;" horiz-adv-x="1024" d="M139 981q0 195 99.5 308.5t271.5 113.5q174 0 272.5 -110t98.5 -308q0 -197 -100 -310.5t-273 -113.5t-271 112t-98 308zM145 0v104h727v-104h-727zM262 981q0 -159 65 -242.5t183 -83.5q119 0 183.5 83.5t64.5 246.5q0 161 -63 242.5t-183 81.5q-119 0 -184.5 -82.5 t-65.5 -245.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="1118" d="M145 164l283 426l-283 424l74 51l336 -436v-80l-336 -434zM563 164l283 426l-283 424l74 51l336 -436v-80l-336 -434z" />
<glyph unicode="&#xbc;" horiz-adv-x="1871" d="M166 1241l272 162h90v-848h-104v735l-211 -123zM477 -104l836 1634l82 -39l-834 -1636zM1159 211v76l266 575l91 -35l-246 -530h276l10 235h93v-235h114v-86h-114v-211h-103v211h-387z" />
<glyph unicode="&#xbd;" horiz-adv-x="1871" d="M166 1241l272 162h90v-848h-104v735l-211 -123zM477 -104l836 1634l82 -39l-834 -1636zM1188 733q109 127 268 127q117 0 186.5 -65t69.5 -166q0 -48 -13.5 -91.5t-36 -84t-74.5 -100t-106 -114.5t-153 -151h408l-15 -88h-516v84q135 134 201 203t117 135t65.5 107.5 t14.5 91.5q0 72 -42 111.5t-114 39.5q-108 0 -188 -92z" />
<glyph unicode="&#xbe;" horiz-adv-x="1871" d="M129 653l64 58q83 -86 194 -86q92 0 142.5 47t50.5 125q0 164 -187 164h-61l14 79h41q72 0 122 39.5t50 114.5q0 65 -43 102t-115 37q-55 0 -99.5 -18t-94.5 -60l-55 64q115 98 256 98q118 0 188 -59.5t70 -153.5q0 -80 -48 -131.5t-129 -66.5q87 -5 143 -60t56 -151 q0 -112 -80.5 -184t-214.5 -72q-159 0 -264 114zM477 -104l836 1634l82 -39l-834 -1636zM1159 211v76l266 575l91 -35l-246 -530h276l10 235h93v-235h114v-86h-114v-211h-103v211h-387z" />
<glyph unicode="&#xbf;" horiz-adv-x="915" d="M84 -131q0 63 20.5 114t53.5 85t72.5 63t79.5 56.5t73 58t53.5 74t20.5 98.5v92h116v-100q0 -65 -20 -117t-52 -86t-71 -63.5t-78 -56t-71 -55t-52 -69t-20 -90.5q0 -87 63.5 -141t169.5 -54q91 0 164.5 37.5t143.5 116.5l84 -65q-86 -96 -182 -142.5t-216 -46.5 q-161 0 -256.5 86t-95.5 205zM414 874q0 42 27.5 70.5t70.5 28.5q41 0 68.5 -28.5t27.5 -70.5q0 -40 -27.5 -68t-68.5 -28q-43 0 -70.5 27.5t-27.5 68.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1157" d="M33 0l469 1403h153l467 -1403h-129l-123 383h-589l-123 -383h-125zM315 487h523l-260 816zM350 1712l53 107l385 -197l-32 -70z" />
<glyph unicode="&#xc1;" horiz-adv-x="1157" d="M33 0l469 1403h153l467 -1403h-129l-123 383h-589l-123 -383h-125zM315 487h523l-260 816zM360 1622l385 197l54 -107l-406 -160z" />
<glyph unicode="&#xc2;" horiz-adv-x="1157" d="M33 0l469 1403h153l467 -1403h-129l-123 383h-589l-123 -383h-125zM276 1622l267 209h73l265 -209l-54 -61l-247 163l-250 -163zM315 487h523l-260 816z" />
<glyph unicode="&#xc3;" horiz-adv-x="1157" d="M33 0l469 1403h153l467 -1403h-129l-123 383h-589l-123 -383h-125zM270 1671q76 139 189 139q40 0 83 -23t83 -46t73 -23q32 0 58 20t57 68l74 -37q-71 -147 -189 -147q-44 0 -89 23.5t-83.5 47t-66.5 23.5q-34 0 -60 -19.5t-55 -62.5zM315 487h523l-260 816z" />
<glyph unicode="&#xc4;" horiz-adv-x="1157" d="M33 0l469 1403h153l467 -1403h-129l-123 383h-589l-123 -383h-125zM303 1696q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57zM315 487h523l-260 816zM690 1696q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23 t-23.5 57z" />
<glyph unicode="&#xc5;" horiz-adv-x="1157" d="M33 0l469 1403h153l467 -1403h-129l-123 383h-589l-123 -383h-125zM315 487h523l-260 816zM377 1747q0 84 56.5 138t146.5 54q89 0 144.5 -54t55.5 -138q0 -85 -55.5 -139t-144.5 -54q-90 0 -146.5 54t-56.5 139zM465 1747q0 -57 30 -88t85 -31q53 0 82.5 31t29.5 88 q0 56 -29 87.5t-83 31.5q-55 0 -85 -31.5t-30 -87.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1605" d="M0 0l547 1403h866l-14 -103h-666l135 -524h529v-100h-502l147 -574h467v-102h-565l-94 377h-584l-143 -377h-123zM305 481h518l-205 824z" />
<glyph unicode="&#xc7;" horiz-adv-x="1126" d="M135 702q0 175 44.5 314.5t122.5 227.5t180 134.5t220 46.5t199 -31.5t162 -97.5l-66 -82q-73 57 -137.5 82t-148.5 25q-95 0 -173.5 -35t-141 -106.5t-97.5 -193.5t-35 -284q0 -155 34 -275t94.5 -193.5t139.5 -110.5t173 -37q100 0 169.5 29.5t141.5 87.5l63 -80 q-139 -135 -346 -146v-108q96 -7 142.5 -48.5t46.5 -107.5q0 -84 -61.5 -131t-153.5 -47q-100 0 -164 43l37 74q57 -31 125 -31q50 0 81 22.5t31 69.5q0 90 -182 90l16 177q-232 19 -374 207t-142 515z" />
<glyph unicode="&#xc8;" horiz-adv-x="1048" d="M227 0v1403h713l-14 -103h-576v-522h498v-102h-498v-574h604v-102h-727zM362 1712l54 107l385 -197l-33 -70z" />
<glyph unicode="&#xc9;" horiz-adv-x="1048" d="M227 0v1403h713l-14 -103h-576v-522h498v-102h-498v-574h604v-102h-727zM373 1622l385 197l53 -107l-405 -160z" />
<glyph unicode="&#xca;" horiz-adv-x="1048" d="M227 0v1403h713l-14 -103h-576v-522h498v-102h-498v-574h604v-102h-727zM289 1622l266 209h74l264 -209l-53 -61l-248 163l-250 -163z" />
<glyph unicode="&#xcb;" horiz-adv-x="1048" d="M227 0v1403h713l-14 -103h-576v-522h498v-102h-498v-574h604v-102h-727zM315 1696q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57zM702 1696q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57z" />
<glyph unicode="&#xcc;" horiz-adv-x="577" d="M57 1712l54 107l385 -197l-33 -70zM227 0v1403h123v-1403h-123z" />
<glyph unicode="&#xcd;" horiz-adv-x="577" d="M68 1622l385 197l53 -107l-406 -160zM227 0v1403h123v-1403h-123z" />
<glyph unicode="&#xce;" horiz-adv-x="577" d="M-16 1622l266 209h74l264 -209l-53 -61l-248 163l-250 -163zM227 0v1403h123v-1403h-123z" />
<glyph unicode="&#xcf;" horiz-adv-x="577" d="M10 1696q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57zM227 0v1403h123v-1403h-123zM397 1696q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57z" />
<glyph unicode="&#xd0;" horiz-adv-x="1304" d="M47 672v98h191v633h278q75 0 139.5 -7.5t132.5 -28t122.5 -53.5t104 -87t82.5 -124.5t52.5 -169.5t19.5 -220q0 -168 -38.5 -297t-99.5 -206t-146.5 -126t-168.5 -66.5t-177 -17.5h-301v672h-191zM360 102h187q493 0 493 611q0 107 -15.5 194t-41.5 147.5t-65.5 105.5 t-81.5 71t-96 42t-102 21.5t-106 5.5h-172v-530h359v-98h-359v-570z" />
<glyph unicode="&#xd1;" horiz-adv-x="1378" d="M227 0v1403h158l666 -1268q-17 251 -17 416v852h117v-1403h-162l-661 1268q16 -256 16 -484v-784h-117zM397 1671q76 139 189 139q40 0 83 -23t83 -46t73 -23q32 0 58 20t57 68l74 -37q-71 -147 -189 -147q-44 0 -89 23.5t-83.5 47t-66.5 23.5q-34 0 -60 -19.5t-55 -62.5 z" />
<glyph unicode="&#xd2;" horiz-adv-x="1415" d="M135 698q0 341 157.5 534t414.5 193q262 0 417.5 -188.5t155.5 -536.5q0 -342 -154.5 -532.5t-418.5 -190.5q-260 0 -416 189t-156 532zM264 698q0 -305 120 -460.5t323 -155.5q211 0 327.5 154.5t116.5 463.5q0 311 -117.5 466t-326.5 155q-205 0 -324 -156.5 t-119 -466.5zM477 1712l53 107l385 -197l-32 -70z" />
<glyph unicode="&#xd3;" horiz-adv-x="1415" d="M135 698q0 341 157.5 534t414.5 193q262 0 417.5 -188.5t155.5 -536.5q0 -342 -154.5 -532.5t-418.5 -190.5q-260 0 -416 189t-156 532zM264 698q0 -305 120 -460.5t323 -155.5q211 0 327.5 154.5t116.5 463.5q0 311 -117.5 466t-326.5 155q-205 0 -324 -156.5 t-119 -466.5zM487 1622l385 197l54 -107l-406 -160z" />
<glyph unicode="&#xd4;" horiz-adv-x="1415" d="M135 698q0 341 157.5 534t414.5 193q262 0 417.5 -188.5t155.5 -536.5q0 -342 -154.5 -532.5t-418.5 -190.5q-260 0 -416 189t-156 532zM264 698q0 -305 120 -460.5t323 -155.5q211 0 327.5 154.5t116.5 463.5q0 311 -117.5 466t-326.5 155q-205 0 -324 -156.5 t-119 -466.5zM403 1622l267 209h73l265 -209l-54 -61l-247 163l-250 -163z" />
<glyph unicode="&#xd5;" horiz-adv-x="1415" d="M135 698q0 341 157.5 534t414.5 193q262 0 417.5 -188.5t155.5 -536.5q0 -342 -154.5 -532.5t-418.5 -190.5q-260 0 -416 189t-156 532zM264 698q0 -305 120 -460.5t323 -155.5q211 0 327.5 154.5t116.5 463.5q0 311 -117.5 466t-326.5 155q-205 0 -324 -156.5 t-119 -466.5zM397 1671q76 139 189 139q40 0 83 -23t83 -46t73 -23q32 0 58 20t57 68l74 -37q-71 -147 -189 -147q-44 0 -89 23.5t-83.5 47t-66.5 23.5q-34 0 -60 -19.5t-55 -62.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1415" d="M135 698q0 341 157.5 534t414.5 193q262 0 417.5 -188.5t155.5 -536.5q0 -342 -154.5 -532.5t-418.5 -190.5q-260 0 -416 189t-156 532zM264 698q0 -305 120 -460.5t323 -155.5q211 0 327.5 154.5t116.5 463.5q0 311 -117.5 466t-326.5 155q-205 0 -324 -156.5 t-119 -466.5zM430 1696q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57zM817 1696q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57z" />
<glyph unicode="&#xd7;" horiz-adv-x="1009" d="M174 369l252 258l-252 262l76 76l252 -265l258 265l78 -76l-256 -258l256 -262l-78 -74l-252 262l-258 -262z" />
<glyph unicode="&#xd8;" horiz-adv-x="1415" d="M135 698q0 341 157.5 534t414.5 193q81 0 155 -18l72 252l108 -29l-81 -260q151 -75 235 -246t84 -424q0 -342 -154.5 -532.5t-418.5 -190.5q-83 0 -160 21l-66 -244l-108 31l80 250q-151 77 -234.5 245.5t-83.5 417.5zM264 698q0 -426 230 -563l346 1168q-62 18 -133 18 q-205 0 -324 -156.5t-119 -466.5zM571 100q61 -18 136 -18q211 0 327.5 154.5t116.5 463.5q0 442 -233 572z" />
<glyph unicode="&#xd9;" horiz-adv-x="1335" d="M207 438v965h123v-954q0 -176 83 -270.5t255 -94.5t254 95t82 270v954h124v-965q0 -210 -118 -335.5t-342 -125.5q-226 0 -343.5 125.5t-117.5 335.5zM438 1712l54 107l385 -197l-33 -70z" />
<glyph unicode="&#xda;" horiz-adv-x="1335" d="M207 438v965h123v-954q0 -176 83 -270.5t255 -94.5t254 95t82 270v954h124v-965q0 -210 -118 -335.5t-342 -125.5q-226 0 -343.5 125.5t-117.5 335.5zM449 1622l385 197l53 -107l-406 -160z" />
<glyph unicode="&#xdb;" horiz-adv-x="1335" d="M207 438v965h123v-954q0 -176 83 -270.5t255 -94.5t254 95t82 270v954h124v-965q0 -210 -118 -335.5t-342 -125.5q-226 0 -343.5 125.5t-117.5 335.5zM365 1622l266 209h74l264 -209l-54 -61l-247 163l-250 -163z" />
<glyph unicode="&#xdc;" horiz-adv-x="1335" d="M207 438v965h123v-954q0 -176 83 -270.5t255 -94.5t254 95t82 270v954h124v-965q0 -210 -118 -335.5t-342 -125.5q-226 0 -343.5 125.5t-117.5 335.5zM391 1696q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57zM778 1696q0 34 23.5 58 t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57z" />
<glyph unicode="&#xdd;" horiz-adv-x="1071" d="M27 1403h137l377 -746l370 746h131l-444 -854v-549h-123v547zM315 1622l385 197l54 -107l-406 -160z" />
<glyph unicode="&#xde;" horiz-adv-x="1167" d="M227 0v1403h123v-250h207q518 0 518 -424q0 -120 -38.5 -208t-110 -140.5t-164.5 -77t-211 -24.5h-201v-279h-123zM350 381h201q73 0 131 9.5t108.5 34t84 63t52.5 99t19 140.5q0 175 -98.5 249.5t-292.5 74.5h-205v-670z" />
<glyph unicode="&#xdf;" horiz-adv-x="1191" d="M217 0v1106q0 198 104.5 307t280.5 109q151 0 243.5 -77.5t92.5 -197.5q0 -52 -18 -95.5t-44.5 -71t-57.5 -55t-57.5 -48.5t-44.5 -49.5t-18 -59.5q0 -35 22 -66.5t57 -57.5t77 -53t84 -61.5t77 -76t57 -102.5t22 -134q0 -152 -94 -246t-240 -94q-110 0 -197 48l37 84 q68 -31 152 -31q99 0 159 62.5t60 178.5q0 55 -16.5 102t-43.5 79.5t-61.5 63t-71 53.5t-71 50t-61.5 53t-43.5 63t-16.5 79q0 41 17.5 76t43.5 58t56.5 49t56.5 50.5t43.5 62t17.5 83.5q0 86 -61 135t-160 49q-122 0 -193 -80.5t-71 -242.5v-1102h-119z" />
<glyph unicode="&#xe0;" d="M115 297q0 167 118 256.5t326 89.5h205v105q0 129 -58 187t-190 58q-125 0 -291 -61l-30 90q182 72 337 72q351 0 351 -340v-516q0 -84 21.5 -121t68.5 -56l-27 -84q-73 14 -113.5 56t-52.5 125q-112 -181 -334 -181q-153 0 -242 88t-89 232zM242 303q0 -110 59.5 -169.5 t165.5 -59.5q191 0 297 188v295h-186q-163 0 -249.5 -64t-86.5 -190zM317 1495l54 107l385 -211l-37 -70z" />
<glyph unicode="&#xe1;" d="M115 297q0 167 118 256.5t326 89.5h205v105q0 129 -58 187t-190 58q-125 0 -291 -61l-30 90q182 72 337 72q351 0 351 -340v-516q0 -84 21.5 -121t68.5 -56l-27 -84q-73 14 -113.5 56t-52.5 125q-112 -181 -334 -181q-153 0 -242 88t-89 232zM242 303q0 -110 59.5 -169.5 t165.5 -59.5q191 0 297 188v295h-186q-163 0 -249.5 -64t-86.5 -190zM297 1391l385 211l53 -107l-399 -174z" />
<glyph unicode="&#xe2;" d="M115 297q0 167 118 256.5t326 89.5h205v105q0 129 -58 187t-190 58q-125 0 -291 -61l-30 90q182 72 337 72q351 0 351 -340v-516q0 -84 21.5 -121t68.5 -56l-27 -84q-73 14 -113.5 56t-52.5 125q-112 -181 -334 -181q-153 0 -242 88t-89 232zM225 1380l264 215h74 l266 -215l-53 -63l-250 174l-247 -174zM242 303q0 -110 59.5 -169.5t165.5 -59.5q191 0 297 188v295h-186q-163 0 -249.5 -64t-86.5 -190z" />
<glyph unicode="&#xe3;" d="M115 297q0 167 118 256.5t326 89.5h205v105q0 129 -58 187t-190 58q-125 0 -291 -61l-30 90q182 72 337 72q351 0 351 -340v-516q0 -84 21.5 -121t68.5 -56l-27 -84q-73 14 -113.5 56t-52.5 125q-112 -181 -334 -181q-153 0 -242 88t-89 232zM219 1413q76 139 189 139 q40 0 83 -23t83 -46t73 -23q32 0 58 20t57 68l74 -37q-71 -147 -189 -147q-44 0 -89 23.5t-83.5 47t-66.5 23.5q-34 0 -60 -19.5t-55 -62.5zM242 303q0 -110 59.5 -169.5t165.5 -59.5q191 0 297 188v295h-186q-163 0 -249.5 -64t-86.5 -190z" />
<glyph unicode="&#xe4;" d="M115 297q0 167 118 256.5t326 89.5h205v105q0 129 -58 187t-190 58q-125 0 -291 -61l-30 90q182 72 337 72q351 0 351 -340v-516q0 -84 21.5 -121t68.5 -56l-27 -84q-73 14 -113.5 56t-52.5 125q-112 -181 -334 -181q-153 0 -242 88t-89 232zM242 303q0 -110 59.5 -169.5 t165.5 -59.5q191 0 297 188v295h-186q-163 0 -249.5 -64t-86.5 -190zM252 1438q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57zM639 1438q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57z" />
<glyph unicode="&#xe5;" d="M115 297q0 167 118 256.5t326 89.5h205v105q0 129 -58 187t-190 58q-125 0 -291 -61l-30 90q182 72 337 72q351 0 351 -340v-516q0 -84 21.5 -121t68.5 -56l-27 -84q-73 14 -113.5 56t-52.5 125q-112 -181 -334 -181q-153 0 -242 88t-89 232zM242 303q0 -110 59.5 -169.5 t165.5 -59.5q191 0 297 188v295h-186q-163 0 -249.5 -64t-86.5 -190zM326 1489q0 84 56 138t146 54q89 0 145 -54t56 -138q0 -85 -56 -139t-145 -54q-90 0 -146 54t-56 139zM414 1489q0 -57 29.5 -88t84.5 -31q53 0 83 31t30 88q0 56 -29.5 87.5t-83.5 31.5 q-55 0 -84.5 -31.5t-29.5 -87.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1734" d="M115 297q0 167 120 257.5t328 90.5h205v103q0 128 -58.5 186.5t-189.5 58.5q-125 0 -291 -61l-32 90q182 72 338 72q259 0 327 -207q112 207 342 207q198 0 304 -139t106 -380q0 -46 -4 -86h-715q9 -209 97.5 -310t234.5 -101q84 0 150.5 25.5t138.5 80.5l57 -75 q-161 -132 -352 -132q-128 0 -226 57t-155 163q-82 -117 -174.5 -168.5t-214.5 -51.5q-155 0 -245.5 88t-90.5 232zM244 303q0 -110 59.5 -169.5t165.5 -59.5q102 0 179 50.5t153 164.5q-33 115 -33 235v33h-188q-162 0 -249 -64t-87 -190zM895 582h600v30 q0 180 -73.5 281.5t-219.5 101.5q-133 0 -215.5 -102t-91.5 -311z" />
<glyph unicode="&#xe7;" horiz-adv-x="960" d="M131 528q0 258 123.5 412t329.5 154q92 0 164 -25.5t139 -83.5l-60 -78q-107 84 -237 84q-151 0 -241.5 -118t-90.5 -343q0 -223 89 -335.5t241 -112.5q128 0 248 90l57 -80q-122 -106 -283 -115v-108q96 -7 142.5 -48.5t46.5 -107.5q0 -84 -61.5 -131t-153.5 -47 q-100 0 -164 43l37 74q57 -31 125 -31q50 0 81 22.5t31 69.5q0 90 -182 90l16 179q-184 21 -290.5 163.5t-106.5 382.5z" />
<glyph unicode="&#xe8;" d="M131 526q0 258 116 413t312 155q205 0 311.5 -138.5t106.5 -380.5q0 -46 -4 -86h-715q10 -209 99.5 -310t234.5 -101q84 0 149 25t140 81l55 -75q-161 -132 -350 -132q-213 0 -334 145.5t-121 403.5zM258 582h602v30q0 180 -74.5 281.5t-222.5 101.5q-133 0 -214.5 -102 t-90.5 -311zM354 1495l54 107l385 -211l-37 -70z" />
<glyph unicode="&#xe9;" d="M131 526q0 258 116 413t312 155q205 0 311.5 -138.5t106.5 -380.5q0 -46 -4 -86h-715q10 -209 99.5 -310t234.5 -101q84 0 149 25t140 81l55 -75q-161 -132 -350 -132q-213 0 -334 145.5t-121 403.5zM258 582h602v30q0 180 -74.5 281.5t-222.5 101.5q-133 0 -214.5 -102 t-90.5 -311zM334 1391l385 211l53 -107l-399 -174z" />
<glyph unicode="&#xea;" d="M131 526q0 258 116 413t312 155q205 0 311.5 -138.5t106.5 -380.5q0 -46 -4 -86h-715q10 -209 99.5 -310t234.5 -101q84 0 149 25t140 81l55 -75q-161 -132 -350 -132q-213 0 -334 145.5t-121 403.5zM258 582h602v30q0 180 -74.5 281.5t-222.5 101.5q-133 0 -214.5 -102 t-90.5 -311zM262 1380l264 215h74l266 -215l-53 -63l-250 174l-248 -174z" />
<glyph unicode="&#xeb;" d="M131 526q0 258 116 413t312 155q205 0 311.5 -138.5t106.5 -380.5q0 -46 -4 -86h-715q10 -209 99.5 -310t234.5 -101q84 0 149 25t140 81l55 -75q-161 -132 -350 -132q-213 0 -334 145.5t-121 403.5zM258 582h602v30q0 180 -74.5 281.5t-222.5 101.5q-133 0 -214.5 -102 t-90.5 -311zM289 1438q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57zM676 1438q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57z" />
<glyph unicode="&#xec;" horiz-adv-x="552" d="M66 1495l53 107l385 -211l-37 -70zM217 0v1071h119v-1071h-119z" />
<glyph unicode="&#xed;" horiz-adv-x="552" d="M45 1391l385 211l53 -107l-399 -174zM217 0v1071h119v-1071h-119z" />
<glyph unicode="&#xee;" horiz-adv-x="552" d="M-27 1380l265 215h73l267 -215l-54 -63l-250 174l-247 -174zM217 0v1071h119v-1071h-119z" />
<glyph unicode="&#xef;" horiz-adv-x="552" d="M0 1438q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57zM217 0v1071h119v-1071h-119zM387 1438q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57z" />
<glyph unicode="&#xf0;" horiz-adv-x="1138" d="M131 477q0 127 43.5 234t136 177t219.5 70q195 0 332 -163q-22 156 -87 276t-183 215l-158 -158l-86 39l164 170q-138 73 -250 93l27 94q155 -30 297 -113l141 150l72 -64l-135 -137q173 -131 253 -318.5t80 -468.5q0 -285 -119 -440.5t-319 -155.5q-195 0 -311.5 134 t-116.5 366zM254 479q0 -192 81 -297.5t224 -105.5q149 0 233.5 129t84.5 373q0 26 -5 98q-134 186 -325 186q-144 0 -218.5 -107.5t-74.5 -275.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1196" d="M217 0v1071h102l13 -182q140 205 358 205q145 0 222 -85.5t77 -242.5v-766h-119v750q0 129 -50.5 188t-151.5 59q-102 0 -181 -57t-151 -164v-776h-119zM299 1413q76 139 188 139q40 0 83.5 -23t83.5 -46t73 -23q32 0 58 20t57 68l73 -37q-71 -147 -188 -147 q-44 0 -89.5 23.5t-84 47t-66.5 23.5q-34 0 -59.5 -19.5t-54.5 -62.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="1169" d="M131 535q0 258 122 408.5t333 150.5q214 0 333 -146.5t119 -408.5q0 -260 -121 -411t-333 -151q-213 0 -333 149.5t-120 408.5zM258 535q0 -223 85 -340t241 -117t242.5 117t86.5 344q0 223 -85 338.5t-242 115.5q-155 0 -241.5 -116t-86.5 -342zM375 1495l53 107 l385 -211l-37 -70z" />
<glyph unicode="&#xf3;" horiz-adv-x="1169" d="M131 535q0 258 122 408.5t333 150.5q214 0 333 -146.5t119 -408.5q0 -260 -121 -411t-333 -151q-213 0 -333 149.5t-120 408.5zM258 535q0 -223 85 -340t241 -117t242.5 117t86.5 344q0 223 -85 338.5t-242 115.5q-155 0 -241.5 -116t-86.5 -342zM354 1391l385 211 l54 -107l-400 -174z" />
<glyph unicode="&#xf4;" horiz-adv-x="1169" d="M131 535q0 258 122 408.5t333 150.5q214 0 333 -146.5t119 -408.5q0 -260 -121 -411t-333 -151q-213 0 -333 149.5t-120 408.5zM258 535q0 -223 85 -340t241 -117t242.5 117t86.5 344q0 223 -85 338.5t-242 115.5q-155 0 -241.5 -116t-86.5 -342zM283 1380l264 215h74 l266 -215l-53 -63l-250 174l-248 -174z" />
<glyph unicode="&#xf5;" horiz-adv-x="1169" d="M131 535q0 258 122 408.5t333 150.5q214 0 333 -146.5t119 -408.5q0 -260 -121 -411t-333 -151q-213 0 -333 149.5t-120 408.5zM258 535q0 -223 85 -340t241 -117t242.5 117t86.5 344q0 223 -85 338.5t-242 115.5q-155 0 -241.5 -116t-86.5 -342zM276 1413 q76 139 189 139q40 0 83.5 -23t83.5 -46t73 -23q32 0 57.5 20t56.5 68l74 -37q-71 -147 -188 -147q-44 0 -89.5 23.5t-84 47t-66.5 23.5q-34 0 -60 -19.5t-55 -62.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1169" d="M131 535q0 258 122 408.5t333 150.5q214 0 333 -146.5t119 -408.5q0 -260 -121 -411t-333 -151q-213 0 -333 149.5t-120 408.5zM258 535q0 -223 85 -340t241 -117t242.5 117t86.5 344q0 223 -85 338.5t-242 115.5q-155 0 -241.5 -116t-86.5 -342zM309 1438q0 34 23.5 58 t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57zM696 1438q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57z" />
<glyph unicode="&#xf7;" horiz-adv-x="1009" d="M141 627v104h727v-104h-727zM408 231q0 40 28.5 68.5t69.5 28.5q43 0 70.5 -28t27.5 -69q0 -42 -27 -70t-71 -28q-41 0 -69.5 28.5t-28.5 69.5zM408 1128q0 40 28.5 68.5t69.5 28.5q43 0 70.5 -28t27.5 -69q0 -42 -27 -70t-71 -28q-41 0 -69.5 28.5t-28.5 69.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1169" d="M131 535q0 258 122 408.5t333 150.5q63 0 129 -17l80 250l100 -29l-84 -260q110 -62 168.5 -189.5t58.5 -309.5q0 -260 -121 -411t-333 -151q-59 0 -125 17l-84 -250l-101 29l88 258q-111 63 -171 193t-60 311zM258 535q0 -307 150 -410l282 854q-43 14 -104 14 q-155 0 -241.5 -116t-86.5 -342zM483 90q47 -12 101 -12q156 0 242.5 117t86.5 344q0 306 -147 405z" />
<glyph unicode="&#xf9;" horiz-adv-x="1185" d="M207 305v766h119v-754q0 -125 52.5 -183t158.5 -58q185 0 313 219v776h119v-1071h-103l-6 190q-127 -213 -350 -213q-145 0 -224 85t-79 243zM377 1495l53 107l385 -211l-37 -70z" />
<glyph unicode="&#xfa;" horiz-adv-x="1185" d="M207 305v766h119v-754q0 -125 52.5 -183t158.5 -58q185 0 313 219v776h119v-1071h-103l-6 190q-127 -213 -350 -213q-145 0 -224 85t-79 243zM356 1391l385 211l54 -107l-400 -174z" />
<glyph unicode="&#xfb;" horiz-adv-x="1185" d="M207 305v766h119v-754q0 -125 52.5 -183t158.5 -58q185 0 313 219v776h119v-1071h-103l-6 190q-127 -213 -350 -213q-145 0 -224 85t-79 243zM285 1380l264 215h74l266 -215l-53 -63l-250 174l-248 -174z" />
<glyph unicode="&#xfc;" horiz-adv-x="1185" d="M207 305v766h119v-754q0 -125 52.5 -183t158.5 -58q185 0 313 219v776h119v-1071h-103l-6 190q-127 -213 -350 -213q-145 0 -224 85t-79 243zM311 1438q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57zM698 1438q0 34 23.5 58t58.5 24 t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57z" />
<glyph unicode="&#xfd;" horiz-adv-x="958" d="M31 1071h129l323 -983l318 983h125l-367 -1075q-32 -93 -66 -157t-84.5 -117.5t-120.5 -87t-167 -52.5l-12 92q80 20 133 45.5t90.5 68t60.5 88.5t51 124h-41zM248 1391l385 211l53 -107l-399 -174z" />
<glyph unicode="&#xfe;" horiz-adv-x="1189" d="M217 -420v1927l119 15v-598q125 170 323 170q194 0 292 -144t98 -409q0 -259 -106 -411.5t-302 -152.5q-188 0 -305 148v-524zM336 242q108 -164 282 -164q149 0 226.5 117t77.5 346q0 454 -285 454q-95 0 -166.5 -48.5t-134.5 -135.5v-569z" />
<glyph unicode="&#xff;" horiz-adv-x="958" d="M31 1071h129l323 -983l318 983h125l-367 -1075q-32 -93 -66 -157t-84.5 -117.5t-120.5 -87t-167 -52.5l-12 92q80 20 133 45.5t90.5 68t60.5 88.5t51 124h-41zM203 1438q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57zM590 1438 q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57z" />
<glyph unicode="&#x152;" horiz-adv-x="1781" d="M135 698q0 170 41.5 308.5t115.5 229.5t174 140t218 49q76 0 147 -22h842l-14 -103h-649q101 -79 159.5 -211.5t67.5 -310.5h344v-102h-342q-4 -197 -66 -343.5t-172 -230.5h687v-102h-863q-71 -23 -141 -23q-119 0 -219 47.5t-173.5 137t-115 227.5t-41.5 309zM264 698 q0 -303 116.5 -459.5t305.5 -156.5q198 0 311 157.5t113 471.5q0 302 -114.5 456t-309.5 154q-191 0 -306.5 -157.5t-115.5 -465.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1880" d="M131 535q0 258 122 408.5t333 150.5q140 0 238.5 -69t152.5 -196q54 125 148 195t221 70q200 0 306.5 -139t106.5 -380q0 -46 -4 -86h-717q10 -209 99.5 -310t234.5 -101q84 0 150.5 25.5t138.5 80.5l55 -75q-158 -132 -350 -132q-139 0 -238.5 70t-154.5 199 q-56 -128 -154 -198.5t-235 -70.5q-213 0 -333 149.5t-120 408.5zM258 535q0 -223 85 -340t241 -117t242.5 117t86.5 344q0 223 -85 338.5t-242 115.5q-155 0 -241.5 -116t-86.5 -342zM1038 582h602v30q0 180 -73 281.5t-217 101.5q-135 0 -219 -102t-93 -311z" />
<glyph unicode="&#x178;" horiz-adv-x="1071" d="M27 1403h137l377 -746l370 746h131l-444 -854v-549h-123v547zM258 1696q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57zM645 1696q0 34 23.5 58t58.5 24t58.5 -24t23.5 -58t-23.5 -57t-58.5 -23t-58.5 23t-23.5 57z" />
<glyph unicode="&#x2c6;" horiz-adv-x="727" d="M61 1380l265 215h73l267 -215l-54 -63l-250 174l-247 -174z" />
<glyph unicode="&#x2dc;" horiz-adv-x="739" d="M61 1413q76 139 189 139q40 0 83 -23t83 -46t73 -23q32 0 58 20t57 68l74 -37q-71 -147 -189 -147q-44 0 -89 23.5t-83.5 47t-66.5 23.5q-34 0 -60 -19.5t-55 -62.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="969" />
<glyph unicode="&#x2001;" horiz-adv-x="1939" />
<glyph unicode="&#x2002;" horiz-adv-x="969" />
<glyph unicode="&#x2003;" horiz-adv-x="1939" />
<glyph unicode="&#x2004;" horiz-adv-x="646" />
<glyph unicode="&#x2005;" horiz-adv-x="484" />
<glyph unicode="&#x2006;" horiz-adv-x="323" />
<glyph unicode="&#x2007;" horiz-adv-x="323" />
<glyph unicode="&#x2008;" horiz-adv-x="242" />
<glyph unicode="&#x2009;" horiz-adv-x="387" />
<glyph unicode="&#x200a;" horiz-adv-x="107" />
<glyph unicode="&#x2010;" horiz-adv-x="821" d="M139 590v102h543v-102h-543z" />
<glyph unicode="&#x2011;" horiz-adv-x="821" d="M139 590v102h543v-102h-543z" />
<glyph unicode="&#x2012;" horiz-adv-x="821" d="M139 590v102h543v-102h-543z" />
<glyph unicode="&#x2013;" horiz-adv-x="1064" d="M82 590v102h901v-102h-901z" />
<glyph unicode="&#x2014;" horiz-adv-x="1603" d="M82 590v102h1440v-102h-1440z" />
<glyph unicode="&#x2018;" horiz-adv-x="448" d="M125 1169q0 33 29 95l116 254h80l-78 -275q41 -24 41 -74q0 -37 -26.5 -63.5t-67.5 -26.5q-42 0 -68 26t-26 64z" />
<glyph unicode="&#x2019;" horiz-adv-x="448" d="M98 1079l78 275q-41 24 -41 73q0 37 26.5 64t67.5 27q42 0 68.5 -26.5t26.5 -64.5q0 -32 -29 -94l-117 -254h-80z" />
<glyph unicode="&#x201a;" horiz-adv-x="448" d="M98 -285l78 275q-41 24 -41 73q0 37 26.5 64t67.5 27q42 0 68.5 -26.5t26.5 -64.5q0 -32 -29 -94l-117 -254h-80z" />
<glyph unicode="&#x201c;" horiz-adv-x="759" d="M125 1169q0 33 29 95l116 254h80l-78 -275q41 -24 41 -74q0 -37 -26.5 -63.5t-67.5 -26.5q-42 0 -68 26t-26 64zM436 1169q0 33 29 95l117 254h80l-78 -275q41 -24 41 -74q0 -37 -27 -63.5t-68 -26.5q-42 0 -68 26t-26 64z" />
<glyph unicode="&#x201d;" horiz-adv-x="759" d="M98 1079l78 275q-41 24 -41 73q0 37 26.5 64t67.5 27q42 0 68.5 -26.5t26.5 -64.5q0 -32 -29 -94l-117 -254h-80zM410 1079l77 275q-41 24 -41 73q0 37 27 64t68 27q42 0 68 -26.5t26 -64.5q0 -32 -29 -94l-117 -254h-79z" />
<glyph unicode="&#x201e;" horiz-adv-x="759" d="M98 -285l78 275q-41 24 -41 73q0 37 26.5 64t67.5 27q42 0 68.5 -26.5t26.5 -64.5q0 -32 -29 -94l-117 -254h-80zM410 -285l77 275q-41 24 -41 73q0 37 27 64t68 27q42 0 68 -26.5t26 -64.5q0 -32 -29 -94l-117 -254h-79z" />
<glyph unicode="&#x2022;" horiz-adv-x="587" d="M141 709q0 65 43 108t109 43q67 0 110 -44t43 -109q0 -64 -43 -107t-110 -43q-66 0 -109 43.5t-43 108.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1286" d="M113 76q0 40 28.5 68t69.5 28q43 0 70.5 -27.5t27.5 -68.5q0 -42 -27.5 -70.5t-70.5 -28.5q-41 0 -69.5 29t-28.5 70zM545 76q0 40 28.5 68t69.5 28q43 0 70.5 -27.5t27.5 -68.5q0 -42 -27.5 -70.5t-70.5 -28.5q-41 0 -69.5 29t-28.5 70zM975 74q0 40 28.5 68t69.5 28 q43 0 70.5 -27.5t27.5 -68.5q0 -42 -27.5 -70.5t-70.5 -28.5q-41 0 -69.5 29t-28.5 70z" />
<glyph unicode="&#x202f;" horiz-adv-x="387" />
<glyph unicode="&#x2039;" horiz-adv-x="700" d="M145 549v80l336 436l74 -51l-281 -424l281 -426l-74 -49z" />
<glyph unicode="&#x203a;" horiz-adv-x="700" d="M145 164l283 426l-283 424l74 51l336 -436v-80l-336 -434z" />
<glyph unicode="&#x205f;" horiz-adv-x="484" />
<glyph unicode="&#x20ac;" horiz-adv-x="1171" d="M74 496l22 88h144v211h-166l22 88h150q45 238 180 369.5t324 131.5q186 0 348 -110l-51 -86q-79 52 -144 73t-149 21q-143 0 -243.5 -100.5t-137.5 -298.5h557l-23 -88h-542v-211h501l-20 -88h-477q27 -213 125.5 -313.5t263.5 -100.5q82 0 144.5 21.5t139.5 64.5v-111 q-146 -80 -301 -80q-206 0 -335 133t-162 386h-170z" />
<glyph unicode="&#x2122;" horiz-adv-x="1771" d="M82 1315v88h641l-16 -88h-256v-701h-103v701h-266zM797 614l39 789h137l225 -598l223 598h135l39 -789h-102l-20 402q-7 177 -3 282h-2l-223 -596h-94l-231 603h-2q3 -142 -5 -289l-18 -402h-98z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1075" d="M0 0v1075h1075v-1075h-1075z" />
<glyph unicode="&#xfb03;" horiz-adv-x="1734" d="M43 975v96h176v156q0 128 83 201t237 73q85 0 157.5 -16t165.5 -55q80 92 252 92q159 0 301 -76l-43 -86q-129 61 -254 61q-104 0 -151 -41.5t-47 -140.5v-168h598v-1071h-119v975h-479v-975h-119v975h-463v-975h-119v975h-176zM338 1071h463v174q0 49 14 103 q-134 55 -268 55q-108 0 -158.5 -42.5t-50.5 -137.5v-152z" />
<glyph unicode="&#xfb04;" horiz-adv-x="1777" d="M43 975v96h176v156q0 128 83 201t237 73q85 0 157.5 -15.5t167.5 -55.5q83 92 256 92q135 0 281 -47l119 16v-1307q0 -53 20.5 -78.5t67.5 -25.5q39 0 71 14l33 -88q-67 -29 -127 -29q-88 0 -136 51.5t-48 155.5v1194q-142 45 -273 45q-108 0 -158 -42.5t-50 -137.5v-172 h282l-14 -96h-268v-975h-119v975h-463v-975h-119v975h-176zM338 1071h463v176q0 55 16 101q-137 55 -270 55q-108 0 -158.5 -42.5t-50.5 -137.5v-152z" />
<hkern u1="C" u2="&#x2d;" k="123" />
<hkern u1="Q" u2="&#x7d;" k="-12" />
<hkern u1="Q" u2="]" k="-12" />
<hkern u1="Q" u2="&#x29;" k="-4" />
<hkern u1="_" u2="y" k="20" />
<hkern u1="f" u2="&#xf0;" k="8" />
<hkern u1="&#xc7;" u2="&#x2d;" k="123" />
<hkern u1="&#xde;" u2="&#xf0;" k="31" />
<hkern g1="ampersand" 	g2="asterisk,registered" 	k="82" />
<hkern g1="ampersand" 	g2="at" 	k="41" />
<hkern g1="ampersand" 	g2="backslash" 	k="205" />
<hkern g1="ampersand" 	g2="bullet" 	k="41" />
<hkern g1="ampersand" 	g2="four" 	k="41" />
<hkern g1="ampersand" 	g2="nine" 	k="41" />
<hkern g1="ampersand" 	g2="one" 	k="131" />
<hkern g1="ampersand" 	g2="percent" 	k="82" />
<hkern g1="ampersand" 	g2="question" 	k="164" />
<hkern g1="ampersand" 	g2="seven" 	k="61" />
<hkern g1="ampersand" 	g2="slash" 	k="82" />
<hkern g1="ampersand" 	g2="trademark" 	k="123" />
<hkern g1="ampersand" 	g2="yen" 	k="102" />
<hkern g1="at" 	g2="asterisk,registered" 	k="61" />
<hkern g1="at" 	g2="backslash" 	k="102" />
<hkern g1="at" 	g2="nine" 	k="20" />
<hkern g1="at" 	g2="one" 	k="61" />
<hkern g1="at" 	g2="question" 	k="82" />
<hkern g1="at" 	g2="seven" 	k="61" />
<hkern g1="at" 	g2="trademark" 	k="61" />
<hkern g1="at" 	g2="yen" 	k="61" />
<hkern g1="at" 	g2="three" 	k="41" />
<hkern g1="at" 	g2="two" 	k="41" />
<hkern g1="at" 	g2="underscore" 	k="102" />
<hkern g1="at" 	g2="ampersand" 	k="41" />
<hkern g1="at" 	g2="guillemotright,guilsinglright" 	k="61" />
<hkern g1="copyright" 	g2="slash" 	k="102" />
<hkern g1="copyright" 	g2="underscore" 	k="205" />
<hkern g1="degree" 	g2="asterisk,registered" 	k="154" />
<hkern g1="degree" 	g2="backslash" 	k="195" />
<hkern g1="degree" 	g2="question" 	k="205" />
<hkern g1="degree" 	g2="slash" 	k="358" />
<hkern g1="degree" 	g2="underscore" 	k="205" />
<hkern g1="degree" 	g2="exclam" 	k="154" />
<hkern g1="less,equal,greater,multiply" 	g2="nine" 	k="20" />
<hkern g1="less,equal,greater,multiply" 	g2="one" 	k="41" />
<hkern g1="less,equal,greater,multiply" 	g2="question" 	k="82" />
<hkern g1="less,equal,greater,multiply" 	g2="yen" 	k="41" />
<hkern g1="less,equal,greater,multiply" 	g2="three" 	k="78" />
<hkern g1="Euro" 	g2="asterisk,registered" 	k="-20" />
<hkern g1="Euro" 	g2="at" 	k="41" />
<hkern g1="Euro" 	g2="bullet" 	k="82" />
<hkern g1="Euro" 	g2="four" 	k="78" />
<hkern g1="Euro" 	g2="nine" 	k="39" />
<hkern g1="Euro" 	g2="one" 	k="61" />
<hkern g1="Euro" 	g2="eight" 	k="31" />
<hkern g1="Euro" 	g2="Euro" 	k="41" />
<hkern g1="Euro" 	g2="two" 	k="20" />
<hkern g1="Euro" 	g2="ampersand" 	k="61" />
<hkern g1="Euro" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="Euro" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="Euro" 	g2="less,equal,greater,multiply" 	k="61" />
<hkern g1="Euro" 	g2="six" 	k="41" />
<hkern g1="Euro" 	g2="zero" 	k="41" />
<hkern g1="percent" 	g2="asterisk,registered" 	k="102" />
<hkern g1="percent" 	g2="backslash" 	k="123" />
<hkern g1="percent" 	g2="one" 	k="123" />
<hkern g1="percent" 	g2="question" 	k="123" />
<hkern g1="percent" 	g2="trademark" 	k="123" />
<hkern g1="percent" 	g2="yen" 	k="61" />
<hkern g1="section" 	g2="four" 	k="41" />
<hkern g1="section" 	g2="one" 	k="82" />
<hkern g1="section" 	g2="seven" 	k="61" />
<hkern g1="section" 	g2="two" 	k="20" />
<hkern g1="sterling" 	g2="at" 	k="41" />
<hkern g1="sterling" 	g2="backslash" 	k="102" />
<hkern g1="sterling" 	g2="bullet" 	k="82" />
<hkern g1="sterling" 	g2="four" 	k="78" />
<hkern g1="sterling" 	g2="nine" 	k="41" />
<hkern g1="sterling" 	g2="one" 	k="82" />
<hkern g1="sterling" 	g2="yen" 	k="20" />
<hkern g1="sterling" 	g2="eight" 	k="41" />
<hkern g1="sterling" 	g2="Euro" 	k="61" />
<hkern g1="sterling" 	g2="ampersand" 	k="61" />
<hkern g1="sterling" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="sterling" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="sterling" 	g2="less,equal,greater,multiply" 	k="41" />
<hkern g1="sterling" 	g2="six" 	k="61" />
<hkern g1="sterling" 	g2="zero" 	k="61" />
<hkern g1="trademark" 	g2="underscore" 	k="205" />
<hkern g1="yen" 	g2="asterisk,registered" 	k="-4" />
<hkern g1="yen" 	g2="at" 	k="61" />
<hkern g1="yen" 	g2="bullet" 	k="41" />
<hkern g1="yen" 	g2="four" 	k="41" />
<hkern g1="yen" 	g2="nine" 	k="31" />
<hkern g1="yen" 	g2="one" 	k="61" />
<hkern g1="yen" 	g2="question" 	k="20" />
<hkern g1="yen" 	g2="slash" 	k="82" />
<hkern g1="yen" 	g2="eight" 	k="20" />
<hkern g1="yen" 	g2="Euro" 	k="41" />
<hkern g1="yen" 	g2="sterling" 	k="61" />
<hkern g1="yen" 	g2="three" 	k="31" />
<hkern g1="yen" 	g2="two" 	k="31" />
<hkern g1="yen" 	g2="underscore" 	k="123" />
<hkern g1="yen" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-4" />
<hkern g1="yen" 	g2="ampersand" 	k="41" />
<hkern g1="yen" 	g2="guillemotright,guilsinglright" 	k="82" />
<hkern g1="yen" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="yen" 	g2="exclam" 	k="-41" />
<hkern g1="yen" 	g2="less,equal,greater,multiply" 	k="41" />
<hkern g1="yen" 	g2="six" 	k="61" />
<hkern g1="yen" 	g2="bracketright" 	k="-4" />
<hkern g1="asterisk,registered" 	g2="asterisk,registered" 	k="-4" />
<hkern g1="asterisk,registered" 	g2="at" 	k="61" />
<hkern g1="asterisk,registered" 	g2="exclam" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="four" 	k="61" />
<hkern g1="asterisk,registered" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="asterisk,registered" 	g2="nine" 	k="-4" />
<hkern g1="asterisk,registered" 	g2="numbersign" 	k="41" />
<hkern g1="asterisk,registered" 	g2="one" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="questiondown" 	k="164" />
<hkern g1="asterisk,registered" 	g2="seven" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="slash" 	k="164" />
<hkern g1="asterisk,registered" 	g2="sterling" 	k="41" />
<hkern g1="asterisk,registered" 	g2="three" 	k="-4" />
<hkern g1="asterisk,registered" 	g2="two" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="underscore" 	k="205" />
<hkern g1="asterisk,registered" 	g2="yen" 	k="-4" />
<hkern g1="asterisk,registered" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-8" />
<hkern g1="backslash" 	g2="asterisk,registered" 	k="164" />
<hkern g1="backslash" 	g2="at" 	k="61" />
<hkern g1="backslash" 	g2="four" 	k="41" />
<hkern g1="backslash" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="backslash" 	g2="nine" 	k="82" />
<hkern g1="backslash" 	g2="one" 	k="90" />
<hkern g1="backslash" 	g2="seven" 	k="82" />
<hkern g1="backslash" 	g2="three" 	k="61" />
<hkern g1="backslash" 	g2="yen" 	k="82" />
<hkern g1="backslash" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="143" />
<hkern g1="backslash" 	g2="ampersand" 	k="123" />
<hkern g1="backslash" 	g2="backslash" 	k="246" />
<hkern g1="backslash" 	g2="bullet" 	k="61" />
<hkern g1="backslash" 	g2="copyright" 	k="102" />
<hkern g1="backslash" 	g2="degree" 	k="195" />
<hkern g1="backslash" 	g2="Euro" 	k="41" />
<hkern g1="backslash" 	g2="percent" 	k="123" />
<hkern g1="backslash" 	g2="question" 	k="164" />
<hkern g1="backslash" 	g2="six" 	k="41" />
<hkern g1="backslash" 	g2="trademark" 	k="164" />
<hkern g1="braceleft" 	g2="questiondown" 	k="-4" />
<hkern g1="braceleft" 	g2="underscore" 	k="-8" />
<hkern g1="bracketleft" 	g2="four" 	k="20" />
<hkern g1="bracketleft" 	g2="one" 	k="49" />
<hkern g1="bracketleft" 	g2="questiondown" 	k="-4" />
<hkern g1="bracketleft" 	g2="underscore" 	k="-8" />
<hkern g1="bracketleft" 	g2="yen" 	k="-4" />
<hkern g1="bullet" 	g2="one" 	k="131" />
<hkern g1="bullet" 	g2="questiondown" 	k="123" />
<hkern g1="bullet" 	g2="seven" 	k="123" />
<hkern g1="bullet" 	g2="slash" 	k="61" />
<hkern g1="bullet" 	g2="three" 	k="102" />
<hkern g1="bullet" 	g2="two" 	k="113" />
<hkern g1="bullet" 	g2="yen" 	k="41" />
<hkern g1="bullet" 	g2="ampersand" 	k="41" />
<hkern g1="bullet" 	g2="backslash" 	k="61" />
<hkern g1="bullet" 	g2="question" 	k="82" />
<hkern g1="bullet" 	g2="trademark" 	k="61" />
<hkern g1="bullet" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="exclam" 	g2="asterisk,registered" 	k="-8" />
<hkern g1="exclam" 	g2="question" 	k="41" />
<hkern g1="exclamdown" 	g2="one" 	k="61" />
<hkern g1="exclamdown" 	g2="seven" 	k="61" />
<hkern g1="exclamdown" 	g2="backslash" 	k="102" />
<hkern g1="exclamdown" 	g2="question" 	k="61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="at" 	k="61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="four" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="guillemotleft,guilsinglleft" 	k="33" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="one" 	k="72" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="seven" 	k="72" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="three" 	k="27" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="two" 	k="37" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="yen" 	k="82" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="backslash" 	k="82" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="bullet" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="trademark" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="asterisk,registered" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="nine" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="one" 	k="113" />
<hkern g1="guillemotright,guilsinglright" 	g2="questiondown" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="seven" 	k="113" />
<hkern g1="guillemotright,guilsinglright" 	g2="slash" 	k="61" />
<hkern g1="guillemotright,guilsinglright" 	g2="three" 	k="98" />
<hkern g1="guillemotright,guilsinglright" 	g2="two" 	k="98" />
<hkern g1="guillemotright,guilsinglright" 	g2="underscore" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="yen" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="backslash" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="question" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="trademark" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="guillemotright,guilsinglright" 	k="276" />
<hkern g1="numbersign" 	g2="asterisk,registered" 	k="-41" />
<hkern g1="numbersign" 	g2="seven" 	k="-4" />
<hkern g1="numbersign" 	g2="three" 	k="25" />
<hkern g1="numbersign" 	g2="two" 	k="25" />
<hkern g1="numbersign" 	g2="underscore" 	k="164" />
<hkern g1="numbersign" 	g2="ampersand" 	k="57" />
<hkern g1="numbersign" 	g2="backslash" 	k="41" />
<hkern g1="numbersign" 	g2="bullet" 	k="41" />
<hkern g1="numbersign" 	g2="question" 	k="41" />
<hkern g1="parenleft" 	g2="four" 	k="20" />
<hkern g1="parenleft" 	g2="one" 	k="41" />
<hkern g1="parenleft" 	g2="questiondown" 	k="-8" />
<hkern g1="parenleft" 	g2="underscore" 	k="-8" />
<hkern g1="question" 	g2="asterisk,registered" 	k="-8" />
<hkern g1="question" 	g2="four" 	k="31" />
<hkern g1="question" 	g2="guillemotleft,guilsinglleft" 	k="57" />
<hkern g1="question" 	g2="numbersign" 	k="61" />
<hkern g1="question" 	g2="questiondown" 	k="205" />
<hkern g1="question" 	g2="slash" 	k="164" />
<hkern g1="question" 	g2="underscore" 	k="205" />
<hkern g1="question" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-8" />
<hkern g1="question" 	g2="ampersand" 	k="82" />
<hkern g1="question" 	g2="question" 	k="90" />
<hkern g1="question" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="questiondown" 	g2="asterisk,registered" 	k="82" />
<hkern g1="questiondown" 	g2="at" 	k="61" />
<hkern g1="questiondown" 	g2="exclam" 	k="61" />
<hkern g1="questiondown" 	g2="four" 	k="102" />
<hkern g1="questiondown" 	g2="guillemotleft,guilsinglleft" 	k="113" />
<hkern g1="questiondown" 	g2="nine" 	k="41" />
<hkern g1="questiondown" 	g2="numbersign" 	k="41" />
<hkern g1="questiondown" 	g2="one" 	k="164" />
<hkern g1="questiondown" 	g2="questiondown" 	k="90" />
<hkern g1="questiondown" 	g2="seven" 	k="82" />
<hkern g1="questiondown" 	g2="three" 	k="41" />
<hkern g1="questiondown" 	g2="two" 	k="41" />
<hkern g1="questiondown" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="123" />
<hkern g1="questiondown" 	g2="ampersand" 	k="61" />
<hkern g1="questiondown" 	g2="backslash" 	k="205" />
<hkern g1="questiondown" 	g2="bullet" 	k="61" />
<hkern g1="questiondown" 	g2="Euro" 	k="82" />
<hkern g1="questiondown" 	g2="percent" 	k="82" />
<hkern g1="questiondown" 	g2="question" 	k="205" />
<hkern g1="questiondown" 	g2="six" 	k="41" />
<hkern g1="questiondown" 	g2="trademark" 	k="102" />
<hkern g1="questiondown" 	g2="guillemotright,guilsinglright" 	k="49" />
<hkern g1="questiondown" 	g2="eight" 	k="41" />
<hkern g1="questiondown" 	g2="exclamdown" 	k="41" />
<hkern g1="questiondown" 	g2="zero" 	k="41" />
<hkern g1="slash" 	g2="at" 	k="102" />
<hkern g1="slash" 	g2="four" 	k="102" />
<hkern g1="slash" 	g2="guillemotleft,guilsinglleft" 	k="123" />
<hkern g1="slash" 	g2="nine" 	k="82" />
<hkern g1="slash" 	g2="numbersign" 	k="41" />
<hkern g1="slash" 	g2="one" 	k="123" />
<hkern g1="slash" 	g2="questiondown" 	k="205" />
<hkern g1="slash" 	g2="slash" 	k="246" />
<hkern g1="slash" 	g2="sterling" 	k="102" />
<hkern g1="slash" 	g2="three" 	k="61" />
<hkern g1="slash" 	g2="two" 	k="82" />
<hkern g1="slash" 	g2="underscore" 	k="246" />
<hkern g1="slash" 	g2="ampersand" 	k="102" />
<hkern g1="slash" 	g2="bullet" 	k="61" />
<hkern g1="slash" 	g2="Euro" 	k="61" />
<hkern g1="slash" 	g2="percent" 	k="41" />
<hkern g1="slash" 	g2="question" 	k="123" />
<hkern g1="slash" 	g2="six" 	k="61" />
<hkern g1="slash" 	g2="guillemotright,guilsinglright" 	k="82" />
<hkern g1="slash" 	g2="eight" 	k="51" />
<hkern g1="slash" 	g2="zero" 	k="20" />
<hkern g1="underscore" 	g2="asterisk,registered" 	k="205" />
<hkern g1="underscore" 	g2="at" 	k="61" />
<hkern g1="underscore" 	g2="four" 	k="246" />
<hkern g1="underscore" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="underscore" 	g2="one" 	k="205" />
<hkern g1="underscore" 	g2="questiondown" 	k="-4" />
<hkern g1="underscore" 	g2="underscore" 	k="20" />
<hkern g1="underscore" 	g2="yen" 	k="123" />
<hkern g1="underscore" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="164" />
<hkern g1="underscore" 	g2="ampersand" 	k="61" />
<hkern g1="underscore" 	g2="backslash" 	k="246" />
<hkern g1="underscore" 	g2="copyright" 	k="205" />
<hkern g1="underscore" 	g2="Euro" 	k="164" />
<hkern g1="underscore" 	g2="percent" 	k="164" />
<hkern g1="underscore" 	g2="question" 	k="164" />
<hkern g1="underscore" 	g2="six" 	k="61" />
<hkern g1="underscore" 	g2="trademark" 	k="205" />
<hkern g1="underscore" 	g2="eight" 	k="41" />
<hkern g1="underscore" 	g2="zero" 	k="61" />
<hkern g1="underscore" 	g2="braceright" 	k="-8" />
<hkern g1="underscore" 	g2="bracketright" 	k="-8" />
<hkern g1="underscore" 	g2="parenright" 	k="-8" />
<hkern g1="eight" 	g2="backslash" 	k="51" />
<hkern g1="eight" 	g2="Euro" 	k="10" />
<hkern g1="eight" 	g2="nine" 	k="39" />
<hkern g1="eight" 	g2="one" 	k="61" />
<hkern g1="eight" 	g2="question" 	k="41" />
<hkern g1="eight" 	g2="seven" 	k="31" />
<hkern g1="eight" 	g2="trademark" 	k="41" />
<hkern g1="eight" 	g2="two" 	k="20" />
<hkern g1="eight" 	g2="underscore" 	k="41" />
<hkern g1="eight" 	g2="yen" 	k="20" />
<hkern g1="four" 	g2="backslash" 	k="102" />
<hkern g1="four" 	g2="nine" 	k="41" />
<hkern g1="four" 	g2="one" 	k="98" />
<hkern g1="four" 	g2="question" 	k="102" />
<hkern g1="four" 	g2="seven" 	k="45" />
<hkern g1="four" 	g2="trademark" 	k="82" />
<hkern g1="four" 	g2="two" 	k="41" />
<hkern g1="four" 	g2="yen" 	k="41" />
<hkern g1="four" 	g2="percent" 	k="82" />
<hkern g1="four" 	g2="asterisk,registered" 	k="82" />
<hkern g1="four" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="four" 	g2="slash" 	k="41" />
<hkern g1="four" 	g2="three" 	k="39" />
<hkern g1="nine" 	g2="backslash" 	k="82" />
<hkern g1="nine" 	g2="one" 	k="41" />
<hkern g1="nine" 	g2="question" 	k="41" />
<hkern g1="nine" 	g2="seven" 	k="37" />
<hkern g1="nine" 	g2="trademark" 	k="20" />
<hkern g1="nine" 	g2="two" 	k="49" />
<hkern g1="nine" 	g2="underscore" 	k="205" />
<hkern g1="nine" 	g2="yen" 	k="20" />
<hkern g1="nine" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-20" />
<hkern g1="nine" 	g2="slash" 	k="82" />
<hkern g1="nine" 	g2="three" 	k="41" />
<hkern g1="nine" 	g2="four" 	k="20" />
<hkern g1="one" 	g2="one" 	k="25" />
<hkern g1="seven" 	g2="Euro" 	k="72" />
<hkern g1="seven" 	g2="nine" 	k="45" />
<hkern g1="seven" 	g2="one" 	k="45" />
<hkern g1="seven" 	g2="trademark" 	k="-41" />
<hkern g1="seven" 	g2="two" 	k="41" />
<hkern g1="seven" 	g2="underscore" 	k="205" />
<hkern g1="seven" 	g2="bullet" 	k="57" />
<hkern g1="seven" 	g2="degree" 	k="-20" />
<hkern g1="seven" 	g2="eight" 	k="47" />
<hkern g1="seven" 	g2="guillemotleft,guilsinglleft" 	k="113" />
<hkern g1="seven" 	g2="percent" 	k="-20" />
<hkern g1="seven" 	g2="six" 	k="55" />
<hkern g1="seven" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-8" />
<hkern g1="seven" 	g2="asterisk,registered" 	k="-8" />
<hkern g1="seven" 	g2="guillemotright,guilsinglright" 	k="72" />
<hkern g1="seven" 	g2="slash" 	k="82" />
<hkern g1="seven" 	g2="three" 	k="27" />
<hkern g1="seven" 	g2="exclam" 	k="-41" />
<hkern g1="seven" 	g2="four" 	k="82" />
<hkern g1="seven" 	g2="ampersand" 	k="41" />
<hkern g1="seven" 	g2="at" 	k="41" />
<hkern g1="seven" 	g2="numbersign" 	k="41" />
<hkern g1="seven" 	g2="sterling" 	k="61" />
<hkern g1="seven" 	g2="zero" 	k="29" />
<hkern g1="six" 	g2="backslash" 	k="61" />
<hkern g1="six" 	g2="nine" 	k="31" />
<hkern g1="six" 	g2="one" 	k="90" />
<hkern g1="six" 	g2="question" 	k="61" />
<hkern g1="six" 	g2="seven" 	k="51" />
<hkern g1="six" 	g2="trademark" 	k="61" />
<hkern g1="six" 	g2="two" 	k="23" />
<hkern g1="six" 	g2="underscore" 	k="41" />
<hkern g1="six" 	g2="yen" 	k="61" />
<hkern g1="six" 	g2="degree" 	k="20" />
<hkern g1="six" 	g2="asterisk,registered" 	k="41" />
<hkern g1="six" 	g2="slash" 	k="41" />
<hkern g1="six" 	g2="three" 	k="20" />
<hkern g1="three" 	g2="backslash" 	k="41" />
<hkern g1="three" 	g2="nine" 	k="29" />
<hkern g1="three" 	g2="one" 	k="57" />
<hkern g1="three" 	g2="question" 	k="20" />
<hkern g1="three" 	g2="seven" 	k="10" />
<hkern g1="three" 	g2="two" 	k="20" />
<hkern g1="three" 	g2="underscore" 	k="41" />
<hkern g1="three" 	g2="yen" 	k="23" />
<hkern g1="three" 	g2="slash" 	k="41" />
<hkern g1="three" 	g2="four" 	k="16" />
<hkern g1="two" 	g2="backslash" 	k="41" />
<hkern g1="two" 	g2="Euro" 	k="31" />
<hkern g1="two" 	g2="nine" 	k="20" />
<hkern g1="two" 	g2="one" 	k="57" />
<hkern g1="two" 	g2="question" 	k="20" />
<hkern g1="two" 	g2="seven" 	k="20" />
<hkern g1="two" 	g2="yen" 	k="31" />
<hkern g1="two" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="two" 	g2="three" 	k="10" />
<hkern g1="two" 	g2="four" 	k="53" />
<hkern g1="two" 	g2="sterling" 	k="20" />
<hkern g1="zero" 	g2="backslash" 	k="20" />
<hkern g1="zero" 	g2="nine" 	k="20" />
<hkern g1="zero" 	g2="one" 	k="57" />
<hkern g1="zero" 	g2="question" 	k="41" />
<hkern g1="zero" 	g2="seven" 	k="41" />
<hkern g1="zero" 	g2="trademark" 	k="41" />
<hkern g1="zero" 	g2="two" 	k="29" />
<hkern g1="zero" 	g2="underscore" 	k="61" />
<hkern g1="zero" 	g2="three" 	k="20" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="backslash" 	k="164" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="Euro" 	k="-4" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="one" 	k="123" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="question" 	k="82" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="seven" 	k="41" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="trademark" 	k="41" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="underscore" 	k="41" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="yen" 	k="31" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="eight" 	k="-4" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="percent" 	k="61" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="asterisk,registered" 	k="61" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="three" 	k="-8" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="four" 	k="-8" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="nine" 	k="-4" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="one" 	k="-4" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="seven" 	k="-4" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="two" 	k="-4" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="underscore" 	k="164" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="yen" 	k="-4" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="asterisk,registered" 	k="-8" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="slash" 	k="123" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="questiondown" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="90" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk,registered" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="72" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="braceright" 	k="-8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="bracketright" 	k="-8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="bullet" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="colon,semicolon" 	k="-4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="exclam" 	k="-2" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="four" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="nine" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="one" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="seven" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="two" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="39" />
<hkern g1="B" 	g2="T" 	k="41" />
<hkern g1="B" 	g2="V" 	k="31" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="45" />
<hkern g1="B" 	g2="asterisk,registered" 	k="20" />
<hkern g1="B" 	g2="backslash" 	k="41" />
<hkern g1="B" 	g2="four" 	k="20" />
<hkern g1="B" 	g2="nine" 	k="41" />
<hkern g1="B" 	g2="one" 	k="82" />
<hkern g1="B" 	g2="question" 	k="41" />
<hkern g1="B" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="B" 	g2="seven" 	k="61" />
<hkern g1="B" 	g2="trademark" 	k="41" />
<hkern g1="B" 	g2="two" 	k="31" />
<hkern g1="B" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="B" 	g2="AE" 	k="20" />
<hkern g1="B" 	g2="J" 	k="27" />
<hkern g1="B" 	g2="dollar,S" 	k="41" />
<hkern g1="B" 	g2="W" 	k="4" />
<hkern g1="B" 	g2="X" 	k="10" />
<hkern g1="B" 	g2="Z" 	k="31" />
<hkern g1="B" 	g2="five" 	k="20" />
<hkern g1="B" 	g2="underscore" 	k="41" />
<hkern g1="B" 	g2="x" 	k="6" />
<hkern g1="C,Ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="33" />
<hkern g1="C,Ccedilla" 	g2="backslash" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="bullet" 	k="123" />
<hkern g1="C,Ccedilla" 	g2="four" 	k="113" />
<hkern g1="C,Ccedilla" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="164" />
<hkern g1="C,Ccedilla" 	g2="nine" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="one" 	k="106" />
<hkern g1="C,Ccedilla" 	g2="seven" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="8" />
<hkern g1="C,Ccedilla" 	g2="dollar,S" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="five" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="x" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="v,y,yacute,ydieresis" 	k="102" />
<hkern g1="C,Ccedilla" 	g2="M" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="ampersand" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="at" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="eight" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="less,equal,greater,multiply" 	k="119" />
<hkern g1="C,Ccedilla" 	g2="f,uniFB01,uniFB02" 	k="49" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="C,Ccedilla" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="j" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="l" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="m,n,p,r,mu,ntilde" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="s" 	k="35" />
<hkern g1="C,Ccedilla" 	g2="six" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="39" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="z" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="zero" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="asterisk,registered" 	k="-4" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="backslash" 	k="61" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="bullet" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="four" 	k="72" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="nine" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="one" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="J" 	k="6" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="dollar,S" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Z" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v,y,yacute,ydieresis" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="at" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="eight" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="l" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="m,n,p,r,mu,ntilde" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="six" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="zero" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-4" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="33" />
<hkern g1="F" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="F" 	g2="bracketright" 	k="-4" />
<hkern g1="F" 	g2="colon,semicolon" 	k="41" />
<hkern g1="F" 	g2="exclam" 	k="-2" />
<hkern g1="F" 	g2="four" 	k="82" />
<hkern g1="F" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="F" 	g2="nine" 	k="41" />
<hkern g1="F" 	g2="one" 	k="61" />
<hkern g1="F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="F" 	g2="two" 	k="20" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="F" 	g2="AE" 	k="143" />
<hkern g1="F" 	g2="J" 	k="14" />
<hkern g1="F" 	g2="dollar,S" 	k="41" />
<hkern g1="F" 	g2="Z" 	k="41" />
<hkern g1="F" 	g2="five" 	k="41" />
<hkern g1="F" 	g2="underscore" 	k="205" />
<hkern g1="F" 	g2="x" 	k="123" />
<hkern g1="F" 	g2="slash" 	k="102" />
<hkern g1="F" 	g2="v,y,yacute,ydieresis" 	k="72" />
<hkern g1="F" 	g2="M" 	k="29" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="123" />
<hkern g1="F" 	g2="ampersand" 	k="61" />
<hkern g1="F" 	g2="at" 	k="41" />
<hkern g1="F" 	g2="eight" 	k="51" />
<hkern g1="F" 	g2="less,equal,greater,multiply" 	k="51" />
<hkern g1="F" 	g2="f,uniFB01,uniFB02" 	k="82" />
<hkern g1="F" 	g2="g" 	k="92" />
<hkern g1="F" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="82" />
<hkern g1="F" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="F" 	g2="j" 	k="70" />
<hkern g1="F" 	g2="l" 	k="20" />
<hkern g1="F" 	g2="m,n,p,r,mu,ntilde" 	k="72" />
<hkern g1="F" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="72" />
<hkern g1="F" 	g2="s" 	k="92" />
<hkern g1="F" 	g2="six" 	k="31" />
<hkern g1="F" 	g2="t" 	k="25" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="80" />
<hkern g1="F" 	g2="w" 	k="51" />
<hkern g1="F" 	g2="z" 	k="102" />
<hkern g1="F" 	g2="zero" 	k="20" />
<hkern g1="F" 	g2="i" 	k="72" />
<hkern g1="F" 	g2="numbersign" 	k="61" />
<hkern g1="G" 	g2="T" 	k="35" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="45" />
<hkern g1="G" 	g2="backslash" 	k="31" />
<hkern g1="G" 	g2="nine" 	k="20" />
<hkern g1="G" 	g2="one" 	k="49" />
<hkern g1="G" 	g2="question" 	k="20" />
<hkern g1="G" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="G" 	g2="seven" 	k="33" />
<hkern g1="G" 	g2="trademark" 	k="20" />
<hkern g1="G" 	g2="two" 	k="20" />
<hkern g1="G" 	g2="J" 	k="4" />
<hkern g1="G" 	g2="X" 	k="4" />
<hkern g1="G" 	g2="Z" 	k="10" />
<hkern g1="H,I,N,Igrave,Ntilde" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-8" />
<hkern g1="Iacute,Icircumflex,Idieresis" 	g2="backslash" 	k="-8" />
<hkern g1="Iacute,Icircumflex,Idieresis" 	g2="braceright" 	k="-45" />
<hkern g1="Iacute,Icircumflex,Idieresis" 	g2="bracketright" 	k="-45" />
<hkern g1="Iacute,Icircumflex,Idieresis" 	g2="parenright" 	k="-61" />
<hkern g1="J" 	g2="underscore" 	k="41" />
<hkern g1="J" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-8" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="98" />
<hkern g1="L" 	g2="T" 	k="156" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="51" />
<hkern g1="L" 	g2="V" 	k="139" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="184" />
<hkern g1="L" 	g2="asterisk,registered" 	k="164" />
<hkern g1="L" 	g2="backslash" 	k="213" />
<hkern g1="L" 	g2="bullet" 	k="164" />
<hkern g1="L" 	g2="four" 	k="104" />
<hkern g1="L" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="205" />
<hkern g1="L" 	g2="nine" 	k="123" />
<hkern g1="L" 	g2="one" 	k="246" />
<hkern g1="L" 	g2="question" 	k="164" />
<hkern g1="L" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="205" />
<hkern g1="L" 	g2="seven" 	k="53" />
<hkern g1="L" 	g2="trademark" 	k="205" />
<hkern g1="L" 	g2="two" 	k="37" />
<hkern g1="L" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="205" />
<hkern g1="L" 	g2="J" 	k="35" />
<hkern g1="L" 	g2="dollar,S" 	k="37" />
<hkern g1="L" 	g2="W" 	k="82" />
<hkern g1="L" 	g2="five" 	k="20" />
<hkern g1="L" 	g2="slash" 	k="41" />
<hkern g1="L" 	g2="v,y,yacute,ydieresis" 	k="131" />
<hkern g1="L" 	g2="M" 	k="20" />
<hkern g1="L" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="L" 	g2="ampersand" 	k="41" />
<hkern g1="L" 	g2="eight" 	k="20" />
<hkern g1="L" 	g2="less,equal,greater,multiply" 	k="61" />
<hkern g1="L" 	g2="f,uniFB01,uniFB02" 	k="49" />
<hkern g1="L" 	g2="g" 	k="51" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="123" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="L" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="L" 	g2="j" 	k="31" />
<hkern g1="L" 	g2="l" 	k="29" />
<hkern g1="L" 	g2="m,n,p,r,mu,ntilde" 	k="20" />
<hkern g1="L" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="L" 	g2="s" 	k="37" />
<hkern g1="L" 	g2="six" 	k="61" />
<hkern g1="L" 	g2="t" 	k="45" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="L" 	g2="w" 	k="86" />
<hkern g1="L" 	g2="zero" 	k="61" />
<hkern g1="L" 	g2="igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="L" 	g2="copyright" 	k="164" />
<hkern g1="M" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="M" 	g2="T" 	k="25" />
<hkern g1="M" 	g2="V" 	k="4" />
<hkern g1="M" 	g2="Y,Yacute,Ydieresis" 	k="27" />
<hkern g1="M" 	g2="four" 	k="20" />
<hkern g1="M" 	g2="nine" 	k="10" />
<hkern g1="M" 	g2="one" 	k="25" />
<hkern g1="M" 	g2="question" 	k="20" />
<hkern g1="M" 	g2="seven" 	k="20" />
<hkern g1="M" 	g2="trademark" 	k="41" />
<hkern g1="M" 	g2="J" 	k="4" />
<hkern g1="M" 	g2="dollar,S" 	k="31" />
<hkern g1="M" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="72" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="57" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="one" 	k="23" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="seven" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="two" 	k="51" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="8" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="25" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="51" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="underscore" 	k="82" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="three" 	k="41" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="M" 	k="12" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="20" />
<hkern g1="P" 	g2="T" 	k="51" />
<hkern g1="P" 	g2="V" 	k="41" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="74" />
<hkern g1="P" 	g2="backslash" 	k="102" />
<hkern g1="P" 	g2="colon,semicolon" 	k="10" />
<hkern g1="P" 	g2="four" 	k="98" />
<hkern g1="P" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="37" />
<hkern g1="P" 	g2="nine" 	k="10" />
<hkern g1="P" 	g2="one" 	k="25" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="246" />
<hkern g1="P" 	g2="question" 	k="82" />
<hkern g1="P" 	g2="seven" 	k="41" />
<hkern g1="P" 	g2="trademark" 	k="20" />
<hkern g1="P" 	g2="two" 	k="57" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="123" />
<hkern g1="P" 	g2="AE" 	k="152" />
<hkern g1="P" 	g2="J" 	k="49" />
<hkern g1="P" 	g2="dollar,S" 	k="47" />
<hkern g1="P" 	g2="W" 	k="20" />
<hkern g1="P" 	g2="X" 	k="57" />
<hkern g1="P" 	g2="Z" 	k="80" />
<hkern g1="P" 	g2="five" 	k="31" />
<hkern g1="P" 	g2="underscore" 	k="205" />
<hkern g1="P" 	g2="x" 	k="31" />
<hkern g1="P" 	g2="slash" 	k="102" />
<hkern g1="P" 	g2="three" 	k="41" />
<hkern g1="P" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="P" 	g2="M" 	k="61" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="119" />
<hkern g1="P" 	g2="ampersand" 	k="70" />
<hkern g1="P" 	g2="at" 	k="61" />
<hkern g1="P" 	g2="eight" 	k="41" />
<hkern g1="P" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="P" 	g2="g" 	k="59" />
<hkern g1="P" 	g2="b,h,k,germandbls,thorn" 	k="41" />
<hkern g1="P" 	g2="j" 	k="41" />
<hkern g1="P" 	g2="l" 	k="20" />
<hkern g1="P" 	g2="m,n,p,r,mu,ntilde" 	k="41" />
<hkern g1="P" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="P" 	g2="s" 	k="76" />
<hkern g1="P" 	g2="six" 	k="20" />
<hkern g1="P" 	g2="t" 	k="10" />
<hkern g1="P" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="59" />
<hkern g1="P" 	g2="w" 	k="10" />
<hkern g1="P" 	g2="z" 	k="61" />
<hkern g1="P" 	g2="numbersign" 	k="61" />
<hkern g1="R" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="R" 	g2="T" 	k="41" />
<hkern g1="R" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="12" />
<hkern g1="R" 	g2="V" 	k="12" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="47" />
<hkern g1="R" 	g2="backslash" 	k="41" />
<hkern g1="R" 	g2="braceright" 	k="-4" />
<hkern g1="R" 	g2="bracketright" 	k="-8" />
<hkern g1="R" 	g2="bullet" 	k="41" />
<hkern g1="R" 	g2="four" 	k="41" />
<hkern g1="R" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="45" />
<hkern g1="R" 	g2="nine" 	k="10" />
<hkern g1="R" 	g2="one" 	k="25" />
<hkern g1="R" 	g2="question" 	k="41" />
<hkern g1="R" 	g2="seven" 	k="31" />
<hkern g1="R" 	g2="trademark" 	k="20" />
<hkern g1="R" 	g2="J" 	k="20" />
<hkern g1="R" 	g2="dollar,S" 	k="12" />
<hkern g1="R" 	g2="Z" 	k="12" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="R" 	g2="ampersand" 	k="20" />
<hkern g1="R" 	g2="g" 	k="20" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="R" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="dollar,S" 	g2="T" 	k="49" />
<hkern g1="dollar,S" 	g2="V" 	k="41" />
<hkern g1="dollar,S" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="dollar,S" 	g2="backslash" 	k="61" />
<hkern g1="dollar,S" 	g2="nine" 	k="39" />
<hkern g1="dollar,S" 	g2="one" 	k="49" />
<hkern g1="dollar,S" 	g2="question" 	k="41" />
<hkern g1="dollar,S" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="dollar,S" 	g2="seven" 	k="41" />
<hkern g1="dollar,S" 	g2="trademark" 	k="31" />
<hkern g1="dollar,S" 	g2="two" 	k="20" />
<hkern g1="dollar,S" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="41" />
<hkern g1="dollar,S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="dollar,S" 	g2="AE" 	k="20" />
<hkern g1="dollar,S" 	g2="J" 	k="4" />
<hkern g1="dollar,S" 	g2="dollar,S" 	k="29" />
<hkern g1="dollar,S" 	g2="W" 	k="20" />
<hkern g1="dollar,S" 	g2="X" 	k="12" />
<hkern g1="dollar,S" 	g2="Z" 	k="31" />
<hkern g1="dollar,S" 	g2="underscore" 	k="61" />
<hkern g1="dollar,S" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="dollar,S" 	g2="M" 	k="20" />
<hkern g1="dollar,S" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="dollar,S" 	g2="z" 	k="20" />
<hkern g1="dollar,S" 	g2="yen" 	k="20" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="72" />
<hkern g1="T" 	g2="asterisk,registered" 	k="-4" />
<hkern g1="T" 	g2="backslash" 	k="61" />
<hkern g1="T" 	g2="bracketright" 	k="-8" />
<hkern g1="T" 	g2="bullet" 	k="102" />
<hkern g1="T" 	g2="colon,semicolon" 	k="82" />
<hkern g1="T" 	g2="exclam" 	k="-4" />
<hkern g1="T" 	g2="four" 	k="102" />
<hkern g1="T" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="102" />
<hkern g1="T" 	g2="nine" 	k="41" />
<hkern g1="T" 	g2="one" 	k="41" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="123" />
<hkern g1="T" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="T" 	g2="AE" 	k="147" />
<hkern g1="T" 	g2="J" 	k="25" />
<hkern g1="T" 	g2="dollar,S" 	k="41" />
<hkern g1="T" 	g2="Z" 	k="41" />
<hkern g1="T" 	g2="five" 	k="41" />
<hkern g1="T" 	g2="underscore" 	k="102" />
<hkern g1="T" 	g2="x" 	k="66" />
<hkern g1="T" 	g2="slash" 	k="102" />
<hkern g1="T" 	g2="v,y,yacute,ydieresis" 	k="82" />
<hkern g1="T" 	g2="M" 	k="41" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="102" />
<hkern g1="T" 	g2="ampersand" 	k="82" />
<hkern g1="T" 	g2="at" 	k="102" />
<hkern g1="T" 	g2="eight" 	k="41" />
<hkern g1="T" 	g2="f,uniFB01,uniFB02" 	k="82" />
<hkern g1="T" 	g2="g" 	k="102" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="T" 	g2="j" 	k="61" />
<hkern g1="T" 	g2="l" 	k="20" />
<hkern g1="T" 	g2="m,n,p,r,mu,ntilde" 	k="61" />
<hkern g1="T" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="T" 	g2="s" 	k="102" />
<hkern g1="T" 	g2="six" 	k="82" />
<hkern g1="T" 	g2="t" 	k="82" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="82" />
<hkern g1="T" 	g2="w" 	k="61" />
<hkern g1="T" 	g2="z" 	k="82" />
<hkern g1="T" 	g2="zero" 	k="61" />
<hkern g1="T" 	g2="numbersign" 	k="102" />
<hkern g1="Thorn" 	g2="T" 	k="82" />
<hkern g1="Thorn" 	g2="V" 	k="45" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ydieresis" 	k="90" />
<hkern g1="Thorn" 	g2="backslash" 	k="82" />
<hkern g1="Thorn" 	g2="four" 	k="37" />
<hkern g1="Thorn" 	g2="nine" 	k="20" />
<hkern g1="Thorn" 	g2="one" 	k="66" />
<hkern g1="Thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="Thorn" 	g2="question" 	k="66" />
<hkern g1="Thorn" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="Thorn" 	g2="seven" 	k="102" />
<hkern g1="Thorn" 	g2="trademark" 	k="61" />
<hkern g1="Thorn" 	g2="two" 	k="61" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="Thorn" 	g2="AE" 	k="102" />
<hkern g1="Thorn" 	g2="J" 	k="29" />
<hkern g1="Thorn" 	g2="dollar,S" 	k="41" />
<hkern g1="Thorn" 	g2="W" 	k="25" />
<hkern g1="Thorn" 	g2="X" 	k="57" />
<hkern g1="Thorn" 	g2="Z" 	k="68" />
<hkern g1="Thorn" 	g2="five" 	k="20" />
<hkern g1="Thorn" 	g2="underscore" 	k="205" />
<hkern g1="Thorn" 	g2="x" 	k="20" />
<hkern g1="Thorn" 	g2="slash" 	k="82" />
<hkern g1="Thorn" 	g2="three" 	k="82" />
<hkern g1="Thorn" 	g2="M" 	k="25" />
<hkern g1="Thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="Thorn" 	g2="ampersand" 	k="41" />
<hkern g1="Thorn" 	g2="eight" 	k="31" />
<hkern g1="Thorn" 	g2="g" 	k="20" />
<hkern g1="Thorn" 	g2="m,n,p,r,mu,ntilde" 	k="41" />
<hkern g1="Thorn" 	g2="s" 	k="47" />
<hkern g1="Thorn" 	g2="z" 	k="41" />
<hkern g1="Thorn" 	g2="numbersign" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="4" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="dollar,S" 	k="18" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="X" 	k="4" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Z" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="underscore" 	k="61" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-8" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="V" 	g2="asterisk,registered" 	k="-4" />
<hkern g1="V" 	g2="bracketright" 	k="-12" />
<hkern g1="V" 	g2="bullet" 	k="20" />
<hkern g1="V" 	g2="colon,semicolon" 	k="20" />
<hkern g1="V" 	g2="exclam" 	k="-20" />
<hkern g1="V" 	g2="four" 	k="41" />
<hkern g1="V" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="57" />
<hkern g1="V" 	g2="nine" 	k="10" />
<hkern g1="V" 	g2="one" 	k="41" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="V" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="V" 	g2="trademark" 	k="-20" />
<hkern g1="V" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-20" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="25" />
<hkern g1="V" 	g2="AE" 	k="49" />
<hkern g1="V" 	g2="J" 	k="4" />
<hkern g1="V" 	g2="dollar,S" 	k="31" />
<hkern g1="V" 	g2="Z" 	k="41" />
<hkern g1="V" 	g2="five" 	k="20" />
<hkern g1="V" 	g2="underscore" 	k="102" />
<hkern g1="V" 	g2="x" 	k="33" />
<hkern g1="V" 	g2="slash" 	k="72" />
<hkern g1="V" 	g2="v,y,yacute,ydieresis" 	k="27" />
<hkern g1="V" 	g2="M" 	k="12" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="V" 	g2="ampersand" 	k="41" />
<hkern g1="V" 	g2="at" 	k="31" />
<hkern g1="V" 	g2="eight" 	k="20" />
<hkern g1="V" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="V" 	g2="g" 	k="61" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="V" 	g2="j" 	k="18" />
<hkern g1="V" 	g2="m,n,p,r,mu,ntilde" 	k="20" />
<hkern g1="V" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="V" 	g2="s" 	k="41" />
<hkern g1="V" 	g2="six" 	k="10" />
<hkern g1="V" 	g2="t" 	k="20" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="V" 	g2="w" 	k="18" />
<hkern g1="V" 	g2="z" 	k="35" />
<hkern g1="V" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-20" />
<hkern g1="V" 	g2="numbersign" 	k="20" />
<hkern g1="W" 	g2="asterisk,registered" 	k="-4" />
<hkern g1="W" 	g2="bracketright" 	k="-4" />
<hkern g1="W" 	g2="four" 	k="20" />
<hkern g1="W" 	g2="one" 	k="41" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="W" 	g2="trademark" 	k="-20" />
<hkern g1="W" 	g2="J" 	k="4" />
<hkern g1="W" 	g2="dollar,S" 	k="20" />
<hkern g1="W" 	g2="Z" 	k="41" />
<hkern g1="W" 	g2="five" 	k="20" />
<hkern g1="W" 	g2="underscore" 	k="123" />
<hkern g1="W" 	g2="x" 	k="20" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="61" />
<hkern g1="W" 	g2="ampersand" 	k="20" />
<hkern g1="W" 	g2="at" 	k="20" />
<hkern g1="W" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="W" 	g2="g" 	k="20" />
<hkern g1="W" 	g2="m,n,p,r,mu,ntilde" 	k="10" />
<hkern g1="W" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="W" 	g2="s" 	k="20" />
<hkern g1="W" 	g2="t" 	k="10" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="W" 	g2="z" 	k="23" />
<hkern g1="W" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-25" />
<hkern g1="K,X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="K,X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="12" />
<hkern g1="K,X" 	g2="backslash" 	k="41" />
<hkern g1="K,X" 	g2="braceright" 	k="-4" />
<hkern g1="K,X" 	g2="bracketright" 	k="-8" />
<hkern g1="K,X" 	g2="bullet" 	k="45" />
<hkern g1="K,X" 	g2="four" 	k="61" />
<hkern g1="K,X" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="66" />
<hkern g1="K,X" 	g2="nine" 	k="20" />
<hkern g1="K,X" 	g2="one" 	k="70" />
<hkern g1="K,X" 	g2="question" 	k="61" />
<hkern g1="K,X" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="14" />
<hkern g1="K,X" 	g2="J" 	k="4" />
<hkern g1="K,X" 	g2="dollar,S" 	k="20" />
<hkern g1="K,X" 	g2="v,y,yacute,ydieresis" 	k="8" />
<hkern g1="K,X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="12" />
<hkern g1="K,X" 	g2="ampersand" 	k="20" />
<hkern g1="K,X" 	g2="at" 	k="61" />
<hkern g1="K,X" 	g2="eight" 	k="14" />
<hkern g1="K,X" 	g2="less,equal,greater,multiply" 	k="61" />
<hkern g1="K,X" 	g2="f,uniFB01,uniFB02" 	k="25" />
<hkern g1="K,X" 	g2="g" 	k="31" />
<hkern g1="K,X" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="K,X" 	g2="j" 	k="20" />
<hkern g1="K,X" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="K,X" 	g2="six" 	k="41" />
<hkern g1="K,X" 	g2="t" 	k="14" />
<hkern g1="K,X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="K,X" 	g2="w" 	k="4" />
<hkern g1="K,X" 	g2="zero" 	k="4" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bracketright" 	k="-8" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bullet" 	k="70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="106" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="four" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="106" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="nine" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="one" 	k="49" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="172" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="two" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="90" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="115" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="8" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="dollar,S" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="five" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="underscore" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="90" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="three" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v,y,yacute,ydieresis" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="M" 	k="27" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="152" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="98" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="eight" 	k="49" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,uniFB01,uniFB02" 	k="49" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="j" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="l" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,mu,ntilde" 	k="90" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="111" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="106" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="six" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="45" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="90" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="106" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="zero" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="numbersign" 	k="102" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="Z" 	g2="T" 	k="10" />
<hkern g1="Z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="Z" 	g2="asterisk,registered" 	k="-20" />
<hkern g1="Z" 	g2="backslash" 	k="82" />
<hkern g1="Z" 	g2="bullet" 	k="82" />
<hkern g1="Z" 	g2="four" 	k="61" />
<hkern g1="Z" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="119" />
<hkern g1="Z" 	g2="nine" 	k="47" />
<hkern g1="Z" 	g2="one" 	k="41" />
<hkern g1="Z" 	g2="two" 	k="20" />
<hkern g1="Z" 	g2="J" 	k="8" />
<hkern g1="Z" 	g2="dollar,S" 	k="31" />
<hkern g1="Z" 	g2="Z" 	k="18" />
<hkern g1="Z" 	g2="five" 	k="31" />
<hkern g1="Z" 	g2="x" 	k="41" />
<hkern g1="Z" 	g2="v,y,yacute,ydieresis" 	k="31" />
<hkern g1="Z" 	g2="M" 	k="20" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="37" />
<hkern g1="Z" 	g2="ampersand" 	k="41" />
<hkern g1="Z" 	g2="at" 	k="41" />
<hkern g1="Z" 	g2="eight" 	k="31" />
<hkern g1="Z" 	g2="less,equal,greater,multiply" 	k="72" />
<hkern g1="Z" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="Z" 	g2="g" 	k="51" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="113" />
<hkern g1="Z" 	g2="guillemotright,guilsinglright" 	k="72" />
<hkern g1="Z" 	g2="j" 	k="37" />
<hkern g1="Z" 	g2="l" 	k="31" />
<hkern g1="Z" 	g2="m,n,p,r,mu,ntilde" 	k="37" />
<hkern g1="Z" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="Z" 	g2="s" 	k="37" />
<hkern g1="Z" 	g2="six" 	k="51" />
<hkern g1="Z" 	g2="t" 	k="41" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="47" />
<hkern g1="Z" 	g2="w" 	k="20" />
<hkern g1="Z" 	g2="z" 	k="31" />
<hkern g1="Z" 	g2="zero" 	k="20" />
<hkern g1="Z" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-4" />
<hkern g1="Z" 	g2="numbersign" 	k="20" />
<hkern g1="five" 	g2="J" 	k="4" />
<hkern g1="five" 	g2="dollar,S" 	k="31" />
<hkern g1="five" 	g2="T" 	k="82" />
<hkern g1="five" 	g2="V" 	k="61" />
<hkern g1="five" 	g2="W" 	k="41" />
<hkern g1="five" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="five" 	g2="Z" 	k="31" />
<hkern g1="five" 	g2="asterisk,registered" 	k="41" />
<hkern g1="five" 	g2="backslash" 	k="61" />
<hkern g1="five" 	g2="degree" 	k="20" />
<hkern g1="five" 	g2="five" 	k="10" />
<hkern g1="five" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-8" />
<hkern g1="five" 	g2="nine" 	k="31" />
<hkern g1="five" 	g2="one" 	k="98" />
<hkern g1="five" 	g2="percent" 	k="41" />
<hkern g1="five" 	g2="question" 	k="41" />
<hkern g1="five" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="61" />
<hkern g1="five" 	g2="s" 	k="20" />
<hkern g1="five" 	g2="seven" 	k="45" />
<hkern g1="five" 	g2="slash" 	k="41" />
<hkern g1="five" 	g2="t" 	k="12" />
<hkern g1="five" 	g2="three" 	k="12" />
<hkern g1="five" 	g2="trademark" 	k="41" />
<hkern g1="five" 	g2="two" 	k="23" />
<hkern g1="five" 	g2="underscore" 	k="41" />
<hkern g1="five" 	g2="v,y,yacute,ydieresis" 	k="14" />
<hkern g1="five" 	g2="w" 	k="2" />
<hkern g1="five" 	g2="x" 	k="14" />
<hkern g1="five" 	g2="yen" 	k="51" />
<hkern g1="five" 	g2="z" 	k="12" />
<hkern g1="five" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="20" />
<hkern g1="j" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-25" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="asterisk,registered" 	k="20" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="backslash" 	k="102" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="five" 	k="20" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="nine" 	k="29" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="one" 	k="102" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="question" 	k="82" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="seven" 	k="82" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="three" 	k="41" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="trademark" 	k="61" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="two" 	k="41" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="underscore" 	k="41" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="v,y,yacute,ydieresis" 	k="4" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="x" 	k="14" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="z" 	k="12" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="10" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,oe" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="J" 	k="41" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="T" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="V" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="Z" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="asterisk,registered" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-74" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="s" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="seven" 	k="-8" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="slash" 	k="205" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="t" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="underscore" 	k="205" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="yen" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="z" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="205" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="AE" 	k="123" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="at" 	k="41" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="braceright" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="bracketright" 	k="-20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="eight" 	k="31" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="four" 	k="82" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="numbersign" 	k="61" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="questiondown" 	k="205" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="six" 	k="20" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="sterling" 	k="61" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="zero" 	k="20" />
<hkern g1="z" 	g2="backslash" 	k="61" />
<hkern g1="z" 	g2="one" 	k="20" />
<hkern g1="z" 	g2="question" 	k="61" />
<hkern g1="z" 	g2="s" 	k="12" />
<hkern g1="z" 	g2="seven" 	k="20" />
<hkern g1="z" 	g2="trademark" 	k="41" />
<hkern g1="z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="z" 	g2="at" 	k="20" />
<hkern g1="z" 	g2="four" 	k="57" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="z" 	g2="numbersign" 	k="4" />
<hkern g1="z" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="12" />
<hkern g1="z" 	g2="six" 	k="20" />
<hkern g1="z" 	g2="ampersand" 	k="41" />
<hkern g1="z" 	g2="bullet" 	k="10" />
<hkern g1="z" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="z" 	g2="g" 	k="20" />
<hkern g1="z" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="78" />
<hkern g1="z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="ampersand" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk,registered" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="backslash" 	k="82" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="g" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="27" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="l" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="one" 	k="102" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="61" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="seven" 	k="61" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="trademark" 	k="61" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v,y,yacute,ydieresis" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="j" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="s" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="ampersand" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="backslash" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="bullet" 	k="51" />
<hkern g1="c,cent,ccedilla" 	g2="eight" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="less,equal,greater,multiply" 	k="51" />
<hkern g1="c,cent,ccedilla" 	g2="four" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="g" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="96" />
<hkern g1="c,cent,ccedilla" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="c,cent,ccedilla" 	g2="one" 	k="35" />
<hkern g1="c,cent,ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="c,cent,ccedilla" 	g2="question" 	k="72" />
<hkern g1="c,cent,ccedilla" 	g2="seven" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="trademark" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="zero" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-20" />
<hkern g1="c,cent,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="at" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="colon,semicolon" 	k="-4" />
<hkern g1="c,cent,ccedilla" 	g2="yen" 	k="41" />
<hkern g1="d" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-12" />
<hkern g1="f" 	g2="ampersand" 	k="20" />
<hkern g1="f" 	g2="asterisk,registered" 	k="-102" />
<hkern g1="f" 	g2="backslash" 	k="-8" />
<hkern g1="f" 	g2="copyright" 	k="-61" />
<hkern g1="f" 	g2="eight" 	k="-10" />
<hkern g1="f" 	g2="four" 	k="35" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="f" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="41" />
<hkern g1="f" 	g2="nine" 	k="-41" />
<hkern g1="f" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="f" 	g2="one" 	k="-41" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="f" 	g2="question" 	k="-102" />
<hkern g1="f" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-143" />
<hkern g1="f" 	g2="seven" 	k="-123" />
<hkern g1="f" 	g2="trademark" 	k="-123" />
<hkern g1="f" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-61" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="43" />
<hkern g1="f" 	g2="braceright" 	k="-33" />
<hkern g1="f" 	g2="bracketright" 	k="-33" />
<hkern g1="f" 	g2="slash" 	k="45" />
<hkern g1="f" 	g2="s" 	k="10" />
<hkern g1="f" 	g2="two" 	k="-41" />
<hkern g1="f" 	g2="underscore" 	k="61" />
<hkern g1="f" 	g2="at" 	k="31" />
<hkern g1="f" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="f" 	g2="z" 	k="20" />
<hkern g1="f" 	g2="five" 	k="-41" />
<hkern g1="f" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-123" />
<hkern g1="f" 	g2="exclam" 	k="-78" />
<hkern g1="f" 	g2="parenright" 	k="-164" />
<hkern g1="f" 	g2="bar,brokenbar" 	k="-4" />
<hkern g1="f" 	g2="percent" 	k="-41" />
<hkern g1="f" 	g2="three" 	k="-45" />
<hkern g1="g" 	g2="backslash" 	k="41" />
<hkern g1="g" 	g2="four" 	k="31" />
<hkern g1="g" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="g" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="g" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="g" 	g2="question" 	k="41" />
<hkern g1="g" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="g" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="35" />
<hkern g1="g" 	g2="s" 	k="12" />
<hkern g1="g" 	g2="underscore" 	k="-4" />
<hkern g1="g" 	g2="z" 	k="4" />
<hkern g1="germandbls" 	g2="asterisk,registered" 	k="82" />
<hkern g1="germandbls" 	g2="backslash" 	k="61" />
<hkern g1="germandbls" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="germandbls" 	g2="g" 	k="20" />
<hkern g1="germandbls" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="germandbls" 	g2="nine" 	k="51" />
<hkern g1="germandbls" 	g2="one" 	k="102" />
<hkern g1="germandbls" 	g2="question" 	k="61" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="72" />
<hkern g1="germandbls" 	g2="seven" 	k="61" />
<hkern g1="germandbls" 	g2="t" 	k="20" />
<hkern g1="germandbls" 	g2="trademark" 	k="61" />
<hkern g1="germandbls" 	g2="v,y,yacute,ydieresis" 	k="23" />
<hkern g1="germandbls" 	g2="w" 	k="2" />
<hkern g1="germandbls" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="41" />
<hkern g1="germandbls" 	g2="underscore" 	k="41" />
<hkern g1="germandbls" 	g2="x" 	k="23" />
<hkern g1="i" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-25" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="asterisk,registered" 	k="-41" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="backslash" 	k="-20" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="l" 	k="-8" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="question" 	k="-12" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-25" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="seven" 	k="-33" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="trademark" 	k="-74" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-12" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="braceright" 	k="-66" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="bracketright" 	k="-66" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="j" 	k="-25" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="five" 	k="-8" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="exclam" 	k="-45" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="parenright" 	k="-66" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="b,h,k,germandbls,thorn" 	k="-12" />
<hkern g1="igrave,iacute,icircumflex,idieresis" 	g2="i" 	k="-25" />
<hkern g1="l,uniFB02" 	g2="asterisk,registered" 	k="20" />
<hkern g1="l,uniFB02" 	g2="backslash" 	k="41" />
<hkern g1="l,uniFB02" 	g2="bullet" 	k="20" />
<hkern g1="l,uniFB02" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="l,uniFB02" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="l,uniFB02" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="l,uniFB02" 	g2="one" 	k="45" />
<hkern g1="l,uniFB02" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="l,uniFB02" 	g2="question" 	k="31" />
<hkern g1="l,uniFB02" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="31" />
<hkern g1="l,uniFB02" 	g2="seven" 	k="25" />
<hkern g1="l,uniFB02" 	g2="trademark" 	k="31" />
<hkern g1="l,uniFB02" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="l,uniFB02" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="31" />
<hkern g1="l,uniFB02" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="l,uniFB02" 	g2="braceright" 	k="-4" />
<hkern g1="l,uniFB02" 	g2="bracketright" 	k="-4" />
<hkern g1="l,uniFB02" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="l,uniFB02" 	g2="z" 	k="-20" />
<hkern g1="l,uniFB02" 	g2="exclam" 	k="-4" />
<hkern g1="h,m,n,ntilde" 	g2="backslash" 	k="78" />
<hkern g1="h,m,n,ntilde" 	g2="one" 	k="61" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="61" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="seven" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="v,y,yacute,ydieresis" 	k="18" />
<hkern g1="h,m,n,ntilde" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="q" 	g2="backslash" 	k="57" />
<hkern g1="q" 	g2="question" 	k="41" />
<hkern g1="q" 	g2="trademark" 	k="20" />
<hkern g1="q" 	g2="j" 	k="-8" />
<hkern g1="r" 	g2="ampersand" 	k="82" />
<hkern g1="r" 	g2="asterisk,registered" 	k="-29" />
<hkern g1="r" 	g2="backslash" 	k="102" />
<hkern g1="r" 	g2="bullet" 	k="10" />
<hkern g1="r" 	g2="eight" 	k="20" />
<hkern g1="r" 	g2="four" 	k="61" />
<hkern g1="r" 	g2="g" 	k="37" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="72" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="31" />
<hkern g1="r" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="76" />
<hkern g1="r" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="r" 	g2="one" 	k="-20" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="r" 	g2="question" 	k="61" />
<hkern g1="r" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-4" />
<hkern g1="r" 	g2="trademark" 	k="41" />
<hkern g1="r" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-41" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="72" />
<hkern g1="r" 	g2="slash" 	k="123" />
<hkern g1="r" 	g2="j" 	k="20" />
<hkern g1="r" 	g2="s" 	k="37" />
<hkern g1="r" 	g2="two" 	k="10" />
<hkern g1="r" 	g2="underscore" 	k="164" />
<hkern g1="r" 	g2="at" 	k="31" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="r" 	g2="z" 	k="20" />
<hkern g1="r" 	g2="five" 	k="20" />
<hkern g1="r" 	g2="three" 	k="41" />
<hkern g1="r" 	g2="numbersign" 	k="41" />
<hkern g1="s" 	g2="backslash" 	k="92" />
<hkern g1="s" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="s" 	g2="nine" 	k="20" />
<hkern g1="s" 	g2="one" 	k="61" />
<hkern g1="s" 	g2="question" 	k="61" />
<hkern g1="s" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="31" />
<hkern g1="s" 	g2="seven" 	k="51" />
<hkern g1="s" 	g2="trademark" 	k="41" />
<hkern g1="s" 	g2="v,y,yacute,ydieresis" 	k="33" />
<hkern g1="s" 	g2="w" 	k="12" />
<hkern g1="s" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="20" />
<hkern g1="s" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="s" 	g2="s" 	k="27" />
<hkern g1="s" 	g2="two" 	k="20" />
<hkern g1="s" 	g2="underscore" 	k="41" />
<hkern g1="s" 	g2="x" 	k="20" />
<hkern g1="s" 	g2="five" 	k="20" />
<hkern g1="t" 	g2="backslash" 	k="82" />
<hkern g1="t" 	g2="bullet" 	k="41" />
<hkern g1="t" 	g2="four" 	k="41" />
<hkern g1="t" 	g2="g" 	k="31" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="t" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="82" />
<hkern g1="t" 	g2="l" 	k="20" />
<hkern g1="t" 	g2="nine" 	k="10" />
<hkern g1="t" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="t" 	g2="one" 	k="41" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="t" 	g2="question" 	k="61" />
<hkern g1="t" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="31" />
<hkern g1="t" 	g2="seven" 	k="41" />
<hkern g1="t" 	g2="six" 	k="20" />
<hkern g1="t" 	g2="trademark" 	k="41" />
<hkern g1="t" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="t" 	g2="zero" 	k="20" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="12" />
<hkern g1="t" 	g2="bracketright" 	k="-20" />
<hkern g1="t" 	g2="at" 	k="20" />
<hkern g1="t" 	g2="colon,semicolon" 	k="-4" />
<hkern g1="t" 	g2="exclam" 	k="-4" />
<hkern g1="t" 	g2="numbersign" 	k="20" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="backslash" 	k="57" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="one" 	k="41" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="question" 	k="41" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="seven" 	k="41" />
<hkern g1="u,mu,ugrave,uacute,ucircumflex,udieresis,uniFB01" 	g2="trademark" 	k="31" />
<hkern g1="v,y,yacute,ydieresis" 	g2="ampersand" 	k="25" />
<hkern g1="v,y,yacute,ydieresis" 	g2="asterisk,registered" 	k="-8" />
<hkern g1="v,y,yacute,ydieresis" 	g2="backslash" 	k="41" />
<hkern g1="v,y,yacute,ydieresis" 	g2="four" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="g" 	k="2" />
<hkern g1="v,y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="4" />
<hkern g1="v,y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="v,y,yacute,ydieresis" 	g2="question" 	k="61" />
<hkern g1="v,y,yacute,ydieresis" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-8" />
<hkern g1="v,y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="27" />
<hkern g1="v,y,yacute,ydieresis" 	g2="s" 	k="12" />
<hkern g1="v,y,yacute,ydieresis" 	g2="two" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="underscore" 	k="61" />
<hkern g1="v,y,yacute,ydieresis" 	g2="colon,semicolon" 	k="-4" />
<hkern g1="v,y,yacute,ydieresis" 	g2="exclam" 	k="-8" />
<hkern g1="v,y,yacute,ydieresis" 	g2="three" 	k="25" />
<hkern g1="w" 	g2="asterisk,registered" 	k="-8" />
<hkern g1="w" 	g2="backslash" 	k="41" />
<hkern g1="w" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="w" 	g2="question" 	k="41" />
<hkern g1="w" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-4" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="23" />
<hkern g1="w" 	g2="underscore" 	k="41" />
<hkern g1="w" 	g2="colon,semicolon" 	k="-4" />
<hkern g1="w" 	g2="exclam" 	k="-4" />
<hkern g1="w" 	g2="three" 	k="20" />
<hkern g1="k,x" 	g2="ampersand" 	k="41" />
<hkern g1="k,x" 	g2="asterisk,registered" 	k="-4" />
<hkern g1="k,x" 	g2="backslash" 	k="41" />
<hkern g1="k,x" 	g2="bullet" 	k="47" />
<hkern g1="k,x" 	g2="less,equal,greater,multiply" 	k="31" />
<hkern g1="k,x" 	g2="four" 	k="31" />
<hkern g1="k,x" 	g2="g" 	k="12" />
<hkern g1="k,x" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="k,x" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="72" />
<hkern g1="k,x" 	g2="nine" 	k="20" />
<hkern g1="k,x" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="k,x" 	g2="one" 	k="37" />
<hkern g1="k,x" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="k,x" 	g2="question" 	k="51" />
<hkern g1="k,x" 	g2="six" 	k="20" />
<hkern g1="k,x" 	g2="trademark" 	k="41" />
<hkern g1="k,x" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="12" />
<hkern g1="k,x" 	g2="zero" 	k="4" />
<hkern g1="k,x" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="-4" />
<hkern g1="k,x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="k,x" 	g2="braceright" 	k="-8" />
<hkern g1="k,x" 	g2="bracketright" 	k="-8" />
<hkern g1="k,x" 	g2="s" 	k="10" />
<hkern g1="k,x" 	g2="at" 	k="20" />
<hkern g1="k,x" 	g2="colon,semicolon" 	k="-4" />
<hkern g1="k,x" 	g2="five" 	k="20" />
<hkern g1="k,x" 	g2="exclam" 	k="-4" />
<hkern g1="ampersand" 	g2="J" 	k="6" />
<hkern g1="ampersand" 	g2="T" 	k="164" />
<hkern g1="ampersand" 	g2="V" 	k="70" />
<hkern g1="ampersand" 	g2="W" 	k="25" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="131" />
<hkern g1="ampersand" 	g2="Z" 	k="20" />
<hkern g1="ampersand" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="ampersand" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="ampersand" 	g2="five" 	k="41" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="164" />
<hkern g1="ampersand" 	g2="v,y,yacute,ydieresis" 	k="41" />
<hkern g1="ampersand" 	g2="w" 	k="41" />
<hkern g1="at" 	g2="T" 	k="102" />
<hkern g1="at" 	g2="V" 	k="31" />
<hkern g1="at" 	g2="W" 	k="20" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="98" />
<hkern g1="at" 	g2="Z" 	k="41" />
<hkern g1="at" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="41" />
<hkern g1="at" 	g2="AE" 	k="41" />
<hkern g1="at" 	g2="X" 	k="61" />
<hkern g1="at" 	g2="s" 	k="20" />
<hkern g1="at" 	g2="x" 	k="20" />
<hkern g1="at" 	g2="z" 	k="20" />
<hkern g1="degree" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="164" />
<hkern g1="less,equal,greater,multiply" 	g2="J" 	k="6" />
<hkern g1="less,equal,greater,multiply" 	g2="Z" 	k="57" />
<hkern g1="less,equal,greater,multiply" 	g2="X" 	k="61" />
<hkern g1="less,equal,greater,multiply" 	g2="x" 	k="31" />
<hkern g1="percent" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="123" />
<hkern g1="sterling" 	g2="five" 	k="31" />
<hkern g1="sterling" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="yen" 	g2="five" 	k="20" />
<hkern g1="yen" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="yen" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="yen" 	g2="dollar,S" 	k="41" />
<hkern g1="yen" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="asterisk,registered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="asterisk,registered" 	g2="AE" 	k="102" />
<hkern g1="asterisk,registered" 	g2="T" 	k="-4" />
<hkern g1="asterisk,registered" 	g2="V" 	k="-4" />
<hkern g1="asterisk,registered" 	g2="W" 	k="-4" />
<hkern g1="asterisk,registered" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="asterisk,registered" 	g2="f,uniFB01,uniFB02" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-41" />
<hkern g1="asterisk,registered" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="asterisk,registered" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="asterisk,registered" 	g2="t" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="v,y,yacute,ydieresis" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="w" 	k="-8" />
<hkern g1="asterisk,registered" 	g2="x" 	k="-4" />
<hkern g1="backslash" 	g2="T" 	k="102" />
<hkern g1="backslash" 	g2="V" 	k="72" />
<hkern g1="backslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="backslash" 	g2="f,uniFB01,uniFB02" 	k="61" />
<hkern g1="backslash" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="205" />
<hkern g1="backslash" 	g2="t" 	k="41" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="backslash" 	g2="Z" 	k="41" />
<hkern g1="backslash" 	g2="j" 	k="-41" />
<hkern g1="braceleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-8" />
<hkern g1="braceleft" 	g2="AE" 	k="-8" />
<hkern g1="braceleft" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-66" />
<hkern g1="braceleft" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="braceleft" 	g2="x" 	k="-8" />
<hkern g1="braceleft" 	g2="j" 	k="-25" />
<hkern g1="braceleft" 	g2="Iacute,Icircumflex,Idieresis" 	k="-45" />
<hkern g1="braceleft" 	g2="J" 	k="-8" />
<hkern g1="braceleft" 	g2="X" 	k="-4" />
<hkern g1="bracketleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-8" />
<hkern g1="bracketleft" 	g2="AE" 	k="-12" />
<hkern g1="bracketleft" 	g2="T" 	k="-4" />
<hkern g1="bracketleft" 	g2="V" 	k="-12" />
<hkern g1="bracketleft" 	g2="W" 	k="-4" />
<hkern g1="bracketleft" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-66" />
<hkern g1="bracketleft" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-20" />
<hkern g1="bracketleft" 	g2="x" 	k="-8" />
<hkern g1="bracketleft" 	g2="Y,Yacute,Ydieresis" 	k="-8" />
<hkern g1="bracketleft" 	g2="j" 	k="-29" />
<hkern g1="bracketleft" 	g2="Iacute,Icircumflex,Idieresis" 	k="-45" />
<hkern g1="bracketleft" 	g2="J" 	k="-8" />
<hkern g1="bracketleft" 	g2="X" 	k="-8" />
<hkern g1="bullet" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="bullet" 	g2="T" 	k="102" />
<hkern g1="bullet" 	g2="V" 	k="20" />
<hkern g1="bullet" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="bullet" 	g2="x" 	k="47" />
<hkern g1="bullet" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="bullet" 	g2="Z" 	k="82" />
<hkern g1="bullet" 	g2="X" 	k="29" />
<hkern g1="bullet" 	g2="dollar,S" 	k="51" />
<hkern g1="bullet" 	g2="five" 	k="20" />
<hkern g1="bullet" 	g2="z" 	k="10" />
<hkern g1="exclam" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-2" />
<hkern g1="exclamdown" 	g2="T" 	k="72" />
<hkern g1="exclamdown" 	g2="V" 	k="41" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="exclamdown" 	g2="j" 	k="-25" />
<hkern g1="exclamdown" 	g2="J" 	k="-8" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Z" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="dollar,S" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="five" 	k="41" />
<hkern g1="numbersign" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="numbersign" 	g2="AE" 	k="45" />
<hkern g1="numbersign" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="numbersign" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-49" />
<hkern g1="numbersign" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="numbersign" 	g2="v,y,yacute,ydieresis" 	k="-20" />
<hkern g1="numbersign" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="numbersign" 	g2="Z" 	k="31" />
<hkern g1="numbersign" 	g2="j" 	k="20" />
<hkern g1="numbersign" 	g2="X" 	k="20" />
<hkern g1="numbersign" 	g2="dollar,S" 	k="31" />
<hkern g1="numbersign" 	g2="z" 	k="20" />
<hkern g1="numbersign" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="numbersign" 	g2="g" 	k="20" />
<hkern g1="numbersign" 	g2="m,n,p,r,mu,ntilde" 	k="10" />
<hkern g1="numbersign" 	g2="s" 	k="31" />
<hkern g1="numbersign" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="parenleft" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-66" />
<hkern g1="parenleft" 	g2="j" 	k="-33" />
<hkern g1="parenleft" 	g2="Iacute,Icircumflex,Idieresis" 	k="-61" />
<hkern g1="parenleft" 	g2="J" 	k="-41" />
<hkern g1="question" 	g2="v,y,yacute,ydieresis" 	k="-8" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="questiondown" 	g2="AE" 	k="41" />
<hkern g1="questiondown" 	g2="T" 	k="164" />
<hkern g1="questiondown" 	g2="V" 	k="82" />
<hkern g1="questiondown" 	g2="W" 	k="61" />
<hkern g1="questiondown" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="45" />
<hkern g1="questiondown" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="questiondown" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="questiondown" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="102" />
<hkern g1="questiondown" 	g2="t" 	k="61" />
<hkern g1="questiondown" 	g2="v,y,yacute,ydieresis" 	k="82" />
<hkern g1="questiondown" 	g2="w" 	k="41" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="172" />
<hkern g1="questiondown" 	g2="Z" 	k="31" />
<hkern g1="questiondown" 	g2="j" 	k="-12" />
<hkern g1="questiondown" 	g2="X" 	k="41" />
<hkern g1="questiondown" 	g2="dollar,S" 	k="61" />
<hkern g1="questiondown" 	g2="five" 	k="41" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="questiondown" 	g2="m,n,p,r,mu,ntilde" 	k="61" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="72" />
<hkern g1="slash" 	g2="AE" 	k="82" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="102" />
<hkern g1="slash" 	g2="f,uniFB01,uniFB02" 	k="61" />
<hkern g1="slash" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="slash" 	g2="t" 	k="61" />
<hkern g1="slash" 	g2="v,y,yacute,ydieresis" 	k="41" />
<hkern g1="slash" 	g2="w" 	k="41" />
<hkern g1="slash" 	g2="x" 	k="61" />
<hkern g1="slash" 	g2="Z" 	k="41" />
<hkern g1="slash" 	g2="Iacute,Icircumflex,Idieresis" 	k="-8" />
<hkern g1="slash" 	g2="X" 	k="41" />
<hkern g1="slash" 	g2="five" 	k="41" />
<hkern g1="slash" 	g2="z" 	k="61" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="slash" 	g2="g" 	k="61" />
<hkern g1="slash" 	g2="m,n,p,r,mu,ntilde" 	k="61" />
<hkern g1="slash" 	g2="s" 	k="61" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="61" />
<hkern g1="underscore" 	g2="T" 	k="102" />
<hkern g1="underscore" 	g2="V" 	k="102" />
<hkern g1="underscore" 	g2="W" 	k="123" />
<hkern g1="underscore" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="underscore" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="underscore" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="underscore" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="205" />
<hkern g1="underscore" 	g2="t" 	k="41" />
<hkern g1="underscore" 	g2="v,y,yacute,ydieresis" 	k="61" />
<hkern g1="underscore" 	g2="w" 	k="41" />
<hkern g1="underscore" 	g2="Y,Yacute,Ydieresis" 	k="164" />
<hkern g1="underscore" 	g2="j" 	k="-33" />
<hkern g1="underscore" 	g2="J" 	k="-45" />
<hkern g1="underscore" 	g2="dollar,S" 	k="41" />
<hkern g1="underscore" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="82" />
<hkern g1="underscore" 	g2="g" 	k="-8" />
<hkern g1="underscore" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="underscore" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="61" />
<hkern g1="underscore" 	g2="l" 	k="20" />
<hkern g1="eight" 	g2="J" 	k="20" />
<hkern g1="eight" 	g2="dollar,S" 	k="20" />
<hkern g1="eight" 	g2="T" 	k="41" />
<hkern g1="eight" 	g2="V" 	k="20" />
<hkern g1="eight" 	g2="X" 	k="14" />
<hkern g1="eight" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="eight" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="31" />
<hkern g1="eight" 	g2="v,y,yacute,ydieresis" 	k="12" />
<hkern g1="four" 	g2="J" 	k="4" />
<hkern g1="four" 	g2="dollar,S" 	k="41" />
<hkern g1="four" 	g2="T" 	k="102" />
<hkern g1="four" 	g2="V" 	k="41" />
<hkern g1="four" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="four" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="102" />
<hkern g1="four" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="four" 	g2="W" 	k="20" />
<hkern g1="four" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="four" 	g2="five" 	k="20" />
<hkern g1="four" 	g2="x" 	k="20" />
<hkern g1="nine" 	g2="J" 	k="20" />
<hkern g1="nine" 	g2="dollar,S" 	k="20" />
<hkern g1="nine" 	g2="T" 	k="41" />
<hkern g1="nine" 	g2="V" 	k="10" />
<hkern g1="nine" 	g2="X" 	k="4" />
<hkern g1="nine" 	g2="Y,Yacute,Ydieresis" 	k="66" />
<hkern g1="nine" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="nine" 	g2="AE" 	k="41" />
<hkern g1="nine" 	g2="Z" 	k="41" />
<hkern g1="nine" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="one" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-41" />
<hkern g1="seven" 	g2="J" 	k="41" />
<hkern g1="seven" 	g2="dollar,S" 	k="41" />
<hkern g1="seven" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-8" />
<hkern g1="seven" 	g2="five" 	k="37" />
<hkern g1="seven" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="seven" 	g2="AE" 	k="102" />
<hkern g1="seven" 	g2="Z" 	k="31" />
<hkern g1="seven" 	g2="s" 	k="51" />
<hkern g1="seven" 	g2="z" 	k="41" />
<hkern g1="seven" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="61" />
<hkern g1="seven" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-33" />
<hkern g1="seven" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="seven" 	g2="g" 	k="51" />
<hkern g1="seven" 	g2="m,n,p,r,mu,ntilde" 	k="20" />
<hkern g1="seven" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="seven" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="six" 	g2="J" 	k="20" />
<hkern g1="six" 	g2="T" 	k="102" />
<hkern g1="six" 	g2="V" 	k="41" />
<hkern g1="six" 	g2="X" 	k="20" />
<hkern g1="six" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="six" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="41" />
<hkern g1="six" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="six" 	g2="five" 	k="10" />
<hkern g1="six" 	g2="x" 	k="20" />
<hkern g1="three" 	g2="T" 	k="57" />
<hkern g1="three" 	g2="Y,Yacute,Ydieresis" 	k="57" />
<hkern g1="two" 	g2="J" 	k="31" />
<hkern g1="two" 	g2="T" 	k="41" />
<hkern g1="two" 	g2="V" 	k="20" />
<hkern g1="two" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="two" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="two" 	g2="W" 	k="10" />
<hkern g1="two" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="two" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="two" 	g2="g" 	k="41" />
<hkern g1="two" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="zero" 	g2="T" 	k="61" />
<hkern g1="zero" 	g2="X" 	k="4" />
<hkern g1="zero" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="zero" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="zero" 	g2="x" 	k="4" />
<hkern g1="zero" 	g2="Z" 	k="20" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="T" 	k="123" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="V" 	k="61" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="Y,Yacute,Ydieresis" 	k="131" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="102" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="onequarter,onehalf,threequarters" 	g2="five" 	k="-4" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="V" 	k="-20" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="X" 	k="14" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="v,y,yacute,ydieresis" 	k="-8" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="x" 	k="-4" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="39" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="AE" 	k="82" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="57" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="igrave,iacute,icircumflex,idieresis" 	k="-12" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="t" 	k="-4" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="w" 	k="-4" />
<hkern g1="at" 	g2="colon,semicolon" 	k="31" />
<hkern g1="at" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="copyright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="degree" 	g2="colon,semicolon" 	k="236" />
<hkern g1="degree" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="184" />
<hkern g1="degree" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="410" />
<hkern g1="Euro" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="82" />
<hkern g1="sterling" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="61" />
<hkern g1="trademark" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="yen" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="41" />
<hkern g1="yen" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="asterisk,registered" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="205" />
<hkern g1="backslash" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="123" />
<hkern g1="bracketleft" 	g2="colon,semicolon" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="AE" 	k="-41" />
<hkern g1="colon,semicolon" 	g2="T" 	k="82" />
<hkern g1="colon,semicolon" 	g2="V" 	k="20" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="106" />
<hkern g1="colon,semicolon" 	g2="Z" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="backslash" 	k="102" />
<hkern g1="colon,semicolon" 	g2="bracketright" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="Euro" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="f,uniFB01,uniFB02" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="five" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="four" 	k="10" />
<hkern g1="colon,semicolon" 	g2="one" 	k="92" />
<hkern g1="colon,semicolon" 	g2="question" 	k="49" />
<hkern g1="colon,semicolon" 	g2="s" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="51" />
<hkern g1="colon,semicolon" 	g2="t" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="three" 	k="-8" />
<hkern g1="colon,semicolon" 	g2="v,y,yacute,ydieresis" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="w" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="x" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="z" 	k="-4" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="102" />
<hkern g1="guillemotright,guilsinglright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="AE" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="T" 	k="102" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="V" 	k="57" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="106" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="Z" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="backslash" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="f,uniFB01,uniFB02" 	k="31" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="five" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="four" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="one" 	k="143" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="question" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="seven" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="t" 	k="10" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="three" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="v,y,yacute,ydieresis" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="x" 	k="72" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="z" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="J" 	k="14" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="dollar,S" 	k="88" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="X" 	k="66" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="ampersand" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="guillemotright,guilsinglright" 	k="102" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="j" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="nine" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="percent" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="questiondown" 	k="164" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="slash" 	k="123" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="trademark" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="two" 	k="145" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="underscore" 	k="164" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,divide,endash,emdash" 	g2="yen" 	k="41" />
<hkern g1="numbersign" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="numbersign" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="102" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-4" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="AE" 	k="-8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="123" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="102" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="172" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Z" 	k="-4" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="backslash" 	k="287" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="bracketright" 	k="-4" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Euro" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="f,uniFB01,uniFB02" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="five" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="four" 	k="86" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="one" 	k="205" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="question" 	k="102" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="s" 	k="-4" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="t" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="three" 	k="-8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v,y,yacute,ydieresis" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="x" 	k="-4" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="z" 	k="-4" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="ampersand" 	k="37" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="nine" 	k="57" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="percent" 	k="205" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="trademark" 	k="164" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="yen" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk,registered" 	k="205" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="copyright" 	k="164" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="g" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="c,d,e,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="205" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="six" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="zero" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni00B2,uni00B3,uni00B9,onequarter,onehalf,threequarters" 	k="102" />
<hkern g1="question" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="205" />
<hkern g1="question" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="113" />
<hkern g1="questiondown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="123" />
<hkern g1="questiondown" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="123" />
<hkern g1="questiondown" 	g2="colon,semicolon" 	k="61" />
<hkern g1="slash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="287" />
<hkern g1="slash" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="123" />
<hkern g1="slash" 	g2="colon,semicolon" 	k="102" />
<hkern g1="underscore" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="164" />
<hkern g1="four" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="20" />
<hkern g1="nine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="119" />
<hkern g1="seven" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="92" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="51" />
<hkern g1="seven" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="195" />
<hkern g1="six" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="two" 	g2="plus,hyphen,asciitilde,logicalnot,uni00AD,periodcentered,divide,endash,emdash" 	k="70" />
<hkern g1="zero" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="23" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
</font>
</defs></svg> 