# -*- coding: utf-8 -*-

# With this config we can disable search
BLOCKED_SEARCH_DATES = 'Blocked search dates'

# If package is cheaper than rate will be showed first
PACKAGES_PRIORITIZE = 'Packages prioritize'

FORCE_PACKAGES_PRIORITIZE = 'Force packages prioritize'

# Will be always displayed normal prices not club
NORMAL_PRICES_PRIORITIZE = 'Normal prices prioritize'

# Priorize packages by name
PACKAGES_NAME_PRIORITIZE = 'Packages name prioritize'

PROMOCODE_GIFTS = 'Promocodes gifts'

# Separate normal rates by tabs
NORMAL_RATES_TABS = 'Rates separated by tabs'


NORMAL_RATES_TABS_SORT = 'Rates separated by tabs sort automatically by price'

# Custom per night label
CUSTOM_PER_NIGHT_RATE = 'Custom per night rate'

STANDARD_TAB_BY_PROMOCODE = "standard tab by promocode"

BREAKDOWN_BOOKING_PROCESS_PRICES = 'Booking prices breakdown'

BOOKING1_BOARD_MESSAGES = 'Booking1 board messages'

ADD_KIDS_DOUBLE_CALENDAR = "Add kids double calendar"

RATES_DOUBLE_CALENDAR_CONFIG = "rates_double_calendar"

EXTRA_B3_TEXT_AFTER_TOTAL = 'Booking3 text after total'

# if populate, we show a message informing that the promocode is OK
INFORM_PROMOCODE_OK = "Inform promocode ok"

MIX_PROMOTIONS_DAYS = "Mix promotions days"

BOOKING1_PROMOTION_PERCENTAGE_PER_RATE = "Booking1 promotion percentage per rate"

BOOKING1_PROMOTION_PERCENTAGE_PER_TAB = "Booking1 promotion percentage per tab"

BOOKING1_FORCE_PROMOTION_PERCENTAGE = "Booking1 force promotion percentage"

# Booking1 rates advices
BOOKING1_RATES_ADVICES = 'Booking1 rates advices'

MARK_ALL_RATES_AS_BEST = "Booking1 mark all rates as best"

ONLY_SHOW_ONE_OFFER_BY_NAME = "only show one offer by name"

BOOKING1_SPECIAL_OFFER_COLUMN = "Booking1 special offer column"

BOOKING1_OLD_OFFERS_FORMAT = "Booking1 old offers format"

BOOKING1_VIRTUAL_TOUR_IN_ROOM_GALLERY = "Booking1 virtual tour in room gallery"

BOOKING1_RATES_DROPDOWN = "Booking1 rates dropdown"

# Filter by words separated by @@ iE Habitacion@@Suite@@Studio use spanish names! We only show rooms with this words
ROOM_FILTER_BY_NAME = "Show Only Rooms by Name"

# Board in Rate (i.e. Wubook restrictions)
BOARD_IN_RATE_STRUCTURE = 'Regimen in Rate Structure'

FLASH_OFFERS_TIME = 'Flash offers time'

# In case we want to show a message when there is not availability for specific dates
NO_AVAILABILITY_MESSAGE_DATES = 'No Availability Message Dates'

# If you want to custom the message for days with minimum stay in the calendar
CUSTOM_RESTRICTION_MESSAGE = 'Custom restriction message'

AGENCIES_EXTRA_DATA = "Agencies extra data"

# Booking 1 Countdown
BOOKING1_COUNTDOWN_BANNER = "Booking1 Countdown Banner"

# Booking message for display if searched dates are inside range. IE: pool info;2016-10-20,2017-02-20@gala;2017-10-20,2017-10-25
BOOKING1_MESSAGE_DATES = 'Booking1 Message Dates'

BOOKING1_HIDE_ROOMS_PACKAGES_TABS = 'Booking hide rooms packages tabs'

# Fuerte hoteles, specific functionality to give club prices even if the user has chosen standard prices
RECALCULATE_PRICES_FOR_USER = "recalculate prices for user"
FORCE_RATE_CONDITIONS_TO_ALL = "Force rate conditions to all"
REGIMEN_ORDER = 'Orden de regimenes'
RATE_ORDER = 'Orden de tarifas'

SHOW_MINIMUM_DISCOUNT_TAB_CLUB = 'Show minimum discount in tab club'

# Show last day to cancell
VIEW_CANCELL_DAYS = "Ver fecha cancelación"

SHOW_SUMMARY_PACKAGE_PRICE = "Show summary package price"

USE_PAYMENT_COMISION = "Use Payment Comision"

HIJIFFY_AS_SOURCE = "Hijiffy as source"

AVOID_PROMOTIONS_CALENDAR_DOUBLE = "Avoid promotions calendar double"

NO_SEND_CANCEL_EMAIL = "No send cancel email"

ACCEPT_ALWAYS_RESERVATION_SIBS = "Accept always reservation sibs"

SAVE_CANCELLATION_REASON_IN_INCIDENTS = "Save CANCELLATION_REASON_IN_INCIDENTS"

SAVE_SHOPPING_CART_OCCUPANCY_BY_ROOM = "Save shopping cart occupancy by room"

ADD_MARKETING_IN_COMMENTS = "Add marketing in comments"

BOOKING3_CUSTOM_BOOKING_CONDITIONS = "Booking3 custom booking conditions"

BOOKING4_SHOW_PROMOCODE = "Booking4 show promocode"

FIX_BONO_IN_SHOPPING_CART = "Fix bono in shopping cart"

NOT_SEND_EMAIL_REFUND_PAYMENT_NR = "not send email refund payment nr"

AVOID_MIX_HOTELS_DOUBLE_CALENDAR = "Avoid mix hotels double calendar"

FIXING_SHOPPING_CART_WITHOUT_MERGING = "fixing shopping cart without merging"

ADDITIONAL_SERVICES_VERSION = 'Additional services version'

BOOKING3_UPGRADE_ROOMS = 'Booking3 upgrade rooms'

UPGRADES_PRICES_BY_OCCUPANCY_NIGHTS = 'Upgrades prices by occupancy and nights'

UPGRADES_VIEW_MORE_POPUP = 'Upgrades view more popup'

ALLOW_MULTIPLE_UPGRADES_AT_BOOKING3 = 'Allow multiple upgrades at booking3'

EXTRA_PARAMS_CAMPAIGN_URL = "Extra params campaign url"

ONLY_SHOW_NO_CREDIT_CARD_OPTION_AVAILABLE = "Only show no credit card option available"

FIXING_TRUNCATE_ROUND_PRICES = "Fixing truncate round prices"

FIXING_PRICE_DAY_SHOPPING_CART = "Fixing price day shopping cart"

AVOID_RATES_IN_BOOKING0 = "Avoid rates in booking0"

HOTEL_INFO = "Hotel info"

CALLCENTER_RESERVATION_AS_BOOKING_ENGINE = "Callcenter reservation as booking engine"

OFFER_LABEL_IN_RATE_BLOCK = 'Offer label in rate block'

CUSTOM_SCROLL_IMAGE = "Custom scroll image"

EXTRA_RATE_DESCRIPTIONS = 'Extra rate descriptions'

USE_DEFAULT_GATEWAY = "use default gateway"

BIZUM_PAYMENT_FULL_RESERVATION = "Bizum payment full reservation"

TIMEZONE_ADJUST = "Timezone adjust"

BOOK_YESTERDAY = "Allow booking yesterday"

THIRD_CLICK_CLOSE_CALENDAR = "Third click close calendar mobile"

BOOKING_PROCESS_CUSTOM_POPUPS = "Booking process custom popups"

SHOW_ALWAYS_PAYMENT_SELECT = "Show always payment select"

BOOKING_TAB_INFO_BY_DOMAIN = "Booking tab info by domain"


### Calendar configs #####

# Hide the prices in the calendar
HIDE_PRICES_CALENDAR = 'Hide prices calendar'

### End calendar configs #####


### Analytics Configs ###

GTM_DATALAYER_CORPORATE = 'GTM datalayer corporate'

ALLOW_ANALYTICS_AT_CALLCENTER = 'Allow analytics at callcenter'

GOOGLE_ADS_ENHANCED_CONVERSIONS = "Google Ads enhanced conversions"

### End Analytics Configs ###


### Tax configs ###

PAY_TAX_AT_HOTEL = 'Pay tax at hotel'

EXTRACT_ADDITIONAL_SERVICES_TAXES = "Extract additional services taxes"

MANAGER_SHOW_BOTH_PRICE_TABLES = 'Manager confirmation show prices per day with and without taxes'

### End Tax configs ###

# Web seeker dev environment
WEB_SEEKER_DEV = "Web seeker dev"

# Canonical website redirection
CANONICAL_URL_LANG_PATH_REDIRECTION = "Canonical URL Lang Path Redirection"

CHECK_POLICIES_BEFORE_SEND_CANCELLATION = "check policies before send cancellation with payments email"

# TODO: This file need to be deleted



LOCKED = 'User rates locked'

KEY_HOTEL_NAME = 'Nombre del hotel'
BOOKING3_HOTEL_NAME = 'Booking3 nombre hotel'
KEY_DOMAIN = 'Dominio asociado'
DOMAIN_UNDER_PROXY = "Behind Proxy"  # If exists this config, will avoid google geolocation
TOP_SECTIONS = 'Secciones en header'
MAIN_SECTIONS = 'Secciones en menu principal'
SECONDARY_SECTIONS = 'Secciones secundarias'
MOBILE_SECTIONS = 'Secciones en web mobile'
BOTTOM_MOBILE_SECTIONS = 'Secciones inferiores en web mobile'
FOOTER_SECTIONS = 'Secciones en footer'
FEATURED_SECTIONS = 'Secciones a destacar'
CONTACT_PHONES = 'Telefonos de contacto'
CUSTOM_LOGO_LINK = "Custom logo link"
HOTEL_EMAIL = 'hotel_email'
HOTEL_EMAIL_HEADER = 'emial hotel header'
SEO_LINKS = 'Prefijo en enlaces'
TRANSLATION_TO_REPLACE = 'Texto a reemplazar'
TRANSLATION_TO_REPLACE_NAMESPACE = 'Texto a reemplazar namespace'
TRANSLATION_TO_REPLACE_I18N = 'Texto a reemplazar i18n'
ROOM_ORDER = 'Orden de habitaciones'
ROOM_ORDER_CALENDAR = 'Orden de habitaciones calendario'
PROMOTION_ORDER = 'Orden de ofertas'
LANGUAGE = 'language'
HOTEL_MANAGER_LANGUAGE = 'Hotel Manager Languages'
WIDGET_ONLY = "Widget Only"
INJECTION_ANALYTICS_CAMPAIGN_TRACKING = "Injection analytics campaign tracking"
CLUB_POPUP_INJECTION = "Club popup injection"
BOOKING_PROCESS_WIDGET = "Booking Process Widget"
BOOKING_PROCESS_VERSION = "Booking process version"
BOOKING_PACKAGES_VERSION = "Booking packages version"
BOOKING_STEPS_VERSION = "Booking steps version"

# Used to modify styles in booking process for some campaign
BOOKING_PROCESS_CAMPAIGN = "Booking process campaign"

EXTRA_POLICIES_SECTIONS = 'Extra policies sections'

CORPORATE_HOTEL = 'Corporate hotel'

BOOKING_SHOPPING_CART = 'Booking shopping cart'

SMART_SHOPPING_CART = "Smart Shopping Cart"

SHOPPING_CART_DETAILS_TAXES_V2 = "Shopping cart details taxes v2"

CALL_CENTER_FORCE_SHOPPING_CART = "Call center force shopping cart"

PRICE_PER_DAY_SHOPPING_CART = "Price per day shopping cart"

PROMOCODE_TITLE_ENABLED = "Promocode Title Enabled"

PROMOCODE_TEXT_BOOKING_PROCESS = "Texto promocode proceso de reserva"

PROMOCODE_ANIMATE = 'Promocode Animado'

PROMOCODE_INPUT_ENABLED = 'Promocode input enabled'

HIDDEN_PROMOCODES = 'Hidden promocodes'

DEFAULT_PROMOCODE = 'default promocode'

#temporary config to validate promocode only for rates in search result, not all rates
RATES_PROMOCODE_VALIDATION = 'Available rates promocode validation'

DEFAULT_PROMOCODE_FOR_MODIFICATION = 'Default promocode for modification'

FORCE_FILTER_RATE_BY_PROMOCODE = "Force filter rate by promocode"

FORCE_FILTER_RATE = "Force filter rate by word"

# Indicates that the current process is really a generic one, so we need to obtain also the hotel_code from the request
GENERIC_BOOKING_PROCESS = 'generic booking process'

# Load custom robots.txt content from a section. @@host@@ will be replaced automatically. Docs - https://paraty.atlassian.net/wiki/spaces/PROYECTOS/pages/2181464075/Set+custom+robots.txt
SEO_ROBOTS_RULES = 'SEO robots rules'

STYLE_NAME = 'styles_name'

TOOLKIT_GUEST = 'toolkit_guest_selector'



PHONE_IN_MOBILE = 'Telefono en version movil'

BOOKING_LOGO_CORPO = 'bookin_logo_corpo'

BOOKING_EXTRA_LOGO = 'Booking extra logo'

# Google Maps Api
USE_GOOGLE_MAPS_API = 'Google Maps API Usage'

# In case we want to allow using currency selectors
USE_CURRENCY_SELECTOR = 'Usar selector de divisas'

# Currency on confirmation
CURRENCY_ON_CONFIRMATION = 'Currency on confirmation'

# Show currency conversion
SHOW_CURRENCY_CONVERSION = 'Show Currency Conversion'

# Base Price Currency
BASE_PRICE_CURRENCY = 'Base Price Currency'

# Mostrar popup de divisas
SHOW_CURRENCY_FIXED = 'Mostrar divisas permanente'

USE_SHORTNAME_IN_CURRENCY_SELECTOR = 'Use shortname in currency selector'

CURRENCY_SELECTOR_VERSION = 'Currency selector version'

# If defined, it enables the profiler in the application
PROFILE_APPLICATION = "Ejecutar profiler"

# Configuration property name to configure the booking search popup.
# The value of the property is the number of seconds to keep the popup open and start the search
BOOKING_POPUP = 'Booking popup'

AUTOMATIC_POPUP_INFO = 'Popup inicio automatico'
AUTOMATIC_POPUP = 'Popup automatico'

BANNER_SUPPLEMENTS = 'banner_in_supplements'

# Specify a section to retreive information about loading popup
BOOKING_POPUP_V2 = 'Booking popup v2'

# Name of the web section that contains the footer of the confirmation email that is sent to the manager
WEB_SECTION_NAME_FOR_MANAGER_EMAIL_FOOTER = "Manager email footer"

NUMBER_OF_ROOMS_DISABLES = "Number of rooms disabled"

WEB_SECTION_HOTEL_DOES_NOT_HAVE_ROOMS_FOR_CAPACITIES_OF_SEARCH = "No rooms for capacities of search"

# This configuration allows to easily configure IPs to be blocked
DOS = "DoS"

BLACKLIST_REFERER = "Blacklist referer"

WHITELIST_MAIL_REFERER = "Whitelist mail referer"



# i.e. if we want to see 10% NR + 5% CLUB AMIGOS
ADD_PERCENTAGE_IN_PROMOTIONS = "Add percentage in promotions"

# This is used for hotels like Amare Marbella that need to support metasearch that goes directly to the server
ALTERNATIVE_SEARCH_PARAMS = "Alternative search params"

# If this is populated it indicates that we DON'T want to ask our customers about their birthday (to send them newsletters)
ASK_BIRTHDAY = "never ask birthday"

HIDE_CITY_BOOKING3 = "hide city booking3"
HIDE_PERSONALID_BOOKING3 = "hide personalID booking3"
OPTIONAL_PERSONALID_BOOKING3 = "Optional personal id booking3"
OPTIONAL_BORNDATE_BOOKING3 = "Optional borndate booking3"
OPTIONAL_INPUTS_BOOKING3 = "Optional inputs booking3"
OPTIONAL_PERSONALID_BOOKING3_GATEWAY = "Optional personal id booking3 gateway"

HIDE_EMAIL_WELCOME = "Hide email welcome"

CUSTOM_BOOKING3_FIELDS = "Custom booking3 fields"

CUSTOM_BOOKING3_FIELDS_BY_ROOM = "Custom booking3 fields by rooms"

SELECTIVE_BOOKING3_DISABLE = "Disable booking3"

SHOW_MANDATORY_FIELD_BOOKING3 = "Show mandatory field booking3"

BOOKING3_OLD_CLIENT_CHECK = "Booking3 old client"

# Hour of checkin
BOOKING3_CHECKIN = "Booking3 checkin"

BOOKING3_MEET_QUESTION = "Referer question booking3"

BOOKING3_CLIENT_QUESTION = "Client question booking3"

BOOKING3_LOADING_POPUP = "Booking3 loading"

DISABLE_BOOKING3_LOADING_POPUP = "disable_booking3_loading_popup"

ALLOW_SUPPLEMENTS_HTML = "Allow html supplements"

TEXT_BEFORE_CHECKS = "Text Before Checks"
TEXT_BEFORE_CHECKS_MOBILE = "Text Before Checks Mobile"

BOOKING3_CUSTOM_AGREE_CHECKBOX = 'Booking3 custom agree checbox'

BOOKING3_CUSTOM_ADDITIONAL_CHECKBOX = 'Booking3 additional checkbox'

# Checkout promocode
PROMOCODE_PLATINUM = 'Promocode platinum'
PROMOCODE_GOLD = 'Promocode gold'
PROMOCODE_SILVER = 'Promocode silver'

NEW_PROMOCODE_EMAIL = 'new promocode email'

SEND_PROMOCODE_ONLY_FORM_CONTACT = "Send promocode only form contact"

# In case we want to ask our customers if they want to receive email notifications
# If they check it, they won't be added to the newsletter list
EMAIL_NOTIFICATIONS = "Check notificaciones comerciales"
EMAIL_NOTIFICATIONS_CHECKED = "Check notificaciones comerciales checked"

FORCED_PROMOTION_NAME_CLIENT_LANGUAGE = 'Forced Promotions Names Language'

# If present, we display 'VAT included' instead 'Taxes included'
BOOKING3_VAT_INCLUDED = "booking3 vat included"

# Values this property can take: the folders under static_1/css/plugins/jquery-ui
# ie. cupertino, ui-darkness (see http://jqueryui.com/themeroller/)
DATEPICKER_THEME = 'Tema colores datepicker'

# Indicates that the application will the booking process on a custom domain i.e. https://www.hotellosjazmines.com
CUSTOM_DOMAIN = "Dominio booking"

CANCEL_DOMAIN = "Dominio cancelacion"

# Custom path steps (This is created only to use with proxies, routes path are static with booking1,booking2,etc..)
CUSTOM_BOOKING_PATHS = "Custom path booking"

# enable button to remove promocode
DELETE_PROMOCODE = "delete_promocode"

# enable show calendar in booking widget
SHOW_CALENDAR_BUTTON = "Widget calendar button"

PROMOCODE_TOGGLE_IN_WIDGET = "Promocode toggle in widget"

# Replace default URL in logo when it's a widget
WIDGET_DOMAIN = "Dominio widget"

# Set a different initial page. This will overwrite sectionType Inicio!
INITIAL_SECTION = "Initial section"

# if populate language selector appears on top
MOBILE_LANGUAGE_ON_TOP = "Mobile language on top"

# if true a GlobalSign SSL Site Seal Code will appear in Booking3 cards_details
CUSTOM_SEAL = "Custom Seal GlobalSign"

# i.e. Don Pancho needs http://hoteldonpancho.es/?idioma=en  (i.e. /?idioma=)
# If value is set to no_available will always go to default domain
LANGUAGE_SEPARATOR = "Separador Idioma"

# Set a different host language format. Ex: 'https://en.barcarolaclub.com' instead of https://barcarolaclub.com/en. Syntax: 'https://@@lang@@.barcarolaclub.com'
CUSTOM_HOST_LANGUAGE_FORMAT = "Custom host language format"

# Ex: 'https://en.barcarolaclub.com' instead of https://barcarolaclub.com/en. Syntax: 'https://@@lang@@.barcarolaclub.com'
BOOKING4_CUSTOM_BACK_LINK = "Booking4 custom back link"

BOOKING4_CUSTOM_BACK_LINK_AGENCIES = "Booking4 custom back link agencies"

BOOKING4_INCLUDE_MISSING_BILLING_DATA = "Booking4 include missing billing data"

# In case the hotel needs a specific booking Identifier builder (i.e. Fuerte Hoteles)
USE_BOOKING_IDENFIER_BUILDER = "Use Booking Identifier Builder"

# This configuration set the languages showed on the website selector
LANGUAGES_IN_WIDGET_HEADER = "widget language header"

# Now we can have languages available on booking process (widget language header is deprecated)
LANGUAGE_SELECTOR_BOOKING_PROCESS = 'language selector booking process'

# Change the lang link of header from shorted link to the complete link
AUTO_CHANGE_LANGUAGE_LINK = "Auto change language link"

# A promocode that is sent in a email, i.e. SPECIALOFFER
PROMOCODE_IN_EMAIL = "Promocode en email"

# A present that is sent in a email, i.e. SPECIALOFFER
PRESENT_IN_EMAIL = "Regalo en email"

# Indicates where the "reply to" button will send the email
REPLY_TO_BOOKING = 'Reply reservas'

EMAIL_BOOKING = 'Email reservas'

EMAIL_BOOKING_PACKAGES = 'Email reservations with packages'

# The name that will appear in the "from" every time we send an email on behalf of the hotel
# TODO: Refactor this config, all calls need to be done to a method, and this is the only one used
EMAIL_SENDER = 'Email sender'

EMAIL_SENDER_MANAGER_FROM_PARATY = 'Email sender manager from paraty'

# The name that will appear in the "from" every time we send an email on behalf of the hotel at contact forms
EMAIL_SENDER_CONTACT = 'Email sender contacto'

# The hotel name will appear in the booking1 even we are not coming from booking 0
HOTEL_NAME_FOR_BOOKING = 'Hotel name for booking'
HOTEL_NAME_FOR_BOOKING3 = 'Hotel name for booking3'
CUSTOM_HOTEL_NAME_IN_LOGO = "custom hotel name in logo"

# Alternative hotel name for mix hotel separated
ALTERNATIVE_HOTEL_NAME = "Alternative hotel name"

SECTION_HOTEL_MIX_INFO = "Mix multiple hotels info section"

# The name that will appear in the "from" every time we send an email on behalf of the hotel
EMAIL_SENDER_CLUB = 'Email sender club'

# To change the email for the cancelations and modifications
CANCEL_AND_MODIFY_EMAIL = "Cancellation and modification email"

# To personalize the subjet in emails confirmation
PERSONALIZED_CONFIRMATION_EMAIL = 'Personalized confirmation email'

# To personalize the subjet in emails cancellation
PERSONALIZED_CANCELLATION_EMAIL = 'Personalized cancellation email'

# Show a popup when the booking is cancelled
POPUP_AT_BOOKING_CANCELLATION = 'Booking cancellation popup'

# Hide rate conditions at cancellations
HIDE_CANCELLATION_RATE_CONDITIONS = 'Hide cancellation rate conditions'

# Hide additional service description at booking confirmation
HIDE_SUPPLEMENT_DESCRIPTION_AT_CONFIRMATION = 'Hide supplement description confirmation'

# To Change the template of the confirmation email
RESERVATION_CONFIRMATION_EMAIL = 'reservation confirmation email'

# Hide cancellation content in the email
CANCELLATION_EMAIL_HIDDEN_CONTENT = 'Hide cancellation content email'

# Banner images in confirmation / cancellation email
BOOKING_4_BANNER_IMAGES = 'booking4 banner images'

# Attach a PDF that can be filled with booking confirmation (https://paraty.atlassian.net/wiki/spaces/PROYECTOS/pages/410910721/Email+Confirmation+-+Attach+PDF)
RESERVATION_CONFIRMATION_PDF = 'reservation confirmation pdf'

# Attach an apple wallet ticket
RESERVATION_CONFIRMATION_APPLE_WALLET = 'Apple wallet confirmation'

# Transform reservation comments into JSON and append specified properties
RESERVATION_COMMENTS_JSON = 'reservation comments json'

# Url of wordpress to get the revolution slider
REVOLUTION_INJECTION = 'revolution injection'

# If you want to show a message to user when the calendar appears and have restrictions
CALENDAR_RESTRICTION_MESSAGE = 'Calendar restriction message'

# If you want to show the restrictions separated by days
CALENDAR_RESTRICTION_DAYS_BREAKDOWN = 'Calendar restrictions days breakdown'

# In case we want to show a message when there is no availability
NO_AVAILABILIY_MESSAGE = 'No Availability Message'

NO_AVAILABILITY_MESSAGE_MIN_STAY = "No Availability Message Min Stay"

NO_AVAILABILITY_MESSAGE_OCCUPANCY = "No Availability Message Occupancy"

BOOKING_TICK_REPLACEMENT = 'Booking ticks replacement'
BOOKING_PARATY_LINK_REPLACEMENT = 'Booking Paraty Link Replacement'
BOOKING_POWERED_PARATY = 'Booking Powered Paraty'
BOOKING_CURRENCY_LANGS = 'Booking currency in langs'
BOOKING_HEADER_FLAG = 'Booking header flag'
BOOKING_HEADER_FLAG_LANGUAGES = 'Booking header flag languages'
BOOKING_HEADER_LANGUAGE_CODE = 'Booking Header Language Code'
BOOKING_HEADER_LANGUAGE_CODE_SELECTOR = 'Booking Header Language Code Selector'

# Show checkbox that applies the promocode and starts a new search. The value is the name of section where title=*promocode to apply*, content=*label* Ej: Landmar
BOOKING_PROMOCODE_CHECKBOX = 'Booking promocode checkbox'

BOOKING_SHOW_CALENDAR_WIDGET = 'Booking show calendar widget'

BOOKING_SHOW_ROOMS_SELECTOR = 'booking_show_room_selector'

# Booking message for display if searched dates are inside range at booking2. IE: pool info;2016-10-20,2017-02-20@gala;2017-10-20,2017-10-25
BOOKING2_MESSAGE_DATES = 'Booking2 Message Dates'

# Booking message for display if searched dates are inside range at booking2. IE: pool info;2016-10-20,2017-02-20@gala;2017-10-20,2017-10-25
BOOKING3_MESSAGE_DATES = 'Booking3 Message Dates'

BOOKING3_MESSAGE_AGENCY = 'Booking3 Message Agency'

AVOID_NIGHT_BOOKING1 = 'Avoid night booking1'

# If we want all possible combinations of rooms
RADICAL_MERGE = "Radical Merge"

BOOKING1_TEMPLATE_RADIO_BUTTONS = 'Booking1 radio buttons'



# Booking1 avoid worst cases when have same prices (Syntax: rate_identifier;rate_identifier@@@rate_identifier;rate_identifier)
BOOKING1_RATES_AVOID_WORST_OPTIONS = 'Booking1 avoid worst cases'

# If populated it indicates we want to use a calendar for no availability
NO_AVAILABILITY_CALENDAR = "No Availability Calendar"

# Availability Calendar Version
NO_AVAILABILITY_CALENDAR_VERSION = 'Availability Calendar Version'

PRICE_CALENDAR_V2 = 'Price calendar v2'

# Range of dates that hotel will be closed(This is used when a hotel is closed in a X range date) IE: 2016-10-20;2017-02-20
CLOSED_HOTEL = "Closed hotel"

NO_CHANGE_CALENDAR_CLOSED_HOTEL = "Closed hotel no change calendar"

HIDE_MULTIPLE_PROMOTIONS = "Hide Multiple Promotions"

# Redirect to booking0 if hotel has not availability
NO_AVAILABILITY_REDIRECT = "No Availability Redirect"
NO_AVAILABILITY_REDIRECT_CALENDAR = "No Availability Redirect calendar"
NO_AVAILABILITY_REDIRECT_RESTRICTED = "No Availability Redirect Restricted"
POPUP_BOOKNG0 = "popup_booking0"
POPUP_BOOKNG0_V2 = "popup_booking0_v2"

# Button in booking that redirects to a booking 0
BUTTON_BOOKING0_REDIRECT = "Redirect button to booking0"

# If this is activated if the user is in Spanish it will geolocate him to spain
SPANISH_TO_SPAIN_GEOLOCATION = "Spanish to Spain Geolocation"

KEEP_BOOKING0_ORDER_BY_IDS = "keep booking0 order by ids"

# show a special booking engine if hotel has not availability. I.e for Integratins like wupro
NO_AVAILABILITY_WITH_SEARCH = "No Availability With Search"

# Personalized message at redirect from B1 to B0
NO_AVAILABILITY_REDIRECT_MESSAGE = "No Availability Redirect Message"

# Website redirections
WEBSITE_REDIRECTIONS = "Website redirections"

# Website custom domains
WEBSITE_LANDING_DOMAINS = "Website landings domains"

# Temporal website redirections
TEMP_WEBSITE_REDIRECTIONS = "Temp website redirections"

# Flexible dates message
NO_AVAILABILITY_FLEXIBLE_MESSAGE = "No Availability Flexible Message"

# Configurations related to the web page itself (i.e. mobile or alternative Logo)
WEB_CONFIGURATION = 'Web Configuration'

# Indicates that the slider pictures should be replaced by the sections picture (or another section using section1-section2)
SECTIONS_WITH_PICTURE_IN_SLIDER = 'Foto en slider'

KIDS_AGE = 'Edad kids'

NO_KIDS_SINCE = 'No kids since'

# Allows hotels to decide specific options for the babies selector
VALID_BABIES_OPTIONS = 'Valid babies Options'

# Allows hotels to decide specific options for the kids selector
VALID_KIDS_OPTIONS = 'Valid kids Options'
DEFAULT_MAX_CHILDRENS = "Default max childrens"

# Allows hotels to decide specific options for the Adults selector
VALID_ADULT_OPTIONS = 'Valid adults Options'
DEFAULT_MAX_ADULTS = "Default max adults"

VALID_ROOMS_OPTIONS = 'Valid rooms Options'

CALL_CENTER_VALID_ROOM_OPTIONS = 'Call center valid room options'

DEFAULT_ADULTS_NUMBER = 'Numero de adultos por defecto'

# Enable visible redirection of ocupancy
VISIBLE_REDIRECTION = 'Visible redirect'

# Indicates that this is a web page that sells golf, not a normal hotel
GOLF = 'Golf'

# i.e. Suite and Suite (3 personas) will share the same availability (This was a problem at don Pancho when only 1 suite was available and we were looking for several rooms)
SHARED_AVAILABILITY_BY_NAME = 'Share availability by name'

# When more than 1 room is selected, we only show results with combinations of the same room
COMBINE_ONLY_SAME_ROOM = "Combine same room"

# Indicates that this is a web page that is for a camping, not a normal hotel
CAMPING = 'Camping'

# this prop. change the title header in confirmation (booking 4) to: "Su solicitud de reserva se esta procesando" (Ie for campings)
BOOKING_IN_PROCESS = "Booking in process confirmation"

# With this config booking 2 will ask for each picture the webPageProperties to find custom select placeholder
BOOKING2_CUSTOM_PLACEHOLDER = 'Booking2 custom placeholder'

BOOKING2_ALLOW_PACKAGES = 'Booking2 allow packages'

# Allow room upgrades at hotel (Booking2)
BOOKING2_UPGRADE = 'Booking2 upgrade'

# Replace room upgrades image
BOOKING2_UPGRADE_IMAGE = 'Booking2 upgrade image'

# create the virtual service in upselling email
REAL_UPGRADING_IN_UPSELLING = "Real upgrade in upselling"

# show upgrading also in Mis Reservas section
REAL_UPGRADING_IN_MIS_RESERVAS = "Real upgrade in Mis Reservas"

# Allow to add extra info in supplements (Booking2)
BOOKING2_EXTRA_INFO = "Extra Info Booking 2"

# With this we specify that we have a supplement wich have to be paid at hotel
BOOKING2_HOTEL_PAYMENT = "supplement hotel payment"

# Set a filter buttons at booking 2
BOOKING2_FILTER_SELECTOR = "Supplements filter selectors"

# if has room type selector in widget, and this configuration is activated, then the label "Habitaciones" will change in all Process (ie. Habitacioness would go to Parcela)
ROOMTYPE_CHANGE_LABEL = "roomtype change label"

FORCE_BOOKING_WIDGET_ID = "Force booking widget id"

SHOW_BABIES = 'Incluir bebes'
BABIES_IN_RATE = 'Bebes en Tarifa'

# i.e. at Isdabe
PROTECTED_SECTIONS = 'Secciones restringidas'

# Password credit cards
CREDIT_CARD_ENCRYPTION = "Password Tarjetas"

# Defines what ticks can be used in the page
TICKS = "Ticks"

# Indicates if the kids prices depends on the age

MULTIPLE_KIDS_AGES = "Edades multiples"

# TODO: This is not the same as 'Valid babies Options'??
MULTIPLE_BABIES_AGES = 'Edades bebes'

# Indicates if we want the to ask the kids ages (but not use it for anything else)
MULTIPLE_KIDS_AGES_SHOW_ONLY = "Edades multiples solo web"

# Indicates if we want the ti send exactly ages to the booking engine
SEND_AGES_TO_ENGINE = "Send ages to engine"

# it indicates the limit age for not send kids to manager or adapter. Also send ages to engines
FREE_KIDS_BY_RANGE = "free kids by range"

# Indicates IPS that should not be taken into account (i.e. IP from the hotel reception)
IPS_TO_IGNORE_IN_STATS = "IPs a ignorar en estadisticas"

# Alternative statics to save datastore
ALTERNATIVE_DATASTORE_STATS = "Alternativas datastore estadisticas"

# Range of ages to show when we ask the kids ages. i.e. 3:13
AGES_RANGE_IN_WIDGET = "Rango edades kids"

# Custom text for kids range
CUSTOM_TEXT_RANGE_KIDS = "Texto custom range kids"

# Age for the kid to consider it like an adult
AGE_LIMIT = "Kids to adults age"

# Range of ages to show when we ask the babys ages
AGES_RANGE_BABYS_IN_WIDGET = "Rango edades babys"
AGES_RANGE_BABYS_IN_WIDGET_TEXT = "Texto edades babys"

# Show 'x+y Kids' instead of 'x Kids, y Babies'
BOOKING_MERGE_KIDS_NUMBER_IN_WIDGET = "Booking merge kids number in widget"

# Show ages label only once
MERGE_AGES_LABEL_IN_WIDGET = "Merge ages label in widget"

# Babies range search
BABIES_MAX_AGES_ENGINE = 'Babies max ages engine'

# Babies free
SHOW_BABIES_ARE_FREE = 'Babies free'

# Babies ages selector
SHOW_BABIES_AGES_SELECTOR = 'Show babies ages selector'

# Indicates that new bookings need to inform the payment gateway so that the customer is charged
USE_PAYMENT_GATEWAY = "Use Payment Gateway"

# Indicates the amount of money to be charged by the gateway
GATEWAY_PAYMENT_AMOUNT = "Gateway Amount"

DISABLE_GATEWAY_FOR_BONO = "Disable gateway for bono"

USE_ONLY_GATEWAY_FOR_BONOS = "Use only gateway for bonos"

GATEWAY_RULES_BY_COBRADOR = "Gateway rules by cobrador"

# Indicates the IDs of rates which are going to be payed by TPV
GATEWAY_SPECIAL_RATES = "Gateway Special Rates"

GATEWAY_SHOW_DISCOUNT = "Gateway show discount"

# Indicates the IDs of offer which are going to be payed by TPV
GATEWAY_SPECIAL_PROMOTION = "Gateway Special Promotion"

PAYMENT_FAILED = "Payment Failed"

# Send a second email for gateway payments to hotel
GATEWAY_SECOND_EMAIL_CONFIRMATION = 'Gateway second email confirmation'

# Indicate if option bizum is enabled in gateway payment
USE_PAYMENT_GATEWAY_WITH_BIZUM = "Gateway with Bizum"

# Indicate if option amazonpay must be enable into gateway redsis
USE_PAYMENT_GATEWAY_AMAZONPAY_WITH_REDSIS = "Gateway AmazonPay with Redsis"

# Indicate if special rate required to enable bizum payment
USE_ONLY_SPECIAL_RATE_FOR_BIZUM = "Use Only Special Rate For Bizum"

# Indicate if special rate required to enable bizum payment
USE_ONLY_SPECIAL_RATE_FOR_AMAZONPAY_REDSIS = "Use Only Special Rate For AmazonPay in Redsis"

# Indicates if ta special rate required a special TVP (for example for testing purposes or for a reta that must be payed by Paypal for example)
SPECIAL_GATEWAY_FOR_RATE = "special gateway for rate"

# indicates if Sermepa is in charge of save credit cards (if something different to True, it must be the name of the bank for information purposes)
GATEWAY_CREDIT_CARD_BY_TOKEN = "credit cards by token"

# indicates if a special rates requires a specail configuration TESTTPV@@SERMEPA TOKEN TEST
GATEWAY_CREDIT_CARD_SPECIAL_RATES_BY_TOKEN = "credit cards by token special rates"

TOKEN_ONLY_SPECIAL_RATE = "Tokenization Only Special Rate"

# indicates if user has to wait infinitely for the response in booking 4 (iE redunicre)
WAIT_FOR_GATEWAY_RESPONSE_ONLINE = "wait for gateway response online"

# indicates if user has to wait infinitely for the response of an adapter (I.e. )
WAIT_FOR_ADAPTER_RESPONSE_ONLINE = "wait for adapter response online"

# indicate if  a payed reservation  is not correctly in adapter, then make a on request reservation
ONREQUEST_PAYED_RESERVATION_IF_ERROR = "Onrequest payed reservation if error"

# Indicates the IDs of offer which are going to be payed by TPV from Cobrador!
GATEWAY_BY_COBRADOR = "Gateway By Cobrador"

PAYMENT_FAILED_MESSAGE = "payment failed message"

# Indicates if available where to send contact form emails
EMAIL_CONTACT_FORMS = "Email formularios contacto"

# Indicates who receive the message depends the language
EXTRA_EMAIL_LANGUAGE = "extra email language"

# Indicates if available where to send work-with-us form emails
EMAIL_WORK_WITH_US_FORMS = "Email formularios trabaja con nosotros"

# Points to a section with different email customizations
WORK_WITH_US_EMAIL_STYLES = "Work with us email styles"

# Some hotels have differents forms, and they have to be sent to different addresses
EMAIL_FORMS_OPTIONAL = "Email otros formularios"

# Secrect captcha key (utilities.py:recaptcha_confirmation)
SECRET_CAPTCHA_KEY = 'Secret Captcha Key'
PUBLIC_CAPTCHA_KEY = 'Public Captcha Key'

SKIP_SERVER_RECAPTCHA_CHECK = 'Skip server recaptcha check'

BYPASS_IP_CHECKER = 'Bypass IP Checker'

BYPASS_FIELDS_CHECKER = 'Bypass Fields Checker'

RD_STATION = "RD Station"

EGOI_API_KEY = "E-goi API Key"
EGOI_LIST = "E-goi list"
EGOI_EXTRA = "E-goi extra"

EGOI_API_KEY_WORK_FORM = "E-goi API Key Work Form"
EGOI_LIST_WORK_FORM = "E-goi list Work Form"

MAILCHIMP_API_KEY = "Mailchimp API Key"
MAILCHIMP_LIST = "Mailchimp list"
MAILCHIMP_API_KEY_SORTEO = "Mailchimp API Key Sorteo"
MAILCHIMP_AUTOMATIC_DISABLED = "Mailchimp Auto Subscribe Disabled"
MAILCHIMP_INFO_URL = "Mailchimp Info Url"
MAILCHIMP_ADD_TAGS = "Mailchimp Add Tags"  # Separated by '@@': tag1@@tag2@@tag3

ACUMBAMAIL_API_KEY = "Acumbamail API Key"  # Auth token;customer id
ACUMBAMAIL_LIST_KEY = "Acumbamail list"  # List ID
ACUMBAMAIL_API_KEY_SORTEO = "Acumbamail API Key Sorteo"

RAPIDMAIL_API_KEY = "Rapidmail API Key"

ROUTE_BASED_MAILING_API_KEY = "Route Based Mailng Api Key"

# Some hotels want to add the rates in the same way as booking does, i.e. everything is a room (no rates or boards)
USE_BOOKING_STRUCTURE_FOR_RESULTS = "Use Booking Structure"

# In order to be able to create the rates we have some boards in one rate and some in other,
# but we want everything to appear in the same some (i.e. rate1 -> AD, SA, rate1. -> MP, TI)
USE_HOLIDAY_STRUCTURE_FOR_RESULTS = "Use Holiday Structure"

# Booking3 mobile structure versions
BOOKING3_MOBILE_STRUCTURE = "Booking3 mobile structure"

MAILERLITE_API_KEY = "Mailerlite API Key"
MAILERLITE_API_KEY_CUSTOM = "Mailerlite API Key Custom"
FIDELTOUR_SUBSCRIBE = 'Fideltour subscribe'

SENDINBLUE_API_KEY = "SendinBlue API"
SENDINBLUE_LIST = "SendinBlue List"
MDIRECTOR_API_KEY = "Mdirector API"  # Syntax: Consumer-key;consumer-secret@@language-code:listID

# Prevents the system from sending confirmation emails to the client
DONT_SEND_EMAILS_CLIENT = "No email to client"

# Use this to avoid sending Club Register confirmation email and to create user as "pending to complete" in database
LOCK_RATES_AS_PARTIAL_CLUB_REGISTER = "Use lock rates as partial club register"

MAILERLITE_API_KEY_SORTEO = "Mailerlite API Key Sorteo"

SUPPORT_AMERICAN_EXPRESS = 'American Express'

# Disable from credit card type specific card
DISABLED_CREDIT_CARD_TYPES = 'Disable credit card type'

# Add new credit card type
ADD_CREDIT_CARD_TYPES = 'Add credit card type'

# Enable the conditionals to allow book without credit card
CREDIT_CARD_CONDITIONAL = 'Credit card conditional'

NOT_DIRECT_PAYMENT_IF_PAYLINKS = "Not use direct payment if paylinks"

# Disable payment with credit card
DISABLE_CREDIT_CARD = 'Disable credit card'

# Rates enabled for credit card conditional
CREDIT_CARD_CONDITIONAL_RATES = 'Conditional card rates'

PRINT_TAXES_INCLUDED_MESSAGE = 'IVA incluido'

# If a hotel belongs to a hotel chain
HOTEL_CHAIN = "Cadena hotelera"

# Some hotels don't have to send the satisfaction forms
DISABLE_SATISFACTION_SURVEY = "Disable satisfaction survey"

# Satisfaction Survey Version
SATISFACTION_SURVEY_VERSION = 'Satisfaction survey version'

SURVEY_BACKGROUND = 'Survey background'

CUSTOM_STYLES_LOGO_SURVEY = 'Custom styles logo survey'

NOTIFICATION_SURVEY_VERSION = 'Notification survey version'

CUSTOM_SURVEY_THANKS = 'custom survey thanks'

# Merchant Center Survey ( This is used to send surveys to clients from google )
MERCHANT_CENTER_SURVEY = 'Merchant center survey'

# Some hotels have special links for the satisfaction surveys
LINK_SATISFACTION_SURVEY = "Link satisfaction survey"

# Satisfaction surveys password
SATISFACTION_SURVEY_PASSWORD = "Satisfaction survey password"

# Satisfaction surveys queries form with multiple hotels /survey/report?filter=true
SATISFACTION_SURVEY_NAMESPACES = "Satisfaction survey namespaces"

# Email Survey
EMAIL_SURVEY = "Email Survey"

SATISFACTION_SURVEY_GRADE = "Satisfaction Survey Grade"

INCLUDE_CONDITIONS_IN_RESERVATION = "Condiciones tarifa en reserva"
CONDITIONS_BY_START_DATE = "Condiciones tarifa por fecha de entrada"



# Cancellation hour
CANCELLATION_HOUR = 'Cancellation hour'

# Custom cancellation days add or substract
CANCELLATION_DAYS_CALCULATION = "Cancellation days custom calculation"

SHOW_CVV_IN_CARD_DETAILS = "Mostrar CVV de tarjeta"

HIDE_CVV = 'Hide CVV'

CC_OWNER_NAME = "ccownername"

NUM_FACTU = "num factura booking3"

NUM_FLIGHT = "num flight booking3"

HOUR_FLIGHT = 'hour flight booking3'

BILLING_DATA = "billing data booking3"

CARD_HOLDER_FROM_BILLING = "Card holder from billing"

TOTAL_BOOKING3_STYLE = "total_booking3_style"

# Rates for loged users (CLUB VERSION)
RATES_LOGIN_USERS_4_CLUB = "Login club rates"

# Mini club version
MINI_CLUB_NEWSLETTER = "Mini club newsletter"

CLUB_REGISTER_WELCOME_POPUP = "Club register welcome popup"

CLUB_SEND_PASSWORD_POPUP = "Club send password popup"

# Rates for loged users
RATES_LOGIN_USERS = "Login user rates"

# Customize the banner top of booking3 for club users
BOOKING3_TOP_BANNER_CLUB = "Booking3 top banner club"
BOOKING3_TOP_CUSTOM_BANNER = "Booking3 top custom banner"
BOOKING3_TOP_CUSTOM_POINTS = "Booking3 top custom points"

# Customize club register form
CUSTOM_CLUB_INPUTS = "Custom club inputs"

# If populated, the rates defiened in "Login user rates" are completely hidden for non users
RATES_LOGIN_HIDDEN = "Login hidden rates"

# Indicates that instead of merging rooms in a standard fashion we want to be able to explicitly define how to do it
USE_MANUAL_MERGE = "Merge Manual"

# If this property is available we must hide the night price column in resultList.html
HIDE_NIGHT_PRICE_COLUMN = "no night column price"

# this property indicates the color of offers in booking process
COLOR_OFFERT_BOOKING_PROCESS = "offers color booking process"

# Hide offers percentage
HIDE_OFFERS_PERCENTAGE = 'Hide offers percentage'

# this property hide kids in booking widgets
ONLY_ADULTS_IN_WIDGET = "only adults"

# Hide rooms text in booking process
HIDE_BOOKING_ROOMS = "Hide booking rooms"

# show packages number in tab
PACKAGES_TAB_NUMBER = "Packages tab number"

# Web Assistant:
PHONE_WEB_SUPPORT = "Telefono Asistencia Web"
PHONE_INTERNATIONAL_SUPPORT = "Telefono Asistencia Internacional"
SHOW_CALL_ME_WEB_SUPPORT = 'Call-me web support'
SHOW_CALL_ME_WEB_SUPPORT_MOBILE = 'Call-me web support mobile'

#Not spanish contact phone
NOT_SPANISH_CONTACT_PHONE = 'international contact phone'

#text in front of the phone number
CALL_US_TEXT_BEFORE_PHONE = 'call us in booking header'

# it decides in whitch languages is available the PHONE_INTERNATIONAL_SUPPORT
# even if HIDE_WEB_SUPPORT is true, when LANGUAGES_FOR_INTERNATIONAL_SUPPORT is configured, the phone will appear in this language
LANGUAGES_FOR_INTERNATIONAL_SUPPORT = "languages for international support"

WEB_SUPPORT = "Asistencia Web"
HOTEL_PHONE_MOBILE_SUPPORT = "Telefono Hotel Asistencia Movil"
HIDE_BOOKING_WEB_SUPPORT = "hide booking web support"
HIDE_WEB_SUPPORT = "hide web support"

# if exist is because Booking0 is ON and it indicates the appIDs (iE: app-1;app-2;app-3)
BOOKING0_IDS = "booking0 ids"

# Booking0 for an only web, host by specifics sections
BOOKING0_BY_SECTION = "Booking0 by sections"

# Replace Logo URL in booking mobile
REPLACE_MOBILE_URL_LOGO = "Replace booking mobile logo url"

# Regimen at booking0 display
BOOKING0_REGIMEN_DISPLAY = 'regimen en booking0'

# indicates if the default language is different to SPANISH, and in this case, indicates whitch
DEFAULT_WEB_LANGUAGE = 'default web language'

# indicates the language hotel email confirmation
MANAGEMENT_LANGUAGE = 'Language Management'

# Set a redirection of language if match in booking1 and booking0. FORMAT: ORIGIN1-TARGET1; ORIGIN2-TARGET2
REDIRECTION_BOOKING_LANGUAGE = 'booking language redirections'

# If we need to change the language  in logo in case the language selected doesn't exist in the web. FORMAT: ORIGIN1-TARGET1; ORIGIN2-TARGET2 but with language code. Ex: en-es; fr-de
LANGUAGE_REDIRECTION_FROM_BOOKING = "Language redirection from booking"

# Set a redirection of language if match geolocation with dict. Values accepted ALL, 'SPANISH-ENGLISH-FRENCH....'
REDIRECTION_GEOLOCATION_LANGUAGE = 'geolocation language redirections'

# Redirect to language that is preferred by browser
PREFERRED_LANGUAGE_REDIRECTION = 'Preferred language redirection'

# Popup with geolocation
POPUP_GEOLOCATION = 'Popup geolocation'

# indicates if we want to round all the decimals in all the prices during the booking process
TRUNCATE_DECIMAL_BOOKING = "truncate decimals booking"

ROUND_DECIMAL_BOOKING = "round decimals booking"

# Allows us to show promotion results as new rates
USE_RATE_CONDITIONS_IN_PROMOTION = "Rate condition in promotions"

# Rooms customs rate conditions
ROOMS_CUSTOM_RATE_CONDITIONS = 'Rooms custom rate conditons'

# In case we are using promotions as rates, but we only want to show the ones that are promoted
FILTER_NON_PROMOTED_RATES = "Remove non promoted rates"

# A link we put in the booking confirmation
# ie. hotel don pancho link with transfer to the airport
BOOKING_4_EXTRA_LINK = "link extra booking 4"

BOOKING_4_PRECHECKIN_LINK = "link precheckin booking 4"

NUM_NIGHTS_EXTRA_LINK = "link extra booking 4 num nights"

BOOKING_4_EXTRA_LINK_AVOID_PROMOCODE = "link extra booking 4 avoid promocode"

BOOKING_4_EXTRA_LINK_ALL_COUNTRIES = "link extra booking 4 all countries"

# A link we put in the booking confirmation, but is a new version:
# not just a link, first we must do a wget and then,  insert the content received
# ie. hotel ohtels for activities widgetÑ http://wdgmul.hotelbeds.com/wdgfront/generator/widget.html?userid=Lesoliveres&config=lesoliveres_confirmationemail_act&language=es&currency=EUR&datefrom=YYYYMMDD&dateto=YYYYMMDD&adults=N&children=N&ages=N1,n2,n3,
BOOKING_4_EXTRA_LINK_GET = "link extra booking 4 GET"

# if exist, we only show the first board in rates indicated. (we'll show a button to show all)
SHOW_ONLY_FIRST_BOARD = "Mostrar solo primer regimen"

SHOW_ONLY_BEST_RATE_BY_POLICY = "Show only best rate by policy"

# If available it includes an adwords conversion tracking, value should be: id;label
ADWORDS = "adwords"

STANDARD_ADWORDS = "standardAdwords"

# If available it includes and adwords conversion event tracking, value should be: name1@@name2@@....
ADWORDS_TRACKING = "adwords event tracking"

# If available it includes a traking for the company 'My Hotel Shop'
MYHOTELSHOP_TRACKING = "myhotelshop tracking"

# Adwords remarketing tracking code, ej. 1999999228
ADWORDS_REMARKETING = 'Adwords remarketing'

HIDE_DAILY_PRICES_IN_CONFIRMATION_EMAIL = 'Ocultar precios diarios'

SHOW_GEOLOCATION_IN_CONFIRMATION_EMAIL = 'Mostrar geolocalizacion reserva'

# If defined, callcenter users can book without having to go through the gateway
NO_GATEWAY_FOR_CALLCENTER = "No Gateway Callcenter"

# if defined, the link in tyhe logo uin the booking process will be this
HOST_LOGO_BOOKING = "Host logo booking process"

# In case we want to use an alternative manager url for this hotel
ALTERNATIVE_MANAGER = "Use alternative manager"

# Alternative manager for no availability and flexible searchs
ALTERNATIVE_BACKGROUND_MANAGER = "Use alternative background manager"

ALTERNATIVE_MANAGER_BOOKING2 = "Use alternative manager booking2"

ALTERNATIVE_MANAGER_BOOKING4 = "Use alternative manager make reservation"

# In case of error we need to go to a specific location
ALTERNATIVE_BACKUP_MANAGER = 'Use alternative backup manager'

# if populate, we have to check again availability before make reservation. I not available, we return the user to anew search
DOUBLE_AVAILABILITY_CHECK = "Double availability check"

# In case we want to run a test against a different booking engine (only when promocode 'TEST_' is included).
TEST_PROMOCODE_MANAGER = 'TEST_ promocode manager'

# Save reservation button for booking 3
SAVE_RESERVATION_BUTTON = u'Save reservation button'

# Rate check: indicates the value of the hotel in priceseekers
PRICESEEKER_HOTEL = "priceSeekerHotel"

# if False, dont insert the script, otherwise, the script is available
RATECHECK = "ratecheck"

# if False, dont insert the script, otherwise, the script is available
RESCUESEEKER = "rescueseeker"

# If its enabled the emails will be sent from hotel-webs
RESCUESEEKER_EMAIL = 'Rescueseeker email'

# if True, we check the prices from ratecheck and we replace them 'onair' in booking process (b1)
PARITYMAKER = "paritymaker"

# contains the words that if appears into Room, boards, or Rates, never make the parity (sep by @@)
NO_PARITYMAKER = "No Parity Maker"

# if True, we check the prices in booking1 from ratecheck and if is higher than ours, we don't let make the reservation.
PANICPARITY = "Panic Parity"

# if populate, paritymakers takes new tample with given styles: iE: background-color:#0000FF;color: white; opacity: 0.5;
PARITYMAKER_CUSTOMIZED = "paritymaker customized"

# it changes the color of the span that appears in the offers with the parity maker
PARITY_SPECIAL_OFFER_COLOR = "paritymaker special offer color"

# Disable results of given rate at booking 1 (ie: 321.10;220;110)
FILTER_INVALID_PRICES = "Filter booking1 price"

# Filter the packages in availability calendar
HIDE_PACKAGES_AVAILABILITY_CALENDARS = "Filter packages in availability calendars"

# Custom price format
CUSTOM_PRICE_FORMAT = 'Custom prices format'

# If Defined, we use booking as our main booking engine, note that the value has to be the same as configured at Price Seeker
USE_SCRAPING_ENGINE = "Use scraping Engine"

# If Defined we support multiples hotels at booking1
MIX_MULTIPLE_HOTELS = "Mix multiple hotels"

DONT_MIX_IF_PROMOCOCE = "dont mix if promocode"

# Show mix separetly by hotels
MIX_MULTIPLE_HOTELS_SEPARATED = "Mix multiple hotels separated"

# Used to modify prices at scraping
ANTI_SCRAPING = "Antiscraping"

# By default the search can't be more than 120 days, if this is defined we can allow a longer or shorter interval for a specific hotel
MAX_SEARCH_LENGTH = "Max search days"

# Filter Max search days by namespace
MAX_SEARCH_LENGTH_HOTEL_FILTER = "Max search days hotel filter"

# If present, after ok rooms in booking1, others room without availabilit will be shown
SHOW_NO_AVAILABLE_ROOMS = "Show no available rooms"

# Set if has to show description of a room at Booking 4
ROOM_DESCRIPTION_B4 = 'Booking 4 room description'

# Shows always the content of the section after the comments
DEFAULT_B4_CONTENT = 'Default b4 content'

# a RE must be inserted there. this is NOT a filter during ALL the process, it is just in the double calendar (for avoid lower prices)
FILTER_ROOM_CALENDAR = "Filter room calendar"

# indicates the seciotn (in spanish) that will contruct the page 404, if not, just redirect to HOME like always
CUSTOM_404_PAGE = "404 page"

PERMANENT_REDIRECTION_404 = '404 permanent redirect'

# if populate, we accetp all promocodes as valid
ACCEPT_EXTERNAL_PROMOCODES = "Accept external promocodes"

# Pop up will displayed when you want to modify a reservation
MODIFY_RESERVATION_POPUP = "modify reservation popup"

# if internal identifier rate is populate, the this rate is always populate
RATES_ALWAYS_CANCELABLE = "Rates always cancelable"

# Allowes real modifications from the customer in the website
MODIFY_RESERVATION = "Modify reservation"

BLOCK_MODIFICATION_BY_SOURCE = "Block reservation modification by source"
BLOCK_MIS_RESERVAS_BY_DATE = "Block mis reservas by date"

MODIFY_RESERVATION_IN_ADAPTER = "modification reservation adapter"

MY_BOOKING_CANCELATION_POPUP = "Popup booking cancelation"

# Select my booking section type
MY_BOOKING_SECTION_VERSION = 'My booking section'

# Transfer bank payment method
TRANSER_BANK_PAYMENT = 'Bank transfer'

TRANSER_BANK_PAYMENT_ONLY_CALLCENTER = 'Bank transfer only callcenter'

TRANSER_BANK_AND_TPV_INCOMPATIBILITY = 'Bank transfer and TPV incompatibility'

# Only for fuerte hotels. Enable transfer bank conditional without web section
TRANSER_BANK_PAYMENT_WITHOUT_WEB_SECTION = 'Bank transfer without web section'

HOTEL_PAYMENT_ONLY_CALLCENTER = 'Hotel payment only callcenter'

BANK_TRANSFER_INFO_RATE_FILTER = 'Bank transfer info rate filter'

# Only for fuerte hotels. Enable paypal as option
PAYPAL_NO_RELOAD = "Paypal no reload"

# Bank transfer prefix of identifier
BANK_TRANSFER_PREFIX_IDENTIFIER = 'Bank transfer prefix identifier'

# Booking identifier custom prefix
CUSTOM_IDENTIFIERS_PREFIX = "Custom identifiers prefix"

# the same but only for NRs
NR_CUSTOM_IDENTIFIERS_PREFIX = "NR Custom identifiers prefix"

# Pre-stay email cron job ('True' or '#a0bb31'); If its true
PRESTAY_CRONJOB = 'prestay'

# Set the number of days before the arrival date to send the pre-stay email to the hotel and the email to send
PRESTAY_HOTEL_SEND = "Prestay hotel send"

# Extra code at booking process
BOOKING_EXTRA_CODE = 'Booking process extra code'

# Extra code at booking process header
EXTRA_CODE_BOOKING_HEADER = 'Extra code booking header'

# Messages for booking 1
VERY_ASKED_ROOM = 'muy solicitado'
JUST_BOOK_MESSAGE = 'recien reservado'

# indicates the rooms (in spanish) that we won´t apply the condiguration
VERY_ASKED_ROOM_FILTER = 'muy solicitado filter'
JUST_BOOK_MESSAGE_FILTER = 'recien reservado filter'

# Banners booking 1
BANNERS_BOOKING_1 = 'banner_transparent_booking1'

BANNERS_BOOKING_CAROUSEL = 'banner_booking_carousel'

# Promocode text - Used for booking 4 confirmation special text
BOOKING4_PROMOCODE_SPECIFIC_TEXT = 'booking4 promocode text'

BOOKING_CONFIRMATION_PRICES_TABLE = 'booking4 prices table'

BOOKING4_SOURCE = 'Booking4 email source'

# No dispo message popup for booking 1
NO_DISPO_MESSAGE_POPUP = 'no dispo message popup'

# special hotel closed message popup for booking 1
NO_DISPO_SPECIAL_MESSAGE_POPUP = 'hotel closed message popup'

# Booking 1 See more room popup version
BOOKING1_SEE_MORE_VERSION = 'Booking1 popup version'

# Booking 1 Cookie popup
BOOKING1_COOKIE_POPUP = 'Booking1 cookie popup'

# Booking 3 Cookie popup
BOOKING3_COOKIE_POPUP = 'Booking3 cookie popup'

# Booking 3 extra text under total amount
BOOKING3_AMOUNT_INFO = 'booking3_amount_info'

# Booking1 use the promotion description as the name of the tab
BOOKING1_PROMOCODES_TABS = 'Promocodes as custom tabs'

# Allows you to configure a message that is displayed when a search includes the
# Format is: nombreSeccion;fecha1;fecha2;...;fecha3
RESULTS_WARNING_MESSAGE_POPUP = 'results message popup'

# valid for modification booking engine in Booking1 (if you want it in another booking engine, just copy functionalty)
LIMIT_DATEPICKERS_NUMDAYS = "limit datepickers numdays"

MOBILE_DATEPICKERS_MONTHS_COUNT = "Mobile datepickers months count"

CUSTOM_DATEPICKER_START_DAY = "Custom begin week day datepicker"

FLEXIBLE_CALENDAR_LIMIT_MONTHS = 'flexible_calendar_limit_months'

# it will delete from searches results all rooms Y, Z... when a kid is OLDER than X
# format: X@@Y@@Z... i.E.: 7@@Apartamento de 1 dormitorio con vistas (2 adultos + 1 niño)
# in this example those rooms will be deleted for kids of 8, 9, 10.. years old
FILTER_ROOM_AGE_GREATER = 'filter room age greater'

# it will delete from searches results all rooms Y, Z... when a kid is YOUNGER than X
# format: X@@Y@@Z... i.E.: 8@@Apartamento de 1 dormitorio con vistas (3 adultos)@@Apartamento de 1 dormitorio con vistas (1 o 2 adultos)
# in this example those rooms will be deleted for kids of 1, 2... 7 years old
FILTER_ROOM_AGER_LOWER = 'filter room age lower'

# Hotel with forfait at booking process
FORFAIT = 'forfait'

# Hotel with equipment rental at booking process
EQUIPMENT_RENTAL = 'equipment rent'

# SSL Verification response
SSL_RESPONSE_VERIFICATION = 'SSL Response verification'

# it is no mandatory to white comments when cancelling
NO_CANCEL_REASON_MANDATORY = 'No cancel reason'

# In case we want to do some A/B Testing
# i.e. 0,1,2,3,4:marinas-abtesting
AB_TESTING = 'AB_Testing'

# This is used to know every moment hotel that has reservations (My bookings, etc..)
AB_TESTING_MAIN_HOTEL = 'AB Testing reservation hotel'

# AB Testing Booking Process
AB_TESTING_BOOKING_PROCESS = 'AB Testing booking process'

# if populate we save link in extra Info. I.e. used by webcamp
SAVE_CC_DATAS = 'Save CC datas'

# FlipTo Code
FLIPTOCODE = "Flipto code"

# if populated, T_tarjeta_reserva_no_valida_mensaje has no : 'For security reasons, you should never send your bank details by email.'
CC_NO_RECOMENDATION = 'CC no recommendation'

# if populate the email sent to customer will have a link to re insert his datas
CC_NO_VALID_CUSTOMER_RETRY = "no valid CC customer retry link"

EMAIL_REGISTRATION = 'Mail registration'

# Section where get icons for rooms
ROOMS_ICONS = "Rooms Icons"

#to differenciate between hotels with or without svg logos
HOTEL_WITH_SVG = "hotel with svg"

HIDEL_ALL_AVAILABILITY_CALENDAR = "Hide ALL No Availability Calendar"

HIDEL_ROOMS_AVAILABILITY_CALENDAR = "Hide Rooms Availability Calendar"

# this configuration render web template with Jinja
USE_JINJA = "jinja_template"

# this configuration load the new version mobile
MOBILE_TEMPLATE = "Mobile Template"

MOBILE_SLIDER_VERSION = "Mobile slider version"

MOBILE_SLIDER_DISABLE_SCROLLDOWN = "Mobile slider disable scroll down"

MOBILE_SLIDER_LINK = "Mobile slider link"

MOBILE_FOOTER_LINK = "Mobile footer link"

NO_RESIZE_SLIDER_MOBILE = "No resize slider mobile"

MOBILE_MENU_EXTRA_SECTIONS = "Mobile menu extra sections"

# if we used HIDEL_ALL_AVAILABILITY_CALENDAR, we can choise de room who we wants to show first
FIRST_ROOM_TO_SHOW_IN_AVAILABILITY_CALENDAR = "First Room To Show In Availability Calendar"

HOTEL_COUNTRY_LOCATION = 'Hotel Country Location'

COUNTRY_LOCATION_CUSTOM_TAX = 'Country location custom tax'

AUTOMATIC_GEOLOCATION_TAX = 'Automatic geolocation tax'

PRECISION_DECIMAL_CURRENCY = 'Precision decimal currencies'

HOTEL_COUNTRY_LOCATION_TAX_INCLUDED = 'Hotel Country Location Tax Included'

BOOKING3_DISABLE_COUNTRY_PRESELECTION = "booking3 disable country preselection"

# this configuration avoid version mobile
RESPONSIVE_WEB = "Responsive Web"

# If exist and his value it's True, then the hotel use datatrans pci dss
PCI_TOKEN = "PCI_TOKEN"

DATATRANS_NO_SHOW_API_CONFIG = "datatrans no show api"

# if populate, and PCI_TOKEN paraty is in charge of save (encrypted) the CVV
PARATY_SAFE_CVV = "Paraty save cvv"

# Section where the content for the second email is loaded
SECOND_MAIL = "Second mail"

# Replace the list of sections to another link
REPLACE_LINK_MENU = "Replace Link Menu"

# Replace the list of sections mobile to another link
REPLACE_LINK_MENU_MOBILE = "Replace Link Menu Mobile"

# If enabled will request to optimize webpage
PAGESPEED_CONFIG = 'PageSpeed Optimization'

# Sometimes we want to use a pull integration but we want to inject some promotions in manager
MANAGER_AS_PROXY = "Manager as proxy"

BOOKING3_EXTRA_CODE = "Booking3 extra code"

RETARGETING_HOME_PAGE = 'Retargeting home page'

NEVER_ADD_PREFIX_TO_LOC = "Never add prefix to identifier"

# Replace newsletter list in booking
BOOKING_CUSTOM_NEWSLETTER = "booking custom newsletter"

# Booking1 popup with custom properties
BOOKING1_CUSTOM_POPUP = "Booking1 custom popup"

# Booking1 share buttons
BOOKING1_SHARE_LNKS = "booking1 share links"

# ========= Members Club Configs ===========
# Add user club to newsletter list (Syntax 'mdirector:5349;2bac3fdf0226e239da60@@en:81;es:80', 'acumbamail@@es:511385;en:511386')
CLUB_NEWSLETTER = 'Newsletter Club'

REMOTE_URL_CREATE_USER_CLUB = 'Remote url create user club'

# Club pdf generator
CLUB_PDF_GENERATE = 'Club PDF Generate'

# Membersclub - > Indicates in whitch namespace are stored the users member
MEMBERS_CLUB_NAMESPACE = "memberclub namespace"

# Membersclub -> Indicates if needs to be sent the email as other hotel
MEMBERS_CLUB_NAMESPACE_EMAIL = "memberclub namespace email"

# MembersClub identifier
MEMBERS_CLUB_BOOKINGS = "memberclub booking"

#Show if user exists when recovery password
SHOW_IF_CLUB_USER_EXIST_RECOVERY_PASSWORD = 'Show if club user exists in recovery password'

# Membersclub - > Indicates in whitch section of the namespace are stored the html that will be include in the confirmation email
MEMBERS_CLUB_SECTION = "membersclub conditions section"

# Indicate if an hotel have Club (This will activate multiple functionalities)
MEMBERS_CLUB = "Members Club"

# Use this to enable recovery password popup content for complaint users
COMPLAINT_CHANNEL = 'Complaint channel'

# Notify to user that points left is 0
MEMBERS_CLUB_POINTS_ZERO_NOTIFICATION = "Members Club Zero Points Notification"

# Membersclub no
MEMBERS_CLUB_NO_POINTS_IN_CONFIRMATION = "Members Club No Points Confirmation"

# Show club member in comments
RESERVATION_CLUB_COMMENTS = "Reservation Club Comments"

MEMBERS_CLUB_SECTIONS = "Club Section"

HOTEL_NAME_IN_CLUB_TRANSACTIONS = "Hotel name in club transactions"

MEMBERS_CLUB_LEVELS = 'Members Club Levels'

CLUB_POINTS_ONLY_ENTRY = "Club points only entry"

REGISTRATION_BONUS_POINTS = "Puntos de bonificacion por registro"

# This config is used to check Park Royal users from old website
CHECK_USER_FROM_ROIBACK = "Check user from roiback"

# This config is used to check if the user is from a different database, so we need to reset password
CHECK_USER_FROM_OLD_DATABASE = "Check user from old club database"

# Club departure automatic promocode
DEPARTURE_CLUB_PROMOCODE = "Departure Club Promocode"

# Departure Customer Promocode
DEPARTURE_PROMOCODE = "Departure promocode"

# Config to retreive users from external companies
ALTERNATIVE_CLUB_CONTROLLER = "Alternative Club Controller"

# Will round the points retreived by a user
ROUND_CLUB_TRANSACTIONS = "Club transactions round"

MASTER_CLUB_PASSWORD = 'Club master password'

# User rates locked
USER_RATES_LOCKER = 'User rates locked'

# Banner for users login
LOGIN_CLUB_BANNER = 'Login club banner'

# ========= End Members Club Configs =========

CUSTOM_SITEMAP = "Custom Sitemap"

SHOW_ROOM_INFO_BOOKING2 = "Show room info booking2"

CUSTOM_COLOR_SURVEY = "survey custom color"

SHOW_BABIES_MOBILE_VERSION = "Show babies mobile version"

FORMAT_DATE_BOOKING_PROCESS = "Format date booking"

SHORT_MONTH_NAME_IN_MOBILE_WIDGET = "Short month name in mobile widget"

INTEGRATION_NAME_FOR_ADWORDS = "integration name for adwords"

PRECHECKIN = "precheckin"

# Custom text in booking2 upselling
CUSTOM_BOOKING2_TEXT_UPSELLING = "Custom booking2 text upselling"

# Additional services tabs
ADDITIONAL_SERVICES_v2_TABS = "Additional services v2 tabs"

# Additional services tabs with scroll
ADDITIONAL_SERVICES_v2_TABS_SCROLL = "Additional services v2 tabs scroll"

# Change flag for code in footer mobile
NO_FLAG_IN_FOOTER_MOBILE = "No flag in footer mobile"

# new search when session is expired
NO_SESSION_EXPIRE = "No session expire"

# special session expire time
SPECIAL_SESSION_MAX_AGE = "special session max age"

# Cancel bookings on request instead of inmediatly
CANCELLATION_ON_REQUEST = "Cancellation on request"

# timeout for searches
TIME_OUT_SEARCH = "search timeout"

DATALAYER_STANDARD_ECOMMERCE = "Datalayer standard ecommerce"
DATALAYER_UNIVERSAL_ECOMMERCE = "Datalayer universal ecommerce"

# Show booking price in original currency and selected currency
CURRENCY_BOOKING_3 = "Currency booking 3"

# Show the number of nights you select in booking process widget
SHOW_NUMBER_NIGHTS_BOOKING_PROCESS_WIDGET = "Show number nights booking process widget"

HIDE_PRIVACY_POLICY_BOOKING_3 = "Hide privacy policy booking3"

# Customize a rate with a message behind it at booking3
RATE_B3_CUSTOM_MESSAGE = 'Rate booking3 custom message'

# Customize a rate with a message behind it at booking3
B3_COMPLETE_CUSTOM_MESSAGE = 'Booking3 complete custom message'
B3_COMPLETE_NO_DISCOUNT_NO_MESSAGE = 'Booking3 No Discount No message'

INTERNAL_PAGE_ERROR = "Internal page error"

BOOKING3_POPUP_AUTILFILL_INFORMATION = "Booking3 popup autofill information"

NEWSLETTER_POPUP_THANKS = "Newsletter popup thanks"

NEWSLETTER_EMAIL_NOTIFICATIONS = "Newsletter email notifications"

NEWSLETTER_THANKS_EMAIL = "Newsletter thanks email"

NEWSLETTER_CAPTCHA_MANDATORY = 'Newsletter captcha mandatory'

NEWSLETTER_BANNER_DISABLE ='Newsletter banner disable'

NET_RATES_CALCULATOR = "net rate calculator"
TAX_INCREMENT_INCLUDED = "tax increment already included"
ACCOMODATION_TAX = "Accomodation taxes"
ACCOMODATION_TAX_INCREMENT_BY_PAX_NIGHT = "accommodation tax pax night"
NOT_INCLUDE_ACCOMODATION_TAX = "not include accomodation tax"
SHOW_TAX_PRICE = "show tax price"

INSTANT_RESERVATION = "Instant reservation"

CALLCENTER_EMAIL_SUBJECT = "callcenter subject email"

AGENCY_EMAIL_SUBJECT = "agency subject email"

CUSTOM_CALL_EMAIL = "Custom call email"

MIN_AGE_ADULT_INFO = "Min age adult info"

ONLY_ONE_ADDITIONAL_SERVICE_FILTER = "Only one additional service filter"

SORT_SERVICES_BY_PRICE = "Sort services by price"

BOOKING3_SORT_SUPLEMENTS_BY_DATE = "Booking3 sort supplements by date"

SHOW_PRICE_IN_FREE_SERVICES = "Show price in free services"

#TODO: This can be removed?
COOKIES_CONFIRMATION = "Cookies confirmation"

SHUTDOWN_WEB = "Shutdown web"

FLOATING_PROMOCODE = "Floating promocode"

PROMOCODE_TOOLTIPS = "Promocode tooltips"

ALLOW_PETS = "Allow pets"

DEFAULT_CURRENCY = "Default currency"

# To add a message in booking widget footer
BOOKING_FOOTER_MESSAGE = "Booking footer message"

# Add a message to booking process footer under legal text
BOOKING_PROCESS_FOOTER_MESSAGE = "Booking process footer content"

CUSTOM_EMAIL_CLUB_REGISTER = "Custom email club register"

HAS_BOOKING_SUBDOMAIN = "has booking subdomain"

NEW_BOOKING_PROCESS_MOBILE = "New booking process mobile"

NEW_ENGINE_MOBILE = "New mobile engine"

MOBILE_DATEPICKER_V1 = "Mobile datepicker v1"

NEW_PROCESS_MOBILE_FOOTER_COLOR = "New booking mobile footer color"

CUSTOM_MAPMARKER_BOOKING_MOBILE = "Custom booking map marker"

EXTRA_BOTTOM_SCRIPT = "extra bottom script"

DISABLE_TRIPADVISOR_PIXEL = "Disable tripadvisor pixel"

HIJIFFY_API = "Hijiffy API"

BOOKING_POPUP_MOBILE = "Booking popup mobile"

POPUP_CANCEL_MODIFY_BOOKING = "Popup cancel modify booking"

# if you need NOT to do an automatic redirection to www for  a certain subdomains, configure this option. In example for excel hotels: heights, hills, mirage, palm
AVOID_AUTOMATIC_WWW_REDIRECTION = "avoid automatic www redirection"
AVOID_AUTOMATIC_HTTPS_REDIRECTION = "avoid automatic https redirection"

NEW_BOOKING_PROCESS_MOBILE_EXCLUSIVE_IP = "New booking process mobile exclusive IP"

LOGIN_AGENCIES = "login agencies"
LOGIN_AGENCIES_MOBILE = "login agencies mobile"
REMOTE_LOGIN_AGENCIES = "remote login agencies"

PAGESPEED_CACHE = "Pagespeed cache"

# Sometimes we want to save pages in cache as different pages based on a parameter in the url
# Param1;paramN
USE_PARAM_AS_PATH_IN_CACHE = "Use param as path in cache"

AUTOMATIC_FLOATING_PICTURE = "Automatic floating picture"

PROMOCODE_INFO_IN_WIDGET = "Promocode info in widget"

REPLACING_FRIENDLY_URLS = "Replacing friendly urls"

# If we are going to have the booking process inside an iframe (i.e. Isla Mallorca)
ALLOW_INSIDE_IFRAME = 'Allow inside iframe'

# Hotel doesn't want to activate security directives
REMOVE_SECURITY_HEADERS = 'Remove security headers'

TRANSFORM_CREDIT_CARD_FIELD = "Transform credit card field"

SITEMAP_INJECTION = "Sitemap Injection"

FORCE_NEWS_FROM_HOTEL = "Force news from hotel"

BOOKING3_ROOM_PRICE_SUMMARY = "Booking3 room price summary"

BOOKING3_CARDS_STYLES = "booking3 cards styles"

# ========= Analytics configs =========#
# Enable bing ads tags
BING_ADS = 'Bing Ads'

# Facebook Pixel ID
FACEBOOK_PIXEL_ID = "Facebook Pixel Id"

FACEBOOK_API_TOKEN = "Facebook API token"

# Google Tag Manager ID
GOOGLE_TAG_MANAGER_ID = "Google Tag Manager Id"

DISABLE_GTAG_CONSENT_MODE = 'Disable gtag consent mode'

DEFAULT_GTAG_CONSENT_GRANTED = 'Default gtag consent granted'

GOOGLE_ANALYTICS_ID = 'Google Analytics Id'

GOOGLE_ANALYTICS_ECOMMERCE_ENHANCED = 'Google Analytics Enhanced'

GTAG_GOOGLE_ANALYTICS_ID = 'Gtag Google Analytics Id'

ANAlYTICS_AVOID_PAGEVIEW = 'Google Analytics avoid pageview'

ANALYTICS_RECOVER = "Analytics recover"

ANALYTICS_LINKER = 'Analytics linker'

ANAlYTICS_ONLY_ECOMMERCE = 'Analytics only ecommerce'

TRACKING_ACROSS_DOMAINS = 'Seguimiento Multidominio'

HOTJAR_ID = 'Hotjar Id'

# Some hotels want to show club rate in the calendar, but not all
SHOW_TARIFA_CLUB_IN_CALENDAR = 'Show Club Rate in Calendar'

# Hotel Ads ID
HOTEL_ADS_ID = 'Hotel ads'

#Account id used for Hotel Ads
HOTEL_ADS_ACCOUNT_ID = 'Hotel ads account id'

# Google Ads conversion label id
GOOGLE_ADS_CONVERSION_LABEL = 'Google ads conversion id'

CAMPAIGN_TRACKING_AT_ALL_PROCESS = 'Campaign tracking at all process'

GCLID_AT_ALL_PROCESS = 'Gclid at all process'

COOKIEBOT = 'Cookiebot'

COOKIES_NOT_FULL_SCREEN = 'Cookies not full screen'

COOKIES_CONFIGS = 'Cookies configs'

# ========== Booking process configs ============

# Disable booking1 minification
DISABLE_B1_MINIFY = 'Disable b1 minification'

# ========== End booking process configs =========#

# ========== External Enterprise ============
# Soft Concept config to enable
SOFTCONCEPT_PROCESS = 'Softconcept process'

SOFTCONCEPT_PROCESS_URL = 'Softconcept process url'

EXTERNAL_CONTROLLER = 'External Controller'

# ========== Social Media Configs ==========#

FLICKR_ID = 'Flickr Id'
FACEBOOK_ID = 'Facebook Id'
YOUTUBE_ID = 'Youtube Id'
LINKEDIN_ID = 'Linkedin Id'
TWITTER_ID = 'Twitter Id'
BLOGGER_ID = 'Blogger Id'
PINTEREST_ID = 'Pinterest Id'
LINE_ID = 'Line Id'
INSTAGRAM_ID = 'Instagram Id'
INSTAGRAM_TOKEN = 'Instagram token'
WHATSAPP_ID = 'Whatsapp Id'
WHATSAPP_MOBILE_ID = 'Whatsapp Mobile Id'
WHATSAPP_LINK = 'Whatsapp link'
GOOGLE_PLUS_ID = "Google+ Id"
VIMEO_ID = "Vimeo Id"
FOURSQUARE_ID = 'Foursquare Id'
BLOG_ID = "Blog Id"
XING_ID = "Xing Id"
SKYPE_ID = "Skype Id"
MESSENGER_ID = "Messenger Id"
TRIPADVISOR_ID = "Tripadvisor Id"
TIKTOK_ID = "Tiktok Id"
SPOTIFY_ID = "Spotify Id"

SHARE_LINK_IMAGE = 'Share link image'

# ========== End Social Media Configs ==========#

# Banned Credit Cards for Datatrans
BANNED_CREDIT_CARDS_TOKEN = 'Banned Credit Cards Token'

MANUAL_CURRENCY_RATE_SET = "Manual Currency Rate Set"

INCREASED_CURRENCY_RATE = "Increased Currency Rate"

HIDE_DIRECT_PAYMENT = "Hide Direct Payment"

HIDE_DIRECT_PAYMENT_NR = "Hide Direct Payment NR"

REDIRECT_MODIFICATION = "Redirect Modification for Manager"

ENABLE_CHANGE_PRICE_B3 = "Enable change price b3"

DISABLE_MESSAGE_PENDING_AMOUNT = "Disable message pending amount"
DISABLE_MESSAGE_PENDING_AMOUNT_IF_NOT_TPV = "Disable message pending amount if not tpv"

INCLUDE_CONDITIONS = "Include conditions in agree"

MULTIPLE_OPTIONS_PAYMENTS = "Multiple Options Payment"

GATEWAY_DEFAULT = "First Gateway Option"

USER_AUTOMATIC_LOGIN_CLUB = "User Automatic Login Club"

CURRENCY_GEOLOCALIZED = "Currency Geolocalized"

NEW_MODIFICATION_OPTION = "New modification option"

BOOKING1_CAPACITY_ICONS = "Booking1 capacity icons"

PAGES_NO_CACHE = "Pages no cache"

NOTIFY_PAGE_CACHE_SAVE = 'Notify page cache save'

NOTIFY_PAGE_CACHE_REMOVE = 'Notify page cache remove'

PREBOOKING = "prebooking"

SEND_SOURCE_BOOKING0_IN_COMMENTS = "booking0_in_comments"

CUSTOM_CLUB_SECTION = "Custom section club register"

ALTERNATIVE_UPGRADE_EMAIL_CLUB = "Alternative upgrade email club"

INCLUDE_LAST_END_DATE_SUPPLEMENTS = "Include Last Day Supplement"

BOOKING3_EXTRA_FIELDS = "Booking3 extra fields"

BOOKING1_EXTRA_MESSAGE_MODIFIED_PRICE = "Booking1 extra message on modified price"

BOOKING4_SKIP_ERROR_AVAILABILITY_SCREEN = "Booking4 skip error availability screen"

POPUP_SEEKER = "Popup seeker"

BOOKING_EXTRA_FOOTER_LINKS = "Booking extra footer links"

BOOKING_SITEMAP_USE_HTML = "Booking sitemap show html"

ENABLE_DIRECT_SEARCH = "Enable direct search"

TAB_CHANGE_CUSTOM_TITLE = 'Tab change custom title'

HIDE_NEWSLETTER = 'Hide newsletter'

PRESTAY_ADD_IVA_INCLUDED_MESSAGE = 'Prestay add iva included message'

PRESTAY_HIDE_TAX_BREAKDOWN = 'Prestay hide tax breakdown'

PRESTAY_SHOW_TAXES_NOT_INCLUDED = 'Prestay show taxes not included'

ENABLE_ADDITIONAL_CLASS_IN_WIDGET = 'Enable additional class in widget'

# If populate, it stop smart cache. Always clean all, and more important: never save entities involved
SMART_CACHE_OFF = "Smart Cache Off"















CHAIN_NAME = 'Chain name'

TRANSLATION_TO_REPLACE_WITH_DATE = 'Traducciones por fecha'

SHOW_STEP_BOOKING4 = 'Show booking4 in steps'

CUSTOM_BOOKING_BODY_CLASS = "Custom booking body class"

CALL_CENTER_CUSTOM_DOMAIN = "Call center custom domain"

FORCE_FILTER_RATE_NO_CALLCENTER = "Force filter rate by word no callcenter"

# Custom map section
CUSTOM_MAP_BOOKING0 = 'custom map section booking 0'

BOOKING0_MAPS_VERSION = 'Booking 0 maps version'

BOOKING0_MAPS_ZOOM = 'Booking 0 maps zoom'

BOOKING0_SERVICES_ICONS = 'Booking0 services icons'

# Currency converion on daily prices
DAILY_PRICES_CURRENCY_CONVERSION = 'Daily prices currency conversion'

BOOKING_MOBILE_BACK_BUTTON_URL = "Booking mobile back button url"

MOBILE_BOOKING_EXTRA_BACK_BUTTON = "Mobile booking extra back button"

DISABLED_IP_CHECK = "Disable ip check"

# Used to bypass security functionality of ips and isp, to allow (only for a specific hotel, not for all ones)
WHITELIST_NETWORK = "Whitelist Network"

WHITELIST_ISPS = "Whitelist ISPS"

REQUIRED_INPUTS_BOOKING3 = "Required inputs booking3"
BOOKING3_GATEWAY_CUSTOM_INPUTS = "Booking3 gateway custom inputs"
PERSONAL_DETAILS_TOOLTIPS = "Custom tooltip at personal details fields"

# Will disable the TPV message at payment type (IE: "sequra;sermepa")
DISABLE_TPV_MESSAGE_AT_PAYMENT_TYPES = "Disable TPV message at payment types"

CUSTOM_BOOKING3_FIELDS_BY_DATE = "Custom booking3 fields by dates"

BOOKING3_RELATION_QUESTION = "Relation question booking3"

BOOKING3_SEPARATED_AGREE_CHECKS = 'Booking3 separated agree checks'

BOOKING3_RATE_CONDITIONS_AGREE = 'Booking3 rate condition agree check'

BOOKING3_EXTEND_AGREE_TEXT = 'Booking3 extend agree text'

BOOKING3_ADDITIONAL_CHECKBOX_TEXT = 'Booking3 additional checkbox text'

BOOKING3_REGIMEN_DESCRIPTION = 'Booking3 regimen description'

# Used to ignore always the custom domain parameter reveived in the request, ie: ?custom_domain=www.gsh-hotels.com
DISABLED_CUSTOM_DOMAIN_PARAM = 'Disable custom domain param'

# show price calendar instead of datepicker in mobile
PRICE_CALENDAR_FOR_MODIFICATION_MOBILE = "Price calendar for modification mobile"

# Set to add a button to disable that calendar close automatically when end date is selected
SHOW_CONFIRM_DATES_BUTTON_MOBILE = "Show confirm dates calendar button mobile"

# Open directly the flex calendar in booking0
MOBILE_BOOKING0_FLEX_CALENDAR = "Booking0 flex calendar on modify button"

EMAIL_BOOKING_BONOS = 'Email bookings with bonos'

BLIND_COPY_BOOKING_EMAIL = 'Blind copy booking email'

# Used to replace the default postmark api key to a new one
POSTMARK_API_KEY = 'Postmark API key'

EMAIL_SENDER_BOOKING = 'Email sender booking'

STOP_SEND_EMAIL_CANCELATTION_WITH_PAYMENTS = "Stop send email cancellation with payments"

EMAIL_PASSWORD_RECOVERY_VERSION = 'Email password recovery version'

# To personalize the subjet in emails modifications
PERSONALIZED_MODIFICATION_EMAIL = 'Personalized modification email'

B4_SHOW_SUPPLEMENT_DESCRIPTION = "B4 show supplement description"

B4_HIDE_LANGUAGE_SELECTOR = "B4 hide language selector"

# To avoid use cusotm template in cancellation email for hotel
NOT_CUSTOM_EMAIL_FOR_CANCELLATION_HOTEL = 'use old cancellation email for hotel'

PRIVACY_POLICIES_IN_EMAIL = 'Privacy policies in reservation confirmation email'

EXTRA_ADDITIONAL_SERVICES_IN_RESERVATION_CONFIRMATION_EMAIL = 'Extra additional services in reservation confirmation email'

# To send customer's version email for agencies
AGENCIES_RESERVATION_CONFIRMATION_EMAIL_AS_CUSTOMER = 'Agencies reservation confirmation as customer email'

# Booking 4 extra info
BOOKING4_USE_EMAIL_AND_USER_NAME = 'booking4 extra user info'
BOOKING4_TAXES_NOT_INCLUDED = 'booking4 taxes not included'
BOOKING4_SHOW_PACKAGES_INFO = 'booking4 show packages info'
BOOKING4_ADD_IVA_INCLUDED_MESSAGE = 'booking4 add iva included message'
BOOKING4_MY_RESERVATION = 'booking4 my reservation'
BOOKING4_BANNER = 'booking4 banner'
BOOKING4_BOTTOM_TABLE_CONTENT = 'booking4 bottom table content'

SHOW_ROOM_PRICES_DETAIL_CUSTOMER_CONFIRMATION_EMAIL = "Show room prices details customer confirmation email"

REDSYS_RECEIPT_IN_CONFIRMATION = "Redsys receipt in confirmation"

# Show comments in the email of confirmation booking and booking4
SHOW_BOOKING_COMMENTS = 'show comments in booking confirmation'

# Attach a PDF with the booking confirmation
EMAIL_RESERVATION_CONFIRMATION_PDF = 'Email reservation confirmation pdf'

# Show a QR code in the booking confirmation that redirects to a booking confirmation url
QR_CODE_IN_BOOKING_CONFIRMATIONS = 'QR code in booking confirmations'

# Attach a Passbook in the booking confirmation
PASSBOOK_IN_BOOKING_CONFIRMATIONS = 'Passbook in booking confirmations'

#Attach payment link if reservation not fully paid
PENDING_PAYMENT_LINK_IN_CONFIRMATION = 'Pending payment link in confirmation'

MIN_STAY_WARNING_COLOR = "min stay warning color"

OCCUPANCY_REDIRECT_INFO_IN_WIDGET = "Occupancy redirect info in widget"

# Booking message when start date is modified because is older than current date
DISABLE_MODIFIED_SEARCH_MESSAGE = 'Disable modified search message'

BOOKING3_REMOVE_SUPPLEMENTS = 'Booking3 Remove Supplements'

B1_REMOVE_BRACKETS_FROM_TOTAL = 'B1 remove brackets from total'

BOOKING1_REACT = 'Booking1 react'
BOOKING2_REACT = 'Booking2 react'
BOOKING2_REACT_SHOW_CONTINUE_BUTTON_SERVICE_TABS = "Booking2 react show continue button on scroll"

# If booking react is active and we want to show price per night
BOOKING1_REACT_PRICE_PER_NIGHT = 'Booking1 react active price per night'

BOOKING1_MULTIPLE_ROOMS_WITHOUT_MERGING = 'Booking1 multiple rooms without merging'

TAXES_INCLUDED_WITHOUT_PRICE = 'Taxes included without price in multiple rooms total'

BOOKING1_MULTIPLE_ROOMS_SELECTOR = 'Booking1 multiple rooms selector'

SHOPPING_CART_LIMIT_ONE_ROOM = 'Shopping cart limit one room'

CLOSE_CALENDAR_V2_BUTTON = 'Close calendar v2 button'

AVAILABILITY_PER_DAY_IN_CALENDAR = "Availability per day in calendar"

NOT_SET_CURRENT_HOTEL_IN_BOOKING0_LIST = "Not check current hotel for booking0 order"

HIDE_NO_AVAILABILITY_HOTELS = "Booking0 hide no availability hotels"

# Show main hotel in booking0 hotel list
SHOW_MAIN_HOTEL_IN_BOOKING0_MOBILE = "Show main hotel in booking0 mobile"

UPGRADES_SHOW_ONLY_TARGET_NAME = 'Upgrades show only target name'

SHOW_UPGRADE_MANAGER_CONFIRMATION = "Show upgrades in manager confirmation"

SHOW_ORIGINAL_PRICE_HOTEL_CONFIRMATION = "Show original price in hotel confirmation"

HIDE_SUPPLEMENTS_IF_NOT_EXIST = "Hide supplements if not exist"

SHOW_KIDS_AGES_IN_BOOKING_CONFIRMATION = 'Show kids ages in booking confirmation'

SHOW_BABIES_AGES_IN_BOOKING_CONFIRMATION = 'Show babies ages in booking confirmation'

HIDE_PACKAGES_IN_BOOKING_CONFIRMATION = 'Hide packages in booking confirmation'

MANAGER_CONFIRMATION_ENCRYPTED_PARAMS = "Manager confirmation email encrypted params"

# add here all extra params tha you wish to add to /search. Format: &xxx=yyy&zzzz=aaaaa
CONFIG_EXTRA_PARAMS_SEARCH = "extra params in manager search"

# Check if hotel offer rooms and beds
IS_HOSTEL = 'Is Hostel'

# In case we want to specify an order for the currencies
CURRENCY_ORDER = 'Currency order'

MULTIPLE_GATEWAYS_NEVER_OPTIONALS = "Multiple gateways never optionals"

FORCE_BLOCKED_PRICE_IN_PREBOOKING = "Always block price in prebooking"

MULTIPLE_GATEWAY_BY_COBRADOR = "Use multiple payment gateway by cobrador"

PAYMENT_FAILED_RESCUE_POPUP = "Payment failed rescue popup"

SPECIAL_GATEWAY_FOR_RATE_FOR_COBRADOR = "special gateway for rate for cobrador"

# indicates if Sermepa is in charge of save credit cards (if something different to True, it must be the name of the bank for information purposes)
GATEWAY_SPECIAL_COUNTRIES_BY_TOKEN = "credit cards by token by countries"

GATEWAY_CREDIT_CARD_BY_TOKEN_OPTIONAL = "credit cards by token always optional"

GATEWAY_AMOUNT_ALWAYS_0 = "Gateway Amount always 0"

ACCEPT_EXTERNAL_FORCED_PROMOS = "accept external forced promos"

# Check contact forms with AI
CONTACT_FORMS_SPAM_AI = "Check contact forms by AI"

# Indicates if the hotel wants to recieve the device in the confirmation email
DEVICE_IN_HOTEL_CONFIRMATION_EMAIL = "device in hotel confirmation email"

MAILCHIMP_ADD_ORIGIN_TAGS = "Mailchimp Add Origin User Tags"  # Separated by '@@': tag1@@tag2@@tag3

LOUNGEUP_API_KEY = "Loungeup API Key"

AVOID_FIDELTOUR_SUBSCRIBE_B4 = 'Avoid fideltour subscribed b4'

EMAIL_COPY_BY_TITLE = 'Email copy by title'

# Disable payment with credit card
USE_NEW_CREDIT_CARD_LOGOS = 'Use new credit card logos'

DISABLE_PAYMENT_IF_ALREADY_PAID = "Disable payment if already paid"

# Cancellation hour
CANCELLATION_HOUR_BY_LANGUAGE = 'Cancellation hour by language'

CANCELLATION_TXT_FOR_FLASH_OFFER = "Cancellation text for flash offer"

NUM_FLIGHT_MANDATORY = "num flight mandatory booking3"

CUSTOM_BILLING_FORM = 'Booking3 billing form'

# Booking3 club register not mandatory
BOOKING3_CLUB_REGISTER_NOT_MANDATORY = "Club register booking3 not mandatory"

# Hide newsletter but always is active
HIDE_ACTIVE_NEWSLETTER = "Newsletter hide active"

PROMOCODES_AVOID_CLUB = 'Promocodes avoid club'

#Banner Club in booking2
BOOKING2_BANNER_CLUB = "Booking2 banner club"

DISABLE_LOCKED_USER_CLUB_EMAIL = "Disable locked user club email"

# If exists normal and club rates, the normal rates tab is set as active
PRIORITIZE_NORMAL_RATES = "Prioritize normal rates"

## If this property is available we show price per night instead of total if club discount desactivated
B1_CLUB_NOT_ACTIVE_PRICE_PER_NIGHT = "B1 club not active price per night"

# If this property is available we must show the night price column in resultList.html
FORCE_NIGHT_PRICE_COLUMN = "Force night column price"

#Booking1 customize submit button text in mobile
BOOKING1_CUSTOM_TEXT_SUBMIT_BUTTON_MOBILE = "Booking1 custom text submit button mobile"

#Booking1 customize submit button text in desktop
BOOKING1_CUSTOM_TEXT_SUBMIT_BUTTON_DESKTOP = "Booking1 custom text submit button desktop"

# indicates the language for the hotel analytics
ANALYTICS_LANGUAGE = 'Language Analytics'

# We show the qr of the reservation in the confirmation email
QR_CODE_MAIL_RESERVATION = "qr_code_mail_reservation"

EXTRA_CONTENT_CONFIRMATION = "extra_content_confirmation"

SHOW_EXTERNAL_IDENTIFIER_HOTEL_CONFIRMATION = "Show external identifier in hotel confirmation"

SHOW_BONO_BREAKDOWN = "Show bono breakdown in confirmation"

# Shows customer data in manager confirmation email for all rooms
SHOW_PERSONAL_DATA_MANAGER_CONFIRMATION = 'Show personal data in manager confirmation'

# Shows customer data in customer confirmation email for all rooms
SHOW_PERSONAL_DATA_CUSTOMER_CONFIRMATION = 'Show personal data in customer confirmation'

# If true will avoid any check of availability in booking 1 for rooms at booking steps, will let to frontend controll it
BOOKING_STEPS_FRONTEND_AVAILABILITY = "Booking steps frontend availability"

# Show a no dispo message for mixed hotels
SHOW_NO_DISPO_MIX_HOTELS = "Show no dispo mix hotels"

SEPARATED_APPS_IDS_ALWAYS = "Separated apps ids always"

SCRAPING_DATA_FAKER = 'Scraping Data Faker'

SHOW_ONLY_VISIBLE_ROOMS_IN_CALENDAR_SELECTOR = 'Show only visible rooms in calendar selector'

# If we are looking for a specific room and we want to show the rest separately
SHOW_FILTERED_ROOM_MESSAGE = "Show filtered room message"

MODIFY_RESERVATION_OLD_WAY = "old way modification"

TRANSFER_BANK_EMAIL_TITLE = 'Bank transfer email title'

# Bank transfer prefix of identifier
PENDING_RESERVATION_WHEN_TRANSFER = 'Pending reservation when transfer'

#External scripts for booking process which must be incerted in document head
BOOKING_EXTRA_HEAD_SCRIPT = 'Booking extra head script'

BOOKING_CONFIRMATION_SHOW_ORIGINAL_INDEX_ROOMS = 'booking4 show original index room'

BOOKING1_MOBILE_SERVICES_ROOMS = "Booking1 services rooms mobile"

BOOKING1_POPUP_SERVICES_ROOMS = "Booking1 popup services rooms"

BOOKING1_POPUP_SERVICES_ROOMS_CAROUSEL = "Booking1 popup services rooms carousel"

BOOKING1_SEE_MORE_DISABLE_AUTOPLAY = 'Booking1 popup disable autoplay'

USE_TEST_RESERVATIONS_ALWAYS = 'Use test reservations always'

EXCHANGE_RATE_MESSAGE = 'Booking3 exchange rate message'

BOOKING3_EXTRA_TEXT_AGENCIES = 'Booking3 extra agencies text'

# Set booking1 prices without taxes for specific countries or IPs. i.e. 'us;fr;0.0.0.0'
BOOKING1_WITHOUT_TAXES_COUNTRIES = 'Booking1 without taxes by countries'

INCLUDE_TAX_PRICE_PER_DAY_GATEWAY = 'Include tax prices per day gateway'

INCLUDE_TAX_PRICE_PER_DAY_PEP = 'Include tax prices per day in pep'

REMOVE_SPACES_PROMOCODE = 'Remove spaces promocode'

TAX_INFO_POPUP_STYLES = 'Tax info popup'

HIDE_TAX_INFO_POPUP = 'Hide tax info popup'

FORCE_TAX_INFO_POPUP = 'Force tax info popup'

TAX_INFO_POPUP_HOVER = 'Tax info popup hover'

SHOW_TAXES_INCLUDED_MESSAGE_ON_BOOKING1 = 'Show taxes included in booking1'

CUSTOM_TAXES_INCLUDED_MESSAGE_ON_BOOKING1 = 'Custom taxes included message in booking1'

CUSTOM_TAXES_MESSAGE = 'Custom taxes message'

B1_ENVIRONMENTAL_TAX_EXCLUDE = 'B1 exclude environmental tax'

SMART_TAXES_LABEL = "Smart taxes label"

BOOKING3_ALWAYS_AUTOFILL_COUNTRY_BY_IP = "Booking3 always autofill country by ip"

BOOKING3_ENABLE_CITY_SELECT = "Booking3 enable city select"

PREFIX_BOOKINGID ="prefix booking"

SUFFIX_BOOKINGID ="suffix booking"

# Gets the section name of the value and changes the newsletter label text to the section content in booking
BOOKING_CUSTOM_NEWSLETTER_LABEL = "Booking custom newsletter label"

#Booking1 inactivity popup
BOOKING1_INACTIVITY_POPUP = "Booking1 inactivity popup"

# Booking1 newsletter popup
BOOKING1_NEWSLETTER_POPUP = "Booking1 special event newsletter popup"

HIDE_NEWSLETTER_USER_LOGGED = 'Hide newsletter when user is logged'

USE_LOYALTY_SESSIONS = 'Use loyalty sessions'

EXPLICIT_NAMESPACE_FOR_CLUB_LOGIN = "Explicit namespace for club login"

CLUB_SEGMENTATION_SECTION = "Club segmentation section"

# Disabled club with agency
DISABLED_CLUB_FOR_AGENCY = "Disable club with agency"

FORCE_AGENCY_PROMOCODE = "Force agency promocode"

# Give points to user even when they are not logged in
CLUB_POINTS_NOT_LOGGED = "Club points not logged"

HOTELVERSE_CONFIRMATION_BANNER = "Hotelverse confirmation banner"

CHECKIN_CONFIRMATION_BANNER = "Checkin confirmation banner"

CLUB_POINTS_ONLY_DEPARTURE = "Club points only departure"

RATES_LOCK_VERSION = 'Rates lock version'

DISABLE_CLUB_WELCOME_EMAILS = 'Disable club welcome emails'

# ========= End Members Club Configs =========

WIDGET_GUESTS_SELECTOR_VERSION = "Widget guests selector version"

# Additional services v2 with counter instead of regular selector input
ADDITIONAL_SERVICES_COUNTER_SELECTOR = "Additional services counter selector"

ADDITIONAL_SERVICES_SHOW_TOTAL_WITH_ROOMS = "Additional services show total with rooms"

ADDITIONAL_SERVICES_FILTERS_BY_CATEGORY = "Additional services filters by category"

ADDITIONAL_SERVICES_SHOW_FIRST = "Additional services show first"

FORCE_X_DECIMALS = "Number of decimals forced in prices"

VISUALLY_ROUND_TOTAL_PRICE = "Visually round total price"

# Booking on requests
BOOKING_ON_REQUEST = "Booking on request"

BOOKING_ON_REQUEST_SHOW_PRICE = "Booking on request show price"

DATALAYER_UNIVERSAL_ECOMMERCE_WITH_PRICES_PER_NIGHT = "Datalayer universal ecommerce with prices per night"

DATALAYER_GA4 = 'Datalayer GA4'

DATALAYER_GA4_QUANTITY_NIGHTS = 'Datalayer GA4 purchase quantity nights'

DATALAYER_GA4_FORCED_CURRENCY = 'Datalayer GA4 forced currency'

DATALAYER_GA4_ADD_OFFER_INFO = 'Datalayer GA4 add offer info'

SEPARATE_CLUB_RATES_IN_DATALAYER = 'Separate club rates in datalayer'

AVOID_CURRENCY_IN_DATALAYER_ITEMS = 'Avoid currency in datalayer items'

ONLY_PRICES_WITHOUT_TAX_IN_DATALAYER = 'Only prices without tax in datalayer'

EUR_CONVERSION_DATALAYER = 'Add datalayer euro conversion'

INCLUDE_TAXES_IN_GA_VALUES = "Include taxes in google analytics values"

DERBYSOFT_TRACKING = "Derbysoft tracking"

CURRENCY_SELECTOR_HEADER = "Currency selector in header"

SHOW_NUMBER_NIGHTS_WIDGET_CALENDAR = "Show selected nights widget calendar"

HIDE_JOIN_THE_CLUB_BOOKING3 ="Hide join the club booking3"

# Add a customized message in booking3
CUSTOM_MESSAGE_BOOKING3 = 'Custom message booking3'

SHOW_TAX_DESCRIPTION_AT_RESERVATION = "Show tax description at reservation"
TAXES_INCLUDED_IN_ROOM_PRICE = "Taxes included in room price"
BREAKDOWN_TAXES_INCLUDED = "breakdown of taxes included"
B4_SHOW_PRICE_AS_SUBTOTAL = "b4 show price as subtotal"

INCLUDE_TAX_TRANSFER ="include tax transfer"

AGENCY_EMAIL_SHOW_ONLY_FINAL_PRICE = "Agency email show only final price"
SUBAGENCY_EMAIL_SHOW_ONLY_FINAL_PRICE = "subagency email show only final price"



BOOKING3_ROOM_BREAKDOWN_SUMMARY = "Booking3 room breakdown summary"

BOOKING3_SHOW_ROOMS_SUBTOTAL = "Booking3 show rooms subtotal"

DATALAYER_IN_BOOKING_CANCELLATION = "Datalayer in booking cancellation"

# Rates list to avoid only in price calendar
AVOID_SPECIFIC_RATES_IN_CALENDAR = 'Rates to avoid in price calendar'

#Force to disable cookies popup - Util for injetions that have their own cookies popup
DISABLE_COOKIES_POPUP = 'Disable cookies popup'

#Add a 'reject' button in cookies popup
ADD_DECLINE_COOKIES_BUTTON = 'Add decline cookies button'

#Show 'necessary cookies' in modify popup
ADD_NECESSARY_COOKIES_SELECTOR = 'Add necessary cookies selector'

#Show an introduction in modify popup
ADD_INTRO_COOKIES_TEXT = 'Add cookies intro text'

CANCEL_EXTERNAL_BEFORE_MODIFICATION = 'Cancel Externals Before Modification'

#if populate, we don't allow a modification if TPV is present in reservation
TPV_RESTRICTION = "TPV restriction for modify"

REDIRECT_LANGUAGE_SEND_CONFIRMATION = "Redirect Language Send Confirmation"

PAYPAL_FRAUDNET = "Paypal fraudnet"

ENABLE_INTERNAL_COMMENTS_CALLCENTER = "Enable internal comments callcenter"

FORCE_CURRENCY_BY_SEARCHED_COUNTRY = "Force currency by searched country"

BOOKING3_FLAGS_SELECTOR = "booking3_country_flags_selector"

BOOKING3_USE_LABEL_INSTEAD_PLACEHOLDER = "booking3 use label instead placeholder"

BOOKING3_INPUT_ERROR_V2 = "booking3 input error v2"

EXTRA_LABEL_COMMENTS = "extra label comments"

BOOKING3_OPTIONAL_COMMENTS = "Booking3 optional comments"

BOOKING3_TELEPHONE_PREFIX_SELECTOR = "booking3 telephone prefix selector"

REQUIRED_SELECT_FIELDS_IN_BOOKING_3 = "Required select fields in booking 3"

BOOKING3_IDENTIFIER_VALIDATOR = "Booking3 identifier validator"

BOOKING3_COMMENTS_MAX_CHARACTERS = "Booking3 comments max characters"

CUSTOM_ERROR_SCREEN = "Custom error screen"

BOOKING_OPEN_COOKIES_BANNER = "Booking open cookies banner"

HIDE_RSS_IN_FOOTER = "Booking hide rss in footer"

SHOW_GATEWAY_LOGO = "Show Gateway Logo"

PACKAGE_TAX_PERCENTAGE = "Package tax percentage"

#This is used to add taxes filtering by rates, example: 5%;RATE_IDENTIFIER_1@@RATE_IDENTIFIER_2
RATE_TAX_PERCENTAGE = "Rate tax percentage"

HIDE_PAYMENT_CALLSEEKER = "Hide Payment for Callseeker"

HIDE_TRANSFER_CALLSEEKER = "Hide Transfer for Callseeker"

RETRY_SIN_ESTANCIA_MINIMA = "Retry sin estancia minima"

RETRY_OCCUPANCY_IN_CALENDAR = "Retry occupancy in calendar"

DISABLE_PARITYMAKER_FOR_AGENCIES = "disable paritymaker for agencies"

DISABLE_RATECHECK_FOR_AGENCIES = "disable ratecheck for agencies"

DISABLE_PROMOTIONS_FOR_AGENCIES = "disable promotions for agencies"

ENABLE_ASK_SUITE = "Enable ask suite"

FACEBOOK_DOMAIN_VERIFICATION = "Facebook domain verification"

SPECIFIC_TIMEZONE = "Specific timezone"

USE_ALTERNATIVE_PAYMENT_SEEKER = "Use alternative payment seeker"

NOT_SEND_MAIL_TO_CUSTOMER = "Not Send Mail To Customer"

MERGE_PAYMENT_OPTIONS_ENABLED = "merge_payment_options_enabled"

FORCE_NUMERIC_ID = "force numeric identifier"

RATE_PERCENT_OVERIDE_GATEWAY_AMOUNT = "Rate percent overide"

FORCE_MERGE_PAYMENT_OPTIONS = "Force merge payment options in callcenter"

USE_SPECIFIC_CURRENCY_FOR_GATEWAYS = "Use alternative currency for gateways"

SPECIAL_CONVERSION_FOR_GATEWAYS = "special conversion for gateway"

VIRTUAL_ADDITIONAL_SERVICES = "Virtual additional service"

FIX_CALCULATE_PRICE_FOR_AGENCIES = "Fix calculate price for agencies"

BOOKING4_SHOW_PAYMENT_METHOD_INFO = "Booking4 show payment method info"

LINK_TPV_VALIDATION = "Validate reservation in booking 3 link tpv"
FORCED_GATEWAYS_BY_PEP_LINKS_CC = "Force gateways shown in callcenter pep links"

ALLOW_MODIFY_ONLY_ONCE = 'allow modify only once'

ALLOW_MODIFY_ONLY_ONCE_RELEASE = 'allow modify only once release'

ALLOW_LIMITED_MODIFICATIONS = "Allow limited modifications"

# Custom text for location reservation modifications on the My Bookings landing
LOCATION_RESERVATION_MODIFICATIONS_CUSTOMTEXT_BUTTONSEARCH = "Location reservation modifications custom text button search"


#Indicates if the hotel has customizable text by payment method
BOOKING3_CUSTOM_TEXT_PAYMENT_METHOD = 'custom text by payment method'

INTERACTIVE_OPTION = 'interactive'

# Change the input type
CUSTOM_INPUT_TYPE_NUMBER_PERSONAL_DETAILS_B3 = 'Custom input type number personal details B3'

DISABLE_CREDIT_CARD_FOR_PREBOOKING = "Disable credit card for prebooking"

MULTIPLE_GATEWAYS_CUSTOM_TRANSLATIONS = "Use custom translations for multiple gateways"

SHOW_FULL_CURRENCY_NAME = "Show full currency name"

#Sroll mouse in steps separated by ";" not including booking3 where it is included by default
MOUSE_SCROLL_IN_STEPS = "Booking process v3 include scroll in steps"

CURRENCY_NAME_ALWAYS_FIRST = "Currency name always first"

BOOKING_SUMMARY_EXTRA_INFO = "Booking summary extra info"

SESSION_STORAGE_RESERVATIONS_WEEK = "Session storage reservations week"

CALL_ME_POPUP_EMAIL_ADRESS = "Call me popup email address"

CONDITIONS_PRIORITY_COLUMN = "Conditions priority column"

SEARCH_ADDING_NIGHTS = "Search adding nights"

CUSTOM_PRIVACY_CONFIG_POLICY_CORPORATE = "Custom privacy policy config corporate"

RECOVER_MISSING_REAL_PROMOTIONS = "Recover missing real promotions"

AGENCY_RATES = 'Agency rates'

BOOKING3_ALTERNATIVE_LAYOUT = 'Booking3 alternative layout'

BOOKING3_ALTERNATIVE_SUMMARY_MOBILE = 'Booking3 alternative summary mobile'

BOOKING3_ALTERNATIVE_SUMMARY_DESKTOP = 'Booking3 alternative summary desktop'

BOOKING3_ADDITIONAL_SUMMARY = 'Booking3 additional summary after personal details'

CALLSEEKER_ALLOWED_DOMAINS = 'Call seeker allowed domains'

METHOD_PAYMENT_IN_EMAIL_SUBJECT = "method payment in hotel email subject"

ANTI_SCRAPING_WHITELIST = "Anti scraping whitelist"

SEND_SIBS_MULTIBANCO_MAIL = "Send sibs multibanco mail"

SAVE_ADDRESS_BREAKDOWN = "Save address breakdown"

SSL_FORCE_APPSPOT_HOST = "SSL force appspot host"

VALIDATE_PROMOCODE_BY_EMAIL = "validate promocode by email"

AVOID_BLIND_COPY_IN_INVALID_CREDIT_CARD_EMAIL = "Avoid blind copy in invalid credit card email"

ENABLE_EXTERNAL_PACKAGES = "Enable External Packages"

EXTERNAL_PACKAGES_WITH_PROMOCODE = "External packages with promocode"

FORCE_CONVERSIONS_FROM_MANAGER = "Force conversion from manager menu"

DOWNLOAD_B1_PDF_ENABLED = "Download b1 pdf enabled"

CLUB_NEWSLETTER_IS_OPTIONAL = "Club newsletter is optional"

SHOW_SID_IN_CALLCENTER = "Show sid in callcenter"

CUSTOM_WALLET_DOMAIN = "Custom wallet domain"

CUSTOM_WALLET_DOMAIN_CREATION = "Custom wallet domain creation"

ONLY_RATES_BY_WORDS_IN_CLUB = "only rates by words in club"

ONLY_BOARDS_BY_WORDS_IN_CLUB = "only boards by words in club"

FORCE_AMOUNT_BY_TPV_FOR_CLUB = "force amount by tpv for club"

FORCE_DISCOUNT_FOR_CLUB = "force discount visual for club"

DISCARD_FORCE_CLUB = "discard force club"

APPLY_DISCOUNT_BONO_ANDALUZ = "Enable Discount Bono Andaluz"

ADDITIONAL_SERVICE_MINSTAY_PROP = "min_stay"
ADDITIONAL_SERVICE_MAXSTAY_PROP = "max_stay"
ADDITIONAL_SERVICE_MAX_QUANTITY_PROP = "max_quantity"
ADDITIONAL_SERVICE_FORCED_RANGE = "forced_range"

ADDITIONAL_SERVICE_PRODUCT_TRANSFER = "product_transfer"
ADDITIONAL_SERVICE_ENABLE_PROMOCODE = "enable_by_promocode"

ADDITIONAL_SERVICE_SHUTTLE = "shuttle"

ALLOW_MULTIPLE_BOARD_STAR_LOGOS = "Allow multiple board star logos"

STAR_PROMOTIONS_DESCRIPTION_IN_CONFIRMATION = "Star promotions description in confirmation"

SEPARATE_PACKAGES_LIST_DATALAYER = "Separate packages list datalayer"

DATALAYER_ADDITIONAL_EVENTS = "Datalayer additional events"

REDUCED_VIEW_ITEM_LIST = "Reduced view item list"

MINIFY_EMAIL_CSS = "Minify email css"

EXCLUDE_NAMESPACE_IN_REDIRECTIONS = "Exclude namespace in redirections"

KEEP_B3_GATEWAY_TEXT_CLASSES = "keep b3 gateway text classes"

CONTACT_EMAIL_COLOR = "Contact email color"

HIDE_CREDIT_CARD_BONO = "Hide credit card bono"

UPGRADE_POPUP_INFO = "Upgrade popup info"

EXCLUDED_CANONICAL_REDIRECTIONS_URLS = "Excluded canonical redirections urls"

SUPPLEMENT_FILTER_BUTTONS_ORDER = 'Supplement filter buttons order'