# Popup anti OTAs
ANTI_OTAS_POPUP = "Anti OTAs"

DATALAYER_BY_PATHS = 'Datalayer by paths'

DATALAYER_BY_PATHS_TEST = 'Datalayer by paths test'

DATALAYER_HTML_RENDER = 'Datalayer html render'

INJECTION_CONFIG = 'Injection config'

BONO_GIFT_CUSTOM = 'bono gift custom'

DATALAYERS_CONFIGS = 'Datalayer configs'

MOBILE_OPTIONS_BOOKING = "Mobile options booking"

DESKTOP_OPTIONS_BOOKING = "Desktop options booking"

PACKAGE_FORFAIT_CONFIG = "packages_forfait_config"

BOOKING_PROCESS_CUSTOMIZED = "Booking process customized"

BOOKING_HOTEL_SELECTOR = "Booking hotel selector"

PRICE_CALENDAR_CONFIG = 'Price calendar config'

BOOKING_TRANSFER_SERVICE = 'Booking transfer service'

WEB_SEEKER_EMAILS = 'Web Seeker Emails'

WHATSAPP_COMMUNICATION = 'Whatsapp communication'

PAYMENT_STYLES = 'Payment Styles'

SCALAPAY = "Scalapay"

DIRECT_SEARCH_EXTRA_REQUEST_PARAMS = 'Direct search extra request params'

# ========= Newsletters ============
MDIRECTOR_CONFIG = 'Mdirector'

# ========= Members Club Configs ===========

CLUB_LOCK_RATES = 'Club lock rates'
CLUB_EXTRA_PDF = 'ClubExtraPdf'
CLUB_CONFIG = 'Club config'


# ========== Tracking codes ===========
SOJERN_PIXEL_CODE = 'Sojern Pixel'

GA_CUSTOM_DIMENSIONS = "GA custom dimensions"

WIHP_CONFIG = "Whip Tracking"

DERBYSOFT_CUSTOM_PARAMS = "Derbysoft custom params"

COOKIEBOT_BY_DOMAIN = 'Cookiebot by domain'

#========== Booking process ===========
BOOKING_PROCESS_CONFIGS = 'Booking process configs'

BOOKING_PROCESS_WEB_SEEKER = 'Booking process web seeker'

BOOKING_PROCESS_VERSIONS = 'Booking process versions'

ZENDESK = 'zendesk'

CUSTOM_RATES_COLORS = 'Custom rates colors'

AB_TESTING_WEB = 'AB Testing'

BONO_NIGHTS = 'bono_nights'

GA4_EXTRA_PARAMS_BY_EVENT = 'GA4 extra params by event'

SOCIAL_LOGIN_INFO = "Social login"

AUTO_PROMOCODE_BY_URL = 'Auto promocode by url'

CUSTOM_PRIVACY_POLICY_BY_NAMESPACE = 'Custom privacy policy by namespace'

AUDIT_JS_ERRORS = 'Audit JS errors'

AUDIT_SENTRY = 'Audit sentry'

GOOGLE_TAG_MANAGER = "Google Tag Manager"

PROMOCODES_CONFIG = "Promocodes config"

ANALYTICS_EVENT_CONSTANTS = "Analytics event constants"

REFERERS_CUSTOM_URL_PARAMS = "Referers custom URL params"

CUSTOM_COOKIES_DOMAIN = "Custom cookies domain"