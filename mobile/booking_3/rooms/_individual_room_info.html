<div data-role="collapsible" class="room-description {% if currentRoom.room_has_loggin %}room_with_login{% endif %}">

    {% for room in currentRoom.roomStructure %}

            <h1 class="room_title_wrapper {% if room.show_gallery_room %}room_title_absolute{% endif %}" {% if not rentacar %}onclick="showDescription($('.room_hidden_description_key_{{ room.roomKey }}'))"{% endif %}>
                <p class="room_title">{{ room.roomName|safe }}</p>
                {% if not club_version %}
                    <img class="plus_image" src="/static_1/images/booking_3/boton_mas.png">
                {% else %}
                    <div class="see_more_room">{{ T_ver_mas }}</div>
                {% endif %}

                {% if room.subtitle %}<p class="room_subtitle">{{ room.subtitle|safe }}</p>{% endif %}
            </h1>

            <div class="hidden_room_description_wrapper room_hidden_description_{{ forloop.parentloop.counter }} room_hidden_description_key_{{ room.roomKey }}" style="display: none;width: 100%;">
            <table class="description_wrapper" cellpadding="0" cellspacing="0" border="0">
                <tr class="title_room_wrapper"><td class="title_room">
                    {{ room.roomName|safe }}
                    {% if room.subtitle %}<p class="room_subtitle">{{ room.subtitle|safe }}</p>{% endif %}
                </td></tr>

                {% if room.pictures or room.roomPicture %}
                    <tr class="img_room_wrapper">
                        <td class="img_room">
                            <div class="flexslider">
                                <ul class="slides">
                                    <li><img src="{{ room.roomPicture }}{% if 'google' in room.roomPicture %}=s650{% endif %}"></li>
                                    {% for picture in room.pictures %}
                                        <li><img src="{{ picture.servingUrl }}{% if 'google' in picture.servingUrl %}=s650{% endif %}"/></li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </td>
                    </tr>
                {% endif %}

                <tr><td class="description_room">{{ room.roomDescription|safe }}</td></tr>
            </table>
            </div>

            {% if room.show_gallery_room %}
            {% if room.pictures or room.roomPicture %}
                <div class="gallery_room_wrapper gallery_room_{{ forloop.counter }}">
                    <div class="flexslider">
                        <ul class="slides">
                            <li><img src="{{ room.roomPicture }}{% if 'google' in room.roomPicture %}=s650{% endif %}"></li>
                            {% for picture in room.pictures %}
                                <li><img src="{{ picture.servingUrl }}{% if 'google' in picture.servingUrl %}=s650{% endif %}"/></li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                <script>
                    $(window).on("load", function () {
                        $(".gallery_room_{{ forloop.counter }}").flexslider({
                            controlNav: false,
                            animationLoop: false,
                            slideshow: false
                        });
                    });
                </script>
            {% endif %}
            {% endif %}


    {% endfor %}
</div>
{% for room in currentRoom.roomStructure %}
    {% if room.options_choices %}
        <div class="sectoption">
            <div class="titSelOpt">{{room.options_choices.title}}</div>
            <div class="bodySelOpt">
                <ul class="listaradio">
                    {% for option in room.options_choices.options %}
                        <li style="padding: 0.5em;">
                            <input type="radio"
                                   id="{{room.roomKey}}_{{forloop.parentloop.counter}}_radio_button"
                                   name="{{room.roomKey}}_{{forloop.parentloop.counter}}_radio_button"
                                   value="{{option.value}}"
                                   {% if option.selected %}checked{% endif %}>
                            <span>{{option.title}}</span>
                            <br>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    {% endif %}
{% endfor %}

<ul class="result_list" data-role="listview" data-inset="true" data-theme="c">

    {% for currentRate in currentRoom.rateResult %}
        <div class="rate_wrapper_element">
            <div style="display: none">
                <div class="rate_conditions" id="rateConditions_{{ forloop.parentloop.counter }}_{{currentRate.rateStructure.2}}" style="font-size:14px;">
                    {{currentRate.rateStructure.1|safe}}
                </div>
            </div>

            <div data-role="list-divider" class="rate_info {% if club_version %}{% if currentRate.logged_users %}only_logged{% endif %}{% endif %}"
                filter-tabs="{% for tab_filter in currentRate.filter_tabs %}{{ tab_filter|safe }} {% endfor %}"
            >
                <p class="list_divider_title">
                    {{currentRate.rateStructure.0|safe}} <a class="fancyAuto" title="{{T_condiciones_de}} {{currentRate.rateStructure.0}}" href="#rateConditions_{{ forloop.parentloop.counter }}_{{currentRate.rateStructure.2}}">{{T_condiciones}}<span class="asterisc">*</span></a>
                </p>

                {% for rate_key, rate_value in rates_conditions_days.items %}
                    {% if currentRate.rateStructure.2|safe == rate_key %}
                        <p class="free_cancellation_date">{{ rate_value.last_day|safe }}</p>
                    {% endif %}
                {% endfor %}

                {% if club_version %}<div class="offer_percentage_discount"></div>{% endif %}
            </div>

            {% for currentRegimen in currentRate.regimenResult %}
                <a href="#" class="regimen_option {% if currentRate.logged_users %}only_logged{% endif %}"
               {% if club_version and not currentRate.logged_users %}{# style="display: none" #}{% endif %}
                {% if currentRegimen.roomResults.promotionPercentage %}regimen_discount="{{ currentRegimen.roomResults.promotionPercentage|safe }}"{% endif %}
               onclick="{% if only_prices %}return false;{% endif %}javascript:event.preventDefault();
               if(event.target.className.indexOf('iconoInfo') > -1){return false};
                $('#selectedUUID').val('{{currentRegimen.roomResults.uuid}}');
                var result_msg = '';
                {% for room in currentRoom.roomStructure %}
                    {% if room.options_choices %}
                        result_msg += '<br><b>{{room.options_choices.title_default}}: </b>' +
                        $('input[name={{room.roomKey}}_{{forloop.parentloop.parentloop.counter}}_radio_button]:checked').val();
                        result_msg += ' ({{room.roomName}})';
                    {% endif %}
                {% endfor %}
                $('#extra_choice_option').val(result_msg);
                $('#selectionForm').submit();">
                    <li class="prices_rooms">
                        <div class="room_list_promotion_wrapper">
                            {% if currentRegimen.min_stay_warning %}
                            <div class="min_stay_info" style="font-size:12px;color:orange;/* margin-left:15px; */float: left;background: url(/static_1/images/booking/new_booking_process/info_orange.png) no-repeat left center;padding-left:28px;">{{ currentRegimen.min_stay_warning|safe }}</div>
                            {% endif %}

                            <div class="room_list_name">
                                {{ currentRegimen.regimenStructure.regimenName|safe }}
                                {% if currentRegimen.regimenStructure.regimenDescription %}
                                    <div class="regimen-popup-v2 fancyAuto" href="#regDesc-{{ currentRoom.roomStructure.0.roomKey }}-{{ forloop.parentloop.counter }}-{{ forloop.counter }} .regimen_content">
                                        <img class="iconoInfo" src="/static_1/images/icono-interrogacion.png" alt="">
                                    </div>

                                    <div class="regimen_popup_description" id="regDesc-{{ currentRoom.roomStructure.0.roomKey }}-{{ forloop.parentloop.counter }}-{{ forloop.counter }}" style="display:none;">
                                        <div class="regimen_content">
                                            <h1 class="regimen_main_title">{{ currentRegimen.regimenStructure.regimenName|safe }}</h1>
                                            {{ currentRegimen.regimenStructure.regimenDescription|safe }}
                                        </div>
                                    </div>
                                {% endif %}
                            </div>


                            {% if currentRegimen.regimenStructure.custom_message %}
                                <div class="custom_message_by_side_board" {% if currentRegimen.regimenStructure.custom_message.style %}style="{{ currentRegimen.regimenStructure.custom_message.style|safe }}" {% endif %}>
                                    {{ currentRegimen.regimenStructure.custom_message.message|safe }}
                                </div>
                            {% endif %}

                            {% if currentRegimen.roomResults.promotionNameList %}
                                <p class="room_list_name_offer">
                                    {% for promotion in currentRegimen.roomResults.promotionNameList %}
                                        {% if promotion.name %}
                                            {% if not forloop.first %}<span class="separator"> + </span>{% endif %}
                                            <span class="promotion_element_name {{ promotion.name_class }}" style="{% if promotion.color %}color: {{ promotion.color|safe }}{% endif %}{{ promotion.style }}">{% if promotion.percentage %}<span class='percentage'>-{{ promotion.percentage }}</span>{% endif %} {{ promotion.name|safe }}</span>
                                        {% endif %}
                                        {% if promotion.countdown %}
                                            <div class="banner_count_offers inherit">
                                                <div class="countdown_wrapper" {% if promotion.countdown.style %}style="background: {{ promotion.countdown.style|safe }}"{% endif %}>
                                                    <div class="icon_wrapper"><i class="fa fa-clock-o"></i></div>
                                                    {% if promotion.countdown.date %}<div class="slider_countdown" date="{{ promotion.countdown.date|safe }}"></div>{% endif %}
                                                </div>
                                            </div>
                                        {% endif %}
                                    {% endfor %}
                                </p>
                            {% else %}
                                {% if currentRegimen.roomResults.promotionName %}<p class="room_list_name_offer">{{ currentRegimen.roomResults.promotionName|safe }}</p>{% endif %}
                            {% endif %}
                        </div>

                        {% if currentRegimen.roomResults.hasPromotion %}
                            <div class="precioTotal">
                            <div class="vertical_center">
                                <span class="numero_noche">
                                    {% if currentRegimen.min_stay_warning %}
                                        <span>{{ currentRegimen.total_nights_message }}</span>
                                    {% else %}
                                        <span>{{ total_nights_message }}</span>
                                    {% endif %}
                                </span>
                                <p class="tachado"><span class="precioTotal_tachado currencyValue">{{currentRegimen.roomResults.total|price_format}} </span> <span class="monedaConv precio_final"> {{ currency|safe }}</span></p>
                                        <div class="precioTachadoDiv" style="display:none">
                                            <span class="tPrecioTachado currencyValue" title=""></span>
                                            <span class="tPrecioTachado monedaConv">{{ currency|safe }}</span><br>
                                        </div>
                                <span class="precio_final currencyValue price_{{ currentRegimen.specialPriceClass }}_{{ currentRoom.roomStructure.0.roomName4Class|safe }}__{{ currentRegimen.rate4Class }}__{{ currentRegimen.regimenStructure.regimen4Class|lower|striptags }}__{{ currentRegimen.roomResults.total4Class }}">{{currentRegimen.roomResults.promotionTotal|price_format}} </span> <span class="monedaConv precio_final"> {{ currency|safe }}</span>
                                {% if country_location_params %}{% if tax_not_included %}<div class="tax_not_included_label">{{ T_impuestos_no_incluidos }}</div>{% endif %}{% endif %}

                            </div>
                            </div>
                        {% else %}
                                                                      <div class="precioTachadoDiv" style="display:none">
                                            <span class="tPrecioTachado currencyValue" title=""></span>
                                            <span class="tPrecioTachado monedaConv">{{ currency|safe }}</span><br>
                                        </div>
                            <div class="precioTotal nopromo">
                                <div class="center_vert">
                                    <span class="center_elem currencyValue price_{{ currentRegimen.specialPriceClass }}_{{ currentRoom.roomStructure.0.roomName4Class|safe }}__{{ currentRegimen.rate4Class }}__{{ currentRegimen.regimenStructure.regimen4Class|lower|striptags }}__{{ currentRegimen.roomResults.total4Class }}">{{currentRegimen.roomResults.total|price_format}}</span>
                                    <span class="monedaConv precio_final">{{ currency|safe }}</span>
                                    {% if not only_prices %}<img class="arrow_room" src="/static_1/images/booking/flecha_motor_der.png">{% endif %}
                                    {% if country_location_params %}{% if tax_not_included %}<div class="tax_not_included_label">{{ T_impuestos_no_incluidos }}</div>{% endif %}{% endif %}
                                </div>
                            </div>

                        {% endif %}
                        {% if not only_prices %}
                        {% if currentRoom.roomStructure.0.on_request %}
                            <i class="fa fa-chevron-right on_request_button"></i>
                        {% else %}
                            <img class="arrow_room" src="/static_1/images/booking_3/flecha_derecha.png">
                        {% endif %}
                        {% endif %}

                    </li>
                </a>
            {% endfor %}
        </div>
    {% endfor %}
</ul>