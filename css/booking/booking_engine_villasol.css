/* BASE STYLING */

div#ui-datepicker-div {
  zoom: 0.95!important;
  margin-top: -30px;

}

.booking_form_title {
  background: #00822e!important;/*booking_widget_color*/
  width: 580px;
  display: none;
}



.booking_form {
  position: relative;
  background: #d8d9dd;/*$booking_widget_color_1*/
  width: 605px;
  padding: 0 20px;
}

.destination_wrapper {
  display: inline-block;
  vertical-align: top;
}

.destination_field {
  position: relative;
}

.destination_wrapper label {
    color: #5b5b5f;/*$booking_widget_color_3*/
}


.destination_wrapper .right_arrow {

    background: #00822e url(/static_1/images/booking/flecha_motor_der.png) no-repeat center center!important;/*$booking_widget_color_2*/
    position: absolute;
    top: 2.5px !important;
    right: 3px;
    height: 25px;
    width: 25px;

 }

 .destination_field .roomType {
  width: 333px;
  height: 30px;
 }

 .roomtype_selector {
  display: none;
  position: absolute;
  left: 20px;
  top: 45px;
  background: white;
  z-index: 999;
  width: 305px;
  padding-left: 9px;
  box-sizing: border-box;
 }

 .stay_selection {
  height: 55px;
  display: inline-block;
  vertical-align: top;
  margin-left: 10px;
  float: right;
 }

 .room_list_wrapper {
  display: inline-block;
  float: left;
  margin-top: 0px;
  width: 345px;
  padding: 3px 0;
  height: 30px;
 }

 .room {
  display: inline-block;
  margin-bottom: 0;
 }

 .room_title {
  vertical-align: top;
  display: inline-block;
  margin-top: 7px !important;
 }

 .adults_selector {
  width: 122px !important;
 }

 .adults_selector label {
  vertical-align: top;
  display: inline-block !important;
  margin-top: 7px;
 }

 .room_selector {
  display: inline-block;
 }

 .children_selector {
  width: 122px !important;
 }

 .children_selector label {
  vertical-align: top;
  display: inline-block !important;
  margin-top: 7px;
 }

 .wrapper_booking_button {
  display: inline-block;
  margin-top: 0;
  padding: 3px 0;
  /*margin-left: 23px;*/
  /*top: 82px;*/
  /*left: 280px;*/
  /*float: right;*/
 }


.stay_selection .entry_date_wrapper label,
.stay_selection .departure_date_wrapper label,
.stay_selection .rooms_number_wrapper label {
    color: #5b5b5f!important;/*$booking_widget_color_3*/
    font-size: 12px;
  }


.date_box .date_day {

    color: #00822e!important;/*$booking_widget_color_2*/
    font-size: 12px;

  }

.date_box .date_year {
    color: #5b5b5f!important;/*$booking_widget_color_1*/
    font-size: 10px;

  }


.room .room_title,
.room .adults_selector label,
.room .children_selector label {
    color: #5b5b5f!important;/*$booking_widget_color_3*/
    font-size: 12px;
  }




.wrapper_booking_button .promocode_input {

    color: #00822e!important;/*$booking_widget_color_2*/
    height: 30px;
    font-size: 12px;
    margin-top: 0;

  }

.wrapper_booking_button button {
    margin-left: 10px;
    background: #00822e!important;/*$booking_widget_color_2*/
    height: 30px;
    font-size: 12px;
    margin-top: 0;

  }

.horizontal_engine{

  background: #d8d9dd !important;/*$booking_widget_color_1*/
}




.selectric {
  background: white!important;

}

.selectric .label {

  color: #00822e!important;/*$booking_widget_color_2*/

}

.selectric .button {

  background: #00822e url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;/*$booking_widget_color_2*/

}

.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */

.selectricItems {

  background: #F9F9F9;
  border: 1px solid #CCC;

}

.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

.selectricItems li {

  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;


}


 .selectricItems li :hover {
    background: #F0F0F0;
    color: #444;
  }

 .selectricItems li .selected {
    background: #EFEFEF;
    color: #444;
    border-top-color: #E0E0E0;
  }


.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;

}


/* Special datepickers */
#ui-datepicker-div{
    margin-left: -20px !important;
}

.ui-widget-header {
background: #00822e!important;
    color: white;
}

.ui-widget-content {
    background: white;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
background: #00822e!important;
color: white;
}



/* BOOKING WIDGTE INLINE */


.boking_widget_inline {
  width: 1140px;
  height: 75px;
  margin: 0 auto;
  padding-top: 0px;
  /*background-color: gray;*/

}

.boking_widget_inline  .hotel_selector {
    left: 20px;
    top: 70px;
  }

.boking_widget_inline  .booking_form {
    padding-top: 0;
    padding-bottom: 0;
    position: relative;
    /*background-color: gray;*/
    background: #d8d9dd;/*$booking_widget_color_1*/
  }

.boking_widget_inline  .booking_form_title {
    display: block;
  }

.boking_widget_inline .booking_form {
    width: 1140px;
    padding: 0;
  }

.boking_widget_inline  .destination_wrapper {
    float: left;
  }

.boking_widget_inline  .stay_selection {
    float: left;
    margin-left: 90px;


  }

.boking_widget_inline  .stay_selection .entry_date_wrapper label,
.boking_widget_inline  .stay_selection .departure_date_wrapper label,
.boking_widget_inline  .stay_selection .rooms_number_wrapper label {
      /*color: rgb(120,120,120);*/
      font-size: 12px;
    }


.boking_widget_inline  .room_list_wrapper {
    float: left;
    margin-left: 20px;
    margin-right: 20px;
    margin-top: 5px;
  }

.boking_widget_inline  .room {
    float: right;
}

.boking_widget_inline .room .room_title,
.boking_widget_inline .room .adults_selector label,
.boking_widget_inline .room .children_selector label,
.boking_widget_inline .room .babies_selector label {
      /*color: gray;*/
      font-size: 12px;
    }
.boking_widget_inline .room .room_title {
      text-align: right;
    }

.boking_widget_inline .wrapper_booking_button {
    display: inline;
    margin-top: 10px;


  }

.boking_widget_inline .wrapper_booking_button  button {
      float: none;
    }

 .boking_widget_inline .promocode_input {
    margin-right: 20px;
    margin-top: 19px;
    /* background-color: rgb(90,90,90); */
  }

 .boking_widget_inline  .wrapper_booking_button button {
    width: 170px !important;
  }

.boking_widget_inline   .selectric, .date_box {
    /*background: rgb(90,90,90);*/
    height: 30px;
 }



/* LAST UPDTAES */

.boking_widget_inline {
  width: 570px;
}

.boking_widget_inline .wrapper_booking_button {
  display: block;
  margin-top: 10px;
  clear: both;
  margin-left: 95px;

}


.boking_widget_inline .booking_form {
  width: 570px;
  height: 150px;
}

.boking_widget_inline .stay_selection {

  margin-left: 20px;
}
.boking_widget_inline .room_list_wrapper {

  margin-left: 10px;
}

.boking_widget_inline div#ui-datepicker-div {
 zoom: 0.58!important;

}

.selectric {
  height: 30px;
}

.selectric .label {
  font-size: 12px;
  line-height: 30px;
}

.selectric .button {
  width: 25px;
  height: 25px;
}