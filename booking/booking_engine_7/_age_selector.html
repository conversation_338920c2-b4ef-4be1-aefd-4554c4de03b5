<style>
.ui-dialog {
	position: absolute;
	top: 0;
	left: 0;
	padding: .2em;
	outline: 0;
}
.ui-dialog .ui-dialog-titlebar {
	padding: .4em 1em;
	position: relative;
}
.ui-dialog .ui-dialog-title {
	float: left;
	margin: .1em 0;
	white-space: nowrap;
	width: 90%;
	overflow: hidden;
	text-overflow: ellipsis;
}
.ui-dialog .ui-dialog-titlebar-close {
	position: absolute;
	right: .3em;
	top: 50%;
	width: 21px;
	margin: -10px 0 0 0;
	padding: 1px;
	height: 20px;
}
.ui-dialog .ui-dialog-content {
	position: relative;
	border: 0;
	padding: .5em 1em;
	background: none;
	overflow: auto;
	text-align: center;
}
.ui-dialog .ui-dialog-buttonpane {
	text-align: left;
	border-width: 1px 0 0 0;
	background-image: none;
	margin-top: .5em;
	padding: .3em 1em .5em .4em;
}
.ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
	/*float: right;*/
	float: none;
	text-align: center;
}
.ui-dialog .ui-dialog-buttonpane button {
	margin: .5em .4em .5em 0;
	cursor: pointer;
}
.ui-dialog .ui-resizable-se {
	width: 12px;
	height: 12px;
	right: -5px;
	bottom: -5px;
	background-position: 16px 16px;
}
.ui-dialog .ui-button-text {
	/*padding: 10px;*/
	/*margin: 10px;*/
}

.ui-dialog .ui-button{
	/*height: 30px;*/
}

.ui-draggable .ui-dialog-titlebar {
	cursor: move;
}

.ui-dialog {
	min-width: 300px;
    z-index: 9999;
}

</style>

 <style>
	.ui-dialog-titlebar-close {
 		display: none !important;
	}

	#dialog-form{
		padding: 10px;

	}

	.ui-dialog-titlebar{
		padding: 5px;
	}

	.kidAgesSelect, .babyAgesSelect{
		margin-left: 10px;

	}

     .ui-dialog{
         z-index: 99999;
     }


</style>

<!-- New style Age popup -->
<style>
body .ui-widget-overlay.ui-front {
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  z-index: 9998;
  position: fixed;
}
body .ui-dialog.ui-widget {
  padding: 20px 10px;
  border: 0;
  border-radius: 0;
  line-height: 1;
    width: 100% !important;
    max-width: 350px;
}
body .ui-dialog.ui-widget .ui-dialog-titlebar {
  background: none!important;
  padding: 0 0 20px 0;
  border-radius: 0;
  border: 0;
  color: black!important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  text-align: center;
  margin-bottom: 20px;
}
body .ui-dialog.ui-widget .ui-dialog-titlebar .ui-dialog-title {
  width: 100%;
  margin: 0;
}
body .ui-dialog.ui-widget #dialog-form {
  padding: 0;
  text-align: left;
  width: 100% !important;
    max-width: 350px;
    overflow: visible;
}
body .ui-dialog.ui-widget #dialog-form form label {
  clear: both;
  width: 100%;
  float: left;
}
body .ui-dialog.ui-widget #dialog-form form .wrapper_age_kids {
  display: inline-block;
  width: 100%;
  float: left;
}
body .ui-dialog.ui-widget #dialog-form form .wrapper_age_kids select {
  width: calc((100% - 10px)/2);
  margin: 0px 10px 10px 0;
  float: left;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 5px 10px;
}
body .ui-dialog.ui-widget #dialog-form form .wrapper_age_kids select:focus {
  opacity: 1 !important;
}
body .ui-dialog.ui-widget #dialog-form form .wrapper_age_kids select:nth-child(even) {
  margin-right: 0;
}
body .ui-dialog.ui-widget #dialog-form form .wrapper_age_kids select:nth-child(-n+2) {
  margin-top: 10px;
}
body .ui-dialog.ui-widget .ui-dialog-buttonpane {
  margin: 0;
  padding: 0;
  vertical-align: bottom;
  display: inline-block;
  float: left;
  width: 100% !important;
    max-width: 350px;
  border-radius: 0;
  margin-top: 10px;
}
body .ui-dialog.ui-widget .ui-dialog-buttonpane .ui-button {
  margin: 0;
  display: inline-block;
  width: 100% !important;
    max-width: 350px;
  border-radius: 0;
  background: #446ca9;
  color:white;
}
body .ui-dialog.ui-widget .ui-resizable-handle {
  display: none;
}
</style>
<!--  <link rel="stylesheet" href="http://code.jquery.com/ui/1.10.1/themes/base/jquery-ui.css" type="text/css"/> -->
 <div id="dialog-form"  title="{{T_edades_nino|safe}}" style="display:none">
    <form>
        {% for roomIndex in "123" %}
            <label id="label{{roomIndex}}" for="name">{{T_habitacion}} {% if not hide_room_index_in_agekids_selector %}{{roomIndex}}{% endif %}</label>

            <div class="wrapper_age_kids clearfix">
                {% if kidIndexRageList %}
                      {% for kidIndexRange in kidIndexRageList %}
                             <select class="kidAgesSelect" id="agesRoom{{roomIndex}}_{{forloop.counter}}" name="name" tabindex="1">
                                 {% for age in kidIndexRange %}
                                      <option value="{{ age }}" {% if forloop.first %}selected="selected"{% endif %}>{{ age }}</option>
                                 {% endfor %}
                            </select>
                      {% endfor %}
                {% else %}
                    {% for kidIndex in "1234" %}
                        <select class="kidAgesSelect" id="agesRoom{{roomIndex}}_{{kidIndex}}" name="name" tabindex="1">
                            {% for age in kidsAgeRange %}
                                <option value="{{ age }}" {% if forloop.first %}selected="selected"{% endif %}>{{ age }}</option>
                            {% endfor %}
                        </select>
                    {% endfor %}
                {%  endif %}
            </div>

            {% if babiesSelection %}
                <label id="baby_label{{roomIndex}}" for="name">{{T_bebes}}</label>
                <div class="wrapper_age_kids clearfix">
                    {% for kidIndex in "1234" %}
                        <select class="babyAgesSelect" id="agesRoom_baby{{ roomIndex }}_{{ forloop.counter }}" name="name"
                                onchange="" tabindex="1">
                            <option value="0" selected="selected">0</option>
                            {% for age in babiesSelection|create_range %}
                                <option value="{{ age }}">{{ age }}</option>
                            {% endfor %}
                        </select>
                    {% endfor %}
                </div>
            {% endif %}
            <br/>
            <br/>
		{% endfor %}

        <input type="hidden" id="id_div_parent">
    </form>
</div>

<script type="text/javascript">
    ($ => {
        window.sendActiveForm = function () {
            return $(".booking_form").each(function () {

                if ($(this).find(".submit_button:not(.popup_button)").css("display") == "none" || $(this).find(".submit_button:not(.popup_button)").attr("disabled") == "disabled") {
                    if ($('#booking-search-popup').length) {
                        show_booking_search_popup($(this));
                        return false;
                    }

                    $(this).submit();
                    return true;
                }
            });
        }

        window.bookingSearchWithAgeSelection = function () {
            $(".booking_form").each(function () {
                let children_selected = false;
                $('.children_selector select.room_selector, .babies_selector select.room_selector').each(function(){
                   if($(this).val() > 0){
                       children_selected = true;
                   }
                });

                if ($('.ages_mandatory_wrapper').length && window.is_children_selector_changed && children_selected) {
                    window.is_children_selector_changed = false;
                    $('.ages_mandatory_wrapper').show();
                    toggle_guest_selector(true);

                    setTimeout(() => {
                        $(this).find(".submit_button").css("display", "block");
                    }, 1000);

                    return false;
                }

                if ($(this).find(".submit_button:not(.popup_button)").css("display") === "none" || $(this).find(".submit_button:not(.popup_button)").attr("disabled") == "disabled") {

                    //Check that user has selected startDate & endDate
                    var start_date = $(this).find("input[name='startDate']").val(),
                        end_date = $(this).find("input[name='endDate']").val();

                    if (!(start_date && end_date)) {
                        $(this).find(".submit_button").css("display", "block");
                        if (!end_date) {
                            if (typeof (open_departure_datepicker) != 'undefined') clearTimeout(open_departure_datepicker);
                            open_departure_datepicker = setTimeout(function () {
                                $(".departure_date_wrapper").trigger('click');
                            }, 200)
                        }
                        return false;
                    }

                    if (kids_selected_in_widget($(this))) {
                        var destination_field = $(this).find(".destination");
                        var booking_url = $(this).attr("action");

                        if (destination_field.length && booking_url.indexOf("booking0") == -1 && destination_field.val() == "") {
                            booking_url = $(this).attr("action", $(this).attr("action").replace("booking1", "booking0")).attr("action");
                        }

                        {% if not inline_ages %}
                            updateAgesOptions();
                            $.fancybox.close();
                            var dialog_opt = {
                                width: "auto",
                                autoOpen: false,
                                modal: true,
                                buttons: {
                                    "{{T_continuar|safe}}": function () {
                                        for (let room_number = 1; room_number <= 3; room_number++) {
                                            const target_hidden_input = $("input[name=agesKid" + room_number + "]");
                                            let ages_value_string = [];

                                            {% if babiesSelection %}
                                                const target_hidden_input_baby = $(`input[name="agesBaby${room_number}"]`);
                                                let ages_value_string_baby = [];
                                            {% endif %}

                                            for (let kid_number = 1; kid_number <= 4; kid_number++) {
                                                let age_selected = '';
                                                const needed_age_id = `agesRoom${room_number}_${kid_number}`;

                                                $(".kidAgesSelect:visible").each(function () {
                                                    if ($(this).attr('id') == needed_age_id) {
                                                        age_selected = $(this).val();
                                                    }
                                                });

                                                // avoid undefined or empty values
                                                if (age_selected) ages_value_string.push(age_selected);

                                                {% if babiesSelection %}
                                                    let age_selected_baby = '';
                                                    const needed_age_id_baby = `agesRoom_baby${room_number}_${kid_number}`;

                                                    $(".babyAgesSelect:visible").each(function () {
                                                        if ($(this).attr('id') == needed_age_id_baby) {
                                                            age_selected_baby = $(this).val();
                                                        }
                                                    });

                                                    if(age_selected_baby) ages_value_string_baby.push(age_selected_baby);
                                                {% endif %}
                                            }

                                            ages_value_string = ages_value_string.join(';');
                                            target_hidden_input.each(function () {
                                                $(this).val(ages_value_string);
                                            });

                                            {% if babiesSelection %}
                                                ages_value_string_baby = ages_value_string_baby.join(';');
                                                target_hidden_input_baby.each(function () {
                                                    $(this).val(ages_value_string_baby);
                                                });
                                            {% endif %}
                                        }

                                        $(this).dialog("close");
                                        sendActiveForm();
                                    }
                                }
                            };
                            $("#dialog-form").dialog(dialog_opt).dialog("open");
                        {% else %}
                            prepare_kids_ages();
                            sendActiveForm();
                        {% endif %}
                    }
                }
            });
        }

        window.kids_selected_in_widget = function (form) {
            let minors_are_selected = false;
            const minor_selectors = $(form).find('select[name^="childrenRoom"], select[name^="babiesRoom"]');
            minor_selectors.each(function() {
                const selected_option = parseInt($(this).find("option:selected").val() ?? 0);
                if (selected_option > 0) {
                    minors_are_selected = true;
                }
            });

            return minors_are_selected;
        }

        window.prepare_kids_ages = function () {
            $(".booking_form").each(function () {
                var booking_form = $(this);
                if (booking_form.find(".submit_button:not(.popup_button)").css('display') == 'none' || booking_form.find(".submit_button:not(.popup_button)").attr("disabled") == "disabled") {
                    let numRooms = booking_form.find(".rooms_number option:selected").val();
                    for (let i = 1; i <= numRooms; i++) {
                        let roomKids = booking_form.find(".children_room_" + i + " option:selected").val(),
                            roomBabies = booking_form.find(".babies_room_" + i + " option:selected").val(),
                            roomAges = booking_form.find(".room_ages_" + i),
                            kids_ages = '',
                            babies_ages = '';

                        for (let j = 1; j <= roomKids; j++) {
                            let ageKid = roomAges.find('.kids_age_selection #agesRoom' + i + '_' + j + ' option:selected').val();
                            kids_ages += ageKid + ';'
                        }

                        for (let j = 1; j <= roomBabies; j++) {
                            let ageBaby = roomAges.find('.babies_age_selection #babyAgesRoom' + i + '_' + j + ' option:selected').val();
                            if (typeof(ageBaby) == 'undefined') {
                                ageBaby = 0;
                            }
                            babies_ages += ageBaby + ';'
                        }

                        // Check if input exists and create it if not
                        if (booking_form.find("#agesKid" + i).length == 0) {
                            booking_form.append('<input type="hidden" id="agesKid' + i + '" name="agesKid' + i + '" value="">');
                        }

                        if (booking_form.find("#agesBaby" + i).length == 0) {
                            booking_form.append('<input type="hidden" id="agesBaby' + i + '" name="agesBaby' + i + '" value="">');
                        }

                        booking_form.find("#agesKid" + i).val(kids_ages);
                        booking_form.find("#agesBaby" + i).val(babies_ages);
                    }
                }
            })
        }


        window.updateAgesOptions = function () {


            $(".booking_form").each(function () {

                if ($(this).find(".submit_button:not(.popup_button)").css("display") == "none" || $(this).find(".submit_button:not(.popup_button)").attr("disabled") == "disabled") {

                    var numHab = $(this).find(".rooms_number option:selected").val();
                    for (var i = 3; i > 0; i--) {
                        for (var j = 1; j <= 4; j++) {
                            $("#agesRoom" + i + "_" + j).hide();
                            $("#agesRoom_baby" + i + "_" + j).hide();

                            var roomElem = $("#agesRoom" + i + "_" + j);
                            var babyElem = $("#agesRoom_baby" + i + "_" + j);

                            if (roomElem.parent().parent().hasClass('selectricWrapper')) {
                                roomElem.parent().parent().hide();
                            }

                            if (babyElem.parent().parent().hasClass('selectricWrapper')) {
                                babyElem.parent().parent().hide();
                            }
                        }

                        $("#label" + i).hide();
                        $("#baby_label" + i).hide();
                    }



                    for (var i = numHab; i > 0; i--) {
                        var childrenRoom = $(this).find(".children_room_" + i + " option:selected").val();
                        var babiesRoom = $(this).find(".babies_room_" + i + " option:selected").val();

                        showAges("#label", i, childrenRoom);
                        showAges("#baby_label", i, babiesRoom);
                    }

                    function showAges(labelPrefix, i, num) {
                        if (num > 0) {
                            $(labelPrefix + i).show();
                            ageSelectPrefix = (labelPrefix === "#baby_label") ? "#agesRoom_baby" : "#agesRoom";

                            for (var j = 1; j <= num; j++) {
                                $(ageSelectPrefix + i + "_" + j).show();
                                if ($(ageSelectPrefix + i + "_" + j).parent().parent().hasClass('selectricWrapper')) {
                                    $(ageSelectPrefix + i + "_" + j).parent().parent().show();
                                }
                            }
                        }
                    }
                }
            });
        }

        $(function () {
            updateAgesOptions(1);

            setTimeout(() => {
                $(".children_selector select.room_selector, .babies_selector select.room_selector").change(function() {
                    window.is_children_selector_changed = true;
                });
            }, 1500);

            $('.ages_mandatory_wrapper .button_mandatory_ages').click(function () {
                $(".booking_form .submit_button").click();
            });
        });


    })(jQuery);
</script>


