<div class="destination_wrapper">
  <label for="destination">{{ T_seleccionar_hotel }}</label>
  <div class="destination_field">
  <input class="destination" readonly="readonly" type="text" name="destination" placeholder="{{T_hotel}}">
  <input  type="hidden" id="default_destination_placeholder" name="default_destination_placeholder" class="default_destination_placeholder" value="{{T_hotel}}">

  <div class="right_arrow"></div>
  </div>
</div>


<div class="hotel_selector hotel_selector_2steps">
    <div class="hotel_selector_inner hotel-selector-inner-step1" id="hotel-selector-inner-step1">
        <div class="close_hotel_selector" style="background: url(/static_1/lib/fancybox/source/fancybox_sprite.png);"></div>


        {% for group_hotel in hotels_list_with_groups_2steps %}

            <ul class="{{ group_hotel.group_class }} num-ul-{{ forloop.counter }}">

            <li class="title_group selector-step1" data-target="{{ group_hotel.group_class }}"><h3>{{ group_hotel.group_label|safe }}</h3></li>

            {% for location in group_hotel.location_list%}
                <li><h3 class="location_selector selector-step1" data-target="{{ group_hotel.group_class }}">{{ location|safe }}</h3></li>

            {% endfor %}

            </ul>
        {% endfor %}



        <div class="close_hotel_selector"></div>
    </div>


{% for group_hotel in hotels_list_with_groups_2steps %}
    <div class="hotel_selector_inner hotel-selector-inner-step2 hotel-selector-inner-step2-{{ group_hotel.group_class }}" id="hotel-selector-inner-step2-{{ group_hotel.group_class }}" style="display:none">
         <div class="close_hotel_selector" style="background: url(/static_1/lib/fancybox/source/fancybox_sprite.png);"></div>




            <ul class="{{ group_hotel.group_class }} num-ul-{{ forloop.counter }}">

            <li class="title_group selector-step1" id="step2-group-title-{{ group_hotel.group_class }}"><h3>{{ group_hotel.group_label|safe }}</h3></li>

           {% for hotel in group_hotel.group_list %}
                <li id="{{ hotel.id }}" class="{{ hotel.class }} hotel_selector_option">
                    {% if hotel.big_name %}
                    <h3 class="selector-big-name">{{ hotel.big_name|safe }}</h3>
                    {% endif %}

                    {% if hotel.newLabel %}
                    <span class="redCircle"></span><span class="newLabel-selector-hotel">{{ newLabel }}</span>
                    {% endif %}


                    <h3 class="title_selector">{{ hotel.name|safe }}</h3>

                </li>
                <input type="hidden" id="url_booking_{{ hotel.id }}" value={{ hotel.url_booking }}>
                <input type="hidden" id="namespace_{{ hotel.id }}" value={{ hotel.namespace }}>

            {% endfor %}


            <li class="back-link-2-step1">{{ back_link|safe }} {{ T_volver }}</li>

             </ul>




        <div class="close_hotel_selector"></div>
    </div>
{% endfor %}


</div>


