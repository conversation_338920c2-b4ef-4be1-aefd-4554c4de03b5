# coding=utf-8
import html.parser
import json
import logging
import random
from bs4 import BeautifulSoup
from datetime import datetime

from flask import request

from booking_process.constants import advance_configs_names
from booking_process.constants.advance_configs_names import CUSTOM_DOMAIN, SUPPORT_AMERICAN_EXPRESS, \
	USE_PAYMENT_GATEWAY, TRUNCATE_DECIMAL_BOOKING, CUSTOM_SEAL, NUMBER_OF_ROOMS_DISABLES, \
	BOOKING3_VAT_INCLUDED, ASK_BIRTHDAY, CREDIT_CARD_CONDITIONAL, HIDE_CITY_BOOKING3, DISABLED_CREDIT_CARD_TYPES, \
	ADD_CREDIT_CARD_TYPES, OPTIONAL_PERSONALID_BOOKING3, \
	OPTIONAL_BORNDATE_BOOKING3, SELECTIVE_BOOKING3_DISABLE, GATEWAY_CREDIT_CARD_BY_TOKEN, \
	HOTEL_COUNTRY_LOCATION, CC_OWNER_NAME, \
	PCI_TOKEN, B<PERSON><PERSON>ING_DATA, SHOW_MANDATORY_FIELD_BOOKING3, NO_SESSION_EXPIRE, \
	ACCOMODATION_TAX_INCREMENT_BY_PAX_NIGHT, NOT_INCLUDE_ACCOMODATION_TAX, \
	TOKEN_ONLY_SPECIAL_RATE, PAYMENT_FAILED_MESSAGE, GENERIC_BOOKING_PROCESS, \
	GATEWAY_PAYMENT_AMOUNT, SHOW_GATEWAY_LOGO, PUBLIC_CAPTCHA_KEY, OPTIONAL_INPUTS_BOOKING3, ROUND_DECIMAL_BOOKING
from booking_process.constants.session_data import REDIRECT_MODIFICATION_ENABLED
from booking_process.constants.web_configs_names import BONO_GIFT_CUSTOM
from booking_process.handlers.bonos.bonos_utils import get_total_price_for_bonos
from booking_process.handlers.errorControl import ErrorControl, SESSION_EXPIRED_RETRY
from booking_process.libs.communication import directDataProvider
from booking_process.libs.external_integrations.fuertehoteles.fuertehoteles_methods import FUERTE
from booking_process.libs.pasarelas.gateway_constants import TOTAL_PRICE, AMOUNT_SENT_TO_GATEWAY, \
	FORCED_PAYMENT_TYPE_ERROR, \
	PAYMENT_GATEWAY_FAILED, NUMDAYS_SENT_TO_GATEWAY, \
	TOTAL_PRICE_FORCED, SERMEPA_SAVE_CC_BY_TOKENIZATION, TPV_TYPE_PAYMENT_SELECTED, GATEWAY_LOGO, \
	USE_PAYMENT_METHOD, GATEWAY_LOGOS, PAY_ALWAYS, PVP_TOTAL_PRICE, NET_TOTAL_PRICE, \
	CURRENCY, SAVE_CARD_TOKEN_BY_TOKENIZATION, TOTAL_PAYMENT_SECURE
from booking_process.libs.pasarelas.gateway_utils import calculatePriceToSendToGateway, getForcedPaymentType, \
	isBizumPayment, getDiscountForGatewayOptionalEarlypay
from booking_process.libs.pasarelas.payment_gateway import PaymentGateway, SERMEPA, PAYPAL, PLACETOPAY, REDUNICRE, \
	SEQURA, SIBS, PAYU, AMAZONPAY, SECURE_PAYMENT, PAYCOMET
from booking_process.libs.pasarelas.sibs.sibs_constants import SIBS_PAYMENT_CARD, SIBS_PAYMENT_MBWAY
from booking_process.utils.auditing import auditUtils
from booking_process.utils.booking import rate_info_calculator
from booking_process.utils.booking.additional_services.additional_services_methods import get_selected_supplements
from booking_process.utils.booking.booking3_utils.booking3_context import \
	update_different_currencies_price_display_context

from booking_process.utils.booking.bookingUtils import getSeparatedPricesPerRoom, \
	news_search_session_expire_data, retreive_free_night_info, \
	_add_prefix_to_identifier, build_autofill_popup, need_to_wait_for_content
from booking_process.utils.booking.booking_context.general_context import build_error_page_template
from booking_process.utils.booking.rates.rate_info import rateIsNonRefundable
from booking_process.utils.prices.price_utils import set_booking_discount
from booking_process.utils.session.booking_cache_datastore import set_booking_html
from booking_process.utils.taxes.accomodation_tax_utils import get_accomodation_total_increment
from booking_process.utils.booking.selections.selection_utils import getSelectedRegimen, getSelectedRateKey, \
	getSelectedRoom, get_booking_params
from booking_process.utils.booking.selections.selection_price_utils import getSelectedPrice, getTotalPrice, \
	getPriceSupplements
from booking_process.utils.booking.booking_datastore_utils import buildResultStructures, \
	get_available_additional_services
from booking_process.utils.booking.marketing.marketing_utils import build_custom_messages_for_rate
from booking_process.utils.booking.conditions.rate_conditions import get_cancellation_days_and_dates, \
	get_text_rate_conditions
from booking_process.utils.booking.sales_demand import build_booking_cookie_popup
from booking_process.utils.booking.searchs.search_utils import buildSearch, check_explicit_namespace_at_request
from booking_process.utils.bookingConstants import PRODUCT_BONO, UPGRADE_SEPARATOR
from booking_process.constants.session_data import SELECTED_OPTION_KEY, PRICE_OPTION_KEY_PREFIX, SEARCH_KEY_PREFIX, \
	PRICE_OPTION_KEY_PREFIX_V2, PRICE_FORCED_ON_REQUEST, CREATE_PRODUCT_PAYMENT, PRICE_BONO_TPV, PRICE_BONO_EXTRA, \
	BOOKING3_LINK_TPV, BONO_GIFT_CUSTOM_DESIGN, BONO_INFO, BONO_NIGHTS, BONOGIFT_USED, NO_ASK_CREDIT_CARD, \
	FORCE_GATEWAY_PAYMENT_MODIFICATION, RESERVATION_MODIFICATION, I_AM_A_RESIDENT, FORCE_EXTERNAL_CANCEL_POLICIES, \
	EXTRA_PARAMS_SEARCH, LOCATION_MODIFICATION, VIRTUAL_SERVICES_SELECTED, ORIGINAL_PRICE_BEFORE_DISCOUNT, \
	SECURE_PAYMENT_FORCED, BIZUM_FORCED, AMAZON_REDSIS_FORCED, UPGRADING_TEXT_ROOM, CUSTOM_SESSION_DOMAIN
from booking_process.utils.callcenter.callcenter_utils import paramsWebSupport
from booking_process.utils.country import ALL_COUNTRIES_LIST
from booking_process.utils.country.configs.country_configs_utils import CountryConfigsBuilder
from booking_process.utils.credit_card.creditCardConditional import CreditCardConditional
from booking_process.utils.credit_card.template_context import credit_card_b3_context, get_credit_cards_type
from booking_process.utils.currency.currencyUtils import get_currency_symbol, get_currency
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.hotel_data import get_internal_url
from booking_process.utils.data_management.integrations_utils import get_integration_configuration_properties
from booking_process.utils.data_management.pictures_utils import getPicturesForKey
from booking_process.utils.booking.promotions.promotions_utils import get_promotions_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.data_management.supplements_utils import get_all_supplements_map
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.dates.dates_management import translate_dateformat_to_datepickerformat
from booking_process.utils.development.dev_booking_utils import DEV
from booking_process.utils.email.email_utils_third_party import notify_exception
from utils.gift_bono.gift_bono_controller import GiftBonoPromocodeController
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_code
from booking_process.utils.mobile.bookingProcessMobile import is_new_booking_process_mobile_version, \
	booking_process_mobile_reservation_summary
from booking_process.utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.namespaces.namespace_utils import get_namespace, set_namespace
from booking_process.utils.packages import package_utils
from booking_process.utils.pci.pci import PCIController
from booking_process.utils.redirections.pathRedirections import custom_redirection_path
from utils.flask_requests import response_utils
from booking_process.utils.request.request_utils import getUserIp
from utils.flask_requests.response_utils import get_response_content
from booking_process.utils.shopping_cart.booking_cart import is_enabled_shopping_cart
from booking_process.utils.taxes import separate_tax_and_price

from booking_process.utils.session import session_manager
from booking_process.utils.session.session_manager import get_session_id
from booking_process.handlers.base_handler import BaseWebHandler


SEND_LINK = 'send_link_owner'


class TPVBonosHandler(BaseWebHandler):
	'''
	In this step the user adds his personal information
	'''
	def showBookingWidget(self):
		return False

	def showBirthdayDate(self):
		if get_config_property_value(ASK_BIRTHDAY):
			return False

		return True

	def optionalId(self):
		if get_config_property_value(OPTIONAL_PERSONALID_BOOKING3) or 'personalID' in get_config_property_value(OPTIONAL_INPUTS_BOOKING3):
			return True

		return False

	def optionalBornDate(self):
		if get_config_property_value(OPTIONAL_BORNDATE_BOOKING3):
			return True

		return True

	def isAmazonpayPayment(self):

		if get_config_property_value(advance_configs_names.USE_PAYMENT_GATEWAY_AMAZONPAY_WITH_REDSIS):
			return True

		return False

	def isBizumPayment(self):

		if DEV:
			return True

		config = get_web_configuration(BONO_GIFT_CUSTOM)
		if config and config.get('optional_payment') and 'bizum' in config.get('optional_payment'):
			return True

		if get_config_property_value(advance_configs_names.USE_PAYMENT_GATEWAY_WITH_BIZUM):
			return True

		return False

	def build_multiple_gateways(self, language):
		logging.info("building TPV for bonos")

		multiple_gateways = []

		payments_ways = get_config_property_value(USE_PAYMENT_GATEWAY)
		config = get_web_configuration(BONO_GIFT_CUSTOM)
		if config and config.get('optional_payment'):
			payments_ways = config.get('optional_payment')

		has_bizum = self.isBizumPayment()
		has_amazonpay_redsis = self.isAmazonpayPayment()

		if payments_ways and (len(payments_ways.split(";")) > 1 or has_bizum or has_amazonpay_redsis):
			logging.info("Building multiple gateways for bonos. Payments ways: %s", payments_ways)
			for my_gateway in payments_ways.split(";"):
				typegateway = ""
				replace_txt_pasarela = ""

				if SERMEPA in my_gateway:
					typegateway = SERMEPA
					replace_txt_pasarela = get_web_dictionary(language).get("T_pasarela_tpv", "") + " <img src='/static_1/images/booking/tarjetasCredito2.gif?v=1' class='icon_pasarela_pago'>"
				if PAYCOMET in my_gateway:
					typegateway = PAYCOMET
					replace_txt_pasarela = get_web_dictionary(language).get("T_pasarela_tpv","") + " <img src='/static_1/images/booking/tarjetasCredito2.gif?v=1' class='icon_pasarela_pago'>"
				elif PAYPAL in my_gateway:
					typegateway = PAYPAL
					#icon paypal contains the word "paypal", so it is enougth with it (no text needed)
					replace_txt_pasarela = "<img src='/static_1/images/booking/paypal.png' class='icon_pasarela_pago'>"
				elif PLACETOPAY in my_gateway:
					typegateway = PLACETOPAY
					#icon paypal contains the word "paypal", so it is enougth with it (no text needed)
					replace_txt_pasarela = "<img src='/static_1/images/booking/paypal.png' class='icon_pasarela_pago'>"
				elif AMAZONPAY in my_gateway:
					typegateway = AMAZONPAY
					replace_txt_pasarela = "<img style='margin-left:5px; width:80px;' src='/static_1/images/booking/amazonpay.png' class='icon_pasarela_pago'>"
				elif SEQURA in my_gateway:
					typegateway = SEQURA
					replace_txt_pasarela = "<img style='width: 60px;' src='/static_1/images/sequra.webp' class='icon_pasarela_pago'>"
				elif SIBS in my_gateway:
					config = get_integration_configuration_properties("SIBS")
					if not config:
						config = get_integration_configuration_properties(my_gateway)
					replace_txt_pasarela = "<img style='width: 60px;' src='/static_1/images/creditcard.png' class='icon_pasarela_pago'>"
					pago_pasarela_msg = get_web_dictionary(language).get("T_pago_multiple", "").replace("@@TIPOPASARELA@@", replace_txt_pasarela)
					multiple_gateways.append({"typegateway": SIBS, "pago_pasarela_msg": pago_pasarela_msg, "subtype": SIBS_PAYMENT_CARD})
					mbway_payment = config.get('MBWAY_payment')
					if mbway_payment and mbway_payment.lower() == 'true':
						replace_txt_pasarela = "<img style='width: 60px;' src='/static_1/images/mbway.png' class='icon_pasarela_pago'>"
						pago_pasarela_msg = get_web_dictionary(language).get("T_pago_multiple", "").replace("@@TIPOPASARELA@@", replace_txt_pasarela)
						multiple_gateways.append({"typegateway": SIBS, "pago_pasarela_msg": pago_pasarela_msg, "subtype": SIBS_PAYMENT_MBWAY})

				if typegateway:
					pago_pasarela_msg = get_web_dictionary(language).get("T_pago_multiple", "").replace("@@TIPOPASARELA@@", replace_txt_pasarela)
					my_gateway={"typegateway": typegateway, "pago_pasarela_msg" : pago_pasarela_msg}
					multiple_gateways.append(my_gateway)

		if SECURE_PAYMENT in payments_ways:
			replace_txt_pasarela = get_web_dictionary(language).get("T_pago_seguro", "")
			typegateway = SECURE_PAYMENT
			my_gateway = {"typegateway": typegateway, "pago_pasarela_msg": replace_txt_pasarela}
			multiple_gateways.append(my_gateway)

		if has_bizum:
			typegateway = "Bizum"
			replace_txt_pasarela = '<span class="bizum_text">%s</span>' % get_web_dictionary(language).get("T_pago_bizum", "")
			replace_txt_pasarela += " <img src='/static_1/images/logo-bizum2.png' class='icon_pasarela_pago'>"
			my_gateway = {"typegateway": typegateway, "pago_pasarela_msg": replace_txt_pasarela}
			multiple_gateways.append(my_gateway)

		if SEND_LINK in payments_ways:
			typegateway = "link-owner"
			replace_txt_pasarela = get_web_dictionary(language).get("T_pago_card_link", "")
			replace_txt_pasarela += " <img src='/static_1/images/booking/tarjetasCredito2.gif?v=1' class='icon_pasarela_pago'>"
			my_gateway = {"typegateway": typegateway, "pago_pasarela_msg": replace_txt_pasarela}
			multiple_gateways.append(my_gateway)

		if has_amazonpay_redsis:
			typegateway = "Amazon_for_redsis"
			replace_txt_pasarela = "Pago mediante " + "<img style='margin-left:5px; width:80px;' src='/static_1/images/booking/amazonpay.png' class='icon_pasarela_pago'>"
			my_gateway = {"typegateway": typegateway, "pago_pasarela_msg": replace_txt_pasarela}
			multiple_gateways.append(my_gateway)

		return multiple_gateways

	def isGatewayOptional(self):

		if session_manager.get(BONOGIFT_USED):
			bono_controller = GiftBonoPromocodeController()
			if not bono_controller.pay_by_gateway_allowed():
				logging.warning("BONOGIFT_USED! dont let the customer pay by gateway")
				return False

		payments_ways = get_config_property_value(USE_PAYMENT_GATEWAY)
		if payments_ways:
			paymentOptions = get_config_property_value(GATEWAY_PAYMENT_AMOUNT)

			#if we had more than one posible configuration for the gateway, is because they are optionals between themselves
			if len(payments_ways.split(";")) > 1:
				return True

			if self.isBizumPayment():
				return True

			if self.isAmazonpayPayment():
				return True

		return False


	def get_different_currencies(self, language):

		currencies_map = directDataProvider.getEntityMap('Currency')
		currencies = [v for v in list(currencies_map.values()) if v.enabled]

		for myCurrency in currencies:
			myCurrency.name = get_web_dictionary(language).get("T_" + myCurrency.shortName, myCurrency.name)

		return currencies


	def buildPersonalDetails(self, language):


		bono_custom = get_web_configuration(BONO_GIFT_CUSTOM)
		multiple_gateways = self.build_multiple_gateways(language)
		disble_gateway_for_bono = get_config_property_value("Disable gateway for bono")
		if get_config_property_value(advance_configs_names.DISABLE_GATEWAY_FOR_BONO):
			multiple_gateways = [x for x in multiple_gateways if not x.get("typegateway") in disble_gateway_for_bono]

		session_manager.set(CREATE_PRODUCT_PAYMENT, PRODUCT_BONO)

		##cambiar por section
		actual_namespace = get_namespace()
		if bono_custom.get("remote_sections"):
			set_namespace(bono_custom.get("remote_sections"))

		sectionMain = get_section_from_section_spanish_name(bono_custom.get("section"), language)
		sectionMainProperties = get_properties_for_entity(sectionMain.get("key"), language)
		section = get_section_from_section_spanish_name(sectionMainProperties.get("bono_custom_data"), language)
		section_properties = get_properties_for_entity(section.get("key"), language)

		if bono_custom.get("remote_sections"):
			set_namespace(actual_namespace)

		widget_hotels_by_destiny = {}
		if bono_custom.get("hotel_selector_section"):

			section_selector = get_section_from_section_spanish_name(bono_custom.get("hotel_selector_section"), language)
			all_section_pics = getPicturesForKey(language, section_selector.get("key"))
			h = html.parser.HTMLParser()

			for my_pic in all_section_pics:
				pic_property = get_properties_for_entity(my_pic.get("key"), language)

				if not pic_property.get("destiny", "") in widget_hotels_by_destiny:
					widget_hotels_by_destiny[pic_property.get("destiny", "")] = []

				if pic_property.get("pictureTitle"):
					pic_property["pictureTitle"] = html.unescape(pic_property["pictureTitle"])

				widget_hotels_by_destiny[pic_property.get("destiny", "")].append(pic_property)

		currency = request.values.get("currency")
		exchange = request.values.get("exchange")
		price = request.values.get("price")
		price_no_change = request.values.get("price_no_change")
		bono_nights = request.values.get("bono_nights")
		session_manager.set(BONO_NIGHTS, bono_nights)
		amount_total = session_manager.get(TOTAL_PRICE)

		if not price:
			session_bono = session_manager.get(BONO_INFO)
			if session_bono:
				price = session_bono.get("price")

		amount_exchange = float(price) / 100.0

		if price_no_change:
			price = float(price_no_change) / 100.0
			amount_total = float(price_no_change) / 100.0
			amount_exchange = float(price_no_change) / 100.0

		if exchange and not price_no_change:
			amount_exchange = float(amount_exchange) / float(exchange)
			amount_total = float(amount_total) / float(exchange)

		session_bono_info = {'price': price, 'amount': amount_total, 'currency': currency, 'exchange': exchange}
		session_manager.set(BONO_INFO, session_bono_info)

		concept_bono = get_web_dictionary(language).get("T_concepto_bono").replace("@@AMOUNT@@", str(amount_total))
		if bono_nights:
			nights_translation = get_web_dictionary(language).get("T_noches", "").lower()
			concept_bono = get_web_dictionary(language).get("T_concepto_bono").replace("@@AMOUNT@@", bono_nights + " " + nights_translation)

		description_concept = section_properties.get("discount_text")
		custom_gift_color = section_properties.get("custom_gift_color")
		custom_gift_title = section_properties.get("custom_gift_title")

		section_pictures = getPicturesForKey(language, sectionMain.get("key"), [], allow_not_enabled=True)
		picture_gift = [x for x in section_pictures if x.get('title') == 'no activar']
		if picture_gift and picture_gift[0].get('servingUrl'):
			custom_gift_color = picture_gift[0].get('servingUrl')

		templateValues = {
			'useCustomSeal': get_config_property_value(CUSTOM_SEAL),
			'email_notifications_checked': get_config_property_value(advance_configs_names.EMAIL_NOTIFICATIONS_CHECKED),
			'use_payment_gateway': session_manager.get(USE_PAYMENT_METHOD),  # useGateway() or request.values.get("forceGateway"),
			'multiple_gateways': multiple_gateways,
			'optional_gateway': self.isGatewayOptional(),
			'forced_gateway': request.values.get("forceGateway"),
			'forced_subtype': request.values.get("payment_sibs"),
			'language': get_language_code(language),
			'language_string': language,
			'hide_city': get_config_property_value(HIDE_CITY_BOOKING3),
			'optional_personalID': self.optionalId(),
			'selective_hide': get_config_property_value(SELECTIVE_BOOKING3_DISABLE),
			'show_mandatory_field': get_config_property_value(SHOW_MANDATORY_FIELD_BOOKING3),
			'internal_url': get_internal_url(),
			'namespace': get_namespace(),
			'price': price,
			'currency': currency,
			'amount_exchange': amount_exchange,
			'amount_total': amount_total,
			'concept_bono': concept_bono,
			'description_concept': description_concept,
			'custom_gift_color': custom_gift_color,
			'custom_gift_title': custom_gift_title,
			'extra_form_text1': section_properties.get("extra_form_text1"),
			'extra_form_text2': section_properties.get("extra_form_text2"),
			'is_agency': request.values.get("is_agency"),
			'bono_nights': request.values.get("bono_nights"),
			'card_design': request.values.get("card_design")
		}

		if widget_hotels_by_destiny:
			templateValues['hotel_selector_list'] = widget_hotels_by_destiny

		captcha_key = get_config_property_value(PUBLIC_CAPTCHA_KEY)
		if captcha_key:
			templateValues['captcha_box'] = captcha_key


		# Check for previous errors
		errorCode = request.values.get("errorCode")
		if errorCode:
			if errorCode == "PAYMENT":
				logging.info("Received errorCode from gateway:%s" % errorCode)
				templateValues['errorPayment'] = True
				templateValues['errorMessagePayment'] = self.getErrorMessage(errorCode, language)
				message = session_manager.get(PAYMENT_FAILED_MESSAGE)
				if message:
					templateValues['errorMessagePayment'] = message

		all_countries = list(ALL_COUNTRIES_LIST.items())
		all_countries.sort(key=lambda x: x[1])
		templateValues['all_countries_list'] = all_countries

		templateValues['credit_card_types'] = get_credit_cards_type(templateValues)

		if session_manager.get(SAVE_CARD_TOKEN_BY_TOKENIZATION):
			bank_name = session_manager.get(SAVE_CARD_TOKEN_BY_TOKENIZATION)
			templateValues['token_text'] = get_web_dictionary(language).get("T_mensaje_token_tpv", "").replace("@@BANCO@@", bank_name)

		if session_manager.get(SERMEPA_SAVE_CC_BY_TOKENIZATION):
			bank_name = session_manager.get(SERMEPA_SAVE_CC_BY_TOKENIZATION)
			templateValues['token_text'] = get_web_dictionary(language).get("T_mensaje_token_tpv", "").replace("@@BANCO@@", bank_name)

		templateValues['has_bizum'] = isBizumPayment()
		templateValues['has_amazonpay_redsis'] = get_config_property_value(advance_configs_names.USE_PAYMENT_GATEWAY_AMAZONPAY_WITH_REDSIS)

		#If using gateway, we want to indicate to the user the amount of money that is going to be charged through the gateway
		selected_transfer_method = request.values.get('payment_method') == 'transfer_bank'
		selected_late_booking =  request.values.get('payment_method') == 'late_booking'
		if templateValues.get("use_payment_gateway") and not selected_transfer_method and not selected_late_booking:
			templateValues['message_before_TPV'] = ""
			templateValues['message_gateway_amount'] = ""

		if request.values.get("forceGateway") == SECURE_PAYMENT:

			totalPrice = session_manager.get(AMOUNT_SENT_TO_GATEWAY)
			amountToSendToGateway = calculatePriceToSendToGateway(totalPrice, "", request)
			session_manager.set(TOTAL_PAYMENT_SECURE, amountToSendToGateway)

			num_days_tpv = session_manager.get(NUMDAYS_SENT_TO_GATEWAY)
			if num_days_tpv:
				templateValues['message_secure_pay'] = get_web_dictionary(language).get("T_discount_phrase_with_num_days_secure_payment", "").replace("@@NUMDAYS@@", str(int(num_days_tpv))).replace("@@AMOUNT@@", str(amountToSendToGateway))
			else:
				templateValues['message_secure_pay'] = get_web_dictionary(language).get("T_Mensaje_Pago_Seguro", "")


		agree_custom_section = get_config_property_value(advance_configs_names.BOOKING3_CUSTOM_AGREE_CHECKBOX)
		target_agree_section = agree_custom_section or "booking3 aviso legal y condiciones de reserva"
		special_agree_msg = get_section_from_section_spanish_name(target_agree_section, language)

		templateValues['special_agree_msg'] = special_agree_msg

		config = get_web_configuration(BONO_GIFT_CUSTOM)
		if config and config.get('optional_payment'):
			if 'direct_payment' not in config.get('optional_payment'):
				templateValues['hide_direct_payment'] = True

		hide_direct_payment = get_config_property_value(advance_configs_names.HIDE_DIRECT_PAYMENT) or bono_custom.get(advance_configs_names.HIDE_DIRECT_PAYMENT)
		if  hide_direct_payment and bono_custom.get("paymethod", "").lower() != "pci":
			templateValues['hide_direct_payment'] = True

		extra_agree_msg = get_section_from_section_spanish_name("_booking3_extra_legal", language)
		templateValues['extra_agree_msg'] = extra_agree_msg

		templateValues['texto_tarjeta'] = get_section_from_section_spanish_name("texto tarjeta booking3", language)

		booking3_amount_info = get_config_property_value(advance_configs_names.BOOKING3_AMOUNT_INFO)
		if booking3_amount_info:
			templateValues['booking3_amount_info'] = get_section_from_section_spanish_name(booking3_amount_info, language)

		extra_required_form_fields = section_properties.get("extra_required_form_fields")
		if extra_required_form_fields:
			templateValues['extra_required_form_fields'] = extra_required_form_fields

		myParams = dict(list(templateValues.items()) + list(get_web_dictionary(language).items()))

		# Disabled credit card if certain conditions are satisfied (it is enough with one of them)
		# Number of Rooms; Date; Explicitly
		# Credit card conditional -> Left rooms(n or False);Today/Tomorrow(True or False);Offer latebooking (True or False)
		if get_config_property_value(CREDIT_CARD_CONDITIONAL):
			credit_card_conditional = CreditCardConditional()
			credit_card_conditional.process_late_booking(myParams, request, language)

		if get_config_property_value(advance_configs_names.DISABLE_CREDIT_CARD):
			myParams['disabled_credit_card'] = True
			myParams['lateBooking_custom_conditions'] = get_section_from_section_spanish_name('_dont_ask_credit_card_text', language)

		if session_manager.get(NO_ASK_CREDIT_CARD) and not session_manager.get(FORCE_GATEWAY_PAYMENT_MODIFICATION):
			myParams['no_ask_credit_card_modification'] = True

		myParams['sid'] = session_manager.get_session_id()

		# Actual Year for credit cards details
		today = datetime.now()
		actualYear = today.strftime("%Y")
		actualMonth = today.strftime("%m")
		myParams['credit_cards_years'] = []
		for i in range(15):
			YearToAppend = int(actualYear)+i
			myParams['credit_cards_years'].append(str(YearToAppend)[-2:])

		if get_config_property_value(CC_OWNER_NAME):
			myParams['ccownername'] = True

		if get_config_property_value(advance_configs_names.TRANSFORM_CREDIT_CARD_FIELD):
			myParams['transform_credit_card_field'] = True

		#and not request.cookies.get('paraty_worker') and request.values.get("forceGateway") != SECURE_PAYMENT and not DEV
		if get_config_property_value(PCI_TOKEN) :
			pci_controller = PCIController()
			myParams['PCI_TOKEN'] = pci_controller.pci_type.lower()
			myParams['DATATRANS_INLINE_MERCH_ID'] = pci_controller.get_token(self.getSelectedUUID())

		credit_card_b3_context(myParams)

		if session_manager.get(GATEWAY_LOGO):
			myParams['gateway_logo'] = session_manager.get(GATEWAY_LOGO)

		if session_manager.get(GATEWAY_LOGOS):
			myParams['gateway_logos'] = session_manager.get(GATEWAY_LOGOS)

		billing_config = get_config_property_value(BILLING_DATA)
		if billing_config:
			myParams['billing_data'] = billing_config
			if 'question' in billing_config:
				myParams['billing_data_question'] = billing_config

		gateway_payment_amount = get_config_property_value(advance_configs_names.GATEWAY_PAYMENT_AMOUNT)
		if gateway_payment_amount and PAY_ALWAYS in gateway_payment_amount:
			myParams['use_always_gateway'] = True

		if request.values.get("force_prepay_amount"):
			myParams['force_prepay_amount'] = True

		if request.values.get('card_design') or session_manager.get(BONO_GIFT_CUSTOM_DESIGN):
			myParams['custom_card_design'] = request.values.get('card_design') or session_manager.get(BONO_GIFT_CUSTOM_DESIGN)
			if request.values.get('card_design'):
				session_manager.set(BONO_GIFT_CUSTOM_DESIGN, request.values.get('card_design'))

		typePaymentGateway = self._get_type_payment()
		myParams['integration_type'] = typePaymentGateway
		amountToSendToGateway = session_manager.get(AMOUNT_SENT_TO_GATEWAY)
		if amountToSendToGateway:
			myParams['amountToSendToGateway'] = float("%.2f" % amountToSendToGateway)
			if exchange:
				myParams['amountToSendToGateway'] = float(amountToSendToGateway) / float(exchange)
				if price_no_change:
					myParams['amountToSendToGateway'] = float(price_no_change) / 100.0

		if not currency:
			myParams['currency'] = get_currency_symbol()
		myParams['reservation_summary'] = ""

		if typePaymentGateway and get_config_property_value(advance_configs_names.USE_ONLY_GATEWAY_FOR_BONOS):
			myParams['hide_direct_payment'] = False
			myParams['use_always_gateway'] = True

		if get_config_property_value(advance_configs_names.TEXT_BEFORE_CHECKS):
			myParams['text_before_checks'] = get_section_from_section_spanish_name(get_config_property_value(advance_configs_names.TEXT_BEFORE_CHECKS), language)

		if get_config_property_value(advance_configs_names.TEXT_BEFORE_CHECKS_MOBILE):
			myParams['text_before_checks_mobile'] = get_section_from_section_spanish_name(get_config_property_value(advance_configs_names.TEXT_BEFORE_CHECKS_MOBILE), language)

		booking3_cards_styles = get_config_property_value(advance_configs_names.BOOKING3_CARDS_STYLES)
		if booking3_cards_styles:
			myParams['booking3_cards_styles'] = booking3_cards_styles
			myParams['is_gift'] = True
			myParams['booking3_cards_styles'] = self.buildTemplate_2("booking_2/booking_process_v1/personal_details/_cards_tpv_v1.html", myParams, False)

		if get_config_property_value(SHOW_GATEWAY_LOGO):
			myParams['gateway_logo'] = get_config_property_value(SHOW_GATEWAY_LOGO)

		if config and config.get('version') == FUERTE:
			myParams['namespace'] = get_namespace()
			if config.get('namespace'):
				myParams['namespace'] = config.get('namespace')
			session_manager.set(AMOUNT_SENT_TO_GATEWAY, price)
			session_manager.save_session_to_datastore()
			content = self.buildTemplate('bonos/_personal_details_bonos_no_tpv.dj.html', myParams, allowMobile=False)
			return content

		content = self.buildTemplate('bonos/_personal_details_bonos.dj.html', myParams)
		return content

	def build_accomodation_taxes(self, accomodation_tax_property, language, myParams):
		increment_total, accomodation_tax_info = get_accomodation_total_increment(language=language)
		myParams['accomodation_tax_label'] = get_web_dictionary(language).get("T_accomodation_tax",
		                                                                                  "Accomodation Tax")
		myParams['accomodation_total_amount'] = increment_total
		myParams['accomodation_tax_info'] = accomodation_tax_info
		if ";" in accomodation_tax_property:
			accomodation_tax_section = get_section_from_section_spanish_name(accomodation_tax_property.split(";")[1],
			                                                            language)
			myParams["accomodation_tax_info_popup"] = accomodation_tax_section


		not_include_taxes = get_config_property_value(NOT_INCLUDE_ACCOMODATION_TAX)
		if not_include_taxes:
			myParams['accomodation_tax_not_included'] = not_include_taxes

			if 'v2' in not_include_taxes:
				myParams['hide_total_booking'] = True
				selectedPrice = []
				for partialSelected in session_manager.get(SELECTED_OPTION_KEY).split(";"):
					selectedPrice.append(session_manager.get(PRICE_OPTION_KEY_PREFIX + partialSelected))

				price = getTotalPrice(selectedPrice)
				myParams['price_without_tax'], myParams['tax_amount'] = separate_tax_and_price(price)
				myParams['total_with_accomodation'] = increment_total + price

	def hide_personal_id_processor(self, hide_personalid_b3_config, selected_price):
		hide_personal_id = True
		rate_key = selected_price[0][0]
		if package_utils.is_package(rate_key):
			package_key, rate_key = package_utils.get_package_and_rate_from_key(rate_key)
		if ';' in hide_personalid_b3_config:
			selected_rate_identifier = directDataProvider.getEntity('Rate', rate_key).localName
			if not selected_rate_identifier in hide_personalid_b3_config:
				hide_personal_id = False

		elif 'is_refundable' in hide_personalid_b3_config:
			rate_is_refundable = not rateIsNonRefundable(rate_key)
			if rate_is_refundable:
				hide_personal_id = True
			else:
				hide_personal_id = False

		return hide_personal_id


	def build_credit_card_info(self, credit_cards_accepted):
		'''Cards separated by ;'''
		available_credit_cards = credit_cards_accepted.split(";")
		cards_info = []
		for credit_card in available_credit_cards:
			if credit_card == 'visa':
				cards_info.append({'name': 'Visa', 'value': 'VI', 'image': '/static_1/images/booking/credit_cards/visa.png'})

			if credit_card == 'mastercard':
				cards_info.append({'name': 'Master Card', 'value': 'CA', 'image': '/static_1/images/booking/credit_cards/mastercard.png'})

			if credit_card == 'dinersclub':
				cards_info.append({'name': 'Diners Club', 'value': 'DC', 'image': '/static_1/images/booking/credit_cards/diners_club.png'})

			if credit_card == 'americanexpress':
				cards_info.append({'name': 'American Express', 'value': 'AX', 'image': '/static_1/images/booking/credit_cards/american_express.png'})

			# if credit_card == '4b':
			# 	cards_info.append({'name': '4B', 'value': '4B', 'image': '/static_1/images/booking/credit_cards/4b.png'})

		add_custom_credit_card_type = get_config_property_value(advance_configs_names.ADD_CREDIT_CARD_TYPES)
		disabled_custom_credit_card_types = get_config_property_value(advance_configs_names.DISABLED_CREDIT_CARD_TYPES)

		if add_custom_credit_card_type:
			add_credit_card_type_splitted = add_custom_credit_card_type.split("@@")
			for credit_card_add in add_credit_card_type_splitted:
				credit_card_add = credit_card_add.split(":")
				if len(credit_card_add) > 1:
					cards_info.append({'name': credit_card_add[0], 'value': credit_card_add[1]})

		if disabled_custom_credit_card_types:
			del_credit_card_list = disabled_custom_credit_card_types.split(";")
			cards_info = [x for x in cards_info if x.get('name') not in del_credit_card_list]

		return cards_info

	def buildGatewayMessage(self, language, optional_discount):
		'''
		If needed it adds to templateValues the message to show to the user, indicating
		the amount of money to be sent to the gateway
		'''


		amountToSendToGateway = session_manager.get(AMOUNT_SENT_TO_GATEWAY)
		totalPrice = session_manager.get(TOTAL_PRICE)

		if request.values.get("forceGateway-with-discount") and optional_discount:

			discount_phrase = get_web_dictionary(language).get("T_discount_applied", "").replace("@@AMOUNT@@", str(amountToSendToGateway))
			discount_phrase = discount_phrase.replace("@@DISCOUNT@@", str(optional_discount))

			return discount_phrase

		reservation = session_manager.get(RESERVATION_MODIFICATION)
		if reservation and reservation.get('extra_info'):

			amount_already_payed = reservation.get('extra_info', {}).get('payed', 0)

			if amount_already_payed:

				payed_phrase = get_web_dictionary(language).get("T_already_payed_phrase", "")
				try:
					payed_phrase = payed_phrase.replace("@@AMOUNT@@", str(amountToSendToGateway)).replace("@@AMOUNT_PAYED@@", str(amount_already_payed)).replace("@@CURRENCY@@", str(get_currency_symbol(), encoding='utf-8'))
				except Exception as e:
					payed_phrase = payed_phrase.replace("@@AMOUNT@@", str(amountToSendToGateway)).replace("@@AMOUNT_PAYED@@", str(amount_already_payed)).replace("@@CURRENCY@@", '')

				return payed_phrase

		#we must inform the user about posibles discounts
		discount_phrase = ""
		if float(amountToSendToGateway) and not session_manager.get(SERMEPA_SAVE_CC_BY_TOKENIZATION) and not session_manager.get(SAVE_CARD_TOKEN_BY_TOKENIZATION):
			#if tokenizator, no rate is necesary here
			discount_phrase = get_web_dictionary(language).get("T_tarifa_norefundable", "")

		if float(totalPrice) > float(amountToSendToGateway):

			logging.info("Found discount in gateway payment: %s VS %s ", totalPrice, amountToSendToGateway)

			num_days_tpv = session_manager.get(NUMDAYS_SENT_TO_GATEWAY)
			if num_days_tpv:
				plural_days = ''
				if num_days_tpv > 1:
					plural_days = 's'

				discount_phrase = get_web_dictionary(language).get("T_discount_phrase_with_num_days", "")

				try:
					currency = discount_phrase.replace("@@AMOUNT@@", str(amountToSendToGateway)).replace("@@CURRENCY@@", get_currency_symbol()).replace("@@NUMDAYS@@", str(int(num_days_tpv))).replace("@@S@@", plural_days)
				except Exception as e:
					currency = discount_phrase.replace("@@AMOUNT@@", str(amountToSendToGateway)).replace("@@CURRENCY@@", '').replace("@@NUMDAYS@@", str(int(num_days_tpv))).replace("@@S@@", plural_days)

				return currency

			else:
				discount_phrase = get_web_dictionary(language).get("T_discount_phrase", "")

		try:
			currency = discount_phrase.replace("@@AMOUNT@@", str(amountToSendToGateway)).replace("@@CURRENCY@@", get_currency_symbol())
		except Exception as e:
			currency = discount_phrase.replace("@@AMOUNT@@", str(amountToSendToGateway)).replace("@@CURRENCY@@", '')

		return currency


	def getTotalPriceAndResetSessionVarsForTPV(self, selectedPrice):
		#we make sure to reset the discount if the user changes his opinion and don't accept the discount (iE earlyPay)
		session_manager.set(TOTAL_PRICE_FORCED, "")
		session_manager.set(I_AM_A_RESIDENT, "")

		set_booking_discount('')

		if request.values.get("iamaresident") and not session_manager.get(BOOKING3_LINK_TPV):
			session_manager.set(I_AM_A_RESIDENT, True)

		#Also, it's posible to return to booking3 from gateway. In this case we offer to the custom the posibility to pay directly io the hotel
		#so we reset the amount
		logging.info("reseting AMOUNT_SENT_TO_GATEWAY to empty!")
		session_manager.set(AMOUNT_SENT_TO_GATEWAY, "")

		userIP = getUserIp()

		totalPrice = getTotalPrice(selectedPrice)
		session_manager.set(TOTAL_PRICE, totalPrice)


		#overwitring total amount with discount (just for show it)

		if request.values.get('forceGateway-with-discount',''):
			session_manager.set(PVP_TOTAL_PRICE, totalPrice)

			discount = getDiscountForGatewayOptionalEarlypay()
			set_booking_discount(discount)
			if discount:
				logging.info(
					"getTotalPriceAndResetSessionVarsForTPV DISCOUNT of %s accepted by user paying the 100 per cent totalPrice before  discount %s",
					discount, totalPrice)
				totalPrice = round((float(totalPrice) - (float(totalPrice) / float(100 / float(discount)))), 2)


			session_manager.set(TOTAL_PRICE_FORCED, totalPrice)
			session_manager.set(TOTAL_PRICE, totalPrice)

		if request.values.get('force_net_payment'):
			session_manager.set(NET_TOTAL_PRICE, totalPrice)

		#Reset the tokenizator always. After check if payment is mandatory, we will check again if CC must be save by Token
		session_manager.set(SERMEPA_SAVE_CC_BY_TOKENIZATION, False)
		session_manager.set(SAVE_CARD_TOKEN_BY_TOKENIZATION, False)

		if session_manager.get(PRICE_FORCED_ON_REQUEST):
			if session_manager.get(PRICE_FORCED_ON_REQUEST) == "token":
				session_manager.set(SAVE_CARD_TOKEN_BY_TOKENIZATION, get_config_property_value(GATEWAY_CREDIT_CARD_BY_TOKEN))

		return totalPrice

	def buildReservationSummary(self, search, selectedPrice, totalPrice, language, country):

		search_html = buildSearch(request, search, language, message=True)

		truncate_decimal = get_config_property_value(TRUNCATE_DECIMAL_BOOKING)
		if truncate_decimal:
			if totalPrice: totalPrice = int(float(totalPrice))
			for currentPrice in selectedPrice:
				currentPrice[3] = int(float(currentPrice[3]))

		if get_config_property_value(ROUND_DECIMAL_BOOKING):
			if totalPrice: totalPrice = int("%.0f" % float(totalPrice))
			for currentPrice in selectedPrice:
				currentPrice[3] = int("%.0f" % float(currentPrice[3]))

		selectedOption = session_manager.get(SELECTED_OPTION_KEY)
		youSave = 0
		totalNoPromos = 0

		available_promotions = []

		for partialSelected in selectedOption.split(";"):
			totalsInfo = session_manager.get(PRICE_OPTION_KEY_PREFIX_V2 + partialSelected)
			available_promotions.append(totalsInfo)

			if totalsInfo and totalsInfo['promotion'] and totalsInfo.get("promotion", {}).get('value'):
				youSave = totalsInfo["promotion"]["value"]

			if totalsInfo and totalsInfo['pricesPerDay']:
				for totalNoPromoPerDay in  totalsInfo["pricesPerDay"]:
					totalNoPromos += totalNoPromoPerDay

		userIP = getUserIp()


		avoid_session_for_policies = True
		if session_manager.get(FORCE_EXTERNAL_CANCEL_POLICIES):
			#external policies from adapters are setted in sessions!!!
			avoid_session_for_policies = False

		#TODO: room, rate, board should be retreived from reservation summary to have all at one point
		template = {'search': search_html,
					'totalPrice': totalPrice,
					'currency': get_currency_symbol(),
					'room': getSelectedRoom(selectedPrice, language),
					'regimen': getSelectedRegimen(selectedPrice[0], language),
					'rateConditions': get_text_rate_conditions(language, selectedPrice[0], avoid_session=avoid_session_for_policies),  #getSelectedRateConditions(selectedPrice[0], language, avoid_session=True),
					'rate': rate_info_calculator.get_rate_name(language, selectedPrice[0], force_query=True),  # getSelectedRate(selectedPrice[0], language, force_query=True),
					'price': getSelectedPrice(selectedPrice),
					'pricePerRoom': getSeparatedPricesPerRoom(selectedPrice),
					'priceSupplements': getPriceSupplements(),
					'price_pay_later_Supplements': getPriceSupplements(pay_later=True),
					'supplements': get_selected_supplements(language),
					'included_supplements': get_selected_supplements(language, included=True),
					'pay_later_supplements': get_selected_supplements(language, pay_later=True),
					'youSave' : youSave,
					'totalNoPromos': totalNoPromos,
					'booking3_vat_included': get_config_property_value(BOOKING3_VAT_INCLUDED)
					}

		rate_key = selectedPrice[0][0]
		if package_utils.is_package(rate_key):
			package_key, rate_key = package_utils.get_package_and_rate_from_key(rate_key)
			package_info = package_utils.get_package_full_info(package_key, language)
			template['package_name'] = package_info.get("package_name", "")

		list_promotion_in_room = get_promotions_name(selectedPrice)
		if list_promotion_in_room:
			template['promotions_names'] = list_promotion_in_room

		if user_agent_is_mobile():
			template['promotions_names'] = available_promotions

		extra_params_search = session_manager.get(EXTRA_PARAMS_SEARCH)
		if extra_params_search and extra_params_search.get('free_night'):
			template['free_night'] = True
			template['free_night_day'] = retreive_free_night_info(extra_params_search.get('free_night'))

		template['rooms_disabled'] = get_config_property_value(NUMBER_OF_ROOMS_DISABLES) or (extra_params_search and extra_params_search.get('free_night'))


		if get_config_property_value(advance_configs_names.VIEW_CANCELL_DAYS):
			template['rates_conditions_days'] = get_cancellation_days_and_dates([selectedPrice[0][0]], search, language)


		if session_manager.get(LOCATION_MODIFICATION):
			template['location_reservation_modificacion'] = session_manager.get(LOCATION_MODIFICATION)
			logging.info("Getting in Booking3 in session location_reservation_modificacion: %s", session_manager.get(LOCATION_MODIFICATION))

		if is_enabled_shopping_cart():
			template['booking_shopping_cart'] = True

		myParams = dict(list(template.items()) + list(get_web_dictionary(language).items()))

		hotel_country_location = get_config_property_value(HOTEL_COUNTRY_LOCATION)
		if hotel_country_location:
			country_info_retreived = CountryConfigsBuilder(hotel_country_location, language)
			myParams['country_rate_tax'] = country_info_retreived.tax_rate
			myParams = dict(list(myParams.items()) + list(country_info_retreived.custom_dict.items()))

		update_different_currencies_price_display_context(myParams)

		myParams['reservation_summary'] = get_booking_params(selectedPrice, language)
		booking3_popup_autofill = get_config_property_value(advance_configs_names.BOOKING3_POPUP_AUTILFILL_INFORMATION)
		if booking3_popup_autofill:
			myParams.update(build_autofill_popup(booking3_popup_autofill, myParams, request, language))

		accomodation_tax_property = get_config_property_value(ACCOMODATION_TAX_INCREMENT_BY_PAX_NIGHT)
		if accomodation_tax_property:
			self.build_accomodation_taxes(accomodation_tax_property, language, myParams)

		show_tax_price = get_config_property_value(advance_configs_names.SHOW_TAX_PRICE)
		if show_tax_price:
			selectedOption = session_manager.get(SELECTED_OPTION_KEY)

			selectedPrice = []
			for partialSelected in selectedOption.split(";"):
				selectedPrice.append(session_manager.get(PRICE_OPTION_KEY_PREFIX + partialSelected))

			price = getTotalPrice(selectedPrice)
			vat_price = "%.2f" % (price * (int(show_tax_price) / 100.0))

			myParams['vat_price'] = vat_price
			myParams['price_without_vat'] = price - float(vat_price)
			myParams['show_tax_price'] = True

		# content = self.buildTemplate('booking/reservationSummary.html', myParams)

		myParams['virtual_upgrading_supplements'] = session_manager.get(VIRTUAL_SERVICES_SELECTED)

		bono_used = session_manager.get(BONOGIFT_USED)
		if bono_used:
			bono_controller = GiftBonoPromocodeController()
			myParams['bono_gift_banner'] = bono_controller.render_banner_booking3(bono_used, language)
			myParams['bono_gift_promocode'] = bono_used
			myParams['original_price_before_discount'] = session_manager.get(ORIGINAL_PRICE_BEFORE_DISCOUNT)

		if get_config_property_value(advance_configs_names.RATE_B3_CUSTOM_MESSAGE):
			myParams['custom_message_rate'] = build_custom_messages_for_rate(language)


		if is_new_booking_process_mobile_version():
			return booking_process_mobile_reservation_summary(myParams)

		content = self.buildTemplate('booking_2/_reservation_summary.dj.html', myParams)
		return content

	# FIXME: Remove this when selectedUUID will be ready... right now arrives empty
	def getSelectedUUID(self):
		selectedUUID = request.values.get("selectedUUID")
		if not selectedUUID or len(selectedUUID) == 0:
			#selectedUUID = '%d' % random.randint(1000,9999999)
			#with DES3 the ORDER id of the transaction must be multiple of 8
			selectedUUID = '%d' % random.randint(10000000,99999999)

		selectedUUID = _add_prefix_to_identifier(selectedUUID)
		return selectedUUID

	def useGateway(self):

		use_payment_gateway = get_config_property_value(USE_PAYMENT_GATEWAY)
		logging.info("useGateway: %s", use_payment_gateway)
		if not use_payment_gateway:
			return False

		# Gateway is not retried if the user couldnt do the payment, lets let him to put cc in paraty form
		if session_manager.get(PAYMENT_GATEWAY_FAILED):
			return False

		if session_manager.get(SAVE_CARD_TOKEN_BY_TOKENIZATION):
			return True

		if session_manager.get(SERMEPA_SAVE_CC_BY_TOKENIZATION):
			return True

		if session_manager.get(FORCED_PAYMENT_TYPE_ERROR):
			return False



	def buildConditionsAndSubmit(self, language):

		bono_custom = get_web_configuration(BONO_GIFT_CUSTOM)

		session_manager.set(CREATE_PRODUCT_PAYMENT, PRODUCT_BONO)

		totalPrice = request.values.get("price")
		if not totalPrice:
			session_bono = session_manager.get(BONO_INFO)
			if session_bono:
				totalPrice = session_bono.get("price")
		is_agency = request.values.get("is_agency")
		totalPrice = get_total_price_for_bonos(totalPrice, is_agency)
		total_price_for_gateway = session_manager.get(PRICE_BONO_EXTRA)

		languageCode = get_language_code(language)

		templateValues = {
						'totalPrice': totalPrice,
						'currency': get_currency_symbol(),
						'email_notifications_checked': get_config_property_value(advance_configs_names.EMAIL_NOTIFICATIONS_CHECKED),
						'language': languageCode,
						'booking3_vat_included': get_config_property_value(BOOKING3_VAT_INCLUDED),
						'optional_personalID': self.optionalId(),
						'internal_url': get_internal_url()
						}

		if request.values.get("forceGateway") == SECURE_PAYMENT:
			session_manager.set(SECURE_PAYMENT_FORCED, True)

		hide_direct_payment = get_config_property_value(advance_configs_names.HIDE_DIRECT_PAYMENT) or bono_custom.get(advance_configs_names.HIDE_DIRECT_PAYMENT)
		if hide_direct_payment and bono_custom.get("paymethod", "").lower() != "pci":
			templateValues['hide_direct_payment'] = True

		if get_config_property_value(BILLING_DATA):
			templateValues['billing_data'] = True
		if user_agent_is_mobile():
			agree_custom_section = get_config_property_value(advance_configs_names.BOOKING3_CUSTOM_AGREE_CHECKBOX)
			target_agree_section = agree_custom_section or "booking3 aviso legal y condiciones de reserva"
			special_agree_msg = get_section_from_section_spanish_name(target_agree_section, language)
			templateValues['special_agree_msg'] = special_agree_msg

		optional_b3_inputs = get_config_property_value(advance_configs_names.OPTIONAL_INPUTS_BOOKING3)
		if optional_b3_inputs:
			templateValues['optional_booking3_inputs'] = optional_b3_inputs.split(";")
 
		# Check for previous errors
		errorCode = request.values.get("errorCode")
		if errorCode:
			templateValues['error'] = True
			templateValues['errorMessage'] = self.getErrorMessage(errorCode, language)
			logging.info("Received errorCode from gateway:%s" % errorCode)

		#if we're forcing again a GateWay, and we're comming from a fail, we reset the error flag.
		if request.values.get("forceGateway"):
			session_manager.set(PAYMENT_GATEWAY_FAILED, False)

		selectedUUID = self.getSelectedUUID()

		enable_tokenizer = False
		# if selected rate is special then we can enable tokenizer
		gateway_credit_card_by_token = get_config_property_value(GATEWAY_CREDIT_CARD_BY_TOKEN)
		if gateway_credit_card_by_token:
			enable_tokenizer = True

		template = 'bonos/_conditions_and_confirmation_bonos.dj.html'

		typePaymentGateway = self._get_type_payment()
		if typePaymentGateway:
			#HOTEL WITH TPV CONFIGURED!!!
			if typePaymentGateway == SEQURA:
				search = session_manager.get(SEARCH_KEY_PREFIX)
				startDate = datetime.strptime("%s %s" % (search['startDate'], "12:00:00"), "%Y-%m-%d %H:%M:%S")
				currentDate = datetime.today()
				if (startDate - currentDate).days < 2:
					use_payment_gateway = False

			#refactor this!
			if self._get_type_payment() == PLACETOPAY:
				templateValues['placetopay'] = True
			if self._get_type_payment() == REDUNICRE:
				templateValues['redunicre'] = True
			try:

				extra_data = {"language": language,
							  "country": session_manager.get('country') or "ES"}

				if self._get_type_payment() == SIBS:
					search = session_manager.get(SEARCH_KEY_PREFIX)
					startDate = datetime.today()
					extra_data.update({'startDate': startDate})


				payment_method_dict = self._payment_method(totalPrice, total_price_for_gateway, selectedUUID, extra_data=extra_data)
				if not session_manager.get(SAVE_CARD_TOKEN_BY_TOKENIZATION) and not session_manager.get(SERMEPA_SAVE_CC_BY_TOKENIZATION) and not session_manager.get(AMOUNT_SENT_TO_GATEWAY):
					use_payment_gateway = False

				templateValues = dict(list(templateValues.items()) + list(payment_method_dict['params'].items()))
				template = payment_method_dict['template']
			except Exception as e:
				message = auditUtils.makeTraceback()
				logging.exception(message)
				session_manager.set(FORCED_PAYMENT_TYPE_ERROR, True)
				notify_exception("[Backend] Exception trying to use payment gateway at booking3", message, add_hotel_info=True)

			session_manager.set(USE_PAYMENT_METHOD, True)

		else:
			#I'm a hotel without GATEWAY!!

			#dirty trick: we use AMOUNT_SENT_TO_GATEWAY even it's not a TPV
			session_manager.set(AMOUNT_SENT_TO_GATEWAY, total_price_for_gateway)
			session_manager.set(TOTAL_PRICE, totalPrice)

			if get_config_property_value(PCI_TOKEN) :
				pci_controller = PCIController()
				templateValues['PCI_TOKEN'] = pci_controller.pci_type.lower()
				templateValues['DATATRANS_INLINE_MERCH_ID'] = pci_controller.get_token(self.getSelectedUUID())

		allow_mobile = True
		if bono_custom.get("version") == "fuerte":
			template = 'bonos/_conditions_and_confirmation_bonos_no_tpv.dj.html'
			allow_mobile = False

		myParams = dict(list(templateValues.items()) + list(get_web_dictionary(language).items()))

		myParams['sid'] = session_manager.get_session_id()

		content = self.buildTemplate(template, myParams, allowMobile=allow_mobile)
		return content

	def _special_tokenizator_for_selected_rate(self):
		special_tokenizer_config = get_config_property_value(TOKEN_ONLY_SPECIAL_RATE)

		if special_tokenizer_config:
			selected_rate_key = getSelectedRateKey()

			if package_utils.is_package(selected_rate_key):
				package_key, selected_rate_key = package_utils.get_package_and_rate_from_key(selected_rate_key)

			current_rate =  buildResultStructures(SPANISH).get(selected_rate_key)

			if current_rate and len(current_rate) > 5:
				local_rate_name = current_rate[5]
				logging.info("local_rate_name: %s", local_rate_name)
				for special_gateway in special_tokenizer_config.split(";"):
					info_special_gateway = special_gateway.split("@@")[0]
					logging.info("info_special_gateway: %s", info_special_gateway)
					if str(local_rate_name) == str(info_special_gateway):
						logging.info("forzing config tokenizer for a special rate %s", local_rate_name)
						return True

		return None

	def _get_type_payment(self):

		bono_custom = get_web_configuration(BONO_GIFT_CUSTOM)
		if bono_custom.get("paymethod", "").lower() == "pci":
			logging.info("Forcing PCI. Not ask for a gateway configuration")
			return ""

		if request.values.get('paymentMethod') == 'direct':
			logging.info("Forcing direct payment with PCI. Not ask for a gateway configuration")
			return ""

		typePaymentGateway = get_config_property_value(USE_PAYMENT_GATEWAY)

		if bono_custom.get("use_gateway"):
			logging.info(f"forcing gateway for bonos: {bono_custom['use_gateway']}")
			typePaymentGateway = bono_custom["use_gateway"]

		forcedGateway = request.values.get("forceGateway","")

		if forcedGateway and not forcedGateway == "Bizum" and not forcedGateway == "Amazon_for_redsis":
			typePaymentGateway = getForcedPaymentType(typePaymentGateway, forcedGateway)

		if "booking3_bonos" not in request.path:
			if forcedGateway and forcedGateway == "Bizum":
				typePaymentGateway = getForcedPaymentType(typePaymentGateway, SERMEPA)

		session_manager.set(TPV_TYPE_PAYMENT_SELECTED, typePaymentGateway)

		logging.info("typePaymentGateway used: %s", typePaymentGateway)

		return typePaymentGateway

	def _payment_method(self, totalPrice, total_price_for_gateway, selectedUUID, extra_data={}, totalPriceForced=None):
		'''We need to modify sessions, context and template'''
		logging.info("Sending user to Gateway")
		templateValues = {}

		typePaymentGateway = self._get_type_payment()

		payment_gateway = PaymentGateway(typePaymentGateway)

		session_manager.set(AMOUNT_SENT_TO_GATEWAY, total_price_for_gateway)
		session_manager.set(TOTAL_PRICE, totalPrice)

		data = {'price': float(total_price_for_gateway)}

		if typePaymentGateway == PAYU:

			hotel_country_location = get_config_property_value(HOTEL_COUNTRY_LOCATION)
			if hotel_country_location:
				country_info = CountryConfigsBuilder(hotel_country_location)
				data['tax'] = float(country_info.tax_rate.replace('%', ''))
				tax_not_included = country_info.custom_dict.get('general_dict', {}).get('tax_not_included', '')

				if tax_not_included:
					data['price_before_tax'] = float(data['price'])
					data['price_after_tax'] = float(data['price']) * ((100.0 + float(data['tax'])) / 100.0)
				else:
					data['price_before_tax'] = float(data['price']) * (100.0 / (100.0 + float(data['tax'])))
					data['price_after_tax'] = float(data['price'])

			data['price_tax'] = "%.2f" % (float(data['price_after_tax']) - float(data['price_before_tax']))
			data['description'] = "Booking"

		# if DEV:
		# 	set_namespace(None)

		data.update(extra_data)

		data['integration_type'] = typePaymentGateway

		currency = session_manager.get(CURRENCY) or get_currency(only_code=True)
		data['currency'] = currency

		# When is a PlaceToPay type, i need add more data.
		if payment_gateway.is_type_gateway(typePaymentGateway):
			data['user_ip'] = request.remote_addr
			data['user_agent'] = request.headers['User-Agent']

		if SIBS in typePaymentGateway:
			data['payment_sibs'] = request.values.get("payment_sibs")

		session_manager.set(BIZUM_FORCED, False)
		session_manager.set(AMAZON_REDSIS_FORCED, False)
		if SERMEPA in typePaymentGateway:
			if request.values.get("forceGateway", '') == "Bizum":
				data['bizum'] = "true"
				session_manager.set(BIZUM_FORCED, True)
			if request.values.get("forceGateway", '') == "Amazon_for_redsis":
				data['amazonpay'] = "true"
				session_manager.set(AMAZON_REDSIS_FORCED, True)

		data['sid'] = get_session_id()

		logging.info("Extra data: %s", data)
		payment_form = payment_gateway.generate_payment_form(data, selectedUUID)

		templateValues['use_payment_gateway'] = True
		templateValues['payment_gateway_id'] = selectedUUID
		templateValues['payment_form'] = payment_form
		templateValues['integration_type'] = typePaymentGateway
		templateValues['language'] = extra_data.get("language", "")


		templateValues['BYPASS_CTE'] = payment_form


		template = 'bonos/_conditions_and_confirmation_with_gateway_bonos.dj.html'


		return {'template': template, 'params': templateValues}


	def buildPersonalDataEntry(self, language, country):

		totalPrice = request.values.get("price")
		if not totalPrice:
			session_bono  = session_manager.get(BONO_INFO)
			if session_bono:
				totalPrice = session_bono.get("price")
		is_agency = request.values.get("is_agency")
		totalPrice = get_total_price_for_bonos(totalPrice, is_agency)

		#Be careful: buildConditionsAndSubmit ALWAYS first than buildPersonalDetails because of session vars
		templateValues = {
						'conditions_and_submit': self.buildConditionsAndSubmit(language),
						'personal_details': self.buildPersonalDetails(language),
						'step': 2
						}

		# if not get_booking_process_version():
		# 	templateValues['reservation_summary'] = self.buildReservationSummary(language, country)

		return templateValues

	def isRequestValid(self):

		price = request.values.get("price")

		session_bono = session_manager.get(BONO_INFO)
		if session_bono:
			price = session_bono.get("price")

		try:
			if float(price) > 0:
				return True
		except (ValueError, TypeError):
			return False

		return False

	#TODO: try to move this to upgradingUtils.py
	def _get_all_upgrading_by_index_room(self, index_room, virtual_supplements_ids):

		all_virtual_upgrading_services = virtual_supplements_ids.split(";")

		filtered_room_upgrading = []


		logging.info("Looping throw all virtual_supplements_ids: %s", virtual_supplements_ids)

		for virtual_sup_id in all_virtual_upgrading_services:

			logging.info("virtual_sup_id: %s", virtual_sup_id)

			if virtual_sup_id and request.values.get("upgrading_selected_" + virtual_sup_id):

				logging.info("from request GET  upgrading_selected_: %s value found", virtual_sup_id)

				info_virtual_sups = virtual_sup_id.split(UPGRADE_SEPARATOR)
				i_room = info_virtual_sups[0]

				if str(index_room) == str(i_room):
					logging.info("room count found!")
					filtered_room_upgrading.append(virtual_sup_id)


		return filtered_room_upgrading

	def getSelectedServices(self):

		supplements = get_all_supplements_map()

		result = {}

		for key in supplements:
			if request.values.get("amount_" + key):
				result["amount_" + key] = int(request.values.get("amount_" + key))
			if request.values.get("days_" + key):
				result["days_" + key] = int(request.values.get("days_" + key))
			if request.values.get("pay_later_" + key):
				result['pay_later_' + key] = True
			if request.values.get("extra_info_" + key):
				result["extra_info_" + key] = request.values.get("extra_info_" + key)


		if len(result) == 0:
			return None
		else:
			return result


	#TODO: try to move this to upgradingUtils.py
	def get_selected_virtual_upgrading_services(self, language):
		virtual_supplements_ids = request.values.get("virtual_supplements_ids", "")

		result = []
		for virtual_sup_id in virtual_supplements_ids.split(";"):

			if virtual_sup_id and request.values.get("upgrading_selected_" + virtual_sup_id):

				info_virtual_sups = virtual_sup_id.split(UPGRADE_SEPARATOR)
				index_room = info_virtual_sups[0]
				room_key = info_virtual_sups[1]
				rate_key = info_virtual_sups[2]
				board_key = info_virtual_sups[3]

				virtual_key = UPGRADE_SEPARATOR.join(map(str, (index_room, room_key, rate_key, board_key)))
				text_service = session_manager.get(UPGRADING_TEXT_ROOM + virtual_key)

				result.append({"text": text_service, "virtual_key": virtual_key})


		if len(result) == 0:
			return None
		else:
			return result

	def getSelectedIncludedServices(self):

		supplements = get_all_supplements_map()

		result = {}

		for key in supplements:
			if request.values.get("amount_included_item_" + key):
				result["amount_included_item_" + key] = int(request.values.get("amount_included_item_" + key))
			if request.values.get("days_" + key):
				result["days_" + key] = int(request.values.get("days_" + key))


		if len(result) == 0:
			return None
		else:
			return result


	def build_booking3_bonos(self):

		content = self.buildTemplate("bonos/_booking_3_bonos.html", {})
		return content

	def buildMainContent(self, language, country):

		if not request.values.get("recovery"):
			session_manager.initSession(createNew=True, save_to_datastore_automatically=False)

		url_for_redirection = need_to_wait_for_content()
		if url_for_redirection:
			response_utils.set_response_redirection(url_for_redirection)
			return "redirect---%s" % url_for_redirection

		response_utils.get_response_headers()["Pragma"] = "no-cache"
		response_utils.get_response_headers()["Cache-Control"] = "no-cache, no-store, must-revalidate, pre-check=0, post-check=0"

		if self.isRequestValid():

			session_manager.set('language', language)

			if self._get_type_payment() == SIBS:
				session_manager.set(PAYMENT_GATEWAY_FAILED, False)

			errorCode = request.values.get("errorCode")
			if errorCode:
				logging.info("Received errorCode from gateway:%s" % errorCode)
				if errorCode != "CC_DENIED_BUT_REQUIRED" and errorCode != "PAYMENT":
					session_manager.set(PAYMENT_GATEWAY_FAILED, True)

			templateValues = self.buildPersonalDataEntry(language, country)
		else:
			if request.values.get("sid") and get_config_property_value(NO_SESSION_EXPIRE):
				args_template = {"search_params": news_search_session_expire_data()}
				return ErrorControl().controlError(language, country, SESSION_EXPIRED_RETRY, args_template)

			#Go back to home page
			logging.info("Session expired at booking3_bonos")
			context = {
				'price': session_manager.get(PRICE_BONO_TPV)
			}

			return build_error_page_template(language, extra_context=context, template="bonos/session_expired.html")

		templateValues.update(paramsWebSupport(language))

		myParams = dict(list(templateValues.items()) + list(get_web_dictionary(language).items()))

		myParams['additional_services_enable'] = get_available_additional_services()

		booking3_popup_config = get_config_property_value(advance_configs_names.BOOKING3_COOKIE_POPUP)
		if booking3_popup_config:
			myParams['booking_cookie_popup'] = build_booking_cookie_popup(language, booking3_popup_config)

		myParams['booking_bonos'] = self.build_booking3_bonos()

		myParams = dict(list(myParams.items()) + list(self.get_params_by_booking_version(language).items()))

		format_date_booking = get_config_property_value(advance_configs_names.FORMAT_DATE_BOOKING_PROCESS)
		if format_date_booking:
			format_date_booking = translate_dateformat_to_datepickerformat(format_date_booking)
			myParams['format_date_booking'] = format_date_booking

		allow_mobile = True
		bono_custom = get_web_configuration(BONO_GIFT_CUSTOM)
		if bono_custom.get("version") == "fuerte":
			allow_mobile = False

		content = self.buildTemplate('bonos/_wizard_bonos.dj.html', myParams, allowMobile=allow_mobile)

		return content

	def post(self):

		session_manager.set_save_automatically(False)

		self.get()

		session_manager.save_session_to_datastore()

		if DEV:
			useCustomDomain = ''
		else:
			useCustomDomain = get_config_property_value(CUSTOM_DOMAIN)

		if session_manager.get(CUSTOM_SESSION_DOMAIN):
			useCustomDomain = session_manager.get(CUSTOM_SESSION_DOMAIN)

		target_path = 'booking3'
		custom_path = get_config_property_value(advance_configs_names.CUSTOM_BOOKING_PATHS)
		if custom_path:
			language_request = request.values.get('language')
			target_path = custom_redirection_path(custom_path, '3', language_request)

		if useCustomDomain:
			urlForRedirection  = str('%s/%s' % (useCustomDomain, target_path))
		else:
			urlForRedirection  = str('/%s' % target_path)

		if not '?' in urlForRedirection:
			urlForRedirection += "?sid=%s" % session_manager.get_session_id()
		else:
			urlForRedirection += "&sid=%s" % session_manager.get_session_id()

		urlForRedirection = check_explicit_namespace_at_request(request, urlForRedirection)

		# Avoid recalculating the HTML
		try:
			set_booking_html(get_response_content(), 'booking3')
		except Exception as e:
			logging.warning("Error saving html to memcache for speed in POST - GET pattern")

		response_utils.set_response_redirection(str(urlForRedirection))

	def buildExtraJS(self, language):
		result = ""

		booking3_extra_code_config = get_config_property_value(advance_configs_names.BOOKING3_EXTRA_CODE)
		if booking3_extra_code_config:
			booking3_extra_code = get_section_from_section_spanish_name(booking3_extra_code_config, language)
			result += booking3_extra_code.get("content", "")

		return result