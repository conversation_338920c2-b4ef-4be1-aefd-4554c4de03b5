from unittest.mock import patch
from booking_process.utils.request.cookies_utils import get_cookie_domain
from tests.test_base import TestBase


class TestCookiesUtils(TestBase):
    @patch('booking_process.utils.request.cookies_utils.get_web_configuration')
    @patch('booking_process.utils.request.cookies_utils.get_language_code')
    def test_get_cookie_domain(self, mock_get_language_code, mock_get_web_configuration):

        mock_get_language_code.return_value = 'es'

        with self.subTest('No custom cookies domain configuration'):
            mock_get_web_configuration.return_value = None

            self.assertEqual(get_cookie_domain('any_cookie', 'es'), '')

        with self.subTest('Cookie not in configuration, no default'):
            mock_get_web_configuration.return_value = {
                'some_cookie': 'domain.com'
            }

            self.assertEqual(get_cookie_domain('unknown_cookie', 'es'), '')

        with self.subTest('Cookie not in configuration, with default'):
            mock_get_web_configuration.return_value = {
                'default': 'default-domain.com'
            }

            self.assertEqual(get_cookie_domain('unknown_cookie', 'es'), 'default-domain.com')

        with self.subTest('Simple domain format'):
            mock_get_web_configuration.return_value = {
                'test_cookie': 'simple-domain.com'
            }

            self.assertEqual(get_cookie_domain('test_cookie', 'es'), 'simple-domain.com')

        with self.subTest('Language-specific domain with matching language'):
            mock_get_web_configuration.return_value = {
                'test_cookie': 'es:spanish-domain.com;en:english-domain.com;fr:french-domain.com'
            }

            self.assertEqual(get_cookie_domain('test_cookie', 'es'), 'spanish-domain.com')

        with self.subTest('Language-specific domain with non-matching language'):
            mock_get_web_configuration.return_value = {
                'test_cookie': 'en:english-domain.com;fr:french-domain.com'
            }

            self.assertEqual(get_cookie_domain('test_cookie', 'es'), '')
