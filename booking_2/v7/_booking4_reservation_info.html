<div class="reservation_info_container">
    <div class="header">
        <table>
            <tr class="first_column_header">
                <td>
                    {{ T_estimado }} {{ name }} <br />
                    {{ T_gracias_por_reservar_2 }} {{ hotel_name }}. {{ T_mostrar_confirmacion }}.
                </td>
            </tr>
            <tr class="last_column_header">
                <td>
                    <br />
                    {{ T_localizador }}: {{ localizador }}
                    <br />
                    {% if extraInfo and extraInfo.hotelverse_reservation %}
                        <span style="top: 10px;position: relative;font-size: 0.7em;">
                            {{ T_room_hotelverse }}: {{ extraInfo.hotelverse_reservation.room_number }}
                        </span>
                    {% endif %}
                </td>
            </tr>
        </table>
    </div>
    <div class="user_info">
        <table>
            <tr class="first_row">
                <td class="left_column">{{ T_nombre|upper }}</td>
                <td class="middle_column">{{ name }} {{ lastName }}</td>
            </tr>
            <tr class="second_row">
                <td class="left_column">{{ T_email|upper }}</td>
                <td class="middle_column">{{ email }}</td>
            </tr>
            <tr class="last_row">
                <td class="left_column">{{ T_mobile|upper }}</td>
                <td class="middle_column">{{ telephone }}</td>
            </tr>
        </table>
    </div>
    <div class="reservation_info">
        <table>
            <tr class="first_row">
                <td class="left_column">{{ T_numero_noches|upper }}</td>
                <td class="middle_column">{{ numDays }}</td>
            </tr>
            <tr class="second_row">
                <td class="left_column">{{ T_entrada|upper }}</td>
                <td class="middle_column">{% if formated_startdate %}{{ formated_startdate|safe }}{% else %}{{ localizedStartDate }}{% endif %}</td>
            </tr>
            <tr class="third_row">
                <td class="left_column">{{ T_salida|upper }}</td>
                <td class="middle_column">{% if formated_enddate %}{{ formated_enddate|safe }}{% else %}{{ localizedEndDate }}{% endif %}</td>
            </tr>
            <tr class="last_row">
                {% if kidsAges and showKidsAges %}
                    <td class="left_column">{{ T_edades|upper }} {{ T_ninos|upper }}</td>
                    <td class="middle_column">
                        {% for room in kidsAges %}
                            {% if room is defined and room|length > 0 %}
                                {{ T_habitacion }} {{ loop.index }}:
                                {% for age in room %}
                                    {{ age }} {% if not loop.last %}-{% endif %}
                                {% endfor %}
                            {% endif %}
                        {% endfor %}
                    </td>
                {% endif %}
            </tr>
        </table>
    </div>
    {% include "booking_2/v7/_booking4_rooms_info.html" %}
</div>