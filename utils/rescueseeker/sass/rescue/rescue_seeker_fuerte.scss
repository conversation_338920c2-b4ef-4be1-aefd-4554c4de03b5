@import "plugins/mixins";

.overlay_rescue {
  @include full_size;
  position: fixed;
  background-color: rgba(black, .4);
}

.rescue_seeker_wrapper {
  width: 95%;
  max-width: 650px;
  box-shadow: 0 0 5px 0 rgba(black, .2);
  @include center_xy;
  position: fixed;
  z-index: 100;

  * {
    box-sizing: border-box;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    font-family: sans-serif;
    line-height: 1;
  }

  .fa {
    font-family: "FontAwesome";
  }

  .rescue_header {
    position: relative;
    background-color: #57A903;

    .logo_hotel {
      display: inline-block;
      height: 35px;
      @include center_y;
      left: 10px;

      img {
        vertical-align: middle;
        height: 35px;
      }
    }

    .rescue_title {
      display: inline-block;
      width: 100%;
      padding: 15px 42px 15px 52px;
      color: white;
      font-size: 24px;

    }

    .close_rescue {
      @include center_y;
      right: 20px;
      color: white;
      font-size: 24px;
      cursor: pointer;
    }
  }

  .rescue_body {
    padding: 30px;
    background-color: #fff;

    .rescue_form {
      .rescue_description {
        font-size: 14px;
        color: #5c5c5c;
        text-align: center;
        line-height: 18px;

        .show_hidden_rescue {
          display: inline-block;
          margin-left: 8px;
          cursor: pointer;
          position: relative;
          z-index: 10;
        }

        .hidden_rescue {
          position: absolute;
          display: none;
          width: 300px;
          background-color: rgba(black, .8);
          z-index: 10;
          padding: 10px 20px;
          left: calc(100% + 8px);
          top: 0;
        }
      }

      .rescue_inputs {
        margin-top: 15px;
        position: relative;

        input {
          margin-top: 5px;
          width: 100%;
          height: 50px;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          border: 1px solid rgba(gray, .4);
          border-radius: 3px;
          text-align: center;
          font-size: 12px;

          &:first-child {
            margin-top: 0;
          }
        }

        label.error_input {
          position: absolute;
          bottom: 100%;
          left: 0;
          border: 1px solid red;
          color: red;
          background-color: #f5c8c8;
          width: auto;
          padding: 3px 10px;
        }
      }

      .rescue_checks {
        margin-top: 15px;
        display: inline-block;
        width: 100%;

        .check_element {
          position: relative;

          input {
            display: inline-block;
            vertical-align: top;
            float: left;
          }

          label {
            display: inline-block;
            vertical-align: top;
            width: calc(100% - 23px);
            margin-top: 3px;
            float: left;

            &.error {
              position: absolute;
              bottom: 100%;
              left: 0;
              border: 1px solid red;
              color: red;
              background-color: #f5c8c8;
              width: auto;
              padding: 3px 10px;
            }
          }
        }
      }

      .rescue_buttons {
        margin-top: 15px;
        display: table;
        width: 100%;

        .button_element {
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          background-color: #57A903;
          color: white;
          text-transform: uppercase;
          font-size: 16px;
          border: 0;
          border-radius: 0;
          border-bottom: 5px solid darken(#57A903, 10%);
          height: 37px;
          width: calc(50% - 5px);
          margin-right: 10px;
          display: inline-block;
          vertical-align: top;
          cursor: pointer;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }
}

@media (max-width: 610px) {
  .rescue_seeker_wrapper {
    .rescue_header {
      .logo_hotel {
        img {

        }
      }

      .rescue_title {

      }

      .close_rescue {

      }
    }

    .rescue_body {
      .rescue_form {
        .rescue_description {

        }

        .rescue_inputs {
          input {

          }
        }

        .rescue_checks {
          .check_element {
            input {

            }

            label {
              font-size: 12px;
              color: #767573;

              a {
                font-size: 12px;
                color: #767573;
              }
            }
          }
        }

        .rescue_buttons {
          .button_element {
            width: 100%;
            margin-right: 0;
            margin-top: 10px;

            &:first-child {
              margin-top: 0;
            }
          }
        }
      }
    }
  }
}