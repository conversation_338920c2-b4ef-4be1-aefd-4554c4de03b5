@import "plugins/mixins";

@font-face {
  font-family: "San Francisco";
  font-style: normal;
  font-weight: 100;
  src: url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-ultralight-webfont.eot?#iefix) format("embedded-opentype"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-ultralight-webfont.woff2) format("woff2"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-ultralight-webfont.woff) format("woff"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-ultralight-webfont.ttf) format("truetype"), url("fonts/sanfrancisco/sanfranciscodisplay-ultralight-webfont.svg#San Francisco Display Ultralight") format("svg")
}
@font-face {
  font-family: "San Francisco";
  font-style: normal;
  font-weight: 300;
  src: url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-thin-webfont.eot?#iefix) format("embedded-opentype"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-thin-webfont.woff2) format("woff2"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-thin-webfont.woff) format("woff"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-thin-webfont.ttf) format("truetype"), url("fonts/sanfrancisco/sanfranciscodisplay-thin-webfont.svg#San Francisco Display Thin") format("svg")
}
@font-face {
  font-family: "San Francisco";
  font-style: normal;
  font-weight: 400;
  src: url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-medium-webfont.eot?#iefix) format("embedded-opentype"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-medium-webfont.woff2) format("woff2"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-medium-webfont.woff) format("woff"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-medium-webfont.ttf) format("truetype"), url("fonts/sanfrancisco/sanfranciscodisplay-medium-webfont.svg#San Francisco Display Medium") format("svg")
}
@font-face {
  font-family: "San Francisco";
  font-style: normal;
  font-weight: 600;
  src: url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-semibold-webfont.eot?#iefix) format("embedded-opentype"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-semibold-webfont.woff2) format("woff2"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-semibold-webfont.woff) format("woff"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-semibold-webfont.ttf) format("truetype"), url("fonts/sanfrancisco/sanfranciscodisplay-semibold-webfont.svg#San Francisco Display Semibold") format("svg")
}
@font-face {
  font-family: "San Francisco";
  font-style: normal;
  font-weight: 700;
  src: url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-bold-webfont.eot?#iefix) format("embedded-opentype"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-bold-webfont.woff2) format("woff2"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-bold-webfont.woff) format("woff"), url(https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-bold-webfont.ttf) format("truetype"), url("fonts/sanfrancisco/sanfranciscodisplay-bold-webfont.svg#San Francisco Display Bold") format("svg")
}

.rescue_seeker_wrapper {
  position: fixed;
  top:0;bottom:0;right:0;left:0;
  background-color: rgba(0,0,0,.6);
  z-index: 2000;
  display: none;
  background-attachment: fixed;
  background-size: cover;
  background-position: center;

  .overlay, .rescue {
    @include full_size;
  }
  .overlay {
    opacity: .8;
  }
  .rescue {
    .content {
      @include center_xy;
      width: 500px;
      font-family: "San Francisco", sans-serif;
      font-weight: 400;
      color: white;
      text-align: center;
      .close_rescue {
        position: relative;
        display: top;
        float: right;
        vertical-align: middle;
        width: 23px;
        height: 23px;
        cursor: pointer;
        @extend .icon-xcross;
      }
      .logo_rescue, .emoji_rescue {
        display: inline-block;
        vertical-align: middle;
        margin: 20px 0;
        img {
          vertical-align: middle;
        }
      }
      h1.title {
        display: inline-block;
        vertical-align: middle;
        font-size: 50px;
        line-height: 45px;
        font-weight: 700;
        color: white;
        padding: 0 20px;
        width: calc(100% - 250px);
      }
      .desc {
        font-size: 16px;
        font-weight: 300;
        line-height: 22px;
        big {
          line-height: 80px;
        }
      }
      .question {
        span.title {
          font-size: 25px;
          line-height: 32px;
          display: inline-block;
          padding: 15px 80px;
        }
        .options a {
          display: inline-block;
          vertical-align: middle;
          width: 180px;
          margin: 0 10px;
          padding: 15px 45px;
          border-radius: 50px;
          background: rgba(white, .8);
          font-size:14px;
          font-weight: 300;
          color:#0A5294;
          line-height: 14px;
          text-align: center;
          box-sizing: border-box;
          &:hover {
            background: #0A5294;
            color: white;
          }
          &.apply_discount {
            text-align: left;
            padding: 15px 20px;
            background: rgba(#FAC55C, .9);
            strong {
              float: left;
              font-size: 30px;
              font-weight: 400;
              display: inline-block;
              padding: 7px 10px 5px 5px;
            }
            &:hover {
              background: #0A5294;
              color: white;
              strong {
                color: #FAC55C;
              }
            }
            &:lang(en) {
                text-align: center;
            }
          }
        }
        .rescue_form {
          display: none;
          .input_email_rescue {
            position: relative;
            display: inline-block;
            vertical-align: middle;
          }
          input[type=text], .button_element {
            display: inline-block;
            vertical-align: middle;
            padding: 15px 25px;
            border-radius: 50px 0 0 50px;
            background: rgba(white, .8);
            font-size:12px;
            font-weight: 300;
            color:#0A5294;
            line-height: 14px;
            border-width: 0;
          }
          input[type=text] {
            width: 180px;
            &.error {
              border: 1px solid red;
            }
          }
          .button_element {
            background: rgba(#FAC55C, .9);
            border-radius: 0 50px 50px 0;
            font-weight: 700;
            font-size: 16px;
            letter-spacing: 1px;
            text-transform: uppercase;
            cursor: pointer;
            &:hover {
              background: #0A5294;
              color: white;
            }
          }
          label.error {
            @include center_x;
            top: 100%;
            white-space: nowrap;
          }
          .close_rescue_form {
            position: relative;
            display: inline-block;
            cursor: pointer;
            vertical-align: middle;
            width: 15px;
            height: 15px;
            margin-left: 10px;
            @extend .icon-xcross;
          }
          .check_element {
            position: relative;
            width: 350px;
            margin: auto;
            padding: 20px 0 0;
            input[type="checkbox"] {
              display: inline-block;
              float: left;
              margin: 5px;
            }
            label {
              text-align: left;
              font-weight: 300;
            }
          }
        }
      }
    }
  }
}