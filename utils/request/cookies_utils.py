import base64
import json

from booking_process.constants.web_configs_names import CUSTOM_COOKIES_DOMAIN
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.language.language_utils import get_language_code


def encode_b64_cookie(data, convert_json=False):
    if convert_json:
        data = json.dumps(data)

    if not isinstance(data, bytes):
        data = data.encode('utf-8')

    return base64.b64encode(data).replace(b"=", b"_eq_").replace(b"+", b"_pl_").replace(b"/", b"_sl_")


def decode_b64_cookie(cookie_value, convert_json=False):
    data = base64.b64decode(cookie_value.replace("_eq_", "=").replace("_pl_", "+").replace("_sl_", "/"))

    if convert_json:
        data = json.loads(data)

    return data


def get_cookie_domain(cookie: str, language: str) -> str:
    """
    Gets the appropriate cookie domain based on cookie type and language.

    :param cookie: The cookie type that should match a key in custom_cookies_domain
    :param language: The language code to use for language-specific domains
    :return: The appropriate domain for the cookie, or None if no matching domain is found
    :unit_test: unit_tests.booking_process.utils.request.test_cookies_utils.TestCookiesUtils.test_get_cookie_domain
    """
    custom_cookies_domain = get_web_configuration(CUSTOM_COOKIES_DOMAIN)
    if not custom_cookies_domain:
        return ''

    language_code = get_language_code(language).lower()

    if cookie not in custom_cookies_domain:
        if custom_cookies_domain.get('default'):
            cookie = 'default'
        else:
            return ''

    domain_value = custom_cookies_domain[cookie]

    if ';' in domain_value and ':' in domain_value:

        for lang_domain in domain_value.split(';'):
            if lang_domain.strip().startswith(f"{language_code}:"):
                return lang_domain.split(':', 1)[1].strip()
        return ''

    else:
        return domain_value