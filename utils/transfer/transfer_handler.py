# -*- coding: utf-8 -*-
import datetime
import json
import logging
from urllib.parse import urljoin

import requests
from flask import request
from flask.views import MethodView

from booking_process.constants.integrationsConstants import TRANSFER_AND_EXPERIENCE_INTEGRATION
from booking_process.utils.booking.booking_email_utils import send_transfer_service_provider_email
from booking_process.utils.development.dev_booking_utils import DEV


from booking_process.constants import advance_configs_names
from booking_process.constants.dates_standard import SEARCH_DATE_FORMAT
from booking_process.handlers.booking4 import ReservationConfirmedHandler
from booking_process.libs.communication import directDataProvider
from booking_process.utils.auditing import auditUtils
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.integrations_utils import get_integration_configuration_properties
from booking_process.utils.email.email_utils_third_party import send_email
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_based_on_request
from booking_process.utils.namespaces import namespace_utils
from booking_process.utils.session import session_manager
from booking_process.utils.transfer.transfer_controller import TransferController
from utils.flask_requests import response_utils
from booking_process.utils.rescueseeker import build_template
from booking_process.utils.templates.template_utils import buildTemplate
from booking_process.utils.transfer.constants import TRANSFER_ADAPTER_ENTRY, SERVICE_GET_TRANSFER, \
	TEMPLATE_TRANSFER_FOR_BACKOFFICE, TEMPLATE_TRANSFER_FOR_CLIENT, SERVICE_PARTNER_TRANSFER, TRANSFER_TO, TRANSFER_FROM, \
	TEMPLATE_KO

ALLOW_NAMESPACES = ['landmar-gigantes', 'landmar-arena']


def create_payment_session_handler():
	session_manager.initSession(createNew=True)
	session_manager.save_session_to_datastore()
	sid = session_manager.get_session_id()
	language = get_language_based_on_request()
	transfer_controller = TransferController(sid, language, False)
	if transfer_controller:
		transfer_controller.register_service_in_session(sid)

	response_utils.add_to_response_content(sid)
	return ""


def send_transfer_service_provider_email_handler():
	is_cancellation = request.values.get('is_cancellation', False)
	modification_from_manager = request.values.get('modification_from_manager', False)
	reservation_id = request.values.get('reservation_id', 'TEST999999999')

	send_transfer_service_provider_email(reservation_id, request.values.get('email', ''),
										 is_cancellation=is_cancellation,
										 is_modification_from_manager=modification_from_manager)

	response_utils.add_to_response_content("OK")
	return ""



class TestReservation():
	def __init__(self):
		self.identifier = "BE-253910818"
		self.extraInfo = '{"service_transfer":{"to": 51182, "from": 0}}'

def transfer_is_not_confirmed(service):
	if not service:
		return False
	if not service.status:
		service.status = ''
	if service.status.lower() != 'confirmed' and service.products and service.products != '{}':
		return True
	return False

def transfer_is_paid(reservation):

	extra_info = json.loads(reservation.extraInfo)
	amount_paid = extra_info.get('payed', 0)
	return amount_paid > 0

class TransferPendingPaymentsHandler(MethodView): # TODO: This is used? can be deleted?

	def html_transfers_pending(self, pending_reservations, language=SPANISH):

		logging.info("rendering html_transfers_pending")

		languageDict = get_web_dictionary(language)
		args = {'reservations': pending_reservations}
		context = dict(list(args.items()) + list(languageDict.items()))
		content = build_template('email/transfer/payment_pending.html', context)
		return content

	def send_transfers_pending_of_the_day(self):
		date = datetime.datetime.now()
		if request.values.get('date'):
			date = datetime.datetime(*list(map(int, request.values.get('date').split("-"))))

		query_date_start = date.strftime(SEARCH_DATE_FORMAT)
		date_end = date + datetime.timedelta(days=1)
		query_date_end = date_end.strftime(SEARCH_DATE_FORMAT)

		for namespace in namespace_utils.get_all_namespaces():
			if 'r__' in namespace or namespace not in ALLOW_NAMESPACES:
				continue
			namespace_utils.set_namespace(namespace)
			logging.info("Setting namespace to -> %s" % namespace)

			reservations = self.get_reservation_with_checkin(query_date_start, query_date_end)
			if not reservations:
				return

			content = self.html_transfers_pending(reservations, language=SPANISH)

			translation = get_web_dictionary(SPANISH)
			subject_email = translation.get("T_subject_pending_payment_transfer")
			date_format = datetime.datetime.strptime(query_date_start, SEARCH_DATE_FORMAT).strftime("%d/%m/%Y"),
			subject_email = subject_email.replace("@@PAYMENT_DAY@@", str(date_format))

			email_sender = get_config_property_value(advance_configs_names.EMAIL_SENDER)

			te_config = get_integration_configuration_properties(TRANSFER_AND_EXPERIENCE_INTEGRATION)
			email_pending_transfer = te_config.get("email_pending_transfer")
			if not email_pending_transfer:
				return

			if request.values.get('email'):
				email_pending_transfer = request.values.get('email')

			response_utils.add_to_response_content(content)

			logging.info("sending email to %s" % email_pending_transfer)

			send_email(email_pending_transfer, subject_email, content, content, sender=email_sender)

	def get_reservation_with_checkin(self, query_date_start, query_date_end):

		logging.info("Getting reservations with checkin between %s and %s" % (query_date_start, query_date_end))

		reservations_with_checkin_today = directDataProvider.get('Reservation', {'startDate': query_date_start})
		logging.info("reservations_today_forward candidates %s" % (len(reservations_with_checkin_today)))

		reservations_with_checkin_today = [x for x in reservations_with_checkin_today if not x.cancelled]
		logging.info("reservations_today_forward not cancelled %s" % (len(reservations_with_checkin_today)))

		reservations_today_forward = [x for x in reservations_with_checkin_today if x.additionalServices]
		logging.info("reservations_today_forward with transfer %s" % (len(reservations_today_forward)))

		pending_reservations = []
		for reservation in reservations_today_forward:

			identifier = reservation.identifier

			external_service = directDataProvider.get('ServiceExternal', {'booking_id': identifier.strip().upper()})
			if external_service and not transfer_is_paid(reservation):

				reservation_dict = {'identifier': reservation.identifier,
				                    'email': reservation.email,
				                    'startDate': datetime.datetime.strptime(reservation.startDate, SEARCH_DATE_FORMAT).strftime("%d/%m/%Y"),
				                    'endDate': datetime.datetime.strptime(reservation.endDate, SEARCH_DATE_FORMAT).strftime("%d/%m/%Y"),
				                    'name': reservation.name,
				                    'lastName': reservation.lastName,
				                    'services': reservation.additionalServices}

				logging.info("reservation added: %s", reservation_dict)

				pending_reservations.append(reservation_dict)

		return pending_reservations

	def get(self):
		try:
			logging.info("getting send_pending_transfer_daily")
			te_config = get_integration_configuration_properties(TRANSFER_AND_EXPERIENCE_INTEGRATION)
			send_pending_transfer_daily = te_config.get("send_pending_transfer_daily")
			if send_pending_transfer_daily:
				logging.info("send_pending_transfer_daily enabled!")
				self.send_transfers_pending_of_the_day()
			response_utils.add_to_response_content("OK")
			return
		except Exception as e:
			message = auditUtils.makeTraceback()
			logging.exception(message)
			# notify_exception("Transfer pending email not sent [%s][%s]" % (namespace_utils.get_application_id(), namespace_utils.get_namespace()), message)
		response_utils.add_to_response_content("KO")

class TransferPendingFormHandler(ReservationConfirmedHandler):

	def _get_receipt_for_agent(self, booking_id, transfer_id, language):

		service_get_transfer = urljoin(TRANSFER_ADAPTER_ENTRY, SERVICE_GET_TRANSFER)


		response = requests.get(service_get_transfer, params={'transfer_id': transfer_id}, timeout=10)
		if response and response.text:
			transfer_data = json.loads(response.text)
			if transfer_data.get('entity') == "transfer":
				transfer_data['confirmed'] = True

		params = dict(get_web_dictionary(language).items())
		params['booking_id'] = transfer_data.get('booking_id')
		params['transfer_to'] = transfer_data
		params['id_affiliate'] = transfer_data.get('id_affiliate')
		params['id_product'] = transfer_data.get('id_product')
		params['saved'] = request.values.get('saved')
		params['wrong'] = request.values.get('wrong')
		params['form_type_for'] = request.values.get('form_type')

		response_to = request.values.get('response_to')

		url_action = urljoin(TRANSFER_ADAPTER_ENTRY, response_to)

		params['url_action'] = url_action

		return params

	def _get_receipt_for_client(self, booking_id, language):

		if booking_id.endswith("-R"):
			booking_id = booking_id[:-2]

		if DEV:
			reservation = TestReservation()
		else:
			reservation = directDataProvider.get("Reservation", {"identifier": booking_id}, 1)[0]

		extra_info = json.loads(reservation.extraInfo)

		services = extra_info.get(SERVICE_PARTNER_TRANSFER)
		transfer_to = services.get(TRANSFER_TO)
		transfer_from = services.get(TRANSFER_FROM)

		url = TRANSFER_ADAPTER_ENTRY

		service_get_transfer = urljoin(url, SERVICE_GET_TRANSFER)

		transfer_to_data = ''
		if transfer_to:
			response = requests.get(service_get_transfer, params={'transfer_id': transfer_to}, timeout=10)
			if response and response.text:
				transfer_to_data = json.loads(response.text)
				if transfer_to_data.get('entity') == "transfer":
					transfer_to_data['confirmed'] = True

		transfer_from_data = ''
		if transfer_from:
			response = requests.get(service_get_transfer, params={'transfer_id': transfer_from}, timeout=10)
			if response and response.text:
				transfer_from_data = json.loads(response.text)
				if transfer_from_data.get('entity') == "transfer":
					transfer_from_data['confirmed'] = True

		language = reservation.language

		params = dict(get_web_dictionary(language).items())
		params['booking_id'] = reservation.identifier
		params['transfer_to'] = transfer_to_data
		params['transfer_from'] = transfer_from_data
		params['id_affiliate'] = transfer_to_data.get('id_affiliate')
		params['id_product'] = transfer_to_data.get('id_product')
		params['saved'] = request.values.get('saved')
		params['wrong'] = request.values.get('wrong')

		response_to = request.values.get('response_to')

		url_action = urljoin(TRANSFER_ADAPTER_ENTRY, response_to)

		params['url_action'] = url_action

		return params

	def buildMainContent(self, language, country):

		try:
			transfer_id = request.values.get('transfer_id')
			booking_id = request.values.get('booking_id')
			for_agent = request.values.get('for_agent')

			if not for_agent:
				context = self._get_receipt_for_client(booking_id, language)
				html = buildTemplate(TEMPLATE_TRANSFER_FOR_CLIENT, context)
				return html

			context = self._get_receipt_for_agent(booking_id, transfer_id, language)
			html = buildTemplate(TEMPLATE_TRANSFER_FOR_BACKOFFICE, context)

			return html

		except Exception as e:
			html = buildTemplate(TEMPLATE_KO, {})
			message = auditUtils.makeTraceback()
			logging.exception(message)
			return html
