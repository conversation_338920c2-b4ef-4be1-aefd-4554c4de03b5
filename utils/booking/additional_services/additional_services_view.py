import logging
from collections import OrderedDict

from booking_process.constants.advance_configs_names import CUSTOM_BOOKING2_TEXT_UPSELLING, ADDITIONAL_SERVICES_v2_TABS, \
    ADDITIONAL_SERVICES_v2_TABS_SCROLL, ADDI<PERSON>ON<PERSON>_SERVICES_COUNTER_SELECTOR, ADDITIONAL_SERVICES_FILTERS_BY_CATEGORY, \
    FORCE_X_DECIMALS, ADDITIONAL_SERVICES_SHOW_TOTAL_WITH_ROOMS, ADDITIONAL_SERVICES_SHOW_FIRST, \
    SHOW_PRICE_IN_FREE_SERVICES, UPGRADE_POPUP_INFO, ADDITIONAL_SERVICES_VERSION
from booking_process.constants.web_configs_names import BOOKING_PROCESS_CONFIGS
from booking_process.utils.booking.selections.selection_price_utils import getTotalPrice
from booking_process.constants.session_data import SELECTED_OPTION_KEY, PRICE_OPTION_KEY_PREFIX
from booking_process.utils.currency.currencyUtils import get_currency_symbol
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import sanitize_html
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_web_dictionary
from booking_process.utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.namespaces.namespace_utils import get_hotel_code
from booking_process.utils.session import session_manager


def build_additional_services_general_context(context, services_list, language):
    # Set free services
    for service in services_list:
        if not service.get('price') and not service.get('price_from'):
            service['free'] = True

    context['show_price_in_free_services'] = get_config_property_value(SHOW_PRICE_IN_FREE_SERVICES)

    if force_x_decimals := get_config_property_value(FORCE_X_DECIMALS):
        context['force_x_decimals'] = force_x_decimals


def generate_upgrade_message(services_list, language):
    general_dict = get_web_dictionary(language)
    context_to_return = {}

    cheapper_additional_service = sorted(services_list, key=lambda service: service['price'])
    minium_upgrade_price = "{0:.2f}".format(cheapper_additional_service[0]['price'])
    price_label_type = cheapper_additional_service[0]['price_label']
    question_to_replace = general_dict['T_room_real_upgrade_question']

    custom_upselling_text = get_config_property_value(CUSTOM_BOOKING2_TEXT_UPSELLING)

    if custom_upselling_text:
        text_upselling = get_section_from_section_spanish_name(custom_upselling_text, language)
        if text_upselling:
            if text_upselling.get("content"):
                question_to_replace = text_upselling.get("content")

            text_upselling_advance = get_properties_for_entity(text_upselling.get('key'), language)
            if text_upselling_advance.get("custom_background"):
                context_to_return['room_upgrade_question_background_color'] = text_upselling_advance['custom_background']

            if text_upselling_advance.get("new_question_upgrade_style"):
                context_to_return['new_question_upgrade_style'] = True

            if text_upselling_advance.get("custom_background_image"):
                context_to_return['room_upgrade_question_background_image'] = text_upselling_advance['custom_background_image']

            if text_upselling_advance.get("custom_background_image_mobile"):
                context_to_return['room_upgrade_question_background_image_mobile'] = text_upselling_advance['custom_background_image_mobile']

    context_to_return['room_upgrade_question'] = question_to_replace.replace('XX', minium_upgrade_price).replace("@@TYPE@@", price_label_type)

    return context_to_return


def build_additional_services_v2_context(services_list, language, upgrade_additional_services=None, selected_additional_services=None):
    context_to_return = {}
    language_dict = get_web_dictionary(language)

    additional_services_tabs = get_config_property_value(ADDITIONAL_SERVICES_v2_TABS)
    if additional_services_tabs:
        context_to_return['additional_services_tabs'] = True

    additional_services_tabs_scroll = get_config_property_value(ADDITIONAL_SERVICES_v2_TABS_SCROLL)
    if additional_services_tabs_scroll:
        context_to_return['additional_services_tabs_scroll'] = True

    if get_config_property_value(ADDITIONAL_SERVICES_COUNTER_SELECTOR):
        context_to_return['counter_service_selector'] = True

    if get_config_property_value(ADDITIONAL_SERVICES_SHOW_TOTAL_WITH_ROOMS):
        selectedPrice = []
        for partialSelected in session_manager.get(SELECTED_OPTION_KEY).split(";"):
            selectedPrice.append(session_manager.get(PRICE_OPTION_KEY_PREFIX + partialSelected))
        price = getTotalPrice(selectedPrice)
        context_to_return['rooms_total'] = price
    if get_config_property_value(UPGRADE_POPUP_INFO):
        context_to_return['upgrade_popup_info'] = get_config_property_value(UPGRADE_POPUP_INFO)

    additional_service_version_config = get_config_property_value(ADDITIONAL_SERVICES_VERSION)
    if 'hide_services_description' in additional_service_version_config:
        context_to_return['hide_services_description'] = True
    if 'merge_upgrades_and_services' in additional_service_version_config:
        context_to_return['merge_upgrades_and_services'] = True

    # Services with categories
    categories_dict = {
        'default': {
            'category_info': {
                'title': language_dict['T_servicios_adicionales'],
                'description': language_dict.get('T_additional_services_description', ''),
                'order': -1
            },
            'services': []
        }
    }

    if selected_additional_services:
        categories_dict['selected'] = {'category_info': {
            'title': language_dict['T_seleccionado'],
            'description': '',
            'order': 10
        }, 'services': selected_additional_services}

    # Services for upgrade
    if upgrade_additional_services:
        categories_dict.update(upgrade_additional_services_v2(upgrade_additional_services, language))

    categories_sections = get_pictures_from_section_name('_additional_services_categories', language)
    categories_main_section = get_section_from_section_spanish_name('_additional_services_categories', language)
    if categories_sections:
        for index, category in enumerate(categories_sections):
            category_properties = get_properties_for_entity(category.get('key'), language)
            category_id = category.get('altText')
            if category_id in {'default', 'upgrade', 'selected'}:
                if not categories_dict.get(category_id):
                    continue
                target_category_info = categories_dict[category_id]['category_info']
                if category.get('title'):
                    target_category_info['title'] = category['title']

                if category.get('description'):
                    target_category_info['description'] = category['description']

                if category_properties.get('custom_title_tab'):
                    target_category_info['custom_title_tab'] = category_properties['custom_title_tab']
            else:
                category_order = index
                if category.get('order'):
                    try:
                        category_order = int(category.get('order'))
                    except ValueError:
                        logging.error(f'Invalid order value for category {category.get("title")}: {category.get("order")}')
                        category_order = index

                categories_dict[category.get('altText')] = {'category_info': {
                    'title': category.get('title'),
                    'custom_title_tab': category_properties.get('custom_title_tab'),
                    'description': category.get('description'),
                    'order': category_order,
                }, 'services': []}
            
        if categories_main_section and categories_main_section.get('subtitle'):
            context_to_return['categorized_services_main_title'] = categories_main_section.get('subtitle')

    first_additional_services = get_config_property_value(ADDITIONAL_SERVICES_SHOW_FIRST)

    # Order dict by key with name order
    categories_ordered = sorted(list(categories_dict.items()), key=lambda x_y: x_y[1]['category_info']['order'], reverse=(not first_additional_services))
    categories_dict = OrderedDict()
    for category in categories_ordered:
        categories_dict[category[0]] = category[1]

    for service in services_list:
        if service.get('category') and categories_dict.get(service.get('category')):
            categories_dict[service.get('category')]['services'].append(service)
        else:
            categories_dict['default']['services'].append(service)

    additional_services_filters_config = get_config_property_value(ADDITIONAL_SERVICES_FILTERS_BY_CATEGORY)
    if additional_services_filters_config:
        build_additional_services_filters(additional_services_filters_config, categories_dict, language)

    context_to_return['services_categorized'] = categories_dict

    booking_process_configs = get_web_configuration(BOOKING_PROCESS_CONFIGS)
    if booking_process_configs:
        custom_colours = {}
        if booking_process_configs.get('additional_services_add'):
            custom_colours['add'] = booking_process_configs.get('additional_services_add')
        if booking_process_configs.get('additional_services_remove'):
            custom_colours['remove'] = booking_process_configs.get('additional_services_remove')
        if booking_process_configs.get('additional_services_continue_booking_btn'):
            custom_colours['continue_booking_btn'] = booking_process_configs.get('additional_services_continue_booking_btn')
        if booking_process_configs.get('custom_upgrade_title_styles'):
            custom_colours['custom_upgrade_title_styles'] = booking_process_configs.get('custom_upgrade_title_styles')
        context_to_return['custom_colours'] = custom_colours

    return context_to_return


def build_additional_services_filters(filters_config, categories_dict, language):
    filters_config_split = filters_config.split(';')
    if len(filters_config_split) == 2:
        category = filters_config_split[0]
        filters_section = filters_config_split[1]

        if categories_dict.get(category):
            filter_selectors = get_pictures_from_section_name(filters_section, language)
            category_filters = []
            for selector in filter_selectors:
                selector_properties = get_properties_for_entity(selector.get('key'), language)
                selector_properties_spanish = get_properties_for_entity(selector.get('key'), SPANISH)

                options = []
                if selector_properties.get('options') and selector_properties_spanish.get('options'):
                    for index, option in enumerate(selector_properties['options'].split(';')):
                        options.append({
                            'title': option,
                            'filter': selector_properties_spanish['options'].split(';')[index]
                        })

                    category_filter = {
                        'selector_title': selector.get('title'),
                        'options': options
                    }

                    category_filters.append(category_filter)

            categories_dict[category]['filter_options'] = category_filters


def upgrade_additional_services_v2(upgrade_services, language):
    language_dict = get_web_dictionary(language)
    upgrade_info = language_dict['T_room_upgrade_question'].split("? ")
    upgrade_info[0] = upgrade_info[0] + '?'

    min_price = min([service.get('price_per_night_person') if service.get('price_per_night_person') else service.get('price') for service in upgrade_services])
    total_price = int(min_price * 100) / 100.0  # Max 2 decimals

    upgrade_info[1] = upgrade_info[1].replace("@@AMOUNT@@", str(total_price)).replace("@@CURRENCY@@", get_currency_symbol())

    upgrade_dict = {'upgrade': {'category_info': {
        'title': upgrade_info[0],
        'description': upgrade_info[1],
        'order': 10,
        'division': '2'
    }, 'services': upgrade_services}}

    return upgrade_dict


def build_extra_additional_services_upselling_list(section_name, services_data, identifier, currency, language=SPANISH):
    banner_data = {}
    banner_section = get_pictures_from_section_name(section_name, language)

    if banner_section and services_data:
        banner_data['title'] = banner_section[0].get('title')
        banner_data['description'] = banner_section[0].get('description')
        banner_data['banner'] = banner_section[0].get('servingUrl')
        banner_data['services_list'] = []
        for service in services_data:
            banner_data['services_list'].append({
                'servingUrl': service.get('picture'),
                'name': sanitize_html(service.get('supplementName', '')).decode('utf8'),
                'description': sanitize_html(service.get('supplementDescription', '')).decode('utf8'),
                'booking_link': f"/booking2_upselling?identifier={identifier}&amp;language={language}&amp;currency={currency}&amp;service={service.get('key')}"
            })

    return banner_data


def process_services_with_banner_info(language, selected_supplements, advanced_selected_supplements, completeDictionary):
    if advanced_selected_supplements:
        for advanced_service in advanced_selected_supplements:
            if advanced_service.get('extra_info') and advanced_service.get('extra_info').get(
                    'special_confirmation_info'):
                completeDictionary['banner_tickets'] = build_banner_tickets(language, advanced_service['extra_info']['special_confirmation_info'], completeDictionary.get('localizador'))
                break
    else:
        for supplement in selected_supplements:
            if isinstance(supplement, list) and len(supplement):
                supplement = supplement[0]
                if supplement.get('special_tickets'):
                    completeDictionary['banner_tickets'] = build_banner_tickets(language, supplement.get('special_tickets'), completeDictionary.get('localizador'))
                    break
    return completeDictionary


def build_banner_tickets(language, section_name_banner_tickets, booking_id):
    section_tickets = get_section_from_section_spanish_name(section_name_banner_tickets, language)
    banner_tickets = {}
    if section_tickets:
        pictures_tickets = get_pictures_from_section_name(section_name_banner_tickets, language)
        advance_properties = get_properties_for_entity(section_tickets.get('key'), language)
        if pictures_tickets and pictures_tickets[0]:
            picture = pictures_tickets[0]
            is_mobile = user_agent_is_mobile() or session_manager.get('userAgentIsMobile')
            params_to_replace = {
                'hotel_code': get_hotel_code(),
                'sid': session_manager.get_session_id(),
                'booking_id': booking_id
            }
            banner_tickets['description'] = section_tickets
            banner_tickets['picture'] = picture
            banner_tickets['mobile_version'] = is_mobile

            if advance_properties and advance_properties.get('main_color'):
                banner_tickets['main_color'] = advance_properties.get('main_color')

            if picture.get('linkUrl'):
                banner_link = picture['linkUrl']
                for param_name, param_value in params_to_replace.items():
                    if param_value:
                        banner_link = banner_link.replace('@@%s@@' % param_name, param_value)
                banner_tickets['link'] = banner_link

    return banner_tickets

