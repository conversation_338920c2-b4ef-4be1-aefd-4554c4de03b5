import logging
import random
import uuid

import requests

from booking_process.components import render_hooks
from booking_process.constants.advance_configs_names import USE_BOOKING_IDENFIER_BUILDER, FORCE_NUMERIC_ID, \
    PREFIX_BOOKINGID, NEVER_ADD_PREFIX_TO_LOC, NR_CUSTOM_IDENTIFIERS_PREFIX, SU<PERSON><PERSON>_BOOKINGID
from booking_process.constants.general_constants import BOOKING_ID_PREFIX, BOOKING_ID_SUFFIX
from booking_process.libs.communication.retryDecorator import retry
from booking_process.libs.pasarelas.gateway_constants import SERMEPA_SAVE_CC_BY_TOKENIZATION, \
    SAVE_CARD_TOKEN_BY_TOKENIZATION, BOOKING_ID_ALREADY_BUILT
from booking_process.utils.agencies.constants import MANAGER_SOURCE_AGENCY
from booking_process.utils.booking.bookingUtils import get_agent_ring2travel, _add_prefix_to_identifier
from booking_process.utils.booking.rates.rate_info import rateIsNonRefundable
from booking_process.utils.booking.selections.selection_utils import getSelected<PERSON><PERSON><PERSON><PERSON>
from booking_process.utils.bookingConstants import <PERSON><PERSON>R_WAIT_FOR_BOOKING_CONTENT
from booking_process.constants.session_data import FORCED_BOOKING_IDENTIFIER, BOOKING_CONTENT, INFO_DETAILS_PREBOOKING, \
    SOURCE, AGENT_ID, HOTELVERSE_ROOM_NUMBER, INCLUDE_IN_EXTRA_INFO_FROM_SEARCH
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.manager_communicator.constants import MANAGER_SOURCE_CALL_CENTER
from booking_process.utils.session import session_manager


def build_reservation_identifier(search: dict, payment_order_id: str | None) -> str:
    """Build the booking identifier for the reservation.

    :param search: The current search data from session.
    :param payment_order_id: The payment order ID for the reservation if reservation is made through a payment gateway.
    :return: The booking identifier for the reservation.
    """
    forced_booking_identifier = session_manager.get(FORCED_BOOKING_IDENTIFIER)

    if get_config_property_value(FORCE_NUMERIC_ID) and not payment_order_id:
        booking_id = '%d' % random.randint(10000000, 99999999)

    # In case we have a custom identifier from search extra info, we use it. It's used for Fuerte Hoteles offers.
    elif (custom_identifier_from_search := _get_identifier_from_search_extra_info()) and not forced_booking_identifier:
        logging.info(f"Using custom identifier from search extra info: {custom_identifier_from_search}")
        # Return it because this identifier is already built with the required prefix and suffix.
        return custom_identifier_from_search

    # Some hotels need a identifier generator (i.e. to have it synchronized with PMS like Fuerte Hoteles)
    elif get_config_property_value(USE_BOOKING_IDENFIER_BUILDER) and not forced_booking_identifier:
        if session_manager.get(BOOKING_ID_ALREADY_BUILT) and not session_manager.get(BOOKING_CONTENT):
            booking_id = session_manager.get(BOOKING_ID_ALREADY_BUILT)
            logging.warning(f"User has reload booking4 or he has done a History -1. BookingId made before: {booking_id}")
            return ERROR_WAIT_FOR_BOOKING_CONTENT
        else:
            logging.info("Trying to retreive a custom booking identifier")
            booking_id = _get_booking_id_from_builder()
            logging.info(f"Booking identifier retreived is: {booking_id}")
            session_manager.set(BOOKING_ID_ALREADY_BUILT, booking_id)

    # In the case of gateways, it simplifies our life if we use the same order ID
    elif payment_order_id:
        booking_id = payment_order_id
        if session_manager.get(SERMEPA_SAVE_CC_BY_TOKENIZATION) and not session_manager.get(SAVE_CARD_TOKEN_BY_TOKENIZATION):
            # Tokenization process need that first identifier (paymentOrderId) given to TPV were always different to real one
            booking_id = booking_id[:-1] + "1"

    elif forced_booking_identifier:
        logging.info(f"Forcing identifier to: {forced_booking_identifier} because of FORCED_BOOKING_IDENTIFIER")
        booking_id = forced_booking_identifier

    else:
        tempUUID = str(uuid.uuid4())
        booking_id = tempUUID[1] + tempUUID[2] + tempUUID[3] + tempUUID[10] + tempUUID[15] + tempUUID[33] + tempUUID[34] + tempUUID[35] + tempUUID[27]

    booking_id = build_reservation_prefix_identifier(booking_id, search, payment_order_id)
    booking_id = build_reservation_suffix_identifier(booking_id)

    return booking_id


def build_reservation_prefix_identifier(booking_id: str, search: dict, payment_order_id: str | None) -> str:
    """Build the booking identifier prefix for the reservation.

    :param booking_id: The current booking identifier.
    :param search: The current search data from session.
    :param payment_order_id: The payment order ID for the reservation if reservation is made through a payment gateway.
    :return: The booking identifier prefix for the reservation.
    """
    special_prefix_booking_id = render_hooks('process_booking_id', id_type=BOOKING_ID_PREFIX) or ''
    prefix_booking = get_web_configuration(PREFIX_BOOKINGID)
    if prefix_booking:
        if session_manager.get(INFO_DETAILS_PREBOOKING):
            prefix_booking_id = prefix_booking.get('prebooking', '')
        else:
            prefix_booking_id = prefix_booking.get(search.get('source', '').lower())
            if not prefix_booking_id:
                prefix_booking_id = prefix_booking.get('default', '')
        if search.get('source', '') == MANAGER_SOURCE_CALL_CENTER:
            if session_manager.get(AGENT_ID):
                is_ring2travel = get_agent_ring2travel(session_manager.get(AGENT_ID))
                if is_ring2travel:
                    prefix_booking_id = prefix_booking.get('ring2travel', '')

        if prefix_booking_id and not booking_id.startswith(prefix_booking_id):
            return f'{special_prefix_booking_id}{prefix_booking_id}{booking_id}'

    if session_manager.get(SOURCE) and session_manager.get(SOURCE) == MANAGER_SOURCE_AGENCY and "AGE" not in booking_id:
        booking_id = f'AGE{booking_id}'

    # Used to identify Ring2Travel reservations
    if search.get('source', '') == MANAGER_SOURCE_CALL_CENTER:
        if not get_config_property_value(NEVER_ADD_PREFIX_TO_LOC) and not payment_order_id:
            booking_id = f'R{booking_id}'
            logging.info(f"BookingId: Prefix added {booking_id}")
        booking_id = booking_id.replace("W", "C")  # Requirements from Fuerte

    booking_id = _add_prefix_to_identifier(booking_id)

    nr_prefix = get_config_property_value(NR_CUSTOM_IDENTIFIERS_PREFIX)
    if nr_prefix and rateIsNonRefundable(getSelectedRateKey()):
        booking_id = f'{nr_prefix}{booking_id}'

    return f'{special_prefix_booking_id}{booking_id}'


def build_reservation_suffix_identifier(booking_id: str) -> str:
    """Build the booking identifier suffix for the reservation.

    :param booking_id: The current booking identifier.
    :return: The booking identifier suffix for the reservation.
    """
    special_sufix_booking_id = render_hooks('process_booking_id', id_type=BOOKING_ID_SUFFIX) or ''
    suffix_booking = get_web_configuration(SUFFIX_BOOKINGID)
    if suffix_booking:
        if suffix_booking.get('hotelverse', '') and session_manager.get(HOTELVERSE_ROOM_NUMBER):
            suffix_booking_id = suffix_booking['hotelverse']
            if not booking_id.endswith(suffix_booking_id):
                booking_id = f'{booking_id}{suffix_booking_id}'

    if special_sufix_booking_id:
        booking_id = f'{booking_id}{special_sufix_booking_id}'

    logging.info(f"[SUFFIX BUILDER] booking_id: {booking_id}")
    return booking_id


@retry(Exception, tries=3, delay=1, backoff=2)
def _get_booking_id_from_builder():
    '''
	Some hotels want to use specific identifiers for reservations so we provide a URL in the integration for this
	:return:
	'''
    target_url = get_config_property_value(USE_BOOKING_IDENFIER_BUILDER)
    response = requests.get(target_url, timeout=8)
    logging.info("Getting identifier from url %s" % target_url)
    return response.text


def _get_identifier_from_search_extra_info() -> str:
    """
    Get the booking identifier from the search extra info.
    :return: The booking identifier from the search extra info or empty string if not found.
    """
    booking_id = ''

    if search_extra_info := session_manager.get(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH):
        try:
            if isinstance(search_extra_info, list):
                booking_id = next(
                    (element.split('PREBOOKING__')[1] for element in search_extra_info if element.startswith('PREBOOKING__')),
                    ''
                )
            elif isinstance(search_extra_info, str):
                if search_extra_info.startswith('PREBOOKING__'):
                    booking_id = search_extra_info.split('PREBOOKING__')[1]
        except Exception as e:
            logging.error(f"Error getting booking id from search extra info: {e}")
            booking_id = ''

    return booking_id
