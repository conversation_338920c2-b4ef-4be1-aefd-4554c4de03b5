import logging

from booking_process.constants.dates_standard import SEARCH_DATE_FORMAT
from booking_process.libs.communication import directData<PERSON>rovider
from booking_process.utils.booking.common_data_provider import get_all_rates_entity_json_map, get_all_rate_conditions
from booking_process.utils.booking.conditions.rate_conditions import NO_CANCELABLE
from booking_process.constants.session_data import SEARCH_KEY_PREFIX
from booking_process.utils.bookingConstants import CANCELLATION_POLICY_MULTIPLE_POLICIES
from booking_process.utils.email.email_utils_third_party import notify_exception
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.session import session_manager
import datetime

from models.rates import Rate, MultiRate
from utils.managers_cache.manager_cache import managers_cache


def get_rate_model(rate_key: str = None, check_multirate: bool = True) -> Rate | MultiRate:
    """
    Search for a rate in the datastore. If it is not found, check if it is a multirate.
    :param rate_key: Key to search at datastore.
    :param check_multirate: True if we want to check if it is a multirate, False otherwise.
    :return: Rate or MultiRate entity found or empty Rate entity if not found.
    """

    if not rate_key:
        return Rate()

    match_rate = directDataProvider.getEntity("Rate", rate_key)
    if not match_rate and check_multirate:
        match_rate = directDataProvider.getEntity("MultiRate", rate_key)

    return match_rate


#@cache_by_request(lambda args: "rateIsNonRefundable" + args[0])
@managers_cache(only_thread_local=True)
def rateIsNonRefundable(selected_rate_key):
    search_dates = session_manager.get(SEARCH_KEY_PREFIX)
    start_date = None
    if search_dates:
        start_date = search_dates.get("startDate")
        start_date = datetime.datetime.strptime(start_date, SEARCH_DATE_FORMAT)

    if not selected_rate_key:
        return False

    current_rate = get_all_rates_entity_json_map().get(selected_rate_key)
    if not current_rate:
        return False

    conditions_rates = get_all_rate_conditions(SPANISH)

    rate_conditions = conditions_rates.get(current_rate.get("rateCondition"))

    if not rate_conditions:
        logging.warning(f"Rate condition not found: {current_rate.get('rateCondition')}")
        notify_exception(
            '[Account] Rate without a valid rate condition',
            f"Rate has configured a rate condition that not exists anymore: {current_rate.get('localName')}",
            add_hotel_info=True
        )
        return True

    if current_rate.get('cancellationPolicy', '') == CANCELLATION_POLICY_MULTIPLE_POLICIES and start_date:
        for condition_period in rate_conditions.get('cancellationPeriods', ''):
            start_date_period = condition_period.split(";")[0]
            end_date_period = condition_period.split(";")[1]
            rate_conditions = condition_period.split(";")[2]
            start_date_period = datetime.datetime.strptime(start_date_period, SEARCH_DATE_FORMAT)
            end_date_period = datetime.datetime.strptime(end_date_period, SEARCH_DATE_FORMAT)
            if start_date < start_date_period or start_date > end_date_period:
                continue

            cancellation_policy_period = conditions_rates.get(rate_conditions, {}).get("cancellationPolicy")
            if cancellation_policy_period == NO_CANCELABLE:
                return True

        return False

    cancellation_policy = current_rate.get('cancellationPolicy', '')
    return cancellation_policy == NO_CANCELABLE
