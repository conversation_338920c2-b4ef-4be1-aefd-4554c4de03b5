import copy
import datetime
import logging
import random

from booking_process.constants.advance_configs_names import VERY_ASKED_ROOM_FILTER, JUST_BOOK_MESSAGE_FILTER
from booking_process.constants.session_data import SEARCH_KEY_PREFIX
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity

from booking_process.utils.session import session_manager

__author__ = 'adrian'


def rooms_availables_is_less_equal_than(number, rooms_structure, searchResult, language):
	'''Used to know when a specific room has less or equal than N number of rooms, and edit the params'''

	very_asked_room_filter = get_config_property_value(VERY_ASKED_ROOM_FILTER)
	rooms_availability = searchResult['extra'].get('availability')

	if not rooms_structure:
		return

	if not rooms_availability:
		rooms_availability = {}
		default_availability = 3
	else:
		default_availability = 0

	if isinstance(rooms_structure[0], list):
		_process_multiple_room_available(rooms_structure, very_asked_room_filter, rooms_availability, default_availability, number, language)
	else:
		_process_single_room_available(rooms_structure, very_asked_room_filter, rooms_availability, default_availability, number, language)


def _process_single_room_available(rooms_structure, very_asked_room_filter, rooms_availability, default_availability, number, language):

	section_to_replace = get_section_from_section_spanish_name('muy-solicitado', language)

	for rooms_rates in rooms_structure:
		for room_selected in rooms_rates['roomStructure']:

			room_key = room_selected.get('roomKey')

			apply = True
			if very_asked_room_filter:
				spanishRoomName = room_selected.get('spanishName','')
				for roomNotAlowed in very_asked_room_filter.split("@@"):
					if spanishRoomName.lower() in roomNotAlowed.lower():
						apply = False
						break

			available_rooms = rooms_availability.get(room_key, default_availability)
			if apply and available_rooms and int(available_rooms) <= int(number):

				if section_to_replace:
					room_selected['very_asked_personalized'] = section_to_replace
				room_selected['very_asked_room'] = True


def _process_multiple_room_available(rooms_structure, very_asked_room_filter, rooms_availability, default_availability, number, language):
	for rooms_selection in rooms_structure:
		for rooms_rates in rooms_selection:
			for room_selected in rooms_rates['roomStructure']:

				room_key = room_selected.get('roomKey')

				apply = True
				if very_asked_room_filter:
					spanishRoomName = room_selected.get('spanishName','')
					for roomNotAlowed in very_asked_room_filter.split("@@"):
						if spanishRoomName.lower() in roomNotAlowed.lower():
							apply = False
							break

				available_rooms = rooms_availability.get(room_key, default_availability)
				if apply and available_rooms and int(available_rooms) <= int(number):
					section_to_replace = get_section_from_section_spanish_name('muy-solicitado', language)
					if section_to_replace:
						room_selected['very_asked_personalized'] = section_to_replace
					room_selected['very_asked_room'] = True


def rooms_reservation_percentage(percentage, rooms_structure):

	just_book_room_filter = get_config_property_value(JUST_BOOK_MESSAGE_FILTER)

	if not rooms_structure:
		return

	if isinstance(rooms_structure[0], list):
		_room_multiple_percentage(rooms_structure, just_book_room_filter, percentage)
	else:
		_room_individual_percentage(rooms_structure, just_book_room_filter, percentage)


def _room_multiple_percentage(rooms_structure, just_book_room_filter, percentage):
	for client_room in rooms_structure:
		for rooms_rates in client_room:
			for room_selected in rooms_rates['roomStructure']:
				selected_random = random.random() * 100


				apply = True
				if just_book_room_filter:
					spanishRoomName = room_selected.get('spanishName','')
					for roomNotAlowed in just_book_room_filter.split("@@"):
						if spanishRoomName.lower() in roomNotAlowed.lower():
							apply = False
							break

				if apply and selected_random <= float(percentage):
					room_selected['just_booking_room'] = True


def _room_individual_percentage(rooms_structure, just_book_room_filter, percentage):
	for rooms_rates in rooms_structure:
		for room_selected in rooms_rates['roomStructure']:
			selected_random = random.random() * 100


			apply = True
			if just_book_room_filter:
				spanishRoomName = room_selected.get('spanishName','')
				for roomNotAlowed in just_book_room_filter.split("@@"):
					if spanishRoomName.lower() in roomNotAlowed.lower():
						apply = False
						break

			if apply and selected_random <= float(percentage):
				room_selected['just_booking_room'] = True


def rooms_info_no_dispo(rooms_structure):
	if not rooms_structure:
		return

	if isinstance(rooms_structure[0], list):
		_room_multiple_no_dispo(rooms_structure)
	else:
		_room_individual_no_dispo(rooms_structure)


def _room_multiple_no_dispo(rooms_structure):
	for rooms_rates in rooms_structure:
		if isinstance(rooms_rates, dict):
			for room_selected in rooms_rates['roomStructure']:
				room_selected['onrequest_no_dispo'] = True
		else:
			for room_options in rooms_rates:
				for room_selected in room_options['roomStructure']:
					room_selected['onrequest_no_dispo'] = True


def _room_individual_no_dispo(rooms_structure):
	for rooms_rates in rooms_structure:
		for room_selected in rooms_rates['roomStructure']:
			room_selected['onrequest_no_dispo'] = True


def build_custom_message_by_dates(results, message_config, language, is_mobile=False):
	custom_messages = []
	all_messages = message_config.split("@")
	searched_start = results.get('search', {}).get('startDate')
	searched_end = results.get('search', {}).get('endDate')
	stay = 1
	if searched_start and searched_end:
		search_start_date = datetime.datetime.strptime(searched_start, '%Y-%m-%d')
		search_end_date = datetime.datetime.strptime(searched_end, '%Y-%m-%d')
		if search_start_date and search_end_date:
			stay = (search_end_date - search_start_date).days

	for message in all_messages:
		config_splitted = message.split(';')
		if config_splitted and len(config_splitted) == 2:
			section_name = config_splitted[0]
			dates = config_splitted[1].split(',')
			if dates and (len(dates) in [2,3]):
				start_date = dates[0]
				end_date = dates[1]

				custom_option = None
				if len(dates) == 3:
					custom_option = dates[2]

				if searched_start and searched_end:
					config_start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d')
					config_end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d')

					search_date_ok = ((search_start_date or search_end_date) >= config_start_date) and ((search_start_date or search_end_date) <= config_end_date)
					if custom_option and 'inside_search' in custom_option:
						startDate_inside = search_start_date >= config_start_date and search_start_date < config_end_date
						endDate_inside = search_end_date > config_start_date and search_end_date <= config_end_date
						startConfig_inside = config_start_date >= search_start_date and config_start_date < search_end_date
						endConfig_inside = config_end_date > search_start_date and config_end_date <= search_end_date
						search_date_ok = startDate_inside or endDate_inside or startConfig_inside or endConfig_inside
					elif custom_option and 'always_search' in custom_option:
						search_date_ok = search_start_date <= config_start_date < search_end_date
					if search_date_ok:
						section_params = copy.copy(get_section_from_section_spanish_name(section_name, language))
						section_pictures = get_pictures_from_section_name(section_name, language)
						section_params['pictures'] = [x for x in section_pictures if (not is_mobile and not x.get("onlyInMobile") and not x.get('onlytablet')) or (is_mobile and (x.get('allowInMobile') or x.get('onlytablet')))]

						if section_params:
							already_added = [x for x in custom_messages if x.get('sectionName') == section_params.get('sectionName')]
							if not already_added:
								custom_messages.append(section_params)


	filtered_sections = []
	filtered_priority_sections = []
	for section_element in custom_messages:
		advance_properties = get_properties_for_entity(section_element.get('key'), language)
		if advance_properties.get('filter_country'):
			user_country = results.get('search', {}).get('countryCode')
			if not user_country in advance_properties['filter_country']:
				continue

		if advance_properties.get('filter_promocode'):
			search_promocode = results.get('search', {}).get('promoCode')
			if not '!' in advance_properties['filter_promocode']:
				if not search_promocode or not search_promocode in advance_properties['filter_promocode']:
					continue
			else:
				if search_promocode or (search_promocode and search_promocode in advance_properties['filter_promocode']):
					continue

		if advance_properties.get('jump_to_rooms'):
			section_element['jump_to_rooms'] = True

		if advance_properties.get('no_promocode'):
			search_promocode = results.get('search', {}).get('promoCode')
			if search_promocode:
				continue

		if advance_properties.get('popup'):
			section_element['popup'] = get_section_from_section_spanish_name(advance_properties.get('popup'), language)
			section_element['popup_images'] = get_pictures_from_section_name(str(advance_properties.get('popup')), language)

		if advance_properties.get('promocode'):
			section_element['predefined_promocode'] = advance_properties['promocode']

		if advance_properties.get("link_promocode"):
			section_element['link_promocode'] = True

		if advance_properties.get('delay'):
			section_element['delay'] = advance_properties['delay']

		if advance_properties.get('min_stay'):
			min_stay_config = advance_properties.get('min_stay').split("@@")
			section_element['dont_show'] = build_stay_limit_in_custom_messages(searched_start, searched_end, min_stay_config, 1, stay)

		if advance_properties.get('max_stay'):
			max_stay_config = advance_properties.get('max_stay').split("@@")
			section_element['dont_show'] = build_stay_limit_in_custom_messages(searched_start, searched_end, max_stay_config, 2, stay)

		if advance_properties.get("toggle_content"):
			section_element['toggle_content'] = True

		if advance_properties.get("to_right"):
			section_element['to_right'] = True

		if advance_properties.get("priority_banner"):
			filtered_priority_sections.append(section_element)

		if advance_properties.get("tracking_name"):
			section_element['tracking_name'] = advance_properties['tracking_name']

		filtered_sections.append(section_element)

	if filtered_priority_sections:
		logging.info(f"Building custom message by range of dates {filtered_priority_sections}")
		return filtered_priority_sections

	logging.info(f"Building custom message by range of dates {filtered_sections}")
	return filtered_sections

def build_stay_limit_in_custom_messages(searched_start, searched_end, limit_config, symbol, stay=1):
	result = False
	not_in_limit = False
	limit = int(limit_config[0])

	if (symbol == 1 and limit > stay) or (symbol == 2 and limit < stay):
		not_in_limit = True

	if len(limit_config) == 1 and not_in_limit:
		result = True

	elif len(limit_config) > 1:
		config_dict = {}
		for config_element in limit_config[1].split(";"):
			config_key, config_value = config_element.split("=")
			config_dict[config_key] = config_value

		if config_dict.get("entry"):
			min_stay_date_start = datetime.datetime.strptime(config_dict["entry"], '%Y-%m-%d')
		else:
			now = datetime.datetime.now()
			min_stay_date_start = now.date()

		if config_dict.get("departure"):
			min_stay_date_send = datetime.datetime.strptime(config_dict["departure"], '%Y-%m-%d')
			if not_in_limit and (str(searched_start) > str(min_stay_date_start) and str(searched_end) < str(min_stay_date_send)):
				result = True
		else:
			if not_in_limit and str(searched_start) > str(min_stay_date_start):
				result = True

	return result



def build_booking_cookie_popup(language, booking_popup_config):
	"""
	Will generate popups by cookies
	Syntax: cookie_name=section_name;cookie_name=section_name
	"""

	booking_cookies_popup = {}

	for section_config in booking_popup_config.split(";"):
		cookie_name, section_name = section_config.split("=")
		section_information = get_section_from_section_spanish_name(section_name, language)
		section_data = get_properties_for_entity(section_information.get('key'), language)
		is_enabled = True

		if section_data.get('promocode'):
			section_information['promocode'] = section_data['promocode']

		if section_data.get('search_filter'):
			is_enabled = _booking_cookie_popup_search_filter(section_data['search_filter'])


		if is_enabled:
			booking_cookies_popup[cookie_name] = section_information

	return booking_cookies_popup


def _booking_cookie_popup_search_filter(search_filer):
	'''23-10-2021,30-12-2021;L,M@23-10-2022,30-12-2022;V,S'''
	search_performed = session_manager.get(SEARCH_KEY_PREFIX)
	search_days_format = '%Y-%m-%d'
	config_days_format = '%d-%m-%Y'
	week_days_base = 'L,M,X,J,V,S,D'
	week_days_map = dict([(n_day[1].lower(), n_day[0]) for n_day in enumerate(week_days_base.split(','))])

	can_be_used = False

	if not search_performed:
		return can_be_used

	#Search info
	entry_datetime = datetime.datetime.strptime(search_performed['startDate'], search_days_format)
	departure_datetime = datetime.datetime.strptime(search_performed['endDate'], search_days_format)
	days_between = (departure_datetime - entry_datetime).days
	start_week_day = entry_datetime.weekday()
	week_days_stay = [start_week_day]
	for n in range(start_week_day, days_between + start_week_day - 1):
		if n > 6:
			week_days_stay.append(n - 7)
		else:
			week_days_stay.append(n + 1)

	dates_filter = search_filer
	dates_list = dates_filter.split("@")
	for date_element in dates_list:
		splitted_data = date_element.split(";")
		splitted_dates = splitted_data[0].split(",")
		week_days = week_days_base

		if len(splitted_data) > 1:
			week_days = splitted_data[1]

		week_days_filter = [week_days_map.get(x.lower()) for x in week_days.split(',')]

		#Config info
		start_config_datetime = datetime.datetime.strptime(splitted_dates[0], config_days_format)
		end_config_datetime = datetime.datetime.strptime(splitted_dates[1], config_days_format)

		days_inside_config = [x for x in week_days_stay if x in week_days_filter]

		if entry_datetime >= start_config_datetime and departure_datetime <= end_config_datetime and days_inside_config:
			can_be_used = True

	return can_be_used