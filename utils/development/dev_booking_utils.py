import os

from paraty import Config
from paraty_commons_3.decorators.cache.distributed_strong_cache import disable_cache

# Development configs #

SAVE_BOOKING_AT_DB = False

USE_CACHE = True

# End development configs #

ISOLATED_SERVER = os.environ.get('SERVER_SOFTWARE', 'Development').startswith('IsolatedServer')
DEV = Config.DEV
DEV_USE_REAL_DATASTORE = os.getenv('USE_REAL_DATASTORE')
DATASTORE_DEV = Config.DEV and not DEV_USE_REAL_DATASTORE
DEV_CACHE = True
DEV_SESSIONS = Config.DEV
IS_UNIT_TEST = Config.TESTING

IS_FLEXIBLE_SERVER = os.getenv('GAE_ENV') != 'standard'

DEV_NAMESPACE_2 = 'demo5'
DEV_EMAIL_2 = os.environ.get('developer_email', '<EMAIL>')
DEV_TEMPLATE_TRACEBACK_2 = True
DEV_APPLICATION_ID_2 = "s~secure-mexico"


REMOTE_PREFIX = "r__"
DEV_NAMESPACE = Config.NAMESPACE if DEV else ''
DEV_APPLICATION_ID = DEV_APPLICATION_ID_2
DEV_EMAIL = DEV_EMAIL_2
DEV_TEMPLATE_TRACEBACK = DEV_TEMPLATE_TRACEBACK_2
DEV_AVOID_CIPHER = True
DEV_GA = ''
DEV_GA4 = ''
DEV_GTM_ID = ''


# External projects
# Used for development when you need to use a local-running external project instead of production one
# (ex. hotel-webs, popup-seeker, loyalty-seeker, etc)
DEV_USE_PYTHON2_STATIC = False
DEV_USE_LOCAL_HOTEL_WEBS = False
DEV_USE_LOCAL_POPUP_SEEKER = False
DEV_USE_LOCAL_WEB_SEEKER = False
DEV_USE_LOCAL_LOYALTY_SEEKER = False
DEV_USE_LOCAL_PREBOOKING_SEEKER = False
DEV_USE_LOCAL_HOTEL_MANAGER_2 = False
DEV_USE_LOCAL_COBRADOR = False
DEV_USE_LOCAL_ADDITIONAL_SERVICES_SEEKER = False

# Max minutes for the data available to be considered valid
# i.e. if we set 24*60, any data older than 1 day will be discarded
#      from the cache and new data will be retrieved from the datastore (slower)
# 0 if you want the latest
DEV_DATA_EXPIRATION_MINUTES = 24 * 7 * 60


if not USE_CACHE:
    disable_cache()
