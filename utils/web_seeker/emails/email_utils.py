import logging

import requests
from flask import request

from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.email.email_utils_third_party import notify_exception
from booking_process.utils.namespaces.namespace_utils import get_hotel_code
from booking_process.utils.web_seeker.emails.email_constants import EMAILS_RENDER_PATH, EMAIL_CONFIGS_BY_TYPE
from booking_process.utils.web_seeker.utils import get_nearest_web_seeker_render_module
from utils.managers_cache.manager_cache import managers_cache


def build_email_render_url(email_type, language):
    hotel_code = get_hotel_code()

    web_seeker_url = get_nearest_web_seeker_render_module(hotel_code)

    render_path = EMAILS_RENDER_PATH.replace('@@hotel_code@@', hotel_code).replace("@@email_type@@", email_type)

    return '%s%s?language=%s' % (web_seeker_url, render_path, language)


def get_email_configuration_by_type(email_type, language):
    target_config = EMAIL_CONFIGS_BY_TYPE.get(email_type)

    if target_config:
        section_name = get_config_property_value(target_config)
        config_section = get_section_from_section_spanish_name(section_name, language)
        config_section.update(get_properties_for_entity(config_section.get('key', False), language))
        return config_section
    else:
        logging.warning('Unknown configuration name for email type: %s' % email_type)
        return {}


# @failover_cache(key_generator=lambda x: "get_email_template_from_ws_failover_%s_%s" % (x[0], x[1]), json_serializable=False)
# @cache_on_datastore(lambda x: "get_email_template_from_ws_%s_%s" % (x[0], x[1]), jsonSerializable=True, tags="Emails")
@managers_cache(ttl_seconds=3600*4, entities='WebConfiguration')
def get_email_template_from_ws(email_type, language):
    render_url = build_email_render_url(email_type, language)
    logging.info("Calling: %s", render_url)

    response = requests.get(render_url, timeout=60)
    status_code = response.status_code
    content = response.content.decode("utf8")

    if status_code == 200:
        return content
    else:
        url = request.url if request else 'Unknown'
        logging.error("Error getting email template from web seeker")
        logging.error("Request url: %s" % url)
        logging.error("Render url: %s" % render_url)
        logging.error("Response status code: %s" % status_code)
        logging.error("Response content: %s" % content)
        notify_exception('Error getting email template from web seeker',
                         'Request url: %s <br> '
                         'Render url: %s <br> '
                         'Response status code: %s <br> '
                         'Response content: <br> %s' % (url, render_url, status_code, content), add_hotel_info=True)

        raise Exception('Error getting email template from web seeker')
