import logging

from flask import request

from booking_process.constants.advance_configs_names import CUSTOM_DOMAIN, KEY_DOMAIN
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.development.dev_booking_utils import DEV
from booking_process.utils.namespaces.namespace_utils import get_namespace, get_application_id, set_namespace, \
    set_application_id, get_all_namespaces
from booking_process.utils.session import session_manager
from paraty import Config
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels


def retreive_namespace_automatically():
    host = request.host

    if request.values.get('namespace'):
        return request.values.get('namespace')

    if DEV:
        logging.info("Dev server namespace: %s" % Config.NAMESPACE)
        return Config.NAMESPACE

    # Try to retreive namespace from url first
    if 'appspot' in host:
        if not '-dot-' in host:
            # Not multitenancy application
            return ''

        splitted_host = host.split("-dot-")
        target_namespace = splitted_host[0].replace('http://', '').replace('https://', '')

        all_hotels = get_all_hotels()
        enabled_hotels = [x.get('applicationId') for x in list(all_hotels.values()) if x.get('enabled')]

        if target_namespace in get_all_namespaces() and target_namespace in enabled_hotels:
            logging.info("Retreiving namespace from host url")
            return target_namespace

    current_namespace = get_namespace()
    current_application_id = get_application_id()

    for namespace_element in get_all_namespaces():
        try:
            set_namespace(namespace_element.key.name)

            booking_domain = get_config_property_value(CUSTOM_DOMAIN)
            if host in booking_domain:
                logging.info("Using namespace with match of Dominio Booking")
                return namespace_element

            default_domain = get_config_property_value(KEY_DOMAIN)
            if host in default_domain:
                logging.info("Using namespace with match of Dominio Asociado")
                return namespace_element

        except:
            pass

    set_namespace(current_namespace)
    set_application_id(current_application_id)

    return None
