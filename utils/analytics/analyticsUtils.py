# import requests
# import html.parser
# import http.client
import html
import json
import logging
import re
import time
import urllib
# import urllib.request, urllib.error, urllib.parse
from datetime import datetime

from flask import request, Request

from booking_process.constants.advance_configs_names import EXTRA_PARAMS_CAMPAIGN_URL, PAY_TAX_AT_HOTEL, \
	DERBYSOFT_TRACKING
from booking_process.constants.web_configs_names import GA_CUSTOM_DIMENSIONS, COOKIEBOT_BY_DOMAIN, GOOGLE_TAG_MANAGER
from bs4 import BeautifulSoup

# TODO migrate, fmatheis
# from google.appengine._internal.django.utils.safestring import SafeUnicode

from booking_process.constants import advance_configs_names
from booking_process.libs.communication import directDataProvider
from booking_process.libs.pasarelas.gateway_constants import FINAL_IDENTIFIER_BUILT
from booking_process.constants import session_data
from booking_process.utils.analytics.analytics_constants import ANALYTICS_CAMPAIGN_PARAMS, FIRST_CAMPAIGN_INTERACTION, \
	LAST_CAMPAIGN_INTERACTION, UTM_SOURCE
from booking_process.utils.auditing import auditUtils
from booking_process.utils.bookingConstants import TRIVAGO_PARTNER_ID
from booking_process.constants.session_data import ANALYTICS_TEMPLATE, SOURCE, AGENT_ID, \
	ANALYTICS_TEMPLATE_TO_RECOVER, DS_CLICK_ID, ANALYTICS_CAMPAIGNS_HISTORY, BOOKING_STEP
from booking_process.utils.callcenter.callcenter_utils import is_call_center
from booking_process.utils.country.configs.country_configs_utils import CountryConfigsBuilder
from booking_process.utils.currency.currencyUtils import get_currency, convert_symbol_to_code
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.data_management.hotel_data import get_hotel_name, get_internal_url
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.development.dev_booking_utils import DEV, DEV_NAMESPACE, DEV_GA, DEV_GTM_ID, IS_UNIT_TEST
from booking_process.utils.email.email_utils_third_party import notify_exception
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_language_code
from booking_process.utils.language.language_utils import get_web_dictionary
from booking_process.utils.manager_communicator.constants import MANAGER_SOURCE_CALL_CENTER
from booking_process.utils.namespaces import namespace_utils
from booking_process.utils.namespaces.namespace_utils import get_namespace
from booking_process.utils.namespaces.namespace_utils import get_hotel_code
from booking_process.utils.packages.package_utils import PACKAGE_PREFIX, PACKAGE_SEPARATOR
from booking_process.utils.request.request_utils import get_cookies, user_agent_pagespeed
from booking_process.utils.security.security_utils import decompress_and_decode
from booking_process.utils.templates.template_utils import buildTemplate, build_template_2

from booking_process.utils.session import session_manager
from booking_process.utils.session.session_manager import get_session_id
from booking_process.utils.users.users_methods import club_is_active, check_if_user_exists
from paraty_commons_3.security_utils import hash_password
from paraty_commons_3.utils.country.country_utils import get_prefix_country_list
from utils.redis.redis_utils import RedisCache
from utils.managers_cache.manager_cache import managers_cache


def retreive_ads_account(config_value):
	paraty_account = 'AW-*********'
	if not config_value:
		return paraty_account

	splitted_value = config_value.split("/")
	if len(splitted_value) != 2:
		actual_namespace = namespace_utils.get_namespace()
		actual_appid = namespace_utils.get_application_id()
		notify_exception('[SEM] Ads account wrong configuration',
											  'The configuration <b>Google ads conversion id</b> is not configured correctly<br>'
											  '%s [%s]' % (actual_namespace, actual_appid))
		return None

	ads_account, conversion_label = config_value.split("/")
	return ads_account


# def google_ads_general_tag(google_ads_id=None):
# 	if not google_ads_id:
# 		google_ads_id = get_config_property_value(advance_configs_names.GOOGLE_ADS_CONVERSION_LABEL)
#
# 	target_ads_account = retreive_ads_account(google_ads_id)
# 	if not target_ads_account:
# 		return ''
#
# 	context = {
# 		'account': target_ads_account
# 	}
#
# 	if google_ads_id:
# 		return buildTemplate('general/analytics/google_ads_general.html', context, allowMobile=False)
#
# 	return ''

def _get_specific_pixels_by_source(reservation):

	source = session_manager.get(SOURCE)
	if source and source.lower() == "trivago":
		return _build_trivago_pixel(reservation)

	return ""

def _build_trivago_pixel(reservation):
	'''
	Pixel example:
	<img height="1" width="1" style="border-style:none;" alt="" src="https://secde.trivago.com/page_check.php?pagetype=track&ref=12345&hotel=HOTEL537&arrival=**********&departure=**********&currency=USD&volume=150.70&booking_id=AC500&margin=14.5"/>

	'''

	try:
		paraty_hotel_code = namespace_utils.get_application_id()
		if namespace_utils.get_namespace():
			paraty_hotel_code = namespace_utils.get_namespace()

		start_date_ts = str(int(time.mktime(datetime.strptime(reservation.startDate, "%Y-%m-%d").timetuple())))
		end_date_ts = str(int(time.mktime(datetime.strptime(reservation.endDate, "%Y-%m-%d").timetuple())))

		taxes_and_price = round((float(reservation.price) + float(reservation.priceSupplements)), 2)
		#dont worry about taxes. Reservation has already added

		currency = get_currency()

		base_pixel = '<img height="1" width="1" style="border-style:none;" alt="" src="https://secde.trivago.com/page_check.php?pagetype=track&ref=@@PARATY_ID@@&hotel=@@HOTEL_CODE@@&arrival=@@ARRIVAL@@&departure=@@DEPARTURE@@&currency=@@CURRENCY@@&volume=@@TOTAL_PRICE@@&booking_id=@@IDENTIFIER@@"/>'

		base_pixel = base_pixel.replace("@@PARATY_ID@@", TRIVAGO_PARTNER_ID)
		base_pixel = base_pixel.replace("@@HOTEL_CODE@@", paraty_hotel_code)
		base_pixel = base_pixel.replace("@@ARRIVAL@@", start_date_ts)
		base_pixel = base_pixel.replace("@@DEPARTURE@@", end_date_ts)
		base_pixel = base_pixel.replace("@@CURRENCY@@", currency)
		base_pixel = base_pixel.replace("@@TOTAL_PRICE@@", str(taxes_and_price))
		base_pixel = base_pixel.replace("@@IDENTIFIER@@", reservation.identifier)

		logging.info("Trivago reservation. Pixel added: %s", base_pixel)

		return base_pixel
	except Exception as e:
		logging.error("Exception building Trivago Pixel: %s", e)
		sendEmailExceptionFromTracking("[Analytics] Exception building Trivago Pixel")
		return ""


def sendEmailExceptionFromTracking(emailSubject):
	message = auditUtils.makeTraceback()
	logging.exception(message)
	notify_exception(emailSubject, message, add_hotel_info=True)


def remarketingTrackingCode():
	"""
	Generate Remarketing tracking code
	"""
	remarketing_tracking = get_config_property_value(advance_configs_names.ADWORDS_REMARKETING)
	tracking_code = None
	if remarketing_tracking:
		trackingParams = {}
		try:
			trackingParams['remarketingID'] = remarketing_tracking
			tracking_code = buildTemplate('booking/adwords/remarketingCode.html', trackingParams, allowMobile=False)

		except Exception as e:
			sendEmailExceptionFromTracking("[Analytics] Exception obtaining Adwords Remarketing")

	return tracking_code if tracking_code else ''


def adword_tracking_template(adwords_tracking, analytics_id, reservation, price, roomName, rate_name):
	try:
		identifier = ''
		if reservation:
			identifier = reservation.identifier

		ga_ua_id = analytics_id
		params_list = []
		singles_adwords_list = adwords_tracking.split("@@")

		taxes = 0
		taxes_and_price = price

		country_location_config = get_config_property_value(advance_configs_names.HOTEL_COUNTRY_LOCATION)
		hotel_country_location_without_taxes = get_config_property_value(advance_configs_names.HOTEL_COUNTRY_LOCATION_TAX_INCLUDED)
		tax_to_be_paid_at_hotel = get_config_property_value(PAY_TAX_AT_HOTEL)

		# TODO: All this should be at a method called 'is_tax_needed_to_be_paid_at_booking'
		if country_location_config and not hotel_country_location_without_taxes and not tax_to_be_paid_at_hotel:
			country_info = CountryConfigsBuilder(country_location_config, SPANISH)
			tax_percentage = country_info.tax_rate
			tax_value = float(tax_percentage.replace("%", ""))
			tax_calculation = lambda x: x * (float(100 + tax_value) / float(100))
			taxes_and_price = tax_calculation(price)
			taxes = tax_calculation(price) - price

		xml_configs_dict = [x.to_dict() for x in directDataProvider.get('IntegrationConfiguration')]
		matched_xml_configs = [x for x in xml_configs_dict if x.get('name') == 'adword_tracking']
		adword_tracking_options = {}
		if matched_xml_configs:
			adword_tracking_options = dict([x.split(" @@ ") for x in matched_xml_configs[0].get('configurations', '')])

		integration_name = get_config_property_value(advance_configs_names.INTEGRATION_NAME_FOR_ADWORDS)
		integration_room_map = {}
		if integration_name:
			matched_xml_configs = [x for x in xml_configs_dict if x.get('name') == integration_name]
			if matched_xml_configs:
				integration_room_map = dict([x.split(" @@ ") for x in matched_xml_configs[0].get('roomMap', '')])

		available_info = {
			'ga_ua_id': ga_ua_id,
			'id': identifier,
			'amount': "%0.2f" % price,
			'shipping': 0,
			'tax': taxes,
			'currency': 'EUR',
			'name': roomName,
			'sku': 'ITEM' + identifier,
			'category': 'Room reservation' if not rate_name else rate_name,
			'trackerName': 'MyEcommerceTracker',
			'taxes_and_price': taxes_and_price,
			'date': datetime.now()
		}

		if integration_room_map:
			available_info['id_paquete'] = integration_room_map.get(reservation.roomType1)

		for single_adword in singles_adwords_list:
			affiliation = single_adword
			params = {
				'ga_ua_id': ga_ua_id,
				'id': identifier,
				'affiliation': affiliation,
				'amount': "%0.2f" % price,
				'shipping': 0,
				'tax': taxes,
				'currency': 'EUR',
				'name' : roomName,
				'sku': 'ITEM' + identifier,
				'category': 'Room reservation' if not rate_name else rate_name,
				'trackerName': 'MyEcommerceTracker',
				'taxes_and_price': taxes_and_price
			}

			dict_to_overwrite = {}

			for key_element, value_element in list(adword_tracking_options.items()):
				for i, j in list(available_info.items()):
					value_element = value_element.replace("@@" + str(i) + "@@", str(j))

				if "date|" in value_element:
					date_key, date_format = value_element.split("|")
					dict_to_overwrite[key_element] = value_element.replace("@@date|" + date_format, available_info.get('date').strftime(date_format.replace("@@", "")))
				else:
					dict_to_overwrite[key_element] = value_element

			params.update(dict_to_overwrite)

			params_list.append(params)

		logging.info("Event Trackin e-Commerce %s", params_list)

		return buildTemplate('booking/adwords/adwords_event_tracking.html', {"adwordEvents": params_list})

	except Exception as e:
		sendEmailExceptionFromTracking("[Analytics] Exception obtaining AdwordTrackins")
		return ''


def default_analytics_ecommerce(analytics_id, reservation, rooms_names, gtag=False, custom_template=None):
	try:
		ga_ua_id = analytics_id if not DEV or IS_UNIT_TEST else DEV_GA

		taxes = 0
		taxes_and_price = '%.2f' % float((float(reservation.price) + float(reservation.priceSupplements)))
		taxes_and_price = float(taxes_and_price)

		rate_name = _get_spanish_rate_name(reservation)

		hotel_name = get_config_property_value(advance_configs_names.EMAIL_SENDER)
		hotel_name = hotel_name.split("-")[0].strip()

		country_location_config = get_config_property_value(advance_configs_names.HOTEL_COUNTRY_LOCATION)
		hotel_country_location_without_taxes = get_config_property_value(advance_configs_names.HOTEL_COUNTRY_LOCATION_TAX_INCLUDED)
		tax_to_be_paid_at_hotel = get_config_property_value(PAY_TAX_AT_HOTEL)

		# TODO: All this should be at a method called 'is_tax_needed_to_be_paid_at_booking'
		if country_location_config and not hotel_country_location_without_taxes and not tax_to_be_paid_at_hotel:
			country_info = CountryConfigsBuilder(country_location_config, SPANISH)
			tax_percentage = country_info.tax_rate
			tax_value = float(tax_percentage.replace("%", ""))
			tax_calculation = lambda x: x * (float(100 + tax_value) / float(100))
			taxes_and_price = tax_calculation(float(taxes_and_price))
			taxes = tax_calculation(taxes_and_price) - taxes_and_price

		actual_namespace = namespace_utils.get_namespace()

		xml_configs_dict = [x.to_dict() for x in directDataProvider.get('IntegrationConfiguration')]
		matched_xml_configs = [x for x in xml_configs_dict if x.get('name') == 'analytics_ecommerce']
		analytics_ecommerce_options = {}
		if matched_xml_configs:
			analytics_ecommerce_options = dict([x.split(" @@ ") for x in matched_xml_configs[0].get('configurations', '')])

		if DEV:
			namespace_utils.set_namespace(actual_namespace)

		available_info = {
			'identifier': reservation.identifier,
			'room_name': rooms_names,
			'rooms_number': reservation.numRooms,
			'start_date': reservation.startDate,
			'end_date': reservation.endDate,
			'nights_number': reservation.nights
		}

		#Default Params
		params = {
			'ga_ua_id': ga_ua_id,
			'id': reservation.identifier,
			'amount': "%0.2f" % float(taxes_and_price),
			'total': "%0.2f" % float(taxes_and_price),
			'shipping': 0,
			'tax': "%0.2f" % taxes,
			'currency': get_currency(),
			'name': unescape(rooms_names),
			'sku': 'ITEM' + reservation.identifier,
			'category': 'Room reservation' if not rate_name else rate_name,
			'trackerName': 'MyEcommerceTracker',
			'affiliation': hotel_name,
			'quantity': 1,
			'nights_number': available_info.get('nights_number'),
			'hotel_name': get_hotel_name(),
			'google_analytics_id': ga_ua_id.split(";"),
			'promocode': reservation.promocode
		}


		#Overwriten params
		dict_to_overwrite = {}

		for key_element, value_element in list(analytics_ecommerce_options.items()):
			for replacement_key, replacement_value in list(available_info.items()):
				value_element = value_element.replace("@@" + str(replacement_key) + "@@", str(replacement_value))

			if '/' in key_element:
				parent, key_name = key_element.split("/")
				dict_to_overwrite.setdefault(parent, {})[key_name] = value_element
			else:
				dict_to_overwrite[key_element] = value_element

		params.update(dict_to_overwrite)

		#Prices recalculation
		params['amount'] = "%0.2f" % (float(params['amount']) / int(params['quantity']))
		params['tax'] = "%0.2f" % (float(params['tax']) / int(params['quantity']))

		params['only_ecommerce'] = get_config_property_value(advance_configs_names.ANAlYTICS_ONLY_ECOMMERCE)
		params['ga_ua_id'] = params['only_ecommerce']

		logging.info("Google Analytics e-Commerce %s", params)

		target_template = 'booking/adwords/analytics_ecommerce.html'
		if gtag:
			params['currency'] = convert_symbol_to_code(params['currency'])
			target_template = 'booking/adwords/gtag_analytics_purchase.html'

		if custom_template:
			target_template = custom_template

		return buildTemplate(target_template, {
			"analytics_ecommerce": params,
			"is_call_center": session_manager.get(AGENT_ID)
		})

	except Exception as e:
		logging.error(e)
		sendEmailExceptionFromTracking("[Analytics] Exception obtaining AdwordTrackins")
		return ''


def standard_adword_template(standardAdwords, price, language):
	try:
		params_list = []

		adwords_price = price
		country_location_config = get_config_property_value(advance_configs_names.HOTEL_COUNTRY_LOCATION)
		hotel_country_location_without_taxes = get_config_property_value(advance_configs_names.HOTEL_COUNTRY_LOCATION_TAX_INCLUDED)
		tax_to_be_paid_at_hotel = get_config_property_value(PAY_TAX_AT_HOTEL)

		# TODO: All this should be at a method called 'is_tax_needed_to_be_paid_at_booking'
		if country_location_config and not hotel_country_location_without_taxes and not tax_to_be_paid_at_hotel:
			country_info = CountryConfigsBuilder(country_location_config, SPANISH)
			tax_percentage = country_info.tax_rate
			tax_value = float(tax_percentage.replace("%", ""))
			tax_calculation = lambda x: x * (float(100 + tax_value) / float(100))
			adwords_price = tax_calculation(price)

		singles_adwords_list = standardAdwords.split("@@")
		for single_adword in singles_adwords_list:
			id, label = single_adword.split(";")
			params = {
				'id': id,
				'label': label,
				'amount': "%0.2f" % adwords_price,
				'language_code': get_language_code(language)
			}

			params_list.append(params)

		return buildTemplate('booking/adwords/adwords.html', {"adwords": params_list})

	except Exception as e:
		sendEmailExceptionFromTracking("[Analytics] Exception obtaining adwords")
		return ''


def adwords_conversion_template(adwords, price, language):
	try:
		params_list = []

		adwords_price = price
		country_location_config = get_config_property_value(advance_configs_names.HOTEL_COUNTRY_LOCATION)
		hotel_country_location_without_taxes = get_config_property_value(advance_configs_names.HOTEL_COUNTRY_LOCATION_TAX_INCLUDED)
		tax_to_be_paid_at_hotel = get_config_property_value(PAY_TAX_AT_HOTEL)

		# TODO: All this should be at a method called 'is_tax_needed_to_be_paid_at_booking'
		if country_location_config and not hotel_country_location_without_taxes and not tax_to_be_paid_at_hotel:
			country_info = CountryConfigsBuilder(country_location_config, SPANISH)
			tax_percentage = country_info.tax_rate
			tax_value = float(tax_percentage.replace("%", ""))
			tax_calculation = lambda x: x * (float(100 + tax_value) / float(100))
			adwords_price = tax_calculation(price)

		singles_adwords_list = adwords.split("@@")
		for single_adword in singles_adwords_list:
			id, label = single_adword.split(";")
			params = {
				'id': id,
				'label': label,
				'amount': "%0.2f" % adwords_price,
				'language_code': get_language_code(language)
			}

			params_list.append(params)

		return buildTemplate('booking/adwords/adwordsAsync.html', {"adwords": params_list})

	except Exception as e:
		sendEmailExceptionFromTracking("[Analytics] Exception obtaining adwordsAsync")
		return ''

def facebook_pixel_template(facebook_pixel_id, price, roomName):
	try:
		default_language = get_config_property_value(advance_configs_names.DEFAULT_WEB_LANGUAGE)
		if not default_language:
			default_language = "SPANISH"

		params = {
			'facebook_pixel_id': facebook_pixel_id,
			'amount': "%0.2f" % price,
			'roomName': roomName,
			'name_pixel': get_web_dictionary(default_language)['T_pixel_facebook_purchase']
		}

		return buildTemplate('booking/adwords/facebook_pixel.html',params)

	except Exception as e:
		sendEmailExceptionFromTracking("[Analytics] Exception obtaining Facebook Pixel Id")
		return ''


def retreive_facebook_pixel_id_info(facebook_pixel_config, step_needed='deafult'):
	return_dict = {}

	#Default config
	splitted_config = facebook_pixel_config.split("||")
	default_config = splitted_config[0]
	return_dict['pixel_ids'] = default_config.split("@")


	steps_tracks = {'default': 'PageView'}

	if len(splitted_config) > 1:
		steps_tracks.update(dict([x.split("=") for x in splitted_config[1:]]))

	return_dict['track'] = steps_tracks.get(step_needed, 'PageView')

	return return_dict



def tracking_company_template(price, identifier, numNights, numRooms):
	try:
		partnerReference = namespace_utils.get_application_id()
		if namespace_utils.get_namespace():
			partnerReference = namespace_utils.get_namespace()

		myshophotel_params = {'revenue': "%0.2f" % price,
							  'currency': 'EUR',
							  'reservationnumber': identifier,
							  'roomnights': numNights,
							  'rooms': numRooms,
							  'partnerReference': partnerReference
							}
		return buildTemplate('booking/adwords/myshophotel_tracking.html', myshophotel_params)

	except Exception as e:
		sendEmailExceptionFromTracking("[Analytics] Exception obtaining myshophotel_tracking")
		return ''


def merchant_center_survey_template(merchant_id, reservation):
	try:
		context = {
			'merchant_id': merchant_id,
			'user_email': reservation.email,
			'order_id': reservation.identifier,
			'date_to_send': reservation.endDate,
			'country_code': reservation.country
		}

		return buildTemplate('booking/adwords/merchant_center_survey.html', context, allowMobile=False)
	except Exception as e:
		sendEmailExceptionFromTracking("[Analytics] Exception obtaining merchant_center_survey")
		return ''


def _get_spanish_rate_name(reservation):
	rate_key = reservation.rate
	if rate_key:
		if PACKAGE_PREFIX in rate_key:
			rate_key = rate_key.split(PACKAGE_SEPARATOR)[2]
		selected_rate = directDataProvider.getEntity('Rate', rate_key)
		if selected_rate:
			return selected_rate.localName

	return None


def _make_extra_content_replacement(content, identifier, price, reservation, roomName):
	content = content.replace('@@booking_id@@', identifier)

	price_format = "%0.2f" % price
	modified_content = content.replace('@@booking_amount@@', price_format)

	modified_content = modified_content.replace('@@email@@', reservation.email)

	checkout_date = reservation.endDate
	modified_content = modified_content.replace('@@checkout_date@@', checkout_date)

	start_date = reservation.startDate
	modified_content = modified_content.replace('@@start_date@@', start_date)

	if roomName:
		modified_content = modified_content.replace('@@room_name@@', roomName)

	modified_content = modified_content.replace('@@booking_id@@', identifier)

	hotel_name = get_config_property_value(advance_configs_names.EMAIL_SENDER)
	if hotel_name and '-' in hotel_name:
		hotel_name = hotel_name.split("-")[0]

	modified_content = modified_content.replace('@@hotel_name@@', hotel_name)

	total_adults = sum([reservation.adults1, reservation.adults2, reservation.adults3])
	modified_content = modified_content.replace("@@adults_number@@", str(total_adults))

	total_children = sum([reservation.kids1, reservation.kids2, reservation.kids3])
	modified_content = modified_content.replace("@@children_number@@", str(total_children))

	modified_content = modified_content.replace("@@rooms_number@@", str(reservation.numRooms))

	currency = get_currency()
	tax_rate = 0.21
	# dont worry about taxes. Reservation has already added
	hotel_country_location = get_config_property_value(advance_configs_names.HOTEL_COUNTRY_LOCATION)
	if hotel_country_location:
		country_configs = CountryConfigsBuilder(hotel_country_location)
		tax_rate = float(country_configs.tax_rate.replace("%", "")) / 100.0

	price_without_tax = "%0.2f" % (float(price) * (1.0 - tax_rate))
	modified_content = modified_content.replace("@@booking_amount_no_tax@@", price_without_tax)
	modified_content = modified_content.replace("@@nights@@", str(reservation.nights))

	modified_content = modified_content.replace("@@currency@@", currency)

	# Replace needed to avoid issue of other dpts when is a copy/paste
	modified_content = modified_content.replace("&nbsp;", ' ')

	return modified_content


def analytics_build_confirmation(booking_result, is_mobile, recover_analytics=False):
	booking_result_soup = None
	finished_analytics_results = None
	if recover_analytics:
		logging.info("Recovering analytics")

	analytics_html = session_manager.get(ANALYTICS_TEMPLATE) if not recover_analytics else session_manager.get(ANALYTICS_TEMPLATE_TO_RECOVER)
	booking_analytics_triggered = analytics_tracking_code_already_fired(session_manager.get(FINAL_IDENTIFIER_BUILT))
	try:
		if analytics_html and (not booking_analytics_triggered or recover_analytics):
			if not booking_result:
				logging.warning("[Analytics] Booking content is empty")
				logging.info(booking_result)
				return None, False

			logging.info(f"Will use the following analytics html:\n{analytics_html}")
			# Fix for issues with non closed br tags
			booking_result = booking_result.replace("<br>", "<br/>")
			booking_result_soup = BeautifulSoup(booking_result, "html.parser")
			if not is_mobile:
				logging.info("Not is mobile")
				confirmation_soup_head = booking_result_soup.find("div", {"id": "wizard"})
				if not confirmation_soup_head:
					confirmation_soup_head = booking_result_soup.find("div", {"class": "booking4"})

				if not confirmation_soup_head:
					logging.warning("[Analytics] Booking content has no booking4 identifier")
					logging.info(booking_result)
					return None, False

				confirmation_soup_head.insert_after(analytics_html)
				booking_result_soup = html.unescape(str(booking_result_soup))
			else:
				logging.info("Is mobile")
				confirmation_soup_head = booking_result_soup.find("div", {"class": "booking4"})
				if not confirmation_soup_head:
					confirmation_soup_head = booking_result_soup.find("div", {"id": "wizard"})

				if not confirmation_soup_head:
					logging.warning("[Analytics] Booking content has no booking4 identifier")
					logging.info(booking_result)
					return None, False

				confirmation_soup_head.insert_after(unescape(analytics_html))
				booking_result_soup = unescape(str(booking_result_soup))

			finished_analytics_results = True

	except Exception as e:
		message = auditUtils.makeTraceback()
		logging.error(message)
		logging.error(booking_result)
		logging.error("Exception at booking4, failed to build analytics tracking codes")
		notify_exception("Exception at booking4, failed to build analytics tracking codes", message, add_hotel_info=True)

	return booking_result_soup, finished_analytics_results


def get_gtm_configs():
	"""
	This contains all available options with GTM
	- environment
	- tag_id
	"""
	return get_web_configuration(GOOGLE_TAG_MANAGER)


def get_gtm_tag_id(request_data=None):
	gtm_id = get_config_property_value(advance_configs_names.GOOGLE_TAG_MANAGER_ID) if not DEV or IS_UNIT_TEST else DEV_GTM_ID

	if request and user_agent_pagespeed():
		return ''

	if gtm_id:
		gtm_id_list = gtm_id.split(';')
		for index, id in enumerate(gtm_id_list):
			if '@' in id:
				splitted_configs = id.split("@")
				splitted_configs = [x.split("=") for x in splitted_configs]
				new_id = [x for x in splitted_configs if len(x) == 1][0][0]
				urls_configs = [x for x in splitted_configs if not(len(x) == 1)]

				if request and urls_configs:
					host = request.host
					urls_configs = [x for x in urls_configs if x[0] in host]
					if urls_configs:
						new_id = urls_configs[0][1]
				gtm_id_list[index] = new_id

		return gtm_id_list

def concatenate_analytics_campaign_url(request, only_confirmation=False):
	available_params = ANALYTICS_CAMPAIGN_PARAMS
	confirmation_params = ['confirmation_utm_source', 'confirmation_utm_medium', 'confirmation_utm_campaign', 'confirmation_utm_term', 'confirmation_utm_content']

	extra_params_campaign = get_config_property_value(EXTRA_PARAMS_CAMPAIGN_URL)
	if extra_params_campaign:
		available_params.extend(extra_params_campaign.split("@@"))

	params_builded = ''
	if not only_confirmation:
		for param_element in available_params:
			if request.values.get(param_element):
				try:
					params_to_add = '&' + param_element + '=' + str(request.values.get(param_element))
				except Exception as e:
					logging.warning(auditUtils.makeTraceback())
					logging.warning("encoding param with problem to: %s", urllib.parse.quote(request.values.get(param_element).encode('utf-8')))
					params_to_add = '&' + param_element + '=' + urllib.parse.quote(request.values.get(param_element).encode('utf-8'))
				params_builded += params_to_add

	confirmation_campaign_session = session_manager.get(session_data.CONFIRMATION_CAMPAIGN)
	if not confirmation_campaign_session:
		confirmation_campaign_session = []
		for confirmation_element in confirmation_params:
			if request.values.get(confirmation_element):
				confirmation_campaign_session.append((confirmation_element, request.values.get(confirmation_element)))

		if confirmation_campaign_session:
			session_manager.set(session_data.CONFIRMATION_CAMPAIGN, confirmation_campaign_session)

	else:
		for confirmation_param in confirmation_campaign_session:
			params_to_add = '&' + confirmation_param[0].replace('confirmation_', '') + '=' + confirmation_param[1]
			params_builded += params_to_add

	# Used to maintain at all booking process the utm campaigns
	if get_config_property_value(advance_configs_names.CAMPAIGN_TRACKING_AT_ALL_PROCESS) or session_manager.get(AGENT_ID):
		if params_builded:
			session_manager.set(session_data.REDIRECTIONS_ANALYTICS_URL, params_builded)
		else:
			redirection_session_info = session_manager.get(session_data.REDIRECTIONS_ANALYTICS_URL)
			if redirection_session_info:
				return redirection_session_info


	# Used to mainatin at all booking process
	if get_config_property_value(advance_configs_names.GCLID_AT_ALL_PROCESS):
		gclid_info = session_manager.get(session_data.GCLID_INSESS)
		if gclid_info:
			params_builded += '&gclid=%s' % gclid_info

	return params_builded


def save_campaign_in_session():
	booking_step = session_manager.get(BOOKING_STEP) or 0
	if analytics_campaigns_history := get_analytics_campaigns_history():
		logging.info(f"[Booking {booking_step}] Saving analytics campaigns history in session: {analytics_campaigns_history}")
		session_manager.set(ANALYTICS_CAMPAIGNS_HISTORY, analytics_campaigns_history)

	if analytics_campaigns := get_analytics_campaigns():
		logging.info(f"[Booking {booking_step}] Saving campaign in session: {analytics_campaigns}")
		session_manager.set(session_data.ANALYTICS_CAMPAIGN, analytics_campaigns)


def build_hijiffy_script(hijiffy_api, language):
	hijiffy_code = hijiffy_api.split("@")[0]
	args = {
		"language": get_language_code(language),
		'hijiffy_code': hijiffy_code
	}
	return build_template_2("scripts/_hijiffy_script.html", args, False)


def build_hijiffy_pixel(hijiffy_api, reservation):
	args = {
		"amount": reservation.price,
		"currency": "EUR",
		"namespace": get_namespace(),
		"reservation_id": reservation.identifier,
		"checkin": reservation.startDate.replace("-", ""),
		"checkout": reservation.endDate.replace("-", "")
	}
	if '@@' in hijiffy_api:
		hijiffy_split = hijiffy_api.split("@@")
	else:
		hijiffy_split = hijiffy_api.split("@")

	if len(hijiffy_split) == 2:
		args['hijiffy_id'] = hijiffy_split[1]

	else:
		args['hijiffy_id'] = "277"

	return build_template_2("scripts/_hijiffy_pixel.html", args, False)


def build_bing_conversion(bing_id, reservation):
	args = {
		"amount": reservation.price,
		"currency": "EUR",
		'bing_id': bing_id
	}

	return buildTemplate('general/analytics/bing_conversion.html', args, allowMobile=False)


def get_ga_custom_dimensions():
	custom_dimensions = get_web_configuration(GA_CUSTOM_DIMENSIONS)
	predefined_values = {
		'@@namespace@@': DEV_NAMESPACE if DEV else get_namespace(),
		'@@agent_id@@': 'call_center' if session_manager.get(AGENT_ID) else 'user'
	}

	for key, value in list(custom_dimensions.items()):
		if value in predefined_values:
			custom_dimensions[key] = predefined_values[value]

	return custom_dimensions


def get_utm_source(request: Request) -> str:
	"""
    Returns the UTM source from the request or from the analytics cookies.

    :param request: The request object.
    :return: The UTM source.
    """
	utm_source = request.values.get(UTM_SOURCE)
	if utm_source:
		logging.info(f'[UTM source] from request: {utm_source}')
		return utm_source

	analytics_raw = get_cookies(request).get('analytics_campaign')
	analytics_data = None

	if analytics_raw:
		try:
			analytics_data = json.loads(analytics_raw)
		except (ValueError, TypeError) as e:
			logging.warning("Error parsing analytics cookies: %s", e)
			logging.warning("Raw analytics cookie: %s", analytics_raw)

	if analytics_data:
		utm_source = analytics_data.get(UTM_SOURCE, '')
		if utm_source == MANAGER_SOURCE_CALL_CENTER and not is_call_center():
			logging.info("[UTM source] Callcenter utm source detected in cookies, but agentId not found. Clearing source.")
			return ''
		logging.info(f'[UTM source] from cookies: {utm_source}')
		return utm_source
	logging.info(f'[UTM source] not found.')
	return ''


def get_analytics_campaigns() -> dict:
	"""
	Returns current analytics.
	:return: Analytics dict.
	"""

	analytics_cookies = get_cookies(request).get('analytics_campaign')
	analytics_campaigns = {}

	if analytics_cookies:
		try:
			analytics_cookies = decompress_and_decode(analytics_cookies)
			logging.info("Analytics Cookies: %s", analytics_cookies)
		except Exception as e:
			logging.warning("Error parsing analytics cookies: %s", e)
			logging.warning(analytics_cookies)
			analytics_cookies = None

	for param_element in ANALYTICS_CAMPAIGN_PARAMS:
		if request.values.get(param_element):
			analytics_campaigns[param_element] = request.values.get(param_element)

		elif analytics_cookies and analytics_cookies.get(param_element):
			analytics_campaigns[param_element] = analytics_cookies.get(param_element)

	return analytics_campaigns


def get_analytics_campaigns_history() -> str:
	"""
	Get analytics history from cookies and returns it.
	:return: Analytics campaigns history.
	"""

	analytics_history_cookies = get_cookies(request).get('analytics_campaigns_history')
	analytics_campaigns_history = []

	if analytics_history_cookies:
		try:
			analytics_history_cookies = decompress_and_decode(analytics_history_cookies)
		except Exception as e:
			logging.warning("Error parsing analytics cookies: %s", e)
			logging.warning(analytics_history_cookies)
			analytics_history_cookies = None

	if analytics_history_cookies and len(analytics_history_cookies) > 0:
		for cookie in analytics_history_cookies:
			if any(cookie.get(param) for param in ANALYTICS_CAMPAIGN_PARAMS):
				analytics_campaigns_history.append(cookie)

	return ';'.join([json.dumps(cookie) for cookie in analytics_campaigns_history])


def analytics_duplication_control(booking_identifier):
	try:
		namespace = get_namespace()
		endpoint = get_internal_url() if not DEV else 'http://localhost:8090'
		endpoint = endpoint + '/utils?action=booking_analytics_triggered'

		context = {
			'endpoint': endpoint,
			'sid': get_session_id(),
			'namespace': namespace,
			'hotel_code': get_hotel_code(),
			'booking_identifier': booking_identifier,
		}
		return build_template_2('general/analytics/analytics_duplication_control.html', context)
	except Exception as e:
		logging.error('Error while building analytics duplication control html: %s', e)
		return ''


def get_cookiebot_config_by_domain():
	"""
	:unit_test: unit_tests.booking_process.utils.analytics.test_analyticsUtils.TestAnalyticsUtils.test_get_cookiebot_config_by_domain
	"""
	cookiebot_by_domain = get_web_configuration(COOKIEBOT_BY_DOMAIN)
	if cookiebot_by_domain:
		domain = request.host
		logging.info(f'Checking custom cookiebot for domain: {domain}')

		for pattern, config in cookiebot_by_domain.items():
			if re.search(pattern, domain):
				logging.info(f'Applied custom cookiebot config: {config}')
				return config

	return None


def analytics_tracking_code_already_fired(booking_id):
	hotel_code = get_hotel_code()

	key_redis = f'{hotel_code}_{booking_id}_booking_analytics_triggered'
	if RedisCache().get(key_redis):
		logging.info(f'Analytics tracking already fired for booking {booking_id}')
		return True


@managers_cache(
	key_generator=lambda f, a, k: f'get_user_info_with_logging_{a[0]}',
	only_thread_local=True
)
def _get_user_info_with_logging(email: str) -> dict:
	"""
	Cached function to retrieve user info and log it only once per email per session.

	:param email: User email to check
	:return: User info dict or None if not found
	:unit_test: unit_tests.booking_process.utils.analytics.test_analyticsUtils.TestAnalyticsUtils.test_get_user_info_with_logging
	"""

	user_info = session_manager.get(session_data.USER_LOGIN_INFO)
	if not user_info and get_config_property_value(advance_configs_names.MEMBERS_CLUB):
		user_info = check_if_user_exists(email=email)

	if user_info and type(user_info) is dict:
		logging.info(f"[GTM] User info is: {user_info}")

	return user_info


def _get_user_extra_info(user_info: dict) -> dict:
	"""
	Helper function to get user extra info.

	:param user_info: User info dict
	:return: User extra info dict or empty dict if not found
	:unit_test: unit_tests.booking_process.utils.analytics.test_analyticsUtils.TestAnalyticsUtils.test_get_user_extra_info
	"""

	try:
		user_extra_info = user_info.get('extraInfo') or {}
		if not type(user_extra_info) is dict:
			user_extra_info = json.loads(user_extra_info)
	except Exception as e:
		user_extra_info = {}
		logging.error(f"[GTM] Error loading user extra info: {e}")

	return user_extra_info


def complete_context_with_club_info(context: dict) -> None:
	"""
	Complete context with club info if user is logged and club is active.

	:param context: Context to complete
	:unit_test: unit_tests.booking_process.utils.analytics.test_analyticsUtils.TestAnalyticsUtils.test_complete_context_with_club_info
	"""

	if not club_is_active():
		return

	# Use cached function to get user info with optimized logging
	user_info = _get_user_info_with_logging(context.get('email') or '')
	if user_info and type(user_info) is dict:
		user_extra_info = _get_user_extra_info(user_info)

		context['user_club_id'] = user_info.get('idmember')
		context['user_promotions_accept'] = bool(user_extra_info.get('promotions_accept'))

		user_params_to_check = ['email', 'name', 'surname', 'birthday', 'telephone']
		for param in user_params_to_check:
			dict_param = param

			if param == 'telephone':
				dict_param = 'phone_number'

			if user_info.get(param):
				context[dict_param] = user_info.get(param)


def hash_analytics_params(context: dict, params: set[str]) -> dict:
	hashed_params = {}
	for param in params:
		current_param_value = context.get(param) or ''

		if current_param_value:
			current_param_value = current_param_value.replace(' ', '').lower()

			if 'phone_number' in param:
				current_param_value = normalize_phone_number(context, current_param_value)

		hashed_params[f"{param}_sha256"] = hash_password(current_param_value) if current_param_value else None

	return hashed_params


def normalize_phone_number(context: dict, phone_number: str) -> str:
	if phone_number.startswith('+'):
		return phone_number

	country_selected = context.get('country') or context.get('pais')
	if not country_selected:
		return phone_number

	prefix_country_list = get_prefix_country_list(session_manager.get(session_data.LANGUAGE))
	prefix = next(
		(prefix_dict.get('dial_code', '')
		 for prefix_dict in prefix_country_list
		 if country_selected == prefix_dict.get('code', '')),
		''
	)

	return f"{prefix}{phone_number}"


def save_analytics_campaigns_in_extra_info(extra_info: dict) -> None:
	"""
	Process analytics campaigns history and save necessary data in extra_info of reservation.
	:param extra_info: Reservation extra info.
	"""

	if analytics_campaigns_history := session_manager.get(ANALYTICS_CAMPAIGNS_HISTORY):
		extra_info['analytics_campaigns_history'] = analytics_campaigns_history

		if isinstance(analytics_campaigns_history, str):
			try:
				campaigns_list = analytics_campaigns_history.split(';')
				first_campaign = json.loads(campaigns_list[0])
				last_campaign = json.loads(campaigns_list[-1])

				first_timestamp = first_campaign.get('campaign_timestamp')
				if isinstance(first_timestamp, list):
					# New format, we need to get the first timestamp.
					first_timestamp = first_timestamp[0]

				last_timestamp = last_campaign.get('campaign_timestamp')
				if isinstance(last_timestamp, list):
					# New format, we need to get the last timestamp.
					last_timestamp = last_timestamp[-1]

				if first_timestamp:
					extra_info[FIRST_CAMPAIGN_INTERACTION] = first_timestamp
				if last_timestamp:
					extra_info[LAST_CAMPAIGN_INTERACTION] = last_timestamp

			except json.JSONDecodeError as e:
				logging.error(f"Error decoding JSON in analytics campaigns history. {e}")
			except ValueError as e:
				logging.error(f"Error processing analytics campaigns history. {e}")

	if get_config_property_value(DERBYSOFT_TRACKING):
		extra_info['ds_click_id'] = get_ds_click_id()


def get_ds_click_id():
    ds_click_id = session_manager.get(DS_CLICK_ID)
    if ds_click_id:
        return ds_click_id

    derbysoft_cookies = request.cookies.get("derbysoft_analytics")
    if not derbysoft_cookies:
        return ""

    try:
        derbysoft_cookies = json.loads(derbysoft_cookies)
        ds_click_id = derbysoft_cookies.get("dsclid", "")
    except json.JSONDecodeError:
        ds_click_id = ""

    return ds_click_id

