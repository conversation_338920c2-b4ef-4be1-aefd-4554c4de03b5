{"isATOL": false, "recommendedFlights": [{"flightId": "be13229729e3146ed1943a75357939f20852bf9ee4ad85d639ae20a814044a7e", "CurrencyCode": "EUR", "totalPrice": "325.096", "luggage_include": false, "bestDeal": true, "inbound": {"origin": "TFS", "destination": "AGP", "departureDate": "2025-07-18", "arrivalTime": "09:10:00", "departureTime": "05:45:00", "arrivalDate": "2025-07-18", "connections": 0, "segments": [{"departureCode": "TFS", "departureAirport": "Tenerife Sur", "departureCityCode": "TCI", "departureCityName": "Tenerife", "departureCountryCode": "ES", "departureDate": "2025-07-18", "departureTime": "05:45:00", "arrivalCode": "AGP", "arrivalAirport": "Málaga-Costa del Sol", "arrivalCityCode": "AGP", "arrivalCityName": "Malaga", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-18", "arrivalTime": "09:10:00", "airlineCode": "FR", "airlineName": "Ryanair Ltd.", "flightCode": "FR3349", "duration": "PT1H25M", "fare_rules": "", "fare_type": " passenger 0:<PERSON><PERSON>BECONOM<PERSON>,  passenger 1:W<PERSON>BECONOM<PERSON>", "rate_class": ""}]}, "outbound": {"origin": "AGP", "destination": "TFS", "departureDate": "2025-07-14", "arrivalTime": "21:45:00", "departureTime": "20:05:00", "arrivalDate": "2025-07-14", "connections": 0, "segments": [{"departureCode": "AGP", "departureAirport": "Málaga-Costa del Sol", "departureCityCode": "AGP", "departureCityName": "Malaga", "departureCountryCode": "ES", "departureDate": "2025-07-14", "departureTime": "20:05:00", "arrivalCode": "TFS", "arrivalAirport": "Tenerife Sur", "arrivalCityCode": "TCI", "arrivalCityName": "Tenerife", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-14", "arrivalTime": "21:45:00", "airlineCode": "FR", "airlineName": "Ryanair Ltd.", "flightCode": "FR3348", "duration": "PT3H40M", "fare_rules": "", "fare_type": " passenger 0:<PERSON><PERSON>BECONOM<PERSON>,  passenger 1:W<PERSON>BECONOM<PERSON>", "rate_class": ""}]}, "source": "PYTON", "pricePerPassenger": [{"currency": "EUR", "passenger_id": "0", "passenger_type": "ADULT", "total": "162.548"}, {"currency": "EUR", "passenger_id": "1", "passenger_type": "ADULT", "total": "162.548"}]}], "flights": [{"flightId": "b3faa9b86fcf2f330b7c381b99b0d605e0e9c0450d80b99d5fee1f48587ac12a", "CurrencyCode": "EUR", "totalPrice": "704.406", "luggage_include": false, "bestDeal": false, "inbound": {"origin": "TFN", "destination": "AGP", "departureDate": "2025-07-18", "arrivalTime": "16:15:00", "departureTime": "09:55:00", "arrivalDate": "2025-07-18", "connections": 1, "segments": [{"departureCode": "TFN", "departureAirport": "Tenerife Norte", "departureCityCode": "TCI", "departureCityName": "Tenerife", "departureCountryCode": "ES", "departureDate": "2025-07-18", "departureTime": "09:55:00", "arrivalCode": "MAD", "arrivalAirport": "<PERSON><PERSON>-Barajas", "arrivalCityCode": "MAD", "arrivalCityName": "Madrid", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-18", "arrivalTime": "13:40:00", "airlineCode": "UX", "airlineName": "Air Europa", "flightCode": "UX9047", "duration": "PT2H45M", "fare_rules": "", "fare_type": " passenger 0:PDYY5L,  passenger 1:PDYY5L", "rate_class": " passenger 0:P,  passenger 1:P"}, {"departureCode": "MAD", "departureAirport": "<PERSON><PERSON>-Barajas", "departureCityCode": "MAD", "departureCityName": "Madrid", "departureCountryCode": "ES", "departureDate": "2025-07-18", "departureTime": "15:00:00", "arrivalCode": "AGP", "arrivalAirport": "Málaga-Costa del Sol", "arrivalCityCode": "AGP", "arrivalCityName": "Malaga", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-18", "arrivalTime": "16:15:00", "airlineCode": "UX", "airlineName": "Air Europa", "flightCode": "UX5039", "duration": "PT1H15M", "fare_rules": "", "fare_type": " passenger 0:PDYY5L,  passenger 1:PDYY5L", "rate_class": " passenger 0:P,  passenger 1:P"}]}, "outbound": {"origin": "AGP", "destination": "TFN", "departureDate": "2025-07-14", "arrivalTime": "13:40:00", "departureTime": "08:35:00", "arrivalDate": "2025-07-14", "connections": 1, "segments": [{"departureCode": "AGP", "departureAirport": "Málaga-Costa del Sol", "departureCityCode": "AGP", "departureCityName": "Malaga", "departureCountryCode": "ES", "departureDate": "2025-07-14", "departureTime": "08:35:00", "arrivalCode": "MAD", "arrivalAirport": "<PERSON><PERSON>-Barajas", "arrivalCityCode": "MAD", "arrivalCityName": "Madrid", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-14", "arrivalTime": "09:45:00", "airlineCode": "UX", "airlineName": "Air Europa", "flightCode": "UX5042", "duration": "PT1H10M", "fare_rules": "", "fare_type": " passenger 0:PDYY5L,  passenger 1:PDYY5L", "rate_class": " passenger 0:P,  passenger 1:P"}, {"departureCode": "MAD", "departureAirport": "<PERSON><PERSON>-Barajas", "departureCityCode": "MAD", "departureCityName": "Madrid", "departureCountryCode": "ES", "departureDate": "2025-07-14", "departureTime": "11:45:00", "arrivalCode": "TFN", "arrivalAirport": "Tenerife Norte", "arrivalCityCode": "TCI", "arrivalCityName": "Tenerife", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-14", "arrivalTime": "13:40:00", "airlineCode": "UX", "airlineName": "Air Europa", "flightCode": "UX9118", "duration": "PT2H55M", "fare_rules": "", "fare_type": " passenger 0:PDYY5L,  passenger 1:PDYY5L", "rate_class": " passenger 0:P,  passenger 1:P"}]}, "source": "GDS", "pricePerPassenger": [{"currency": "EUR", "passenger_id": "0", "passenger_type": "ADULT", "total": "352.203"}, {"currency": "EUR", "passenger_id": "1", "passenger_type": "ADULT", "total": "352.203"}]}, {"flightId": "f33853bd1691c8c6544d9ecb4b5f31a943281ebd5f9265abbba933a4eb955e24", "CurrencyCode": "EUR", "totalPrice": "888.893", "luggage_include": false, "bestDeal": false, "inbound": {"origin": "TFS", "destination": "AGP", "departureDate": "2025-07-18", "arrivalTime": "11:20:00", "departureTime": "10:45:00", "arrivalDate": "2025-07-19", "connections": 2, "segments": [{"departureCode": "TFS", "departureAirport": "Tenerife Sur", "departureCityCode": "TCI", "departureCityName": "Tenerife", "departureCountryCode": "ES", "departureDate": "2025-07-18", "departureTime": "10:45:00", "arrivalCode": "ZRH", "arrivalAirport": "ZRH", "arrivalCityCode": "ZRH", "arrivalCityName": "Zurich", "arrivalCountryCode": "CH", "arrivalDate": "2025-07-18", "arrivalTime": "15:55:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH4465", "duration": "PT4H10M", "fare_rules": "", "fare_type": " passenger 0:VETLGTU0,  passenger 1:VETLGTU0", "rate_class": " passenger 0:V,  passenger 1:V"}, {"departureCode": "ZRH", "departureAirport": "ZRH", "departureCityCode": "ZRH", "departureCityName": "Zurich", "departureCountryCode": "CH", "departureDate": "2025-07-18", "departureTime": "21:00:00", "arrivalCode": "MUC", "arrivalAirport": "Múnich<PERSON><PERSON>", "arrivalCityCode": "MUC", "arrivalCityName": "Munich", "arrivalCountryCode": "DE", "arrivalDate": "2025-07-18", "arrivalTime": "22:00:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH5769", "duration": "PT1H", "fare_rules": "", "fare_type": " passenger 0:VETLGTU0,  passenger 1:VETLGTU0", "rate_class": " passenger 0:V,  passenger 1:V"}, {"departureCode": "MUC", "departureAirport": "Múnich<PERSON><PERSON>", "departureCityCode": "MUC", "departureCityName": "Munich", "departureCountryCode": "DE", "departureDate": "2025-07-19", "departureTime": "08:15:00", "arrivalCode": "AGP", "arrivalAirport": "Málaga-Costa del Sol", "arrivalCityCode": "AGP", "arrivalCityName": "Malaga", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-19", "arrivalTime": "11:20:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH1834", "duration": "PT3H5M", "fare_rules": "", "fare_type": " passenger 0:VETLGTU0,  passenger 1:VETLGTU0", "rate_class": " passenger 0:V,  passenger 1:V"}]}, "outbound": {"origin": "AGP", "destination": "TFS", "departureDate": "2025-07-14", "arrivalTime": "09:45:00", "departureTime": "14:30:00", "arrivalDate": "2025-07-15", "connections": 2, "segments": [{"departureCode": "AGP", "departureAirport": "Málaga-Costa del Sol", "departureCityCode": "AGP", "departureCityName": "Malaga", "departureCountryCode": "ES", "departureDate": "2025-07-14", "departureTime": "14:30:00", "arrivalCode": "MUC", "arrivalAirport": "Múnich<PERSON><PERSON>", "arrivalCityCode": "MUC", "arrivalCityName": "Munich", "arrivalCountryCode": "DE", "arrivalDate": "2025-07-14", "arrivalTime": "17:25:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH1833", "duration": "PT2H55M", "fare_rules": "", "fare_type": " passenger 0:QETLGTU0,  passenger 1:QETLGTU0", "rate_class": " passenger 0:Q,  passenger 1:Q"}, {"departureCode": "MUC", "departureAirport": "Múnich<PERSON><PERSON>", "departureCityCode": "MUC", "departureCityName": "Munich", "departureCountryCode": "DE", "departureDate": "2025-07-14", "departureTime": "19:05:00", "arrivalCode": "ZRH", "arrivalAirport": "ZRH", "arrivalCityCode": "ZRH", "arrivalCityName": "Zurich", "arrivalCountryCode": "CH", "arrivalDate": "2025-07-14", "arrivalTime": "20:05:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH5768", "duration": "PT1H", "fare_rules": "", "fare_type": " passenger 0:QETLGTU0,  passenger 1:QETLGTU0", "rate_class": " passenger 0:Q,  passenger 1:Q"}, {"departureCode": "ZRH", "departureAirport": "ZRH", "departureCityCode": "ZRH", "departureCityName": "Zurich", "departureCountryCode": "CH", "departureDate": "2025-07-15", "departureTime": "06:10:00", "arrivalCode": "TFS", "arrivalAirport": "Tenerife Sur", "arrivalCityCode": "TCI", "arrivalCityName": "Tenerife", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-15", "arrivalTime": "09:45:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH4464", "duration": "PT4H35M", "fare_rules": "", "fare_type": " passenger 0:QETLGTU0,  passenger 1:QETLGTU0", "rate_class": " passenger 0:Q,  passenger 1:Q"}]}, "source": "NDC", "pricePerPassenger": [{"currency": "EUR", "passenger_id": "0", "passenger_type": "ADULT", "total": "444.447"}, {"currency": "EUR", "passenger_id": "1", "passenger_type": "ADULT", "total": "444.447"}]}, {"flightId": "0ba81d5a28ba49a4b0a6da08bd2aa840bc9c407bc0663e3e0f7776dc909d0df4", "CurrencyCode": "EUR", "totalPrice": "905.076", "luggage_include": false, "bestDeal": false, "inbound": {"origin": "TFS", "destination": "AGP", "departureDate": "2025-07-18", "arrivalTime": "11:20:00", "departureTime": "10:45:00", "arrivalDate": "2025-07-19", "connections": 2, "segments": [{"departureCode": "TFS", "departureAirport": "Tenerife Sur", "departureCityCode": "TCI", "departureCityName": "Tenerife", "departureCountryCode": "ES", "departureDate": "2025-07-18", "departureTime": "10:45:00", "arrivalCode": "ZRH", "arrivalAirport": "ZRH", "arrivalCityCode": "ZRH", "arrivalCityName": "Zurich", "arrivalCountryCode": "CH", "arrivalDate": "2025-07-18", "arrivalTime": "15:55:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH4465", "duration": "PT4H10M", "fare_rules": "", "fare_type": " passenger 0:VETLGTU0,  passenger 1:VETLGTU0", "rate_class": " passenger 0:V,  passenger 1:V"}, {"departureCode": "ZRH", "departureAirport": "ZRH", "departureCityCode": "ZRH", "departureCityName": "Zurich", "departureCountryCode": "CH", "departureDate": "2025-07-18", "departureTime": "21:00:00", "arrivalCode": "MUC", "arrivalAirport": "Múnich<PERSON><PERSON>", "arrivalCityCode": "MUC", "arrivalCityName": "Munich", "arrivalCountryCode": "DE", "arrivalDate": "2025-07-18", "arrivalTime": "22:00:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH5769", "duration": "PT1H", "fare_rules": "", "fare_type": " passenger 0:VETLGTU0,  passenger 1:VETLGTU0", "rate_class": " passenger 0:V,  passenger 1:V"}, {"departureCode": "MUC", "departureAirport": "Múnich<PERSON><PERSON>", "departureCityCode": "MUC", "departureCityName": "Munich", "departureCountryCode": "DE", "departureDate": "2025-07-19", "departureTime": "08:15:00", "arrivalCode": "AGP", "arrivalAirport": "Málaga-Costa del Sol", "arrivalCityCode": "AGP", "arrivalCityName": "Malaga", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-19", "arrivalTime": "11:20:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH1834", "duration": "PT3H5M", "fare_rules": "", "fare_type": " passenger 0:VETLGTU0,  passenger 1:VETLGTU0", "rate_class": " passenger 0:V,  passenger 1:V"}]}, "outbound": {"origin": "AGP", "destination": "TFS", "departureDate": "2025-07-14", "arrivalTime": "09:45:00", "departureTime": "13:15:00", "arrivalDate": "2025-07-15", "connections": 2, "segments": [{"departureCode": "AGP", "departureAirport": "Málaga-Costa del Sol", "departureCityCode": "AGP", "departureCityName": "Malaga", "departureCountryCode": "ES", "departureDate": "2025-07-14", "departureTime": "13:15:00", "arrivalCode": "FRA", "arrivalAirport": "Fráncfort del Meno", "arrivalCityCode": "FRA", "arrivalCityName": "Frankfurt", "arrivalCountryCode": "DE", "arrivalDate": "2025-07-14", "arrivalTime": "16:15:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH1149", "duration": "PT3H", "fare_rules": "", "fare_type": " passenger 0:QETLGTU0,  passenger 1:QETLGTU0", "rate_class": " passenger 0:Q,  passenger 1:Q"}, {"departureCode": "FRA", "departureAirport": "Fráncfort del Meno", "departureCityCode": "FRA", "departureCityName": "Frankfurt", "departureCountryCode": "DE", "departureDate": "2025-07-14", "departureTime": "20:45:00", "arrivalCode": "ZRH", "arrivalAirport": "ZRH", "arrivalCityCode": "ZRH", "arrivalCityName": "Zurich", "arrivalCountryCode": "CH", "arrivalDate": "2025-07-14", "arrivalTime": "21:50:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH5740", "duration": "PT1H5M", "fare_rules": "", "fare_type": " passenger 0:QETLGTU0,  passenger 1:QETLGTU0", "rate_class": " passenger 0:Q,  passenger 1:Q"}, {"departureCode": "ZRH", "departureAirport": "ZRH", "departureCityCode": "ZRH", "departureCityName": "Zurich", "departureCountryCode": "CH", "departureDate": "2025-07-15", "departureTime": "06:10:00", "arrivalCode": "TFS", "arrivalAirport": "Tenerife Sur", "arrivalCityCode": "TCI", "arrivalCityName": "Tenerife", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-15", "arrivalTime": "09:45:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH4464", "duration": "PT4H35M", "fare_rules": "", "fare_type": " passenger 0:QETLGTU0,  passenger 1:QETLGTU0", "rate_class": " passenger 0:Q,  passenger 1:Q"}]}, "source": "NDC", "pricePerPassenger": [{"currency": "EUR", "passenger_id": "0", "passenger_type": "ADULT", "total": "452.538"}, {"currency": "EUR", "passenger_id": "1", "passenger_type": "ADULT", "total": "452.538"}]}, {"flightId": "b17717f932d27fc1f873c401b025576eb2dceda17b7621e89a1c59b570587271", "CurrencyCode": "EUR", "totalPrice": "967.513", "luggage_include": true, "bestDeal": false, "inbound": {"origin": "TFS", "destination": "AGP", "departureDate": "2025-07-18", "arrivalTime": "00:10:00", "departureTime": "10:45:00", "arrivalDate": "2025-07-19", "connections": 1, "segments": [{"departureCode": "TFS", "departureAirport": "Tenerife Sur", "departureCityCode": "TCI", "departureCityName": "Tenerife", "departureCountryCode": "ES", "departureDate": "2025-07-18", "departureTime": "10:45:00", "arrivalCode": "ZRH", "arrivalAirport": "ZRH", "arrivalCityCode": "ZRH", "arrivalCityName": "Zurich", "arrivalCountryCode": "CH", "arrivalDate": "2025-07-18", "arrivalTime": "15:55:00", "airlineCode": "LX", "airlineName": "SWISS", "flightCode": "LX8215", "duration": "PT4H10M", "fare_rules": "", "fare_type": " passenger 0:VETCLSU0,  passenger 1:VETCLSU0", "rate_class": " passenger 0:V,  passenger 1:V"}, {"departureCode": "ZRH", "departureAirport": "ZRH", "departureCityCode": "ZRH", "departureCityName": "Zurich", "departureCountryCode": "CH", "departureDate": "2025-07-18", "departureTime": "21:25:00", "arrivalCode": "AGP", "arrivalAirport": "Málaga-Costa del Sol", "arrivalCityCode": "AGP", "arrivalCityName": "Malaga", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-19", "arrivalTime": "00:10:00", "airlineCode": "LX", "airlineName": "SWISS", "flightCode": "LX2120", "duration": "PT2H45M", "fare_rules": "", "fare_type": " passenger 0:VETCLSU0,  passenger 1:VETCLSU0", "rate_class": " passenger 0:V,  passenger 1:V"}]}, "outbound": {"origin": "AGP", "destination": "TFS", "departureDate": "2025-07-14", "arrivalTime": "09:45:00", "departureTime": "16:50:00", "arrivalDate": "2025-07-15", "connections": 1, "segments": [{"departureCode": "AGP", "departureAirport": "Málaga-Costa del Sol", "departureCityCode": "AGP", "departureCityName": "Malaga", "departureCountryCode": "ES", "departureDate": "2025-07-14", "departureTime": "16:50:00", "arrivalCode": "ZRH", "arrivalAirport": "ZRH", "arrivalCityCode": "ZRH", "arrivalCityName": "Zurich", "arrivalCountryCode": "CH", "arrivalDate": "2025-07-14", "arrivalTime": "19:30:00", "airlineCode": "LX", "airlineName": "SWISS", "flightCode": "LX2115", "duration": "PT2H40M", "fare_rules": "", "fare_type": " passenger 0:QETCLSU0,  passenger 1:QETCLSU0", "rate_class": " passenger 0:Q,  passenger 1:Q"}, {"departureCode": "ZRH", "departureAirport": "ZRH", "departureCityCode": "ZRH", "departureCityName": "Zurich", "departureCountryCode": "CH", "departureDate": "2025-07-15", "departureTime": "06:10:00", "arrivalCode": "TFS", "arrivalAirport": "Tenerife Sur", "arrivalCityCode": "TCI", "arrivalCityName": "Tenerife", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-15", "arrivalTime": "09:45:00", "airlineCode": "LX", "airlineName": "SWISS", "flightCode": "LX8214", "duration": "PT4H35M", "fare_rules": "", "fare_type": " passenger 0:QETCLSU0,  passenger 1:QETCLSU0", "rate_class": " passenger 0:Q,  passenger 1:Q"}]}, "source": "NDC", "pricePerPassenger": [{"currency": "EUR", "passenger_id": "0", "passenger_type": "ADULT", "total": "483.756"}, {"currency": "EUR", "passenger_id": "1", "passenger_type": "ADULT", "total": "483.756"}]}, {"flightId": "6d34bf3a6e7914191659c29ae5f49f9830ec10447705c8a737f24c8956973049", "CurrencyCode": "EUR", "totalPrice": "1073.685", "luggage_include": false, "bestDeal": false, "inbound": {"origin": "TFS", "destination": "AGP", "departureDate": "2025-07-18", "arrivalTime": "11:20:00", "departureTime": "10:45:00", "arrivalDate": "2025-07-19", "connections": 2, "segments": [{"departureCode": "TFS", "departureAirport": "Tenerife Sur", "departureCityCode": "TCI", "departureCityName": "Tenerife", "departureCountryCode": "ES", "departureDate": "2025-07-18", "departureTime": "10:45:00", "arrivalCode": "ZRH", "arrivalAirport": "ZRH", "arrivalCityCode": "ZRH", "arrivalCityName": "Zurich", "arrivalCountryCode": "CH", "arrivalDate": "2025-07-18", "arrivalTime": "15:55:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH4465", "duration": "PT4H10M", "fare_rules": "", "fare_type": " passenger 0:VETLGTU0,  passenger 1:VETLGTU0", "rate_class": " passenger 0:V,  passenger 1:V"}, {"departureCode": "ZRH", "departureAirport": "ZRH", "departureCityCode": "ZRH", "departureCityName": "Zurich", "departureCountryCode": "CH", "departureDate": "2025-07-18", "departureTime": "21:00:00", "arrivalCode": "MUC", "arrivalAirport": "Múnich<PERSON><PERSON>", "arrivalCityCode": "MUC", "arrivalCityName": "Munich", "arrivalCountryCode": "DE", "arrivalDate": "2025-07-18", "arrivalTime": "22:00:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH5769", "duration": "PT1H", "fare_rules": "", "fare_type": " passenger 0:VETLGTU0,  passenger 1:VETLGTU0", "rate_class": " passenger 0:V,  passenger 1:V"}, {"departureCode": "MUC", "departureAirport": "Múnich<PERSON><PERSON>", "departureCityCode": "MUC", "departureCityName": "Munich", "departureCountryCode": "DE", "departureDate": "2025-07-19", "departureTime": "08:15:00", "arrivalCode": "AGP", "arrivalAirport": "Málaga-Costa del Sol", "arrivalCityCode": "AGP", "arrivalCityName": "Malaga", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-19", "arrivalTime": "11:20:00", "airlineCode": "LH", "airlineName": "Lufthansa Cargo", "flightCode": "LH1834", "duration": "PT3H5M", "fare_rules": "", "fare_type": " passenger 0:VETLGTU0,  passenger 1:VETLGTU0", "rate_class": " passenger 0:V,  passenger 1:V"}]}, "outbound": {"origin": "AGP", "destination": "TFS", "departureDate": "2025-07-14", "arrivalTime": "17:00:00", "departureTime": "20:20:00", "arrivalDate": "2025-07-15", "connections": 1, "segments": [{"departureCode": "AGP", "departureAirport": "Málaga-Costa del Sol", "departureCityCode": "AGP", "departureCityName": "Malaga", "departureCountryCode": "ES", "departureDate": "2025-07-14", "departureTime": "20:20:00", "arrivalCode": "BRU", "arrivalAirport": "BRU", "arrivalCityCode": "BRU", "arrivalCityName": "Brussels", "arrivalCountryCode": "BE", "arrivalDate": "2025-07-14", "arrivalTime": "23:10:00", "airlineCode": "SN", "airlineName": "Brussels Airlines", "flightCode": "SN3738", "duration": "PT2H50M", "fare_rules": "", "fare_type": " passenger 0:UETLGTU0,  passenger 1:UETLGTU0", "rate_class": " passenger 0:U,  passenger 1:U"}, {"departureCode": "BRU", "departureAirport": "BRU", "departureCityCode": "BRU", "departureCityName": "Brussels", "departureCountryCode": "BE", "departureDate": "2025-07-15", "departureTime": "13:20:00", "arrivalCode": "TFS", "arrivalAirport": "Tenerife Sur", "arrivalCityCode": "TCI", "arrivalCityName": "Tenerife", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-15", "arrivalTime": "17:00:00", "airlineCode": "SN", "airlineName": "Brussels Airlines", "flightCode": "SN3781", "duration": "PT4H40M", "fare_rules": "", "fare_type": " passenger 0:UETLGTU0,  passenger 1:UETLGTU0", "rate_class": " passenger 0:U,  passenger 1:U"}]}, "source": "NDC", "pricePerPassenger": [{"currency": "EUR", "passenger_id": "0", "passenger_type": "ADULT", "total": "536.843"}, {"currency": "EUR", "passenger_id": "1", "passenger_type": "ADULT", "total": "536.843"}]}, {"flightId": "32b76506bca4b84befe42f4d18c22e08eeceacd060024c7ad3008308e9cbeb50", "CurrencyCode": "EUR", "totalPrice": "1136.893", "luggage_include": true, "bestDeal": false, "inbound": {"origin": "TFN", "destination": "AGP", "departureDate": "2025-07-18", "arrivalTime": "12:45:00", "departureTime": "21:10:00", "arrivalDate": "2025-07-19", "connections": 1, "segments": [{"departureCode": "TFN", "departureAirport": "Tenerife Norte", "departureCityCode": "TCI", "departureCityName": "Tenerife", "departureCountryCode": "ES", "departureDate": "2025-07-18", "departureTime": "21:10:00", "arrivalCode": "MAD", "arrivalAirport": "<PERSON><PERSON>-Barajas", "arrivalCityCode": "MAD", "arrivalCityName": "Madrid", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-19", "arrivalTime": "00:55:00", "airlineCode": "IB", "airlineName": "IBERIA", "flightCode": "IB1582", "duration": "PT2H45M", "fare_rules": "", "fare_type": " passenger 0:LJN0M7<PERSON>,  passenger 1:LJN0M7TO", "rate_class": " passenger 0:L,  passenger 1:L"}, {"departureCode": "MAD", "departureAirport": "<PERSON><PERSON>-Barajas", "departureCityCode": "MAD", "departureCityName": "Madrid", "departureCountryCode": "ES", "departureDate": "2025-07-19", "departureTime": "11:25:00", "arrivalCode": "AGP", "arrivalAirport": "Málaga-Costa del Sol", "arrivalCityCode": "AGP", "arrivalCityName": "Malaga", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-19", "arrivalTime": "12:45:00", "airlineCode": "IB", "airlineName": "IBERIA", "flightCode": "IB1003", "duration": "PT1H20M", "fare_rules": "", "fare_type": " passenger 0:LJN0M7<PERSON>,  passenger 1:LJN0M7TO", "rate_class": " passenger 0:L,  passenger 1:L"}]}, "outbound": {"origin": "AGP", "destination": "TFS", "departureDate": "2025-07-14", "arrivalTime": "02:10:00", "departureTime": "17:45:00", "arrivalDate": "2025-07-15", "connections": 1, "segments": [{"departureCode": "AGP", "departureAirport": "Málaga-Costa del Sol", "departureCityCode": "AGP", "departureCityName": "Malaga", "departureCountryCode": "ES", "departureDate": "2025-07-14", "departureTime": "17:45:00", "arrivalCode": "MAD", "arrivalAirport": "<PERSON><PERSON>-Barajas", "arrivalCityCode": "MAD", "arrivalCityName": "Madrid", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-14", "arrivalTime": "19:05:00", "airlineCode": "IB", "airlineName": "IBERIA", "flightCode": "IB1006", "duration": "PT1H20M", "fare_rules": "", "fare_type": " passenger 0:VJN0M7<PERSON>,  passenger 1:VJN0M7TO", "rate_class": " passenger 0:V,  passenger 1:V"}, {"departureCode": "MAD", "departureAirport": "<PERSON><PERSON>-Barajas", "departureCityCode": "MAD", "departureCityName": "Madrid", "departureCountryCode": "ES", "departureDate": "2025-07-15", "departureTime": "00:05:00", "arrivalCode": "TFS", "arrivalAirport": "Tenerife Sur", "arrivalCityCode": "TCI", "arrivalCityName": "Tenerife", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-15", "arrivalTime": "02:10:00", "airlineCode": "IB", "airlineName": "IBERIA", "flightCode": "IB1555", "duration": "PT3H5M", "fare_rules": "", "fare_type": " passenger 0:VJN0M7<PERSON>,  passenger 1:VJN0M7TO", "rate_class": " passenger 0:V,  passenger 1:V"}]}, "source": "GDS", "pricePerPassenger": [{"currency": "EUR", "passenger_id": "0", "passenger_type": "ADULT", "total": "568.447"}, {"currency": "EUR", "passenger_id": "1", "passenger_type": "ADULT", "total": "568.447"}]}, {"flightId": "b20fb54cde0e7328c97162a086242851aae86baaa1d6492b923885c304af8027", "CurrencyCode": "EUR", "totalPrice": "1151.066", "luggage_include": true, "bestDeal": false, "inbound": {"origin": "TFN", "destination": "AGP", "departureDate": "2025-07-18", "arrivalTime": "12:45:00", "departureTime": "06:50:00", "arrivalDate": "2025-07-18", "connections": 1, "segments": [{"departureCode": "TFN", "departureAirport": "Tenerife Norte", "departureCityCode": "TCI", "departureCityName": "Tenerife", "departureCountryCode": "ES", "departureDate": "2025-07-18", "departureTime": "06:50:00", "arrivalCode": "MAD", "arrivalAirport": "<PERSON><PERSON>-Barajas", "arrivalCityCode": "MAD", "arrivalCityName": "Madrid", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-18", "arrivalTime": "10:40:00", "airlineCode": "IB", "airlineName": "IBERIA", "flightCode": "IB1586", "duration": "PT2H50M", "fare_rules": "", "fare_type": " passenger 0:LJN0M7<PERSON>,  passenger 1:LJN0M7TO", "rate_class": " passenger 0:L,  passenger 1:L"}, {"departureCode": "MAD", "departureAirport": "<PERSON><PERSON>-Barajas", "departureCityCode": "MAD", "departureCityName": "Madrid", "departureCountryCode": "ES", "departureDate": "2025-07-18", "departureTime": "11:25:00", "arrivalCode": "AGP", "arrivalAirport": "Málaga-Costa del Sol", "arrivalCityCode": "AGP", "arrivalCityName": "Malaga", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-18", "arrivalTime": "12:45:00", "airlineCode": "IB", "airlineName": "IBERIA", "flightCode": "IB1003", "duration": "PT1H20M", "fare_rules": "", "fare_type": " passenger 0:LJN0M7<PERSON>,  passenger 1:LJN0M7TO", "rate_class": " passenger 0:L,  passenger 1:L"}]}, "outbound": {"origin": "AGP", "destination": "TFN", "departureDate": "2025-07-14", "arrivalTime": "10:40:00", "departureTime": "17:45:00", "arrivalDate": "2025-07-15", "connections": 1, "segments": [{"departureCode": "AGP", "departureAirport": "Málaga-Costa del Sol", "departureCityCode": "AGP", "departureCityName": "Malaga", "departureCountryCode": "ES", "departureDate": "2025-07-14", "departureTime": "17:45:00", "arrivalCode": "MAD", "arrivalAirport": "<PERSON><PERSON>-Barajas", "arrivalCityCode": "MAD", "arrivalCityName": "Madrid", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-14", "arrivalTime": "19:05:00", "airlineCode": "IB", "airlineName": "IBERIA", "flightCode": "IB1006", "duration": "PT1H20M", "fare_rules": "", "fare_type": " passenger 0:VJN0M7<PERSON>,  passenger 1:VJN0M7TO", "rate_class": " passenger 0:V,  passenger 1:V"}, {"departureCode": "MAD", "departureAirport": "<PERSON><PERSON>-Barajas", "departureCityCode": "MAD", "departureCityName": "Madrid", "departureCountryCode": "ES", "departureDate": "2025-07-15", "departureTime": "08:45:00", "arrivalCode": "TFN", "arrivalAirport": "Tenerife Norte", "arrivalCityCode": "TCI", "arrivalCityName": "Tenerife", "arrivalCountryCode": "ES", "arrivalDate": "2025-07-15", "arrivalTime": "10:40:00", "airlineCode": "IB", "airlineName": "IBERIA", "flightCode": "IB1565", "duration": "PT2H55M", "fare_rules": "", "fare_type": " passenger 0:VJN0M7<PERSON>,  passenger 1:VJN0M7TO", "rate_class": " passenger 0:V,  passenger 1:V"}]}, "source": "GDS", "pricePerPassenger": [{"currency": "EUR", "passenger_id": "0", "passenger_type": "ADULT", "total": "575.533"}, {"currency": "EUR", "passenger_id": "1", "passenger_type": "ADULT", "total": "575.533"}]}], "Passengers": {"NumberOfAdults": 2, "ChildrenAges": []}}