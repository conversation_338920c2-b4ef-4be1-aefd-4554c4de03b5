import json
import os
import requests
import concurrent.futures
import copy
from tqdm import tqdm

AI_SERVICE_URL = "https://ai-seeker-u6sqyga6xq-ew.a.run.app?referrer=hotelwebs_local"
BASIC_AUTH_USERNAME = "paraty"
BASIC_AUTH_PASSWORD = "paratyRules!"

def _read_json_file(file_path: str) -> dict:
    """
    Read a JSON file and return its content as a dictionary.

    :param file_path: Path to the JSON file.
    :return: Dictionary containing the JSON data.
    """
    with open(file_path, "r", encoding="utf-8") as file:
        data = json.load(file)
    return data


def _read_existing_translations(file_path: str) -> dict:
    """
    Read existing translations from a file if it exists.

    :param file_path: Path to the translation file.
    :return: Dictionary containing existing translations or empty dict if file doesn't exist.
    """
    if os.path.exists(file_path):
        try:
            return _read_json_file(file_path)
        except json.JSONDecodeError:
            print(f"Warning: Could not parse existing translation file {file_path}. Will create a new one.")
    return {}


def translate_text_to_language(text: str, language: str) -> str:
    message = [
        f"Translate the following airport name to {language}. \
        Airport name: `{text}`. It is always the airport name in ENGLISH.\
        If unsure leave it as is without providing any explanation. \
        If it is a name without translation and without any other information, keep it as is without providing any explanation. \
        Do not add any other information or explanation. \
        Do not make any questions. Your answer should be ONLY the airport name, without any details\
        If you think the airport name is not correct, it is empty or it doesn't correspond to an airport, just maintain it as is without providing any explanation."
    ]

    # Using AI translate this
    response = requests.post(AI_SERVICE_URL, json=message, auth=(BASIC_AUTH_USERNAME, BASIC_AUTH_PASSWORD))
    result = response.json()[0].strip()
    return result


def translate_airport_file_to_language(airports: dict, output_file_path: str, language: str):
    """
    Translate an airport file to a specific language.
    Checks if translations already exist and only translates missing airports.
    """
    # Read existing translations if file exists
    existing_translations = _read_existing_translations(output_file_path)
    print(f"Found {len(existing_translations)} existing translations in {output_file_path}")

    # Identify airports that need translation
    airports_to_translate = {}
    for code, airport in airports.items():
        if code not in existing_translations:
            airports_to_translate[code] = airport

    print(f"Need to translate {len(airports_to_translate)} new airports")

    if not airports_to_translate:
        print("All airports already translated, no work needed.")
        return

    def translate_airport(airport_code):
        airport = airports[airport_code]
        airport_name = airport["name"]
        translated_name = translate_text_to_language(airport_name, language)
        return airport_code, translated_name

    # Create dict for new translations
    new_translations = {}

    # Translate missing airport names in parallel
    with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
        future_to_airport = {executor.submit(translate_airport, code): code for code in airports_to_translate.keys()}

        total_airports = len(future_to_airport)
        with tqdm(total=total_airports, desc="Translating airports", unit="airport") as progress_bar:
            for future in concurrent.futures.as_completed(future_to_airport):
                try:
                    airport_code, translated_name = future.result()
                    new_translations[airport_code] = translated_name
                    # Print the translation progress to console
                    print(f"Translated {airport_code}: '{airports[airport_code]['name']}' → '{translated_name}'")
                    progress_bar.update(1)
                except Exception as e:
                    airport_code = future_to_airport[future]
                    print(f"Error translating airport {airport_code}: {e}")
                    progress_bar.update(1)

    # Combine existing and new translations
    all_translations = {**existing_translations, **new_translations}

    # Order the translations alphabetically by key
    ordered_translations = {key: all_translations[key] for key in sorted(all_translations.keys())}

    # Write the combined translations to the file
    with open(output_file_path, "w", encoding="utf-8") as output_file:
        json.dump(ordered_translations, output_file, ensure_ascii=False, indent=2)

if __name__ == "__main__":
    airports = _read_json_file('airports.json')

    for language in ('CATALAN', 'DUTCH', 'FRENCH', 'GERMAN', 'ITALIAN', 'POLISH', 'PORTUGUESE', 'RUSSIAN', 'SPANISH'):
        output_file_path = f'airports_names_{language}.json'
        translate_airport_file_to_language(airports, output_file_path, language)