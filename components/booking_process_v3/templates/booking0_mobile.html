<style>
     :root {
         --b0-font1:{{font1|safe}};
         --b0-font2:{{font2|safe}};
     }
</style>

<link rel="stylesheet" href="/static-booking/styles/generated/booking_process_v3/booking0_mobile.css">

<script data-cookieconsent="ignore" type="text/javascript" src="/static_1/lib/isotope/isotope.min.js"></script>
<script data-cookieconsent="ignore" type="text/javascript" src="/static-booking/js/generated/booking_process_v3/booking0.js?v=@@automatic_version@@"></script>

{% if booking0_filters %}
    <input type="hidden" id="filters_b0" value="{{ booking0_filters|safe }}">
{% endif %}

{% if no_dispo_message_popup_redirection %}
    <h4 class="alternatives_titles_b0">{{ T_alternativas_b0 }}</h4>
{% endif %}

<div class="booking_0_buttons_wrapper">
    <div class="top_content_wrapper">
        <div class="hotels_found_wrapper">
            <span class="total_hotels">0</span>
            <span class="hotels_found_translation multiple"> {{ T_hoteles_encontrados|capitalize }}</span>
            <span style="display:none" class="hotels_found_translation unique"> {{ T_hotel_encontrado|capitalize }}</span>
        </div>
        {% if poi_information %}
            <div class="view_map_button">
                <i class="fal fa-map-marker-alt"></i>
                <span>{{ T_mostrar_mapa }}</span>
            </div>
            <div class="view_list_button hide">
                <i class="fa-light fa-list"></i>
                <span>{{ T_mostrar_lista }}</span>
            </div>
        {% endif %}
    </div>
    <div class="bottom_content_wrapper">
        <div class="order_selector_wrapper custom_selector" data-selector_class="order">
            <div class="label">
                <i class="fa-light fa-sort"></i>
                <span class="text">{{ T_ordenar_por }}</span>
            </div>
            <div class="selector order mobile">
                <div class="head">
                    <div class="left_side">
                        <i class="fa-light fa-sort"></i>
                        <span class="text">{{ T_ordenar_por }}</span>
                    </div>
                    <i class="fa-light fa-xmark close_filters_btn"></i>
                </div>
                <div class="scroll_wrapper">
                    <div class="option_wrapper price_asc">
                        <div class="option" data-selector="price_asc">
                            <div class="custom_radio"></div>
                            {{ T_precio_asc }}
                        </div>
                    </div>
                    <div class="option_wrapper price_desc">
                        <div class="option" data-selector="price_desc">
                            <div class="custom_radio"></div>
                            {{ T_precio_desc }}
                        </div>
                    </div>
                    {% if not remove_fields_from_order_selector.distance %}
                    <div class="option_wrapper distance">
                        <div class="option" data-selector="distance">
                            <div class="custom_radio"></div>
                            {{ T_distancia_centro }}
                        </div>
                    </div>
                    {% endif %}
                    {% if not remove_fields_from_order_selector.city %}
                    <div class="option_wrapper city">
                        <div class="option" data-selector="city">
                            <div class="custom_radio"></div>
                            {{ T_ciudad }}
                        </div>
                    </div>
                    {% endif %}
                    {% if not remove_fields_from_order_selector.country %}
                    <div class="option_wrapper country">
                        <div class="option" data-selector="country">
                            <div class="custom_radio"></div>
                            {{ T_pais }}
                        </div>
                    </div>
                    {% endif %}
                    {% if not remove_fields_from_order_selector.brand %}
                    <div class="option_wrapper brand">
                        <div class="option" data-selector="brand">
                            <div class="custom_radio"></div>
                            {{ T_marca }}
                        </div>
                    </div>
                </div>
                {% endif %}
                <div class="footer">
                    <div class="btn_show_results">Show results</div>
                </div>
            </div>
        </div>
        {%if not hide_filters%}
        {% if hide_filters_items %}
            <input type="hidden" id="avoided_filters" value="{% for item in hide_filters_items %}{{ item }}{% if not loop.last %};{% endif %}{% endfor %}">
        {% endif %}
        <div class="filter_selector_wrapper custom_selector" data-selector_class="filters">
            <div class="label">
                <i class="fa-light fa-filter"></i>
                <span class="text">{{ T_filtrar_por }}</span>
            </div>
            <div class="selector with_titles filters mobile">
                <div class="head">
                    <div class="left_side">
                        <i class="fa-light fa-filter"></i>
                        <span class="text">{{ T_filtrar_por }}</span>
                    </div>
                    <i class="fa-light fa-xmark close_filters_btn"></i>
                </div>
                <div class="scroll_wrapper">
                    <div class="option_wrapper" data-type="city">
                        <div class="option_title">
                            <span class="text">{{ T_ciudad }}</span>
                            <i class="fa-light fa-chevron-down chevron"></i>
                        </div>
                        <div class="options">
                            <div class="expand">
                                <div class="options_grid"></div>
                            </div>
                        </div>
                    </div>
                    <div class="option_wrapper" data-type="country">
                        <div class="option_title">
                            <span class="text">{{ T_pais }}</span>
                            <i class="fa-light fa-chevron-down chevron"></i>
                        </div>
                        <div class="options">
                            <div class="expand">
                                <div class="options_grid"></div>
                            </div>
                        </div>
                    </div>
                    <div class="option_wrapper" data-type="brand">
                        <div class="option_title">
                            <span class="text">{{ T_marca }}</span>
                            <i class="fa-light fa-chevron-down chevron"></i>
                        </div>
                        <div class="options">
                            <div class="expand">
                                <div class="options_grid"></div>
                            </div>
                        </div>
                    </div>
                    <div class="option_wrapper" data-type="distance">
                        <div class="option_title">
                            <span class="text">{{ T_distancia_centro }}</span>
                            <i class="fa-light fa-chevron-down chevron"></i>
                        </div>
                        <div class="options">
                            <div class="expand">
                                <div class="options_grid"></div>
                            </div>
                        </div>
                    </div>
                    <div class="option_wrapper" data-type="services">
                         <div class="option_title">
                            <span class="text">{{ T_servicios }}</span>
                            <i class="fa-light fa-chevron-down chevron"></i>
                         </div>
                         <div class="options">
                             <div class="expand">
                                 <div class="options_grid"></div>
                             </div>
                         </div>
                    </div>
                    {% if custom_filters_grouped_all %}
                        {% for group_code, group in custom_filters_grouped_all.items() %}
                        <div class="option_wrapper" data-type="{{ group_code }}">
                            <div class="option_title">
                        <span class="text">
                            {% if  group.title == T_other %}
                                {{ T_show_more_filters }}
                            {% else %}
                                {{ group.title }}
                            {% endif %}
                        </span>
                                <i class="fa-light fa-chevron-down chevron"></i>
                            </div>
                            <div class="options">
                                <div class="expand">
                                    <div class="options_grid"></div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% endif %}
                </div>
                <div class="footer">
                    <div class="btn_show_results">Show results</div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    <div class="tags_wrapper">
        <span class="clean_filters">
            <i class="fa-light fa-xmark close"></i>
            {{ T_borrar_filtros }}
        </span>
    </div>
</div>

{% if price_calendar_v2 %}
    <div id="calendar-app-root" class="calendar_app {% if no_dispo_from_booking1 %}show_on_load{% endif %}"></div>
{% endif %}

<div class="booking_0_hotels_wrapper">
    <div class="hotels">
        <div style="display: none">{{ search }}</div>
        {{ hotel_list|safe }}
    </div>
</div>

{% if poi_information %}
    {% if booking0_maps_version == '2' %}
        {% include "components/generated/booking_process_v3/_booking0_maps_v2_mobile.html" %}
    {% else %}
        {% include "components/generated/booking_process_v3/_booking0_maps.html" %}
    {% endif %}
{% endif %}